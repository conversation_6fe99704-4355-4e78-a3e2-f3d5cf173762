# Các ghi chú khi tiếp cận kho code, cập nhật phiên bản cần đọc

## Hướng dẫn cài đặt trên localhost

- Virtual host: http://dauthau.vinades.my/
- CSDL tên dauthau_2018. Import từ file dauthau_2018.sql.gz nếu site thường, import dauthau_2018_sso.sql.gz nếu cài với SSO
- Chép file config.php và config_global.php trong thư mục vinades ra đúng vị trí
- Đăng nhập quản trị bằng tài khoản `admin / o21TguzziIu6B8837iH`
- Ch<PERSON> ý toàn bộ các tài khoản các nếu có mật khẩu cũng tương tự như trên
- Đọc và hướng dẫn để build CSS ra lần đầu hoặc khi sửa scss ở /dev/README.md

<PERSON><PERSON> thể import file .gz bằng lệnh

```
zcat dauthau_2018.sql.gz | mysql -u root dauthau_2018
zcat dauthau_2018_sso.sql.gz | mysql -u root dauthau_2018
```

Nếu import bằng lệnh trên mà gặp lỗi "data too long for column xxx" và tiếng Việt trong admin bị lỗi hiển thị thì import lại như sau
```
zcat dauthau_2018.sql.gz | mysql -u root dauthau_2018 --default-character-set utf8
zcat dauthau_2018_sso.sql.gz | mysql -u root dauthau_2018 --default-character-set utf8
```

Nếu dùng ES cần chỉnh file host

```
127.0.0.1 elastic_dbhost
127.0.0.1 elastic_dtnet
```

## Tài khoản quản trị

admin / o21TguzziIu6B8837iH

## Gộp core NukeViet

12/05/2025 Thêm cấu hình loại dữ liệu có cấu trúc module page

## Cập nhật phiên bản cần kiểm tra

Các file sau đã sửa so với kho code NukeViet, khi trộn code nên kiểm tra lại

- modules/news/funcs/rss.php sửa chèn km và uid khi có trên url
- vendor/vinades/nukeviet/Core/Request.php thêm hàm compound_unicode
- modules/users/funcs/register.php: Thêm đoạn gửi email bidding
- modules/users/funcs/active.php: Thêm đoạn ghi lại ID thống kê bên

git remote add nukeviet https://github.com/nukeviet/nukeviet.git
git fetch nukeviet
git merge nukeviet/nukeviet4.6

**Cài lại thư viện composer nếu thấy thư mục vendor/composer có file bị thay đổi**

- composer remove elasticsearch/elasticsearch
- composer remove phpoffice/phpspreadsheet
- composer remove aws/aws-sdk-php-resources
- composer remove jasny/sso
- composer require elasticsearch/elasticsearch ^7.0
- composer require phpoffice/phpspreadsheet ^1.6
- composer require aws/aws-sdk-php-resources ^0.3
- composer require jasny/sso v0.3.0

rm -rf ./vendor/and/oauth/examples
rm -rf ./vendor/bin
rm -rf ./vendor/elasticsearch/elasticsearch/.ci
rm -rf ./vendor/elasticsearch/elasticsearch/docs
rm -rf ./vendor/gregwar/cache/Gregwar/Cache/demo
rm -rf ./vendor/gregwar/cache/Gregwar/Cache/tests
rm -rf ./vendor/guzzlehttp/guzzle/vendor-bin
rm -rf ./vendor/aws/aws-crt-php/ext
rm -rf ./vendor/jasny/sso/examples
rm -rf ./vendor/jasny/sso/tests
rm -rf ./vendor/phpoffice/phpspreadsheet/.php-cs-fixer.dist.php
rm -rf "./vendor/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/Renderer/PHP Charting Libraries.txt"
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-af.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-ar.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-az.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-ba.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-be.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-bg.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-ca.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-ch.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-cs.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-da.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-de.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-el.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-eo.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-es.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-et.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-fa.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-fi.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-fo.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-gl.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-he.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-hi.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-hr.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-hu.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-hy.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-id.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-it.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-ja.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-ka.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-ko.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-lt.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-lv.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-mg.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-ms.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-nb.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-nl.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-pl.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-pt_br.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-pt.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-ro.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-sk.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-sl.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-sr.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-sv.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-tl.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-tr.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-uk.php
rm -rf ./vendor/phpmailer/phpmailer/language/phpmailer.lang-zh_cn.php
