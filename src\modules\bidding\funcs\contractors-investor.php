<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_IS_MOD_BIDDING'))
    die('Stop!!!');

use NukeViet\Dauthau\Share;
$title_lock = Share::langTitleUnlock();
$temp_id = isset($array_op[1]) ? $array_op[1] : "";

if (!empty($temp_id)) {
    $array_id = explode('-', $temp_id);
    $id = intval(end($array_id));
}

if ($id == 0) {
    nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
}

$page = $nv_Request->get_page('page', 'post,get', 1);
$ord = $nv_Request->get_int('order', 'post,get', 0);
$sql = 'SELECT * FROM ' . TBL_BIDDING_SOLICTOR . ' WHERE id=' . $id;
$result = $db->query($sql);
$row = $result->fetch();

if (empty($row)) {
    nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
}

$alias = !empty($row['alias']) ? $row['alias'] : strtolower(change_alias($row['title']));

// alias đa ngôn ngữ
$row['english_name'] = !empty($row['english_name']) ? $row['english_name'] : '';
if (NV_LANG_DATA != 'vi' && !empty($row['english_name'])) {
    $alias = strtolower(change_alias($row['english_name']));
    $row['title'] = $row['english_name'];
}

$page_title = sprintf($nv_Lang->getModule('title_project_investor_page_title'), $row['title']);
$array_mod_title[] = array(
    'title' => $module_info['funcs'][$module_info['alias']['project-owner']]['func_custom_name'],
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['project-owner'], true)
);

if (!empty($ord)) {
    $page_url = $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $alias . '-' . $row['id'] . '&order=' . $ord;
} else {
    $page_url = $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $alias . '-' . $row['id'];
}

$row['link'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=project-owner/' . $alias . '-' . $row['id'], true);

$array_mod_title[] = array(
    'title' => $row['title'],
    'link' => $row['link']
);

$array_mod_title[] = array(
    'title' => $page_title,
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=contractors-investor/' . $alias . '-' . $row['id'], true)
);

if ($page > 1) {
    $page_url .= '&page=' . $page;
}
$canonicalUrl = getCanonicalUrl($page_url);

// Xác định link cho block ngôn ngữ
$global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $other_lang_module_info['alias']['contractors-investor'] . '/' . ((NV_LANG_DATA == 'vi' && !empty(trim($row['english_name']))) ? change_alias($row['english_name']) : $row['alias']) . '-' . $row['id'];

if (!defined('NV_IS_USER')) {
    if ($row['hide_info'] == 1 && $row['time_hide_end'] >= NV_CURRENTTIME) {
        $nv_BotManager->setPrivate();
        $contents = user_info_exit($nv_Lang->getModule('hide_info_1'));
    } else {
        $link_login = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $link__popup = sprintf($nv_Lang->getModule('view_result_one_1'), $link_register, nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3'));
        $contents = get_list(3, $link__popup);
        
        $contents = str_replace("##title__vip##", $nv_Lang->getModule('title_lock_login'), $contents);
        $contents = str_replace("##link__vip##", $link_login, $contents);
    }
    
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}
$arr_vip = [];
// Kiểm tra xem tk có là VIP hay không
if (!empty($global_array_vip)) {
    if (isset($global_array_vip[88])) {
        unset($global_array_vip[88]);
    }
    $arr_vip = $global_array_vip;
}

$view_all_content = false;
if (!empty($arr_vip)) {
    if (isset($arr_vip[3])) {
        define('NV_IS_VIP3', true);
        $arr_customs = $global_array_vip[3];
    } elseif (isset($arr_vip[31])) {
        define('NV_IS_VIP3', true);
        $arr_customs = $global_array_vip[31];
    } elseif (isset($global_array_vip[100])) {
        // Nếu có sử dụng gói T100 sẽ được xem toàn bộ dữ liệu với nhà thầu mà họ đã tải file pdf
        $_request_pdf = $db->query("SELECT id FROM " . NV_PREFIXLANG . "_businesslistings_exportpdf_request WHERE id_solicitor =" . $id . ' AND userid=' . $user_info['userid'])->fetchColumn();
        $view_all_content = !empty($_request_pdf) ? true : false;
    }
    define('NV_IS_VIP', true);
}

// $array_data['title_lock'] = Share::langTitleLogin();

if (empty($arr_customs) and !$view_all_content) {
    // Kiểm tra nếu gói VIP 3 hết hạn
    $vip3_renew = $db->query('SELECT * FROM ' . TBL_BIDDING_CUSTOM . ' WHERE user_id=' . $user_info['userid'] . ' AND status != 1 AND (vip = 3 OR vip = 31)')->fetch();
    if (!empty($vip3_renew)) {
        $link = sprintf($nv_Lang->getModule('view_result_user_view1_renew'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3');
    } else {
        $link = sprintf($nv_Lang->getModule('view_result_user_view1'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
    }

    if ($row['hide_info'] == 1 && $row['time_hide_end'] >= NV_CURRENTTIME) {
        $contents = user_info_exit($link);
    } else {
        $contents = get_list(3, $link);
    }

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
    die();
}

$url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=contractors-investor/' . $alias . '-' . $row['id'], true);

$contents = get_list(20, '');

include (NV_ROOTDIR . "/includes/header.php");
echo nv_site_theme($contents);
include (NV_ROOTDIR . "/includes/footer.php");

function get_list($per_page, $link_register = "")
{
    global $module_data, $db, $nv_Request, $page, $base_url, $page_url, $row, $url, $nextPage, $prevPage, $vip3_renew, $nv_Lang;
    $nopage = false;
    if (defined('NV_IS_USER')) {
        $page = 1;
        $nopage = true;
    } else {
        $page = 1;
        $nopage = true;
    }

    $list_order = [
        -1 => $nv_Lang->getModule('select_0'),
        2 => $nv_Lang->getModule('option_goithau_total_asc'),
        -2 => $nv_Lang->getModule('option_goithau_total_desc'),
        3 => $nv_Lang->getModule('option_goithau_trung_asc'),
        -3 => $nv_Lang->getModule('option_goithau_trung_desc'),
        4 => $nv_Lang->getModule('option_goithau_truot_asc'),
        -4 => $nv_Lang->getModule('option_goithau_truot_desc'),
        5 => $nv_Lang->getModule('option_goithau_chuaketqua_asc'),
        -5 => $nv_Lang->getModule('option_goithau_chuaketqua_desc'),
    ];

    $sort_order = $nv_Request->get_title('order', 'get', -1);
    if (!in_array($sort_order, array_keys($list_order))) {
        nv_redirect_location($url);
    }

    $db->sqlreset()
        ->select('count(*)')
        ->from(TBL_BIDDING_SOLICTOR_STATIC)
        ->where('investor_id = ' . $row['id']);
    $sth = $db->prepare($db->sql());
    $sth->execute();
    $num_items = $sth->fetchColumn();

    $db->select('*')
        ->limit($per_page)
        ->offset(($page - 1) * $per_page);
    $sth = $db->prepare($db->sql());
    $sth->execute();

    $_arr_tbmt = $array_business = [];
    while ($_row = $sth->fetch()) {
        $_row['so_dkkd'] = ($_row['so_dkkd'] == '' or $_row['so_dkkd'] == 0) ? 0 : $_row['so_dkkd'];
        $array_business[$_row['so_dkkd']]['bidder_name'] = '';
        $array_business[$_row['so_dkkd']]['no_business_licence'] = $_row['so_dkkd'];
        $array_business[$_row['so_dkkd']]['total'] = 0;
        $array_business[$_row['so_dkkd']]['num_truot'] = 0;
        $array_business[$_row['so_dkkd']]['num_trung'] = 0;
        $array_business[$_row['so_dkkd']]['num_chuacoketqua'] = 0;
        $arr_tbmt = json_decode($_row['so_tbmt'], true);
        foreach ($arr_tbmt as $so_tbmt) {
            $array_business[$_row['so_dkkd']]['row'][$so_tbmt]['trung_thau'] = 3;
            $_arr_tbmt[$so_tbmt] = $so_tbmt;
        }
    }

    // Kiểm tra KQ của các gói thầu đó
    $_arr_data_tbmt = [];
    if (!empty($_arr_tbmt)) {
        // Lấy dữ liệu bảng result
        $sqlResult = $db->query("SELECT id, code, bidder_name FROM " . NV_PREFIXLANG . "_bidding_result WHERE code IN ('" . implode("','", $_arr_tbmt) . "')");
        $listResult = [];
        while ($v = $sqlResult->fetch()) {
            $listResult[$v['id']] = $v;
        }
        $sqlResult->closeCursor();

        if (!empty($listResult)) {
            $sqlResult = $db->query("SELECT resultid, no_business_licence FROM " . NV_PREFIXLANG . "_bidding_result_business WHERE resultid IN (" .  implode(',', array_keys($listResult)) . ")");
            while ($v = $sqlResult->fetch()) {
                isset($listResult[$v['resultid']]) && $_arr_data_tbmt[$listResult[$v['resultid']]['code']] = $v['no_business_licence'];
            }
            $sqlResult->closeCursor();
        }
    }

    // Lấy thông tin nhà thầu
    $_businesslistings_info = $db->query("SELECT id, code, companyname FROM " . TBL_BUSINESS_INFO . " WHERE code IN ('" . implode("','", array_keys($array_business)) . "')");

    $array_businesslistings_info = array();
    while ($_row = $_businesslistings_info->fetch()) {
        $array_businesslistings_info[$_row['code']] = $_row;
    }

    foreach ($array_business as $k => $business) {
        if (isset($array_businesslistings_info[$k])) {
            $array_business[$k]['bidder_name'] = $array_businesslistings_info[$k]['companyname'];
            $array_business[$k]['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($array_businesslistings_info[$k]['companyname']) . '-' . $array_businesslistings_info[$k]['id'];
            $array_business[$k]['link_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detailinvestor/' . change_alias($array_businesslistings_info[$k]['companyname']) . '-' . $array_businesslistings_info[$k]['id'] . '/' . $row['id'];
        } else {
            $array_business[$k]['bidder_name'] = $nv_Lang->getModule('no_name');
            $array_business[$k]['link_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detailinvestor/' . change_alias($nv_Lang->getModule('no_name')) . '-' . 0 . '/' . $row['id'];
        }

        foreach ($business['row'] as $sotbmt => $value) {
            if (isset($_arr_data_tbmt[$sotbmt]) and $_arr_data_tbmt[$sotbmt] == $k) {
                $array_business[$k]['row'][$sotbmt]['trung_thau'] = 1;
                $value['trung_thau'] = 1;
            } else if (isset($_arr_data_tbmt[$sotbmt])) {
                $array_business[$k]['row'][$sotbmt]['trung_thau'] = 2;
                $value['trung_thau'] = 2;
            }
            $array_business[$k]['total']++;
            if ($value['trung_thau'] == 1) {
                $array_business[$k]['num_trung']++;
            } else if ($value['trung_thau'] == 2) {
                $array_business[$k]['num_truot']++;
            } else {
                $array_business[$k]['num_chuacoketqua']++;
            }
        }
    }

    if (in_array($sort_order, array_keys($list_order))) {
        switch($sort_order) {
            case 2: {
                array_msort($array_business, 'total', 'desc');
                break;
            }

            case -2: {
                array_msort($array_business, 'total', 'asc');
                break;
            }

            case 3: {
                array_msort($array_business, 'num_trung', 'asc');
                break;
            }

            case -3: {
                array_msort($array_business, 'num_trung', 'desc');
                break;
            }

            case 4: {
                array_msort($array_business, 'num_truot', 'desc');
                break;
            }

            case -4: {
                array_msort($array_business, 'num_truot', 'desc');
                break;
            }

            case 5: {
                array_msort($array_business, 'num_chuacoketqua', 'desc');
                break;
            }

            case -5: {
                array_msort($array_business, 'num_chuacoketqua', 'desc');
                break;
            }

            default: {
                array_msort($array_business, 'total', 'desc');
                break;
            }
        }
    }

    $data = $array_business;
    $row['tongket'] = sprintf($nv_Lang->getModule('tongket_contractors_investor'), $row['title'], $num_items);
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    // Đánh số trang
    $urlappend = '&page=';
    betweenURLs($page, ceil($num_items / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

    $contents = nv_theme_bidding_solicitor_contractors_investor($row, $array_business, $generate_page, $page, $per_page, $link_register, $nopage, $list_order, $sort_order);

    if (!defined('NV_IS_VIP3') and defined('NV_IS_USER')) {
        // Kiểm tra nếu gói VIP 3 hết hạn
        if (!empty($vip3_renew)) {
            // Nếu hết hạn
            $contents = str_replace("##title__vip##", $nv_Lang->getModule('title_renew_vip'), $contents);
            $contents = str_replace("##link__vip##", NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3', $contents);
        } else {
            $contents = str_replace("##title__vip##", $nv_Lang->getModule('title_resg_vip'), $contents);
            $contents = str_replace("##link__vip##", NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3', $contents);
        }
    }
    return $contents;
}
