/*Override css theme default*/
#body_modal_log {
    word-break: break-all;
}

#body_modal_log td {
    vertical-align: top!important;
}
#bidding__menu {
    background: #fbfafa;
    border-radius: 3px;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    margin-bottom: 19px;
    box-shadow: 0 1px 2px rgb(0 0 0 / 10%);
    position: absolute;
    top: 115%;
    right: 0;
    z-index: 3;
    border:  1px dashed #e91e63;
    transition: all 0.3s linear;
    animation: grow 0.3s ease-in;
    transform-origin: 90% top;
    display: none;
    width: 270px;
}
.btn-menu #bidding__menu {
    border:  1px dashed #0685d6;
}
.bidding_menu_wrapper {
    top: 149% !important;
}
.bidding_menu_wrapper.no-amination {
    transition: unset !important;
    animation: unset !important;
    transform-origin: unset !important;
}
.no-amination .menu-list {
    transform-origin: 90% top;
}
.bidding_menu_wrapper::before{
    right: 42px !important;
}
.show {
    display: block;
}

@keyframes grow {
    from {
        opacity: 0;
        transform: scale(0);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

#bidding__menu::before {
    position: absolute;
    content: "";
    border-style: solid;
    border-width: 12px 9px;
    border-color: transparent transparent red transparent;
    top: -24px;
    right: 16px;
    border-color: transparent transparent #e91e6366 transparent;
}
.btn-menu #bidding__menu::before {
    border-color: transparent transparent #0685d6 transparent;
    border-color: transparent transparent #0685d6 transparent;
    top: -23px;
}
.bidding__menu-title {
    padding-top: 12px;
    padding-bottom: 12px;
    padding-left: 6px;
    box-shadow: inset 0 -1px 0 #d8e0e8;
    display: flex;
    justify-content: space-between;
}

.bidding__menu-title span {
    font-size: 17px;
    color: #000000;
    font-family: "Segoe UI";
    font-weight: 600;
}

.bidding__menu-title i {
    margin-right: 10px;
    font-size: 16px;
    background: #ededed;
    padding: 5px 7px;
    border-radius: 19%;
}
.detail-list-btn .bidding__menu-title {
    background-color: #0685d6;
}
.detail-list-btn .bidding__menu-title span,
.detail-list-btn .bidding__menu-title i {
    color: #fff;
    background: none;
}
#tableMenuContent {
    padding: 12px 18px;
    padding-bottom: 0;
}

#tableMenuContent li {
    margin-bottom: 4px;
}

#tableMenuContent li a {
    position: relative;
    display: inline-block;
    width: 100%;
    padding: 6px 8px;
    font-size: 16px;
    line-height: 24px;
    color: #334155;
    transition: all .2s ease-out;
}

.bidding__link.active {
    background-color: #fff;
    box-shadow: 0 0 0 1px #e4eaf1;
    border-radius: 12px;
    font-weight: 500;
    padding: 6px 16px;
}

.bidding__link:hover {
    background-color: #fff;
    box-shadow: 0 0 0 1px #e4eaf1;
    border-radius: 12px;
    font-weight: 500;
    padding: 6px 16px;
}

#fixmenu {
    transition: all 0.6s ease-out;
    z-index: 2;
    background: #fbfafa;
    width: 270px;
    border: 1px dashed #e91e63;
    transition: all 0.3s linear;
    animation: grow 0.3s linear;
    /* max-height: 310px; */
    min-height: 250px;
}
.btn-menu #fixmenu {
    border: 1px dashed #0685d6;
}
.btn-menu {
    margin-left: 15px;
}
.solicitor-menu {
    margin-left: 0px;
}
.detail-wrapper #bidding__menu,
.detail-wrapper #fixmenu{
    width: 280px;
}
.bidding__menu-tab-navigation {
    user-select: none;
}
.cls_info_1 {
    padding: 0 10px;
}
.solicitor-menu .menu-show-left {
    left: 0 !important;
    transform-origin: 0% top !important;
}
.solicitor-menu .menu-show-left::before {
    left: 40px !important;
    right: auto !important;
}
@media (min-width: 768px) and (max-width: 1366px) {
    .btn-menu #tableMenuContent {
        padding: 12px 10px;
    }

    .btn-menu #bidding__menu, .btn-menu #fixmenu {
        width: 250px !important;
    }
}
@media only screen and (max-width: 768px) {
    #tableMenuContent {
        padding: 12px 10px;
    }

    #bidding__menu, #fixmenu {
        width: 250px !important;
    }

    #tableMenuContent li a {
        font-size: 15px;
    }

    .btn__list_ol::before, .btn__list_ol::after {
        display: none !important;
    }
}

.panel-vipplan .panel-heading {
    min-height: 194px;
}
.detail-wrapper .crawl_time {
    margin-left: auto;
}
.crawl_time-solicitor {
    width: calc(100% - 98px);
}
#main__menu_bidding {
    position: relative;
}
.detail-list-btn {
    display: flex;
    align-items: center;
}
.btn__list_ol {
    border: 1px dashed #00000066;
}

.menu-list.mb-fixed{
    right: 10px;
}
#fixmenu.static {
    position: static;
    bottom: 0;
    top: auto;
}
#fixmenu.fixed {
    position: fixed;
    top: 0;
}
#fixmenu.mb-fixed {
    position: fixed;
    top: 58px;
}
.btn_list_menu.mb-fixed {
    position: fixed;
    top: 58px;
    right: 10px;
    z-index: 3;
}
.btn_list_menu.static {
    position: static;
}
.btn_list_menu.fixed {
    position: fixed;
    top: 0;
    z-index: 3;
}

.btn__list_ol:hover {
    background: #fff;
}

.hidden__nav {
    cursor: pointer;
}

.hidden__bidding__menu-tab {
    cursor: pointer;
    user-select: none;
}

/* .btn__list_ol::before {
    content: attr(data-tolltip);
    position: absolute;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    top: -24px;
    left: -64px;
    font-size: 12px;
    padding: 6px;
    line-height: 12px;
    border-radius: 6px;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.2);
    z-index: 2;
}

.btn__list_ol::after {
    content: "";
    position: absolute;
    border-style: solid;
    border-width: 7px 5px;
    border-color: transparent transparent rgba(0, 0, 0, 0.6) transparent;
    top: -7px;
    left: 22px;
} */

#sess {
    font-family: "Segoe UI";
    /* background: #112d42; */
    color: #fff;
    position: relative;
    /* min-height: 85vh; */
    justify-content: center;
    align-items: center;
    display: flex;
    font-size: 13px;
    animation: fadeIn 0.6s ease-in;
    transition: all 0.3s;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: scale(0);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

#sess::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    /* background: #03a9f4; */
}

.mainvip {
    position: relative;
    min-width: 1200px;
    min-height: 950px;
    display: flex;
    z-index: 10;
}

.mainvip__header_title {
    font-weight: 500;
    font-size: 19px;
}
.mainvip form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
}
@media (max-width: 991px) {
    .mainvip form {
        flex-wrap: wrap;
    }
}
.mainvip .mainvip__left {
    position: relative;
    width: 490px;
    min-height: calc(100% - 80px);
    background: url('../images/bg-page.webp'), rgba(5,120,193,.8);
    background-size: 480px;
    z-index: 1;
    box-shadow: 0 20px 20px rgba(0, 0, 0, 0.2);
    margin: 40px 0px;
}
.mainvip .mainvip__right {
    color: #000;
    background: #fff;
    padding: 40px 50px;
    width: calc(100% - 160px);
    box-shadow: 0 10px 50px rgba(0, 0, 0, 0.2);
    margin-left: -335px;
    padding-left: 355px;
}

.mainvip__header {
    padding: 10px;
    display: flex;
    align-items: center;
}

.mainvip__header .mainvip__header_title {
    margin: 0;
}

.mainvip__alert {
    position: absolute;
    color: black;
    text-align: center;
    z-index: 1;
    top: 6px;
    width: 100%;
}

.main__vip_flex {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.mainvip__right label {
    font-family: "Segoe UI";
    font-weight: 400;
}

.mainvip__right input, .mainvip__right textarea {
    font-family: "Segoe UI";
    font-size: 14px;
}

#sess .font_segoe label {
    font-family: "Segoe UI";
    font-size: 14px;
    font-weight: 500 !important;
}

.main__vip_infovip label {
    font-family: "Segoe UI" !important;
    font-size: 14px;
    user-select: none;
    font-weight: 500;
    font-size: 14px;
}

.main__vip_infovip a {
    text-decoration: none;
    color: #fff;
    font-weight: 500;
    font-size: 14px;
}

.main__vip_infovip select {
    padding: 0;
    font-size: 16px;
    padding-left: 2px;
}

.min__vip {
    /* min-height: 45px; */
}

.form__checkbox-custom {
    display: flex;
    justify-content: center;
    align-items: center;
}

.form__checkbox-custom input {
    height: 22px;
    width: 22px;
    margin-right: 5px;
    outline: none;
    cursor: pointer;
}

.form__checkbox-custom a {
    margin-left: 5px;
}

.form__label_checkbox-custom {
    display: flex;
    justify-content: left;
    align-items: center;
}

.form__label_checkbox-custom input {
    height: 22px;
    width: 22px;
    margin-right: 5px;
    outline: none;
    border: none;
    cursor: pointer;
}

.from__checkbox-custom a {
    margin-left: 6px;
}

.show__email {
    background: #fff;
    padding: 8px;
    border-radius: 4px;
    display: block;
}

.mainvip .mainvip__left .mainvip__header {
    margin: 8px 0;
}

.mainvip .mainvip__right .mainvip__header {
    background: #efeded38;
}

.mainvip__box {
    overflow: hidden;
}

.scoll__menu {
    margin: 10px 0;
}

.wrap__text {
    word-break: break-word;
}

@media only screen and (max-width: 768px) {
    .mainvip {
        display: flex;
        flex-direction: column;
        min-width: 100%;
        max-width: 100%;
    }

    .mainvip__alert {
        position: relative;
        left: 0;
    }

    .mainvip .mainvip__left, .mainvip .mainvip__right {
        position: relative;
        width: 100% !important;
        height: auto;
    }

    .mainvip .mainvip__left {
        top: 0;
        padding-bottom: 14px;
        margin-bottom: 14px;
        min-width: 100%;
        max-width: 100%;
    }

    .mainvip .mainvip__right {
        padding: 0;
        margin-left: 0;
        padding-bottom: 14px;
    }

    .main__vip_flex, .mainvip__header_title {
        font-size: 15px;
    }

    .mainvip__right .mainvip__header {
        background: #e1e1e12e;
        border: 1px solid #dfdddd94;
    }

    #sess label {
        font-size: 14px;
    }

    .mainvip .mainvip__left .mainvip__header_title {
        font-size: 20px;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .mainvip {
        min-width: 100%;
    }

    .mainvip .mainvip__left {
        height: auto;
        overflow: hidden;
    }
}

@media only screen and (min-width: 771px) and (max-width: 991px) {
    .mainvip .mainvip__left {
        width: 370px;
    }

    .mainvip {
        min-height: 1100px;
    }

    .mainvip__right {
        margin-left: 190px;
        padding-left: 210px;
        width: calc(100% - 190px);
    }


}

@media only screen and (min-width: 992px) and (max-width: 1200px) {
    .mainvip {
        min-width: 1024px;
        min-height: 1024px;
    }
    .mainvip__header {
        background: #efeded38;
    }
}

.title__view {
    text-transform: uppercase;
}

.breadcrumbs-wrap .breadcrumbs a>span {
    display: inline-block;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    max-width: 100%;
    text-overflow: ellipsis;
}

.ttchuyen {
    display: block;
    color: #464646;
    text-align: center;
    font-weight: 500;
    margin: 5px 0;
}

.dis_block {
    display: block;
}

.bidding-detail-item .list-group-item {
    word-break: break-word;
}

.waring_file {
    color: #ae6f00;
    margin: 0;
    font-size: 14px;
    font-style: italic;
    /*  -webkit-animation: my 6s infinite;
     -moz-animation: my 6s infinite;
     -o-animation: my 6s infinite;
     animation: my 6s infinite; */
}

.box__attach a[rel='nofollow']{
    font-weight: 500;
}

.warning__file {
    cursor: pointer;
    margin-bottom: 0;
    margin-right: 5px;
    user-select: none;
    font-size: 14px;
}

.box__attach .popover-title {
    font-size: 14px;
    padding: 5px;
}

.download-link {
    overflow: inherit;
}

.box__list-group-item {
    display: flex;
    align-items: center;
    padding-left: 10px;
    background: #fff;
    border: 1px solid #ccc;
    margin-top: 3px;
}

.box__list-group-item .list-group-item {
    flex: 1;
    border: none;
    padding-left: 0;
}

.box__list-group-item .list-group-item span::before {
    font-family: FontAwesome;
    content: " ";
    margin-right: 5px;
}

.nav-tabs.download-nav li:first-child {
    margin-left: 0;
}

.box__list-group-item .list-group-item span.is_ie {
    text-decoration: line-through;
}

.box__list-group-item .list-group-item span::after {
    font-family: FontAwesome;
    margin-left: 2px;
}

.box__list-group-item .list-group-item span.is_ie::after {
    content: " ";
}

.box__list-group-item .list-group-item span.is_pdf::after {
    content: " ";
}

.bidding-sub-title {
    padding: 15px 0;
    margin-bottom: 0;
}


.body-content > tr {page-break-inside:avoid; page-break-after:auto }
.body-content > table, th, td {
    page-break-inside:auto;
    border: 1px solid #ddd;
    border-collapse: collapse;
}
.body-content > th, td {
    padding: 5px;
}
.bdl{
    padding: 10px;
}

.btn-radius {
    padding: 5px 20px;
    border-radius: 20px;
    -moz-border-radius: 20px;
    -webkit-border-radius: 20px;
    border: none;
    cursor: pointer;
}
.table tbody tr td, .table thead tr th {
    vertical-align: middle !important;
}

.info__filterlog {
    padding: 10px 5px;
    background: #f8f8f8;
    border-left: 4px #ffbc00 solid;
    font-weight: 500;
}

.thuocvip {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 2px;
    font-size: 12px;
    /* box-shadow: -13px 8px 16px rgb(70 69 69 / 16%); */
    font-family: "Segoe UI";
    font-weight: 600;
    background: #ffffff;
    border-left: 2px solid #7fd3e1;
    border-top: 1px dotted #ccc;
    border-bottom: 1px dotted #ccc;
    border-right: 1px dotted #ccc;
}

.dateexpired_old {
    text-transform: lowercase;
}

.dateexpired_new {
    font-family: "Segoe UI";
    font-weight: 500;
}

.lower-case {
    text-transform:none!important;
}

.title__filter {
    font-weight: 600;
    word-break: break-word;
}

.open_filter {
    display: flex;
    align-items: center;
    padding: 8px 4px;
    color: #626262;
    background: #f9f9f9;
    position: relative;
}

.open_filter:before {
    content: '';
    position: absolute;
    top: 0;
    right: 20px;
    width: 9%;
    height: 100%;
    transform: skew(38deg);
    background: rgb(181 181 181 / 15%);
}

.open_filter em {
    margin-right: 5px;
}

.selected_phanmuc{
    width: 100%;
}

.panel-body ul li {
    margin-bottom: 0;
}

.title__gd {
    margin: 0;
    display: flex;
    align-items: center;
    margin-top: 6px;
    padding: 0 5px;
}

.title__heading {
    color: #fff;
    background: #0685d6;
    font-size: 16px;
    font-weight: 400;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: rgba(0,0,0,0);
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    padding: 10px;
}

.block__custom {
    border: 1px solid #0685d6;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgb(0 0 0 / 5%);
}

.block__custom_content {
    padding: 15px;
}

.list_item_block > .block__custom_content > .items_block {
    margin-bottom: 7px;
    padding-bottom: 7px;
    border-bottom: 1px #DADADA solid;
}

.content__text {
    text-align: justify;
}

.fa-cloud-download {
    color: #0685d6;
}

/* begin css timeline */
.timeline {
    position: relative;
    width: 100%;
    max-width: 1140px;
    margin: 0 auto;
    padding: 15px 0;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 2px;
    background: #0685d6;
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -1px;
}

.container-timeline {
    padding: 15px 30px;
    position: relative;
    background: inherit;
    width: 50%;
}

.container-timeline.left {
    left: 0;
}

.container-timeline.right {
    left: 50%;
}

.container-timeline::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: calc(50% - 8px);
    right: -8px;
    background: #ffffff;
    border: 2px solid #0685d6;
    border-radius: 16px;
    z-index: 1;
}

.container-timeline.right::after {
    left: -8px;
}

.container-timeline::before {
    content: '';
    position: absolute;
    width: 50px;
    height: 2px;
    top: calc(50% - 1px);
    right: 8px;
    background: #0685d6;
    z-index: 1;
}

.container-timeline.right::before {
    left: 8px;
}

.container-timeline .date {
    position: absolute;
    display: inline-block;
    top: calc(50% - 8px);
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    color: #0685d6;
    text-transform: uppercase;
    letter-spacing: 1px;
    z-index: 1;
}

.container-timeline.left .date {
    right: -110px;
}

.container-timeline.right .date {
    left: -110px;
}

.container-timeline .icon {
    position: absolute;
    display: inline-block;
    width: 40px;
    height: 40px;
    padding: 9px 0;
    top: calc(50% - 20px);
    background: #f8f8f8;
    border: 2px solid #0685d6;
    border-radius: 40px;
    text-align: center;
    font-size: 18px;
    color: #0685d6;
    z-index: 1;
}

.container-timeline.left .icon {
    right: 56px;
}

.container-timeline.right .icon {
    left: 56px;
}

.container-timeline .content {
    padding: 30px 90px 30px 30px;
    background: #f8f8f8;
    position: relative;
    border: 2px solid #0685d6;
    border-radius: 0 100px 100px 0;
}

.container-timeline.right .content {
    padding: 30px 30px 30px 90px;
    border: 2px solid #0685d6;
    border-radius: 100px 0 0 100px;
}

.container-timeline .content h2 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: bold;
    color: #000000;
}

.container-timeline .content p {
    margin: 0;
    font-size: 16px;
    line-height: 22px;
    color: #000000;
}

@media (max-width: 767.98px) {
    .timeline::after {
        left: 40px;
    }

    .container-timeline {
        width: 100%;
        padding-left: 120px;
        padding-right: 30px;
    }

    .container-timeline.right {
        left: 0%;
    }

    .container-timeline.left::after,
    .container-timeline.right::after {
        left: 82px;
    }

    .container-timeline.left::before,
    .container-timeline.right::before {
        left: 100px;
        border-color: transparent #000000 transparent transparent;
    }

    .container-timeline.left .date,
    .container-timeline.right .date {
        right: auto;
        left: -5px;
        max-width: 10px;
    }

    .container-timeline.left .icon,
    .container-timeline.right .icon {
        right: auto;
        left: 146px;
    }

    .container-timeline.left .content,
    .container-timeline.right .content {
        padding: 30px 30px 30px 90px;
        border-radius: 100px 0 0 100px;
    }
}

.fw-normal {
    font-weight: normal;
}

.tb_custom {
    box-shadow: 0px 15px 42px rgb(100 100 100 / 20%);
}

.tb_custom tbody tr td, .tb_custom head tr td, .tb_custom head tr th  {
    vertical-align: middle !important;
    border: none;
    background: #f6f7f9;
}

.tb_custom tbody tr {
    margin-bottom: 2px;
    border-bottom: 2px solid #fff;
}

.title_tk {
    background-color: rgba(225,234,236,.46);
    font-weight: 700;
    padding: 12px 10px;
    margin-bottom: 2px

}

.font-weight {
    font-weight: bold;
}

.title__td {
    font-weight: bold;
    background: #ecf1f3;
}

.box_flex {
    display: flex;
    justify-content: space-between;
}

.ghichu {
    padding: 5px;
    border-radius: 5px;
    color: #fff;
    box-shadow: 0px 6px 18px rgb(106 101 101 / 20%);
    text-align: center;
}

.bg_color_success {
    background: #029d8e;
}

.bg_color_red {
    background: #029d8e;
}

.bg_no_color {

}

.bg_color_red {
    background: #e9311e;
}

/* #open_record {
    width: 50vw;
    margin: 0 auto;
    height: 500px;
    background: red;
    position: fixed;
    transition: all 0.6s;
    animation: fadeOut ease-in 1s;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
} */

@keyframes fadeOut {
    0% {
        opacity: 0;
        transform: scale(0);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.color_red {
    color: red;
    font-weight: 600;
}

.flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flex-vertical-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.div_updattime {
    padding: 5px;
    margin-right: 10px;
}

.title_updatetime {
    background: #d5d5d5;
    padding: 5px;
    border-radius: 8px;
    font-weight: 500;
    box-shadow: 0 3px 6px rgb(165 161 161 / 20%);
}

.title_text_body {
    padding: 10px 0;
    font-weight: 600;
}

.title_header_modal {
    padding: 9px;
    background: #00bcd4;
    color: #fff;
    border-radius: 2px;
    box-shadow: 0px 2px 5px rgb(0 0 0 / 20%);
}

.seemore {
    background: #ff5e00;
    color: #fff;
    padding: 6px;
    border-radius: 5px;
    box-shadow: 0px 2px 5px rgb(0 0 0 / 20%);
}

.title_seemore {
    color: #ff5e00;
}

.mg-bt-15 {
    margin-bottom: 15px !important;
}

#dshs .active a {
    color: #fff !important;
}

.overdue_noti {
    z-index: 10000;
    background-color: white;
    position: absolute;
    top: 12px;
    left: -3%;
    cursor: pointer;
}
.overdue_noti:before {
    font-size: 20px;
    color: #d3730f;
}

/* ----------------------------------------------- * Timeline * --------------------------------------------- */
.timeline {
    list-style: none;
    padding: 10px 0;
    position: relative;
    display: grid;
    grid-template-columns: 1fr 1fr;
}

.timeline:before {
    top: 0;
    bottom: 0;
    position: absolute;
    content: " ";
    width: 2px;
    background: #0685d6;
    left: 50%;
    margin-left: -1.5px;
}

.timeline > li {
    margin-bottom: 20px;
    position: relative;
    width: 100%;
    float: left;
    clear: left;
}

.timeline > li:before, .timeline > li:after {
    content: " ";
    display: table;
}

.timeline > li:after {
    clear: both;
}

.timeline > li:before, .timeline > li:after {
    content: " ";
    display: table;
}

.timeline > li:after {
    clear: both;
}

.timeline > li > .timeline-panel {
    width: calc(100% - 25px);
    width: -moz-calc(100% - 25px);
    width: -webkit-calc(100% - 25px);
    float: left;
    border: 1px solid #dcdcdc;
    background: #fff;
    position: relative;
}

.timeline > li > .timeline-panel:before {
    position: absolute;
    top: 26px;
    right: -15px;
    display: inline-block;
    border-top: 15px solid transparent;
    border-left: 15px solid #dcdcdc;
    border-right: 0 solid #dcdcdc;
    border-bottom: 15px solid transparent;
    content: " ";
}

.timeline > li > .timeline-panel:after {
    position: absolute;
    top: 27px;
    right: -14px;
    display: inline-block;
    border-top: 14px solid transparent;
    border-left: 14px solid #fff;
    border-right: 0 solid #fff;
    border-bottom: 14px solid transparent;
    content: " ";
}

.timeline > li > .timeline-badge {
    color: #fff;
    width: 26px;
    height: 26px;
    line-height: 23px;
    text-align: center;
    position: absolute;
    top: 30px;
    right: -13px;
    z-index: 100;
    background: #ffffff;
    border-top-right-radius: 50%;
    border-top-left-radius: 50%;
    border-bottom-right-radius: 50%;
    border-bottom-left-radius: 50%;
}

.fa-lg-vt{
    font-size: 1.6em;
    line-height: .75em;
    vertical-align: -15%;
}

.timeline > li.timeline-inverted > .timeline-panel {
    float: right;
}

.timeline > li.timeline-inverted > .timeline-panel:before {
    border-left-width: 0;
    border-right-width: 15px;
    left: -15px;
    right: auto;
}

.timeline > li.timeline-inverted > .timeline-panel:after {
    border-left-width: 0;
    border-right-width: 14px;
    left: -14px;
    right: auto;
}

.timeline-badge > a {
    color: #0685d6 !important;
}

.timeline-badge a:hover {
    color: #0685d6 !important;
}

.timeline-title {
    margin-top: 0;
    color: inherit;
}

.timeline-heading h4 {
    font-weight: 400;
    padding: 0 15px;
    color: #4679bd;
}

.timeline-body > p, .timeline-body > ul {
    padding: 10px 15px;
    margin-bottom: 0;
}

.timeline-footer {
    padding: 5px 15px;
    background-color: #f4f4f4;
}

.timeline-footer p {
    margin-bottom: 0;
}

.timeline-footer > a {
    cursor: pointer;
    text-decoration: none;
}

.timeline > li.timeline-inverted {
    float: right;
    clear: right;
}

.timeline > li.timeline-inverted {
    margin-top: 60px;
}

.timeline > li.timeline-inverted > .timeline-badge {
    left: -13px;
}

.timeline-badge.primary {
    background-color: #2e6da4 !important;
}

.timeline-badge.success {
    background-color: #3f903f !important;
}

.timeline-badge.warning {
    background-color: #f0ad4e !important;
}

.timeline-badge.danger {
    background-color: #d9534f !important;
}

.timeline-badge.info {
    background-color: #5bc0de !important;
}

.no-float {
    float: none !important;
}

@media (max-width: 767px) {

    .timeline{
        grid-template-columns: none;
    }

    ul.timeline:before {
        left: 40px;
    }

    ul.timeline > li {
        margin-bottom: 0px;
        position: relative;
        width: 100%;
        float: left;
        clear: left;
    }

    ul.timeline > li > .timeline-panel {
        width: calc(100% - 65px);
        width: -moz-calc(100% - 65px);
        width: -webkit-calc(100% - 65px);
    }

    ul.timeline > li > .timeline-badge {
        left: 28px;
        margin-left: 0;
        top: 30px;
    }

    ul.timeline > li > .timeline-panel {
        float: right;
    }

    ul.timeline > li > .timeline-panel:before {
        border-left-width: 0;
        border-right-width: 15px;
        left: -15px;
        right: auto;
    }

    ul.timeline > li > .timeline-panel:after {
        border-left-width: 0;
        border-right-width: 14px;
        left: -14px;
        right: auto;
    }

    .timeline > li.timeline-inverted {
        float: left;
        clear: left;
        margin-top: 30px;
        margin-bottom: 30px;
    }

    .timeline > li.timeline-inverted > .timeline-badge {
        left: 28px;
    }
}

#main_change_notificaion {
    font: normal 14px/1.4 sans-serif;
    background: #f1f1f1;
    padding: 10px;
}

.title_change_notifi {
    padding: 10px;
}

ins {
    background: lightgreen;
    text-decoration: none;
}

del {
    background: pink;
}

.card {
    background: #fff;
    /*border: 1px solid #ccc;*/
    border-radius: 3px;
    /*margin: 1rem;*/
    /*padding: 1rem;*/
}

.card .card {
    margin: 0;
    flex: 1 0 0;
}

.row_compare {
    display: flex;
    justify-content: space-between;
    margin-right: -8px;
}

.col {
    display: flex;
    flex: 1 0 0;
    flex-direction: column;
    margin-right: 8px;
}

#showTB .modal-lg, #show_goods_subdivision .modal-lg {
    width: 97%;
}

#show_goods_subdivision .modal-lg {
    width: 80%;
}

#view_change_tb {
    outline: none;
}

@media screen and (max-width: 500px) {
    .row_compare {
        display: block;
    }
    .bttn-reg-vipqt {
        margin-top: 5px;
    }
    .btn-menu .no-amination {
        border:  none !important;
    }
    .no-amination::before {
        display: none;
    }
}

.momo {
    opacity: 0.5;
    filter: grayscale(1);
}

.damdam {
    opacity: 1;
    filter: grayscale(0);
}

.icon__download {
    position: relative;
    width: 100%;
    height: 20px;
    transition: all 0.6s;
}

.icon__download div {
    position: absolute;
    right: 22px;
    animation: mymove 0.8s ease-out infinite;
}

@keyframes mymove {
    0% {
        bottom: 50px;
    }

    25% {
        bottom: 30px;
    }

    50% {
        bottom: 20px;
    }

    75% {
        bottom: 15px;
    }

    100% {
        bottom: 5px;
    }
}

.ctx-khlcnt-pl {
    background: #f5f5f5;
    border: 1px solid #e8e8e8;
    box-sizing: border-box;
    border-radius: 20px;
    font-size: 12px;
    line-height: 16px;
    color: #8c8c8c;
    margin-left: 5px;
    cursor: pointer;
    padding: 0.35rem !important;
}

.border-or {
    display: flex;
    align-items:center;
    box-sizing: border-box;
    margin: 15px;
}

.border-or > hr {
    border-top: 2px solid #ddd;
}

.filetrigger {
    position: relative;
    display: inline-block;
}

.filetrigger > input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    opacity: 0.0;
    filter: alpha(opacity=0);
    -ms-filter: "alpha(opacity=0)";
    -khtml-opacity: 0.0;
    -moz-opacity: 0.0;
}

.download a.btn-warning, .download a.btn-warning:hover {
    color: #fff;
}

.list_hanghoa {
    border: 1px solid #e3e3e3;
    padding: 14px;
    box-shadow: 0px 0px 10px 0px #ccc;
}

.close_custom {
    background: #8a8686;
    display: inline-block;
    color: #ffff;
    padding: 6px;
    font-size: 14px;
    border-radius: 2px;
    box-shadow: 0px 5px 4px rgb(170 162 162 / 60%);
    cursor: pointer;
}

.box_hanghoa {
    position: fixed;
    top: 30%;
    left: 50%;
    width: 87%;
    background: #fff;
    z-index: 999;
    transform: translate(-50%, -30%);
    overflow-y: scroll;
    max-height: 87vh;
    transition: all 0.3s;
    animation: zoom 0.3s ease-in;
}

@keyframes zoom {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

.bg_momo_show {
    background: black;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 888;
}

.text-ellip {
    display: -webkit-box;
    font-size: 16px;
    line-height: 1.3;
    -webkit-line-clamp: 5;  /* số dòng hiển thị */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0;
    cursor: auto;
    user-select: initial;
}

.text-ellip.active {
    -webkit-line-clamp: unset;
}

.responsive_tb_custom {
    position: relative;
    overflow: auto;
    width: 100%;
    max-width: 100%;
    max-height: 700px;
}

.btn-dayleft {
    background-color: #df2753;
    border-color: #df2753;
    display: flex;
    align-items: center;
    cursor: default;
    color: #fff;
}

.btn-dayleft.focus, .btn-dayleft:focus, .btn-dayleft:hover {
    background-color: #d52852;
    border-color: #d52852;
    color: #fff;
}

.note__date {
    border: 1px solid red;
    padding: 5px;
}

.title__list_ld {
    color: #006a83;
    font-size: 24px;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #b7cde9;
}

.ani_rotate {
    animation: xoaytron 5s ease-in infinite;
}

@keyframes xoaytron {
    0%   {
        transform: rotate(20deg);
    }

    25%  {
        transform: rotate(0deg);
    }

    50%  {
        transform: rotate(-20deg);
    }

    100% {
        transform: rotate(0deg);
    }
}

.text-weight {
    font-weight: bold;
}

.bidding_vp .item{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    font-size: 16px;
    background: #fff;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgb(0 0 0 / 3%);
    border: 1px solid #f5f5f5;
}
.bidding_vp .item:nth-child(odd){
    background: rgba(230,247,255,0.5);
}
.bidding_vp .item:first-child{
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
.bidding_vp .item:last-child{
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}
.bidding_vp .item .item_t{
    width: 20%;
}
.bidding_vp .item .nghi_dinh{
    width: 60%;
}
.bidding_vp .item .item_t_n{
    padding: 10px;
    border-bottom: 1px solid #e3e9ef;
}
.bidding_vp .item .nghi_dinh{
    padding: 10px;
    border-left: 1px solid #e3e9ef;
    border-right: 1px solid #e3e9ef;
}
.bidding_vp .item .item_b{
    display: flex;
    align-items: center;
    justify-content: center;
}
.bidding_vp .item .name_lable{
    font-weight: normal;
    font-size: 16px;
    line-height: 22px;
    color: #007aa4;
    margin-bottom: 0;
    border: 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.bidding_vp .item .name{
    font-weight: bold;
    font-size: 18px;
    line-height: 24px;
    color: #007aa4;
    margin-bottom: 0;
}
.bidding_vp .item .js-btn-tooltip{
    background: #fff;
    border: 1px solid #007aa4;
    box-sizing: border-box;
    border-radius: 20px;
    color: #007aa4;
    margin-top: 16px;
    margin-bottom: 16px;
    padding: 2px 5px;
    outline: none;
}
.bidding_vp .item .vp_name{
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
    color: #007aa4;
    margin-bottom: 1.5rem;
}
.bidding_vp .item .name_violate p{
    font-weight: normal;
    font-size: 16px;
    line-height: 22px;
    color: #595959;
    margin-bottom: 0;
}
.bidding_vp .item .blue-btn {
    color: #fff;
    border: 1px solid #007aa4;
    cursor: pointer;
    display: inline-block;
    background: #007aa4;
    box-shadow: 0 4px 20px rgb(0 0 0 / 3%);
    border-radius: 8px;
    font-weight: normal;
    font-size: 14px;
    line-height: 22px;
    padding: 5px 10px;
    box-sizing: border-box;
}

.tree__custom, .tree__custom ul {
    margin:0;
    padding:0;
    list-style:none
}

.tree__custom ul {
    margin-left:1em;
    position:relative
}

.tree__custom ul ul {
    margin-left:.5em
}

.tree__custom ul:before {
    content:"";
    display:block;
    width:0;
    position:absolute;
    top:0;
    bottom:0;
    left:0;
    border-left:1px solid
}

.tree__custom li {
    margin:0;
    padding:0 1em;
    line-height:2em;
    color:#369;
    font-weight:700;
    position:relative
}

.tree__custom ul li:before {
    content:"";
    display:block;
    width:10px;
    height:0;
    border-top:1px solid;
    margin-top:-1px;
    position:absolute;
    top:1em;
    left:0
}

.tree__custom ul li:last-child:before {
    background:#fff;
    height:auto;
    top:1em;
    bottom:0
}

.indicator {
    margin-right:5px;
}

.tree__custom li a {
    text-decoration: none;
    color:#369;
    font-size: 14px;
}

.tree__custom li button, .tree__custom li button:active, .tree__custom li button:focus {
    text-decoration: none;
    color:#369;
    border:none;
    background:transparent;
    margin:0px 0px 0px 0px;
    padding:0px 0px 0px 0px;
    outline: 0;
}

.branch {
    user-select: none;
}

#openIndustry, #closeIndustry {
    cursor: pointer;
    color: #0685d6;
    user-select: none;
}

.removeindustry .closeIndustry {
    display: block;
}

.responsive_tb_custom {
    display: block;
    overflow-y: scroll;
    max-height: 100vh;
}

#myModal_QuyHoach .modal-dialog {
    width: 90%;
}

#myModal_QuyHoach .modal-dialog .img__view_large {
    max-width: 100%;
}

@media (max-width: 991px) {
    .bidding_vp .item .item_t{
        width: 100%;
    }
    .bidding_vp .item .nghi_dinh{
        width: 100%;
        border: 1px solid #e3e9ef;
        border-left: none;
        border-right: none;
    }
    .bidding_vp .item .item_b{
        padding: 10px;
    }
    #province_bodycontent .border-bidding::before {
        display: none;
    }
}

.bg_list_khlcnt {
    top: 0;
}

.li__renwal:before {
    content: "X" !important;
    font-weight: bold;
}

.responsive_tb_phlo {
    max-height: 278px;
    margin-bottom: 40px;
}

.bg__good {
    transition: all 0.6s;
    animation: fadeOut 4s ease-out;
}

.is_active_table--bg {
    background: #0685d633;
}

/* cảnh báo vi phạm quy định đấu thầu qua mạng */
.warning-area blockquote {
    display: none;
}

@keyframes fadeOut {
    0% {
        background: #fdcb17;
    }

    100% {
        background: #fff;
    }
}

/*Responsive table*/
@media screen and (max-width: 760px) {
    .label_responsive {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .responsivetb table {
        border: none;
    }

    .info_product {
        padding: 0;
    }

    .responsivetb table thead {
        border: none;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }

    .responsivetb table tr {
        border-bottom: 1px dotted #ccc;
        display: block;
        margin-bottom: 24px;
        background: #f9f9f905;
    }

    .responsivetb table td {
        display: flex;
        font-size: 13px;
        justify-content: space-between;
        text-align: right;
        /*align-items: center;*/
    }

    .responsivetb table td:last-child {
        display: block !important;
        display: flex !important;
        flex-direction: row;
    }

    .responsivetb table td::before {
        content: attr(data-label);
        /*font-weight: 500;*/
        /* text-transform: uppercase; */
        text-align: left;
        word-break: break-word;
        min-width: 78px;
        margin-right: 21px;
        font-family: 'Roboto';
    }

    .responsivetb table td:last-child {
        border-bottom: 0;
    }

    .responsivetb table td span {
        text-align: justify;
        margin-left: 16px;
        min-width: 50px;
        font-size: 14px;
    }

    .responsivetb table .stt {
        padding: 6px 4px;
        background: orange;
        color: #fff;
        font-weight: 500;
        border-radius: 2px;
        text-align: center;
        box-shadow: 0px 5px 10px rgb(151 151 151 / 20%);
        min-width: 36px;
    }

    .responsivetb table .content {
        font-weight: 500;
        border-radius: 7px;
        text-align: center;
        box-shadow: 0px 5px 10px rgb(163 163 163 / 20%);
        margin-left: 0;
        padding: 4px 17px;
        text-align: justify;
    }

    .responsivetb table td:last-child span {
        margin-left: 0 !important;
    }

    .responsivetb table td.td__reg {
        display: block !important;
    }

    #show_goods_subdivision .modal-lg {
        width: 100% !important;
    }

    .row-ellip {
        width: 100%;
        padding: 0px 10px;
    }

    .box_hanghoa {
        top: 34%;
        width: 95%;
        max-height: 92vh;
    }
}

/*Responsive table*/
@media screen and (max-width: 1330px) {
    .responsivetb-lg table {
        border: none;
    }

    .responsivetb-lg table thead {
        border: none;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }

    .responsivetb-lg table tr {
        border-bottom: 2px dotted #03a9f4bd;
        display: block;
        margin-bottom: 24px;
        background: #f9f9f905;
    }

    .responsivetb-lg table td {
        display: flex;
        font-size: 13px;
        justify-content: space-between;
        text-align: right;
        /*align-items: center;*/
    }

    .responsivetb-lg table td:last-child {
        display: block !important;
        display: flex !important;
        flex-direction: row;
    }

    .responsivetb-lg table td::before {
        content: attr(data-label);
        /*font-weight: 500;*/
        /* text-transform: uppercase; */
        text-align: left;
        word-break: break-word;
        min-width: 78px;
        margin-right: 21px;
        font-family: 'Roboto';
    }

    .responsivetb-lg table td:last-child {
        border-bottom: 0;
    }

    .responsivetb-lg table td span {
        text-align: justify;
        margin-left: 16px;
        min-width: 50px;
        font-size: 14px;
    }

    .responsivetb-lg table .stt {
        padding: 6px 4px;
        background: orange;
        color: #fff;
        font-weight: 500;
        border-radius: 2px;
        text-align: center;
        box-shadow: 0px 5px 10px rgb(151 151 151 / 20%);
        min-width: 36px;
    }

    .responsivetb-lg table .content {
        font-weight: 500;
        border-radius: 7px;
        text-align: center;
        box-shadow: 0px 5px 10px rgb(163 163 163 / 20%);
        margin-left: 0;
        padding: 4px 17px;
        text-align: justify;
    }

    .responsivetb-lg table td:last-child span {
        margin-left: 0 !important;
    }

    .responsivetb-lg table td.td__reg {
        display: block !important;
    }
}

.action__icb {
    cursor: pointer;
    user-select: none;
}

.active__color {
    color: #0685d6;
    transition: all 0.6s;
}

.action__icb:hover {
    color: #0685d6;
}

.pagination__link, .pagination__link_related {
    user-select: none;
}

.pagination__main li a.active {
    color: #fff !important;
}

span.color__white {
    color: #fff;
}

.order__solitictor {
    /*margin: 0px 4px;*/
    cursor: pointer;
    user-select: none;
    font-size: 14px;
}

.list_tbtm {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
}

.list_tbtm__item {
    width: 50%;
}

#subdiv {
    visibility: hidden;
}

.title__tab_heading {
    text-transform: uppercase;
}

.data-pc .active .title__tab_heading {
    border-bottom: 4px #0685d6 solid;
}

#myTabs {
    margin-bottom: 20px;
}

#myTabs li a {
    font-weight: 500;
}

.sort {
    cursor: pointer;
    font-size: 20px;
    padding: 0px 10px;
}

.sort a {
    color: #fff;
}

.sort__desc {
    display: none;
}

.flex__sort {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex_end {
    display: flex;
    justify-content: flex-end;
}

.sort .fa-sort-desc {
    position: relative;
    top: -3px;
}

.sort .fa-sort-asc {
    top: 3px;
    position: relative;
}

a[href=""].bidding_link {
    color: #000 !important;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        height: 0;
    }

    100% {
        opacity: 1;
        height: auto;
    }
}

.no-before:before {
    content: none !important;
}

.bg__language {
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: contain !important;
}

.language a {
    height: 34px;
    width: 40px;
}

.bg_detailsolicitor_all {
    top: -9px;
}

.bg_list_solicitorrelationship, .bg_list_solicitorresult, .bg_list_solicitortbmt {
    top: -10px;
}

.bg_list_solicitorid, .bg_list_solicitorpq {
    top: -20px;
}

.bg_list_solicitorkhlcnt {
    top: -20px;
    left: 40px;
}

.bg_list_solicitorpq {
    left: 10px;
}

.bg_list_solicitorresult {
    left: 60px;
}

fieldset {
    margin: 0 0 30px 0;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 3px;
}

legend {
    background: #eee;
    padding: 4px 10px;
    color: #000;
    margin: 0 auto;
    display: inline-block !important;
    width: auto !important;
    font-size: 16px;
    margin-left: 10px;
    border-radius: 3px;
}

fieldset legend h2 {
    font-weight: 600;
    font-size: 16px;
    display: inline-block;
}

fieldset .title {
    font-size: 15px !important;
}

fieldset img {
    max-width: 100% !important;
    height: auto; /* Giữ tỷ lệ khung hình */
}

.icon__qh {
    font-size: 12px !important;
}

.no-active {
    color: #000000;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 32px;
}

.select2-container--default .select2-selection--single, .select2-container--bootstrap .select2-selection--single {
    height: 38px !important;
}

.select2-container--default .select2-results>.select2-results__options {
    max-height: 500px !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    user-select: none;
    outline: none;
}

.featured__custom {
    display: flex;
}

.featured__custom_left {
    width: 20%;
    padding: 0px 10px;
}

.featured__custom_right {
    width: 80%;
    padding: 0px 10px;
}

.featured__custom_left_img {
    background: none !important;
}

select[name="ketqua_luachon_tochuc_dgts"] {
    margin-top: 10px;
    display: none;
}

select.bidorganization[name="ketqua_luachon_tochuc_dgts"] {
    display: block;
}

/* Form đăng nhập gọi từ ajax nút bấm */
div#tip{
    right: 0;
    margin-right: 0;
}

#tip .login-box,
#tip .login-box .g_id_signin > div > div > div {
    max-width: 100%!important;
}
#tip .login-box .g_id_signin iframe {
    width: calc(100% + 10px)!important;
    border-radius: 6px;
}

#onlrule-violate-msg, .content_notifi {
    position: relative;
}

.custom_border {
    border-left: 5px solid #c19415;
    box-shadow: 0px 8px 15px #ccc;
    transition: all 0.6s;
    font-weight: 500;
    color: #c19415;
}

.customer_border_support_error {
    color: black;
    text-align: right;
    font-size: 15px;
    font-weight: 400;
    margin-top: 10px;
}

#onlrule-violate-msg i.fa-times-circle, .close_notifi {
    position: absolute;
    right: 4px;
    top: 4px;
    z-index: 2;
    cursor: pointer;
    color: #a97242;
}

#onlrule-violate-msg i.fa-exclamation-circle, .support_notifi {
    position: absolute;
    right: 30px;
    top: 4px;
    z-index: 2;
    cursor: pointer;
    color: #f31a2c;
}

#onlrule-violate:hover {
    cursor: pointer;
}

label[for^="unmail"] {
    display: inline;
    font-weight: normal;
    color: #0685d6;
}

label[for^="unmail"]:hover {
    cursor: pointer;
}

.chopsang {
    transition: all 1s;
    animation: chopsang 0.6s linear;
}

.zoomIn {
    transition: all 1s;
    animation: zoomIn 0.8s linear;
    position: relative;
}
#soheadunlink {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
}
#soheadunlink i {
    font-weight: normal;
    font-size: 16px!important;
    text-transform: none;
    font-style: normal;
    min-width: 100px;
    padding-left: 10px;
}
@keyframes zoomIn {
    0% {
        transform: scale(0.8);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes chopsang {
    0%, 10%, 60% {
        color:#fff;
    },
30%, 75%, 100% {
    color: #c19415;
}
}

@media screen and (max-width: 678px) {

}

@media screen and (max-width: 768px) {
    .box_lockvip__mobile {
        display: block;
        left: 0px !important;
        top: 17px !important;
        .bg_list_khlcnt {
            left: 0;
        }
    }
}

@media screen and (min-width: 678px) {
    .box_lockvip__mobile {
        display: none;
    }
}

@media screen and (min-width: 680px) {
    .box_lockvip {
        display: block !important;
    }
}

@media screen and (min-width: 769px) {
    .box_lockvip {
        display: none !important;
    }

    .box_lockvip__mobile {
        display: block !important;
    }

    .coating span, .coating i {
        -webkit-filter: blur(5px);
    }
}

@media screen and (min-width: 991px) {
    .box_lockvip__mobile {
        display: none !important;
    }

    .box_lockvip {
        display: block !important;
    }

    .coating span, .coating i {
        -webkit-filter: none !important;
    }
}
.column-error{
    background-color: #d61c1d;
    color: white;
    position: relative;
}
.column-error > div > span{
    cursor: pointer;
}
.pt-10{
    padding: 10px 0px 10px 0px;
}
blockquote.list ul ul {
    padding-left: 20px;
}
blockquote.list ul ul li {
    font-size: 14px;
}
blockquote.list ul ul li::before {
    content: "\f061";
    font-family: FontAwesome;
}
.blink_me {
    animation: blinker 1s linear infinite;
    color: red;
    font-size: 18px;
    font-weight: bold;
}
.column-error {
    background-color: #d61c1d;
    color: white;
    position: relative;
}
.column-warning {
    background: orange;
    color: #fff;
    cursor: pointer;
}
.border-bidding>h1 {
    border-bottom: 4px #0685d6 solid;
    text-transform: uppercase;
    font-size: 20px;
    color: #0685d6;
    font-weight: 500;
    padding-bottom: 3px;
    display: inline-block;
}

.logotherlists {
    padding-left: 15px;
    font-size: 14px;
    color: #777;
}

.box-info-renewal-auto {
    border-top: 1px dashed #dcdcdc;
    margin-bottom: 0;
    margin-top: 6px;
    padding-top: 3px;
    opacity: .75;
    cursor: help;
}

.tags-tab {
    font-weight: 500;
    background: #f5f5f5;
    border-radius: 100px !important;
    padding: 4px 12px !important;
    color: #7a7a7a;
    text-decoration: none;
    cursor: pointer;
    margin-right: 5px;
    font-size: 15px;
}

li.active > .tags-tab{
    background: #0685d6;
    color: #fff;
}

.follow_stats {
    list-style-type: square;
    list-style-position: inside;
    padding: 0;
    font-weight: bold;
}

.warning-area blockquote{
    transition: none;
    font-size: 14px;
    margin: 10px 0 0;
}

.online-rule-violate, #onlrule-violate {
    color: #c19415;
}

.online-rule-violate:hover {
    cursor: pointer;
}

#staticsolicitor .apexcharts-legend-text{
    font-size: 13px !important;
}

acronym {
    border-bottom: none;
    text-decoration: none;
}

.h-ten-goithau {
    width: 40%;
}
.h-ten-plan {
    width: 45%;
}

/* Gov Agency */
.gov-agency-table th .col-xs-20{
    padding: 0 5px;
}
.gov-agency-table th .col-xs-2{
    padding: 0;
}

.gov-agency-anonymous-mobile, .gov-agency-table-mobile {
    display: none;
}
.gov-agency-anonymous-mobile th, .gov-agency-table-mobile th {
    max-width: 70%;
    width: 70%;
    font-weight: normal;
}
.gov-agency-anonymous-mobile td {
    text-align: center;
}
.cw-name {
    width: 300px;
    min-width: 300px;
    max-width: 300px;
}
.cw-big {
    width: 140px;
    min-width: 140px;
    max-width: 140px;
}
.dmdc-group {
    display: none;
}
#form-filter.dmdc .dmdc-group {
    display: block;
}
#form-filter.dmdc .msc-group {
    display: none;
}
.dmdc-group .select2 {
    width: 100%!important;
}
#levels .form-group:after {
    content: "";
    display: block;
    width: 100%;
    clear: both;
}

.bx-level .select2,
.mf-gov-agency .msc-group .select2 {
    width: 100% !important;
}

.mf-gov-agency .msc-group .select2-selection,
.mf-gov-agency .dmdc-group .select2-selection,
.bx-level .select2 .select2-selection{
    min-height: 38px;
}

.mf-gov-agency .main_lockvip {
    width: 480px;
    margin: 0 auto;
}

.bidding-page-btn .last-crawl-update {
    flex: 1;
}

@media (max-width: 1290px) {
    .gov-agency-anonymous, .gov-agency-table {
        display: none;
    }
    .gov-agency-anonymous-mobile, .gov-agency-table-mobile {
        display: block;
    }
}

.chart {
    min-height: 365px;
    padding: 10px;
    box-shadow: 0px 4px 10px 0px rgb(197 197 197 / 62%);
    margin-bottom: 30px;
    margin-top: 20px;
    position: relative;
    z-index: 2;
}

#investor-activity-chart.main_chart_history .business-chart-title {
    font-size: 16px;
    text-transform: unset;
}

#investor-activity-chart.main_chart_history .apexcharts-yaxis-title-text {
    font-weight: 700;
}

#investor-activity-chart.main_chart_history .bussiness-chart-description {
    text-align: center;
    font-size: 13px;
    font-style: italic;
    color: #555;
}

#investor-activity-chart.main_chart_history.business-chart {
    padding-left: 10px;
    padding-right: 10px;
}

#investor-activity-chart.main_chart_history #solicitortotalinvest {
    margin-bottom: 20px;
}

.gov-agency-anonymous .box_lockvip {
    margin-top: 180px;
}

.gov-agency-anonymous .box_lockvip, .gov-agency-anonymous .box_lockvip__mobile {
    width: unset;
    left: 300px;
}

.gov-agency-anonymous .box_lockvip {
    position: sticky;
}

.gov-agency-anonymous .gov-agency-table {
    margin-top: -300px;
}

.bidding-page-btn > *:first-child {
    margin-left: 0;
}

#preview-button i {
    vertical-align: middle;
    margin-left: 5px;
}

#sitemodalTerm ul {
    list-style: initial;
}

@media screen and (max-width: 678px) {
    .mf-gov-agency .filter-label {
        text-align: left;
        margin-bottom: 5px;
    }
    #investor-activity-chart.main_chart_history .apexcharts-legend {
        display: none;
    }

    #investor-activity-chart.main_chart_history #solicitortotalinvest {
        padding: 0;
    }
}
