<!-- BEGIN: main -->
<div class="border-bidding" id="bodycontent">
    <h2 class="title__tab_heading"><span>{LANG.pagetitle_khttlcnt}</span></h2>
</div>

<!-- BEGIN: empty -->
<div class="alert alert-warning">{LANG.empty_result}</div>
<!-- END: empty -->
<!-- BEGIN: add_filter -->
<div class="alert alert-success">{LINK_ADD}</div>
<!-- END: add_filter -->
<!-- BEGIN: error_money -->
<div class="alert alert-danger">{ERROR_MONEY}</div>
<!-- END: error_money -->
<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->
<!-- BEGIN: view -->
<form action="{NV_BASE_SITEURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <table class="bidding-table">
        <thead>
            <tr>
                <th>{LANG.ten_du_an}</th>
                <th>{LANG.investor}</th>
                <th>{LANG.ngay_dang_tai}</th>
            </tr>
        </thead>
        <tbody>
            <!-- BEGIN: loop -->
            <tr>
                <td class="order-header" data-column="{LANG.ten_du_an}">
                    <div>
                        <a title="{VIEW.title}" href="{VIEW.link}"><span class="plan-code">{VIEW.code}</span> {VIEW.title}</a>
                    </div>
                </td>
                <td data-column="{LANG.investor}">
                    <div>
                        <!-- BEGIN: link_solicitor -->
                        <a title="{VIEW.solicitor_title}" href="{VIEW.link_solicitor}"> <!-- BEGIN: solicitor_code --> <span class="solicitor-code">{VIEW.solicitor_code}</span> <!-- END: solicitor_code --> {VIEW.solicitor_title}
                        </a>
                        <!-- END: link_solicitor -->

                        <!-- BEGIN: no_link_solicitor -->
                            <!-- BEGIN: solicitor_code -->
                            <span class="solicitor-code">{VIEW.solicitor_code}</span>
                            <!-- END: solicitor_code -->
                            {VIEW.solicitor_title}
                        <!-- END: no_link_solicitor -->
                    </div>
                </td>
                <td class="txt-center" data-column="{LANG.ngay_dang_tai}"><div>{VIEW.addtime}</div></td>
            </tr>
            <!-- END: loop -->
        </tbody>
    </table>

    <!-- BEGIN: generate_page -->
    <div class="text-center">{NV_GENERATE_PAGE}</div>
    <!-- END: generate_page -->
</form>
<!-- END: view -->
<!-- END: main -->
