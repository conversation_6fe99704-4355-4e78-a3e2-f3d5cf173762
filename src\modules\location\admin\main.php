<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> JSC <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Dec 3, 2010  11:33:22 AM
 */

if (!defined('NV_IS_FILE_ADMIN'))
    die('Stop!!!');

$proviceList = nv_Province();
$page_title = $nv_Lang->getModule('main');

if (empty($proviceList) and !$nv_Request->isset_request('add', 'get')) {
    nv_redirect_location(NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=main&add");
}

// Thay doi thu tu tinh
if ($nv_Request->isset_request('cWeight, id', 'post')) {
    $id = $nv_Request->get_int('id', 'post');
    $cWeight = $nv_Request->get_int('cWeight', 'post');
    if (!isset($proviceList[$id]))
        die("ERROR");

    if ($cWeight > ($count = count($proviceList)))
        $cWeight = $count;

    $sql = "SELECT id FROM " . NV_PREFIXLANG . "_" . $module_data . "_province WHERE id!=" . $id . " ORDER BY weight ASC";
    $result = $db->query($sql);

    $weight = 0;
    while ($row = $result->fetch()) {
        $weight++;

        if ($weight == $cWeight)
            $weight++;

        $query = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_province SET weight=" . $weight . " WHERE id=" . $row['id'];
        $db->query($query);
    }

    $query = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_province SET weight=" . $cWeight . " WHERE id=" . $id;
    $db->query($query);

    $nv_Cache->delMod($module_name);
    nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('logChangeWeight'), "Id: " . $id, $admin_info['userid']);
    nv_htmlOutput('OK');
}

// Xoa tinh
if ($nv_Request->isset_request('del', 'post')) {
    $id = $nv_Request->get_int('del', 'post', 0);

    if (!isset($proviceList[$id]))
        die($nv_Lang->getModule('errorCatNotExists'));

    $sql = "SELECT COUNT(*) FROM " . NV_PREFIXLANG . "_" . $module_data . "_district WHERE idprovince=" . $id;
    $result = $db->query($sql);
    $count = $result->fetchColumn();

    if ($count > 0)
        die($nv_Lang->getModule('errorCatYesRow'));

    $query = "DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_province WHERE id = " . $id;
    $db->query($query);

    fix_catWeight();
    $nv_Cache->delMod($module_name);
    nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('logDelCat'), "Id: " . $id, $admin_info['userid']);
    nv_htmlOutput('OK');
}
$id = $nv_Request->get_int('id', 'post,get', 0);
if ($id) {
    $action = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
} else {
    $action = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $id;
}
$currentpath = NV_UPLOADS_DIR . '/' . $module_upload;
$xtpl = new XTemplate("main.tpl", NV_ROOTDIR . "/themes/" . $global_config['module_theme'] . "/modules/" . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('tieude', $nv_Lang->getModule('province'));
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
$xtpl->assign('MODULE_URL', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE);
$xtpl->assign('UPLOADS_DIR_USER', NV_UPLOADS_DIR . '/' . $module_upload);
$xtpl->assign('UPLOAD_CURRENT', $currentpath);
$xtpl->assign('FORM_ACTION', $action);
$xtpl->assign('add', $nv_Lang->getModule('addprovince'));
$xtpl->assign('op', 'main');

if ($nv_Request->isset_request('add', 'get') or $nv_Request->isset_request('edit, id', 'get')) {
    $post = array();
    if ($nv_Request->isset_request('edit', 'get')) {
        $post['id'] = $nv_Request->get_int('id', 'get');
        if (empty($post['id']) or !isset($proviceList[$post['id']])) {
            nv_redirect_location(NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=main");
        }

        $xtpl->assign('PTITLE', $nv_Lang->getModule('editprovince'));
        $xtpl->assign('ACTION_URL', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=main&edit&id=" . $post['id']);

        $log_title = $nv_Lang->getModule('editprovince');
    } else {
        $xtpl->assign('PTITLE', $nv_Lang->getModule('addprovince'));
        $xtpl->assign('ACTION_URL', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=main&add");

        $log_title = $nv_Lang->getModule('addprovince');
    }

    if ($nv_Request->isset_request('save', 'post')) {
        $post['title'] = $nv_Request->get_title('title', 'post', '', 1);

        if (empty($post['title'])) {
            die($nv_Lang->getModule('errorIsEmpty') . ": " . $nv_Lang->getModule('title'));
        }
        $post['images'] = $nv_Request->get_string('images', 'post', '');
        if (!empty($post['images']) and file_exists(NV_UPLOADS_REAL_DIR . '/' . $module_upload . '/' . $post['images'])) {
            $post['images'] = NV_BASE_SITEURL . NV_UPLOADS_DIR . '/' . $module_upload . '/' . $post['images'];
            $currentpath = dirname($post['images']);
        }
        $post['description'] = $nv_Request->get_textarea('description', '', 'br', 1);
        $alias = change_alias($post['title']);

        if (isset($post['id'])) {
            try {
                $query = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_province SET
                    alias=" . $db->quote($alias) . ",
                    title=" . $db->quote($post['title']) . ",
                    images=" . $db->quote($post['images']) . ",
                    description=" . $db->quote($post['description']) . "
                WHERE id=" . $post['id'];
                $db->query($query);
            } catch (PDOException $e) {
                trigger_error($e->getMessage());
            }
            $handleID = $post['id'];
        } else {
            $weight = count($proviceList);
            $weight++;

            try {
                $query = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_province (id, title, alias, weight, status, images, description, region_id) VALUES (
                NULL, " . $db->quote($post['title']) . "," . $db->quote($alias) . "," . $weight . ",1, " . $db->quote($post['images']) . ", " . $db->quote($post['description']) . ", 1)";
                $db->query($query);
            } catch (PDOException $e) {
                trigger_error($e->getMessage());
            }
            $handleID = $db->lastInsertId();
        }

        $nv_Cache->delMod($module_name);
        nv_insert_logs(NV_LANG_DATA, $module_name, $log_title, "Province Id: " . $handleID, $admin_info['userid']);
        nv_htmlOutput('OK');
    }

    $post['title'] = ($nv_Request->isset_request('edit', 'get')) ? $proviceList[$post['id']]['title'] : "";
    $post['images'] = ($nv_Request->isset_request('edit', 'get')) ? $proviceList[$post['id']]['images'] : "";
    $post['description'] = ($nv_Request->isset_request('edit', 'get')) ? $proviceList[$post['id']]['description'] : "";

    $xtpl->assign('CAT', $post);
    $xtpl->parse('action');
    $contents = $xtpl->text('action');

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_admin_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
    exit;
}

if ($nv_Request->isset_request('list', 'get')) {
    $a = 0;

    $count = count($proviceList);
    foreach ($proviceList as $id => $values) {
        $values['id'] = $id;
        $values['alink1'] = "<a href=" . NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=district&idprovince=" . $id . ">";
        $values['alink2'] = "</a>";
        $xtpl->assign('LOOP', $values);
        $xtpl->assign('CLASS', $a % 2 ? " class=\"second\"" : "");

        for ($i = 1; $i <= $count; $i++) {
            $opt = array('value' => $i, 'selected' => $i == $values['weight'] ? " selected=\"selected\"" : "");
            $xtpl->assign('NEWWEIGHT', $opt);
            $xtpl->parse('list.loop.option');
        }

        $xtpl->parse('list.loop');
        $a++;
    }

    $xtpl->parse('list');
    $xtpl->out('list');
    exit;
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
