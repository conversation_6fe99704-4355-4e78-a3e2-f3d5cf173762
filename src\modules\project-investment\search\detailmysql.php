<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_PROJECT_INVESTMENT'))
    die('Stop!!!');

$where = ' date_post>=' . $sfrom1 . ' AND date_post<=' . $sto1;
if (!empty($without_key) and $is_advance) {
    // Tìm theo từ khóa loại trừ
    $arr_key = explode(',', $without_key);
    foreach ($arr_key as $key) {
        if ($search_type_content == 1) {
            $key = trim($key);
            $where .= " AND content_full NOT LIKE " . $db->quote('%' . $key . '%') . ' COLLATE utf8_bin';
        } else {
            $key = str_replace('-', ' ', change_alias($key));
            $key = trim($key);
            $where .= " AND content NOT LIKE " . $db->quote('%' . $key . '%');
        }
    }
}

if (!empty($key_search2) and $is_advance) {
    // Tìm theo Từ khóa bổ sung
    $arr_key = explode(',', $key_search2);
    foreach ($arr_key as $key) {
        if ($search_type_content == 1) {
            $key = trim($key);
            $where .= " AND content_full LIKE " . $db->quote('%' . $key . '%') . ' COLLATE utf8_bin';
        } else {
            $key = str_replace('-', ' ', change_alias($key));
            $key = trim($key);
            $where .= " AND content LIKE " . $db->quote('%' . $key . '%');
        }
    }
}

if (!empty($q)) {
    $where .= ' AND (';
    $num = 0;
    $arr_key = explode(',', $q);
    foreach ($arr_key as $key) {
        if ($search_type_content == 1) {
            $key = trim($key);
            if ($num == 0) {
                $where .= "content_full LIKE " . $db->quote('%' . $key . '%') . ' COLLATE utf8_bin';
            } else {
                $where .= " OR content_full LIKE " . $db->quote('%' . $key . '%') . ' COLLATE utf8_bin';
            }
        } else {
            $key = str_replace('-', ' ', change_alias($key));
            $key = trim($key);
            if ($num == 0) {
                $where .= "content LIKE " . $db->quote('%' . $key . '%');
            } else {
                $where .= " OR content LIKE " . $db->quote('%' . $key . '%');
            }
        }
        $num++;
    }
    $where .= ')';
}

// Tìm kiếm theo tỉnh thành
if ($idprovince >= 0) {
    $where .= ' AND province_id = ' . $idprovince;
}

if ($solicitor_id > 0) {
    $where .= ' AND solicitor_id= ' . $solicitor_id;
}
if ($oda > 0 and $is_advance) {
    $where .= ' AND oda= ' . ($oda == 1 ? 1 : 0);
}
if ($khlcnt == 1 and $is_advance) {
    $where .= ' AND num_khlcnt > 0 ';
}
if ($khlcnt == 2 and $is_advance) {
    $where .= ' AND num_khlcnt = 0 ';
}
if ($total_investment_from > 0 and $is_advance) {
    $where .= " AND total_investment >= " . $total_investment_from;
}
if ($total_investment_to > 0 and $is_advance) {
    $where .= " AND total_investment <= " . $total_investment_to;
}

$db->sqlreset()
    ->select('COUNT(id)')
    ->from('' . NV_PREFIXLANG . '_project_investment')
    ->where($where);
$sth = $db->prepare($db->sql());
$sth->execute();
$total = $sth->fetchColumn();

$db->select('*')
    ->order('date_post DESC')
    ->limit($perpage)
    ->offset(($page - 1) * $perpage);
$sth = $db->prepare($db->sql());
$sth->execute();
while ($view = $sth->fetch()) {
    $view['total_investment'] = number_format($view['total_investment']) . ' VNĐ';
    $view['date_post_format'] = nv_date("d/m/Y", $view['date_post']);
    $view['url_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $view['alias'] . '-' . $view['id'] . '.html';
    $array_data[$view['id']] = $view;
    $arr_solicitor_id[$view['solicitor_id']] = $view['solicitor_id'];
}
