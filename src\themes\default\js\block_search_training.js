var formObject = $("[id*=blcourse]");
function bl_advanceShow() {
    $(".advance-search", formObject).is(":hidden") && $(".advance-search", formObject).slideDown();
    $('input[name="is_advance"]', formObject).val(1);
    $(".btn-search em", formObject).removeClass($(".btn-search", formObject).data("icon-search-simple")).addClass($(".btn-search", formObject).data("icon-search-advance"));
    $(".btn-search .txt", formObject).text($(".btn-search", formObject).data("search-simple"))
}

function bl_advanceHide() {
    $(".advance-search", formObject).not(":hidden") && $(".advance-search", formObject).slideUp();
    $('input[name="is_advance"]', formObject).val(0);
    $(".btn-search em", formObject).removeClass($(".btn-search", formObject).data("icon-search-advance")).addClass($(".btn-search", formObject).data("icon-search-simple"));
    $(".btn-search .txt", formObject).text($(".btn-search", formObject).data("search-advance"))
    reset_validate();
}

function bl_advanceBtnShow() {
    var a = $(".btn-search", formObject);
    a.is(":hidden") && a.show();
    a.parents(".panel").is(":hidden") && a.parents(".panel").show()
}

function bl_advanceBtnHide() {
    var a = $(".btn-search", formObject);
    a.parents(".panel").not(":hidden") && a.parents(".panel").hide();
    a.not(":hidden") && a.hide()
}

function bl_setDaterangepicker(selector, nameFrom, nameTo, _options) {
    var maxspan = parseInt(formObject.attr("data-maxspan")), 
        mindate = formObject.attr("data-mindate"), 
        opens = formObject.attr("data-opens"), 
        _maxspanDate = moment().subtract(maxspan, 'months');

    // Menu khoảng tìm kiếm
    var ranges = {};
    ranges[$(selector, formObject).attr("data-today-lang")] = [moment(), moment()];
    ranges[$(selector, formObject).attr("data-last7days-lang")] = [moment().subtract(6, 'days'), moment()];
    ranges[$(selector, formObject).attr("data-last14days-lang")] = [moment().subtract(13, 'days'), moment()];
    ranges[$(selector, formObject).attr("data-last30days-lang")] = [moment().subtract(29, 'days'), moment()];
    ranges[$(selector, formObject).attr("data-last3months-lang")] = [moment().subtract(3, 'months'), moment()];
    ranges[$(selector, formObject).attr("data-last6months-lang")] = [moment().subtract(6, 'months'), moment()];
    ranges[$(selector, formObject).attr("data-last-maxspan-lang")] = [_maxspanDate, moment()];

    var allday = $(selector, formObject).data('lastall');
    if (allday !== '') {
        ranges[allday] = [moment(mindate, "DD/MM/YYYY"), moment()];
    }
    if (selector === ".search_range_birthday") {
        var startDateValue = _options?.startDate && _options?.startDate !== "" 
        ? moment(_options.startDate, "DD/MM/YYYY") 
        : null; // Nếu trống, không đặt startDate

        var calendar_options = {
            showDropdowns: true,
            autoUpdateInput: false, // Không tự động cập nhật input nếu input trống
            locale: {
                cancelLabel: $(selector, formObject).attr("data-cancel-lang"),
                applyLabel: $(selector, formObject).attr("data-apply-lang"),
                customRangeLabel: $(selector, formObject).attr("data-customrange-lang"),
                format: "DD/MM/YYYY",
            },
            minDate: moment(mindate, "DD/MM/YYYY"),
            maxDate: moment(),
            opens: opens,
            drops: "auto",
            linkedCalendars: false,
        };
        if (startDateValue) {
            calendar_options.startDate = startDateValue;
        }
        calendar_options.singleDatePicker = true;
        delete calendar_options.ranges; // Không cần ranges cho ngày đơn
        setTimeout(function () {
            $(".daterangepicker.single .drp-calendar").css("width", "100%");
            $(".daterangepicker.single .drp-selected").css("display", "block");
            $(".daterangepicker.single .drp-help").css("display", "none");
            
        }, 100);
        $(selector, formObject).daterangepicker(calendar_options, function(start) {
            // Khi chọn ngày, cập nhật input
            var selectedDate = start.format('DD/MM/YYYY');
            $(selector, formObject).val(selectedDate);
            if (nameFrom) {
                $(`[name=${nameFrom}]`, formObject).val(selectedDate);
            }

            // Xóa phần dư ngay lập tức
            cleanDateDisplay();
        })
        .on('show.daterangepicker', function() {
            // Khi mở lịch, đảm bảo chỉ hiển thị 1 ngày
            cleanDateDisplay();
            observeDateChanges(); // Bắt đầu theo dõi sự thay đổi
        })
        .on('apply.daterangepicker', function() {
            // Khi chọn ngày, tiếp tục kiểm tra lại
            setTimeout(cleanDateDisplay, 50);
        })
        .on('cancel.daterangepicker', function() {
            // Khi bấm "Hủy", giữ giá trị rỗng
            $(selector, formObject).val("").trigger("change");
            if (nameFrom) {
                $(`[name=${nameFrom}]`, formObject).val("");
            }
        });
        $.extend(calendar_options, _options);
        // Xử lý trường hợp người dùng xóa input bằng bàn phím
        $(selector, formObject).on("input", function() {
            if ($(this).val().trim() === "") {
                $(this).val("").trigger("change");
                if (nameFrom) {
                    $(`[name=${nameFrom}]`, formObject).val("");
                }
            } else {
                cleanDateDisplay();
            }
        });
    } else {
        var calendar_options = {
            // singleDatePicker: true,
            showDropdowns: true,
            locale: {
                cancelLabel: $(selector, formObject).attr("data-cancel-lang"),
                applyLabel: $(selector, formObject).attr("data-apply-lang"),
                customRangeLabel: $(selector, formObject).attr("data-customrange-lang"),
                format: "DD/MM/YYYY",
            },
            ranges: ranges,
            startDate: moment().subtract(6, 'days'),
            endDate: moment(),
            minDate: moment(mindate, "DD/MM/YYYY"),
            maxDate: moment(),
            opens: opens,
            drops: "auto",
            linkedCalendars: false
        };
        $.extend(calendar_options, _options);
        $(selector, formObject).daterangepicker(calendar_options, function(start, end, label) {
            $(`[name=${nameFrom}]`, formObject).val(start.format('DD/MM/YYYY'));
            $(`[name=${nameTo}]`, formObject).val(end.format('DD/MM/YYYY'));
        });
        
    }
    function cleanDateDisplay() {
        var drpSelected = document.querySelector(".daterangepicker.single .drp-selected");
        if (drpSelected && drpSelected.innerText.includes(" - ")) {
            drpSelected.innerText = drpSelected.innerText.split(" - ")[0]; // Giữ lại ngày đầu tiên
        }
    }    

    // **Bắt đầu theo dõi sự thay đổi của `.drp-selected`**
    function observeDateChanges() {
        var targetNode = document.querySelector(".daterangepicker.single .drp-selected");
        if (!targetNode) return;

        var observer = new MutationObserver(function(mutationsList) {
            for (let mutation of mutationsList) {
                if (mutation.type === "childList") {
                    cleanDateDisplay(); // Khi nội dung thay đổi, tự động cắt chuỗi ngay lập tức
                }
            }
        });

        observer.observe(targetNode, { childList: true, subtree: true });
    }
}

function bl_checkSearchFormc(data) {
    var type = parseInt($("[name=type_couse]", formObject).val()), adv = parseInt($("[name=is_advance]").val()), is_error = false, error_mess = '', error_input = false;
    $(".has-error", formObject).removeClass("has-error");

    $("input[name][type=text]:not(.money-format)", formObject).each(function() {
        var v = trim($(this).val());
        v = v.replace(/\s+/g, ' ');
        if (v != '') {
            v = trim(strip_tags(v));
        }
        $(this).val(v);
        v2 = v.replace(/\s+/g, '');
        if (v != '' && v2.length < 2) {
            if (!is_error) {
                is_error = true;
                error_mess = $(this).attr("data-error");
                error_input = this;
            }
        }
    });

    if (is_error) {
        $("#sitemodal").on('hidden.bs.modal', function() {
            $(error_input).parent().addClass("has-error");
            $(error_input).focus()
        });
        modalShow('', error_mess);
        return false
    }
    if (type == 1) {
        $(".search_couses [name], .search_student [name]", formObject).removeAttr("name");
        formObject.attr("action", formObject.data("action"));
    }
    if (type == 2) {
        $(".search_student [name]", formObject).removeAttr("name");
        formObject.attr("action", formObject.data("action1"));
    }
    if (type == 3) {
        $(".search_couses [name]", formObject).removeAttr("name");
        formObject.attr("action", formObject.data("action2"));
    }
    $("input[name]", formObject).each(function() {
        if ($(this).val() == '') {
            $(this).removeAttr("name")
        }
    });
    return true;
}

function bl_changeTypeInfoc() {
    var type_couse = parseInt($("[name=type_couse]", formObject).val()), is_advance = parseInt($('input[name="is_advance"]').val());
    $(".search_couses, .search_student", formObject).hide();
    if (type_couse == 1) {
        bl_advanceBtnHide();
        formObject.attr("action", formObject.data("action"));
        bl_advanceHide();
    }
    if (type_couse == 2) {
        bl_advanceBtnShow();
        $(".search_couses", formObject).show();
        formObject.attr("action", formObject.data("action1"));
    }
    if (type_couse == 3) {
        bl_advanceBtnShow();
        $(".search_student", formObject).show();
        formObject.attr("action", formObject.data("action2"));
    }
}
function bl_changeTypeSearch() {
    reset_validate();
    var is_advance = parseInt($('input[name="is_advance"]').val());
    bl_advanceBtnShow();
    if (is_advance == 1) {
        bl_advanceShow();
    } else {
        bl_advanceHide();
    }
}
function bl_formReset() {
    $(".has-error", formObject).removeClass("has-error");
    $("[data-default]", formObject).each(function() {
        if ($(this).is("input[type=text], input[type=hidden]")) {
            $(this).val($(this).attr("data-default"))
        } else if ($(this).is("select")) {
            $(this).val($(this).attr("data-default"));
            $('#keyword_id_province').val(0).trigger('change.select2');
        } else if ($(this).is("input[type=radio]")) {
            if ($(this).attr("data-default") == -1) {
                $(this).prop('checked', false);
            } else {
                $(this).filter("[value=" + $(this).attr("data-default") + "]").prop('checked', true);
            }
        } else if ($(this).is("input[type=checkbox]")) {
            var regex = /^\s*(true|1|on)\s*$/i;
            $(this).prop('checked', regex.test($(this).attr("data-default")))
        }
    });
    //Không đóng form nâng cao khi reset form
    if (parseInt($('input[name="is_advance"]').val()) !== 1) {
        $(".btn-search", formObject).trigger('click');
    }
    bl_setDaterangepicker(".search_range_time", "sfrom_time_traning", "sto_time_traning", { startDate: $("[name=sfrom_time_traning]", formObject).val(), endDate: $("[name=sto_time_traning]", formObject).val() });
    bl_setDaterangepicker(".search_range", "sfrom_certificate_date", "sto_certificate_date", { startDate: $("[name=sfrom_certificate_date]", formObject).val(), endDate: $("[name=sto_certificate_date]", formObject).val() });
    $("[name=sfrom_birthday]", formObject).val("");
    bl_setDaterangepicker(".search_range_birthday", "sfrom_birthday", null, {
        startDate: ""
    });
    bl_setDaterangepicker(".search_range_issuedate", "sfrom_issuedate", "sto_issuedate", { startDate: $("[name=sfrom_issuedate]", formObject).val(), endDate: $("[name=sto_issuedate]", formObject).val() });
    $('html, body').animate({ scrollTop : formObject.offset().top }, 800);
}
$(function() {
    $('.fselect2').select2({ language : nv_lang_interface });
    is_advance = $('input[name="is_advance"]', formObject).val();
    // Thay thế các lệnh gọi cũ
    bl_setDaterangepicker(".search_range_time", "sfrom_time_traning", "sto_time_traning", { startDate: $("[name=sfrom_time_traning]", formObject).val(), endDate: $("[name=sto_time_traning]", formObject).val() });
    bl_setDaterangepicker(".search_range", "sfrom_certificate_date", "sto_certificate_date", { startDate: $("[name=sfrom_certificate_date]", formObject).val(), endDate: $("[name=sto_certificate_date]", formObject).val() });
    bl_setDaterangepicker(".search_range_birthday", "sfrom_birthday", null, {
        startDate: $("[name=sfrom_birthday]", formObject).val() || ""
    });
    bl_setDaterangepicker(".search_range_issuedate", "sfrom_issuedate", "sto_issuedate", { startDate: $("[name=sfrom_issuedate]", formObject).val(), endDate: $("[name=sto_issuedate]", formObject).val() });
    $("[name=typecourse]", formObject).on("change", function(a) {
        bl_changeTypeSearch();
    });
    $(".btn-search", formObject).click(function(a) {
        a.preventDefault();
        type_info = parseInt($("[name=type_couse]", formObject).val());
        parseInt($('input[name="is_advance"]', formObject).val()) ? bl_advanceHide() : bl_advanceShow()
    });
    if (is_advance == 1) {
        bl_advanceShow();
    } else {
        bl_advanceHide();
    }
    
});
function reset_validate() {
    $("button:contains('Tìm kiếm')").prop("disabled", false);
    $("input[value='Tìm kiếm']").prop("disabled", false);
}
