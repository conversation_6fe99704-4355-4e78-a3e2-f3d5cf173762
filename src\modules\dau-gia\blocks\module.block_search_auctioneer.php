<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES., JSC. All rights reserved
 * @Createdate 3/9/2010 23:25
 */

if (!defined('NV_MAINFILE'))
    die('Stop!!!');

if (!nv_function_exists('nv_search_blocks')) {
    function nv_search_blocks($block_config)
    {
        global $module_info, $module_name, $module_data, $nv_Request, $module_file, $nv_Cache, $global_array_config, $array_op, $db, $district_list, $ward_list, $province_list, $user_info, $nv_Lang;
        global $global_config;

        if (file_exists(NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file . "/block_search_auctioneer.tpl")) {
            $block_theme = $module_info['template'];
        } else {
            $block_theme = "default";
        }

        $xtpl = new XTemplate("block_search_auctioneer.tpl", NV_ROOTDIR . "/themes/" . $block_theme . "/modules/" . $module_file);
        $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

        if ($global_config['rewrite_enable']) {
            $xtpl->assign('FORM_ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['auctioneer'], true));
        } else {
            $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php');
            $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
            $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
            $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
            $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
            $xtpl->assign('NV_OP_VALUE', $module_info['alias']['auctioneer']);
            $xtpl->assign('MODULE_NAME', $module_name);
            $xtpl->parse('main.no_rewrite');
        }

        $name = $nv_Request->get_string('name', 'get', '');
        $cchn = $nv_Request->get_string('cchn', 'get', '');
        $province = $nv_Request->get_int('province', 'get', 0);
        $id_bidder = $nv_Request->get_int('bidder', 'get', 0);

        $a = $b = '';
        if (isset($array_op[1])) {
            $a = substr($array_op[1], 0, 1);
            $b = substr($array_op[1], 2);
            if(preg_match('/^([a-zA-Z0-9\-]+)\-([0-9]+)$/i', $b, $m)) {
                $b = $m[2];
            }
        }
        $module = "location";
        if ($a == "T") {
            $sql = "SELECT id FROM " . NV_PREFIXLANG . "_" . $module . "_province WHERE status=1 AND id = " . $db->quote($b);
            $result = $db->query($sql);
            list($_id) = $result->fetch(3);
            $province = $_id;
        }

        //province
        $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province";
        $province_list = $nv_Cache->db($sql, 'id', 'location');
        foreach ($province_list as $province_list_i) {
            $sl = ($province == $province_list_i['id']) ? "selected='selected'" : "";
            $arr_province[] = array(
                "id" => $province_list_i['id'],
                "title" => $province_list_i['title'],
                "sl" => $sl);
        }

        foreach ($arr_province as $province_i) {
            $xtpl->assign('sl_province', $province_i['sl']);
            $xtpl->assign('key_province', $province_i['id']);
            $xtpl->assign('val_province', $province_i['title']);
            $xtpl->parse('main.province');

        }

        //Tổ chức đấu giá
        if ($id_bidder > 0) {
            $sql = "SELECT id_bidder, name_bidder FROM " . "nv4_dau_gia_bidder WHERE id_bidder = " . $id_bidder . " ORDER BY name_bidder DESC";
            $result = $db->query($sql);
            while ($row2 = $result->fetch()) {
                $array_bidder[$row2['id_bidder']] = $row2;
            }
            foreach ($array_bidder as $key => $bidder) {
                $xtpl->assign('BIDDER', array(
                    'key' => $key,
                    'name_bidder' => $bidder['name_bidder'],
                    "selected" => $key == $id_bidder ? 'selected="selected"' : ''
                ));
                $xtpl->parse('main.bidder');
            }
        }

        $xtpl->assign('name', $name);
        $xtpl->assign('cchn', $cchn);
        $xtpl->parse('main');
        return $xtpl->text('main');
    }
}

if (defined('NV_SYSTEM')) {
    $content = nv_search_blocks($block_config);
}
