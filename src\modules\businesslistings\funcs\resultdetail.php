<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_IS_MOD_BUSINESSLISTINGS'))
    die('Stop!!!');

use NukeViet\Dauthau\Url;
use NukeViet\Dauthau\Share;
$title_lock = Share::langTitleUnlock();
$key_words = $module_info['keywords'];
$module_industry = $site_mods[$global_array_config['module_industry']];

$temp_id = isset($array_op[1]) ? $array_op[1] : "";

if (!empty($temp_id)) {
    $array_id = explode('-', $temp_id);
    $id = intval(end($array_id));
}

if ($id == 0) {
    nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
}

$sql = "SELECT * FROM " . BUSINESS_PREFIX_GLOBAL . "_info WHERE active = 1 AND id = " . $id;
$result = $db->query($sql);
$row = $result->fetch();
$global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias((NV_LANG_DATA == 'vi' && !empty(trim($row['officialname']))) ? $row['officialname'] : $row['companyname']) . '-' . $row['id'];
(NV_LANG_DATA != 'vi' && !empty(trim($row['officialname']))) && $row['companyname'] = $row['officialname'];
if (empty($row)) {
    nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
}

$page_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($row['companyname']) . '-' . $row['id'];

$page_title = sprintf($nv_Lang->getModule('listresult_old'), $row['companyname']);
$array_mod_title[] = array(
    'title' => $row['companyname'],
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=detail/' . change_alias($row['companyname']) . '-' . $row['id'], true)
);
$array_mod_title[] = array(
    'title' => $page_title,
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($row['companyname']) . '-' . $row['id'], true)
);
// province
$sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province";
$province_list = $nv_Cache->db($sql, 'id', 'location');
$province_list[0] = [
    'id' => 0,
    'title' => $nv_Lang->getModule('undefined'),
    'alias' => change_alias($nv_Lang->getModule('undefined'))
];

$province_list[824] = [
    'id' => 824,
    'title' => $nv_Lang->getModule('nationwide'),
    'alias' => change_alias($nv_Lang->getModule('nationwide'))
];

$province_list[825] = [
    'id' => 825,
    'title' => $nv_Lang->getModule('vn_out_territory'),
    'alias' => change_alias($nv_Lang->getModule('vn_out_territory'))
];

if (!defined('NV_IS_USER')) {
    if ($row['hide_info'] == 1 && $row['time_hide_end'] >= NV_CURRENTTIME) {
        $nv_BotManager->setPrivate();
        $contents = user_info_exit($nv_Lang->getModule('hide_info'));
    } else {
        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $link = vsprintf($nv_Lang->getModule('view_result_one'), array(
            $link_register,
            nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3')
        ));

        $chart_linkview = sprintf($nv_Lang->getModule('notif_view_dropdown_not_user'), $link_register);
        $contents = get_list(3, false, $link, $chart_linkview);
    }
    
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
    die();
}

$view_all_content = false;
if (isset($global_array_vip[3])) {
    $arr_customs = $global_array_vip[3];
    define('NV_IS_VIP3', true);
} elseif (isset($global_array_vip[31])) {
    $arr_customs = $global_array_vip[31];
    define('NV_IS_VIP3', true);
} elseif (isset($global_array_vip[100])) {
    // Nếu có sử dụng gói T100 sẽ được xem toàn bộ dữ liệu với nhà thầu mà họ đã tải file pdf
    $_request_pdf = $db->query("SELECT id FROM " . NV_PREFIXLANG . "_" . $module_data . "_exportpdf_request WHERE id_business =" . $id . ' AND userid=' . $user_info['userid'])->fetchColumn();
    $view_all_content = !empty($_request_pdf) ? true : false;
}

if (!empty($arr_customs_permission) || !empty($global_array_vip)) {
    define('NV_IS_ANY_VIP', true);
}

if (empty($arr_customs) and !$view_all_content) {
    // Kiểm tra nếu gói VIP 3 hết hạn
    $vip3_renew = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id=' . $user_info['userid'] . ' AND status != 1 AND (vip = 3 OR vip = 31) AND prefix_lang = ' . BID_LANG_DATA)->fetch();
    if (!empty($vip3_renew)) {
        $chart_linkview = sprintf($nv_Lang->getModule('notif_view_dropdown_vip_expired'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3');
    } else {
        $chart_linkview = sprintf($nv_Lang->getModule('notif_view_dropdown_not_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
    }

    $link = sprintf($nv_Lang->getModule('view_result_user_view'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
    if ($row['hide_info'] == 1 && $row['time_hide_end'] >= NV_CURRENTTIME) {
        $contents = user_info_exit($link);
    } else {
        $contents = get_list(3, false, $link, $chart_linkview);
    }

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
    die();
}

$contents = get_list(20, true);
include (NV_ROOTDIR . "/includes/header.php");
echo nv_site_theme($contents);
include (NV_ROOTDIR . "/includes/footer.php");

function get_list($per_page, $check_user = false, $link_register = "", $chart_linkview = '')
{
    global $module_name, $row, $op, $ord, $db, $nv_Request, $site_mods, $global_array_config, $global_config, $prevPage, $nextPage, $canonicalUrl, $config_bidding, $elas_max, $province_list, $global_lang_url, $vip3_renew, $nv_Lang;
    $bidding_module_info_vi = nv_site_mods('vi')['bidding'];
    // kiểm tra quyền truy cập
    if ($row['hide_info'] == 1 and $row['hide_vip3'] == 1) {
        nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
    }
    // $per_page = 1;
    $link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=bidding&amp;" . NV_OP_VARIABLE . "=";

    $page = $nv_Request->get_page('page', 'post,get', 1);
    $ord = $nv_Request->get_int('order', 'post,get', 7);
    $prov = $nv_Request->get_int('prov', 'post,get', '-1');
    $package = $nv_Request->get_int('package', 'post,get', 1);
    if ($check_user === false) {
        $page = 1;
    }

    $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($row['companyname']) . '-' . $row['id'] . '&order=' . $ord;
    $global_lang_url .= '&order=' . $ord;
    $prov >= 0 && $base_url .= '&prov=' . $prov;
    $prov >= 0 && $global_lang_url .= '&prov=' . $prov;
    $package >= 0 && $base_url .= '&package=' . $package;
    $package >= 0 && $global_lang_url .= '&package=' . $package;

    ($prov >= 0 && $nv_Request->isset_request('ajax', 'post, get')) && $page = 1;
    ($package >= 0 && $nv_Request->isset_request('ajax', 'post, get')) && $page = 1;
    $page_url = $base_url;
    if ($page > 1) {
        $page_url .= '&amp;page=' . $page;
        $global_lang_url .= '&amp;page=' . $page;
    }
    $canonicalUrl = getCanonicalUrl($page_url);

    $array_order = array();
    $array_order[0] = $nv_Lang->getModule('select_0');
    $array_order[7] = $nv_Lang->getModule('select_11');
    $array_order[8] = $nv_Lang->getModule('select_12');
    $array_order[1] = $nv_Lang->getModule('select_1');
    $array_order[2] = $nv_Lang->getModule('select_2');
    $array_order[3] = $nv_Lang->getModule('select_3');
    $array_order[4] = $nv_Lang->getModule('select_4');
    $array_order[5] = $nv_Lang->getModule('select_5');
    $array_order[6] = $nv_Lang->getModule('select_6');

    $request = [
        'id' => $row['id'],
        'businessdetail' => 1 // Lấy dữ liệu cho function detail này
    ];
    $_data = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'GetDataContractor', $request, 'businesslistings', 1);

    if ($_data['status'] == 'success') {
        $add_data = json_decode($_data['add_data'], true);

        // Xử lý các placeholder các alias của các functions
        $add_data = nv_str_replace_recursion([
            '#openalias#',
            '#viewopenalias#',
            '#resultalias#',
            '#viewalias#',
            '#nv_base_siteurl#',
            '#nv_lang_data#',
            '#lang_companyname_alias#'
        ], [
            $bidding_module_info_vi['alias']['open'],
            $bidding_module_info_vi['alias']['viewopen'],
            $bidding_module_info_vi['alias']['result'],
            $bidding_module_info_vi['alias']['view'],
            NV_BASE_SITEURL,
            NV_LANG_DATA,
            change_alias($row['companyname'])
        ], $add_data);
        $arr_row = $add_data['arr_row'];
        $arr_soclocitor = $add_data['arr_soclocitor'];
        $arr_row_data = $arr_row;

        // $array_bidding = $add_data['array_bidding'];
        $array_pq = $add_data['array_pq'];
        unset($add_data);
    } else {
        $arr_row_data = [];
        $array_pq = [];
        // 1. Danh sách các gói thầu đã tham gia:
        $so_dkkd = $row['code'];
        $_sql = "SELECT so_tbmt, so_dkkd, ten_nha_thau, gia_sau_giam, partnership, gia_du_thau FROM " . NV_PREFIXLANG . "_bidding_open_detail WHERE so_dkkd = '" . $so_dkkd . "' ORDER BY so_tbmt DESC";
        $result_bidding = $db->query($_sql);
        $array_thamgia_thau = array();
        $array_bidding_open = array();
        while ($tmp = $result_bidding->fetch()) {
            $tmp['so_tbmt'] = str_replace(' ', '', $tmp['so_tbmt']);
            $array_thamgia_thau[$tmp['so_tbmt']] = $tmp['so_tbmt'];
            $array_bidding_open[$tmp['so_tbmt']]['code'] = $tmp['so_tbmt'];
            $array_bidding_open[$tmp['so_tbmt']]['bidder_name'] = $tmp['ten_nha_thau'];
            $array_bidding_open[$tmp['so_tbmt']]['no_business_licence'] = $tmp['so_dkkd'];
            $array_bidding_open[$tmp['so_tbmt']]['win_price'] = ($tmp['gia_du_thau'] == 0) ? $tmp['gia_sau_giam'] : $tmp['gia_du_thau'];
            $array_bidding_open[$tmp['so_tbmt']]['plan_code'] = '';
            $array_bidding_open[$tmp['so_tbmt']]['investor'] = '';
            $array_bidding_open[$tmp['so_tbmt']]['solicitor_id'] = 0;
            $array_bidding_open[$tmp['so_tbmt']]['title'] = '';
            $array_bidding_open[$tmp['so_tbmt']]['finish_time'] = '';
            $array_bidding_open[$tmp['so_tbmt']]['id'] = 0;
            $array_bidding_open[$tmp['so_tbmt']]['trung_thau'] = 3;
            $array_bidding_open[$tmp['so_tbmt']]['link_result'] = $link . $site_mods['bidding']['alias']['open'] . '&q=' . $tmp['so_tbmt'];
            $array_bidding_open[$tmp['so_tbmt']]['partnership'] = $tmp['partnership'];
        }

        // lấy danh sách các gói thầu đã trúng thầu
        $array_result = array();
        $_sql = "SELECT t1.id, t1.code, t1.title, t1.investor, t1.solicitor_id, t2.no_business_licence, t1.bidder_name, t1.win_price, t2.bidwinningprice, t1.plan_code, t1.finish_time, t2.partnership, t1.open_time FROM " . NV_PREFIXLANG . "_bidding_result as t1 INNER JOIN " . NV_PREFIXLANG . "_bidding_result_business as t2 ON t1.id=t2.resultid WHERE t2.no_business_licence = '" . $so_dkkd . "' ORDER BY t1.finish_time DESC";
        $result_bidding = $db->query($_sql);
        $array_bidding = $array_solicitor_id = array();
        while ($tmp = $result_bidding->fetch()) {
            $tmp['trung_thau'] = 1;
            $tmp['code'] = str_replace(' ', '', $tmp['code']);
            $tmp['win_price'] = ($tmp['bidwinningprice'] > 0) ? convert_vnd($tmp['bidwinningprice']) : convert_vnd($tmp['win_price']);
            $tmp['link_result'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['result'] . '&id=' . $tmp['id'];
            $array_bidding[$tmp['code']] = $tmp;
            $array_solicitor_id[$tmp['solicitor_id']] = $tmp['solicitor_id'];
            $array_result[$tmp['code']] = $tmp['code'];
            $array_bidding_open[$tmp['code']]['solicitor_id'] = $tmp['solicitor_id'];
        }

        $arr_row = array_merge($array_bidding_open, $array_bidding);

        if (!empty($array_thamgia_thau)) {
            if ($config_bidding['elas_use']) {
                $nukeVietElasticSearh = new \NukeViet\ElasticSearch\Functions($config_bidding['elas_host'], $config_bidding['elas_port'], NV_LANG_ELASTIC . 'dauthau_open', $config_bidding['elas_user'], $config_bidding['elas_pass']);
                $size = 0;
                // Chia làm nhiều lần tìm kiếm
                while ($size < sizeof($array_thamgia_thau)) {
                    $array_query_elastic = $search_elastic = [];
                    foreach (array_slice($array_thamgia_thau, $size, $elas_max) as $code) {
                        $search_elastic['should'][] = [
                            'match' => [
                                'so_tbmt.keyword' => $code
                            ]
                        ];
                    }
                    if (!empty($search_elastic)) {
                        $search_elastic['minimum_should_match'] = '1';
                        $search_elastic['boost'] = '1.0';
                        $array_query_elastic['query']['bool'] = $search_elastic;
                        $array_query_elastic['from'] = 0;
                        $array_query_elastic['size'] = 100000;
                        $array_query_elastic['_source'] = [
                            'so_tbmt',
                            'solicitor_id',
                            'ben_moi_thau',
                            'thoi_diem_hoan_thanh',
                            'thoi_diem_mo_thau',
                            'goi_thau'
                        ];
                        $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_bidding_open', $array_query_elastic);
                    }
                    foreach ($response['hits']['hits'] as $value) {
                        if (!empty($value['_source'])) {
                            $_row = $value['_source'];
                            $arr_row[$_row['so_tbmt']]['title'] = $_row['goi_thau'];
                            $arr_row[$_row['so_tbmt']]['investor'] = $_row['ben_moi_thau'];
                            $arr_row[$_row['so_tbmt']]['solicitor_id'] = $_row['solicitor_id'];
                            // Trường hợ chưa có kết quả sẽ lấy thời gian mở thầu
                            if ($arr_row[$_row['so_tbmt']]['finish_time'] == "") {
                                $arr_row[$_row['so_tbmt']]['finish_time'] = $_row['thoi_diem_mo_thau'];
                            }
                            $array_solicitor_id[$_row['solicitor_id']] = $_row['solicitor_id'];
                        }
                    }
                    $size += $elas_max;
                }
            } else {
                $result_open = $db->query("SELECT so_tbmt, solicitor_id, goi_thau, ben_moi_thau, thoi_diem_hoan_thanh, thoi_diem_mo_thau  FROM " . NV_PREFIXLANG . "_bidding_open WHERE so_tbmt IN ('" . implode("','", $array_thamgia_thau) . "')");
                while ($_row = $result_open->fetch()) {
                    $arr_row[$_row['so_tbmt']]['title'] = $_row['goi_thau'];
                    $arr_row[$_row['so_tbmt']]['investor'] = $_row['ben_moi_thau'];
                    $arr_row[$_row['so_tbmt']]['solicitor_id'] = $_row['solicitor_id'];
                    // Trường hợ chưa có kết quả sẽ lấy thời gian mở thầu
                    if ($arr_row[$_row['so_tbmt']]['finish_time'] == "") {
                        $arr_row[$_row['so_tbmt']]['finish_time'] = $_row['thoi_diem_mo_thau'];
                    }
                    $array_solicitor_id[$_row['solicitor_id']] = $_row['solicitor_id'];
                }
            }
            $array_thamgia_not_result = array_diff($array_thamgia_thau, $array_result);
            // $_result = $db->query("SELECT id, code, finish_time FROM " . NV_PREFIXLANG . "_bidding_result WHERE code IN ('" . implode("','", $array_thamgia_not_result) . "')");
            // while ($_row = $_result->fetch()) {
            // $arr_row[$_row['code']]['finish_time'] = $_row['finish_time'];
            // $arr_row[$_row['code']]['trung_thau'] = 2;
            // $arr_row[$_row['code']]['link_result'] = $link . $site_mods['bidding']['alias']['result'] . '&id=' . $_row['id'];
            // }
            // Kiểm tra gói thầu có bị huỷ không bằng cách kiểm tra trong bảng bidding_result và bảng bidding_detail
            if (!empty($array_thamgia_not_result)) {
                foreach ($array_thamgia_not_result as $code) {
                    $array_thamgia_not_result_sql[] = $db->quote($code);
                }
                $array_result_fail = [];
                if ($config_bidding['elas_result_use']) {
                    $nukeVietElasticSearh = new \NukeViet\ElasticSearch\Functions($config_bidding['elas_result_host'], $config_bidding['elas_result_port'], NV_LANG_ELASTIC . 'dauthau_result', $config_bidding['elas_result_user'], $config_bidding['elas_result_pass']);
                    $size = 0;
                    // Chia làm nhiều lần tìm kiếm
                    while ($size < sizeof($array_thamgia_not_result)) {
                        $array_query_elastic = $search_elastic = [];
                        foreach (array_slice($array_thamgia_not_result, $size, $elas_max) as $code) {
                            $search_elastic['should'][] = [
                                'match' => [
                                    'code.keyword' => $code
                                ]
                            ];
                        }
                        if (!empty($search_elastic)) {
                            $search_elastic['minimum_should_match'] = '1';
                            $search_elastic['boost'] = '1.0';
                            $array_query_elastic['query']['bool'] = $search_elastic;
                            $array_query_elastic['from'] = 0;
                            $array_query_elastic['size'] = 100000;
                            $array_query_elastic['_source'] = [
                                'id',
                                'code',
                                'time_cancel'
                            ];
                            $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_bidding_result', $array_query_elastic);
                        }
                        foreach ($response['hits']['hits'] as $value) {
                            if (!empty($value['_source'])) {
                                $_row = $value['_source'];
                                if (!empty($_row['time_cancel'])) {
                                    $arr_row[$_row['code']]['trung_thau'] = 4;
                                } else {
                                    $arr_row[$_row['code']]['trung_thau'] = 2;
                                }
                                $arr_row[$_row['code']]['link_result'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['result'] . '&id=' . $_row['id'];
                                $array_result_fail[$_row['code']] = $_row['code'];
                            }
                        }
                        $size += $elas_max;
                    }
                } else {
                    $_result = $db->query("SELECT id, code, time_cancel FROM " . NV_PREFIXLANG . "_bidding_result WHERE code IN (" . implode(",", $array_thamgia_not_result_sql) . ')');
                    while ($_row = $_result->fetch()) {
                        if (!empty($_row['time_cancel'])) {
                            $arr_row[$_row['code']]['trung_thau'] = 4;
                        } else {
                            $arr_row[$_row['code']]['trung_thau'] = 2;
                        }
                        $arr_row[$_row['code']]['link_result'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['result'] . '&id=' . $_row['id'];
                        $array_result_fail[$_row['code']] = $_row['code'];
                    }
                }
                // if (!empty($array_thamgia_thau)) {
                // $array_thamgia_thau_sql = [];
                // foreach ($array_thamgia_thau as $code) {
                // $array_thamgia_thau_sql[] = 'so_tbmt LIKE ' . $db->quote(explode('-', $code)[0] . '%');
                // }
                // $_result = $db->query("SELECT id, so_tbmt FROM " . NV_PREFIXLANG . "_bidding_row WHERE " . implode(" OR ", $array_thamgia_thau_sql) . " ORDER BY so_tbmt");
                // while ($_row = $_result->fetch()) {
                // $_arr_row_table[$_row['id']] = $_row;
                // }
                // if (!empty($_arr_row_table)) {
                // $_result = $db->query("SELECT id, hinh_thuc_thong_bao FROM " . NV_PREFIXLANG . "_bidding_detail WHERE id IN (" . implode(",", array_keys($_arr_row_table)) . ") ORDER BY id");
                // while ($_row = $_result->fetch()) {
                // if (in_array($_row['hinh_thuc_thong_bao'], ['Thông báo đã bị huỷ', 'Thông báo đã bị hủy'])) {
                // foreach (array_keys($arr_row) as $so_tbmt) {
                // if (explode('-', $so_tbmt)[0] == explode('-', $_arr_row_table[$_row['id']]['so_tbmt'])[0]) {
                // $arr_row[$so_tbmt]['trung_thau'] = 4;
                // $arr_row[$so_tbmt]['link_result'] = $link . $site_mods['bidding']['alias']['view'] . '/' . Url::getTBMT() . '/' . strtolower(change_alias($arr_row[$so_tbmt]['title'])) . '-' . $_row['id'] . $global_config['rewrite_exturl'];
                // break;
                // }
                // }
                // }
                // }
                // }
                // }
            }
        }

        $arr_soclocitor = array();
        if (!empty($array_solicitor_id)) {
            $_result_solicitor = $db->query("SELECT id, alias, title, solicitor_code FROM " . BID_PREFIX_GLOBAL . "_solicitor WHERE id IN (" . implode(',', $array_solicitor_id) . ")");
            while ($_row = $_result_solicitor->fetch()) {
                $arr_soclocitor[$_row['id']]['id'] = $_row['id'];
                $arr_soclocitor[$_row['id']]['title'] = $_row['title'];
                $arr_soclocitor[$_row['id']]['alias'] = $_row['alias'];
                $arr_soclocitor[$_row['id']]['link_detail'] = $link . 'detailinvestor/' . change_alias($row['companyname']) . '-' . $row['id'] . '/' . $_row['id'];
            }
        }
        $arr_show_key = array_keys($arr_row);
        $arr_bidding_row = array();
        if (!empty($arr_show_key)) {
            if ($config_bidding['elas_use']) {
                $nukeVietElasticSearh = new \NukeViet\ElasticSearch\Functions($config_bidding['elas_host'], $config_bidding['elas_port'], NV_LANG_ELASTIC . 'dauthau_bidding', $config_bidding['elas_user'], $config_bidding['elas_pass']);
                $size = 0;
                // Chia làm nhiều lần tìm kiếm
                while ($size < sizeof($arr_show_key)) {
                    $array_query_elastic = $search_elastic = [];
                    foreach (array_slice($arr_show_key, $size, $elas_max) as $code) {
                        $search_elastic['should'][] = [
                            'match' => [
                                'so_tbmt.keyword' => $code
                            ]
                        ];
                    }
                    if (!empty($search_elastic)) {
                        $search_elastic['minimum_should_match'] = '1';
                        $search_elastic['boost'] = '1.0';
                        $array_query_elastic['query']['bool'] = $search_elastic;
                        $array_query_elastic['from'] = 0;
                        $array_query_elastic['size'] = 100000;
                        $array_query_elastic['_source'] = [
                            'id',
                            'so_tbmt',
                            'alias',
                            'goi_thau',
                            'ben_moi_thau',
                            'hinh_thuc_thong_bao'
                        ];
                        $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_bidding_row', $array_query_elastic);
                    }
                    foreach ($response['hits']['hits'] as $value) {
                        if (!empty($value['_source'])) {
                            $_row = $value['_source'];
                            $arr_bidding_row[$_row['so_tbmt']] = $_row;
                            if (in_array($_row['hinh_thuc_thong_bao'], [
                                'Thông báo đã bị huỷ',
                                'Thông báo đã bị hủy'
                            ])) {
                                $arr_row[$_row['so_tbmt']]['trung_thau'] = 4;
                                $arr_row[$_row['so_tbmt']]['link_result'] = $link . $site_mods['bidding']['alias']['view'] . '/' . Url::getTBMT() . '/' . $_row['alias'] . '-' . $_row['id'] . $global_config['rewrite_exturl'];
                            }
                        }
                    }
                    $size += $elas_max;
                }
            } else {
                $bidding_row = $db->query("SELECT id, goi_thau, so_tbmt, ben_moi_thau, hinh_thuc_thong_bao FROM " . NV_PREFIXLANG . "_bidding_row WHERE so_tbmt IN ('" . implode("','", $arr_show_key) . "')");
                while ($_row = $bidding_row->fetch()) {
                    $arr_bidding_row[$_row['so_tbmt']] = $_row;
                    if (in_array($_row['hinh_thuc_thong_bao'], [
                        'Thông báo đã bị huỷ',
                        'Thông báo đã bị hủy'
                    ])) {
                        $arr_row[$_row['so_tbmt']]['trung_thau'] = 4;
                        $arr_row[$_row['so_tbmt']]['link_result'] = $link . $site_mods['bidding']['alias']['view'] . '/' . Url::getTBMT() . '/' . $_row['alias'] . '-' . $_row['id'] . $global_config['rewrite_exturl'];
                    }
                }
            }
        }
    }

    // đếm số lượng gói trúng, trượt, chưa có kq
    $number_result = array();
    $number_result['result1'] = 0;
    $number_result['result2'] = 0;
    $number_result['result3'] = 0;
    $number_result['result4'] = 0;

    $array_prov_all = [];
    if (NV_LANG_DATA != 'vi') {
        foreach ($arr_soclocitor as $key => $value) {
            !isset($value['english_name']) && $value['english_name'] = '';
            if (!empty(trim($value['english_name']))) {
                $arr_soclocitor[$key]['title'] = $value['english_name'];
                $arr_soclocitor[$key]['alias'] = strtolower(change_alias($value['english_name']));
            }
        }
    }

    foreach ($arr_row as $_so_tbmt => $_row) {
        if (is_array($_row['province_id'])) {
            foreach ($_row['province_id'] as $v) {
                $array_prov_all[] = $v;
            }
            if ($prov >= 0 && !in_array($prov, $_row['province_id'])) {
                unset($arr_row[$_so_tbmt]);
                continue;
            }
        } else {
            $array_prov_all[] = $_row['province_id'];
            if ($prov >= 0 && isset($_row['province_id']) && $_row['province_id'] != $prov) {
                unset($arr_row[$_so_tbmt]);
                continue;
            }
        }

        ++$number_result['result' . $_row['trung_thau']];
        // if (array_key_exists($_so_tbmt, $arr_bidding_row)) {
        // $arr_row[$_so_tbmt]['link'] = $link . $site_mods['bidding']['alias']['view'] . '/' . Url::getTBMT() . '/' . strtolower(change_alias($arr_bidding_row[$_row['code']]['goi_thau'])) . '-' . $arr_bidding_row[$_row['code']]['id'] . $global_config['rewrite_exturl'];
        // $arr_row[$_so_tbmt]['title'] = $arr_bidding_row[$_row['code']]['goi_thau'];
        // $arr_row[$_so_tbmt]['investor'] = $arr_bidding_row[$_row['code']]['ben_moi_thau'];
        // // if ($arr_row[$_so_tbmt]['finish_time'] == 0) {
        // // $arr_row[$_so_tbmt]['finish_time'] = $arr_bidding_row[$_row['code']]['den_ngay'];
        // // }
        // } else {
        // $arr_row[$_so_tbmt]['link'] = $link . 'detail&q=' . $_row['code'];
        // }

        if (array_key_exists($_row['solicitor_id'], $arr_soclocitor)) {
            $arr_row[$_so_tbmt]['link_solicitor'] = $link . $site_mods['bidding']['alias']['solicitor'] . '/' . $arr_soclocitor[$_row['solicitor_id']]['alias'] . '-' . $_row['solicitor_id'];
            $arr_row[$_so_tbmt]['investor'] = $arr_soclocitor[$_row['solicitor_id']]['title'];
        }

        if (isset($_row['province_id'])) {
            if (is_array($_row['province_id'])) {
                $arr_row[$_so_tbmt]['province'] = array_map(function ($a) use ($province_list, $nv_Lang) {
                    if (isset($province_list[$a])) {
                        return $province_list[$a]['title'];
                    } else {
                        return $nv_Lang->getModule('undefined');
                    }
                }, $_row['province_id']);
                $arr_row[$_so_tbmt]['province'] = implode(', ', $arr_row[$_so_tbmt]['province']);
            } elseif (isset($province_list[$_row['province_id']])) {
                $arr_row[$_so_tbmt]['province'] = $province_list[$_row['province_id']]['title'];
            } else {
                $arr_row[$_so_tbmt]['province'] = $nv_Lang->getModule('undefined');
            }
        } else {
            $arr_row[$_so_tbmt]['province'] = $nv_Lang->getModule('undefined');
        }
        if ($arr_row[$_so_tbmt]['type'] == 'pq') {
            $arr_row[$_so_tbmt]['link_result'] = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=vi&amp;" . NV_NAME_VARIABLE . "=bidding&amp;" . NV_OP_VARIABLE . "=" . $bidding_module_info_vi['alias']['resultpq'] . '/' . Url::URL_NT[NV_LANG_DATA] . '/' . strtolower(change_alias($array_pq[$_so_tbmt]['title'])) . '-' . $arr_row[$_so_tbmt]['result_id'] . $global_config['rewrite_exturl'];
            $arr_row[$_so_tbmt]['title'] = $array_pq[$_so_tbmt]['title'];
            $arr_row[$_so_tbmt]['win_price'] = '-';
        }
    }
    $array_prov_all = array_unique($array_prov_all);
    $number_result['total'] = sizeof($arr_row);
    $num_items = $number_result['total'];
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);

    // Đánh số trang
    $urlappend = '&amp;page=';
    betweenURLs($page, ceil($num_items / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

    $_arr_row = array();
    $array_data = $row;

    array_msort($arr_row, "finish_time", 'desc');

    usort($arr_row, function ($a, $b) {
        if ($a['trung_thau'] == $b['trung_thau']) {
            return $b['finish_time'] <=> $a['finish_time'];
        }
        return $a['trung_thau'] <=> $b['trung_thau'];
    });

    // Dữ liệu biểu đồ Tổng giá trị trúng thầu và tổng giá trị các gói dự thầu
    $number_result['total_win_price'] = 0;
    $number_result['total_desire'] = 0;
    $number_result['total_doclap'] = 0;
    $array_data['value_chart_desire'] = [];
    $array_data['value_chart_real'] = [];
    $array_data['value_chart_inde'] = [];

    // Chỉ các gói chỉ định thầu
    $number_result['total_win_price_cdt'] = 0;
    $number_result['total_desire_cdt'] = 0;
    $number_result['total_inde_cdt'] = 0;
    $array_data['value_chart_desire_cdt'] = [];
    $array_data['value_chart_real_cdt'] = [];
    $array_data['value_chart_inde_cdt'] = [];

    // Chỉ các gói có KQLCNT mà không có TBMT & KHLCNT
    $number_result['total_win_price_not_tbmt'] = 0;
    $number_result['total_desire_not_tbmt'] = 0;
    $number_result['total_inde_not_tbmt'] = 0;
    $array_data['value_chart_desire_not_tbmt'] = [];
    $array_data['value_chart_real_not_tbmt'] = [];
    $array_data['value_chart_inde_not_tbmt'] = [];

    $number_result_cdt = array();
    $number_result_cdt['result1'] = $number_result_cdt['result2'] = $number_result_cdt['result3'] = $number_result_cdt['result4'] = 0;
    $number_result_not_tbmt = array();
    $number_result_not_tbmt['result1'] = $number_result_not_tbmt['result2'] = $number_result_not_tbmt['result3'] = $number_result_not_tbmt['result4'] = 0;

    $_data_not_tbmt_arr = [];
    $_data_cdt_arr = [];

    foreach ($arr_row_data as $_so_tbmt => $_row) {
        if (is_array($_row['province_id'])) {
            if ($prov >= 0 && !in_array($prov, $_row['province_id'])) {
                unset($arr_row[$_so_tbmt]);
                continue;
            }
        } else {
            if ($prov >= 0 && isset($_row['province_id']) && $_row['province_id'] != $prov) {
                unset($arr_row[$_so_tbmt]);
                continue;
            }
        }
        if (!array_key_exists($_so_tbmt, $array_pq) || $_row['type'] != 'pq') {
            if (isset($_row['province_id'])) {
                if (is_array($_row['province_id'])) {
                    $_row['province'] = array_map(function ($a) use ($province_list, $nv_Lang) {//
                        if (isset($province_list[$a])) {
                            return $province_list[$a]['title'];
                        } else {
                            return $nv_Lang->getModule('undefined');
                        }
                    }, $_row['province_id']);
                    $_row['province'] = implode(', ', $_row['province']);
                } elseif (isset($province_list[$_row['province_id']])) {
                    $_row['province'] = $province_list[$_row['province_id']]['title'];
                } else {
                    $_row['province'] = $nv_Lang->getModule('undefined');
                }
            } else {
                $_row['province'] = $nv_Lang->getModule('undefined');
            }
            if (isset($_row['type_choose_id'])) {
                if ($_row['type_choose_id'] == 13 or $_row['type_choose_id'] == 14) {
                    ++$number_result_cdt['result' . $_row['trung_thau']];
                    $_data_cdt_arr[$_so_tbmt] = $_row;
                    $number_result['total_desire_cdt'] += !empty($arr_row_data[$_row['code']]['gia_goi_thau']) ? $arr_row_data[$_row['code']]['gia_goi_thau'] : (!empty($_row['win_price_number']) ? $_row['win_price_number'] : 0);
                    if ($_row['trung_thau'] == 1) {
                        $number_result['total_win_price_cdt'] += $_row['win_price_number']; // Tính tổng giá trị trúng thầu
                        if (isset($_row['partnership']) && $_row['partnership'] == 1) {
                            $number_result['total_inde_cdt'] += $_row['win_price_number'];
                        }
                    }
                    $array_data['value_chart_desire_cdt'][] = [
                        $_row['finish_time'] * 1000,
                        $number_result['total_desire_cdt']
                    ];
                    $array_data['value_chart_real_cdt'][] = [
                        $_row['finish_time'] * 1000,
                        $number_result['total_win_price_cdt']
                    ];
                    $array_data['value_chart_inde_cdt'][] = [
                        $_row['finish_time'] * 1000,
                        $number_result['total_inde_cdt']
                    ];
                }

                $type_choose_id = [
                    13,
                    14,
                    // 16,
                    19,
                    20,
                    23
                ];
                if (in_array($_row['type_choose_id'], $type_choose_id)) {
                    ++$number_result_not_tbmt['result' . $_row['trung_thau']];
                    $_data_not_tbmt_arr[$_so_tbmt] = $_row;
                    $number_result['total_desire_not_tbmt'] += !empty($arr_row_data[$_row['code']]['gia_goi_thau']) ? $arr_row_data[$_row['code']]['gia_goi_thau'] : (!empty($_row['win_price_number']) ? floatval($_row['win_price_number']) : 0);
                    if ($_row['trung_thau'] == 1) {
                        $number_result['total_win_price_not_tbmt'] += floatval($_row['win_price_number']); // Tính tổng giá trị trúng thầu
                        if (isset($_row['partnership']) && $_row['partnership'] == 1) {
                            $number_result['total_inde_not_tbmt'] += floatval($_row['win_price_number']);
                        }
                    }
                    $array_data['value_chart_desire_not_tbmt'][] = [
                        $_row['finish_time'] * 1000,
                        $number_result['total_desire_not_tbmt']
                    ];
                    $array_data['value_chart_real_not_tbmt'][] = [
                        $_row['finish_time'] * 1000,
                        $number_result['total_win_price_not_tbmt']
                    ];
                    $array_data['value_chart_inde_not_tbmt'][] = [
                        $_row['finish_time'] * 1000,
                        $number_result['total_inde_not_tbmt']
                    ];
                }
            }
            $number_result['total_desire'] += $arr_row_data[$_row['code']]['gia_goi_thau'] ?? ($_row['win_price_number'] ?? 0);
            if ($_row['trung_thau'] == 1) {
                $number_result['total_win_price'] += floatval($_row['win_price_number']); // Tính tổng giá trị trúng thầu
                if (isset($_row['partnership']) && $_row['partnership'] == 1) {
                    $number_result['total_doclap'] += floatval($_row['win_price_number']);
                }
            }
            $array_data['value_chart_desire'][] = [
                $_row['finish_time'] * 1000,
                $number_result['total_desire']
            ];
            $array_data['value_chart_real'][] = [
                $_row['finish_time'] * 1000,
                $number_result['total_win_price']
            ];
            $array_data['value_chart_inde'][] = [
                $_row['finish_time'] * 1000,
                $number_result['total_doclap']
            ];
        }
    }
    $order = $nv_Request->get_int('order', 'post, get', 7);

    if ($order == 1) {
        array_msort($arr_row, "investor", 'asc');
    } else if ($order == 2) {
        array_msort($arr_row, "investor", 'desc');
    } else if ($order == 3) {
        array_msort($arr_row, "win_price_number", 'desc');
    } else if ($order == 4) {
        array_msort($arr_row, "win_price_number", 'asc');
    } else if ($order == 5) {
        // TH trúng trượt
        array_msort($arr_row, "trung_thau", 'asc');
        usort($arr_row, function ($a, $b) {
            if ($a['trung_thau'] == $b['trung_thau']) {
                return $b['finish_time'] <=> $a['finish_time'];
            }
            return $a['trung_thau'] <=> $b['trung_thau'];
        });
    } else if ($order == 6) {
        // TH trượt trúng
        array_msort($arr_row, "trung_thau", 'desc');
        usort($arr_row, function ($a, $b) {
            if ($a['trung_thau'] == $b['trung_thau']) {
                return $b['finish_time'] <=> $a['finish_time'];
            }
            return $b['trung_thau'] <=> $a['trung_thau'];
        });
    } else if ($order == 7) {
        array_msort($arr_row, "finish_time", 'desc');
    } else if ($order == 8) {
        array_msort($arr_row, "finish_time", 'asc');
    }

    $arr_key_row = array_keys($arr_row);
    for ($j = (($page - 1) * $per_page); $j < ($page * $per_page); $j++) {
        if (isset($arr_key_row[$j]) && isset($arr_row[$arr_key_row[$j]])) {
            $_arr_row[$j + 1] = $arr_row[$arr_key_row[$j]];
        }
    }
    if (!empty($_arr_row)) {
        // Nếu ngôn ngữ không phải tiếng Việt thì vào CSDL lấy lại title và link
        $_result = $db->query('SELECT id, so_tbmt, goi_thau, alias FROM ' . NV_PREFIXLANG . '_bidding_row WHERE so_tbmt IN (' . implode(', ', array_map([$db, 'quote'], array_column($_arr_row, 'code'))) . ')');
        while ($_row = $_result->fetch()) {
            foreach ($_arr_row as $k => $v) {
                if ($v['code'] == $_row['so_tbmt']) {
                    $_arr_row[$k]['title'] = $_row['goi_thau'];
                    $_arr_row[$k]['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['view'] . '/' . Url::getTBMT() . '/' . $_row['alias'] . '-' . $_row['id'] . $global_config['rewrite_exturl'];
                }
            }
        }
        $_result->closeCursor();

        $_result = $db->query('SELECT so_tbmt, goi_thau, alias FROM ' . NV_PREFIXLANG . '_bidding_open WHERE so_tbmt IN (' . implode(', ', array_map([$db, 'quote'], array_column($_arr_row, 'code'))) . ')');
        while ($_row = $_result->fetch()) {
            foreach ($_arr_row as $k => $v) {
                if ($v['code'] == $_row['so_tbmt']) {
                    $_arr_row[$k]['title'] = $_row['goi_thau'];
                    $_arr_row[$k]['link_open'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['viewopen'] . '/' . $_row['alias'] . '-' . $_row['so_tbmt'] . $global_config['rewrite_exturl'];
                }
            }
        }
        $_result->closeCursor();

        $_result = $db->query('SELECT id, code, title, alias FROM ' . NV_PREFIXLANG . '_bidding_result WHERE code IN (' . implode(', ', array_map([$db, 'quote'], array_column($_arr_row, 'code'))) . ')');
        while ($_row = $_result->fetch()) {
            foreach ($_arr_row as $k => $v) {
                if ($v['code'] == $_row['code']) {
                    $_arr_row[$k]['title'] = $_row['title'];
                    $_arr_row[$k]['link_result'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['result'] . '/' . Url::getKQLCNT() . '/' . $_row['alias'] . '-' . $_row['id'] . $global_config['rewrite_exturl'];
                    preg_match('/op=' . $site_mods['bidding']['alias']['result'] . '/', $_arr_row[$k]['link']) && $_arr_row[$k]['link'] = $_arr_row[$k]['link_result'];
                }
            }
        }
        $_result->closeCursor();
    }

    if ($nv_Request->isset_request('order', 'post, get')) {
        $package_type = $nv_Request->get_int('package', 'post, get', 0);
        $view_chart = $nv_Request->get_int('view_chart', 'post, get', 1);
        if ($package_type == 2) {

            $arr_row = $_data_cdt_arr;
            $number_result['result1'] = $number_result_cdt['result1'];
            $number_result['result2'] = $number_result_cdt['result2'];
            $number_result['result3'] = $number_result_cdt['result3'];
            $number_result['result4'] = $number_result_cdt['result4'];
        } else if ($package_type == 3) {
            $arr_row = $_data_not_tbmt_arr;
            $number_result['result1'] = $number_result_not_tbmt['result1'];
            $number_result['result2'] = $number_result_not_tbmt['result2'];
            $number_result['result3'] = $number_result_not_tbmt['result3'];
            $number_result['result4'] = $number_result_not_tbmt['result4'];
        }
        $number_result['total'] = sizeof($arr_row);
        $num_items = $number_result['total'];
        $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
        if ($nv_Request->isset_request('ajax', 'post, get')) {
            $arr_key_row = array_keys($arr_row);
            $_arr_row = [];
            for ($j = (($page - 1) * $per_page); $j < ($page * $per_page); $j++) {
                if (isset($arr_key_row[$j]) && isset($arr_row[$arr_key_row[$j]])) {
                    $_arr_row[$j + 1] = $arr_row[$arr_key_row[$j]];
                }
            }
            if (!empty($_arr_row)) {
                // Nếu ngôn ngữ không phải tiếng Việt thì vào CSDL lấy lại title và link
                $_result = $db->query('SELECT id, so_tbmt, goi_thau, alias FROM ' . NV_PREFIXLANG . '_bidding_row WHERE so_tbmt IN (' . implode(', ', array_map([$db, 'quote'], array_column($_arr_row, 'code'))) . ')');
                while ($_row = $_result->fetch()) {
                    foreach ($_arr_row as $k => $v) {
                        if ($v['code'] == $_row['so_tbmt']) {
                            $_arr_row[$k]['title'] = $_row['goi_thau'];
                            $_arr_row[$k]['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['view'] . '/' . Url::getTBMT() . '/' . $_row['alias'] . '-' . $_row['id'] . $global_config['rewrite_exturl'];
                        }
                    }
                }
                $_result->closeCursor();

                $_result = $db->query('SELECT so_tbmt, goi_thau, alias FROM ' . NV_PREFIXLANG . '_bidding_open WHERE so_tbmt IN (' . implode(', ', array_map([$db, 'quote'], array_column($_arr_row, 'code'))) . ')');
                while ($_row = $_result->fetch()) {
                    foreach ($_arr_row as $k => $v) {
                        if ($v['code'] == $_row['so_tbmt']) {
                            $_arr_row[$k]['title'] = $_row['goi_thau'];
                            $_arr_row[$k]['link_open'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['viewopen'] . '/' . $_row['alias'] . '-' . $_row['so_tbmt'] . $global_config['rewrite_exturl'];
                        }
                    }
                }
                $_result->closeCursor();

                $_result = $db->query('SELECT id, code, title, alias FROM ' . NV_PREFIXLANG . '_bidding_result WHERE code IN (' . implode(', ', array_map([$db, 'quote'], array_column($_arr_row, 'code'))) . ')');
                while ($_row = $_result->fetch()) {
                    foreach ($_arr_row as $k => $v) {
                        if ($v['code'] == $_row['code']) {
                            $_arr_row[$k]['title'] = $_row['title'];
                            $_arr_row[$k]['link_result'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['result'] . '/' . Url::getKQLCNT() . '/' . $_row['alias'] . '-' . $_row['id'] . $global_config['rewrite_exturl'];
                            preg_match('/op=' . $site_mods['bidding']['alias']['result'] . '/', $_arr_row[$k]['link']) && $_arr_row[$k]['link'] = $_arr_row[$k]['link_result'];
                        }
                    }
                }
                $_result->closeCursor();
            }
            $contents = nv_ajax_resultdetail($array_data, $global_array_config, $_arr_row, $number_result, $array_order, $page, $per_page, $generate_page);

            if ($prov == -1) {
                $at_prov = $nv_Lang->getModule('on_all_nation');
            } elseif ($prov == 0) {
                $at_prov = sprintf($nv_Lang->getModule('at'), $nv_Lang->getModule('province') . ' ' . $province_list[$prov]['title']);
            } else {
                $at_prov = sprintf($nv_Lang->getModule('at'), $province_list[$prov]['title']);
            }

            $package_text = '';
            if ($package_type == 2) {
                $package_text = $nv_Lang->getModule('array_type_lc_14') . ' ';
            } elseif ($package_type == 3) {
                $package_text = $nv_Lang->getModule('pg_with_kqlcnt_without_tbmt_1') . ' ';
            }
            $lock_symbol = '<i class="fa fa-lock" aria-hidden="true"></i>';
            if ($check_user) {
                $content_prov = sprintf($nv_Lang->getModule('number_result_prov'), $array_data['companyname'], $number_result['total'], $package_text, $at_prov, $number_result['result1'], $number_result['result2'], $number_result['result3'], $number_result['result4']);
            } else {
                $content_prov = sprintf($nv_Lang->getModule('number_result_prov'), $array_data['companyname'], $lock_symbol, $package_text, $at_prov, $lock_symbol, $lock_symbol, $lock_symbol, $lock_symbol);
            }

            $data_desire = $data_real = $data_inde = [];
            $data_desire_full = $data_real_full = $data_inde_full = [];
            $year = date('Y', NV_CURRENTTIME) - 7; // Chỉ hiển thị 7 năm
            $i = 0;
            if ($package == 1) {
                foreach ($array_data['value_chart_desire'] as $value_desire) {
                    $data_desire_full[] = $value_desire;
                    if (date('Y', $value_desire[0] / 1000) >= $year) {
                        $data_desire[] = $value_desire;
                    }
                }
                foreach ($array_data['value_chart_real'] as $value_real) {
                    $data_real_full[] = $value_real;
                    if (date('Y', $value_real[0] / 1000) >= $year) {
                        $data_real[] = $value_real;
                    }
                }
                foreach ($array_data['value_chart_inde'] as $value_inde) {
                    $data_inde_full[] = $value_inde;
                    if (date('Y', $value_inde[0] / 1000) >= $year) {
                        $data_inde[] = $value_inde;
                    }
                }
            } else if ($package == 2) {
                foreach ($array_data['value_chart_desire_cdt'] as $value_desire_cdt) {
                    $data_desire_full[] = $value_desire_cdt;
                    if (date('Y', $value_desire_cdt[0] / 1000) >= $year) {
                        $data_desire[] = $value_desire_cdt;
                    }
                }
                foreach ($array_data['value_chart_real_cdt'] as $value_real_cdt) {
                    $data_real_full[] = $value_real_cdt;
                    if (date('Y', $value_real_cdt[0] / 1000) >= $year) {
                        $data_real[] = $value_real_cdt;
                    }
                }
                $i = 0;
                foreach ($array_data['value_chart_inde_cdt'] as $value_inde_cdt) {
                    $data_inde_full[] = $value_inde_cdt;
                    if (date('Y', $value_inde_cdt[0] / 1000) >= $year) {
                        $data_inde[] = $value_inde_cdt;
                    }
                }
            } else if ($package == 3) {

                foreach ($array_data['value_chart_desire_not_tbmt'] as $value_desire_2) {
                    $data_desire_full[] = $value_desire_2;
                    if (date('Y', $value_desire_2[0] / 1000) >= $year) {
                        $data_desire[] = $value_desire_2;
                    }
                }
                $i = 0;
                foreach ($array_data['value_chart_real_not_tbmt'] as $value_real_2) {
                    $data_real_full[] = $value_real_2;
                    if (date('Y', $value_real_2[0] / 1000) >= $year) {
                        $data_real[] = $value_real_2;
                    }
                }
                $i = 0;
                foreach ($array_data['value_chart_inde_not_tbmt'] as $value_inde_2) {
                    $data_inde_full[] = $value_inde_2;
                    if (date('Y', $value_inde_2[0] / 1000) >= $year) {
                        $data_inde[] = $value_inde_2;
                    }
                }
            }
            nv_jsonOutput([
                'content' => $contents,
                'content_prov' => $content_prov,
                'data_desire' => ($view_chart == 2) ? $data_desire_full : $data_desire,
                'data_real' => ($view_chart == 2) ? $data_real_full : $data_real,
                'data_inde' => ($view_chart == 2) ? $data_inde_full : $data_inde
            ]);
        }
    }

    $contents = nv_theme_businesslistings_resultdetail($array_data, $global_array_config, $_arr_row, $number_result, $array_order, $generate_page, $page, $per_page, $ord, $prov, $array_prov_all, $package, $check_user, $link_register, $chart_linkview);

    if (!defined('NV_IS_VIP3') and defined('NV_IS_USER')) {
        // Kiểm tra nếu gói VIP 3 hết hạn
        if (!empty($vip3_renew)) {
            // Nếu hết hạn
            $contents = str_replace("##title__vip##", $nv_Lang->getModule('title_renew_vip'), $contents);
            $contents = str_replace("##link__vip##", NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3', $contents);
        } else {
            $contents = str_replace("##title__vip##", $nv_Lang->getModule('title_resg_vip'), $contents);
            $contents = str_replace("##link__vip##", NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3', $contents);
        }
    }
    return $contents;
}
