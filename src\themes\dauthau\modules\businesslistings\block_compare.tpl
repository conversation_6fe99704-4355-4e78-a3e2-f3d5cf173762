{function list_contractor ARR=[]}
	{foreach $ARR as $c}
		<div class="list-group-item">
			<div class="clearfix">
				<div class="listing-summary">
					<div class="row">
						<div class="col-xs-24 col-md-20">
							<span class="org-code">{$c.orgcode}</span> <a href="{$c.link}" target="_blank"><b>{$c.companyname}</b></a>
						</div>
					</div>
					<div class="row">
						<div class="col-xs-24">
							<div class="address">
								<p class="add_content">
									<b># {$LANG->getModule('taxcode')}:</b> {$c.code}
								</p>
								<p class="add_content">
									<b><i class="fa fa-location-arrow" aria-hidden="true"></i> {$LANG->getModule('province')}:</b>
									{$c.location}
								</p>
							</div>
						</div>
					</div>
					<a href="#" class="btn btn-primary btn-cmp-ar btn-compare-actt{if $c.is_added == 1} hidden{/if} btn-xs" data-id="{$c.id}" onclick="addCttCompare({$c.id}, '{$c.checkaddcp}');"><em class="fa fa-plus">&nbsp;</em>{$LANG->getModule('add_compare')}</a>
					<a href="#" class="btn btn-danger btn-cmp-ar btn-compare-rctt{if $c.is_added == 0} hidden{/if} btn-xs" data-id="{$c.id}" onclick="removeCttCompare({$c.id}, '{$c.checkremvcp}');"><em class="fa fa-minus">&nbsp;</em>{$LANG->getModule('remove_compare')}</a>
				</div>
			</div>
		</div>
	{/foreach}
{/function}

{function list_contractor_compare ARR=[]}
	{foreach $ARR as $c}
		{if empty($c)}
		<div class="formsg col-sm-7 col-xs-24 cp-plus-area">
			<a href="#" class="cp-plus" data-toggle="modal" data-target="#modalCompare" onclick="getRecentCtt();">
				<i class="cp-symbol"></i>
				<p>{$LANG->getModule('add_business')}</p>
			</a>
		</div>
		{else}
		<div class="formsg col-sm-7 col-xs-24">
			<a href="{$c.link}">
				<b>{$c.companyname}</b>
			</a>
			<p class="add_content">
				<b># {$LANG->getModule('taxcode')}:</b> {$c.code}
			</p>
			<p class="add_content m-bottom">
				<b><i class="fa fa-location-arrow" aria-hidden="true"></i> {$LANG->getModule('province')}:</b>
				{$c.location}
			</p>
			<div class="remove-compare">
				<a href="#" onclick="removeCttCompare({$c.id}, '{$c.checkremvcp}');">
					<i class="fa fa-times"></i>
				</a>
			</div>
		</div>
		{/if}
	{/foreach}
	<div class="formsg closecompare col-sm-3 col-xs-24 text-center">
		<btn class="btn btn-primary" onclick="window.location.href='{$LINK_CMP}'">{$LANG->getModule('sosanh')}</btn>
		<a href="#" onclick="RemoveAllIdCompare('{$CHECKCLRALL}')" class="txtremoveall">{$LANG->getModule('remove_all_business')}</a>
	</div>
{/function}

{if !empty($IS_MAIN)}
<div class="hidden mload_compare" data-mload="{$MLOAD_COMPARE}"></div>
<div id="cmp-loaded" data-loaded="{if $SIZE == 0}1{else}0{/if}"></div>
<div class="stickcompare rolling-bottom" style="display: block;">
	<a href="#" class="collapse-compare hidden clearall">{$LANG->getModule('collapse')}&nbsp;<i class="icon-chevron-down"></i></a>
	<a href="#" class="expand-compare clearall">{$LANG->getModule('business_compare')}&nbsp;(<span id='size-cp-ctt'>{$SIZE}</span>)&nbsp;<i class="icon-chevron-up"></i></a>
	<div class="listcompare">
		{assign var="ARR_CMP" value=[[], [], []]}
		{list_contractor_compare ARR=$ARR_CMP}
	</div>
</div>

<!-- Modal -->
<div class="modal fade" id="modalCompare" tabindex="-1" role="dialog" aria-labelledby="compareLabel">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="{$LANG->getModule('close')}"><span aria-hidden="true">&times;</span></button>
				<p class="modal-title h4" id="compareLabel">{$LANG->getModule('add_business')}</p>
			</div>
			<div class="modal-body">
				<div class="m-bottom h2">
					{$LANG->getModule('recent_viewed_ctt')}
				</div>
				<div class="hidden" id="recent-ctt-load" data-loaded="0"></div>
				<div class="recent-view-ctt">
				</div>
				<hr>
				<div class="m-bottom h2">
					{$LANG->getModule('search')}
				</div>
				<form class="row">
					<div class="form-group">
						<div class="col-sm-20 col-md-21 m-bottom">
							<input class="form-control" placeholder="{$LANG->getModule('search_key_title')}" type="text" value="" name="q_compare"/>
						</div>
						<div class="col-sm-4 col-md-3">
							<input class="btn btn-primary" type="button" onclick="searchCompare()" value="{$LANG->getModule('search')}"/>
						</div>
					</div>
				</form>
				<div class="row" id="compare-search-result">
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-danger" data-dismiss="modal">{$LANG->getModule('close')}</button>
			</div>
		</div>
	</div>
</div>

<script type="text/javascript">
	document.querySelector(".collapse-compare").addEventListener("click", function() {
		collapseCmpPosition()
		document.querySelector(".collapse-compare").classList.add('hidden');
		document.querySelector(".expand-compare").classList.remove('hidden');
	});

	document.querySelector(".expand-compare").addEventListener("click", function() {
		let is_load = document.getElementById('cmp-loaded').getAttribute('data-loaded');
		if (is_load != 1) {
			let url_cp = document.querySelector('.mload_compare').getAttribute('data-mload');
			fetch(url_cp, {
				method: 'POST',
				body: new URLSearchParams({
					checkgetlst: '{$CHECKGETLST}',
				})
			})
			.then(response => response.json())
			.then(res => {
				if (res.res === 'error') {
					alert(res.mess);
				} else {
					document.querySelector('.listcompare').innerHTML = res.html;
					document.getElementById('cmp-loaded').setAttribute('data-loaded', 1);
				}
			})
		}
		document.querySelector(".stickcompare").style.bottom = '0';
		document.querySelector(".collapse-compare").classList.remove('hidden');
		document.querySelector(".expand-compare").classList.add('hidden');
	});

	collapseCmpPosition(false);

	searchCompare = () => {
		let url_cp = document.querySelector('.mload_compare').getAttribute('data-mload');
		fetch(url_cp, {
			method: 'POST',
			body: new URLSearchParams({
				checksectt: '{$CHECKSECTT}',
				q: document.querySelector('[name="q_compare"]').value,
				l: 1
			})
		})
		.then(response => response.json())
		.then(res => {
			if (res.res === 'error') {
				alert(res.mess);
			} else {
				document.getElementById('compare-search-result').innerHTML = res.html;
			}
		})
	}

	getRecentCtt = () => {
		let url_cp = document.querySelector('.mload_compare').getAttribute('data-mload');
		let is_load = document.getElementById('recent-ctt-load').getAttribute('data-loaded');
		if (is_load != 1) {
			fetch(url_cp, {
				method: 'POST',
				body: new URLSearchParams({
					checkgetrcctt: '{$CHECKGETRCCTT}',
				})
			})
			.then(response => response.json())
			.then(res => {
				if (res.res === 'success') {
					document.querySelector('.recent-view-ctt').innerHTML = res.html;
					document.getElementById('recent-ctt-load').setAttribute('data-loaded', 1);
				}
			})
		}
	}
	window.addEventListener('resize', function() {
		if (!document.querySelector(".expand-compare").classList.contains('hidden')) {
			collapseCmpPosition(false);
		}
	});
	document.getElementsByName('q_compare')[0].addEventListener("keypress", function(event) {
		if (event.key === "Enter") {
			event.preventDefault();
			searchCompare();
		}
	});
</script>
{/if}

{if !empty($IS_LIST_CONTRACTOR)}
{list_contractor ARR=$ARR_CTT}
{/if}

{if !empty($IS_LIST_CONTRACTOR_COMPARE)}
{list_contractor_compare ARR=$ARR_CMP}
{/if}

