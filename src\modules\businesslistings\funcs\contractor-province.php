<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_IS_MOD_BUSINESSLISTINGS'))
    die('Stop!!!');

use NukeViet\Dauthau\Url;
use NukeViet\Dauthau\Share;
$title_lock = Share::langTitleUnlock();
$temp_id = isset($array_op[1]) ? $array_op[1] : "";

if (!empty($temp_id)) {
    $array_id = explode('-', $temp_id);
    $id = intval(end($array_id));
}

if ($id == 0) {
    nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
}

$sql = "SELECT * FROM " . BUSINESS_PREFIX_GLOBAL . "_info WHERE active = 1 AND id = " . $id;
$result = $db->query($sql);
$row = $result->fetch();
$global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias((NV_LANG_DATA == 'vi' && !empty(trim($row['officialname']))) ? $row['officialname'] : $row['companyname']) . '-' . $row['id'];
(NV_LANG_DATA != 'vi' && !empty(trim($row['officialname']))) && $row['companyname'] = $row['officialname'];
if (empty($row)) {
    nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
}

$page_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($row['companyname']) . '-' . $row['id'];

$page_title = sprintf($nv_Lang->getModule('list_contractor_province_1'), $row['companyname']);
$array_mod_title[] = array(
    'title' => $row['companyname'],
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=detail/' . change_alias($row['companyname']) . '-' . $row['id'], true)
);
$array_mod_title[] = array(
    'title' => $page_title,
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($row['companyname']) . '-' . $row['id'], true)
);

if (!defined('NV_IS_USER')) {
    if ($row['hide_info'] == 1 && $row['time_hide_end'] >= NV_CURRENTTIME) {
        $nv_BotManager->setPrivate();
        $contents = user_info_exit($nv_Lang->getModule('hide_info'));
    } else {
        $link = vsprintf($nv_Lang->getModule('view_result_one'), array(
            $link_register,
            nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3')
        ));
        $contents = get_list(3, false, $link);
    }
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
    die();
}

$view_all_content = false;
if (isset($global_array_vip[3])) {
    $arr_customs = $global_array_vip[3];
    define('NV_IS_VIP3', true);
} elseif (isset($global_array_vip[31])) {
    $arr_customs = $global_array_vip[31];
    define('NV_IS_VIP3', true);
} elseif (isset($global_array_vip[100])) {
    // Nếu có sử dụng gói T100 sẽ được xem toàn bộ dữ liệu với nhà thầu mà họ đã tải file pdf
    $_request_pdf = $db->query("SELECT id FROM " . NV_PREFIXLANG . "_" . $module_data . "_exportpdf_request WHERE id_business =" . $id . ' AND userid=' . $user_info['userid'])->fetchColumn();
    $view_all_content = !empty($_request_pdf) ? true : false;
}

if (!empty($arr_customs_permission) || !empty($global_array_vip)) {
    define('NV_IS_ANY_VIP', true);
}

if (empty($arr_customs) and !$view_all_content) {
    $link = sprintf($nv_Lang->getModule('view_result_user_view'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
    if ($row['hide_info'] == 1 && $row['time_hide_end'] >= NV_CURRENTTIME) {
        $contents = user_info_exit($link);
    } else {
        $contents = get_list(3, false, $link);
    }

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
    die();
}

$contents = get_list(20, true);
include (NV_ROOTDIR . "/includes/header.php");
echo nv_site_theme($contents);
include (NV_ROOTDIR . "/includes/footer.php");

function get_list($per_page, $check_user = false, $link_register = "")
{
    global $module_name, $row, $op, $ord, $db, $nv_Request, $site_mods, $global_config, $prevPage, $nextPage, $canonicalUrl, $config_bidding, $elas_max, $province_list, $global_lang_url, $vip3_renew, $nv_Lang;
    $bidding_module_info_vi = nv_site_mods('vi')['bidding'];
    // kiểm tra quyền truy cập
    if ($row['hide_info'] == 1 and $row['hide_vip3'] == 1) {
        nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
    }
    $link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=bidding&amp;" . NV_OP_VARIABLE . "=";

    $page = $nv_Request->get_page('page', 'post,get', 1);
    if ($check_user === false) {
        $page = 1;
    }

    $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($row['companyname']) . '-' . $row['id'];
    $page_url = $base_url;
    if ($page > 1) {
        $page_url .= '&amp;page=' . $page;
        $global_lang_url .= '&amp;page=' . $page;
    }
    $canonicalUrl = getCanonicalUrl($page_url);

    $request = [
        'id' => $row['id'],
        'businessdetail' => 1, // Lấy dữ liệu cho function detail này
        'is_province' => 1
    ];
    $_data = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'GetDataContractor', $request, 'businesslistings', 1);
    if ($_data['status'] == 'success') {
        $add_data = json_decode($_data['add_data'], true);
        // Xử lý các placeholder các alias của các functions
        $add_data = nv_str_replace_recursion([
            '#openalias#',
            '#viewopenalias#',
            '#resultalias#',
            '#viewalias#',
            NV_BASE_SITEURL,
            '#nv_lang_data#',
            '#lang_companyname_alias#'
        ], [
            $bidding_module_info_vi['alias']['open'],
            $bidding_module_info_vi['alias']['viewopen'],
            $bidding_module_info_vi['alias']['result'],
            $bidding_module_info_vi['alias']['view'],
            NV_BASE_SITEURL,
            NV_LANG_DATA,
            change_alias($row['companyname'])
        ], $add_data);
        $arr_row = $add_data['arr_row'];
        $arr_soclocitor = $add_data['arr_soclocitor'];
        $arr_row_data = $arr_row;
        $array_pq = $add_data['array_pq'];
        $contractor_province = $add_data['province_static'];
        unset($add_data);
    } else {
        $contractor_province = $arr_show_key = [];
        // 1. Danh sách các gói thầu đã tham gia:
        $so_dkkd = $row['code'];
        $_sql = "SELECT so_tbmt, so_dkkd, ten_nha_thau, gia_sau_giam, partnership, gia_du_thau FROM " . NV_PREFIXLANG . "_bidding_open_detail WHERE so_dkkd = '" . $so_dkkd . "' ORDER BY so_tbmt DESC";
        $result_bidding = $db->query($_sql);
        $array_thamgia_thau = $array_bidding = $arr_open_code = $array_bidding_['open'] = [];
        $array_bidding['open'] = [];
        while ($tmp = $result_bidding->fetch()) {
            $tmp['so_tbmt'] = str_replace(' ', '', $tmp['so_tbmt']);
            $arr_open_code[] = $tmp['so_tbmt'];
            $array_bidding_['open'][$tmp['so_tbmt']]['code'] = $tmp['so_tbmt'];
            $array_bidding_['open'][$tmp['so_tbmt']]['bidder_name'] = $tmp['ten_nha_thau'];
            $array_bidding_['open'][$tmp['so_tbmt']]['no_business_licence'] = $tmp['so_dkkd'];
            $array_bidding_['open'][$tmp['so_tbmt']]['win_price'] = 0;
            $array_bidding_['open'][$tmp['so_tbmt']]['win_price_number'] = 0;
            $array_bidding_['open'][$tmp['so_tbmt']]['plan_code'] = '';
            $array_bidding_['open'][$tmp['so_tbmt']]['trung_thau'] = 3;
            $array_bidding_['open'][$tmp['so_tbmt']]['type'] = 'open';
            $array_bidding_['open'][$tmp['so_tbmt']]['gia_du_thau'] = floatval($tmp['gia_du_thau']);
            $array_bidding_['open'][$tmp['so_tbmt']]['gia_sau_giam'] = floatval($tmp['gia_sau_giam']);
            $array_bidding_['open'][$tmp['so_tbmt']]['partnership'] = $tmp['partnership'];
        }

        if (!empty($arr_open_code)) {
            if ($config_bidding['elas_use']) {
                $nukeVietElasticSearh = new \NukeViet\ElasticSearch\Functions($config_bidding['elas_host'], $config_bidding['elas_port'], 'dauthau_open', $config_bidding['elas_user'], $config_bidding['elas_pass']);
                $array_query_elastic = $search_elastic = [];
                $search_elastic['filter']['terms']['so_tbmt.keyword'] = $arr_open_code;
                if (!empty($search_elastic)) {
                    $array_query_elastic['query']['bool'] = $search_elastic;
                    $array_query_elastic['from'] = 0;
                    $array_query_elastic['size'] = 60000;
                    $array_query_elastic['_source'] = [
                        'so_tbmt',
                        'solicitor_id',
                        'ben_moi_thau',
                        'thoi_diem_hoan_thanh',
                        'thoi_diem_mo_thau',
                        'goi_thau',
                        'gia_goi_thau',
                        'alias'
                    ];
                    try {
                        $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_bidding_open', $array_query_elastic);
                    } catch (Exception $e) {
                        trigger_error(print_r($e, true), 256);
                    }
                }
                foreach ($response['hits']['hits'] as $value) {
                    if (!empty($value['_source'])) {
                        $tmp = $value['_source'];
                        $tmp['so_tbmt'] = str_replace(' ', '', $tmp['so_tbmt']);
                        $tmp['finish_time'] = !empty($tmp['thoi_diem_hoan_thanh']) ? $tmp['thoi_diem_hoan_thanh'] : $tmp['thoi_diem_mo_thau'];
                        $array_thamgia_thau[$tmp['so_tbmt']] = $tmp['so_tbmt'];
                        $array_bidding['open'][$tmp['so_tbmt']] = $array_bidding_['open'][$tmp['so_tbmt']];
                        $array_bidding['open'][$tmp['so_tbmt']]['investor'] = $tmp['ben_moi_thau'];
                        $array_bidding['open'][$tmp['so_tbmt']]['solicitor_id'] = $tmp['solicitor_id'];
                        $array_bidding['open'][$tmp['so_tbmt']]['title'] = $tmp['goi_thau'];
                        $array_bidding['open'][$tmp['so_tbmt']]['finish_time'] = $tmp['finish_time'];
                        $array_bidding['open'][$tmp['so_tbmt']]['thoi_diem_mo_thau'] = $tmp['thoi_diem_mo_thau'];
                        $array_bidding['open'][$tmp['so_tbmt']]['id'] = 0;
                        $array_bidding['open'][$tmp['so_tbmt']]['goi_thau'] = $tmp['goi_thau'];
                        $array_bidding['open'][$tmp['so_tbmt']]['gia_goi_thau'] = floatval($tmp['gia_goi_thau']);
                        $arr_show_key[$tmp['so_tbmt']] = $tmp['so_tbmt'];
                        $array_bidding['open'][$tmp['so_tbmt']]['link_open'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=vi&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['viewopen'] . '/' . $tmp['alias'] . '-' . $tmp['so_tbmt'] . $global_config['rewrite_exturl'];
                    }
                }
            } else {
                $_sql = "SELECT so_tbmt, alias, solicitor_id, ben_moi_thau, thoi_diem_hoan_thanh, thoi_diem_mo_thau, goi_thau, gia_goi_thau FROM " . NV_PREFIXLANG . "_bidding_open WHERE so_tbmt IN ('" . implode("','", $arr_open_code) . "')";
                $result_bidding = $db->query($_sql);
                while ($tmp = $result_bidding->fetch()) {
                    $tmp['so_tbmt'] = str_replace(' ', '', $tmp['so_tbmt']);
                    $tmp['finish_time'] = !empty($tmp['thoi_diem_hoan_thanh']) ? $tmp['thoi_diem_hoan_thanh'] : $tmp['thoi_diem_mo_thau'];
                    $array_thamgia_thau[$tmp['so_tbmt']] = $tmp['so_tbmt'];
                    $array_bidding['open'][$tmp['so_tbmt']] = $array_bidding_['open'][$tmp['so_tbmt']];
                    $array_bidding['open'][$tmp['so_tbmt']]['investor'] = $tmp['ben_moi_thau'];
                    $array_bidding['open'][$tmp['so_tbmt']]['solicitor_id'] = $tmp['solicitor_id'];
                    $array_bidding['open'][$tmp['so_tbmt']]['title'] = $tmp['goi_thau'];
                    $array_bidding['open'][$tmp['so_tbmt']]['finish_time'] = $tmp['finish_time'];
                    $array_bidding['open'][$tmp['so_tbmt']]['thoi_diem_mo_thau'] = $tmp['thoi_diem_mo_thau'];
                    $array_bidding['open'][$tmp['so_tbmt']]['id'] = 0;
                    $array_bidding['open'][$tmp['so_tbmt']]['goi_thau'] = $tmp['goi_thau'];
                    $array_bidding['open'][$tmp['so_tbmt']]['gia_goi_thau'] = floatval($tmp['gia_goi_thau']);
                    $array_bidding['open'][$tmp['so_tbmt']]['link_open'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=vi&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['viewopen'] . '/' . $tmp['alias'] . '-' . $tmp['so_tbmt'] . $global_config['rewrite_exturl'];
                    $arr_show_key[$tmp['so_tbmt']] = $tmp['so_tbmt'];
                }
            }
        }

        $array_pq = array();
        $array_bidding['pq'] = [];
        // Lấy danh sách tham gia sơ tuyển
        $_sql_pq = "SELECT t1.result_id, t1.joint_venture, t1.so_dkkd, t2.code, t2.title, (CASE WHEN t2.time_open > 0 THEN t2.time_open ELSE t2.post_time END) as finish_time, t2.title_solicitor as investor, t2.solicitor_id, t2.number_bid, t2.bid_price as bid_price_number FROM " . NV_PREFIXLANG . "_bidding_result_prequalification_business as t1 INNER JOIN " . NV_PREFIXLANG . "_bidding_result_prequalification as t2 on t1.result_id = t2.id WHERE so_dkkd = '" . $so_dkkd . "' ORDER BY t1.id DESC";
        $pq_bidding = $db->query($_sql_pq);
        while ($pq_i = $pq_bidding->fetch()) {
            $pq_i['code'] = str_replace(' ', '', $pq_i['code']);
            $array_bidding['pq'][$pq_i['code']]['code'] = $pq_i['code'];
            $array_bidding['pq'][$pq_i['code']]['result_id'] = $pq_i['result_id'];
            $array_bidding['pq'][$pq_i['code']]['title'] = $pq_i['title'];
            $array_bidding['pq'][$pq_i['code']]['finish_time'] = $pq_i['finish_time'];
            $array_bidding['pq'][$pq_i['code']]['solicitor_id'] = $pq_i['solicitor_id'];
            $array_bidding['pq'][$pq_i['code']]['joint_venture'] = $pq_i['joint_venture'];
            $array_bidding['pq'][$pq_i['code']]['so_dkkd'] = $pq_i['so_dkkd'];
            $array_bidding['pq'][$pq_i['code']]['type'] = 'pq';
            $array_bidding['pq'][$pq_i['code']]['trung_thau'] = '3';
            // array_pq = 0 : CÓ kết quả sơ tuyển, =1 trượt, =2 trúng
            if (!empty($pq_i['so_dkkd'])) {
                $array_bidding['pq'][$pq_i['code']]['array_pq'] = 2;
            } else {
                $array_bidding['pq'][$pq_i['code']]['array_pq'] = intval($db->query("SELECT type_info FROM " . NV_PREFIXLANG . "_bidding_result_goods WHERE result_id = " . $pq_i['result_id'] . " AND number_res = " . $db->quote($pq_i['so_dkkd']))
                    ->fetchColumn());
            }
            // Lấy số nhà thầu cùng tham gia sơ tuyển
            $array_bidding['pq'][$pq_i['code']]['count_ld'] = $pq_i['number_bid'] - 1;
            $arr_show_key[$pq_i['code']] = $pq_i['code'];
            $array_pq[$pq_i['code']] = $arr_show_key[$pq_i['code']];
        }

        // lấy danh sách các gói thầu đã trúng thầu
        $array_result = array();
        $_sql = "SELECT resultid, no_business_licence, partnership, bid_price as win_price_business, bidwinningprice FROM " . NV_PREFIXLANG . "_bidding_result_business WHERE no_business_licence = " . $db->quote($so_dkkd);
        $result_bidding = $db->query($_sql);
        $array_bidding['data'] = [];
        $arrPiceResultBussiness = [];
        while ($tmp = $result_bidding->fetch()) {
            $array_result_id[$tmp['resultid']] = $tmp['resultid'];
            $array_result_business[$tmp['resultid']] = $tmp;
            $arrPiceResultBussiness[$tmp['resultid']] = $tmp;
        }
        if (!empty($array_result_id)) {
            // Tìm trên Elastic
            if ($config_bidding['elas_result_use']) {
                $nukeVietElasticSearh = new \NukeViet\ElasticSearch\Functions($config_bidding['elas_result_host'], $config_bidding['elas_result_port'], 'dauthau_result', $config_bidding['elas_result_user'], $config_bidding['elas_result_pass']);
                // Chia làm nhiều lần tìm kiếm
                $array_query_elastic = $search_elastic = [];
                if (!empty($array_bidding['pq'])) {
                    $search_elastic['should'][] = [
                        'terms' => [
                            'id' => array_keys($array_result_id)
                        ]
                    ];
                    $search_elastic['should'][] = [
                        'terms' => [
                            'code.keyword' => array_keys($array_bidding['pq'])
                        ]
                    ];
                    $search_elastic['minimum_should_match'] = '1';
                    $search_elastic['boost'] = '1.0';
                } else {
                    $search_elastic['filter']['terms']['id'] = array_keys($array_result_id);
                }
                if (!empty($search_elastic)) {
                    $array_query_elastic['query']['bool'] = $search_elastic;
                    $array_query_elastic['sort'] = [
                        'finish_time' => [
                            'order' => 'desc'
                        ]
                    ];
                    $array_query_elastic['from'] = 0;
                    $array_query_elastic['size'] = 60000;
                    $array_query_elastic['_source'] = [
                        'type_choose_id',
                        'id',
                        'code',
                        'title',
                        'alias',
                        'investor',
                        'solicitor_id',
                        'bid_price_number',
                        'tender_price',
                        'bidder_name',
                        'win_price',
                        'win_price_number',
                        'finish_time',
                        'time_cancel',
                        'id_province',
                        'date_approval'
                    ];
                    $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_bidding_result', $array_query_elastic);
                }

                foreach ($response['hits']['hits'] as $value) {
                    if (!empty($value['_source'])) {
                        $tmp = $value['_source'];
                        $tmp['trung_thau'] = in_array($tmp['id'], $array_result_id) ? 1 : 2;
                        $tmp['code'] = str_replace(' ', '', $tmp['code']);
                        $tmp['link_result'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=vi&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['result'] . '/' . Url::URL_KQLCNT['vi'] . '/' . $tmp['alias'] . '-' . $tmp['id'] . $global_config['rewrite_exturl'];
                        $tmp['win_price_number'] = floatval($arrPiceResultBussiness[$tmp['id']]['bidwinningprice'] ?? 0);
                        $tmp['win_price'] = !empty($arrPiceResultBussiness[$tmp['id']]['bidwinningprice']) ? number_format($arrPiceResultBussiness[$tmp['id']]['bidwinningprice'], 0, ',', '.') . ' VND' : $tmp['win_price'];
                        $tmp['bid_price_number'] = floatval($tmp['bid_price_number']);
                        $tmp['tender_price'] = floatval($tmp['tender_price']);

                        $array_bidding['data'][$tmp['code']] = $tmp + ($array_result_business[$tmp['id']] ?? []);
                        $array_bidding['data'][$tmp['code']]['type'] = 'data';
                        $array_bidding['data'][$tmp['code']]['province_id'] = empty($tmp['id_province']) ? 0 : explode(',', $tmp['id_province']);

                        if (empty($array_bidding['data'][$tmp['code']]['win_price_number']) && !empty($array_bidding['data'][$tmp['code']]['win_price_business'])) {
                            $array_bidding['data'][$tmp['code']]['win_price'] = $array_bidding['data'][$tmp['code']]['win_price_business'];
                            $array_bidding['data'][$tmp['code']]['win_price_number'] = floatval(str_replace([
                                ',',
                                '.'
                            ], [
                                '.',
                                ''
                            ], $array_bidding['data'][$tmp['code']]['win_price_business']));
                        }
                        $array_result[$tmp['code']] = $tmp['code'];
                        if (!empty($array_bidding['open'][$tmp['code']])) {
                            $array_bidding['open'][$tmp['code']]['solicitor_id'] = $tmp['solicitor_id'];
                            $array_bidding['open'][$tmp['code']]['investor'] = $tmp['investor'];
                            $array_bidding['open'][$tmp['code']]['title'] = $tmp['title'];
                            $array_bidding['data'][$tmp['code']]['partnership'] = $array_bidding['open'][$tmp['code']]['partnership'];
                        } else {
                            $array_bidding['data'][$tmp['code']]['partnership'] = $array_result_business[$tmp['id']]['partnership'];
                        }
                        $arr_show_key[$tmp['code']] = $tmp['code'];
                        $array_bidding['data'][$tmp['code']]['link_result'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=vi&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['result'] . '/' . Url::URL_KQLCNT['vi'] . '/' . $tmp['alias'] . '-' . $tmp['id'] . $global_config['rewrite_exturl'];
                    }
                }
            } else {
                $_sql = "SELECT type_choose_id, id, code, alias, title, investor, solicitor_id, bid_price_number, tender_price, bidder_name, win_price, win_price_number, finish_time, time_cancel, id_province as province_id, date_approval FROM " . NV_PREFIXLANG . "_bidding_result WHERE id IN (" . implode(',', $array_result_id) . ')' . (!empty($array_bidding['pq']) ? ' OR code IN (' . implode(', ', array_map([
                    $db,
                    'quote'
                ], array_keys($array_bidding['pq']))) . ')' : '') . ' ORDER BY finish_time DESC';
                $result_bidding = $db->query($_sql);

                while ($tmp = $result_bidding->fetch()) {
                    $tmp['trung_thau'] = 1;
                    $tmp['code'] = str_replace(' ', '', $tmp['code']);
                    $tmp['link_result'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=vi&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['result'] . '/' . Url::URL_KQLCNT['vi'] . '/' . $tmp['alias'] . '-' . $tmp['id'] . $global_config['rewrite_exturl'];
                    $tmp['win_price_number'] = floatval($arrPiceResultBussiness[$tmp['id']]['bidwinningprice'] ?? 0);
                    $tmp['win_price'] = !empty($arrPiceResultBussiness[$tmp['id']]['bidwinningprice']) ? number_format($arrPiceResultBussiness[$tmp['id']]['bidwinningprice'], 0, ',', '.') . ' VND' : $tmp['win_price'];
                    $tmp['bid_price_number'] = floatval($tmp['bid_price_number']);
                    $tmp['tender_price'] = floatval($tmp['tender_price']);

                    $array_bidding['data'][$tmp['code']] = $tmp + $array_result_business[$tmp['id']];
                    $array_bidding['data'][$tmp['code']]['type'] = 'data';
                    $array_result[$tmp['code']] = $tmp['code'];
                    if (empty($array_bidding['data'][$tmp['code']]['win_price_number']) && !empty($array_bidding['data'][$tmp['code']]['win_price_business'])) {
                        $array_bidding['data'][$tmp['code']]['win_price'] = $array_bidding['data'][$tmp['code']]['win_price_business'];
                        $array_bidding['data'][$tmp['code']]['win_price_number'] = str_replace([
                            ',',
                            '.'
                        ], [
                            '.',
                            ''
                        ], $array_bidding['data'][$tmp['code']]['win_price_business']);
                    }
                    if (!empty($array_bidding['open'][$tmp['code']])) {
                        $array_bidding['open'][$tmp['code']]['solicitor_id'] = $tmp['solicitor_id'];
                        $array_bidding['open'][$tmp['code']]['investor'] = $tmp['investor'];
                        $array_bidding['open'][$tmp['code']]['title'] = $tmp['title'];
                        $array_bidding['data'][$tmp['code']]['partnership'] = $array_bidding['open'][$tmp['code']]['partnership'];
                    } else {
                        $array_bidding['data'][$tmp['code']]['partnership'] = $array_result_business[$tmp['id']]['partnership'];
                    }
                    $arr_show_key[$tmp['code']] = $tmp['code'];
                    $array_bidding['data'][$tmp['code']]['link_result'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=vi&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['result'] . '/' . Url::URL_KQLCNT['vi'] . '/' . $tmp['alias'] . '-' . $tmp['id'] . $global_config['rewrite_exturl'];
                }
            }
        }
        // gộp dữ liệu 2 bảng-> Tổng số gói tham gia
        $arr_row = array_merge($array_bidding['pq'], $array_bidding['open'], $array_bidding['data']);
        // Tìm giá gói thầu
        if (!empty($arr_row)) {
            // Xóa những gói thầu phiên bản cũ, chỉ lấy phiên bản mới nhất
            $arr_code = [];
            foreach ($arr_row as $code => $_row) {
                $_code = explode('-', $code);
                $arr_code[$_code[0]][intval($_code[1])] = $code;
            }
            foreach ($arr_code as $code) {
                if (sizeof($code) > 1) {
                    foreach ($code as $k => $c) {
                        if ($k != max(array_keys($code))) {
                            unset($arr_row[$c]);
                            unset($array_thamgia_thau[$c]);
                            unset($array_result[$c]);
                        }
                    }
                }
            }

            foreach ($arr_row as $_row) {
                if (!empty($_row['bid_price_number'])) {
                    $arr_row[$_row['code']]['gia_goi_thau'] = floatval($_row['bid_price_number']);
                }
                $arr_so_tbmt[] = $_row['code'];
                !isset($arr_row[$_row['code']]['province_id']) && $arr_row[$_row['code']]['province_id'] = 0;
            }
            if ($config_bidding['elas_use']) {
                $nukeVietElasticSearh = new \NukeViet\ElasticSearch\Functions($config_bidding['elas_host'], $config_bidding['elas_port'], 'dauthau_bidding', $config_bidding['elas_user'], $config_bidding['elas_pass']);
                $array_query_elastic = $search_elastic = [];
                $search_elastic['filter']['terms']['so_tbmt.keyword'] = $arr_so_tbmt;
                if (!empty($search_elastic)) {
                    $array_query_elastic['query']['bool'] = $search_elastic;
                    $array_query_elastic['from'] = 0;
                    $array_query_elastic['size'] = 60000;
                    $array_query_elastic['_source'] = [
                        'id',
                        'so_tbmt',
                        'alias',
                        'price',
                        'goi_thau',
                        'ben_moi_thau',
                        'province_id',
                        'solicitor_id',
                        'hinh_thuc_lua_chon',
                        'hinh_thuc_thong_bao',
                        'money_bid'
                    ];
                    $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_bidding_row', $array_query_elastic);
                }
                foreach ($response['hits']['hits'] as $value) {
                    if (!empty($value['_source'])) {
                        $_row = $value['_source'];
                        $arr_row_table[$_row['id']] = $_row;
                    }
                }
            } else {
                $result_row = $db->query('SELECT id, so_tbmt, price, goi_thau, ben_moi_thau, solicitor_id, province_id, hinh_thuc_lua_chon, hinh_thuc_thong_bao, money_bid, alias FROM ' . NV_PREFIXLANG . '_bidding_row WHERE so_tbmt IN (' . implode(',', array_map(function ($a) {
                    global $db;
                    return $db->quote($a);
                }, $arr_so_tbmt)) . ')');
                while ($_row = $result_row->fetch()) {
                    $arr_row_table[$_row['id']] = $_row;
                }
            }
            if (!empty($arr_row_table)) {
                foreach ($arr_row_table as $_row) {
                    if (!empty($_row['bid_price_number'])) {
                        $arr_row[$_row['so_tbmt']]['gia_goi_thau'] = floatval($_row['bid_price_number']);
                    }
                    if (!empty($_row['price'])) {
                        $arr_row[$_row['so_tbmt']]['gia_goi_thau'] = floatval($_row['price']);
                    } else {
                        $array_no_price[] = $_row['so_tbmt'];
                    }
                    $arr_row[$_row['so_tbmt']]['money_bid'] = floatval($_row['money_bid']);
                    $arr_row[$_row['so_tbmt']]['id_row'] = $_row['id'];
                    $arr_row[$_row['so_tbmt']]['alias_row'] = $_row['alias'];
                    empty($arr_row[$_row['so_tbmt']]['title']) && $arr_row[$_row['so_tbmt']]['title'] = $_row['goi_thau'];
                    $arr_row[$_row['so_tbmt']]['province_id'] = explode(',', $_row['province_id']);
                    $arr_row[$_row['so_tbmt']]['hinh_thuc_lua_chon'] = !empty($arr_row_table[$_row['id']]['hinh_thuc_lua_chon']) ? $arr_row_table[$_row['id']]['hinh_thuc_lua_chon'] : '';
                    $arr_row[$_row['so_tbmt']]['hinh_thuc_thong_bao'] = !empty($arr_row_table[$_row['id']]['hinh_thuc_thong_bao']) ? $arr_row_table[$_row['id']]['hinh_thuc_thong_bao'] : '';
                    if (empty($arr_row[$_row['so_tbmt']]['solicitor_id'])) {
                        $arr_row[$_row['so_tbmt']]['solicitor_id'] = $_row['solicitor_id'];
                    }
                    empty($arr_row[$_row['so_tbmt']]['investor']) && $arr_row[$_row['so_tbmt']]['investor'] = $_row['ben_moi_thau'];
                    $arr_row[$_row['so_tbmt']]['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=vi&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['view'] . '/' . Url::URL_TBMT['vi'] . '/' . $_row['alias'] . '-' . $_row['id'] . $global_config['rewrite_exturl'];
                }
                if (!empty($array_no_price)) {
                    foreach ($array_no_price as $code) {
                        $array_no_price_sql[] = $db->quote($code);
                    }
                    $result_price_plan = $db->query('SELECT so_tbmt_mst, price FROM ' . NV_PREFIXLANG . '_bidding_plans_contract WHERE so_tbmt_mst IN (' . implode(',', $array_no_price_sql) . ')');
                    while ($_row = $result_price_plan->fetch()) {
                        if (!empty($_row['price'])) {
                            $arr_row[$_row['so_tbmt_mst']]['gia_goi_thau'] = floatval($_row['price']);
                        } else {
                            $arr_row[$_row['so_tbmt_mst']]['gia_goi_thau'] = 0;
                        }
                    }
                }
            }
        }
        if (!empty(count($arr_row))) {
            foreach ($arr_row as $_so_tbmt => $_row) {
                !isset($_row['province_id']) && $_row['province_id'] = 0;
                if (is_string($_row['province_id']) && strpos($_row['province_id'], ',') !== false) {
                    $_row['province_id'] = explode(',', $_row['province_id']);
                }
                if (is_array($_row['province_id'])) {
                    foreach ($_row['province_id'] as $tt) {
                        if (!isset($contractor_province[$tt]['num_total'])) {
                            $contractor_province[$tt]['num_total'] = 1;
                        } else {
                            $contractor_province[$tt]['num_total']++;
                        }
                        !isset($contractor_province[$tt]['result1']) && $contractor_province[$tt]['result1'] = 0;
                        !isset($contractor_province[$tt]['result2']) && $contractor_province[$tt]['result2'] = 0;
                        !isset($contractor_province[$tt]['num_total_win_doclap']) && $contractor_province[$tt]['num_total_win_doclap'] = 0;
                        !isset($contractor_province[$tt]['num_total_win_liendanh']) && $contractor_province[$tt]['num_total_win_liendanh'] = 0;
                        !isset($contractor_province[$tt]['total_win_price']) && $contractor_province[$tt]['total_win_price'] = 0;
                        !isset($contractor_province[$tt]['total_win_price_doclap']) && $contractor_province[$tt]['total_win_price_doclap'] = 0;
                        !isset($contractor_province[$tt]['total_win_price_liendanh']) && $contractor_province[$tt]['total_win_price_liendanh'] = 0;
                        if ($_row['trung_thau'] == 1) {
                            $contractor_province[$tt]['total_win_price'] += $_row['win_price_number'];
                            ++$contractor_province[$tt]['result1'];
                            if (isset($_row['partnership']) && $_row['partnership'] == 1) {
                                $contractor_province[$tt]['total_win_price_doclap'] += $_row['win_price_number'];
                                ++$contractor_province[$tt]['num_total_win_doclap'];
                            } else {
                                $contractor_province[$tt]['total_win_price_liendanh'] += $_row['win_price_number'];
                                ++$contractor_province[$tt]['num_total_win_liendanh'];
                            }
                        } else if ($_row['trung_thau'] == 2) {
                            ++$contractor_province[$tt]['result2'];
                        }
                    }
                } else {
                    if (!isset($array_data['prov_list'][$_row['province_id']])) {
                        $array_data['prov_list'][$_row['province_id']] = 1;
                    } else {
                        $array_data['prov_list'][$_row['province_id']]++;
                    }
                    $contractor_province[$_row['province_id']]['num_total'] = $array_data['prov_list'][$_row['province_id']];
                    !isset($contractor_province[$_row['province_id']]['result1']) && $contractor_province[$_row['province_id']]['result1'] = 0;
                    !isset($contractor_province[$_row['province_id']]['result2']) && $contractor_province[$_row['province_id']]['result2'] = 0;
                    !isset($contractor_province[$_row['province_id']]['num_total_win_liendanh']) && $contractor_province[$_row['province_id']]['num_total_win_liendanh'] = 0;
                    !isset($contractor_province[$_row['province_id']]['num_total_win_doclap']) && $contractor_province[$_row['province_id']]['num_total_win_doclap'] = 0;
                    !isset($contractor_province[$_row['province_id']]['total_win_price']) && $contractor_province[$_row['province_id']]['total_win_price'] = 0;
                    !isset($contractor_province[$_row['province_id']]['total_win_price_doclap']) && $contractor_province[$_row['province_id']]['total_win_price_doclap'] = 0;
                    !isset($contractor_province[$_row['province_id']]['total_win_price_liendanh']) && $contractor_province[$_row['province_id']]['total_win_price_liendanh'] = 0;
                    if ($_row['trung_thau'] == 1) {
                        $contractor_province[$_row['province_id']]['total_win_price'] += $_row['win_price_number'];
                        ++$contractor_province[$_row['province_id']]['result1'];
                        if (isset($_row['partnership']) && $_row['partnership'] == 1) {
                            $contractor_province[$_row['province_id']]['total_win_price_doclap'] += $_row['win_price_number'];
                            ++$contractor_province[$_row['province_id']]['num_total_win_doclap'];
                        } else {
                            $contractor_province[$_row['province_id']]['total_win_price_liendanh'] += $_row['win_price_number'];
                            ++$contractor_province[$_row['province_id']]['num_total_win_liendanh'];
                        }
                    } else if ($_row['trung_thau'] == 2) {
                        ++$contractor_province[$_row['province_id']]['result2'];
                    }
                }
            }
        }
    }

    $num_items = sizeof($contractor_province);
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);

    // Đánh số trang
    $urlappend = '&amp;page=';
    betweenURLs($page, ceil($num_items / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

    $array_data = $row;
    $contents = nv_theme_businesslistings_contractor_province($array_data, $contractor_province, $generate_page, $page, $per_page, $check_user, $link_register);
    if (!defined('NV_IS_VIP3') and defined('NV_IS_USER')) {
        // Kiểm tra nếu gói VIP 3 hết hạn
        if (!empty($vip3_renew)) {
            // Nếu hết hạn
            $contents = str_replace("##title__vip##", $nv_Lang->getModule('title_renew_vip'), $contents);
            $contents = str_replace("##link__vip##", NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3', $contents);
        } else {
            $contents = str_replace("##title__vip##", $nv_Lang->getModule('title_resg_vip'), $contents);
            $contents = str_replace("##link__vip##", NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3', $contents);
        }
    }
    return $contents;
}
