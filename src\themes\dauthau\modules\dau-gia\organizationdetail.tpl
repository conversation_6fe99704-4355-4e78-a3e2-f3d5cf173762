<!-- BEGIN: main -->
<!-- BEGIN: popup_login -->
{POPUP_LOGIN}
<!-- END: popup_login -->
<div class="bidding-detail-wrapper bidding-detail-wrapper-result">
    <h1 class="bidding-name">{ROW.name_bidder}</h1>
    <div class="row">
        <div class=" col-xs-24">
            <div class="btn-share-group">
                <span>{LANG.share} </span>
                <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_fb_share();" title="{LANG.share_facebook}">
                    <span class="icon-facebook"></span>
                </a>
                <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_tw_share('', '{ROW.name_bidder}');" title="{LANG.share_twitter}">
                    <span class="icon-twitter"></span>
                </a>
                <a href="javascript:void(0)" class="btn-share btn-copy-link" title="{LANG.copy_link}">
                    <em class="fa fa-link"></em>
                    <span class="tip" style="display: none;">{LANG.copy_success}</span>
                </a>
            </div>
            <div class="text-right crawl_time">
                <div class="small">
                    {LANG.updated_at}: <strong>{ROW.update_at}</strong>
                </div>
            </div>
        </div>
    </div>
    <div class="bidding-detail">
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.name_tcdg}</div>
            <div class="c-val">{ROW.name_bidder}</div>
        </div>
        <!-- BEGIN: department -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.department_name}</div>
            <div class="c-val">
                <a href="{ROW.dep.url_dep}">{ROW.dep.fullname}</a>
            </div>
        </div>
        <!-- END: department -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.address}</div>
            <div class="c-val text-capitalize">{ROW.address_bidder}</div>
        </div>
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.phone}</div>
                <div class="c-val">{ROW.phone}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.fax}</div>
                <div class="c-val">{ROW.fax}</div>
            </div>
        </div>
        <!-- BEGIN: so_quyet_dinh -->
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.so_quyet_dinh}</div>
                <div class="c-val">{ROW.so_quyet_dinh}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.ngay_quyet_dinh}</div>
                <div class="c-val">{ROW.ngay_quyet_dinh}</div>
            </div>
        </div>
        <!-- END: so_quyet_dinh -->
        <!-- BEGIN: so_giay_dkhd -->
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.so_giay_dkhd}</div>
                <div class="c-val">{ROW.so_giay_dkhd}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.ngay_cap}</div>
                <div class="c-val">{ROW.ngay_cap}</div>
            </div>
        </div>
        <!-- END: so_giay_dkhd -->
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.number_dgv}</div>
                <div class="c-val">{ROW.quantityauctioneer}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.email}</div>
                <div class="c-val">{ROW.email}</div>
            </div>
        </div>
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.so_cchn}</div>
                <div class="c-val">{ROW.so_cchn}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.auctioneerName}</div>
                <div class="c-val text-capitalize">{ROW.auctioneername}</div>
            </div>
        </div>
        <!-- BEGIN: logs -->
        <div class="bidding-detail-item org_version">
            <div class="c-tit">{LANG.updated_histories}</div>
            <div class="c-val">
                <!-- BEGIN: loop -->
                <p>
                    <label>
                        <input type="checkbox" value="{ROW_LOG.id}" class="chbox_change_item" {ROW_LOG.checked}>
                        <strong>{ROW_LOG.created_at}</strong> - {ROW_LOG.text_fields}<!-- BEGIN: action_inline -->&nbsp;(<a href="javascript:;" class="a_change_org" data-id="{ROW_LOG.id}-{ROW_LOG.prev}">{LANG.view_change}</a>)<!-- END: action_inline -->
                    </label>
                </p>
                <!-- END: loop -->
                <span data-toggle="tooltip" data-placement="right" title="{LANG.tooltip_org_version_check}">
                    <button class="btn btn-primary btn-xs" id="btn_change_org" value="">{LANG.view_change}</button>
                </span>
            </div>
        </div>
        <!-- END: logs -->
    </div>
</div>
<!-- BEGIN: chi_nhanh -->
<div class="bidding-simple">
    <h2 class="border-bidding">
        <span>{LANG.chi_nhanh}</span>
    </h2>
    <section class="block-bidding" style="margin-top: 10px">
        <table class="bidding-table">
            <thead>
                <tr>
                    <th class="w50">{LANG.stt}</th>
                    <th>{LANG.chi_nhanh_name}</th>
                    <th class="w150">{LANG.chi_nhanh_dkhd}</th>
                    <th class="w150">{LANG.chi_nhanh_timedkhd}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="txt-center" data-column="{LANG.stt}">
                        <div>{CHI_NHANH.stt}</div>
                    </td>
                    <td data-column="{LANG.chi_nhanh_name}">
                        <h3>{CHI_NHANH.name}</h3>
                    </td>
                    <td class="txt-center" data-column="{LANG.chi_nhanh_dkhd}">
                        <div>{CHI_NHANH.number_dkhd}</div>
                    </td>
                    <td data-column="{LANG.chi_nhanh_timedkhd}" class="txt-center">
                        <div>{CHI_NHANH.time_dkhd}</div>
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </section>
</div>
<!-- END: chi_nhanh -->
<!-- BEGIN: van_phong -->
<div class="bidding-simple">
    <h2 class="border-bidding">
        <span>{LANG.van_phong}</span>
    </h2>
    <section class="block-bidding" style="margin-top: 10px">
        <table class="bidding-table">
            <thead>
                <tr>
                    <th class="w50">{LANG.stt}</th>
                    <th>{LANG.van_phong_name}</th>
                    <th class="w150">{LANG.van_phong_number}</th>
                    <th class="w150">{LANG.van_phong_time}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="txt-center" data-column="{LANG.stt}">
                        <div>{VAN_PHONG.stt}</div>
                    </td>
                    <td data-column="{LANG.van_phong_name}">
                        <h3>{VAN_PHONG.name}</h3>
                    </td>
                    <td class="txt-center" data-column="{LANG.van_phong_number}">
                        <div>{VAN_PHONG.number}</div>
                    </td>
                    <td data-column="{LANG.van_phong_time}" class="txt-center">
                        <div>{VAN_PHONG.time}</div>
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </section>
</div>
<!-- END: van_phong -->
<!-- BEGIN: dau_gia_vien -->
<div class="bidding-simple">
    <h2 class="border-bidding">
        <span>{LANG.dgv}</span>
    </h2>
    <section class="block-bidding" style="margin-top: 10px">
        <table class="bidding-table">
            <thead>
                <tr>
                    <th class="w50">{LANG.stt}</th>
                    <th>{LANG.dgv_name}</th>
                    <th class="w150">{LANG.dgv_birthday}</th>
                    <th class="w150">{LANG.dgv_number_cchn}</th>
                    <th class="w150">{LANG.dgv_time_cchn}</th>
                    <th class="w150">{LANG.dgv_number_card}</th>
                    <th class="w150">{LANG.dgv_time_card}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="txt-center" data-column="{LANG.stt}">
                        <div>{DAU_GIA_VIEN.stt}</div>
                    </td>

                    <td data-column="{LANG.dgv_name}">
                        <h3><a href="{DAU_GIA_VIEN.url_detail}">{DAU_GIA_VIEN.name}</a></h3>
                    </td>
                    <td class="txt-center" data-column="{LANG.dgv_birthday}">
                        <div>{DAU_GIA_VIEN.birthday}</div>
                    </td>
                    <td data-column="{LANG.dgv_number_cchn}" class="txt-center">
                        <div>{DAU_GIA_VIEN.number_cchn}</div>
                    </td>
                    <td data-column="{LANG.dgv_time_cchn}" class="txt-center">
                        <div>{DAU_GIA_VIEN.time_cchn}</div>
                    </td>
                    <td data-column="{LANG.dgv_number_card}" class="txt-center">
                        <div>{DAU_GIA_VIEN.num_dgv}</div>
                    </td>
                    <td data-column="{LANG.dgv_time_card}" class="txt-center">
                        <div>{DAU_GIA_VIEN.time_dgv}</div>
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </section>
</div>
<!-- END: dau_gia_vien -->

<div class="business-chart">
    <div class="clearfix">
        <div class="pull-right margin-top margin-left margin-right">
            <select class="form-control" id="view_chart" name="view_chart">
                <option value="1">{LANG.view_chart_7_years}</option>
                <option value="2">{LANG.view_chart_full}</option>
            </select>
        </div>
    </div>

    <div class="business-chart-title no-margin-bottom">{LANG.title_num_organization}</div>
    <div class="row chart main_chart_history">
        <div class="col-md-24 col-sm-24 col-xs-24">
            <div id="value_chart">
            </div>
        </div>
    </div>
</div>

<!-- BEGIN: view -->
<div class="bidding-simple">
    <h2 class="border-bidding">
        <span>{LANG.thong_bao_dau_gia}</span>
    </h2>
    <section class="block-bidding" style="margin-top: 10px">
        <table class="bidding-table">
            <thead>
                <tr>
                    <th>{LANG.thong_bao_dau_gia}</th>
                    <th class="w200">{LANG.name_propertys}</th>
                    <th class="w150">{LANG.open_date_bid}</th>
                    <th class="w150">{LANG.time_nop_hs}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="order-header" data-column="{LANG.thong_bao_dau_gia}">
                        <div>
                            <a href="{BID.url_detail}">{BID.title}</a>
                        </div>
                    </td>
                    <td data-column="{LANG.name_propertys}">
                        <div>{BID.name_owner}</div>
                    </td>
                    <td class="txt-center" data-column="{LANG.open_date_bid}">
                        <div>{BID.date_bid_format}<br />&<br />{BID.opening_bid_format}</div>
                    </td>
                    <td data-column="{LANG.time_sale}" class="txt-center">
                        <div>
                            {BID.opening_reg_format} </br> {LANG.to} {BID.closing_reg_format}
                        </div>
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>

        <!-- BEGIN: show_more -->
        <blockquote>
            {SUMMARY_BIDDER_BID}
        </blockquote>
        <!-- END: show_more -->
    </section>
</div>
<!-- END: view -->

<!-- BEGIN: select_result -->
<div class="bidding-simple">
    <h2 class="border-bidding">
        <span>{LANG.list_result_join_select_organization}</span>
    </h2>
    <section class="block-bidding" style="margin-top: 10px">
        <table class="bidding-table">
            <thead>
                <tr>
                    <th>{LANG.announcement_result_select_organization}</th>
                    <th class="w200">{LANG.name_propertys}</th>
                    <th class="w150">{LANG.date_public}</th>
                    <th class="w150">{LANG.start_price}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="order-header" data-column="{LANG.announcement_result_select_organization}">
                        <div>
                            <a href="{SELECT_RESULT.url_detail}">{SELECT_RESULT.title}</a>
                        </div>
                    </td>
                    <td data-column="{LANG.name_propertys}">
                        <div>{SELECT_RESULT.name_owner}</div>
                    </td>
                    <td class="txt-center" data-column="{LANG.date_public}">
                        <div>{SELECT_RESULT.date_published_format}</div>
                    </td>
                    <td data-column="{LANG.start_price}" class="txt-center">
                        <div>
                            {SELECT_RESULT.min_bid_price}
                        </div>
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
        <!-- BEGIN: show_more -->
        <blockquote>
            {SUMMARY_BIDDER_SELECT}
        </blockquote>
        <!-- END: show_more -->
    </section>
</div>
<!-- END: select_result -->

<script src="{LINK_JS}/js/apexcharts.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        // Load mặc định dữ liệu
        let data_bid = '{ROW.data_bid}';
        data_bid = JSON.parse(data_bid);
        buildChart(data_bid);
    });

    $(document).on('change', '#view_chart', function () {
        let id = $(this).val();
        loadDataChart(id);
    });

    // Lấy dữ liệu data thống kê
    function loadDataChart(id) {
        $.ajax({
            url: location.href,
            type: 'POST',
            data: {
                'action': 'get_data_chart',
                'view_chart': id
            }
        })
            .done(function (dataBid) {
                $("#value_chart").html("");
                buildChart(dataBid);
            });
    }

    function buildChart(dataBid) {
        if (dataBid.length == 0) {
            $(".business-chart").hide();
            return !1;
        }

        const date = [];
        const data_desire = [];
        const data_inde = [];

        Object.entries(dataBid).map(([year, data]) => {
            date.push(year);
            data_desire.push(parseFloat(data.total_min_bid_prices_bid));

        });

        Object.entries(dataBid).map(([year, data]) => {
            data_inde.push(parseFloat(data.total_min_bid_prices_asset));
        });

        const options = {
            chart: {
                type: 'area',
                toolbar: {
                    show: false
                },
                zoom: {
                    enabled: false,
                },
                stacked: false,
                height: '97%',
                offsetY: 10,
                offsetX: 5,
            },
            dataLabels: {
                enabled: false // 👈 Tắt hiển thị con số trên biểu đồ
            },
            series: [
                {
                    name: '{LANG.thong_bao_dau_gia}',
                    data: data_desire
                },
                {
                    name: '{LANG.title_kqlctc}',
                    data: data_inde
                }
            ],
            xaxis: {
                type: 'category',
                categories: date,
                title: {
                    text: '{LANG.time_static}',
                    style: {
                        fontWeight: 600
                    }
                }
            },
            yaxis: {
                title: {
                    text: '{LANG.title_value_chart}',
                    style: {
                        fontWeight: 600
                    }
                },
                labels: {
                    formatter: function (val) {
                        return (val / 1000000).toLocaleString();
                    }
                }
            },
            tooltip: {
                shared: true,
                x: {
                    format: 'yyyy'
                },
                y: {
                    formatter: function (val) {
                        return (val / 1000000).toLocaleString();
                    }
                }
            },
            colors: ['#1E90FF', '#FF4D4F'],
            stroke: {
                width: 3,
                curve: 'smooth'
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.3,
                    opacityTo: 0.1,
                    stops: [0, 90, 100]
                }
            },
            markers: {
                size: 5
            },
            legend: {
                position: 'top'
            }
        };

        var chart = new ApexCharts(document.querySelector("#value_chart"), options);
        chart.render();
    }
</script>

<div id="showUpdatedHistories" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <div class="modal-title"><strong>{LANG.updated_histories}</strong></div>
            </div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{LANG.close}</button>
            </div>
        </div>
    </div>
</div>

<!-- START FOOTER -->
<div id="showPopupCompare" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <div class="modal-title"><strong>{LANG.modal_compare_title}</strong></div>
            </div>
            <div class="modal-body">
                <div id="main_change_org">
                    <div class="change_org">
                        <div class="row">
                            <div class="col-md-24">
                                <label>{LANG.select_type_compare}: </label> <label><input type="radio" name="org_compare_type" checked value="1">&nbsp;{LANG.compare_2_col}</label> <label><input type="radio" name="org_compare_type" value="2">&nbsp;{LANG.compare_1_col}</label>
                            </div>
                            <div class="col-md-24">
                                <div class="card">
                                    <label><input type="checkbox" class="change_by_line" value="3">&nbsp;{LANG.view_change_line}</label>
                                    <div class="row_compare">
                                        <div class="col">
                                            <div class="card" id="outputOriginal"></div>
                                        </div>
                                        <div class="col">
                                            <div class="card" id="output"></div>
                                        </div>
                                        <div class="col" id="new_version">
                                            <div class="card" id="outputNew"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{LANG.close}</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var title_tooltip = $("#btn_change_org").parent().attr("title");
    if (title_tooltip == "" || typeof title_tooltip === typeof undefined) {
        title_tooltip = $("#btn_change_org").parent().attr("data-original-title");
    }
    $(document).ready(function () {
        update_id_checkbox();
        $(".chbox_change_item").change(function () {
            var numberOfChecked = $('.chbox_change_item:checkbox:checked').length;
            if (numberOfChecked >= 2) {
                $('.chbox_change_item:checkbox').not(':checked').prop("disabled", true);
            } else {
                $('.chbox_change_item:checkbox').not(':checked').prop("disabled", false);
            }
            update_id_checkbox();
        });
        function update_id_checkbox() {
            var $boxes = $('.chbox_change_item[type=checkbox]:checked');
            arrid = [];
            $boxes.each(function (v, k) {
                arrid.push(k.value);
            });
            $("#btn_change_org").val(arrid.join("-"));
            if ($boxes.length < 2) {
                $("#btn_change_org").attr('disabled', true);
                $("#btn_change_org").parent().attr("data-original-title", title_tooltip);
            } else {
                $("#btn_change_org").attr('disabled', false);
                $("#btn_change_org").parent().attr("data-original-title", "");
            }
        }
        $("#btn_change_org, .a_change_org").click(function () {
            id = $(this).attr('data-id');
            if (typeof id === typeof undefined) {
                id = $(this).val();
            }
            // Ẩn chế độ 1 cột trên mobile
            if (window.matchMedia("(max-width: 768px)").matches) {
                $("input[name='org_compare_type'][value='1']").closest('label').hide();
                $("input[name='org_compare_type'][value='2']").click();
            } else {
                $("input[name='org_compare_type'][value='1']").closest('label').show();
                $("input[name='org_compare_type'][value='1']").click();
            }
            $.ajax({
                url: location.href,
                type: 'POST',
                data: {
                    'action': 'view_change_org',
                    'id': id
                },
                success: function (data) {
                    $("#outputOriginal tbody").html("");
                    $("#output tbody").html("");
                    if (data['res'] == 'error') {
                        alert(data['data']);
                        $("#showPopupCompare").modal('hide');
                        setTimeout(function () {
                            location.reload();
                        }, 2000);
                        return false;
                    }
                    $("#showPopupCompare").modal('show').css('z-index', '10000000000');
                    compare_highlight_difference("outputOriginal", "output", "outputNew", false, JSON.stringify(data['data']['org']), JSON.stringify(data['data']['org1']));
                    tr_0 = $(".table__tb").eq(0).find('tr');
                    tr_1 = $(".table__tb").eq(1).find('tr');
                    for (i = 0; i < tr_0.length; i++) {
                        tr_0.eq(i).attr('data-row', i + 1);
                    }
                    for (i = 0; i < tr_1.length; i++) {
                        tr_1.eq(i).attr('data-row', i + 1);
                    }
                    html_old = $("#outputOriginal tbody").html();
                    html1_old = $("#output tbody").html();
                    $(".change_by_line").click(function () {
                        view_change_line(html_old, html1_old);
                    });
                    change_by_line = $(".change_by_line:checked").val();
                    if (change_by_line == '3') {
                        view_change_line(html_old, html1_old);
                    }
                }
            })
        });
        function view_change_line(html_old, html1_old) {
            arr_show = [];
            table = $(".change_by_line").closest('#main_change_org').find('#output').find('table');
            del = table.find('tr').find('td');
            for (i = 0; i < del.length; i++) {
                row = del.eq(i).find('del').length;
                if (!row) {
                    row = del.eq(i).find('ins').length;
                }
                if (row) {
                    data_row = del.eq(i).closest('tr').attr('data-row');
                    arr_show.push(data_row);
                }
            }
            arr_show = [...new Set(arr_show)];
            html = '';
            html1 = '';
            for (i = 0; i < arr_show.length; i++) {
                html += '<tr data-row="' + arr_show[i] + '">' + $('#outputOriginal .table__tb tr[data-row="' + arr_show[i] + '"]').html() + "</tr>";
                html1 += '<tr data-row="' + arr_show[i] + '">' + $('#output .table__tb tr[data-row="' + arr_show[i] + '"]').html() + "</tr>";
            }
            if ($(".change_by_line").is(':checked')) {
                $("#outputOriginal tbody").html(html);
                $("#output tbody").html(html1);
            } else {
                $("#outputOriginal tbody").html(html_old);
                $("#output tbody").html(html1_old);
            }
        }
        $("input[name='org_compare_type']").change(function () {
            if ($(this).val() == 2) {
                $("#outputOriginal").parent().hide(500);
                $("#output").parent().show(500);
            } else if ($(this).val() == 3) {
                $("#outputOriginal").parent().hide();
                $("#output").parent().hide();
            } else {
                $("#outputOriginal").parent().show(500);
                $("#output").parent().show(500);
            }
        });
    });
</script>
<!-- END FOOTER -->
<!-- END: main -->

<!-- BEGIN: info_table -->
<table class="table table-hover table__tb">
    <tbody>
        <tr>
            <th>{LANG.name_tcdg}</th>
            <td>{ROW.name_bidder}</td>
        </tr>
        <tr>
            <th>{LANG.department_name}</th>
            <td>{ROW.dep.fullname}</td>
        </tr>
        <tr>
            <th>{LANG.address}</th>
            <td>{ROW.address_bidder}</td>
        </tr>
        <tr>
            <th>{LANG.phone}</th>
            <td>{ROW.phone}</td>
        </tr>
        <tr>
            <th>{LANG.fax}</th>
            <td>{ROW.fax}</td>
        </tr>
        <tr>
            <th>{LANG.so_quyet_dinh}</th>
            <td>{ROW.so_quyet_dinh}</td>
        </tr>
        <tr>
            <th>{LANG.ngay_quyet_dinh}</th>
            <td>{ROW.ngay_quyet_dinh}</td>
        </tr>
        <tr>
            <th>{LANG.so_giay_dkhd}</th>
            <td>{ROW.so_giay_dkhd}</td>
        </tr>
        <tr>
            <th>{LANG.ngay_cap}</th>
            <td>{ROW.ngay_cap}</td>
        </tr>
        <tr>
            <th>{LANG.number_dgv}</th>
            <td>{ROW.quantityauctioneer}</td>
        </tr>
        <tr>
            <th>{LANG.email}</th>
            <td>{ROW.email}</td>
        </tr>
        <tr>
            <th>{LANG.so_cchn}</th>
            <td>{ROW.so_cchn}</td>
        </tr>
        <tr>
            <th>{LANG.auctioneerName}</th>
            <td>{ROW.auctioneername}</td>
        </tr>
    </tbody>
</table>
<!-- END: info_table -->