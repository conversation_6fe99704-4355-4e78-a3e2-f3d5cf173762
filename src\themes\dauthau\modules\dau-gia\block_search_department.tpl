<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<form action="{FORM_ACTION}" method="get" class="block-bidding form-horizontal form-search" id="departmentSearchBlock">
    <!-- BEGIN: no_rewrite -->
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
    <input type="hidden" name="{NV_OP_VARIABLE}" value="{NV_OP_VALUE}">
    <!-- END: no_rewrite -->
    <div class="form-group">
        <label class="control-label col-md-5">{LANG.department_name}:</label>
        <div class="col-md-19">
            <input class="form-control" type="text" value="{department_name}" name="department_name" placeholder="{LANG.search_by_department_name}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-md-5">{LANG.department_search_province}:</label>
        <div class="col-md-19">
            <select name="province" id="province" class="form-control fselect2">
                <option value="0">{LANG.pleaseselect}</option>
                <!-- BEGIN: province -->
                <option value="{key_province}" {sl_province}>{val_province}</option>
                <!-- END: province -->
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-md-5">{LANG.department_director}:</label>
        <div class="col-md-19">
            <input class="form-control" type="text" value="{director}" name="director" placeholder="{LANG.search_by_director_name}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-md-5">{LANG.count_chinhanh}:</label>
        <div class="col-md-19">
            <div class="row">
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">{LANG.from}:</span>
                        <input class="form-control" type="number" min="0" value="{min_chinhanh}" name="min_chinhanh" placeholder="{LANG.search_total_chinhanh_from}">
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">{LANG.to}:</span>
                        <input class="form-control" type="number" min="0" value="{max_chinhanh}" name="max_chinhanh" placeholder="{LANG.search_total_chinhanh_to}">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-md-5">{LANG.count_dgv}:</label>
        <div class="col-md-19">
            <div class="row">
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">{LANG.from}:</span>
                        <input class="form-control" type="number" min="0" value="{min_dgv}" name="min_dgv" placeholder="{LANG.search_total_dgv_from}">
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="input-group">
                        <span class="input-group-addon">{LANG.to}:</span>
                        <input class="form-control" type="number" min="0" value="{max_dgv}" name="max_dgv" placeholder="{LANG.search_total_dgv_to}">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-19 col-md-offset-5">
            <input id="fsearch" type="submit" value="{LANG.search}" class="btn btn-primary">
        </div>
    </div>
</form>
<script type="text/javascript">
    $(function() {
        $('.fselect2').select2({ language : nv_lang_interface });
    });
</script>
<!-- END: main -->
