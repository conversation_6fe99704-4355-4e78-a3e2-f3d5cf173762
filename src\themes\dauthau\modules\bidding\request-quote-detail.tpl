<!-- BEGIN: main -->
<style>
    #form_value .pagination>.active>a {
        color: #fff;
    }
</style>
<div id="popup_not_dismiss" class="modal fade auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body"></div>
        </div>
    </div>
</div>
<div class="msgshow" id="msgshow"></div>

<!-- BEGIN: popup_login -->
    {POPUP_LOGIN}
<!-- END: popup_login -->

<!-- BEGIN: popup_not_point -->
<script>
    $(function() {
        var mess = '{LANG.info_point_not_enought}';
        mess += '<p class="text-center"><a class="btn btn-danger" href="https://id.dauthau.net/{NV_LANG_DATA}/points/#muadiem" target="_blank">{LANG.buy_points}</a></p>';

        $("#popup_not_dismiss").find(".modal-body").html(mess);
        $("#popup_not_dismiss").modal({
            backdrop: "static",
            keyboard: false
        });
    });
</script>
<!-- END: popup_not_point -->

<!-- BEGIN: msgshow -->
<script>
alert_msg('{LANG.message_point_view_suss}');
</script>
<!-- END: msgshow -->

<!-- BEGIN: recaptcha -->
<div id="captchaModal" class="modal fade auto-width auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <p class="modal-title">{LANG.recapcha_title}</p>
            </div>
            <div class="modal-body">
                <div>{LANG.recapcha_body}</div>
                <div data-toggle="recaptcha" data-callback="verify_captcha" id="{RECAPTCHA_ELEMENT}"></div>
            </div>
        </div>
    </div>
</div>
<script>
    function verify_captcha(e) {
        click_update();
    }
</script>
<!-- END: recaptcha -->

<div class="bidding-wrapper">
    <div class="bidding-title">
        <!-- <div class="subtl">{LANG.listopendetailt}</div> -->
        <h1 class="tl wrap__text">{VIEW.request_quote_name}</h1>
    </div>

    <div class="text-right">
        {FILE "button_show_log.tpl"}
    </div>
    <!-- BEGIN: days_left_note -->
    <div class="flex-vertical-center m-bottom">
        <div class="alert alert-success" role="alert" title="{VIEW.reception_date_to}">
            <i class="fa fa-clock-o fa-lg" aria-hidden="true"></i>
            &nbsp;
            {VIEW.days_left}
        </div>
    </div>
    <!-- END: days_left_note -->
    <!-- BEGIN: time_expired_note -->
    <div class="flex-vertical-center m-bottom">
        <div class="alert alert-warning" role="alert" title="{VIEW.reception_date_to}">
            <i class="fa fa-hourglass-end" aria-hidden="true"></i>
            &nbsp;
            {LANG.ycbg_time_expired_note}
        </div>
    </div>
    <!-- END: time_expired_note -->

    <div class="row">
        <div class="col-xs-24 btn-share-group">
            <span>{LANG.share} </span>
            <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_fb_share();" title="{LANG.fb_share}">
                <span class="icon-facebook"></span>
            </a>
            <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_tw_share('', '{VIEW.request_quote_name}');" title="{LANG.tweet}">
                <span class="icon-twitter"></span>
            </a>
            <a href="javascript:void(0)" class="btn-share btn-copy-link" title="{LANG.copy_link}">
                <em class="fa fa-link"></em>
                <span class="tip" style="display: none;">{LANG.link_copy_successfully}</span>
            </a>
        </div>
    </div>

    <div class="bidding-page-btn m-bottom">
        <!-- BEGIN: follow -->
            <!-- BEGIN: yes -->
            <div class="btn-group follow">
                <button class="btn btn-danger btn-follow dropdown-toggle" data-toggle="dropdown">
                    <em class="fa fa-check"></em> {LANG.follow_yes} &nbsp;<span class="caret"></span>
                </button>
                <ul class="dropdown-menu" role="menu">
                    <li><a href="{VIEW.link_follow_manager}"><em class="fa fa-cog"></em> {LANG.follow_manager1}</a></li>
                    <li><a href="javascript:void(0)" onclick="click_unfollow('{LANG.follow_delete_confirm_rq}', '{VIEW.link_follow_manager}', {VIEW.follow_id}, '{VIEW.delete_checkss}');"><em class="fa fa-trash-o"></em> {LANG.follow_delete}</a></li>
                </ul>
            </div>
            <!-- END: yes -->
            <!-- BEGIN: no -->
            <button
                class="btn btn-primary btn-follow" onclick="click_follow('{VIEW.link_follow}', '{CHECKSESS}', this, '{YCBG_NO}', '{USERID}')">
                <em class="fa fa-plus"></em> {LANG.follow_no}
            </button>
            <!-- END: no -->
        <!-- END: follow -->
        <!-- BEGIN: link_msc -->
        <a href="{LINK_MSC}" style="margin-left: 0px"> <button class="btn btn-primary btn-follow" style="margin-left: 20px">{LANG.icon_vneps} Link MSC</button></a>
        <!-- END: link_msc -->
        <div class="text-right crawl_time mg-bt-5">
            <div class="small">
                {LANG.crawl_time}: <strong>{VIEW.fget_time}</strong>
            </div>
            <!-- BEGIN: update -->
            <div class="margin-top-sm">
                <span class="small">{VIEW.update_info}</span> 
                
                <!-- BEGIN: show_reupdate -->
                <a style="margin-left: auto" id="reupdate" class="btn btn-default btn-xs active" onclick="show_captcha()" href="javascript:void(0)" data-id="{VIEW.id}" data-check="{CHECKSESS_UPDATE}">{LANG.reupdate}</a>
                <!-- END: show_reupdate -->
                
                <img id="update_wait" style="display: none" src="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/images/load_bar.gif" />
                <!-- BEGIN: crawl_request_history_button -->
                <a style="margin-left: auto" id="crawl_request_history" class="btn btn-default btn-xs active" href="javascript:void(0)">{LANG.crawl_request_history}</a>
                <!-- END: crawl_request_history_button -->
            </div>
            <!-- END: update -->
        </div>
    </div>

    {FILE "crawl_request_history_list.tpl"}
        
    <div class="scoll__menu1">
        <div class="bidding-sub-title display-flex">
            {LANG.rq_bid_info}
        </div>
        <div class="bidding-detail">
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.p_name}</div>
                <div class="c-val">{VIEW.p_name}</div>
            </div>
    
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.bid_name}</div>
                <div class="c-val">{VIEW.bid_name}</div>
            </div>
        </div>
    </div>

    <div class="scoll__menu1">
        <div class="bidding-sub-title display-flex">
            {LANG.rq_basic_info}
        </div>
        <div class="bidding-detail">
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.request_quote_code}</div>
                <div class="c-val"><span class="bidding-code">{VIEW.request_quote_code}</span></div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.status}</div>
                <div class="c-val">{VIEW.status_title}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.request_quote_name}</div>
                <div class="c-val">{VIEW.request_quote_name}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.investor_ycbg}</div>
                <div class="c-val">
                    <!-- BEGIN: investor_yes_link -->
                        <a class="bidding_link" href="{VIEW.link_investor}" title="{VIEW.investor_name}">{VIEW.investor_name}</a>
                    <!-- END: investor_yes_link -->
    
                    <!-- BEGIN: investor_no_link -->
                    {VIEW.investor_name}
                    <!-- END: investor_no_link -->
                </div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.address_ycbg}</div>
                <div class="c-val">{VIEW.investor_address}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.request_quote_type}</div>
                <div class="c-val">{VIEW.request_quote_type_title}</div>
            </div>
        </div>
    </div>

    <div class="scoll__menu1">
        <div class="bidding-sub-title display-flex">
            {LANG.rq_responsible_info}
        </div>
        <div class="bidding-detail">
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.responsible_name}</div>
                <div class="c-val">{VIEW.responsible_name}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.responsible_position}</div>
                <div class="c-val">{VIEW.responsible_position}</div>
            </div>
    
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.responsible_phone_number}</div>
                <div class="c-val">{VIEW.responsible_phone_number_link}</div>
            </div>
    
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.responsible_address}</div>
                <div class="c-val">{VIEW.responsible_address}</div>
            </div>
    
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.responsible_email}</div>
                <div class="c-val">{VIEW.responsible_email_link}</div>
            </div>
        </div>
    </div>
        
    <div class="scoll__menu1">
        <div class="bidding-sub-title display-flex">
            {LANG.rq_form_info}
        </div>
        <div class="bidding-detail">
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.request_quote_form}</div>
                <div class="c-val">{VIEW.request_quote_form_title}</div>
            </div>
    
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.rq_issue_location}</div>
                <div class="c-val">
                    <a href="http://muasamcong.mpi.gov.vn" target="_blank" rel="nofollow">http://muasamcong.mpi.gov.vn</a>
                </div>
            </div>
    
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.reception_date}</div>
                <div class="c-val">{VIEW.reception_date}</div>
            </div>
    
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.rq_validity_period}</div>
                <div class="c-val">{VIEW.rq_validity_period} {VIEW.rq_validity_period_unit_title}</div>
            </div>
        </div>
    </div>  
    
    <div class="scoll__menu1" >
        <div class="bidding-sub-title display-flex">
            {LANG.rq_table_info}
        </div>
        
        <div id="pageContent">&nbsp;</div>
        <!-- BEGIN: form_value -->
        <div id="form_value">
            <p class="bidding-sub-title">{LANG.form_value}</p>
            <div class="scoll__menu1">
                <div class="data_goods">
                    <div class="form-group">
                        <div class="input-group">
                            <input type="text" id="search-input" class="form-control" placeholder="{LANG.search_block_title}">

                            <span id="filter-btn" class="input-group-addon filter-btn">
                                <i class="fa fa-filter" aria-hidden="true"></i> {LANG.title_filter_search}
                            </span>
                        </div>
                        <p class="alert-warning title_waring_max_row"><small></small></p>

                    </div>

                    <div class="form-group">
                        <label for="rows-per-page">{LANG.title_show_pagination}:</label>
                        <select id="rows-per-page" class="form-control">
                            <option value="5">5</option>
                            <option value="10" selected>10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                            <option value="200">200</option>
                            <option value="500">500</option>
                            <option value="all">All</option>
                        </select>
                        <span><strong>{LANG.title_data_one_pagination}</strong>
                    </div>

                    <div class="box__table">
                        <table class="bidding-table">
                            <thead>
                                <tr class="info">
                                    <!-- BEGIN: th_loop -->
                                    <th class="text-center column-header" data-column="1">{LANG.rq_pos}</th>
                                    <th class="text-center column-header" data-column="2">{LANG.rq_category}</th>
                                    <th class="text-center column-header" data-column="3">{LANG.rq_qty}</th>
                                    <th class="text-center column-header" data-column="4">{LANG.rq_unit}</th>
                                    <th class="text-center column-header" data-column="5">{LANG.rq_description}</th>
                                    <th class="text-center column-header" data-column="6">{LANG.rq_location}</th>
                                    <th class="text-center column-header" data-column="7">{LANG.rq_specification}</th>
                                    <th class="text-center column-header" data-column="8">{LANG.rq_note}</th>
                                    <th class="text-center column-header" data-column="9">{LANG.feature}</th>
                                    <!-- END: th_loop -->

                                    <!-- BEGIN: th_loop_med_type_1 -->
                                    <th class="text-center column-header" data-column="1">{LANG.rq_pos}</th>
                                    <th class="text-center column-header" data-column="2">{LANG.med_type_1_medicine_code}</th>
                                    <th class="text-center column-header" data-column="3">{LANG.med_type_1_ten_hoat_chat}</th>
                                    <th class="text-center column-header" data-column="4">{LANG.med_type_1_nhom_TCKT}</th>
                                    <th class="text-center column-header" data-column="5">{LANG.med_type_1_nong_do}</th>
                                    <th class="text-center column-header" data-column="6">{LANG.med_type_1_duong_dung}</th>
                                    <th class="text-center column-header" data-column="7">{LANG.med_type_1_dang_bao_che}</th>
                                    <th class="text-center column-header" data-column="8">{LANG.med_type_1_uom}</th>
                                    <th class="text-center column-header" data-column="9">{LANG.med_type_1_quantity}</th>
                                    <th class="text-center column-header" data-column="10">{LANG.feature}</th>
                                    <!-- END: th_loop_med_type_1 -->

                                    <!-- BEGIN: th_loop_med_type_2 -->
                                    <th class="text-center column-header" data-column="1">{LANG.rq_pos}</th>
                                    <th class="text-center column-header" data-column="2">{LANG.med_type_2_medicineCode}</th>
                                    <th class="text-center column-header" data-column="3">{LANG.med_type_2_tenHoatChat}</th>
                                    <th class="text-center column-header" data-column="4">{LANG.med_type_2_tenThuoc}</th>
                                    <th class="text-center column-header" data-column="5">{LANG.med_type_2_nongDo}</th>
                                    <th class="text-center column-header" data-column="6">{LANG.med_type_2_duongDung}</th>
                                    <th class="text-center column-header" data-column="7">{LANG.med_type_2_dangBaoChe}</th>
                                    <th class="text-center column-header" data-column="8">{LANG.med_type_2_uom}</th>
                                    <th class="text-center column-header" data-column="9">{LANG.med_type_2_quantity}</th>
                                    <th class="text-center column-header" data-column="10">{LANG.feature}</th>
                                    <!-- END: th_loop_med_type_2 -->

                                    <!-- BEGIN: th_loop_med_type_3 -->
                                    <th class="text-center column-header" data-column="1">{LANG.rq_pos}</th>
                                    <th class="text-center column-header" data-column="2">{LANG.med_type_3_medicineCode}</th>
                                    <th class="text-center column-header" data-column="3">{LANG.med_type_3_tenHoatChat}</th>
                                    <th class="text-center column-header" data-column="4">{LANG.med_type_3_nhomTCKT}</th>
                                    <th class="text-center column-header" data-column="5">{LANG.med_type_3_nongDo}</th>
                                    <th class="text-center column-header" data-column="6">{LANG.med_type_3_duongDung}</th>
                                    <th class="text-center column-header" data-column="7">{LANG.med_type_3_dangBaoChe}</th>
                                    <th class="text-center column-header" data-column="8">{LANG.med_type_3_uom}</th>
                                    <th class="text-center column-header" data-column="9">{LANG.med_type_3_quantity}</th>
                                    <th class="text-center column-header" data-column="10">{LANG.feature}</th>
                                    <!-- END: th_loop_med_type_3 -->

                                    <!-- BEGIN: th_loop_med_type_4 -->
                                    <th class="text-center column-header" data-column="1">{LANG.rq_pos}</th>
                                    <th class="text-center column-header" data-column="2">{LANG.med_type_4_tenHoatChat}</th>
                                    <th class="text-center column-header" data-column="3">{LANG.med_type_4_nhomTCKT}</th>
                                    <th class="text-center column-header" data-column="4">{LANG.med_type_4_tenThuoc}</th>
                                    <th class="text-center column-header" data-column="5">{LANG.med_type_4_medicineCode}</th>
                                    <th class="text-center column-header" data-column="6">{LANG.med_type_4_dangBaoChe}</th>
                                    <th class="text-center column-header" data-column="7">{LANG.med_type_4_dangBaoChe}</th>
                                    <th class="text-center column-header" data-column="8">{LANG.med_type_4_uom}</th>
                                    <th class="text-center column-header" data-column="9">{LANG.med_type_4_quantity}</th>
                                    <th class="text-center column-header" data-column="10">{LANG.feature}</th>
                                    <!-- END: th_loop_med_type_4 -->

                                    <!-- BEGIN: th_loop_med_type_5 -->
                                    <th class="text-center column-header" data-column="1">{LANG.rq_pos}</th>
                                    <th class="text-center column-header" data-column="2">{LANG.med_type_5_tenHoatChat}</th>
                                    <th class="text-center column-header" data-column="3">{LANG.med_type_5_nhomTCKT}</th>
                                    <th class="text-center column-header" data-column="4">{LANG.med_type_5_medicineCode}</th>
                                    <th class="text-center column-header" data-column="5">{LANG.med_type_5_tenThuoc}</th>
                                    <th class="text-center column-header" data-column="6">{LANG.med_type_5_dangBaoChe}</th>
                                    <th class="text-center column-header" data-column="7">{LANG.med_type_5_qualityStandards}</th>
                                    <th class="text-center column-header" data-column="8">{LANG.med_type_5_uom}</th>
                                    <th class="text-center column-header" data-column="9">{LANG.med_type_5_quantity}</th>
                                    <th class="text-center column-header" data-column="10">{LANG.feature}</th>
                                    <!-- END: th_loop_med_type_5 -->

                                    <!-- BEGIN: th_loop_med_type_7 -->
                                    <th class="text-center column-header" data-column="1">{LANG.rq_pos}</th>
                                    <th class="text-center column-header" data-column="2">{LANG.med_type_7_medicineCode}</th>
                                    <th class="text-center column-header" data-column="3">{LANG.med_type_7_tenHoatChat}</th>
                                    <th class="text-center column-header" data-column="4">{LANG.med_type_7_nhomTCKT}</th>
                                    <th class="text-center column-header" data-column="5">{LANG.med_type_7_nongDo}</th>
                                    <th class="text-center column-header" data-column="6">{LANG.med_type_7_duongDung}</th>
                                    <th class="text-center column-header" data-column="7">{LANG.med_type_7_dangBaoChe}</th>
                                    <th class="text-center column-header" data-column="8">{LANG.med_type_7_uom}</th>
                                    <th class="text-center column-header" data-column="9">{LANG.med_type_7_quantity}</th>
                                    <th class="text-center column-header" data-column="10">{LANG.feature}</th>
                                    <!-- END: th_loop_med_type_7 -->
                                </tr>
                            </thead>
                            <tbody id="data-container">
                                <!-- BEGIN: td_loop -->
                                <tr class='table-row' data-index='{FORM_VALUE.stt}'>
                                    <td data-column="{LANG.rq_pos}" class="text-center"><div>{FORM_VALUE.pos}</div></td>
                                    <td data-column="{LANG.rq_category}"><div>{FORM_VALUE.category}</div></td>
                                    <td data-column="{LANG.rq_qty}"><div>{FORM_VALUE.qty}</div></td>
                                    <td data-column="{LANG.rq_unit}"><div>{FORM_VALUE.unit}</div></td>
                                    <td data-column="{LANG.rq_description}"><div>{FORM_VALUE.description}</div></td>
                                    <td data-column="{LANG.rq_location}"><div>{FORM_VALUE.location}</div></td>
                                    <td data-column="{LANG.rq_specification}"><div>{FORM_VALUE.specification}</div></td>
                                    <td data-column="{LANG.rq_note}"><div>{FORM_VALUE.note}</div></td>
                                    <td class="text-center">
                                        <span class="span__buton">
                                            <button type="button" class="btn btn-primary btn-radius" data-toggle="modal" data-target=".bd-example-modal-lg" onclick="search_goods('{FORM_VALUE.category}','{ARR_DATA.investor_id}','{ARR_DATA.id}')">{LANG.reference}</button>
                                        </span>
                                    </td>
                                </tr>
                                <!-- END: td_loop -->
                                <!-- BEGIN: td_loop_med_type_1 -->
                                <tr class='table-row' data-index='{FORM_VALUE.stt}'>
                                    <td data-column="{LANG.rq_pos}" class="text-center"><div>{FORM_VALUE.pos}</div></td>
                                    <td data-column="{LANG.med_type_1_medicine_code}"><div>{FORM_VALUE.medicineCode}</div></td>
                                    <td data-column="{LANG.med_type_1_ten_hoat_chat}"><div>{FORM_VALUE.tenHoatChat}</div></td>
                                    <td data-column="{LANG.med_type_1_nhom_TCKT}"><div>{FORM_VALUE.nhomTCKT}</div></td>
                                    <td data-column="{LANG.med_type_1_nong_do}"><div>{FORM_VALUE.nongDo}</div></td>
                                    <td data-column="{LANG.med_type_1_duong_dung}"><div>{FORM_VALUE.duongDung}</div></td>
                                    <td data-column="{LANG.med_type_1_dang_bao_che}"><div>{FORM_VALUE.dangBaoChe}</div></td>
                                    <td data-column="{LANG.med_type_1_uom}"><div>{FORM_VALUE.uom}</div></td>
                                    <td data-column="{LANG.med_type_1_quantity}"><div>{FORM_VALUE.quantity}</div></td>
                                    <td class="text-center">
                                        <span class="span__buton">
                                            <button type="button" class="btn btn-primary btn-radius" data-toggle="modal" data-target=".bd-example-modal-lg" onclick="search_goods('{FORM_VALUE.tenHoatChat}','{ARR_DATA.investor_id}','{ARR_DATA.id}')">{LANG.reference}</button>
                                        </span>
                                    </td>
                                </tr>
                                <!-- END: td_loop_med_type_1 -->
                                <!-- BEGIN: td_loop_med_type_2 -->
                                <tr class='table-row' data-index='{FORM_VALUE.stt}'>
                                    <td data-column="{LANG.rq_pos}" class="text-center"><div>{FORM_VALUE.pos}</div></td>
                                    <td data-column="{LANG.med_type_2_medicineCode}"><div>{FORM_VALUE.medicineCode}</div></td>
                                    <td data-column="{LANG.med_type_2_tenHoatChat}"><div>{FORM_VALUE.tenHoatChat}</div></td>
                                    <td data-column="{LANG.med_type_2_tenThuoc}"><div>{FORM_VALUE.tenThuoc}</div></td>
                                    <td data-column="{LANG.med_type_2_nongDo}"><div>{FORM_VALUE.nongDo}</div></td>
                                    <td data-column="{LANG.med_type_2_duongDung}"><div>{FORM_VALUE.duongDung}</div></td>
                                    <td data-column="{LANG.med_type_2_dangBaoChe}"><div>{FORM_VALUE.dangBaoChe}</div></td>
                                    <td data-column="{LANG.med_type_2_uom}"><div>{FORM_VALUE.uom}</div></td>
                                    <td data-column="{LANG.med_type_2_quantity}"><div>{FORM_VALUE.quantity}</div></td>
                                    <td class="text-center">
                                        <span class="span__buton">
                                            <button type="button" class="btn btn-primary btn-radius" data-toggle="modal" data-target=".bd-example-modal-lg" onclick="search_goods('{FORM_VALUE.tenHoatChat}','{ARR_DATA.investor_id}','{ARR_DATA.id}')">{LANG.reference}</button>
                                        </span>
                                    </td>
                                </tr>
                                <!-- END: td_loop_med_type_2 -->
                                <!-- BEGIN: td_loop_med_type_3 -->
                                <tr class='table-row' data-index='{FORM_VALUE.stt}'>
                                    <td data-column="{LANG.rq_pos}" class="text-center"><div>{FORM_VALUE.pos}</div></td>
                                    <td data-column="{LANG.med_type_3_medicineCode}"><div>{FORM_VALUE.medicineCode}</div></td>
                                    <td data-column="{LANG.med_type_3_tenHoatChat}"><div>{FORM_VALUE.tenHoatChat}</div></td>
                                    <td data-column="{LANG.med_type_3_nhomTCKT}"><div>{FORM_VALUE.nhomTCKT}</div></td>
                                    <td data-column="{LANG.med_type_3_nongDo}"><div>{FORM_VALUE.nongDo}</div></td>
                                    <td data-column="{LANG.med_type_3_duongDung}"><div>{FORM_VALUE.duongDung}</div></td>
                                    <td data-column="{LANG.med_type_3_dangBaoChe}"><div>{FORM_VALUE.dangBaoChe}</div></td>
                                    <td data-column="{LANG.med_type_3_uom}"><div>{FORM_VALUE.uom}</div></td>
                                    <td data-column="{LANG.med_type_3_quantity}"><div>{FORM_VALUE.quantity}</div></td>
                                    <td class="text-center">
                                        <span class="span__buton">
                                            <button type="button" class="btn btn-primary btn-radius" data-toggle="modal" data-target=".bd-example-modal-lg" onclick="search_goods('{FORM_VALUE.tenHoatChat}','{ARR_DATA.investor_id}','{ARR_DATA.id}')">{LANG.reference}</button>
                                        </span>
                                    </td>
                                </tr>
                                <!-- END: td_loop_med_type_3 -->
                                <!-- BEGIN: td_loop_med_type_4 -->
                                <tr class='table-row' data-index='{FORM_VALUE.stt}'>
                                    <td data-column="{LANG.rq_pos}" class="text-center"><div>{FORM_VALUE.pos}</div></td>
                                    <td data-column="{LANG.med_type_4_tenHoatChat}"><div>{FORM_VALUE.tenHoatChat}</div></td>
                                    <td data-column="{LANG.med_type_4_nhomTCKT}"><div>{FORM_VALUE.nhomTCKT}</div></td>
                                    <td data-column="{LANG.med_type_4_tenThuoc}"><div>{FORM_VALUE.tenThuoc}</div></td>
                                    <td data-column="{LANG.med_type_4_medicineCode}"><div>{FORM_VALUE.medicineCode}</div></td>
                                    <td data-column="{LANG.med_type_4_dangBaoChe}"><div>{FORM_VALUE.dangBaoChe}</div></td>
                                    <td data-column="{LANG.med_type_4_qualityStandards}"><div>{FORM_VALUE.qualityStandards}</div></td>
                                    <td data-column="{LANG.med_type_4_uom}"><div>{FORM_VALUE.uom}</div></td>
                                    <td data-column="{LANG.med_type_4_quantity}"><div>{FORM_VALUE.quantity}</div></td>
                                    <td class="text-center">
                                        <span class="span__buton">
                                            <button type="button" class="btn btn-primary btn-radius" data-toggle="modal" data-target=".bd-example-modal-lg" onclick="search_goods('{FORM_VALUE.tenHoatChat}','{ARR_DATA.investor_id}','{ARR_DATA.id}')">{LANG.reference}</button>
                                        </span>
                                    </td>
                                </tr>
                                <!-- END: td_loop_med_type_4 -->
                                <!-- BEGIN: td_loop_med_type_5 -->
                                <tr class='table-row' data-index='{FORM_VALUE.stt}'>
                                    <td data-column="{LANG.rq_pos}" class="text-center"><div>{FORM_VALUE.pos}</div></td>
                                    <td data-column="{LANG.med_type_5_tenHoatChat}"><div>{FORM_VALUE.tenHoatChat}</div></td>
                                    <td data-column="{LANG.med_type_5_nhomTCKT}"><div>{FORM_VALUE.nhomTCKT}</div></td>
                                    <td data-column="{LANG.med_type_5_medicineCode}"><div>{FORM_VALUE.medicineCode}</div></td>
                                    <td data-column="{LANG.med_type_5_tenThuoc}"><div>{FORM_VALUE.tenThuoc}</div></td>
                                    <td data-column="{LANG.med_type_5_dangBaoChe}"><div>{FORM_VALUE.dangBaoChe}</div></td>
                                    <td data-column="{LANG.med_type_5_qualityStandards}"><div>{FORM_VALUE.qualityStandards}</div></td>
                                    <td data-column="{LANG.med_type_5_uom}"><div>{FORM_VALUE.uom}</div></td>
                                    <td data-column="{LANG.med_type_5_quantity}"><div>{FORM_VALUE.quantity}</div></td>
                                    <td class="text-center">
                                        <span class="span__buton">
                                            <button type="button" class="btn btn-primary btn-radius" data-toggle="modal" data-target=".bd-example-modal-lg" onclick="search_goods('{FORM_VALUE.tenHoatChat}','{ARR_DATA.investor_id}','{ARR_DATA.id}')">{LANG.reference}</button>
                                        </span>
                                    </td>
                                </tr>
                                <!-- END: td_loop_med_type_5 -->
                                <!-- BEGIN: td_loop_med_type_7 -->
                                <tr class='table-row' data-index='{FORM_VALUE.stt}'>
                                    <td data-column="{LANG.rq_pos}" class="text-center"><div>{FORM_VALUE.pos}</div></td>
                                    <td data-column="{LANG.med_type_7_medicineCode}"><div>{FORM_VALUE.medicineCode}</div></td>
                                    <td data-column="{LANG.med_type_7_tenHoatChat}"><div>{FORM_VALUE.tenHoatChat}</div></td>
                                    <td data-column="{LANG.med_type_7_nhomTCKT}"><div>{FORM_VALUE.nhomTCKT}</div></td>
                                    <td data-column="{LANG.med_type_7_nongDo}"><div>{FORM_VALUE.nongDo}</div></td>
                                    <td data-column="{LANG.med_type_7_duongDung}"><div>{FORM_VALUE.duongDung}</div></td>
                                    <td data-column="{LANG.med_type_7_dangBaoChe}"><div>{FORM_VALUE.dangBaoChe}</div></td>
                                    <td data-column="{LANG.med_type_7_uom}"><div>{FORM_VALUE.uom}</div></td>
                                    <td data-column="{LANG.med_type_7_quantity}"><div>{FORM_VALUE.quantity}</div></td>
                                    <td class="text-center">
                                        <span class="span__buton">
                                            <button type="button" class="btn btn-primary btn-radius" data-toggle="modal" data-target=".bd-example-modal-lg" onclick="search_goods('{FORM_VALUE.tenHoatChat}','{ARR_DATA.investor_id}','{ARR_DATA.id}')">{LANG.reference}</button>
                                        </span>
                                    </td>
                                </tr>
                                <!-- END: td_loop_med_type_7 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="pagination-custom text-center">
                <nav>
                    <ul class="pagination" id="pagination1"></ul>
                </nav>
            </div>
        </div>
        <!-- END: form_value -->

        <div class="bidding-detail">
            <!-- BEGIN: gen_rq_file -->
            {VIEW.gen_rq_file}
            <!-- END: gen_rq_file -->
            <!-- BEGIN: gen_other_file -->
            {VIEW.gen_other_file}
            <!-- END: gen_other_file -->
        </div>
    </div>
    <!-- BEGIN: decision -->
    <div class="scoll__menu1">
        <div class="bidding-sub-title display-flex">
            {LANG.rq_decision_info}
        </div>
        <div class="bidding-detail">
            <!-- BEGIN: decision_date -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.decision_date}</div>
                <div class="c-val">{VIEW.decision_date}</div>
            </div>
            <!-- END: decision_date -->
            <!-- BEGIN: decision_no -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.decision_no}</div>
                <div class="c-val">{VIEW.decision_no}</div>
            </div>
            <!-- END: decision_no -->
            <!-- BEGIN: gen_decision_file -->
            {VIEW.gen_decision_file}
            <!-- END: gen_decision_file -->
        </div>
    </div>
    <!-- END: decision -->

    <!-- BEGIN: cancel_reason -->
    <div class="scoll__menu1">
        <div class="bidding-sub-title display-flex">
            {LANG.rq_cancel_info}
        </div>
        <div class="bidding-detail">
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.cancel_reason}</div>
                <div class="c-val">{VIEW.cancel_reason}</div>
            </div>

            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.cancel_decision_no}</div>
                <div class="c-val">{VIEW.cancel_decision_no}</div>
            </div>

            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.cancel_decision_date}</div>
                <div class="c-val">{VIEW.cancel_decision_date}</div>
            </div>
            <!-- BEGIN: gen_cancel_file -->
            {VIEW.gen_cancel_file}
            <!-- END: gen_cancel_file -->
        </div>
    </div>
    <!-- END: cancel_reason -->

    <!-- BEGIN: history_delay -->
    <div class="scoll__menu1">
        <div class="bidding-sub-title display-flex">
            {LANG.rq_renewal_info}
        </div>
        <div class="bidding-detail">
            <div class="bidding-detail-item">
                <div class="c-val">
                    <table class="bidding-table">
                        <colgroup>
                            <col style="width: 10%;">
                            <col style="width: 15%;">
                            <col style="width: 15%;">
                            <col style="width: 15%;">
                            <col style="width: 45%;">
                        </colgroup>
                        <thead>
                            <tr class="info">
                                <th class="text-center">{LANG.rq_pos}</th>
                                <th class="text-center">{LANG.rq_success_renewal_time}</th>
                                <th class="text-center">{LANG.expiry_date_old_rq}</th>
                                <th class="text-center">{LANG.expiry_date_new_rq}</th>
                                <th class="text-center limit-w-mobile">{LANG.rq_reason_renewal}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- BEGIN: loop -->
                            <tr>
                                <td data-column="{LANG.rq_pos}">
                                    <div>
                                        {STT}
                                    </div>
                                </td>
                                <td data-column="{LANG.rq_success_renewal_time}">
                                    <div>{HISTORY_DELAY.success_renewal_time}</div>
                                </td>
                                <td data-column="{LANG.expiry_date_old_rq}">
                                    <div>{HISTORY_DELAY.expiry_date_old_rq}</div>
                                </td>
                                <td data-column="{LANG.expiry_date_new_rq}">
                                    <div>{HISTORY_DELAY.expiry_date_new_rq}</div>
                                </td>
                                <td data-column="{LANG.rq_reason_renewal}" class="limit-w-mobile">
                                    <div>
                                        {HISTORY_DELAY.reason}
                                    </div>
                                </td>
                            </tr>
                            <!-- END: loop -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- END: history_delay -->

    <!-- BEGIN: ycbg_lien_quan -->
    <!-- BEGIN: yes_relate -->
    <div class="bidding-detail">
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.rq_ycbg_version}</div>
            <div class="c-val link-icon">
                <!-- BEGIN: loop -->
                <p>
                    <label><input type="checkbox" {RELATE.checked} value="{RELATE.id_ycbg}" class="chk_notification"> {RELATE.public_date} {RELATE.title_checked}</label> - <a href="{RELATE.link_relate}">{RELATE.title_tb}</a> {RELATE.title_tb_first} <span class="info_change_tb" data-tb='{RELATE.key}'>{RELATE.text_changetb}</span> <span class="link_change_a">(<a href="javascript:void(0)" data-id="{RELATE.data_id_ycbg}" data-toggle="modal" data-target="#showTB">{LANG.title_change_tb}</a>)
                    </span>
                </p>
                <!-- END: loop -->
                <span data-toggle="tooltip" data-placement="right" title="{LANG.tooltip_tb}">
                    <button class="btn btn-primary btn-xs" id="view_change_tb" data-toggle="modal" data-target="#showTB">{LANG.title_change_tb}</button>
                    <button class="btn btn-primary btn-xs" id="view_change_tb1">{LANG.title_change_tb}</button>

                </span> <label class="label label-warning">{NO_RELATE}</label>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="showTB" class="modal fade" role="dialog">
        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{LANG.title_ssycbg}</h4>
                </div>
                <div class="modal-body">
                    <div id="main_change_notificaion">
                        <div class="change_notificaion">
                            <div class="row">
                                <div class="col-md-24">
                                    <label>{LANG.kieusosanh}: </label> <label><input type="radio" name="sosanhtb" checked value="1">&nbsp;{LANG.title_muti}</label> <label><input type="radio" name="sosanhtb" value="2">&nbsp;{LANG.title_one}</label>
                                </div>
                                <div class="col-md-24">
                                    <div class="card">
                                        <label><input type="checkbox" class="change_by_line" value="3">&nbsp;{LANG.title_linebyline}</label>
                                        <div class="row_compare">
                                            <div class="col">
                                                <div class="card" id="outputOriginal"></div>
                                            </div>
                                            <div class="col">
                                                <div class="card" id="output"></div>
                                            </div>
                                            <div class="col" id="new_version">
                                                <div class="card" id="outputNew"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{LANG.close}</button>
                </div>
            </div>

        </div>
    </div>

    <!-- END: yes_relate -->
    <!-- END: ycbg_lien_quan -->

    <!-- BEGIN: chance_for_you -->
    <div id="tienich" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.chance_for_you}</h2>
        <div class="chance-cont" id="box_for_you">
            <div class="chance">
                <!-- BEGIN: follow_reg -->
                <div class="panel panel-default">
                    <div class="cont panel-heading">
                        <div class="tl">{LANG.follow_rq}</div>
                        <div class="ct">{FOLLOW.content}</div>
                        <div class="bt">
                            <button type="button" class="btn btn-primary" onclick="click_follow('{VIEW.link_follow}', '{CHECKSESS}', this, '{YCBG_NO}', '{USERID}')">{FOLLOW.title}</button>
                        </div>
                    </div>
                </div>
                <!-- END: follow_reg -->
                <!-- BEGIN: receive_email_content -->
                <div class="panel panel-default">
                    <div class="cont panel-heading">
                        <div class="tl">{LANG.receive_email_rq}</div>
                        <div class="ct">{RECEIVE_EMAIL.content}</div>
                        <div class="bt">
                            <a class="btn btn-danger" href="{RECEIVE_EMAIL.reg_link}">{RECEIVE_EMAIL.vip_paket}</a>
                        </div>
                    </div>
                </div>
                <!-- END: receive_email_content -->
            </div>
        </div>
    </div>
    <!-- END: chance_for_you -->

    [BLOCK_SUPPORT]

    <div class="open_record_viewopen">
        <div id="myModal" class="modal fade" role="dialog"></div>
    </div>

    <div class="margin-top margin-bottom display-flex">
        <div class="h3">
            <span class="label label-primary">{LANG.totalview}: <span class="badge">{VIEW.totalview}</span></span>
        </div>
    </div>
</div>

<div id="confirm" class="modal fade auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body"></div>
            <div class="modal-footer">
                <span class="button"></span>
                <button type="button" class="ok btn btn-primary">{LANG.ok}</button>
                <button type="button" data-dismiss="modal" class="btn">{LANG.close}</button>
            </div>
        </div>
    </div>
</div>

{FILE "modal_log.tpl"}

<!-- START FORFOOTER -->
<div class="modal fade bd-example-modal-lg modal-goods" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">×</button>
            </div>
            <div class="modal-body">
                <p>{LANG.title_h5}:</p>
                <div class="responsivetb">
                    <table class="table table-bordered table-hover" id="search_good1"></table>
                </div>
                <div class="text-center spin hidden">
                    <i class="text-center fa fa-spin fa-spinner fa-lg"></i>
                </div>
                <blockquote id="quote1">{LANG.quote1}</blockquote>
                <div class="text-center">
                    <a class="hidden" target="_blank" id="link1" href="">{LANG.see_more}...</a>
                </div>
                <p>{LANG.title_h5_2}:</p>
                <div class="responsivetb">
                    <table class="table table-bordered table-hover" id="search_good"></table>
                </div>
                <div class="text-center spin hidden">
                    <i class="text-center fa fa-spin fa-spinner fa-lg"></i>
                </div>
                <blockquote id="quote2">{LANG.quote2}</blockquote>
                <div class="text-center">
                    <a class="hidden" target="_blank" id="link2" href="">{LANG.see_more}...</a>
                </div>

                <p>{LANG.title_h5_3}:</p>
                <div class="responsivetb">
                    <table class="table table-bordered table-hover" id="search_good3"></table>
                </div>
                <div class="text-center spin hidden">
                    <i class="text-center fa fa-spin fa-spinner fa-lg"></i>
                </div>
                <blockquote id="quote3">{LANG.quote3}</blockquote>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{LANG.close}</button>
            </div>
        </div>
    </div>
    <div id="trudiem"></div>
</div>
<!-- END FORFOOTER -->

<style>
    #trudiem {
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background: #d15c18d1;
        padding: 10px;
        color: #ffffff;
        margin: 0;
        box-shadow: 0px 5px 10px rgb(141 136 136 / 60%);
        border-radius: 2px;
        display: none;
    }

    #trudiem p {
        margin: 0;
    }
    .tooltip {
        width: 400px;
    }
</style>

<script type="text/javascript">
    var checkess='{NV_CHECK_SESSION}';
    const LNG = {read:"{LANG.read}", origlink:"{LANG.origlink}", otherlink:"{LANG.otherlink}", viplink: "{LANG.viplink}"};

    startTimer({COUNTDOWN}, $(".countdown"));

    link_reformat();

    $("#subdiv").hide();

    $(document).ready(function($) {

        $(document).on("click", ".download-file",function(){
            var idfile = $(this).attr("id-file");
            var checkss = $(this).attr("checkss");
            var typefile = $(this).attr("typefile");
            if (idfile !== "undefined" ) {
                $.ajax({
                    url: location.href,
                    type: 'POST',
                    data: {
                        'action': 'download_static',
                        'idfile' : idfile,
                        'did' : {VIEW.id},
                        'type_file' : typefile,
                        'checkss' : checkss
                    },
                    success: function(data) {
                        if (data['res'] == 'success') {
                            window.open(data['link'], '_blank');
                        } else {
                            alert(data['message']);
                        }
                    }
                })
            }
        });

        $(".btn__open").click(function(event) {
            $.ajax({
                url: window.location.href,
                type: 'POST',
                data: {
                    'action':'open_record',
                    'status': $(this).val()
                },
                success:function(data) {
                    $(".open_record_viewopen > #myModal").html(data)
                }
            })
        });

        $("#view").change(function() {
            if ($(this).val() == 1) {
                $("#subdiv").css("visibility", "visible")
                $("#subdiv").show(400);
                $("#theonhathau").hide();

                setTimeout(function() {
                    calTheadWidth();
                }, 200);
            }

            if ($(this).val() == 2) {
                $("#theonhathau").css("visibility", "visible")
                $("#theonhathau").show(400);
                $("#subdiv").hide();
            }
        });
    });
</script>

<script type="text/javascript">
    $(function() {
        update_id_checkbox();
        $('[data-toggle="tooltip"]').tooltip();
        // Danh sách thông báo
        DATA_TB = '{DATA_TB}';
        $(".link_change_a:last").hide();

        $(".chk_notification").change(function(){
            var numberOfChecked = $('.chk_notification:checkbox:checked').length;

            if (numberOfChecked >= 2) {
                $('.chk_notification:checkbox').not(':checked').prop("disabled", true);
            } else {
                $('.chk_notification:checkbox').not(':checked').prop("disabled", false);
            }
            update_id_checkbox();
        });

        var title = $("#view_change_tb").parent().attr("data-original-title");
        function update_id_checkbox() {
            var $boxes = $('.chk_notification[type=checkbox]:checked');
            arrid = [];
            $boxes.each(function(v, k){
               arrid.push(k.value);
            });

            $("#view_change_tb").val(arrid.join("-"));
            if ($boxes.length < 2) {
                $("#view_change_tb1").removeClass("damdam");
                $("#view_change_tb1").addClass("momo");
                $("#view_change_tb").parent().attr("data-original-title", title);
                $("#view_change_tb1").show();
                $("#view_change_tb").hide();
            } else {
                $("#view_change_tb1").removeClass("momo");
                $("#view_change_tb1").addClass("damdam");
                $("#view_change_tb").parent().attr("data-original-title", "");
                $("#view_change_tb1").hide();
                $("#view_change_tb").show();
            }
        }

        $("#view_change_tb, .link_change_a a").click(function() {
            id = $(this).attr('data-id');
            if (id === undefined) {
                id = $(this).val();
            }

            // Ẩn chế độ 1 cột trên mobile
            if (window.matchMedia("(max-width: 768px)").matches) {
                $("input[name='sosanhtb'][value='1']").closest('label').hide();
                $("input[name='sosanhtb'][value='2']").click();
            } else {
                $("input[name='sosanhtb'][value='1']").closest('label').show();
                $("input[name='sosanhtb'][value='1']").click();
            }

            $.ajax({
                url: location.href,
                type: 'POST',
                data: {
                    'action': 'view_change_tb',
                    'id' : id
                },
                success: function(data) {
                    $("#outputOriginal tbody").html("");
                    $("#output tbody").html("");
                    if (data['res'] == 'error') {
                        alert(data['data']);
                        $("#showTB").modal('hide');
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                        return false;
                    }

                    compare_highlight_difference("outputOriginal", "output", "outputNew", false, JSON.stringify(data['data']['thongbao']), JSON.stringify(data['data']['thongbao1']));
                    tr_0 = $(".table__tb").eq(0).find('.table__tb-tbody > tr');
                    tr_1 = $(".table__tb").eq(1).find('.table__tb-tbody > tr');
                    for (i = 0; i < tr_0.length; i++) {
                        tr_0.eq(i).attr('data-row', i+1);
                    }

                    for (i = 0; i < tr_1.length; i++) {
                        tr_1.eq(i).attr('data-row', i+1);
                    }

                    html_old = $("#outputOriginal tbody").html();
                    html1_old = $("#output tbody").html();
                    $(".change_by_line").click(function() {
                        view_change_line(html_old, html1_old);
                    });

                    change_by_line = $(".change_by_line:checked").val();

                    if (change_by_line == '3') {
                        view_change_line(html_old, html1_old);
                    }
                }
            })
        });



        function view_change_line(html_old, html1_old) {
            arr_show = [];
            table = $(".change_by_line").closest('#main_change_notificaion').find('#output').find('table');
            del = table.find('tr').find('td');
            for (i = 0; i < del.length; i++) {
                row = del.eq(i).find('del').length;
                if (!row) {
                    row = del.eq(i).find('ins').length;
                }
                if (row) {
                    data_row = del.eq(i).closest('tr').attr('data-row');
                    arr_show.push(data_row);
                }
            }
            arr_show = [...new Set(arr_show)];

            console.log(arr_show);
            html = '';
            html1 = '';

            for (i = 0; i < arr_show.length; i++) {
                html += '<tr data-row="' + arr_show[i] +'">' + $('#outputOriginal .table__tb tr[data-row="' + arr_show[i] +'"]').html() + "</tr>";
                html1 += '<tr data-row="' + arr_show[i] +'">' + $('#output .table__tb tr[data-row="' + arr_show[i] +'"]').html() + "</tr>";
            }

            if($(".change_by_line").is(':checked')) {
                $("#outputOriginal tbody").html(html);
                $("#output tbody").html(html1);
            } else {
                $("#outputOriginal tbody").html(html_old);
                $("#output tbody").html(html1_old);
            }
        }

        // $("#linebyline").hide();
        $("input[name='sosanhtb']").change(function() {
            if ($(this).val() == 2) {
                $("#outputOriginal").parent().hide(500);
                $("#output").parent().show(500);
            } else if ($(this).val() == 3) {
                $("#outputOriginal").parent().hide();
                $("#output").parent().hide();

            } else {
                $("#outputOriginal").parent().show(500);
                $("#output").parent().show(500);
            }
        });
        $('#crawl_request_history').on('click', function () {
            $('#crawl_request_history_list').slideToggle(150);
        });

        
    });
    //search_goods
    function search_goods(name,investor_id, id) {
        var data = {
            'action_search_goods': 1,
            'name': name,
            'investor_id': investor_id,
            'id_ycbg': id
        }
        var key = name.replaceAll(" ", "%20");
        $("#link1").prop("href", '{LINK_HANGHOA}'+'?type_search=1&type=goods_name&key='+key+'&solicitor_id='+investor_id);
        $("#link2").prop("href", '{LINK_HANGHOA}'+'?type_search=2&type=goods_name&key='+key+'&solicitor_id='+investor_id);
        $('#search_good').html('');
        $('#search_good1').html('');
        $('.spin').removeClass('hidden');
        $('#quote1').addClass('hidden');
        $('#quote2').addClass('hidden');
        $.ajax({
            type: "POST",
            url: window.location.href,
            cache: !1,
            data: data,
            success: function (res) {
                $('.spin').addClass('hidden');
                if (res.html2 != '') {
                    $('#search_good').html(res.html2);
                    $('#quote2').addClass('hidden');
                    $('#link2').removeClass('hidden');
                }else {
                    $('#quote2').removeClass('hidden');
                    $('#link2').addClass('hidden');
                }
                if (res.html1 != '') {
                    $('#search_good1').html(res.html1);
                    $('#quote1').addClass('hidden');
                    $('#link1').removeClass('hidden');
                } else {
                    $('#quote1').removeClass('hidden');
                    $('#link1').addClass('hidden');
                }
                if (res.html3 != '') {
                    $('#search_good3').html(res.html3);
                    $('#quote3').addClass('hidden');
                    $('#link3').removeClass('hidden');
                } else {
                    $('#quote3').removeClass('hidden');
                    $('#link3').addClass('hidden');
                }
                $('.spin').addClass('hidden');
            }
        })
    }
    function view_price(id, stbmt, link_name) {
        $html = '';
        $html += '<td data-column="{LANG.so_tbmt}"> <span class="so_tbmt_' + id +'"></span></td>';
        $html += '<td data-column="{LANG.win_price}"><span class="win_price_' + id +'"></span></td>';
        //console.log($(this));
        $(".view__price" + id).closest('tr').append($html);
        $(".view__price" + id).parent().remove();

        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                'view_price': 1,
                'stbmt': stbmt,
                'link_name': link_name,
                'id_goods': id
            },
            success: function (res) {
                result = JSON.parse(res.mess);
                if (result['khongdudiem'] == 1) {
                    alert("{LANG.point_miss_goods}");
                } else {
                    $(".win_price_" + result['data']['id']).html("<a href=" + result['linkgoithau'] + ">" + result['tbmt'] + "</a>");
                    $(".so_tbmt_" + result['data']['id']).html(result['data']['bid_price']);
                    $("#trudiem").html(result['notifi']);
                    $("#trudiem").slideDown(500);
                    setTimeout(function() {
                        $("#trudiem").slideUp(500);
                    }, 2000);
                }
            }
        })
    }
</script>

<!-- BEGIN: error -->
<div class="alert alert-danger">{LANG.error_data}</div>
<!-- END: error -->

<div class="filter-overlay" id="filter-overlay"></div>
<div class="filter-popup" id="filter-popup">
    <p><b>{LANG.title_select_solumns_to_display}</b></p>
    <label><input type="checkbox" id="select-all-columns" checked>&nbsp;{LANG.select_all_cl}</label><br>
    <div id="filter-options"></div>
    <div class="text-center">
        <button class="btn btn-default btn-sm" id="close-filter">{LANG.close}</button>
        <button class="btn btn-primary btn-sm" id="apply-filter">{LANG.title_apply}</button>
    </div>
</div>

<script type="text/javascript">
    (() => {
        var totalRows = $("#data-container .table-row").length;
        var rowsPerPage = "{PER_PAGE}";
        var totalPages = Math.ceil(totalRows / rowsPerPage);
        var currentPage = 1;

        // Hiển thị trang
        function displayRows(page) {
            const rows = $("#data-container .table-row");
            rows.hide();

            const start = (page - 1) * rowsPerPage;
            const end = page * rowsPerPage;

            rows.slice(start, end).show();
        }

        // Function khởi tạo phân trang và các xử lý phân trang bên dưới đây
        function renderPagination() {
            const paginationContainer = $("#pagination1");
            paginationContainer.empty();

            // Previous button
            if (currentPage > 1) {
                const prevLi = $("<li><a href='#'>«</a></li>");
                prevLi.click(function (e) {
                    e.preventDefault();
                    changePage(currentPage - 1);
                });
                paginationContainer.append(prevLi);
            }

            // First Page button
            const firstPageLi = $("<li><a href='#'>1</a></li>");
            firstPageLi.click(function (e) {
                e.preventDefault();
                changePage(1);
            });
            if (currentPage === 1) firstPageLi.addClass('active');  // Set active if on page 1
            paginationContainer.append(firstPageLi);

            // Pages and "..."
            if (currentPage > 3) {
                paginationContainer.append("<li><a href='#'>...</a></li>");
            }

            const pageNumbers = [];
            for (let i = Math.max(2, currentPage - 2); i <= Math.min(totalPages - 1, currentPage + 2); i++) {
                pageNumbers.push(i);
            }

            pageNumbers.forEach(function (page) {
                createPageButton(page, paginationContainer, currentPage === page);
            });

            if (currentPage < totalPages - 2) {
                paginationContainer.append("<li><a href='#'>...</a></li>");
            }

            let lastPageLi;
            // Last Page button
            if (totalPages > 1) {
                lastPageLi = $("<li><a href='#'>" + totalPages + "</a></li>");
                lastPageLi.click(function (e) {
                    e.preventDefault();
                    changePage(totalPages);
                });
            }


            if (totalPages > 1 && currentPage === totalPages) lastPageLi.addClass('active');  // Set active if on last page
            paginationContainer.append(lastPageLi);

            // Next button
            if (currentPage < totalPages) {
                const nextLi = $("<li><a href='#'>»</a></li>");
                nextLi.click(function (e) {
                    e.preventDefault();
                    changePage(currentPage + 1);
                });
                paginationContainer.append(nextLi);
            }
        }

        // Function to create page button
        function createPageButton(page, container, isActive = false) {
            const li = $("<li>").addClass(isActive ? "active" : "");
            const a = $("<a href='#'>").text(page);
            a.click(function (e) {
                e.preventDefault();
                changePage(page);
            });
            li.append(a);
            container.append(li);
        }

        // Change current page
        function changePage(page) {
            currentPage = page;
            displayRows(currentPage);
            renderPagination();
            // Di chuyển tới đầu table
            $("html, body").animate({
                scrollTop: $("#search-input").offset().top - 80
            }, 500);
        }

        $("#search-input").on("input", function () {
            const query = $(this).val().trim().toLowerCase();
            const rows = $("#data-container .table-row");
            // Nếu ô tìm kiếm trống, hiển thị lại toàn bộ danh sách
            if (query === '') {
                rowsPerPage = 10;
                rows.show(); // Hiển thị tất cả các hàng
                totalPages = Math.ceil(rows.length / rowsPerPage); // Cập nhật lại số trang
                currentPage = 1; // Đặt lại trang về 1
                displayRows(currentPage); // Hiển thị các hàng theo trang hiện tại
                renderPagination(); // Cập nhật phân trang
                return;
            }

            // Lọc các hàng phù hợp với từ khóa
            filteredRowsGlobal = rows.filter(function () {
                const text = $(this).text().toLowerCase();
                return text.includes(query); // Giữ các hàng phù hợp
            });

            rows.hide(); // Ẩn tất cả các hàng
            filteredRowsGlobal.show(); // Hiển thị hàng tìm được

            // Cập nhật số trang dựa trên kết quả tìm kiếm
            const filteredCount = filteredRowsGlobal.length;
            totalPages = Math.floor(filteredCount / rowsPerPage);

            if (filteredCount > 50) {
                $(".title_waring_max_row small").text("{LANG.title_waring_max_row}");
            } else {
                $(".title_waring_max_row small").text("");
            }

            totalPages = 0;

            // Set dữ liệu tìm kiếm khoảng 50 rows
            rowsPerPage = 50;

            // Reset về trang đầu tiên và hiển thị kết quả
            currentPage = 1;
            displayFilteredRows(currentPage);
            renderPagination();
        });

        $("#search-input").on("change", function () {
            query = $(this).val().trim();
            $(this).val(query);
        });

        function displayFilteredRows(page) {
            const start = (page - 1) * rowsPerPage;
            const end = page * rowsPerPage;

            filteredRowsGlobal.hide(); // Ẩn tất cả các hàng
            filteredRowsGlobal.slice(start, end).show(); // Hiển thị hàng trong phạm vi trang
        }

        // Filter Popup functionality
        const filterBtn = $("#filter-btn");
        const filterPopup = $("#filter-popup");
        const filterOverlay = $("#filter-overlay");
        const closeFilterBtn = $("#close-filter");
        const applyFilterBtn = $("#apply-filter");
        const filterOptions = $("#filter-options");

        // arr select column bảng tb
        let selectedColumns = [];

        // Load các danh sách tr table ra
        function populateFilterOptions() {
            filterOptions.empty();

            // Nếu selectedColumns rỗng, mặc định chọn tất cả các cột
            if (selectedColumns.length === 0) {
                $(".column-header").each(function () {
                    const column = $(this).data("column");
                    selectedColumns.push(column);
                });
            }

            $(".column-header").each(function () {
                let column = $(this).data("column");
                let text = $(this).text();
                isChecked = selectedColumns.includes(column); // Luôn đúng do đã gán mặc định
                if (column  <= 2) {
                    disabled = 'disabled';
                } else {
                    disabled = '';
                }
                let checkbox = '<label><input type="checkbox" ' + disabled + '  class="column-checkbox" data-column="' + column + '"' + (isChecked ? "checked" : "") + '>&nbsp;' + text + '</label><br>';
                filterOptions.append(checkbox);
            });
        }

        // Mở filter popup
        filterBtn.click(function () {
            populateFilterOptions();
            filterPopup.show();
            filterOverlay.show();
        });

        // Đóng popup
        closeFilterBtn.click(function () {
            filterPopup.hide();
            filterOverlay.hide();
        });

        // Cập nhật lựa chọn hiển thị hoặc ẩn cột
        applyFilterBtn.click(function () {
            selectedColumns = $(".column-checkbox:checked").map(function () {
                return $(this).data("column");
            }).get();  // Update selectedColumns array with checked columns

            // Show/hide columns based on selected checkboxes
            $(".column-header").each(function () {
                const column = $(this).data("column");
                $(this).toggle(selectedColumns.includes(column));
            });

            $(".table-row").each(function () {
                const columns = $(this).children();
                columns.each(function (index) {
                    const columnIndex = index + 1;
                    $(this).toggle(selectedColumns.includes(columnIndex));
                });
            });

            filterPopup.hide();
            filterOverlay.hide();
        });

        // Xử lý sự kiện checkbox "Chọn tất cả"
        $("#select-all-columns").on("change", function () {
            let isChecked = $(this).is(":checked");
            $(".column-checkbox:not([disabled])").each(function () {
                $(this).prop("checked", isChecked);
            });
        });

        // Handle rows theo select người dùng chọn...
        $("#rows-per-page").change(function () {
            const selectedValue = $(this).val(); // Lấy giá trị mới từ selector

            if (selectedValue === "all") {
                rowsPerPage = totalRows; // Hiển thị tất cả các dòng
                totalPages = 1; // Chỉ có một trang
                currentPage = 1; // Reset về trang đầu tiên
                displayRows(currentPage); // Hiển thị tất cả các dòng
                renderPagination(); // Cập nhật phân trang
            } else {
                rowsPerPage = parseInt(selectedValue, 10); // Chuyển đổi giá trị thành số nguyên
                totalPages = Math.ceil(totalRows / rowsPerPage); // Cập nhật tổng số trang
                currentPage = 1; // Reset về trang đầu tiên
                displayRows(currentPage); // Hiển thị các dòng tương ứng
                renderPagination(); // Cập nhật phân trang
            }
        });

        // Khởi tạo table and phân trang
        function init() {
            displayRows(currentPage);
            renderPagination();
        }

        // Khởi tạo bảng tb
        init();
    })();
</script>
<!-- END: main -->
