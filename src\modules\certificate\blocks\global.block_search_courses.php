<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_MOD_CERTIFICATE')) {
    exit('Stop!!!');
}

if (!function_exists('nv_bidding_block_search_courses')) {


    /**
     * nv_block_search_courses()
     *
     * @param mixed $block_config
     * @return
     */
    function nv_block_search_courses($block_config)
    {
        global $db, $array_data, $nv_Lang, $nv_Request, $nv_Cache, $module_file, $module_config, $is_advance, $module_name, $op, $module_info, $array_op, $site_mods;
        $module = $block_config['module'];
        $mod_data = $site_mods[$module]['module_data'];
        $mod_file = $site_mods[$module]['module_file'];
        $nv_Lang->changeLang();
        $nv_Lang->loadModule($module_file, false, true);
        $type = $nv_Request->get_int('type', 'get', 0) == 1 ? 2 : 1;
        $skey = $nv_Request->get_title('q', 'get,post', '');
        $certificate_singer = $nv_Request->get_title('certificate_singer', 'get,post', '');
        $teacher = $nv_Request->get_title('teacher', 'get,post', '');
        $role = $nv_Request->get_title('role', 'get,post', '');
        $certificate_code = $nv_Request->get_title('certificate_code', 'get,post', '');
        $identify_code = $nv_Request->get_title('identify_code', 'get,post', '');
        $name_training = $nv_Request->get_title('training', 'get,post', '');
        $type_info = $nv_Request->get_int('type_couse', 'get', 0);
        $type_search = $nv_Request->get_int('typecourse', 'get', 1);
        $sregion = $nv_Request->get_title('idregion', 'get', '');
        $_idprovince = $nv_Request->get_array('idprovince', 'get,post', []); 
        $result = $nv_Request->get_int('result', 'get', 0);
        
        if (empty($type_info)) {
            if ($module == $module_name and isset($module_info['funcs'][$op])) {
                if ($module_info['funcs'][$op]['func_name'] == 'courses' || $module_info['funcs'][$op]['func_name'] == 'training') {
                    $type_info = 2;
                } else if ($module_info['funcs'][$op]['func_name'] == 'student-list') {
                    $type_info = 3;
                } else {
                    $type_info = 1;
                }
                if ($module_info['funcs'][$op]['func_name'] == 'training') {
                    $name_training = $nv_Request->get_title('training', 'get,post', $array_data['name']);
                }
            }
        }
        if (file_exists(NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $mod_file . "/block_search_courses.tpl")) {
            $block_theme = $module_info['template'];
        } else {
            $block_theme = "default";
        }

        $xtpl = new XTemplate("block_search_courses.tpl", NV_ROOTDIR . "/themes/" . $block_theme . "/modules/" . $mod_file);
        $from = nv_date('d/m/Y', NV_CURRENTTIME - (13 * 86400)); // Mặc định 14 ngày gầy đây
        $to = nv_date('d/m/Y', NV_CURRENTTIME);
        $sfrom = nv_substr($nv_Request->get_title('sfrom_certificate_date', 'get', $from), 0, 10);
        $sto = nv_substr($nv_Request->get_title('sto_certificate_date', 'get', $to), 0, 10);
        if (!preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sfrom)) {
            $sfrom = $from;
        }
        if (!preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sto)) {
            $sto = $to;
        }
        $sfrom_time_traning = nv_substr($nv_Request->get_title('sfrom_time_traning', 'get', $from), 0, 10);
        $sto_time_traning = nv_substr($nv_Request->get_title('sto_time_traning', 'get', $to), 0, 10);
        if (!preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sfrom_time_traning)) {
            $sfrom_time_traning = $from;
        }
        if (!preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sto_time_traning)) {
            $sto_time_traning = $to;
        }
        $sfrom_birthday = nv_substr($nv_Request->get_title('sfrom_birthday', 'get', ''), 0, 10);
        $sfrom_issuedate = nv_substr($nv_Request->get_title('sfrom_issuedate', 'get', $from), 0, 10);
        $sto_issuedate = nv_substr($nv_Request->get_title('sto_issuedate', 'get', $to), 0, 10);
        if (!preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sfrom_issuedate)) {
            $sfrom_issuedate = $from;
        }
        if (!preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sto_issuedate)) {
            $sto_issuedate = $to;
        }
        $regions_list = [
            '0' => [
                'title' => $nv_Lang->getModule('undefined'),
            ],
            '1' => [
                'title' => $nv_Lang->getModule('north'),
            ],
            '2' => [
                'title' => $nv_Lang->getModule('central'),
            ],
            '3' => [
                'title' => $nv_Lang->getModule('south'),
            ],
        ];
        foreach ($regions_list as $key => $t) {
            $xtpl->assign('REGION', array(
                'key' => $key,
                'title' => $t['title'],
                'selected' => $key == $sregion ? ' selected="selected"' : ''
            ));
            $xtpl->parse('main.loopregions');
        }

        $result_list = [
            '0' => [
                'title' => $nv_Lang->getModule('optionresult'), 
            ],
            '1' => [
                'title' => $nv_Lang->getModule('average'), 
            ],
            '2' => [
                'title' => $nv_Lang->getModule('good'),
            ],
            '3' => [
                'title' => $nv_Lang->getModule('excellent'),
            ],
            '4' => [
                'title' => $nv_Lang->getModule('outstanding'),
            ],
        ];

        // Lặp qua danh sách kết quả để hiển thị trong template
        foreach ($result_list as $key => $t) {
            $xtpl->assign('RESULT', array(
                'key' => $key,
                'title' => $t['title'],
                'selected' => $key == $result ? ' selected="selected"' : '' // Gắn selected nếu phù hợp
            ));
            $xtpl->parse('main.loopresult');
        }
        $maxspan = defined('NV_IS_ADMIN') ? 24 : $module_config['bidding']['search_mysql_range'];
        $xtpl->assign('MAXSPAN', $maxspan);

        if ($maxspan % 12 == 0) {
            // Chuyển tháng lên năm
            $xtpl->assign('LAST_MAXSPAN_LANG', sprintf($nv_Lang->getModule('last_maxspan_years'), $maxspan / 12));
        } else {
            // Hiển thị tháng
            $xtpl->assign('LAST_MAXSPAN_LANG', sprintf($nv_Lang->getModule('last_maxspan_months'), $maxspan));
        }
        $xtpl->assign('LANG', \NukeViet\Core\Language::$tmplang_module);
        $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
        $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
        $xtpl->assign('NV_MY_DOMAIN', NV_MY_DOMAIN);
        $xtpl->assign('TEMPLATE', $block_theme);
        $xtpl->assign('MODULE_FILE', $module_file);
        $xtpl->assign('BLOCKID', $block_config['bid']);
        $xtpl->assign('Q', $skey); // từ khóa chính
        $xtpl->assign('Q3', $certificate_singer);
        $xtpl->assign('Q4', $teacher);
        $xtpl->assign('Q5', $role);
        $xtpl->assign('Q6', $certificate_code);
        $xtpl->assign('Q7', $identify_code);
        $xtpl->assign('Q8', $name_training);
        $xtpl->assign('FROM', $sfrom);
        $xtpl->assign('FROMTIMETRANING', $sfrom_time_traning);
        $xtpl->assign('FROMTIMEBIRTHDAY', $sfrom_birthday);
        $xtpl->assign('FROMISSUE', $sfrom_issuedate);
        $xtpl->assign('FROM_DEFAULT', $from);
        $xtpl->assign('TOTIMETRANING', $sto_time_traning);
        $xtpl->assign('TOISSUE', $sto_issuedate);
        $xtpl->assign('TO', $sto);
        $xtpl->assign('TO_DEFAULT', $to);
        $_module = $block_config['module'];
        if ($type_search == 2) {
            $xtpl->assign('TYPE_SEARCH2', ' checked');
        } else if ($type_search == 3) {
            $xtpl->assign('TYPE_SEARCH3', ' checked');
        } else {
            $xtpl->assign('TYPE_SEARCH1', ' checked');
        }
        if ($type_info == 1) {
            $xtpl->assign('ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module, true));
        } else {
            $form_action = ($type_info == 2) ? 'courses' : 'student-list';
            $xtpl->assign('ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module . '&amp;' . NV_OP_VARIABLE . '=' . $form_action, true));
        }
        $xtpl->assign('FORM_ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module, true));
        $xtpl->assign('FORM_ACTION1', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=courses', true));
        $xtpl->assign('FORM_ACTION2', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=student-list', true));
        if (!empty($array_op[1])) {
            if ($array_op[1] == 'tinh-thanh' || $array_op[1] == 'tenderlistbylocation') {
                if ($array_op[2] == 'Chua-phan-loai' || $array_op[2] == 'unknown') {
                    $_idprovince[] = -1;
                } else {
                    $_temp = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_location_province WHERE alias = ' . $db->quote($array_op[2]))->fetchColumn();
                    if (!empty($_temp)) {
                        $_idprovince[] = $_temp;
                    }
                }
            }
        }
        $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province";
        $province_list = $nv_Cache->db($sql, 'id', 'location');
        $province_list[0] = array(
            'id' => 0,
            'title' => $nv_Lang->getModule('undefined'),
            'alias' => strtolower(change_alias($nv_Lang->getModule('undefined')))
        );
        foreach ($province_list as $_province) {
            if ($_province['id'] == -1) {
                $_province['title'] = $nv_Lang->getModule('no_title');
                $_province['alias'] = 'Chua-phan-loai';
            }
            $xtpl->assign('PROVINCE', [
                'alias' => $_province['alias'],
                'key' => $_province['id'],
                'title' => $_province['title'],
                'selected' => in_array($_province['id'], $_idprovince) ? ' selected="selected"' : ''
            ]);
            $xtpl->parse('main.province');
        }
        $arr_info = array(
            1 => $nv_Lang->getModule('search_tranning'),
            2 => $nv_Lang->getModule('search_courses'),
            3 => $nv_Lang->getModule('search_student'),
        );
        foreach ($arr_info as $key => $t) {
            $xtpl->assign('TYPE', array(
                'key' => $key,
                'title' => $t,
                'selected' => $key == $type_info ? ' selected="selected"' : ''
            ));
            $xtpl->parse('main.type_info');
        }
        $default_advance = 0;
        $is_advance = $nv_Request->get_int('is_advance', 'get', $default_advance);
        $advance_show = !empty($is_advance);
        $advance_btn_show = ($advance_show or ($type_info == 2 or $type_info == 3)) ? true : false;
        $advance_bl_show = ($advance_show || $advance_btn_show) ? true : false;
        // Luon đóng khi load trang
        // $advance_show = false;

        if (!$advance_bl_show) {
            $xtpl->parse('main.advance_bl_hide');
        }

        if (!$advance_btn_show) {
            $xtpl->parse('main.advance_btn_hide');
        }
        if ($advance_show) {
            $xtpl->assign('LANG_ADVANCE', $nv_Lang->getModule('search_simple'));
            $xtpl->assign('ADVANCE', 1);
            $xtpl->parse('main.advance_icon_1');
        } else {
            $xtpl->assign('LANG_ADVANCE', $nv_Lang->getModule('search_advance'));
            $xtpl->assign('ADVANCE', 0);
            $xtpl->parse('main.advance_icon_0');
            $xtpl->parse('main.advance_hide');
        }

        if ($type_info == 2) {
            $xtpl->parse('main.no_student');
        }
        if ($type_info == 3) {
            $xtpl->parse('main.no_couses');
        }
        $xtpl->parse('main');
        return $xtpl->text('main');
    }
}

if (defined('NV_SYSTEM')) {
    global $nv_Cache, $site_mods, $module_name;
    $module = $block_config['module'];

    if (isset($site_mods[$module])) {
        $content = nv_block_search_courses($block_config);
    }
}
