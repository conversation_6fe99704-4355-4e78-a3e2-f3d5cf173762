<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript">
    var ranges = {};
    ranges['{LANG.this_month}'] = [moment().startOf('month'), moment().endOf('month')];
    ranges['{LANG.last_3_months}'] = [moment().subtract(3, 'months').startOf('month'), moment().endOf('month')];
    ranges['{LANG.last_6_months}'] = [moment().subtract(6, 'months').startOf('month'), moment().endOf('month')];
    ranges['{LANG.this_year}'] = [moment().startOf('year'), moment().endOf('year')];
    ranges['{LANG.last_all_days}'] = [moment('{MIN_DATE}', "DD/MM/YYYY"), moment()];

    $('#formInvestor .approval_range').daterangepicker(
        {
            "showDropdowns" : true,
            "startDate" : $('#formInvestor [name=approval_date_sfrom]').val(),
            "endDate" : $('#formInvestor [name=approval_date_sto]').val(),
            "minDate" : nv_lang_data == 'vi' ? moment("31/12/2010", "DD/MM/YYYY") : moment("12/31/2010", "MM/DD/YYYY"),
            "maxDate" : moment(),
            "opens" : "left",
            locale : {
                cancelLabel : $('#formInvestor .approval_range').attr("data-cancel-lang"),
                applyLabel : $('#formInvestor .approval_range').attr("data-apply-lang"),
                help: ''
            }
    }, function(start, end, label) {
        $("#formInvestor [name=approval_date_sfrom]").val(start.format(nv_lang_data == 'vi' ? 'DD/MM/YYYY' : 'MM/DD/YYYY'));
        $("#formInvestor [name=approval_date_sto]").val(end.format(nv_lang_data == 'vi' ? 'DD/MM/YYYY' : 'MM/DD/YYYY'));
    });

    $("#industry").select2({ language : "{LANG_T}"});
</script>

<div class="border-bidding">
    <span>{LANG.title_search_investor_approved}</span>
</div>
<div class="search_investor">
    <form method="get" class="form-horizontal form-search form-bussiness-search" id="formInvestor">
        <div class="form-group margin-bottom-sm">
            <label class="control-label col-md-5">{LANG.search_keys}:</label>
            <div class="col-md-19">
                <input class="form-control" placeholder="{LANG.search_title}" type="text" value="{ARR_SEARCH.q}" name="q">
                <em class="help-block margin-bottom-sm">{LANG.title_search_investor}</em>
            </div>
        </div>
        <div class="row">
            <div class="col-md-19 col-md-offset-5">
                <input type="hidden" name="is_advance" value="{ARR_SEARCH.is_advance}">
                <button id="fsearch" type="submit" value="1" class="btn btn-primary bussubmit_search_investor" name="bussubmit_search_investor">{LANG.search} </button>
                <a class="btn-search-advance btn btn-default" href="javascript:void(0);" data-search-simple="{LANG.search_simple}" data-search-advance="{LANG.search_advance}" data-icon-search-simple="icon-chevron-down" data-icon-search-advance="icon-chevron-up">
                    <em class="margin-right-sm icon-chevron-down"></em>
                    <em class="margin-right-sm icon-chevron-up"></em>
                    <strong class="txt">{LANG.search_advance}</strong>
                </a>
            </div>
        </div>

        <div class="advance-search-content" style="display: none">
            <!-- BEGIN: search_industry -->
            <div class="form-group">
                <label class="control-label col-md-5">{LANG.career}:</label>
                <div class="col-md-19" id="block-search-industry">
                    <select name="industry[]" multiple id="industry" class="form-control" width="100%">
                        <option value="-1">--- {LANG.title_option_all_industry} ---</option>
                        <!-- BEGIN: loop -->
                            <option value="{ROW_INDUSTRY.id}" {ROW_INDUSTRY.selected}>{ROW_INDUSTRY.name}</option>
                        <!-- END: loop -->
                    </select>
                </div>
            </div>
            <!-- END: search_industry -->

            <!-- BEGIN: search_lvtg -->
            <div class="form-group">
                <label class="control-label col-md-5">{LANG.title_lvtg}:</label>
                <div class="col-md-19">
                    <!-- BEGIN: loop -->
                    <div id="linh_vuc_{LVTG.k}">
                        <label class="custom-checkbox">
                            <input type="checkbox" id="ccb_field_block_{LVTG.k}" name="field_lvtg[]" class="ccb_field" value="{LVTG.k}" {LVTG.checked} data-default="false">
                            <span class="txt">{LVTG.v}</span>
                        </label>
                    </div>
                    <!-- END: loop -->
                </div>
            </div>
            <!-- END: search_lvtg -->
            <!-- BEGIN: province -->
            <div class="form-group">
                <label class="control-label col-md-5">{LANG.diadiem}:</label>
                <div class="col-md-19">
                    <select class="form-control fselect2 selected_phanmuc"
                    id="idprovince" name="idprovince">
                    <option value="-1">{LANG.all_htlc}</option>
                    <!-- BEGIN: loop -->
                        <option value="{PROVINCE.id}"{PROVINCE.selected}>{PROVINCE.title}</option>
                    <!-- END: loop -->
                    </select>
                </div>
            </div>
            <!-- END: province -->

            <!-- BEGIN: business_type -->
            <div class="form-group">
                <label class="control-label col-md-5">{LANG.solicitor_type}:</label>
                <div class="col-md-19">
                    <select class="form-control fselect2 selected_phanmuc"
                    id="businesstype" name="businesstype">
                    <option value="-1">{LANG.all_htlc}</option>
                    <!-- BEGIN: loop -->
                        <option value="{BUSINESS.code}"{BUSINESS.selected}>{BUSINESS.name}</option>
                    <!-- END: loop -->
                    </select>
                </div>
            </div>
            <!-- END: business_type -->

            <div class="form-group">
                <label class="control-label col-md-5">{LANG.filter_ngay_phe_duyet}:</label>
                <div class="col-md-19" id="block-search-location">
                    <input type="hidden" name="approval_date_sfrom" value="{ARR_SEARCH.approval_date_sfrom}" data-default="">
                    <input type="hidden" name="approval_date_sto" value="{ARR_SEARCH.approval_date_sto}" data-default="">
                    <input class="form-control approval_range" type="text" value="dd/mm/yyyy - dd/mm/yyyy" data-cancel-lang="{LANG.title_cancel_filter}" data-apply-lang="{LANG.title_apply}">
                </div>
            </div>
        </div>
    </form>
</div>
<hr>

<p class="text-right mt-2 mb-2"><i>{NUM}</i></p>

<div class="panel panel-default">
    <div class="panel-heading">
        <h2 class="panel-title">{LANG.title_investor_approved}</h2>
    </div>
    <!-- BEGIN: list -->
    <div class="list-group">
        <!-- BEGIN: loop -->
        <div class="list-group-item">
            <div class="clearfix">
                <div class="listing-summary">
                    <div class="row">
                        <h3>
                            <div class="col-xs-24 col-md-20">
                                <span class="org-code">{ROW.org_code}</span> <a href="{ROW.link_detail}">{ROW.org_fullname}</a>
                            </div>
                        </h3>
                    </div>
                    <div class="row">
                        <div class="col-xs-24">
                            <div class="address mt-2">
                                <p class="add_content">
                                    <b># {LANG.tax}:</b> {ROW.tax}
                                </p>
                                <p class="add_content">
                                    <b><i class="fa fa-map-marker" aria-hidden="true"></i> {LANG.contact_address}:</b> {ROW.full_address}
                                </p>
                                <p class="add_content">
                                    <span >
                                        <b><i class="fa fa-calendar-o" aria-hidden="true"></i> {LANG.decision_date}:</b> {ROW.aproval_time}
                                    </span>
                                </p>
                                <!-- BEGIN: lvtg_lcnt_fm -->
                                <p class="add_content">
                                    <span> <i class="fa fa-circle-o" aria-hidden="true"></i><b></i> {LANG.title_lvtg}:</b> {ROW.lvtg_lcnt_fm}
                                    </span>
                                </p>
                                <!-- END: lvtg_lcnt_fm -->
                            </div>
                        </div>
                    </div>
                </div>

                <span class="btn {ROW.class_status} btn-cmp-ar btn-compare-actt btn-xs">{ROW.status}</span>
            </div>
        </div>
        <!-- END: loop -->
    </div>

    <!-- BEGIN: generate_page -->
    <div class="text-center">
        {NV_GENERATE_PAGE}
    </div>
    <!-- END: generate_page -->
    <!-- END: list -->

    <!-- BEGIN: no_list -->
    <div class="alert alert-warning mt-2">
        {LANG.title_notification_no_data_investor}
    </div>
    <!-- END: no_list -->
</div>
<script type="text/javascript">
    var formObject_investor = $("[id=formInvestor]");
    $(".icon-chevron-up", formObject_investor).hide();

    $(document).ready(function() {
        if (parseInt($("input[name='is_advance']", formObject_investor).val())) {
            showAdvance();
        }

        $(".btn-search-advance", formObject_investor).click(function(event) {
            event.preventDefault(); // Ngăn chặn hành vi mặc định của nút bấm
            showAdvance();
        });

        function showAdvance() {
            // Kiểm tra trạng thái của div và thực hiện slideToggle
            var advanceContent = $(".advance-search-content");
            if (advanceContent.is(":visible")) {
                advanceContent.slideUp(); // Ẩn nếu đang hiển thị
                $("input[name='is_advance']", formObject_investor).val(0);
                $(".icon-chevron-down", formObject_investor).show();
                $(".icon-chevron-up", formObject_investor).hide();
            } else {
                advanceContent.slideDown(); // Hiển thị nếu đang ẩn
                $("input[name='is_advance']", formObject_investor).val(1);
                $(".icon-chevron-down", formObject_investor).hide();
                $(".icon-chevron-up", formObject_investor).show();
            }
        }
    });
</script>
<!-- END: main -->
