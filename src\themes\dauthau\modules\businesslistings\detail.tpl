<!-- BEGIN: main -->
<!-- START POINT_NOT_ENOUGTH -->
<div id="popup_not_dismiss" class="modal fade auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <div>
                    {LANG.info_point_not_enough}
                    <p class="text-center">
                        <a class="btn btn-danger" href="{LINK_MUA_DIEM}" target="_blank">{LANG.buy_points}</a>
                    </p>
                    <script>
                        $(function() {
                            $("#popup_not_dismiss").modal({
                                backdrop: "static",
                                keyboard: false
                            });
                        });
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END POINT_NOT_ENOUGTH -->
<div class="msgshow" id="msgshow">
    <script>
        $(document).ready(function() {
            $('#msgshow').html('{LANG.message_point_view_suss}').fadeIn('slow').delay(5000).fadeOut('slow');
        });
    </script>
</div>
<<popup_login>>
    <div id="confirm" class="modal fade auto-height" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body"></div>
                <div class="modal-footer">
                    <span class="button"></span>
                    <button type="button" class="ok btn btn-primary">{LANG.ok}</button>
                    <button type="button" data-dismiss="modal" class="btn">{LANG.close}</button>
                </div>
            </div>
        </div>
    </div>
<!-- BEGIN: detail -->
<!-- BEGIN: recaptcha -->
<div id="captchaModal" class="modal fade auto-width auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <p class="modal-title">{LANG.recapcha_title}</p>
            </div>
            <div class="modal-body">
                <div>{LANG.recapcha_body}</div>
                <div data-toggle="recaptcha" data-callback="verify_captcha" id="{RECAPTCHA_ELEMENT}"></div>
            </div>
        </div>
    </div>
</div>
<script>
    function verify_captcha(e) {
        click_update_business();
    }
</script>
<!-- END: recaptcha -->
<div class="detail-wrapper">
    <h1 class="border-bidding">
        <span>{ROW.code} - {ROW.companyname}</span>
    </h1>
    <div class="row share-content">
        <div class="col-xs-24 btn-share-group">
            <span>{LANG.share} </span>
            <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_fb_share();" title="{LANG.fb_share}">
                <span class="icon-facebook"></span>
            </a>
            <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_tw_share('', '{ROW.companyname}');" title="{LANG.tweet}">
                <span class="icon-twitter"></span>
            </a>
            <a href="javascript:void(0)" class="btn-share btn-copy-link" title="{LANG.copy_link}">
                <em class="fa fa-link"></em>
                <span class="tip" style="display: none;">{LANG.link_copy_successfully}</span>
            </a>
        </div>
    </div>
    <div class="detail-list-btn">
        <a href="#" class="btn btn-primary<<add-compare-hide>> btn-compare-actt" data-id="{ROW.id}" onclick="addCttCompare({ROW.id}, '##CHECK_ADD_CP##');"><em class="fa fa-plus">&nbsp;</em>{LANG.add_compare}</a>
        <a href="#" class="btn btn-danger<<rem-compare-hide>> btn-compare-rctt" data-id="{ROW.id}" onclick="removeCttCompare({ROW.id}, '##CHECK_REM_CP##');"><em class="fa fa-minus">&nbsp;</em>{LANG.remove_compare}</a>
        <div id="main__menu_bidding" class="btn-menu">
            <span class="btn btn-primary btn_list_menu"><em class="fa fa-list-ol" aria-hidden="true"></em>&nbsp;{LANG.index}</span>
            <!-- BEGIN: re_download -->
            <button class="btn btn-success btn_re_download" {disable}><em class="fa fa-download" aria-hidden="true"></em>&nbsp;{LANG.re_download}</button>
            <!-- END: re_download -->
            <div id="bidding__menu" class="bidding_menu_wrapper">
                <div id="fixmenu" class="menu-list">
                    <div class="bidding__menu-title">
                        <span class="table-heading f-w-500"><i class="fa fa-bars hidden__bidding__menu-tab" aria-hidden="true"></i>{LANG.index}</span> <span class="hidden__nav"> <i class="fa fa-times close__menu" aria-hidden="true"></i>
                        </span>
                    </div>
                    <div class="bidding__menu-main">
                        <nav class="bidding__menu-tab-navigation" id="navigation">
                            <ul id="tableMenuContent">
                                <li><a class="bidding__link active" href="#info_contractor">{LANG.info_contractor}</a></li>
                                <li><a class="bidding__link" href="#business_industry">{LANG.title_business_industry}</a></li>
                                <li><a class="bidding__link" href="#result_contractor">{LANG.result_contractor}</a></li>
                                <!-- BEGIN: list_vnr -->
                                <li><a class="bidding__link" href="#vnr_500_info">{LANG.vnr_info}</a></li>
                                <!-- END: list_vnr -->
                                <li><a class="bidding__link" href="#ability_core">{LANG.ability_core}</a></li>
                                <!-- BEGIN: list_charts -->
                                <li><a class="bidding__link" href="#list_charts">{LANG.list_charts}</a></li>
                                <!-- END: list_charts -->
                                <li><a class="bidding__link" href="#utilities">{LANG.utilities}</a></li>
                                <li><a class="bidding__link" href="#contractor_activities">{LANG.contractor_activities}</a></li>
                                <!-- BEGIN: list_table_data -->
                                <li><a class="bidding__link" href="#detail_data">{LANG.detail_data}</a></li>
                                <!-- END: list_table_data -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        <div class="text-right crawl_time mg-bt-5">
            <div class="small">
                {LANG.crawl_time}: <strong>{ROW.time_crawler}</strong>
            </div>
            <!-- BEGIN: update -->
            <div class="margin-top-sm">
                <span class="small">{ROW.update_info}</span> <a style="margin-left: auto" id="reupdate" class="btn btn-default btn-xs active" onclick="show_capcha()" href="javascript:void(0)" data-id="{ROW.id}" data-check="{CHECKSESS_UPDATE}">{LANG.reupdate}</a>
                <img id="update_wait" style="display: none" src="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/images/load_bar.gif" />
                <!-- BEGIN: crawl_request_history_button -->
                <a style="margin-left: auto" id="crawl_request_history" class="btn btn-default btn-xs active" href="javascript:void(0)">{LANG.crawl_request_history}</a>
                <!-- END: crawl_request_history_button -->
            </div>
            <!-- END: update -->
        </div>
    </div>

    <!-- BEGIN: re_download_warning -->
    <div class="alert alert-info">{MSS}</div>
    <!-- END: re_download_warning -->
    <div class="text-right margin-bottom-sm view_history">
        {FILE "button_show_log.tpl"}
    </div>
    {FILE "crawl_request_history.tpl"}

    <h2>{LANG.info_contractor}</h2>
    {FILE "slide-include/table_detail_info.tpl"}
    <!-- BEGIN: chart -->
    <div class="detail_scoll__menu" id="result_contractor">
        <h2>{LANG.result_contractor}</h2>
        <!-- BEGIN: static -->
        <div class="static data-analysis" style="position: relative;">
            <lock_detail>
            <div class="box_lockvip__detail">
                <div class="main_lockvip">
                    <div class="tw_lockvip">
                        <div class="tw_lockvip_head">
                            <img class="tw_lockvip_head__image">
                        </div>

                        <div class="tw_lockvip__button">
                            <a href="##link_lock_detail##" class="btn__lock_login">##title_lock_detail##</a>
                        </div>

                        <div class="tw_lockvip__content">
                            <p class="tw_lockvip__content__des">{LANG.title_view_info}</p>
                        </div>
                    </div>
                </div>
            </div>
            </lock_detail>
            {STATIC}
            {STATIC1}
            ##lock##
        </div>
        <!-- END: static -->
        {FILE "biz_type_info.tpl"}
    </div>
    <!-- BEGIN: error_api -->
    <p class="alert alert-warning">{LANG.error_api_warning}</p>
    <!-- END: error_api -->
    <div class="row detail_scoll__menu" id="ability_core">
        <div class="col-md-24 col-sm-24 col-xs-24">
            <div class="business-chart">
                <div class="clearfix"></div>
                <div class="business-chart-title"><h2>{LANG.ability_point}</h2></div>
                <div class="chart">
                    <div class="row ability-point-container text-center m-bottom">
                        <button class="btn btn-warning btn--social ability-point" id="ability-point">
                                <span class="ability-btn-title">{LANG.ability_point_short}</span>
                                <strong>&#127775;##ability_point##</strong>
                        </button>
                        <button class="btn btn-success btn--social ability-point" id="ability-rank">
                            <span class="ability-btn-title">{LANG.ability_rank}</span>
                            <strong>&#127942;###ability_rank##</strong>
                        </button>
                    </div>
                    <div class="ability-point-explain">
                        <div class="m-bottom text-justify">
                            {LANG.ability_point_description}&nbsp;{LANG.see_more_ability}
                        </div>
                        <div style="position: relative">
                            <table class="table text-center table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">{LANG.evaluation_criteria}</th>
                                        <th class="text-center w-50">{LANG.point_evaluation}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td data-column="{LANG.evaluation_criteria}">{LANG.experience_ability}</td>
                                        <td data-column="{LANG.point_evaluation}" class="text-center" id="experience_ability">##experience_ability##</td>
                                    </tr>
                                    <tr>
                                        <td data-column="{LANG.evaluation_criteria}">{LANG.finace_ability}</td>
                                        <td data-column="{LANG.point_evaluation}" class="text-center" id="finace_ability">##finace_ability##</td>
                                    </tr>
                                    <tr>
                                        <td data-column="{LANG.evaluation_criteria}">{LANG.compete_ability}</td>
                                        <td data-column="{LANG.point_evaluation}" class="text-center" id="compete_ability">##compete_ability##</td>
                                    </tr>
                                    <tr>
                                        <td data-column="{LANG.evaluation_criteria}">{LANG.enterprise_scale}</td>
                                        <td data-column="{LANG.point_evaluation}" class="text-center" id="enterprise_scale">##enterprise_scale##</td>
                                    </tr>
                                    <tr>
                                        <td data-column="{LANG.evaluation_criteria}">{LANG.violation_history}</td>
                                        <td data-column="{LANG.point_evaluation}" class="text-center" id="violation_history">##violation_history##</td>
                                    </tr>
                                </tbody>
                            </table>
                            <lock_detail_score>
                                <div class="box_lockvip__detail overlay_ability_point">
                                    <div class="main_lockvip main_bg_ability_point">
                                        <div class="tw_lockvip bg_ability_point">
                                            <div class="tw_lockvip_head">
                                                <img class="tw_lockvip_head__image">
                                            </div>

                                            <div class="tw_lockvip__button">
                                                <a href="##link_lock_ability##" class="btn__lock_login">##title_lock_ability##</a>
                                            </div>
                                            <!-- BEGIN: btn_use_point_ability -->
                                            <div>{LANG.or}</div>
                                            <div class="tw_lockvip__button">
                                                <a href="" class="btn__lock_login btn_point_view_ability">##title_lock_view_ability##</a>
                                            </div>
                                            <!-- END: btn_use_point_ability -->
                                            <div class="tw_lockvip__content">
                                                <p class="tw_lockvip__content__des">{LANG.title_view_ability}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </lock_detail_score>
                        </div>
                        <div class="lock-ability">##lock_ability##</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- BEGIN: btn_use_point_ability_modal -->
    <div class="modal fade modal-center" tabindex="-1" role="dialog" id="view-ability-confirm" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <div class="modal-title h4">{LANG.notice}</div>
                </div>
                <div class="modal-body">
                    <p id="confirm_view_ability"></p>

                </div>
                <div class="modal-footer">
                    <button type="button" id="view-ability-submit" class="btn btn-primary btn-fw">{LANG.ok}</button>
                    <button type="button" class="btn btn-default btn-fw" id="view-ability-cancel" data-dismiss="modal">{LANG.cancel}</button>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        $('.btn_point_view_ability').on('click', () => {
            $.ajax({
                url: window.location.href,
                method: 'POST',
                dataType: 'json',
                data: {
                    check_point_view_ability: 1,
                    checkss: '##CHECK_POINT_VIEW_ABILITY##'
                },
                success: (data) => {
                    if (data.res == 'success') {
                        $('#confirm_view_ability').text(data.mess);
                        $('#view-ability-submit').removeClass('hidden');
                    } else {
                        alertDiv = '<div class="alert alert-warning">' + data.mess + '</div>';
                        $('#confirm_view_ability').html(alertDiv);
                        $('#view-ability-submit').addClass('hidden');
                    }
                    $('#view-ability-confirm').modal('show');
                }
            });
        });
        $('#view-ability-submit').on('click', () => {
            $.ajax({
                url: window.location.href,
                method: 'POST',
                dataType: 'json',
                data: {
                    view_ability: 1,
                    checkss: '##CHECK_VIEW_ABILITY##'
                },
                success: (data) => {
                    if (data.res == 'success') {
                        $('#experience_ability').text(data.point[1]);
                        $('#finace_ability').text(data.point[2]);
                        $('#compete_ability').text(data.point[3]);
                        $('#enterprise_scale').text(data.point[4]);
                        $('#violation_history').text(data.point[5]);
                        $('.overlay_ability_point').addClass('hidden');
                        $('#view-ability-submit').addClass('hidden');
                        $('.lock-ability').html('');
                        $('#view-ability-confirm').modal('hide');
                    } else {
                        alertDiv = '<div class="alert alert-warning">' + data.mess + '</div>';
                        $('#confirm_view_ability').html(alertDiv);
                        $('#view-ability-submit').addClass('hidden');
                        $('#view-ability-confirm').modal('show');
                    }
                }
            });
        });
    </script>
    <!-- END: btn_use_point_ability_modal -->
    <!-- Biểu đồ -->
    <div id="list_charts" class="detail_scoll__menu">
        <h2>{LANG.list_charts}</h2>
        <!-- BEGIN: chart_history -->
        <div class="row">
            <div class="col-md-24 text-center col-sm-24 col-xs-24">
                <div class="business-chart">
                    <div class="clearfix"></div>
                    <div class="business-chart-title no-margin-bottom"><h3>{SUM_CHART_TITLE}</h3></div>
                    <div class="main_chart_history">
                        <div id="history_char" class="chart" style="height: 365px;">
                        </div>
                        <div class="text__static text--static-{NV_LANG_DATA}">
                            <p class="text_tong_win"></p>
                            <p class="text_tong_order"></p>
                            <p class="text_tong_joint_venture"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="hidden">
            <div id="string_chart" data-sc="{STRING_CHART}" data-scy="{STRING_CHART_BY_YEAR}"></div>
        </div>
        <script src="{LINK_JS}js/apexcharts.js"></script>

        <script type="text/javascript">
                // Lấy dữ liệu từ PHP trả về
                var string_chart = $('#string_chart').data('sc');
                var labelChart = string_chart['title_ressult'];
                var link = string_chart['link'];
                $(".text_tong_win").html(string_chart['char_ratio_win']['tong']);
                $(".text_tong_order").html(string_chart['char_ratio_order']['tong']);
                $(".text_tong_joint_venture").html(string_chart['char_ratio_joint_venture']['tong']);
                var options = {
                    series: [
                        {
                        name: labelChart['label_hit_rate'],
                        data: [string_chart['char_ratio_win']['hit_rate'], string_chart['char_ratio_order']['hit_rate'], string_chart['char_ratio_joint_venture']['hit_rate']]
                        },
                        {
                        name: labelChart['label_no_result'],
                        data: [string_chart['char_ratio_win']['no_result'], string_chart['char_ratio_order']['no_result'], string_chart['char_ratio_joint_venture']['no_result']]
                        },
                        {
                        name: labelChart['label_slip_rate'],
                        data: [string_chart['char_ratio_win']['slip_rate'], string_chart['char_ratio_order']['slip_rate'], string_chart['char_ratio_joint_venture']['slip_rate']]
                        },
                        {
                        name: labelChart['label_cancel_result'],
                        data: [string_chart['char_ratio_win']['result_cancel'], string_chart['char_ratio_order']['result_cancel'], string_chart['char_ratio_joint_venture']['result_cancel']]
                        }

                    ],
                    colors : ['#4576b5', '#ff9f43', '#ff6b6b', 'gray'],
                    chart: {
                        type: 'bar',
                        height: '90%',
                        width: '96%',
                        stacked: true,
                        stackType: '100%',
                        offsetY: '10'
                    },
                    plotOptions: {
                        bar: {
                            horizontal: true,
                        },
                    },
                    dataLabels: {
                        enabled: false,
                    },
                    stroke: {
                        width: 1,
                        colors: ['#fff']
                    },
                    // title: {
                    //     text: labelChart['lichsudauthau'],
                    //     align: 'center',
                    //     margin: 10,
                    //     offsetX: 0,
                    //     offsetY: 0,
                    //     floating: false,
                    //     style: {
                    //       fontSize:  '14px',
                    //       fontWeight:  'bold',
                    //       fontFamily:  undefined,
                    //       color:  '#263238'
                    //     },
                    // },
                    xaxis: {
                        categories: [labelChart['tongquan'], labelChart['dauthaudoclap'], labelChart['nhathauliendanh']],
                        title: {
                            text: labelChart['tyle'],
                            offsetX: -5,
                            style: {
                                fontSize: '13px',
                                colors: ['#333'],
                                fontWeight: 700
                            },
                        },
                        // labels: {
                        //     formatter: function(val) {
                        //         return val
                        //     }
                        // }
                    },
                    yaxis: {
                        labels: {
                            offsetX: 0,
                            maxWidth: '100%',
                            style: {
                                fontSize: '13px',
                                fontWeight: 700,
                            },
                        }
                    },
                    // yaxis: {
                    //     show: true,
                    //     labels: {
                    //         show: true,
                    //         align: 'left',
                    //         maxWidth: '100%',
                    //         style: {
                    //             colors: ['#076cbd'],
                    //             fontSize: '13px',
                    //             fontFamily: 'Helvetica, Arial, sans-serif',
                    //             fontWeight: 600,
                    //         },
                    //         offsetX: 10,
                    //     },
                    //     title: {
                    //         show: true,
                    //         text: labelChart['thongkedauthau'],
                    //         offsetX: 0,
                    //         style: {
                    //             fontSize: '15px',
                    //             fontFamily: 'Helvetica, Arial, sans-serif',
                    //             colors: ['#333'],
                    //             fontWeight: 300,
                    //             cssClass: 'font__italic',
                    //         },
                    //     }
                    // },
                    tooltip: {
                        enabled: true,
                        style: {
                            fontSize: '13px',
                            fontFamily: 'Helvetica, Arial, sans-serif',
                            fontWeight: 300,
                        },
                        marker: {
                            show: true,
                        },
                        y: {
                            formatter: function(value, { series, seriesIndex, dataPointIndex, w }) {
                                valueFomat = series[0][dataPointIndex] + series[1][dataPointIndex] + series[2][dataPointIndex];
                                valueFomat = value / valueFomat;
                                tongPoint = valueFomat * 100;
                                if (tongPoint.toString().split('').length > 3) {
                                    tongPoint = tongPoint.toFixed(2);
                                }
                                string = value + labelChart['goithau'] + ' (' + tongPoint  + '%)';
                                return trim(string);
                            },
                        },
                    },
                    fill: {
                        opacity: 1
                    },
                    legend: {
                        position: 'top',
                        horizontalAlign: "center",
                        offsetX: 17,
                    },
                    responsive: [
                        {
                            breakpoint: 1000,
                            options: {
                                legend: {
                                    position: 'top',
                                    horizontalAlign: "center",
                                    offsetX: 0,
                                },
                            yaxis: {
                                show: true,
                                labels: {
                                    show: true,
                                    align: 'left',
                                    maxWidth: '100%',
                                    offsetX: -12,
                                    style: {
                                        fontSize: '13px',
                                        fontFamily: 'Helvetica, Arial, sans-serif',
                                        fontWeight: 600,
                                    },
                                },

                                    // title: {
                                    //     text: labelChart['thongkedauthau'],
                                    //     offsetX: 20,
                                    //     style: {
                                    //         fontSize: '13px',
                                    //         fontFamily: 'Helvetica, Arial, sans-serif',
                                    //         colors: ['#333'],
                                    //         fontWeight: 300,
                                    //         cssClass: 'font__italic',
                                    //     },
                                    // }
                                }
                            }
                        }
                    ]
                };

                var chart = new ApexCharts(document.querySelector("#history_char"), options);
                chart.render();

                // Chen link
                title = $('#history_char').find('.apexcharts-yaxis-label');
                for (i = 0; i < title.length; i++) {
                    // Link tỔng quan
                    if (i == 0) {
                        if (link['link_tongquat'] != "") {
                        title.eq(i).find('title').attr('data-link', link['link_tongquat'])
                        }
                    }

                    // Link đấu thầu với các nhà thầu khác

                    if (i == 1) {
                        if (link['link_nhathaukhac'] != "") {
                            title.eq(i).find('title').attr('data-link', link['link_nhathaukhac'])
                        }
                    }

                    // Link đấu thầu khi liên danh sẽ chèn ở đây

                    if (i == 2) {
                        if (link['link_liendanh'] != "") {
                            title.eq(i).find('title').attr('data-link', link['link_liendanh'])
                        }
                    }
                }


                $('#history_char').find('.apexcharts-yaxis-label').mousedown(function(event) {
                    switch (event.which) {
                        case 1:
                            if ($(this).find('title').attr('data-link') && $(this).find('title').attr('data-link') != undefined) {
                                location.href = $(this).find('title').attr('data-link')
                            }
                            break;
                        case 2:
                            if (confirm("Bạn có muốn mở một tab mới không?")) {
                                if ($(this).find('title').attr('data-link') && $(this).find('title').attr('data-link') != undefined) {
                                    var win = window.open($(this).find('title').attr('data-link'), '_blank');
                                    if (win) {
                                        //Browser has allowed it to be opened
                                        win.focus();
                                    } else {
                                        //Browser has blocked it
                                        alert('Please allow popups for this website');
                                    }
                                }
                            }
                            break;
                        case 3:
                            if (confirm("Bạn có muốn mở một tab mới không?")) {
                                if ($(this).find('title').attr('data-link') && $(this).find('title').attr('data-link') != undefined) {
                                    var win = window.open($(this).find('title').attr('data-link'), '_blank');
                                    if (win) {
                                        //Browser has allowed it to be opened
                                        win.focus();
                                    } else {
                                        //Browser has blocked it
                                        alert('Please allow popups for this website');
                                    }
                                }
                            }
                            break;
                        default:
                            if ($(this).find('title').attr('data-link') && $(this).find('title').attr('data-link') != undefined) {
                                location.href = $(this).find('title').attr('data-link')
                            }
                            break;
                    }
                });
        </script>
        <!-- END: chart_history -->
        <!-- BEGIN: chart_history_year -->
        <div class="row">
            <div class="col-md-24 text-center col-sm-24 col-xs-24">
                <div class="business-chart">
                    <div class="clearfix"></div>
                    <div class="business-chart-title no-margin-bottom"><h3>{YEAR_CHART_TITLE}</h3></div>
                    <div class="main_chart_history">
                        <div id="history_char_by_year" class="chart" style="height: 415px;">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script type="text/javascript">
            // Lấy dữ liệu từ PHP trả về
            var string_chart_by_year = $('#string_chart').data('scy');
            var labelChart = string_chart_by_year['title_ressult'];
            var options = {
                series: [{
                    name: labelChart['label_hit_rate'],
                    group: 'actual',
                    data: string_chart_by_year['data']['hit_rate']
                },{
                    name: labelChart['label_no_result_rate'],
                    group: 'actual',
                    data: string_chart_by_year['data']['no_result_rate']
                },{
                    name: labelChart['label_slip_rate'],
                    group: 'actual',
                    data: string_chart_by_year['data']['slip_rate']
                },{
                    name: labelChart['label_cancel_result'],
                    group: 'actual',
                    data: string_chart_by_year['data']['cancel_result']
                },{
                    name: labelChart['label_tongthau'],
                    data: string_chart_by_year['data']['tong_thau']
                }],
                colors : ['#4576b5', '#ff9f43', '#ff6b6b', 'gray', '#00a56e'],
                dataLabels: {
                    enabled: true,
                },
                chart: {
                    type: 'bar',
                    height: '87%',
                    stacked: true,
                    offsetY: '10'
                },
                plotOptions: {
                    bar: {
                        columnWidth: string_chart_by_year['data']['tong_thau'].length >= 7 ? '70%' : string_chart_by_year['data']['tong_thau'].length * 10 + '%'
                    }
                },
                // title: {
                //     text: labelChart['lichsudauthau_byyear'],
                //     align: 'center',
                //     margin: 10,
                //     offsetX: 0,
                //     offsetY: 0,
                //     floating: false,
                //     style: {
                //         fontSize:  '14px',
                //         fontWeight:  'bold',
                //         fontFamily:  undefined,
                //         color:  '#263238'
                //     },
                // },
                tooltip: {
                    enabled: true,
                    style: {
                        fontSize: '13px',
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        fontWeight: 300,
                    },
                    marker: {
                        show: true,
                    },
                    y: {
                        formatter: function(value, { series, seriesIndex, dataPointIndex, w }) {
                            valueFomat = value / series[4][dataPointIndex];
                            tongPoint = valueFomat * 100;
                            if (tongPoint.toString().split('').length > 3) {
                                tongPoint = tongPoint.toFixed(2);
                            }
                            string = value + labelChart['goithau'] + ' (' + tongPoint  + '%)';
                            return trim(string);
                        },
                    },
                    tooltip: {
                        share: true
                    }
                },
                responsive: [
                    {
                        breakpoint: 1000,
                        options: {
                            legend: {
                                position: 'top',
                                horizontalAlign: "center"
                            },
                            dataLabels: {
                                enabled: true,
                            },
                            yaxis: {
                                show: true,
                                labels: {
                                    show: true,
                                    align: 'left',
                                    style: {
                                        fontSize: '13px',
                                        fontFamily: 'Helvetica, Arial, sans-serif',
                                        fontWeight: 600,
                                    },
                                },
                                title: {
                                    text: '{LANG.num_bid_row}',
                                    style: {
                                        fontSize: '14px',
                                        fontFamily: 'Helvetica, Arial, sans-serif',
                                        colors: ['#333'],
                                        fontWeight: 600
                                    }
                                }
                            }
                        }
                    },
                    {
                        breakpoint: 700,
                        options: {
                            dataLabels: {
                                enabled: false,
                            }
                        }
                    }
                ],
                xaxis: {
                    categories: string_chart_by_year['list_year'],
                    title: {
                        text: '{LANG.statistics_time}',
                        style: {
                            fontSize: '14px',
                            fontFamily: 'Helvetica, Arial, sans-serif',
                            colors: ['#333'],
                            fontWeight: 600
                        }
                    }
                },
                yaxis: {
                    labels: {
                        show: true,
                        formatter: function(val, index) {
                            return new Intl.NumberFormat(nv_lang_data == 'vi' ? 'vi-VN' : 'en-US', {
                                maximumFractionDigits: 0,
                            }).format(val);
                        }
                    },
                    title: {
                        text: '{LANG.num_bid_row}',
                        style: {
                            fontSize: '14px',
                            fontFamily: 'Helvetica, Arial, sans-serif',
                            colors: ['#333'],
                            fontWeight: 600
                        }
                    }
                },
                fill: {
                    opacity: 1
                },
                legend: {
                    position: 'top',
                    horizontalAlign: "center"
                }
            };
            var chart = new ApexCharts(document.querySelector("#history_char_by_year"), options);
            chart.render();
        </script>
        <!-- END: chart_history_year -->
        <!-- END: chart -->
        <!-- BEGIN: value_chart -->
        <div class="business-chart">
            <div class="clearfix">
                <div class="pull-right margin-top margin-left margin-right">
                    <select class="form-control" id="view_chart" onchange="buildChartNew();" name="view_chart" >
                        <option value="1">{LANG.view_chart_7_years}</option>
                        <option value="2">{LANG.view_chart_full}</option>
                    </select>
                </div>
                <!-- BEGIN: dropdown -->
                    <div class="pull-right margin-top margin-left margin-right">
                        <select name="package_type" onchange="buildChartNew();" class="form-control">
                            <option value="1">{LANG.all_packages}</option>
                            <option value="2">{LANG.pg_appointment_contractors}</option>
                            <option value="3">{LANG.pg_with_kqlcnt_without_tbmt}</option>
                        </select>
                    </div>
                <!-- END: dropdown -->
                <div class="hidden">
                    <div id="vchartdtype_1" data-desire="{DATA_DESIRE_FULL}" data-real="{DATA_REAL_FULL}" data-inde="{DATA_INDE_FULL}"></div>
                    <div id="vchartdtype_2" data-desire="{DATA_DESIRE_CDT_FULL}" data-real="{DATA_REAL_CDT_FULL}" data-inde="{DATA_INDE_CDT_FULL}"></div>
                    <div id="vchartdtype_3" data-desire="{DATA_DESIRE_NOT_TBMT_FULL}" data-real="{DATA_REAL_NOT_TBMT_FULL}" data-inde="{DATA_INDE_NOT_TBMT_FULL}"></div>
                </div>
            </div>
            <div class="business-chart-title no-margin-bottom"><h3>{LANG.value_chart_title}</h3></div>
            <div class="row chart main_chart_history">
                <div class="col-md-24 col-sm-24 col-xs-24">
                    <div id="value_chart" style="height: 365px;padding-right: 5px;">
                    </div>
                    <div class="value-chart-title">
                        {LANG.statistics_time}
                    </div>
                </div>
                <div class="col-md-24 text-center col-sm-24 col-xs-24 des-value-chart">
                    {LANG.des_value_desire}
                </div>
                <p class="text-left cls_info_1">{CHART_LINKVIEW_1}</p>
                <p class="text-left cls_info_2">{CHART_LINKVIEW_2}</p>
                <p class="text-left cls_info_3">{CHART_LINKVIEW_3}</p>
            </div>
        </div>
        <script type="text/javascript">

            buildChart(1);
            function buildChartNew() {
                $("#value_chart").html("");
                var type = $("select[name='package_type']").val();
                buildChart(type);
            }
            function buildChart(type) {
                let data_desire = [];
                let data_real = [];
                let data_inde = [];

                data_desire = JSON.parse($('#vchartdtype_' + type).attr('data-desire'));
                data_real = JSON.parse($('#vchartdtype_' + type).attr('data-real'));
                data_inde = JSON.parse($('#vchartdtype_' + type).attr('data-inde'));
                if ($('#view_chart').val() == 1) {
                    let nv_currenttime = Date.now();
                    let count_del = 0;
                    data_desire.forEach((e) => {
                        if (e[0] < nv_currenttime - 220752000000) {
                            count_del++;
                        } else {
                            return;
                        }
                    });
                    data_desire.splice(0, count_del);
                    data_real.splice(0, count_del);
                    data_inde.splice(0, count_del);
                }

                var options = {
                    series: [{
                        name: '{LANG.value_desire}',
                        data: data_desire
                    },
                    {
                        name: '{LANG.value_real}',
                        data: data_real
                    },
                    {
                        name: '{LANG.value_inde}',
                        data: data_inde
                    }],
                    chart: {
                        type: 'area',
                        stacked: false,
                        height: '87%',
                        zoom: {
                            enabled: true,
                        },
                        offsetY: 10,
                        offsetX: 5,
                    },
                    stroke: {
                        curve: 'smooth'
                    },
                    dataLabels: {
                        enabled: false
                    },
                    tooltip: {
                        enabled: ##is_vip##,
                        x: {
                            format: 'MM/yyyy'
                        },
                        y: {
                            formatter: function(val, index) {
                                return new Intl.NumberFormat('vi-VN', {
                                    maximumFractionDigits: 0,
                                }).format(val) + ' VND';
                            }
                        },
                        tooltip: {
                            share: true
                        }
                    },
                    // title: {
                    //     text: '{LANG.value_chart_title}',
                    //     align: 'center',
                    //     margin: 10,
                    //     offsetX: 0,
                    //     offsetY: 0,
                    //     floating: false,
                    //     style: {
                    //       fontSize:  '14px',
                    //       fontWeight:  'bold',
                    //       fontFamily:  'Helvetica, Arial, sans-serif',
                    //       color:  '#263238'
                    //     },
                    // },
                    colors : ['#4576b5', '#ff9f43', '#ff6b6b'],
                    fill: {
                        type: 'gradient',
                        gradient: {
                            shadeIntensity: 1,
                            inverseColors: false,
                            opacityFrom: 0.7,
                            opacityTo: 0.2,
                            stops: [0, 50, 100]
                        },
                    },
                    xaxis: {
                        type: 'datetime',
                        labels: {
                            datetimeFormatter: {
                                year: 'yyyy',
                                month: 'MM/yyyy',
                                day: 'dd/MM/yyyy',
                                hour: 'HH:mm'
                            }
                        }
                    },
                    yaxis: {
                        labels: {
                            show: ##is_vip##,
                            formatter: function(val, index) {
                                val = val / 1000000;
                                return new Intl.NumberFormat('vi-VN', {
                                    maximumFractionDigits: 0,
                                }).format(val);
                            },
                            offsetX: 3,
                        },
                        title: {
                            text: '{LANG.title_value_chart}',
                            offsetX: 2,
                            style: {
                                fontSize: '14px',
                                fontFamily: 'Helvetica, Arial, sans-serif',
                                colors: ['#333'],
                                fontWeight: 600
                            }
                        }
                    }, legend: {
                        position: 'top',
                        horizontalAlign: "center"
                    }
                };
                var chart = new ApexCharts(document.querySelector("#value_chart"), options);
                chart.render();
            }
        </script>
        <!-- END: value_chart -->

        <!-- BEGIN: difference_chart -->
        <div class="business-chart">
            <div class="clearfix"></div>
            <div class="business-chart-title"><h3>{LANG.difference_price_title}</h3></div>
            <div class="row chart difference-chart">
                <div class="col-md-24 col-sm-24 col-xs-24">
                    <div class="difference_price_chart">
                        <div id="difference_price_pie"></div>
                    </div>
                </div>
                <div class="col-md-24 col-sm-24 col-xs-24">
                    <div class="difference_price_table">
                        <table class="table res-table text-center table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">{LANG.grouping}</th>
                                    <th class="text-center">{LANG.quantity}</th>
                                    <th class="text-center">{LANG.percentage}</th>
                                </tr>
                            <thead>
                            <tbody>
                                <!-- BEGIN: loop -->
                                <tr>
                                    <td>{DIFFERENCE_PRICE_DATA.label}</td>
                                    <td>{DIFFERENCE_PRICE_DATA.value}</td>
                                    <td>{DIFFERENCE_PRICE_DATA.percentage}</td>
                                </tr>
                                <!-- END: loop -->
                                <tr>
                                    <th class="text-center">{LANG.difference_price_total}</th>
                                    <th class="text-center">{DIFFERENCE_PRICE_TOTAL}</th>
                                    <th class="text-center">100%</th>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
        <script type="text/javascript">
            var chart_data = {DIFFERENCE_CHART_DATA};
            var chart_color = {DIFFERENCE_CHART_COLOR};
            google.charts.load("current", { packages: ["corechart"] });
            google.charts.setOnLoadCallback(function () {
                var data = google.visualization.arrayToDataTable(chart_data);
                var options = {
                    title: "",
                    pieSliceText: "percentage",
                    legend: { position: "labeled" },
                    colors: chart_color,
                    pieSliceTextStyle: {
                        color: "black",
                        fontSize: 12,
                    },
                    chartArea: {
                        width: '95%',
                        height: '95%',
                    },
                    sliceVisibilityThreshold: 0
                };
                var chart = new google.visualization.PieChart(
                    document.getElementById("difference_price_pie")
                );
                chart.draw(data, options);
            });
        </script>
        <!-- END: difference_chart -->
        <!-- BEGIN: interest_chart -->
        <div class="hidden">
            <div id="string_chart_fields" data-scf="{STRING_CHART_BY_FIELDS}"></div>
        </div>
        <div class="business-chart">
            <div class="clearfix"></div>
            <div class="business-chart-title"><h3>{LANG.chart_bidsfield}</h3></div>
            <div class="row chart interest_chart_wrapp">
                <div class="interest_chart_wrapp">
                    <div id="interest_chart" style="height: 415px;"></div>
                </div>
            </div>
        </div>
        <script type="text/javascript">
            var string_chart_by_fields = $('#string_chart_fields').data('scf');
            var labelChartFields = string_chart_by_fields['title'];
            var options3 = {
                    chart: {
                        height: 400,
                        type: "line",
                        stacked: false
                    },
                    dataLabels: {
                        enabled: false
                    },
                    colors: ['#4576B5', '#EC7E30', '#2ECC71', '#FEC001', '#808080', '#97593C', '#0000FF', '#FF0000'],
                    series: [{
                        name: labelChartFields['lb_total_doclap'],
                        data: string_chart_by_fields['data']['total_doclap'],
                        type: 'column'
                    }, {
                        name: labelChartFields['lb_total_liendanh'],
                        data: string_chart_by_fields['data']['total_liendanh'],
                        type: 'column'
                    }, {
                        name: labelChartFields['lb_total_win_doclap'],
                        data: string_chart_by_fields['data']['total_win_doclap'],
                        type: 'column'
                    },  {
                        name: labelChartFields['lb_total_win_liendanh'],
                        data: string_chart_by_fields['data']['total_win_liendanh'],
                        type: 'column'
                    }, {
                        name: labelChartFields['lb_total_num_slip_dl'],
                        data: string_chart_by_fields['data']['total_slip_doclap'],
                        type: 'column'
                    }, {
                        name: labelChartFields['lb_total_num_slip_ld'],
                        data: string_chart_by_fields['data']['total_slip_liendanh'],
                        type: 'column'
                    }, {
                        name: labelChartFields['lb_total_price_doclap'],
                        type: 'line',
                        data: string_chart_by_fields['data']['total_price_doclap'],
                    }, {
                        name: labelChartFields['lb_total_price_liendanh'],
                        type: 'line',
                        data: string_chart_by_fields['data']['total_price_liendanh']
                    }],
                    stroke: {
                        width: [0, 0, 0, 0, 0, 0, 2, 2],
                        curve: 'smooth'
                    },
                    markers: {
                        size: 3,
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: "60%"
                        }
                    },
                    fill: {
                        opacity: 1
                    },
                    xaxis: {
                        categories: string_chart_by_fields['list_fields'],
                        tickPlacement: 'between',
                        title: {
                            text: labelChartFields['interest_bids'],
                            offsetY: -5,
                            style: {
                                fontSize: '14px',
                                fontFamily: 'Helvetica, Arial, sans-serif',
                                colors: ['#333'],
                                fontWeight: 600
                            }
                        }
                    },
                    yaxis: [
                        {
                            seriesName: labelChartFields['lb_total_doclap'],
                            axisTicks: {
                                show: true
                            },
                            axisBorder: {
                                show: true
                            },
                            labels: {
                                formatter: function(val, index) {
                                    return new Intl.NumberFormat(nv_lang_data == 'vi' ? 'vi-VN' : 'en-US', {
                                        maximumFractionDigits: 0,
                                    }).format(val);
                                }
                            },
                            title: {
                                text: labelChartFields['num_bid_row'],
                                offsetX: -5,
                                style: {
                                    fontSize: '14px',
                                    fontFamily: 'Helvetica, Arial, sans-serif',
                                    colors: ['#333'],
                                    fontWeight: 600
                                }
                            }
                        }, {
                            seriesName: labelChartFields['lb_total_doclap'],
                            show: false
                        }, {
                            seriesName: labelChartFields['lb_total_doclap'],
                            show: false
                        }, {
                            seriesName: labelChartFields['lb_total_doclap'],
                            show: false
                        }, {
                            seriesName: labelChartFields['lb_total_doclap'],
                            show: false
                        }, {
                            seriesName: labelChartFields['lb_total_doclap'],
                            show: false
                        },  {
                            seriesName: labelChartFields['lb_total_price_liendanh'],
                            show: false
                        }, {
                            opposite: true,
                            seriesName: labelChartFields['lb_total_price_liendanh'],
                            axisTicks: {
                                show: true
                            },
                            axisBorder: {
                                show: true,
                            },
                            labels: {
                                formatter: function(val, index) {
                                    val = val / 1000000;
                                    return new Intl.NumberFormat('vi-VN', {
                                        maximumFractionDigits: 0,
                                    }).format(val);
                                }
                            },
                            title: {
                                text: labelChartFields['total_win_price_title_y'],
                                offsetX: 5,
                                style: {
                                    fontSize: '14px',
                                    fontFamily: 'Helvetica, Arial, sans-serif',
                                    colors: ['#333'],
                                    fontWeight: 600
                                }
                            }
                        }
                    ],
                    tooltip: {
                        shared: true,
                        intersect: false,
                        x: {
                            show: false
                        },
                        y: {
                            formatter: function (value, { seriesIndex }) {
                                if (seriesIndex >= 6) {
                                    return new Intl.NumberFormat('vi-VN', {
                                        maximumFractionDigits: 0,
                                    }).format(value) + ' VND';
                                } else {
                                    return new Intl.NumberFormat(nv_lang_data == 'vi' ? 'vi-VN' : 'en-US', {
                                        maximumFractionDigits: 0,
                                    }).format(value) + labelChartFields['goithau'];
                                }
                                return value;
                            }
                        },
                        style: {
                            fontSize: '13px'
                        }
                    }, legend: {
                        position: 'top',
                        horizontalAlign: "center"
                    }
                };
                var chart = new ApexCharts(document.querySelector("#interest_chart"), options3);
                chart.render();
        </script>
        <!-- END: interest_chart -->
    </div>

    <script type="text/javascript">
        var capcha = 0;
        function show_capcha() {
            $("#captchaModal").modal();
        }
        function click_update_business() {
            $('#captchaModal').modal('hide');
            confirm_crawl_detail();
            return !1;
        }
        function confirm_crawl_detail() {
            $.ajax({
                url: "{URL_UPDATE}",
                type: "POST",
                dataType: "json",
                data: {
                    'confirm_crawl': 1,
                    'g-recaptcha-response' : $("[name=g-recaptcha-response]").val() ?? ''
                },
                success: function(a) {
                    modalShow("", a.mess);
                    $("#accept_crawl").click(function() {
                        text = $(this).attr('data-text');
                        $("#accept_crawl").prop("disabled", true).html('<i class="fa fa-spinner fa-spin"></i> ' + text);
                        setTimeout(function() {
                            updateCrawl();
                        }, 800)
                    });
                }
            });
        }

        function updateBusinessCrawl() {
            $.ajax({
                type : "POST",
                url : "{URL_UPDATE}",
                data : {
                    check : '{CHECKSESS_UPDATE}',
                    update : '1'
                },
                success : function(res) {
                    alert(res);
                    window.location.href = window.location.href;
                }
            });
            return !1;
        }
        var checkess='<<CHECK_SESSION_DOWNFILE>>';

        $(function() {
            var hash = $(location).attr('hash');
            if (hash) {
                $('html, body').animate({ scrollTop : $("[id*='" + hash + "']").offset().top }, 800);
            }
        });

        $(function() {
            var hash = $(location).attr('hash');
            if (hash) {
                $('html, body').animate({ scrollTop : $("[id*='" + hash + "']").offset().top }, 800);
            }
        });

    </script>
    <!-- END: detail -->

    <div id="utilities" class="detail_scoll__menu">
        <!-- BEGIN: downloadPDF -->
        <h2 class="bidding-sub-title">{LANG.tienich}</h2>
        <!--T100-->
        <div class="chance-cont">
            <div class="chance">
                <div class="panel panel-default" style="margin-bottom: 0;">
                    <div class="cont panel-heading">
                        <div class="info_download_pdf">
                            <p>{LANG.information_pdf}</p>
                        </div>
                        <div class="##margin_bottom## text-center" style="border-bottom: 0;">
                            <button id='downloadPDF' ##disabled## class="btn" title="{LANG.title_btn_download_pdf}"  style="background-color: #34A853; color: white; margin-bottom: 5px;" onclick="checkPoint();"><i class="fa fa-file-pdf-o" aria-hidden="true"></i> {LANG.download_PDF}</button>
                            <<point_download>>
                            <<no_login>>
                            <div id="alertdownloadPDF"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--T100-->
        <!-- END: downloadPDF -->
        <<t100_alert>>
    </div>

    <br/>
    <!-- BEGIN: timeline -->
    <div id="contractor_activities" class="detail_scoll__menu">
        {FILE "slide-include/timeline.tpl"}
    </div>
    <!-- END: timeline -->
    <div id="detail_data" class="detail_scoll__menu">
        <h2>{LANG.detail_data}</h2>
        <!-- BEGIN: bidding -->
        {FILE "slide-include/table_bidding.tpl"}
        <!-- END: bidding -->

        <!-- BEGIN: investor -->
        {FILE "slide-include/table_invester.tpl"}
        <!-- END: investor -->

        <!-- BEGIN: business -->
        {FILE "slide-include/table_business.tpl"}
        <!-- END: business -->

        <!-- BEGIN: partnership -->
        {FILE "slide-include/table_partnership.tpl"}
        <!-- END: partnership -->

        <!-- BEGIN: contractor_province -->
        {FILE "slide-include/table_contractor_province.tpl"}
        <!-- END: contractor_province -->

    </div>
    <!-- <div class="news_column panel panel-default ">
        <div class="panel-body">[FACEBOOK_COMMENT]</div>
    </div> -->

    <!-- BEGIN: list_vipham -->
    <div class="bidding-simple wrap__text block-bidding" id="number_qdxp">
        <div class="border-bidding">
            <span>{LANG.listvipham}</span>
        </div>
        <!-- BEGIN: hide -->
        </br>
        <p class="alert alert-warning">{LINK_VIP_HIDE}</p>
        <!-- END: hide -->
        <!-- BEGIN: show -->
        <div class="bidding_vp m-bottom">
            <!-- BEGIN: loop -->
                <div class="item">
                    <div class="item_t text-center">
                        <div class="item_t_n">
                            <div class="name_lable">{LANG.decisionno}</div>
                            <div class="name">{VIEW.decisionno}</div>
                        </div>
                        <div class="item_t_b">
                            <button class="btn btn-default js-btn-tooltip" data-toggle="tooltip" data-placement="top"
                                data-custom-class="tooltip-primary" title="{VIEW.issued_date}">
                                {VIEW.issued_date}
                            </button>
                        </div>
                    </div>
                    <div class="nghi_dinh">
                        <h3 class="vp_name">{LANG.vp_name_violate}: {VIEW.name_violate}</h3>
                        <div class="name_violate">
                            <p>{LANG.vp_pentype}: {VIEW.pentype}</p>
                        </div>
                    </div>
                    <!-- BEGIN: client -->
                    <div class="item_t text-center item_b c-pub c-business blur_filter">
                        {client}
                    </div>
                    <!-- END: client -->
                    <!-- BEGIN: user -->
                    <div class="item_t text-center item_b c-pub c-business blur_filter">
                        {user}
                    </div>
                    <!-- END: user -->
                    <!-- BEGIN: vip -->
                    <div class="item_t text-center item_b">
                        <a title="{VIEW.name}" href="{VIEW.link_detail}" class="blue-btn"><i class="fa fa-eye"></i> <span>{LANG.detail}</span></a>
                    </div>
                    <!-- END: vip -->
                </div>
            <!-- END: loop -->
        </div>
        <div>
            <p>{NUMBER_VIPHAM}</p>
            {view_listvipham}
        </div>
        <!-- END: show -->
    </div>
    <script type="text/javascript">
        $(document).ready(function(){
            $('.js-btn-tooltip').tooltip();
        });
    </script>
    <!-- END: list_vipham -->
</div>

<!-- BEGIN: news_api_isset -->
<div class="border-bidding">
    <span>{LANG.news_api}</span>
</div>
<div>
    <!-- BEGIN: news_api -->
    <div class="col-md-24 text-justify">
        <a class="bidding_link" href="{NEWS.href}" title="{NEWS.title}" target="_blank">{NEWS.title}</a>
        <p class="text-mute">
            <em class="fa fa-clock-o">&nbsp;</em>{NEWS.publish_date} <i class="fa fa-link"></i> {NEWS.source}
        </p>
    </div>
    <!-- END: news_api -->
</div>
<!-- END: news_api_isset -->

<!-- BEGIN: downloadPDFScript -->
<script type="text/javascript">
    function checkPoint () {
        var currentLocation = window.location.href;
        var confirm_download_pdf = 1;
        var b = 'confirm_download_pdf=' + confirm_download_pdf;
        $.ajax({
            url: currentLocation,
            method: 'POST',
            dataType: 'json',
            data: b,
            beforeSend:function()
            {
                var download_check_timer = nv_settimeout_disable('downloadPDF', 3000);
            },
            success: function(data) {
                if (data.res == 'success') {
                    if (data.id_request != '') {
                        $('#confirm_re_download').html(data.mess_re_download);
                        $('#confirm_download').html(data.mess);
                        $('#confirm_box-re-submit').attr('data-id', data.id_request);
                        $('#confirm_box_re_download').modal('show');
                    } else {
                        $('#confirm_download').html(data.mess);
                        $('#confirm_box').modal('show');
                    }
                } else {
                    alertDiv = '<div class="alert alert-warning">' + data.mess + '</div>';
                    $('#alertdownloadPDF').html(alertDiv);
                }
            }
        });
    }

    function showConfirm () {
        $('#confirm_box_re_download').modal('hide');
        setTimeout(function() {
            $('#confirm_box').modal('show');
        }, 500);
    }
    function reDownloadPdf () {
        var id_request = $('#confirm_box-re-submit').attr('data-id');
        var url = nv_base_siteurl + 'index.php?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=exportpdf&downloadfile=1&requestid=' + id_request;
        if (id_request != '') {
            window.location.href = url;
            return;
        }
    }
    function submitDownloadPdf () {
        var currentLocation = window.location.href;
        var checksess_download_pdf = '<<check_session>>';
        var contractor_name = '<<contractor_name>>';
        var id_business = '<<contractor_id>>';
        var params = 'checksess_download_pdf=' + checksess_download_pdf + '&name=' + contractor_name + '&id_business=' + id_business;
        var modal = $('#confirm_box');
        modal.modal('hide');
        $.ajax({
            url: currentLocation,
            method: 'POST',
            dataType: 'json',
            data: params,
            beforeSend:function()
            {
                $('#downloadPDF').html('{LANG.processing} <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>');
                $('#downloadPDF').attr('disabled', 'disabled');
            },
            success: function(data) {
                if (data.res == 'success') {
                    $('#confirm_redirect').text(data.mess);
                    $('#redirect_box').modal('show');
                    $('#alertDownloadPDF').html('');
                    console.log('time run: ' + data.time_run);
                } else {
                    modalShow('', data.mess);
                }
                $('#downloadPDF').html('<i class="fa fa-file-pdf-o" aria-hidden="true"></i> {LANG.download_PDF}');
                setInterval(function(){
                    $('#downloadPDF').attr('disabled', false);
                }, 2000);
            },
            error: function() {
                $('#downloadPDF').html('<i class="fa fa-file-pdf-o" aria-hidden="true"></i> {LANG.download_PDF}');
                modalShow('', '{LANG.unknown_error}');
                setInterval(function(){
                    $('#downloadPDF').attr('disabled', false);
                }, 2000);
            }
        });
    }
</script>
<!-- START MODAL CONFIRM RE-DOWNLOAD -->
<div class="modal fade modal-center" tabindex="-1" role="dialog" id="confirm_box_re_download" data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <p class="modal-title h4">{LANG.notice}</p>
            </div>
            <div class="modal-body">
                <p id="confirm_re_download"></p>
            </div>
            <div class="modal-footer">
                <button type="button" data-id="" id="confirm_box-re-submit" onclick="reDownloadPdf();" class="btn btn-primary btn-fw">{LANG.download_old_pdf}</button>
                <button type="button" class="btn btn-default btn-fw" onclick="showConfirm()" id="confirm_box-re-cancel">{LANG.download_new_pdf}</button>
            </div>
        </div>
    </div>
</div>
<!-- END MODAL CONFIRM RE-DOWNLOAD -->
<!-- START MODAL CONFIRM -->
<div class="modal fade modal-center" tabindex="-1" role="dialog" id="confirm_box" data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <p class="modal-title h4">{LANG.notice}</p>
            </div>
            <div class="modal-body">
                <p id="confirm_download"></p>
            </div>
            <div class="modal-footer">
                <button type="button" id="confirm_box-submit" onclick="submitDownloadPdf();" class="btn btn-primary btn-fw">{LANG.ok}</button>
                <button type="button" class="btn btn-default btn-fw" id="confirm_box-cancel" data-dismiss="modal">{LANG.cancel}</button>
            </div>
        </div>
    </div>
</div>
<!-- END MODAL CONFIRM -->
<!-- START MODAL REDIRECT -->
<div class="modal fade modal-center" tabindex="-1" role="dialog" id="redirect_box" data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <p class="modal-title h4">{LANG.notice}</p>
            </div>
            <div class="modal-body">
                <p id="confirm_redirect"></p>
            </div>
            <div class="modal-footer">
                <a href="{PDF_LINK}" type="button" id="redirect_box-submit" class="btn btn-primary btn-fw" style="color: white;">{LANG.redirect_to_manager_request}</a>
                <button type="button" class="btn btn-default btn-fw" id="redirect_box-cancel" data-dismiss="modal">{LANG.continue_browsing}</button>
            </div>
        </div>
    </div>
</div>
<!-- END MODAL REDIRECT -->
<!-- END: downloadPDFScript -->

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "@id": "{NV_MY_DOMAIN}{ROW.detail_link}",
    "name": {ROW.companyname_seo},
    "image": "{NV_MY_DOMAIN}{ROW.logo}",
    "address": {
    "@type": "PostalAddress",
    "streetAddress": {ROW.address_seo},
    "addressLocality": "{ROW.province}",
    "addressRegion": "{ROW.province}"
    },
    "taxID": "{ROW.taxcode}",
    "logo": "{NV_MY_DOMAIN}{ROW.logo}",
    "faxNumber": "{ROW.fax}",
    "email": "{ROW.email}",
    "url": "{NV_MY_DOMAIN}{ROW.detail_link}",
    "telephone": "{ROW.phone_seo}",
    "foundingDate": "{ROW.dateestablished_seo}",
    "numberOfEmployees": "{ROW.so_nhan_vien}",
    "priceRange":"{ROW.von_dieu_le}VND"
}
</script>
<script type="text/javascript">
    (function( $ ){
        if ($(window).width() < 483) {
            // Kiểm tra xem cả hai thẻ đều tồn tại trước khi tiếp tục
            if ($('.crawl_time').length > 0 && $('.detail-list-btn').length > 0) {
                // Di chuyển phần tử crawl_time ra trên thẻ div chính
                $('.crawl_time').insertBefore($('.detail-list-btn'));
                $('.view_history').insertBefore($('.detail-list-btn'));
            }
        }
        $(document).on("click", ".download-file",function(){
            var idfile = $(this).attr("idfile");
            var checkss = $(this).attr("checkss");
            var type_file = $(this).attr("data-type");
            if (idfile !== "undefined" ) {
                $.ajax({
                    url: location.href,
                    type: 'POST',
                    data: {
                        'action': 'download_static',
                        'idfile' : idfile,
                        'did' : {ROW.id},
                        'type_file' : type_file,
                        'checkss' : checkss
                    },
                    success: function(data) {
                        if (data['res'] == 'success') {
                            window.open(data['link'], '_blank');
                        } else {
                            alert(data['message']);
                        }
                    }
                })
            }
        });

        $('.btn_re_download').click(function() {
            re_download();
        });

        function re_download() {
            $.ajax({
                url: location.href,
                type: 'POST',
                data: {
                    'action': 're_download',
                    'did' : {ROW.id},
                    'checkss' : '{CHECKSESS_UPDATE}',
                },
                success: function(data) {
                    if (data['res'] == 'success') {
                        alert(data['message']);
                        location.reload();
                    } else {
                        alert(data['message']);
                    }
                }
            })
        }

        $.fn.fitText = function( kompressor, options ) {
            // Setup options
            var compressor = kompressor || 1,
                settings = $.extend({
                    'minFontSize' : Number.NEGATIVE_INFINITY,
                    'maxFontSize' : Number.POSITIVE_INFINITY
                }, options);

            return this.each(function(){

                // Store the object
                var $this = $(this);

                // Resizer() resizes items based on the object width divided by the compressor * 10
                var resizer = function () {
                $this.css('font-size', Math.max(Math.min($this.width() / (compressor*10), parseFloat(settings.maxFontSize)), parseFloat(settings.minFontSize)));
                };

                // Call once to set.
                resizer();

                // Call on resize. Opera debounces their resize by default.
                $(window).on('resize.fittext orientationchange.fittext', resizer);

            });
        };
    })( jQuery );

</script>
{FILE "modal_log.tpl"}
<!-- END: main -->
