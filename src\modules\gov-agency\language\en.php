<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2022 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Wed, 11 May 2022 09:11:42 GMT
 */

if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

$lang_translator['author'] = 'VINADES.,JSC (<EMAIL>)';
$lang_translator['createdate'] = '11/05/2022, 09:11';
$lang_translator['copyright'] = '@Copyright (C) 2022 VINADES.,JSC. All rights reserved';
$lang_translator['info'] = '';
$lang_translator['langtype'] = 'lang_module';

$lang_module['agency'] = 'Detail statistics by project of';
$lang_module['national'] = 'National statistics';
$lang_module['solicitor'] = 'Detail statistics by investor/solicitor of';

$lang_module['main'] = 'Goverment Agency';
$lang_module['main_title'] = 'Bidding statistics of government agencies';
$lang_module['main_desc'] = 'Bidding data of Vietnam goverment agencies, compiled and analyzed by DauThau.info with comprehensive information of each agency.';
$lang_module['agency_func_desc'] = 'Detailed bidding information of each state management unit is statistically and scientifically presented by DauThau.info of the projects along with related bidding data history.';
$lang_module['chudautu_func_desc'] = 'Bidding data statistics table of state management agencies by investor/inviting party provides information on: number of projects, total number of IT bid packages, total investment, total bid package price...';

$lang_module['project'] = 'Detail statistics by project';
$lang_module['filter'] = 'Filter';
$lang_module['time_filter'] = 'Post time of project or plan';
$lang_module['agency_filter'] = 'Government Agency';
$lang_module['source_filter'] = 'Filter by source';
$lang_module['classify_select_txt'] = 'Method to classify agencies';
$lang_module['source_list'] = [
    0 => 'All',
    1 => 'Investment projects',
    2 => 'Mandatory spending',
    3 => 'Career development fund',
    4 => 'ODA',
    9 => 'Others'
];
$lang_module['type_filter'] = 'Agency type';
$lang_module['type_list'] = [
    0 => 'All',
    5 => 'Ministries and Local Authorities',
    1 => 'Ministries',
    2 => 'Local Authorities',
    3 => 'Public Sector Units and Enterprises',
    4 => 'Others',
    6 => 'No Supervisory Authority'
];
$lang_module['type_management_list'] = [
    0 => 'All',
    1 => 'Province/City',
    2 => 'Ministries/Departments',
    3 => 'Corporations/Companies',
    4 => 'No Supervisory Authority'
];
$lang_module['classify'] = [
    'msc' => 'By MSC',
    'dmdc' => 'By DMDC'
];

$lang_module['chua_xac_dinh'] = 'Undefined';
$lang_module['c1'] = 'Level 1';
$lang_module['c2'] = 'Level 2';
$lang_module['c3'] = 'Level 3';
$lang_module['c4'] = 'Level 4';

$lang_module['get_c2_from_c1'] = 'Get by level 1';
$lang_module['get_c3_from_c2'] = 'Get by level 2';
$lang_module['get_c4_from_c3'] = 'Get by level 3';

$lang_module['no_c1'] = 'No data of level 1';
$lang_module['no_c2'] = 'No data of level 2';
$lang_module['no_c3'] = 'No data of level 3';
$lang_module['no_c4'] = 'No data of level 4';

$lang_module['unit_b'] = '(billion vnd)';
$lang_module['unit_m'] = '(million vnd)';
$lang_module['unit_d'] = '(vnd)';

$lang_module['stt'] = 'STT';
$lang_module['num_investor'] = 'Total investors';
$lang_module['num_project'] = 'Total projects';
$lang_module['num_project_not_complete'] = 'Total of not completed projects';
$lang_module['num_project_completed'] = 'Total of completed projects';
$lang_module['num_bid'] = 'Total bid';
$lang_module['total_investment'] = 'Total investment';
$lang_module['total_bidding'] = 'Total bid prices';
$lang_module['total_bidding_win'] = 'Total bid winning prices';
$lang_module['num_business'] = 'Total businesses';
$lang_module['total_invest_all'] = 'Total investment';
$lang_module['total_invest_all_vnd'] = 'Total investment released';
$lang_module['project_status'] = ['Not completed', 'Completed'];
$lang_module['project_name'] = 'Project name';
$lang_module['project_goal'] = 'Project goal';
$lang_module['project_content'] = 'Project content';
$lang_module['num_bid_project'] = 'Number of bid per project';
$lang_module['total_bid_price'] = 'Total of bid price per project';
$lang_module['project_stat'] = 'Project status (completed/not completed)';
$lang_module['ce_num_projects_round'] = 'Number project';
$lang_module['days_of_week'] = 'Sun Mon Tue Wed Thu Fri Sat';
$lang_module['cal_month'] = 'Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec';
$lang_module['apply'] = 'Apply';
$lang_module['cancel'] = 'Cancel';
$lang_module['last_3_months'] = 'Recent 3 months';
$lang_module['last_6_months'] = 'Recent 6 months';
$lang_module['current_year'] = 'This year';
$lang_module['1_year_ago'] = '1 year ago';
$lang_module['2_years_ago'] = '2 year ago';
$lang_module['3_years_ago'] = '3 year ago';
$lang_module['last_1_year'] = 'last 1 year';
$lang_module['last_2_years'] = 'last 2 year';
$lang_module['last_3_years'] = 'last 3 year';
$lang_module['last_4_years'] = 'last 4 year';
$lang_module['last_5_years'] = 'last 5 year';
$lang_module['custom_range'] ='Other';
$lang_module['round_to'] = 'Round to';
$lang_module['round_select'] = ['Billion', 'Million'];
$lang_module['billionVND'] = 'billion';
$lang_module['millionVND'] = 'million';
$lang_module['solicitor_name'] = 'Name of investor/solicitor';
$lang_module['agency_name'] = 'Agency name';
$lang_module['bid_name'] = 'Bid name';
$lang_module['project_plan_code'] = 'Project plan code';
$lang_module['solictor'] = 'Solictor statistics';
$lang_module['desc'] = 'Description';
$lang_module['number'] = 'No.';
$lang_module['place'] = 'Project place';
$lang_module['investor'] = 'Investor';
$lang_module['list_plan'] = 'Contractor selection plan';
$lang_module['code'] = 'Project code';
$lang_module['type'] = 'Notification type';
$lang_module['total_invest'] = 'Total investment';
$lang_module['finish_time'] = 'Finish time';
$lang_module['win_price_number'] = 'Bid winning price';
$lang_module['win_price'] = 'Bid winning price';
$lang_module['time_todo'] = 'Finish in';
$lang_module['sum'] = 'Sum';

$lang_module['view_gov_agc_as_guest'] = 'You need login to view. To see statistics data of each government agency, please register <b>VIP 3</b>.';
$lang_module['ask_for_vip3'] = "To see detail, you need <a href='%s'>register VIP 3.";

$lang_module['login'] = 'Sign in';
$lang_module['lock_icon'] = '<a href="%s" title="Sign in to see full information"><i class="fa fa-lock" aria-hidden="true"></i></a>';
$lang_module['anonymous_lock_title'] = 'Sign in to see full information';
$lang_module['view_result_one'] = 'The data was partially hidden <strong><a href="%s" title="The data was partially hidden, please signin">Sign in</a></strong> or <strong><a href="%s" title="The data was partially hidden, please register">Register</a> </strong> to see full information.';

$lang_module['ask_register_vip3'] = 'The data was partially hidden, to see full, please <strong><a href="%s" title="Register VIP3">Register VIP3</a></strong>';
$lang_module['ask_renew_vip3'] = 'The data was partially hidden, to see full, please <strong><a href="%s" title="Renew VIP3">Renew VIP3</a></strong>';

$lang_module['not_vip_overlay'] = 
'<div class="box_lockvip">
    <div class="main_lockvip">
        <div class="tw_lockvip">
            <div class="tw_lockvip_head">
                <img class="tw_lockvip_head__image">
            </div>
            <div class="tw_lockvip__button">
                <a href="%s" title="%s" class="btn__lock_login">%s</a>
            </div>
            <div class="tw_lockvip__content">
                <p class="tw_lockvip__content__des">To see full information</p>
            </div>
        </div>
    </div>
</div>';

$lang_module['main_summary'] = 'In summary, in all provinces there are <b>%s</b> projects from <b>%s</b> investors, among them <b>%s</b> in-progress projects, <b>%s</b> completed projects. Total <b>%s</b> of bids were posted';
$lang_module['agency_summary'] = 'In summary, the agency <b>%s</b> receive total investment of <b>%s</b>, among them <b>%s</b> bids with total price of <b>%s</b>. There are <b>%s</b> contractors won with total winning price of <b>%s</b>';
$lang_module['chudautu_summary'] = 'In summary, there are <b>%s</b> investors/solicitors invest in <b>%s</b> projects with total investment of <b>%s</b>, among them there are <b>%s</b> bids with total price of <b>%s</b>.';

$lang_module['agency_page_title'] = 'Bidding statistics of the agency %s';
$lang_module['cdt_page_title'] = 'Investors statistics of %s';

$lang_module['limit_alert'] = 'More than the limit of 200 government agencies, please narrow your list!';
$lang_module['loading_wait'] = 'Displaying, please wait...';
