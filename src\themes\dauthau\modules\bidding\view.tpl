<!-- BEGIN: error -->
<div class="alert alert-danger">{LANG.error_data}</div>
<!-- END: error -->
<!-- BEGIN: main -->
<div class="hidden wanring_file1">
    <label class="warning__file" data-toggle="popover" title="Cảnh báo tải file" data-content='{WARNING_FILE1}'><i class="fa fa-exclamation-triangle" aria-hidden="true"></i></label>
</div>
<div id="popup_not_dismiss" class="modal fade auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body"></div>
        </div>
    </div>
</div>

<div class="msgshow" id="msgshow"></div>

<!-- BEGIN: popup_login -->
{POPUP_LOGIN}
<!-- END: popup_login -->

<!-- BEGIN: popup_not_point -->
<script>
    $(function() {
        var mess = '{LANG.info_point_not_enought}';
        mess += '<p class="text-center"><a class="btn btn-danger" href="{URL_CRM_SITE}{NV_LANG_DATA}/points/#muadiem" target="_blank">{LANG.buy_points}</a></p>';

        $("#popup_not_dismiss").find(".modal-body").html(mess);
        $("#popup_not_dismiss").modal({
            backdrop: "static",
            keyboard: false
        });
    });
</script>
<!-- END: popup_not_point -->
<!-- BEGIN: msgshow -->
<script>
    alert_msg('{LANG.message_point_view_suss}');
</script>
<!-- END: msgshow -->
<div class="border-bidding">
    <span>{PTL}</span>
</div>
<div id="confirm" class="modal fade auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body"></div>
            <div class="modal-footer">
                <span class="button"></span>
                <button type="button" class="ok btn btn-primary">{LANG.ok}</button>
                <button type="button" data-dismiss="modal" class="btn">{LANG.close}</button>
            </div>
        </div>
    </div>
</div>
<!-- BEGIN: recaptcha -->
<div id="captchaModal" class="modal fade auto-width auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <p class="modal-title">{LANG.recapcha_title}</p>
            </div>
            <div class="modal-body">
                <div>{LANG.recapcha_body}</div>
                <div data-toggle="recaptcha" data-callback="verify_captcha" id="{RECAPTCHA_ELEMENT}"></div>
            </div>
        </div>
    </div>
</div>
<script>
    function verify_captcha(e) {
        click_update();
    }
</script>
<!-- END: recaptcha -->
<div class="bidding-wrapper">
    <!-- Thông báo mời thầu -->
    <div id="ttchsmt" class="scoll__menu">
        <div class="bidding-title">
            <h1 class="tl wrap__text">{DATA.goi_thau}</h1>
        </div>

        <div class="margin-bottom">
            <div class="prb_container">
                <div class="prb clearfix">
                    <!-- BEGIN: mess_item -->
                    <span class="prb-progressbar">{MESS}</span>
                    <!-- END: mess_item -->
                </div>
                <div class="prb clearfix">
                    <!-- BEGIN: prb_item -->
                    <!-- BEGIN: if_a -->
                    <a class="item" href="{PROCESS.url}"> <span class="icn {PROCESS.classes}" title="{PROCESS.title}"></span> <span class="tl">{PROCESS.title}</span>
                        <!-- BEGIN: notification -->
                        <a href="javascript:void(0)" class="item__notifi"> <i class="fa fa-exclamation-circle overdue_noti" aria-hidden="true" data-toggle="tooltip" data-placement="top" title="{DATA.overdue_notification}!"></i>
                        </a>
                        <!-- END: notification -->
                    </a>
                    <!-- END: if_a -->
                    <!-- BEGIN: if_span -->
                    <span class="item"> <span class="icn {PROCESS.classes}" title="{PROCESS.title}"></span> <span class="tl">{PROCESS.title}</span>
                        <!-- BEGIN: notification -->
                        <a href="javascript:void(0)" class="item__notifi"> <i class="fa fa-exclamation-circle overdue_noti"></i>
                        </a>
                        <!-- END: notification -->
                    </span>
                    <!-- END: if_span -->
                    <!-- END: prb_item -->
                </div>
            </div>
        </div>
        <div class="content_notifi" hidden>
            <blockquote class="custom_border">
                {DATA.overdue_notification}
                <p class="customer_border_support_error">{LANG.support_error_notifi}</p>
            </blockquote>
            <i class="fa fa-times-circle close_notifi" aria-hidden="true"></i>
            <a href="{LINK_SUPPORT_ERROR}" target="_blank"><i class="fa fa-exclamation-circle support_notifi" aria-hidden="true" data-toggle="tooltip" data-placement="top" title="{LANG.send_error}"></i></a>
        </div>

        <!-- BEGIN: violate_warning_txt -->
        <div id="onlrule-violate-msg">
            <blockquote class="custom_border">
                {DATA.violate_msg}
                <p class="customer_border_support_error">{LANG.support_error_notifi}</p>
            </blockquote>
            <i class="fa fa-times-circle" aria-hidden="true"></i>
            <a href="{LINK_SUPPORT_ERROR}" target="_blank"><i class="fa fa-exclamation-circle support_notifi" aria-hidden="true" data-toggle="tooltip" data-placement="top" title="{LANG.send_error}"></i></a>
        </div>
        <!-- END: violate_warning_txt -->

        <div class="text-right">{FILE "button_show_log.tpl"}</div>
        <!-- BEGIN: days_left_note -->
        <div class="flex-vertical-center m-bottom">
            <button class="btn btn-dayleft">
                <svg style="display: inline-block; margin: auto" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor" width="20" height="20">
                    <path d="M232 120C232 106.7 242.7 96 256 96C269.3 96 280 106.7 280 120V243.2L365.3 300C376.3 307.4 379.3 322.3 371.1 333.3C364.6 344.3 349.7 347.3 338.7 339.1L242.7 275.1C236 271.5 232 264 232 255.1L232 120zM256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0zM48 256C48 370.9 141.1 464 256 464C370.9 464 464 370.9 464 256C464 141.1 370.9 48 256 48C141.1 48 48 141.1 48 256z" />
                </svg>
                &nbsp;
                {DATA.days_left}
            </button>
        </div>
        <!-- END: days_left_note -->
        <div class="row">
            <div class="col-xs-24 btn-share-group">
                <span>{LANG.share} </span>
                <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_fb_share();" title="{LANG.fb_share}">
                    <span class="icon-facebook"></span>
                </a>
                <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_tw_share('', '{DATA.title}');" title="{LANG.tweet}">
                    <span class="icon-twitter"></span>
                </a>
                <a href="javascript:void(0)" class="btn-share btn-copy-link" title="{LANG.copy_link}">
                    <em class="fa fa-link"></em>
                    <span class="tip" style="display: none;">{LANG.link_copy_successfully}</span>
                </a>
            </div>
        </div>
        <div class="bidding-page-btn m-bottom">
            <!-- BEGIN: follow -->
            <!-- BEGIN: yes -->
            <div class="btn-group follow">
                <button class="btn btn-danger btn-follow dropdown-toggle" data-toggle="dropdown">
                    <em class="fa fa-check"></em> {LANG.follow_yes} &nbsp;<span class="caret"></span>
                </button>
                <ul class="dropdown-menu" role="menu">
                    <li><a href="{DATA.link_follow_manager}"><em class="fa fa-cog"></em> {LANG.follow_manager1}</a></li>
                    <li><a href="javascript:void(0)" onclick="click_unfollow('{LANG.follow_delete_confirm}', '{DATA.link_follow_manager}', {DATA.follow_id}, '{DATA.delete_checkss}');"><em class="fa fa-trash-o"></em> {LANG.follow_delete}</a></li>
                </ul>
            </div>
            <!-- END: yes -->
            <!-- BEGIN: no -->
            <button class="btn btn-primary btn-follow" onclick="click_follow('{DATA.link_follow}', '{CHECKSESS}', this, '{bidNo}', '{USERID}')">
                <em class="fa fa-plus"></em> {LANG.follow_no}
            </button>
            <!-- END: no -->
            <!-- END: follow -->
            <button class="btn btn-primary btn-print" onclick="nv_open_browse('{LINK_PRINT}', 1, 840, 500, 'resizable=yes,scrollbars=yes,toolbar=no,location=no,status=no');return false">
                <em class="fa fa-print"></em> {LANG.print}
            </button>
            <a href="{link_msc}" style="margin-left: 0px"> <button class="btn btn-primary btn-follow {class_link_msc}" style="margin-left: 20px">{LANG.icon_vneps} Link MSC</button></a>
            <div class="text-right crawl_time mg-bt-5">
                <div class="small">
                    {LANG.crawl_time}: <strong>{DATA.fget_time}</strong>
                </div>
                <!-- BEGIN: update -->
                <div class="margin-top-sm">
                    <span class="small">{DATA.update_info}</span>

                    <!-- BEGIN: show_reupdate -->
                    <a style="margin-left: auto" id="reupdate" class="btn btn-default btn-xs active" onclick="show_captcha()" href="javascript:void(0)" data-id="{DATA.id}" data-check="{CHECKSESS_UPDATE}">{LANG.reupdate}</a>
                    <!-- END: show_reupdate -->

                    <img id="update_wait" style="display: none" src="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/images/load_bar.gif" />
                    <!-- BEGIN: crawl_request_history_button -->
                    <a style="margin-left: auto" id="crawl_request_history" class="btn btn-default btn-xs active" href="javascript:void(0)">{LANG.crawl_request_history}</a>
                    <!-- END: crawl_request_history_button -->
                </div>
                <!-- END: update -->
            </div>
        </div>

        {FILE "crawl_request_history_list.tpl"}

        <!-- BEGIN: note -->
        <blockquote>
            <span class="fa fa-flag fa-lg mr-1"></span>{DATA.note}
        </blockquote>
        <!-- END: note -->
        <!-- BEGIN: alert_download -->
        <blockquote>
            <span class="fa fa-download fa-lg mr-1"></span>{DATA.alert_download}
        </blockquote>
        <!-- END: alert_download -->
        <!-- BEGIN: chance_note -->
        <blockquote>
            <p>
                <span class="fa fa-bell fa-lg mr-1"></span>{LANG.chu_y}: {CHANCE.note_time}
            </p>
            <p>{CHANCE.content_chance}</p>
        </blockquote>
        <!-- END: chance_note -->
        <!-- BEGIN: last_ver -->
        <div class="alert alert-info">
            <i class="fa fa-newspaper-o fa-lg" aria-hidden="true"></i> <a href="{LAST_VER.link}"><b class="text-info"><u>{LAST_VER.title}</u></b></a>
        </div>
        <!-- END: last_ver -->

        <!-- BEGIN: show_waring_kqlcnt -->
        <div class="alert alert-warning">
            <p><i class="fa fa-exclamation-triangle" aria-hidden="true"></i> {LINK_WARNING_KQLCNT}</p>
        </div>
        <!-- END: show_waring_kqlcnt -->

        <div class="bidding-detail">
            <div class="bidding-detail-item col-four">
                <div>
                    <div class="c-tit">{LANG.so_tbmt}</div>
                    <div class="c-val">
                        <span class="bd-code">{DATA.so_tbmt}</span><span>{DATA.tbmt_time}</span>
                        <br />{DATA.tbmt_extend_time}
                    </div>
                </div>

                <div>
                    <div class="c-tit">{LANG.ngay_dang_tai}</div>
                    <div class="c-val">{DATA.ngay_dang_tai}</div>
                    <div id="main__menu_bidding">
                        <div id="bidding__menu">
                            <!-- <button id="show__dmmu"><span>Tam giacs</span></button> -->
                            <div id="fixmenu">
                                <div class="bidding__menu-title">
                                    <span class="table-heading f-w-500"><i class="fa fa-bars hidden__bidding__menu-tab" aria-hidden="true"></i>{LANG.main_conntent}</span> <span class="hidden__nav"> <i class="fa fa-times close__menu" aria-hidden="true"></i>
                                    </span>
                                </div>
                                <div class="bidding__menu-main">
                                    <nav class="bidding__menu-tab-navigation" id="navigation">
                                        <ul id="tableMenuContent">
                                            <li><a class="bidding__link active" href="#ttchsmt">{LANG.tttbmt}</a></li>
                                            <!-- BEGIN: menu_noi_dung -->
                                            <li><a class="bidding__link" href="#nd">{LANG.noi_dung}</a></li>
                                            <!-- END: menu_noi_dung -->
                                            <li><a class="bidding__link" href="#tdt">{LANG.thamduthau}</a></li>
                                            <li><a class="bidding__link" href="#mt">{LANG.mothau}</a></li>
                                            <!-- BEGIN: menu_bid_security -->
                                            <li><a class="bidding__link" href="#bddt">{LANG.bddt}</a></li>
                                            <!-- END: menu_bid_security -->
                                            <li><a class="bidding__link" href="#hsmt">{LANG.hsmt}</a></li>
                                            <!-- BEGIN: menu_chi_tiet_ho_so -->
                                            <li><a class="bidding__link" href="#cths">{LANG.chi_tiet_ho_so}</a></li>
                                            <!-- END: menu_chi_tiet_ho_so -->
                                            <!-- BEGIN: menu_nha_thau_tu_van -->
                                            <li><a class="bidding__link" href="#tu_van">{LANG.consultants_list}</a></li>
                                            <!-- END: menu_nha_thau_tu_van -->
                                            <!-- BEGIN: menu_gia_han -->
                                            <li><a class="bidding__link" href="#gia_han">{LANG.renew_title}</a></li>
                                            <!-- END: menu_gia_han -->
                                            <!-- BEGIN: menu_huy -->
                                            <li><a class="bidding__link" href="#huy">{LANG.title_cancel}</a></li>
                                            <!-- END: menu_huy -->
                                            <!-- BEGIN: menu_lam_ro_hsmt -->
                                            <li><a class="bidding__link" href="#lam_ro_hsmt">{LANG.clarify_title}</a></li>
                                            <!-- END: menu_lam_ro_hsmt -->
                                            <!-- BEGIN: menu_goods -->
                                            <li><a class="bidding__link" href="#dshs">{LANG.title_goods}</a></li>
                                            <!-- END: menu_goods -->
                                            <!-- BEGIN: menu_subdivision -->
                                            <li><a class="bidding__link" href="#ttpl">{LANG.title_subdivision}</a></li>
                                            <!-- END: menu_subdivision -->
                                            <!-- BEGIN: menu_static -->
                                            <li><a class="bidding__link" href="#static">{LANG.ptnt}</a></li>
                                            <!-- END: menu_static -->
                                            <li><a class="bidding__link" href="#tienich">{LANG.tienich}</a></li>
                                            <li><a class="bidding__link" href="#biddingcosts">{LANG.bidding_costs}</a></li>
                                            <li><a class="bidding__link" href="#support">{LANG.support_and_reporting}</a></li>
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>
                        <span class="btn btn__list_ol"> <i class="fa fa-list-ol" aria-hidden="true"></i>
                        </span>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="hinh_thuc_thong_bao">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div id="table_html" class="modal-body">
                        </div>
                    </div>
                </div>
            </div>
            <div class="bidding-detail-item col-four">
                <!-- BEGIN: hinh_thuc_thong_bao -->
                <div>
                    <div class="c-tit">{LANG.status_bid}</div>
                    <div class="c-val">
                        <div class="{data.class_bnt}" onclick="show_httbModal()">{data.class_icon} {DATA.hinh_thuc_thong_bao}</div>
                        <!-- BEGIN: reason -->
                        <br />
                        <div id="table_html_content" class="hidden">
                            <table class="table table-bordered">
                                <!-- BEGIN: loop -->
                                <tr>
                                    <th class="c-tit">{REASON_LANG}</th>
                                    <td>{REASON}</td>
                                </tr>
                                <!-- END: loop -->
                            </table>
                        </div>
                        <form name="attFile" method="post" action="http://muasamcong.mpi.gov.vn:8081/webentry/downloadFile">
                            <input type="hidden" name="urlFile" value="{DINH_KEM_QUYET_DINH_FILE}">
                        </form>

                        <script type="text/javascript">
                        function getAttachFile(){
                            var contextPath = "http://muasamcong.mpi.gov.vn:8081/webentry"
                            document.attFile.method="post";
                            document.attFile.action= contextPath+"/downloadFile";
                            document.attFile.submit();
                            return;
                        }
                    </script>
                        <!-- END: reason -->
                    </div>
                </div>
                <!-- END: hinh_thuc_thong_bao -->
                <!-- BEGIN: linh_vuc_thong_bao -->
                <div>
                    <div class="c-tit">{LANG.linh_vuc_thong_bao} <i class="fa fa-info-circle ms-1 text-primary" data-toggle="tooltip" data-placement="right" title="{LANG.linh_vuc_thong_bao_note}"></i>
                    </div>
                    <div class="c-val">{DATA.linh_vuc_thong_bao}</div>
                </div>
                <!-- END: linh_vuc_thong_bao -->
                <!-- BEGIN: linh_vuc_thong_bao_old -->
                <div>
                    <div class="c-tit">{LANG.linh_vuc_thong_bao_2}</div>
                    <div class="c-val">{LANG.du_an_dau_tu}</div> <!-- Công cụ tra cứu đang fix cứng như vậy -->
                </div>
                <!-- END: linh_vuc_thong_bao_old -->

            </div>

            <!-- BEGIN: loai_thong_bao -->
            <div class="bidding-detail-item">
                <div class="c-tit xcc">{LANG.loai_thong_bao}</div>
                <div class="c-val">{DATA.loai_thong_bao}</div>
            </div>
            <!-- END: loai_thong_bao -->

            <!-- BEGIN: ten_du_an -->
            <div class="bidding-detail-item">
                <div class="c-tit xcc">{LANG.ten_du_an}</div>
                <div class="c-val">{DATA.ten_du_an}</div>
            </div>
            <!-- END: ten_du_an -->

            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.goi_thau}</div>
                <div class="c-val wrap__text">{DATA.goi_thau}</div>
            </div>

            <!-- BEGIN: don_vi_cong_bo_du_an -->
            <div class="bidding-detail-item">
                <div class="c-tit xcc">{LANG.don_vi}</div>
                <div class="c-val">{DATA.don_vi_cong_bo_du_an}</div>
            </div>
            <!-- END: don_vi_cong_bo_du_an -->

            <!-- BEGIN: co_quan_tham_quyen -->
            <div class="bidding-detail-item">
                <div class="c-tit xcc">{LANG.agency_name}</div>
                <div class="c-val">{DATA.co_quan_tham_quyen}</div>
            </div>
            <!-- END: co_quan_tham_quyen -->

            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.ben_moi_thau}</div>
                <div class="c-val wrap__text">
                    <!-- BEGIN: solicitor -->
                    <a href="{DATA.link_solicitor}" title="{DATA.ben_moi_thau}">{DATA.ben_moi_thau}</a>
                    <!-- END: solicitor -->
                    <!-- BEGIN: solicitor_unlink -->
                    <a href="{DATA.link_solicitor_unlink}">{DATA.ben_moi_thau}</a>
                    <!-- END: solicitor_unlink -->
                    <!-- BEGIN: solicitor_nolink -->
                    <strong>{DATA.ben_moi_thau}</strong>
                    <!-- END: solicitor_nolink -->
                </div>
            </div>


            <!-- BEGIN: chu_dau_tu -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.chu_dau_tu}</div>
                <div class="c-val wrap__text">
                    <!-- BEGIN: investor -->
                    <a href="{DATA.link_investor}" title="{DATA.chu_dau_tu}">{DATA.chu_dau_tu}</a>
                    <!-- END: investor -->
                    <!-- BEGIN: investor_nolink -->
                    <strong>{DATA.chu_dau_tu}</strong>
                    <!-- END: investor_nolink -->
                </div>
            </div>
            <!-- END: chu_dau_tu -->

            <div class="bidding-detail-item col-four">
                <!-- BEGIN: khlcnt_code -->
                <div>
                    <div class="c-tit">{LANG.num_plan}</div>
                    <div class="c-val link-icon">
                        <a href="{DATA.link_khlcnt}">{DATA.khlcnt_code}</a>
                    </div>
                </div>
                <!-- END: khlcnt_code -->
            </div>

            <!-- BEGIN: khlcnt_title -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.name_plan}</div>
                <div class="c-val">{DATA.khlcnt_title}</div>
            </div>
            <!-- END: khlcnt_title -->

            <div class="bidding-detail-item col-four">
                <!-- BEGIN: phan_loai -->
                <div>
                    <div class="c-tit">{LANG.phan_loai}</div>
                    <div class="c-val">{DATA.phan_loai}</div>
                </div>
                <!-- END: phan_loai -->
                <!-- BEGIN: is_domestic -->
                <div>
                    <div class="c-tit">{LANG.is_domestic}</div>
                    <div class="c-val">{DATA.is_domestic}</div>
                </div>
                <!-- END: is_domestic -->
            </div>

            <!-- BEGIN: nguon_von -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.nguon_von}</div>
                <div class="c-val">{DATA.nguon_von}</div>
            </div>
            <!-- END: nguon_von -->

            <!-- BEGIN: loai_nguon_von -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.pham_vi}</div>
                <div class="c-val">{DATA.pham_vi}</div>
            </div>
            <!-- END: loai_nguon_von -->

            <!-- BEGIN: phuong_thuc -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.phuong_thuc}</div>
                <div class="c-val">{DATA.phuong_thuc}</div>
            </div>
            <!-- END: phuong_thuc -->

            <!-- BEGIN: phuong_thuc_hop_dong -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.phuong_thuc_hop_dong}</div>
                <div class="c-val">{DATA.phuong_thuc_hop_dong}</div>
            </div>
            <!-- END: phuong_thuc_hop_dong -->

            <!-- BEGIN: loai_hop_dong -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.loai_hop_dong}</div>
                <div class="c-val">{DATA.loai_hop_dong}</div>
            </div>
            <!-- END: loai_hop_dong -->

            <!-- BEGIN: thoi_gian_thuc_hien -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.thoi_gian_thuc_hien}</div>
                <div class="c-val" id="thoi_gian_thuc_hien" data-typesearch="{TYPESEARCH}">
                    <!-- BEGIN: link -->
                    {view_adv_point} {LINK_USER}
                    <!-- END: link -->
                    <!-- BEGIN: data -->
                    {DATA.thoi_gian_thuc_hien}
                    <!-- END: data -->
                </div>
            </div>
            <!-- END: thoi_gian_thuc_hien -->

            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.hinh_thuc_lua_chon}</div>
                <div class="c-val">{DATA.hinh_thuc_lua_chon}</div>
            </div>

            <!-- BEGIN: dia_diem_thuc_hien_goi_thau -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.dia_diem_thuc_hien_goi_thau}</div>
                <div class="c-val">
                    {DATA.dia_diem_thuc_hien_goi_thau}
                </div>
            </div>
            <!-- END: dia_diem_thuc_hien_goi_thau -->
            <!-- BEGIN: dien_tich_dat -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.dien_tich_dat}</div>
                <div class="c-val">{DATA.dien_tich_dat}</div>
            </div>
            <!-- END: dien_tich_dat -->
            <!-- Thông báo liên quan dùng cho trường hợp link giữa bài đăng lần đầu và đăng lại -->
            <!-- BEGIN: thong_bao_lien_quan -->
            <!-- BEGIN: yes_relate -->

            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.thong_bao_lien_quan}</div>
                <div class="c-val link-icon">
                    <!-- BEGIN: loop -->
                    <p>
                        <label><input type="checkbox" {RELATE.checked} value="{RELATE.id_tbmt}" class="chk_notification"> {RELATE.ngay_dang_tai} {RELATE.title_checked}</label> - <a href="{RELATE.link_relate}">{RELATE.title_tb}</a> {RELATE.title_tb_first} <span class="info_change_tb" data-tb='{RELATE.key}'>{RELATE.text_changetb}</span> <span class="link_change_a">(<a href="javascript:void(0)" data-id="{RELATE.data_id_tbmt}" data-toggle="modal" data-target="#showTB">{LANG.title_change_tb}</a>)
                        </span>
                    </p>
                    <!-- END: loop -->
                    <span data-toggle="tooltip" data-placement="right" title="{LANG.tooltip_tb}">
                        <button class="btn btn-primary btn-xs" id="view_change_tb" data-toggle="modal" data-target="#showTB">{LANG.title_change_tb}</button>
                        <button class="btn btn-primary btn-xs" id="view_change_tb1">{LANG.title_change_tb}</button>

                    </span> <label class="label label-warning">{NO_RELATE}</label>
                </div>
            </div>

            <!-- Modal -->
            <div id="showTB" class="modal fade" role="dialog">
                <div class="modal-dialog modal-lg">
                    <!-- Modal content-->
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                            <h4 class="modal-title">{LANG.title_sstbmt}</h4>
                        </div>
                        <div class="modal-body">
                            <div id="main_change_notificaion">
                                <div class="change_notificaion">
                                    <div class="row">
                                        <div class="col-md-24">
                                            <label>{LANG.kieusosanh}: </label> <label><input type="radio" name="sosanhtb" checked value="1">&nbsp;{LANG.title_muti}</label> <label><input type="radio" name="sosanhtb" value="2">&nbsp;{LANG.title_one}</label>
                                        </div>
                                        <div class="col-md-24">
                                            <div class="card">
                                                <label><input type="checkbox" class="change_by_line" value="3">&nbsp;{LANG.title_linebyline}</label>
                                                <div class="row_compare">
                                                    <div class="col">
                                                        <div class="card" id="outputOriginal"></div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="card" id="output"></div>
                                                    </div>
                                                    <div class="col" id="new_version">
                                                        <div class="card" id="outputNew"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">{LANG.close}</button>
                        </div>
                    </div>

                </div>
            </div>

            <!-- END: yes_relate -->
            <!-- END: thong_bao_lien_quan -->

            <!-- BEGIN: thoi_diem_dong_thau -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.close_time}</div>
                <div class="c-val" id="den_ngay">
                    <!-- BEGIN: link -->
                    {view_adv_point} {LINK_USER}
                    <!-- END: link -->
                    <!-- BEGIN: data -->
                    {DATA.den_ngay}
                    <!-- END: data -->
                </div>
            </div>
            <!-- END: thoi_diem_dong_thau -->
            <!-- BEGIN: time_ehsdt -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.time_ehsdt}</div>
                <div class="c-val">{DATA.time_ehsdt}</div>
            </div>
            <!-- END: time_ehsdt -->

            <!-- BEGIN: phan_muc -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.phan_muc} <i class="fa fa-info-circle ms-1 text-primary" data-toggle="tooltip" data-placement="right" title="{LANG.phan_muc_note}"></i>
                </div>
                <div class="c-val">{DATA.phan_muc}</div>
            </div>
            <!-- END: phan_muc -->

            <!-- BEGIN: vsic -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.vsic} <i class="fa fa-info-circle ms-1 text-primary" data-toggle="tooltip" data-placement="right" title="{LANG.vsic_note}"></i>
                </div>
                <div class="c-val">{DATA.vsic}</div>
            </div>
            <!-- END: vsic -->

            <!-- BEGIN: so_quyet_dinh -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.so_quyet_dinh}</div>
                <div class="c-val">{DATA.so_quyet_dinh}</div>
            </div>
            <!-- END: so_quyet_dinh -->

            <!-- BEGIN: ngay_quyet_dinh -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.ngay_quyet_dinh}</div>
                <div class="c-val">{DATA.ngay_quyet_dinh_format}</div>
            </div>
            <!-- END: ngay_quyet_dinh -->

            <!-- BEGIN: co_quan_quyet_dinh -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.co_quan_quyet_dinh}</div>
                <div class="c-val">{DATA.co_quan_quyet_dinh}</div>
            </div>
            <!-- END: co_quan_quyet_dinh -->

            <!-- BEGIN: file_quyet_dinh_phe_duyet -->
            <div class="bidding-detail-item flex-direction-column">
                <div class="c-tit">{LANG.quyet_dinh_phe_duyet}</div>
                <div class="c-val">
                    <div><span class="list-group-item">{DATA.quyet_dinh_phe_duyet}</span></div>
                </div>
            </div>
            <!-- END: file_quyet_dinh_phe_duyet -->

            <!-- BEGIN: quyet_dinh_phe_duyet -->
            <div class="bidding-detail-item flex-direction-column">
                <div class="c-tit">{LANG.quyet_dinh_phe_duyet}</div>
                <div class="c-val">
                    <p id="tai_ho_so">{DOWNLOAD_MESS}</p>
                    <!-- BEGIN: nv_not_by -->
                    <div class="container-download">
                        <div class="column-download">
                            <div class="button-container">
                                <div class="center-buttons" id="tai_ho_so">
                                    <button class="btn btn-primary" onclick="click_view_detail_adv(this)" data-check="true" data-id="{DATA.id}" data-confirm="{LANG.noti_point}">{LANG.check}</button>
                                </div>
                                <p>{nv_not_by}</p>
                            </div>
                        </div>
                    </div>
                    <!-- END: nv_not_by -->
                    <!-- BEGIN: point_or_t0 -->
                    <div class="container-download">
                        <div class="column-download">
                            <div class="button-container">
                                <div class="center-buttons">
                                    <button class="btn btn-primary" onclick="buy_fastlink(this)" data-id="{DATA.id}" data-confirm="{LANG.down_point_confirm}">{LANG.link_file_fast}</button>
                                </div>
                                <p>{info_T0}</p>
                            </div>
                        </div>
                        <div class="column-download">
                            <div class="button-container">
                                <div class="center-buttons">
                                    <button class="btn btn-primary" onclick="redirect_link('{link_T0}')">{LANG.buy_TO}</button>
                                </div>
                                <p>{LANG.show_info_down_t0_2227}</p>
                            </div>
                        </div>
                    </div>
                    <!-- END: point_or_t0 -->
                    <!-- BEGIN: vip_size_info -->
                    <p>
                        <em class="fa fa-bell"></em> {VIPSIZE_MESS}
                    </p>
                    <!-- END: vip_size_info -->
                    <div class="tab-content download {ICON_PLANE}">
                        <div class="tab-pane fade{HOME_ACTIVE} in list-group download-link is_points" id="quyet_dinh_nav_first" role="tabpanel" aria-labelledby="quyet_dinh_nav_first_tab">
                            <div class="list-group-item display-flex {DOWNLOAD_NORMAL}">
                                {QUYET_DINH_NORMAL}
                                <div class="text-nowrap" id="quyet_dinh_link" style="margin-left:auto"></div>
                            </div>
                        </div>

                        <!-- BEGIN: is_point_down_show2 -->
                        <div id="myDiv" class="tab-pane fade{POINT_ACTIVE} in list-group download-link is_points{IS_OTHER_BROWSER}" id="quyet_dinh_nav_second" role="tabpanel" aria-labelledby="quyet_dinh_nav_second_tab">{QUYET_DINH}</div>
                        <!-- END: is_point_down_show2 -->
                    </div>
                    <!-- BEGIN: if_ie_down -->
                    <small><i class="fa fa-paper-plane-o"></i> {note_ie_down}</small>
                    <br>
                    <small><i class="fa fa-exclamation-circle"></i> {LANG.note_ehsmt} </small>
                    <!-- END: if_ie_down -->
                </div>
            </div>
            <!-- END: quyet_dinh_phe_duyet -->
        </div>
    </div>
    <!-- BEGIN: noi_dung -->
    <div id="nd" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.noi_dung}</h2>
        <div class="bidding-detail">
            <div class="bidding-detail-item">
                <div class="c-val">{DATA.noi_dung}</div>
            </div>
        </div>
    </div>
    <!-- END: noi_dung -->
    <!-- Tham gia dự thầu-->
    <!-- BEGIN: du_thau -->
    <div id="tdt" class="scoll__menu">
        <h2 class="bidding-sub-title">
            {LANG.bid_join}
        </h2>
        <div class="bidding-detail">
            <!-- Một trong hai loại dưới đây sẽ được hiển thị -->
            <!-- Loại 1: dùng cho đấu thầu điện tử -->
            <!-- BEGIN: dau_thau_dien_tu -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.hinh_thuc_nhan_hs}</div>
                <div class="c-val"><span class="online-code">{DATA.hinh_thuc_nhan_hs}</span></div>
            </div>
            <div class="bidding-detail-item col-four">
                <!-- BEGIN: thoi_gian_nhan_hsdt_tu -->
                <div>
                    <div class="c-tit">{LANG.thoi_gian_nhan_hsdt_tu}</div>
                    <div class="c-val">{DATA.thoi_gian_nhan_hsdt_tu}</div>
                </div>
                <!-- END: thoi_gian_nhan_hsdt_tu -->
                <div>
                    <div class="c-tit">{LANG.time_dong_thau}</div>
                    <div class="c-val">{DATA.den_ngay}</div>
                </div>
            </div>

            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.chi_phi_ehsdt}</div>
                <div class="c-val">{DATA.gia_ban}</div>
            </div>
            <!-- BEGIN: mua_ho_so_moi_thau -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.mua_ho_so_moi_thau}</div>
                <div class="c-val">{DATA.mua_ho_so_moi_thau}</div>
            </div>
            <!-- END: mua_ho_so_moi_thau -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.dia_diem_nhan_hsdt}</div>
                <div class="c-val" id="dia_diem_nhan_hsdt" data-typesearch="{TYPESEARCH}">
                    <!-- BEGIN: link -->
                    {view_adv_point} {LINK_USER}
                    <!-- END: link -->
                    <!-- BEGIN: data -->
                    <span class="link-icon">{DATA.dia_diem_nhan_hsdt}</span>
                    <!-- END: data -->
                </div>
            </div>
            <!-- END: dau_thau_dien_tu -->

            <!-- Loại 2: dùng cho đấu thầu trực tiếp -->
            <!-- Một trong hai trường sẽ được hiển thị: hsmt hoặc hsyc -->
            <!-- BEGIN: dau_thau_truc_tiep -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.hinh_thuc_nhan_hs}</div>
                <div class="c-val">
                    <span class="direct-code">{DATA.hinh_thuc_nhan_hs}</span>
                    <!-- BEGIN: violate_warning -->
                    <i id="onlrule-violate" class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                    <!-- END: violate_warning -->
                </div>
            </div>
            <!-- BEGIN: thoi_gian_ban_hsmt_tu -->
            <div class="bidding-detail-item col-four">
                <div>
                    <div class="c-tit">{LANG.thoi_gian_ban_hsmt_tu}</div>
                    <div class="c-val" id="thoi_gian_ban_hsmt_tu" data-typesearch="{TYPESEARCH}">
                        <!-- BEGIN: link -->
                        {view_adv_point} {LINK_USER}
                        <!-- END: link -->
                        <!-- BEGIN: data -->
                        {DATA.thoi_gian_ban_hsmt_tu}
                        <!-- END: data -->
                    </div>
                </div>
                <div>
                    <div class="c-tit">{LANG.time_dong_thau}</div>
                    <div class="c-val" id="den_ngay" data-typesearch="{TYPESEARCH}">
                        <!-- BEGIN: denngay_link -->
                        {view_adv_point} {LINK_USER}
                        <!-- END: denngay_link -->
                        <!-- BEGIN: denngay_data -->
                        {DATA.den_ngay}
                        <!-- END: denngay_data -->
                    </div>
                </div>
            </div>
            <!-- END: thoi_gian_ban_hsmt_tu -->
            <!-- BEGIN: thoi_gian_ban_hsyc_tu -->
            <div class="bidding-detail-item col-four">
                <div>
                    <div class="c-tit">{LANG.thoi_gian_ban_hsyc_tu}</div>
                    <div class="c-val" id="thoi_gian_ban_hsyc_tu" data-typesearch="{TYPESEARCH}">
                        <!-- BEGIN: link -->
                        {view_adv_point} {LINK_USER}
                        <!-- END: link -->
                        <!-- BEGIN: data -->
                        {DATA.thoi_gian_ban_hsyc_tu}
                        <!-- END: data -->
                    </div>
                </div>
                <div>
                    <div class="c-tit">{LANG.time_dong_thau}</div>
                    <div class="c-val" id="den_ngay" data-typesearch="{TYPESEARCH}">
                        <!-- BEGIN: denngay_link -->
                        {view_adv_point} {LINK_USER}
                        <!-- END: denngay_link -->
                        <!-- BEGIN: denngay_data -->
                        {DATA.den_ngay}
                        <!-- END: denngay_data -->
                    </div>
                </div>
            </div>
            <!-- END: thoi_gian_ban_hsyc_tu -->
            <!-- BEGIN: dia_diem -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.dia_diem}</div>
                <div class="c-val" id="dia_diem_nhan_hsdt" data-typesearch="{TYPESEARCH}">
                    <!-- BEGIN: data -->
                    {DATA.dia_diem_nhan_hsdt}
                    <!-- END: data -->
                    <!-- BEGIN: link -->
                    {view_adv_point} {LINK_USER}
                    <!-- END: link -->
                </div>
            </div>
            <!-- BEGIN: dia_diem_phat_hanh_hsmt -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.dia_diem_phat_hanh_hsmt}</div>
                <div class="c-val">{DATA.dia_diem_phat_hanh_hsmt}</div>
            </div>
            <!-- END: dia_diem_phat_hanh_hsmt -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.gia_ban}</div>
                <div class="c-val" id="gia_ban" data-typesearch="{TYPESEARCH}">
                    <!-- BEGIN: data_giaban -->
                    {DATA.gia_ban}
                    <!-- END: data_giaban -->
                    <!-- BEGIN: link_giaban -->
                    {view_adv_point} {LINK_USER}
                    <!-- END: link_giaban -->
                </div>
            </div>
            <!-- END: dia_diem -->
            <!-- END: dau_thau_truc_tiep -->
            <!-- BEGIN: loai_cong_trinh -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.search_lct}</div>
                <div class="c-val">{DATA.work_type}</div>
            </div>
            <!-- END: loai_cong_trinh -->
        </div>
    </div>
    <!-- END: du_thau -->

    <!-- BEGIN: chao_gia -->
    <div id="cgtt" class="scoll__menu">
        <h2 class="bidding-sub-title">
            {LANG.price_quotation}
        </h2>
        <div class="bidding-detail">
            <div class="bidding-detail-item col-four">
                <div>
                    <div class="c-tit">{LANG.price_quotation_start}</div>
                    <div class="c-val">
                        <span>{DATA.thoi_diem_mo_thau}</span>
                    </div>
                </div>
                <div>
                    <div class="c-tit">{LANG.price_quotation_end}</div>
                    <div class="c-val">
                        <span>{DATA.den_ngay}</span>
                    </div>
                </div>
            </div>
            <!-- BEGIN: no_subdivision -->
            <div class="bidding-detail-item col-four">
                <div>
                    <div class="c-tit">{LANG.price_init}</div>
                    <div class="c-val">
                        <span>{DATA.gia_tran}</span>
                    </div>
                </div>
                <div>
                    <div class="c-tit">{LANG.price_step}</div>
                    <div class="c-val">
                        <span>{DATA.buoc_gia}</span>
                    </div>
                </div>
            </div>
            <!-- END: no_subdivision -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.time_ehsdt}</div>
                <div class="c-val">{DATA.time_ehsdt}</div>
            </div>
        </div>
    </div>
    <!-- END: chao_gia -->

    <!-- Mở thầu -->
    <!-- BEGIN: bid_openning -->
    <div id="mt" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.bid_openning}</h2>
        <div class="bidding-detail">
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.thoi_diem_mo_thau}</div>
                <div class="c-val">{DATA.thoi_diem_mo_thau}</div>
            </div>
            <!-- BEGIN: dia_diem_mo_thau -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.dia_diem_mo_thau}</div>
                <div class="c-val" id="dia_diem_mo_thau" data-typesearch="{TYPESEARCH}">
                    <!-- BEGIN: data -->
                    <span class=link-icon>{DATA.dia_diem_mo_thau}</span>
                    <!-- END: data -->
                    <!-- BEGIN: link -->
                    {view_adv_point} {LINK_USER}
                    <!-- END: link -->
                </div>
            </div>
            <!-- END: dia_diem_mo_thau -->
            <!-- BEGIN: gia_goi_thau -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.gia_goi_thau}</div>
                <div class="c-val" id="gia_goi_thau" data-typesearch="{TYPESEARCH}">
                    <!-- BEGIN: data -->
                    {DATA.gia_goi_thau}
                    <!-- END: data -->
                    <!-- BEGIN: link_dutoan -->
                    {LINK_DUTOAN1}
                    <!-- END: link_dutoan -->
                    <!-- BEGIN: link -->
                    {view_adv_point} {LINK_USER}
                    <!-- END: link -->
                </div>
            </div>
            <!-- END: gia_goi_thau -->
            <!-- BEGIN: so_tien_chu_gia_goi -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.so_tien_chu_gia_goi}</div>
                <div class="c-val" id="so_tien_chu_gia_goi" data-typesearch="{TYPESEARCH}">
                    <!-- BEGIN: data -->
                    {DATA.so_tien_chu_gia_goi}
                    <!-- END: data -->
                    <!-- BEGIN: link_dutoan -->
                    {LINK_DUTOAN}
                    <!-- END: link_dutoan -->
                    <!-- BEGIN: link -->
                    {view_adv_point} {LINK_USER}
                    <!-- END: link -->
                </div>
            </div>
            <!-- END: so_tien_chu_gia_goi -->
            <!-- BEGIN: gia_du_toan -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.du_toan_goi_thau}</div>
                <div class="c-val" id="gia_du_toan" data-typesearch="{TYPESEARCH}">
                    <!-- BEGIN: data -->
                    {DATA.gia_du_toan}
                    <!-- END: data -->
                    <!-- BEGIN: link_dutoan -->
                    {LINK_DUTOAN}
                    <!-- END: link_dutoan -->
                    <!-- BEGIN: link -->
                    {view_adv_point} {LINK_USER}
                    <!-- END: link -->
                </div>
            </div>
            <!-- END: gia_du_toan -->
            <!-- BEGIN: so_tien_chu_gia_du_toan -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.so_tien_chu_gia_goi}</div>
                <div class="c-val" id="so_tien_chu_gia_du_toan" data-typesearch="{TYPESEARCH}">
                    <!-- BEGIN: data -->
                    {DATA.so_tien_chu_gia_du_toan}
                    <!-- END: data -->
                    <!-- BEGIN: link_dutoan -->
                    {LINK_DUTOAN}
                    <!-- END: link_dutoan -->
                    <!-- BEGIN: link -->
                    {view_adv_point} {LINK_USER}
                    <!-- END: link -->
                </div>
            </div>
            <!-- END: so_tien_chu_gia_du_toan -->
            <!-- BEGIN: listopen -->
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.listopen}</div>
                <div class="c-val">{LANG_RESULT_OPEN}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.kq_lcnt}</div>
                <div class="c-val">{LANG_RESULT}</div>
            </div>
            <!-- END: listopen -->
        </div>
    </div>
    <!-- END: bid_openning -->

    <!-- bảo đảm dự thầu -->
    <!-- BEGIN: bid_security -->
    <div id="dbmt" class="scoll__menu">
        <h2 class="bidding-sub-title scoll__menu" id="bddt" height-id="#div_bddt">{LANG.bid_security}</h2>
        <div class="bidding-detail">
            <div class="" id="div_bddt">
                <!-- BEGIN: hinh_thuc_dam_bao -->
                <div class="bidding-detail-item">
                    <div class="c-tit">{LANG.hinh_thuc_dam_bao}</div>
                    <div class="c-val">{DATA.hinh_thuc_dam_bao}</div>
                </div>
                <!-- END: hinh_thuc_dam_bao -->
                <!-- BEGIN: so_tien_dam_bao -->
                <div class="bidding-detail-item">
                    <div class="c-tit">{LANG.so_tien_dam_bao}</div>
                    <div class="c-val">
                        <!-- BEGIN: data1 -->
                        {DATA.so_tien_dam_bao}
                        <!-- END: data1 -->
                        <!-- BEGIN: link1 -->
                        {LINK_USER}
                        <!-- END: link1 -->
                    </div>
                </div>
                <div class="bidding-detail-item">
                    <div class="c-tit">{LANG.so_tien_bang_chu}</div>
                    <div class="c-val">
                        <!-- BEGIN: data -->
                        {DATA.so_tien_bang_chu}
                        <!-- END: data -->
                        <!-- BEGIN: link -->
                        {LINK_USER}
                        <!-- END: link -->
                    </div>
                </div>
                <!-- END: so_tien_dam_bao -->
                <!-- BEGIN: thoi_han_dam_bao -->
                <div class="bidding-detail-item">
                    <div class="c-tit">{LANG.thoi_han_dam_bao}</div>
                    <div class="c-val">
                        {DATA.thoi_han_dam_bao}
                    </div>
                </div>
                <!-- END: thoi_han_dam_bao -->
            </div>
        </div>
    </div>
    <!-- END: bid_security -->

    <!-- BEGIN: ho_so_mt -->
    <div id="hsmt" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.ho_so}</h2>
        <div class="bidding-detail">
            <!-- BEGIN: ho_so -->
            <div class="bidding-detail-item flex-direction-column">
                <div class="c-val">
                    <p>{DOWNLOAD_MESS}</p>
                    <!-- BEGIN: nv_not_by -->
                    <div class="container-download">
                        <div class="column-download">
                            <div class="button-container">
                                <div class="center-buttons" id="tai_ho_so">
                                    <button class="btn btn-primary" onclick="click_view_detail_adv(this)" data-id="{DATA.id}" data-confirm="{LANG.noti_point}">{LANG.check}</button>
                                </div>
                                <p>{nv_not_by}</p>
                            </div>
                        </div>
                    </div>
                    <!-- END: nv_not_by -->
                    <!-- BEGIN: point_or_t0 -->
                    <div class="container-download">
                        <div class="column-download">
                            <div class="button-container">
                                <div class="center-buttons">
                                    <button class="btn btn-primary" onclick="buy_fastlink(this)" data-id="{DATA.id}" data-confirm="{LANG.down_point_confirm}">{LANG.link_file_fast}</button>
                                </div>
                                <p>{info_T0}</p>
                            </div>
                        </div>
                        <div class="column-download">
                            <div class="button-container">
                                <div class="center-buttons">
                                    <button class="btn btn-primary" onclick="redirect_link('{link_T0}')">{LANG.buy_TO}</button>
                                </div>
                                <p>{LANG.show_info_down_t0_2227}</p>
                            </div>
                        </div>
                    </div>
                    <!-- END: point_or_t0 -->
                    <!-- BEGIN: vip_size_info -->
                    <p>
                        <em class="fa fa-bell"></em> {VIPSIZE_MESS}
                    </p>
                    <!-- END: vip_size_info -->
                    <div class="tab-content download">
                        <div class="tab-pane fade{HOME_ACTIVE} in" id="ho_so_nav_first" role="tabpanel" aria-labelledby="ho_so_nav_first_tab">
                            <div class="no-hover list-group download-link{IS_OTHER_BROWSER}">
                                {HO_SO_NORMAL}
                            </div>
                        </div>
                        <!-- BEGIN: is_point_down_show2 -->
                        <div id="myDiv" class="tab-pane fade{POINT_ACTIVE} in list-group download-link is_points{IS_OTHER_BROWSER}" id="ho_so_nav_second" role="tabpanel" aria-labelledby="ho_so_nav_second_tab">
                            {HO_SO}
                            <!--<div class="display-flex"><div class="text-nowrap" style="margin-left:auto"><a href="{DATA.link_msc}" class="btn btn-primary btn-sm" id="lastLink">{LANG.download_all_hsmt}</a></div></div>-->
                        </div>
                        <!-- END: is_point_down_show2 -->
                    </div>
                    <!-- BEGIN: if_ie_down -->
                    <small><i class="fa fa-paper-plane-o"></i> {note_ie_down}</small>
                    <br>
                    <small><i class="fa fa-exclamation-circle"></i> {LANG.note_ehsmt} </small>
                    <!-- END: if_ie_down -->
                </div>
            </div>
            <!-- END: ho_so -->
            <!-- BEGIN: ho_so_project -->
            <div class="bidding-detail-item flex-direction-column">
                <div class="c-tit">{LANG.ho_so}</div>
                <div class="c-val">
                    {HO_SO_NORMAL} <small><i class="fa fa-internet-explorer"></i> {LANG.note_ie}</small>
                </div>
            </div>
            <!-- END: ho_so_project -->
        </div>
    </div>
    <!-- END: ho_so_mt -->

    <!-- BEGIN: msc_search_old_tbmdt_list_files -->
    <div class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.ho_so}</h2>
        <div class="bidding-detail">

            <div class="bidding-detail-item flex-direction-column">
                <div class="c-tit">{LANG.ho_so}</div>
                <div class="c-val">
                    <!-- BEGIN: file_item -->
                    <a class="list-group-item" href="{FILE_LINK}"><i class="fa fa-paperclip" aria-hidden="true"></i> {FILE_ITEM_NAME}</a>
                    <!-- END: file_item -->
                </div>
            </div>

        </div>
    </div>
    <!-- END: msc_search_old_tbmdt_list_files -->

    <!-- BEGIN: msc_search_old_tbmdt_clarify_list -->
    <div class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.clarify_title}</h2>
        <!-- BEGIN: cla_item -->
        <div class="bidding-detail">
            <div class="bidding-detail-item flex-direction-column">
                <div class="c-tit">{LANG.clarify_question}</div>
                <div class="c-val">
                    {CLA_ITEM.question}
                </div>
            </div>
            <div class="bidding-detail-item flex-direction-column">
                <div class="c-tit">{LANG.clarify_response}</div>
                <div class="c-val">
                    {CLA_ITEM.answer}
                </div>
            </div>
            <div class="bidding-detail-item flex-direction-column">
                <div class="c-tit">{LANG.clarify_sign_res_date}</div>
                <div class="c-val">
                    {CLA_ITEM.createDate}
                </div>
            </div>
            <div class="bidding-detail-item flex-direction-column">
                <div class="c-tit">{LANG.clarify_res_file_name}</div>
                <div class="c-val">
                    <a class="list-group-item" href="{FILE_LINK}"><i class="fa fa-paperclip" aria-hidden="true"></i> {FILE_ITEM_NAME}</a>
                </div>
            </div>
        </div>
        <!-- END: cla_item -->
    </div>
    <!-- END: msc_search_old_tbmdt_clarify_list -->

    <!-- BEGIN: nha_thau_tu_van -->
    <div id="tu_van" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.consultants_list}</h2>
        <div class="bidding-detail">
            <table class="table table-striped table-bordered bidding-table">
                <thead>
                    <tr>
                        <th style="width: 1%;">{LANG.STT}</th>
                        <th style="width: 30%;">{LANG.partnership}</th>
                        <th>{LANG.companyname}</th>
                        <th style="width: 30%;">{LANG.address}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop -->
                    <tr>
                        <td class="text-center" data-column="{LANG.STT}">{CONSULTANT.stt}</td>
                        <td data-column="{LANG.partnership}">{CONSULTANT.role}</td>
                        <td data-column="{LANG.companyname}"><!-- BEGIN: link --><a href="{CONSULTANT.link}">{CONSULTANT.companyname}</a><!-- END: link --><!-- BEGIN: no_link -->{CONSULTANT.companyname}<!-- END: no_link --></td>
                        <td data-column="{LANG.address}">{CONSULTANT.address}</td>
                    </tr>
                    <!-- END: loop -->
            </table>
        </div>
    </div>
    <!-- END: nha_thau_tu_van -->

    <!-- BEGIN: gia_han -->
    <div id="gia_han" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.renew_title}</h2>
        <div class="bidding-detail">
            <table class="table table-striped table-bordered bidding-table">
                <thead>
                    <tr>
                        <th>{LANG.STT}</th>
                        <th>{LANG.time_renew}</th>
                        <th>{LANG.time_close_old}</th>
                        <th>{LANG.time_close_new}</th>
                        <th>{LANG.time_open_old}</th>
                        <th>{LANG.time_open_new}</th>
                        <th>{LANG.reason_title}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop -->
                    <tr>
                        <td data-column="{LANG.STT}">{DELAY.stt}</td>
                        <td data-column="{LANG.time_renew}">{DELAY.time_renew}</td>
                        <td data-column="{LANG.time_close_old}">{DELAY.time_close_old}</td>
                        <td data-column="{LANG.time_close_new}">{DELAY.time_close_new}</td>
                        <td data-column="{LANG.time_open_old}">{DELAY.time_open_old}</td>
                        <td data-column="{LANG.time_open_new}">{DELAY.time_open_new}</td>
                        <td data-column="{LANG.reason_title}">{DELAY.reason}</td>
                    </tr>
                    <!-- END: loop -->
            </table>
        </div>
    </div>
    <!-- END: gia_han -->

    <!-- BEGIN: huy -->
    <div id="dbmt" class="scoll__menu">
        <h2 class="bidding-sub-title scoll__menu" id="huy">{LANG.title_cancel}</h2>
        <div class="bidding-detail">
            <div>
                <div class="bidding-detail-item">
                    <div class="c-tit">{LANG.reason_cancel}</div>
                    <div class="c-val">{CANCEL.reason_cancel}</div>
                </div>
            </div>
            <div>
                <div class="bidding-detail-item">
                    <div class="c-tit">{LANG.cancel_no}</div>
                    <div class="c-val">{CANCEL.cancel_no}</div>
                </div>
            </div>
            <div>
                <div class="bidding-detail-item">
                    <div class="c-tit">{LANG.cancel_date_no}</div>
                    <div class="c-val">{CANCEL.cancel_date_no}</div>
                </div>
            </div>
            <!-- BEGIN: gen_cancel_file -->
                {CANCEL.gen_cancel_file}
            <!-- END: gen_cancel_file -->
            <div>
                <div class="bidding-detail-item">
                    <div class="c-tit">{LANG.cancel_date}</div>
                    <div class="c-val">{CANCEL.cancel_date}</div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: huy -->

    <!-- BEGIN: lam_ro_hsmt_moi -->
    <div id="lam_ro_hsmt" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.clarify_title}</h2>
        <div class="bidding-detail" style="max-height:450px; overflow: auto;">
            <!-- BEGIN: gen_clarify_file -->
            {GEN_CLARIFY_FILE}
            <!-- END: gen_clarify_file -->            
        </div>
    </div>
    <!-- END: lam_ro_hsmt_moi -->
    <!-- BEGIN: lam_ro_hsmt_cu -->
    <div id="lam_ro_hsmt" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.clarify_title}</h2>
        <div class="bidding-detail" style="max-height:450px; overflow: auto;">
            <!-- BEGIN: loop -->
            <div>
                <div>
                    <div>
                        <table class="table table-striped table-bordered ">
                            <tbody>
                                <tr>
                                    <th>{LANG.clarify_question}</th>
                                    <td>{CLARIFY.clarify_req_content}</td>
                                </tr>
                                <tr>
                                    <th>{LANG.clarify_file_name}</th>
                                    <td>{CLARIFY.clarify_file_name}</td>
                                </tr>
                                <tr>
                                    <th>{LANG.clarify_response}</th>
                                    <td>{CLARIFY.clarify_res_content}</td>
                                </tr>
                                <tr>
                                    <th>{LANG.clarify_res_file_name}</th>
                                    <td>{CLARIFY.clarify_res_file_name}</td>
                                </tr>
                                <tr>
                                    <th>{LANG.clarify_sign_res_date}</th>
                                    <td>{CLARIFY.sign_res_date}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <hr>
            </div>
            <!-- END: loop -->
        </div>
    </div>
    <!-- END: lam_ro_hsmt_cu -->

    <!-- BEGIN: yeu_cau_kien_nghi -->
    <div id="yeu_cau_kien_nghi" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.petition_request}</h2>
        <div class="bidding-detail" style="max-height:500px; overflow: auto;">
            <!-- BEGIN: gen_yckn_file -->
            {GEN_YCKN_FILE}
            <!-- END: gen_yckn_file -->
        </div>
    </div>
    <!-- END: yeu_cau_kien_nghi -->

    <!-- BEGIN: chi_tiet_ho_so -->
    <div id="cths" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.chi_tiet_ho_so}</h2>
        <div class="bidding-detail">
            <div class="bidding-detail-item flex-direction-column">
                <div class="c-val">
                    <nav>
                        <ul class="nav nav-tabs download-nav act_dulieu" role="tablist">
                            <!-- BEGIN: bang_du_lieu -->
                            <li class="nav-item"><a class="nav-link" id="bang_du_lieu_tab" onclick="change_my_url('#bang_du_lieu');" data-toggle="tab" href="#bang_du_lieu" role="tab" aria-controls="bang_du_lieu" aria-selected="true">
                                    <h3 class="fw-normal">{LANG.bang_du_lieu}</h3>
                                </a></li>
                            <!-- END: bang_du_lieu -->

                            <!-- BEGIN: pham_vi_cung_cap -->
                            <li class="nav-item"><a class="nav-link" id="phamvicc_tab" onclick="change_my_url('#phamvicc');" data-toggle="tab" href="#phamvicc" role="tab" aria-controls="phamvicc" aria-selected="true" aria-expanded="true">
                                    <h3 class="fw-normal">{LANG.pham_vi_cung_cap}</h3>
                                </a></li>
                            <!-- END: pham_vi_cung_cap -->

                            <!-- BEGIN: dich_vu_lien_quan -->
                            <li class="nav-item"><a class="nav-link" id="dich_vu_lien_quan_tab" onclick="change_my_url('#dich_vu_lien_quan');" data-toggle="tab" href="#dich_vu_lien_quan" role="tab" aria-controls="dich_vu_lien_quan" aria-selected="true">
                                    <h3 class="fw-normal">{LANG.dich_vu_lien_quan}</h3>
                                </a></li>
                            <!-- END: dich_vu_lien_quan -->
                            <!-- BEGIN: tien_do_thuc_hien -->
                            <li class="nav-item"><a class="nav-link" id="tien_do_thuc_hien_tab" onclick="change_my_url('#tien_do_thuc_hien');" data-toggle="tab" href="#tien_do_thuc_hien" role="tab" aria-controls="tien_do_thuc_hien" aria-selected="true">
                                    <h3 class="fw-normal">{LANG.tien_do_thuc_hien}</h3>
                                </a></li>
                            <!-- END: tien_do_thuc_hien -->
                            <!-- BEGIN: tieu_chuan_danh_gia -->
                            <li class="nav-item"><a class="nav-link" id="tieu_chuan_danh_gia_tab" onclick="change_my_url('#tieu_chuan_danh_gia');" data-toggle="tab" href="#tieu_chuan_danh_gia" role="tab" aria-controls="tieu_chuan_danh_gia" aria-selected="true">
                                    <h3 class="fw-normal">{LANG.tieu_chuan_danh_gia}</h3>
                                </a></li>
                            <!-- END: tieu_chuan_danh_gia -->
                            <!-- BEGIN: nhan_su_chu_chot -->
                            <li class="nav-item"><a class="nav-link" id="nhan_su_chu_chot_tab" onclick="change_my_url('#nhan_su_chu_chot');" data-toggle="tab" href="#nhan_su_chu_chot" role="tab" aria-controls="nhan_su_chu_chot" aria-selected="true">
                                    <h3 class="fw-normal">{LANG.nhan_su_chu_chot}</h3>
                                </a></li>
                            <!-- END: nhan_su_chu_chot -->
                            <!-- BEGIN: hang_muc_xay_lap -->
                            <li class="nav-item"><a class="nav-link" id="hang_muc_xay_lap_tab" onclick="change_my_url('#hang_muc_xay_lap');" data-toggle="tab" href="#hang_muc_xay_lap" role="tab" aria-controls="hang_muc_xay_lap" aria-selected="true">
                                    <h3 class="fw-normal">{LANG.hang_muc_xay_lap}</h3>
                                </a></li>
                            <!-- END: hang_muc_xay_lap -->
                            <!-- BEGIN: thiet_bi_thi_cong -->
                            <li class="nav-item"><a class="nav-link" id="thiet_bi_thi_cong_tab" onclick="change_my_url('#thiet_bi_thi_cong');" data-toggle="tab" href="#thiet_bi_thi_cong" role="tab" aria-controls="thiet_bi_thi_cong" aria-selected="true">
                                    <h3 class="fw-normal">{LANG.thiet_bi_thi_cong}</h3>
                                </a></li>
                            <!-- END: thiet_bi_thi_cong -->
                        </ul>
                    </nav>
                    <div class="tab-content download act_dulieu {ICON_PLANE}" style="background-color: white">
                        <!-- BEGIN: bang_du_lieu2 -->
                        <div class="tab-pane fade in" id="bang_du_lieu" role="tabpanel" aria-labelledby="bang_du_lieu_tab">
                            <div class="list-group table-responsive bdl">{DATA.bang_du_lieu}</div>
                        </div>
                        <!-- END: bang_du_lieu2 -->

                        <!-- BEGIN: pham_vi_cung_cap2 -->
                        <div class="tab-pane fade in" id="phamvicc" role="tabpanel" aria-labelledby="phamvicc_tab">
                            <div class="list-group table-responsive bdl">{DATA.pham_vi_cung_cap}</div>
                        </div>
                        <!-- END: pham_vi_cung_cap2 -->

                        <!-- BEGIN: dich_vu_lien_quan2 -->
                        <div class="tab-pane fade in" id="dich_vu_lien_quan" role="tabpanel" aria-labelledby="dich_vu_lien_quan_tab">
                            <div class="list-group table-responsive bdl">{DATA.dich_vu_lien_quan}</div>
                        </div>
                        <!-- END: dich_vu_lien_quan2 -->
                        <!-- BEGIN: tien_do_thuc_hien2 -->
                        <div class="tab-pane fade in" id="tien_do_thuc_hien" role="tabpanel" aria-labelledby="tien_do_thuc_hien_tab">
                            <div class="list-group table-responsive bdl">{DATA.tien_do_thuc_hien}</div>
                        </div>
                        <!-- END: tien_do_thuc_hien2 -->
                        <!-- BEGIN: tieu_chuan_danh_gia2 -->
                        <div class="tab-pane fade in" id="tieu_chuan_danh_gia" role="tabpanel" aria-labelledby="tieu_chuan_danh_gia_tab">
                            <div class="list-group table-responsive bdl">{DATA.tieu_chuan_danh_gia}</div>
                        </div>
                        <!-- END: tieu_chuan_danh_gia2 -->
                        <!-- BEGIN: nhan_su_chu_chot2 -->
                        <div class="tab-pane fade in" id="nhan_su_chu_chot" role="tabpanel" aria-labelledby="nhan_su_chu_chot_tab">
                            <div class="list-group table-responsive bdl">{DATA.nhan_su_chu_chot}</div>
                        </div>
                        <!-- END: nhan_su_chu_chot2 -->
                        <!-- BEGIN: hang_muc_xay_lap2 -->
                        <div class="tab-pane fade in" id="hang_muc_xay_lap" role="tabpanel" aria-labelledby="hang_muc_xay_lap_tab">
                            <div class="list-group table-responsive bdl">{DATA.hang_muc_xay_lap}</div>
                        </div>
                        <!-- END: hang_muc_xay_lap2 -->
                        <!-- BEGIN: thiet_bi_thi_cong2 -->
                        <div class="tab-pane fade in" id="thiet_bi_thi_cong" role="tabpanel" aria-labelledby="thiet_bi_thi_cong_tab">
                            <div class="list-group table-responsive bdl">{DATA.thiet_bi_thi_cong}</div>
                        </div>
                        <!-- END: thiet_bi_thi_cong2 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: chi_tiet_ho_so -->

    <!-- BEGIN: subdivision -->
    <div id="ttpl" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.title_subdivision}</h2>

        <div class="box__table">
            <table class="bidding-table table-sticky-head">
                <thead>
                <tr>
                    <th width="7%">{LANG.number}</th>
                    <!-- BEGIN: phan_lo -->
                    <th>{LANG.title_lotname}</th>
                    <th>{LANG.title_lotprice}</th>
                    <th>{LANG.title_price_estimate}</th>
                    <th>{LANG.title_lotguarantee}</th>
                    <th>{LANG.thoi_gian}</th>
                    <!-- END: phan_lo -->
                    <!-- BEGIN: chao_gia -->
                    <th>{LANG.title_lotno}</th>
                    <th>{LANG.title_lotname}</th>
                    <th>{LANG.title_lotprice}</th>
                    <th>{LANG.title_lotprice_step}</th>
                    <!-- END: chao_gia -->
                </tr>

                </thead>
                <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="text-center" data-column="{LANG.number}">
                        <div>{VALUE.stt}</div>
                    </td>
                    <!-- BEGIN: phan_lo -->
                    <td data-column="{LANG.title_lotname}">
                        <div>{VALUE.lotname}</div>
                    </td>
                    <td data-column="{LANG.title_lotprice}">
                        <div>{VALUE.lotprice}</div>
                    </td>
                    <td data-column="{LANG.title_price_estimate}">
                        <div>{VALUE.lotestimate_price}</div>
                    </td>
                    <td data-column="{LANG.title_lotguarantee}">
                        <div>{VALUE.lotguarantee_value}</div>
                    </td>
                    <td data-column="{LANG.thoi_gian}">
                        <div>{VALUE.cperiod} {VALUE.cperiodunit}</div>
                    </td>
                    <!-- END: phan_lo -->
                    <!-- BEGIN: chao_gia -->
                    <td data-column="{LANG.title_lotno}">
                        <div>{VALUE.lotno}</div>
                    </td>
                    <td data-column="{LANG.title_lotname}">
                        <div>{VALUE.lotname}</div>
                    </td>
                    <td data-column="{LANG.title_lotprice}">
                        <div>{VALUE.lotprice}</div>
                    </td>
                    <td data-column="{LANG.title_lotprice_step}">
                        <div>{VALUE.lotprice_step}</div>
                    </td>
                    <!-- END: chao_gia -->
                </tr>
                <!-- END: loop -->
                </tbody>
            </table>
        </div>
        <div class="text-center">
            <ul class="pagination pagination__main">
            </ul>
        </div>
    </div>
    <!-- END: subdivision -->

    <!-- BEGIN: goods_old -->
    <!-- BEGIN: goods -->
    <script type="text/javascript">
            search_url_good(location.href);
            function load_goods(my_url) {
                search_url_good(my_url);
            }

            function search_url_good(my_url) {
                let position = my_url.search("#dshs") + 1;
                let result_url = my_url.substr(position);
                if (position > 0) {
                    $("html, body").animate({
                        scrollTop: $("#dshs").offset().top - 80
                    }, 500);

                    $.ajax({
                        url: location.href,
                        type: 'POST',
                        data: {
                            'action': 'load_goods',
                            'url' : result_url,
                            'chssg': '{CHECKSS_GOODS}'
                        },
                        success: function(data) {
                            if (data['res'] == 'error') {
                                position = my_url.search("#dshs");
                                url = my_url.substr(0, position);
                                location.href = url;
                            } else {
                                $(".data_goods").html(data);
                            }
                        }
                    })
                }
            }

        </script>

        <div id="dshs" class="table-responsive bidding_table scoll__menu margin-top-sm">
            <div class="flex">
                <h2 class="bidding-sub-title">{LANG.title_goods}:</h2>
                <!-- BEGIN: show_excel_hh -->
                <div class="text-right margin-bottom-sm">
                    <button type="button" class="btn xuatexcel" onclick="confirm_export()"><i class="fa fa-file-excel-o" aria-hidden="true"></i> {LANG.exporthh}</button>
                    {LANG.link_follow_hh}
                </div>
                <!-- END: show_excel_hh -->
            </div>
            <div id="goods_ajax{DATA.id}" class="data_goods">
                <!-- BEGIN: goods_ajax -->
                <script type="text/javascript">

                    li = $(".data_goods").find('.pagination li');
                    for (i = 0; i < li.length; i++) {
                        if (li.eq(i).attr('class') != 'disabled') {
                            li.eq(i).addClass('li_pg');
                        }
                        li.eq(i).find('a').attr('data-location', li.eq(i).find('a').attr('data-href'));
                    }

                    $(".li_pg").click(function(){
                        $("html, body").animate({
                            scrollTop: $("#dshs").offset().top - 80
                        }, 500);

                        history.pushState(null, null, $(this).find("a").data("location"));
                    });
                </script>
            <div class="box__table">
                <table class="bidding-table table-sticky-head">
                    <thead>
                        <tr>
                            <th>{LANG.number}</th>
                            <th class="w150">{LANG.name_goods}</th>
                            <th class="w150">{LANG.code_goods}</th>
                            <th>{LANG.mass}</th>
                            <th>{LANG.unit}</th>
                            <th class="w250">{LANG.desc}</th>
                            <th>{LANG.note}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- BEGIN: loop -->
                        <tr>
                            <td data-column="{LANG.number}" class="text-center"><span class="stt">{GOODS.weight}</span></td>
                            <td data-column="{LANG.name_goods}" class="text-center"><span class="content">{GOODS.name}</span></td>
                            <td data-column="{LANG.code_goods}" class="text-center"><span>
                                    <div class="wrap__text">{GOODS.code}</div>
                                </span></td>
                            <td data-column="{LANG.mass}" class="text-center"><span class="content">{GOODS.mass}</span></td>
                            <td data-column="{LANG.unit}" class="text-center"><span class="content">{GOODS.unit}</span></td>
                            <td data-column="{LANG.desc}" class="text-center"><span class="content">{GOODS.description}</span></td>
                            <td class="text-center">
                                <span class="span__buton">
                                    <button type="button" class="btn btn-primary btn-radius" data-toggle="modal" data-target=".bd-example-modal-lg" onclick="search_goods('{GOODS.name}','{GOODS.solicitor_id}','{GOODS.id_goi_thau}')">{LANG.reference}</button>
                                </span>
                            </td>
                        </tr>
                        <!-- END: loop -->
                    </tbody>
                </table>
            </div>
            <div class="text-center">{DATA.generate_page_hh}</div>
            <!-- END: goods_ajax -->
        </div>
    </div>
    <!-- END: goods -->
    <!-- END: goods_old -->

    <!-- BEGIN: goods_new -->
    <div id="dshs" class="margin-top-sm">
        <h2 class="bidding-sub-title">{LANG.title_goods}:</h2>
        <!-- BEGIN: goods -->
        <script type="text/javascript">
                search_url_good(location.href);
                function load_goods(my_url) {
                    search_url_good(my_url);
                }

                function search_url_good(my_url) {
                    let position = my_url.search("#dshs_msc_") + 1;
                    let result_url = my_url.substr(position);
                    str = my_url.substr(position).split('?')[0];
                    id_good = str.split('_')[2];

                    if (position > 0) {
                        $("html, body").animate({
                            scrollTop: $("#dshs_" + id_good).offset().top - 80
                        }, 500);

                        $.ajax({
                            url: location.href,
                            type: 'POST',
                            data: {
                                'action': 'load_goods1',
                                'url' : result_url,
                                'chssg': '{CHECKSS_GOODS}'
                            },
                            success: function(data) {
                                if (data['res'] == 'error') {
                                    position = my_url.search("#dshs");
                                    url = my_url.substr(0, position);
                                    location.href = url;
                                } else {
                                    $("#dshs_" + id_good).html(data);
                                    setTimeout(syncScrollbars, 300);
                                }
                            }
                        })
                    }
                }

            </script>
            <div class="bidding-page-btn">
                <h2 class="bidding-sub-title">{ROW.name_table}:</h2>
                <!-- BEGIN: show_excel_hh -->
                <div class="text-right margin-bottom-sm">
                    <button type="button" class="btn xuatexcel" onclick="confirm_export()"><i class="fa fa-file-excel-o" aria-hidden="true"></i> {LANG.exporthh}</button>
                    {LANG.link_follow_hh}
                </div>
                <!-- END: show_excel_hh -->
            </div>


            <div id="dshs_{ROW.code}" class="table-responsive bidding_table">
                <div id="goods_ajax_{ROW.code_table}" class="data_goods">
                    <!-- BEGIN: goods_ajax -->
                    <script type="text/javascript">
                        li = $("#dshs").find('.pagination li');
                        for (i = 0; i < li.length; i++) {
                            if (li.eq(i).attr('class') != 'disabled') {
                                li.eq(i).addClass('li_pg');
                            }
                            li.eq(i).find('a').attr('data-location', li.eq(i).find('a').attr('data-href'));
                        }

                        $(".li_pg").click(function(){
                            // $("html, body").animate({
                            //     scrollTop: $("#dshs").offset().top - 80
                            // }, 500);

                            history.pushState(null, null, $(this).find("a").data("location"));
                        });
                    </script>
                    <div class="box__table">
                        <div class="scroll-wrapper">
                            <div class="scrollbar-top">
                                <div class="table-header scrollbar-content">
                                    <table class="table-container bidding-table table-sticky-head">
                                        <thead>
                                            <tr>
                                                <th class="w70">{LANG.number}</th>
                                                <!-- BEGIN: lot -->
                                                <th>{LANG.subdivision_code}</th>
                                                <th>{LANG.subdivision_name}</th>
                                                <!-- END: lot -->
                                                <th class="w300">{LANG.name_goods}</th>
                                                <!-- BEGIN: code -->
                                                <th>{LANG.code_goods}</th>
                                                <!-- END: code -->
                                                <!-- BEGIN: brand -->
                                                <th>{LANG.brand}</th>
                                                <th>{LANG.manufacture}</th>
                                                <th>{LANG.origin}</th>
                                                <th>{LANG.manufacture_year}</th>
                                                <!-- END: brand -->
                                                <th class="w75">{LANG.mass}</th>
                                                <th class="w75">{LANG.unit}</th>
                                                <th class="w300">{LANG.desc}</th>
                                                <!-- BEGIN: date -->
                                                <th>{LANG.place}</th>
                                                <th>{LANG.from_date}</th>
                                                <th>{LANG.to_date}</th>
                                                <!-- END: date -->
                                                <!-- BEGIN: complete_date -->
                                                <th>{LANG.complete_date}</th>
                                                <!-- END: complete_date -->
                                                <th class="scroll-action">{LANG.note}</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>

                            <div class="scroll-content">
                                <table class="table-container bidding-table table-sticky-head">
                                    <tbody>
                                        <!-- BEGIN: loop -->
                                        <tr>
                                            <td data-column="{LANG.number}" class="text-center w70"><div><span class="stt"> {GOODS.weight}</span></div></td>
                                            <!-- BEGIN: lot -->
                                            <td data-column="{LANG.subdivision_code}" class="text-center"><div><span class="content">{GOODS.lotno}</span></div></td>
                                            <td data-column="{LANG.subdivision_name}" class="text-center"><div><span class="content">{GOODS.lotname}</span></div></td>
                                            <!-- END: lot -->
                                            <td data-column="{LANG.name_goods}" class="w300 text-center"><div><span class="content">{GOODS.name}</span></div></td>
                                            <!-- BEGIN: code2-->
                                            <td data-column="{LANG.code_goods}" class="text-center"><div><span class="content">{GOODS.code}</span></div></td>
                                            <!-- END: code2 -->
                                            <!-- BEGIN: brand-->
                                            <td data-column="{LANG.brand}" class="text-center"><div><span class="content">{GOODS.brand}</span></div></td>
                                            <td data-column="{LANG.manufacture}" class="text-center"><div><span class="content">{GOODS.manufacture}</span></div></td>
                                            <td data-column="{LANG.origin}" class="text-center"><div><span class="content">{GOODS.origin}</span></div></td>
                                            <td data-column="{LANG.manufacture_year}" class="text-center"><div><span class="content">{GOODS.manufacture_year}</span></div></td>
                                            <!-- END: brand -->
                                            <td data-column="{LANG.mass}" class="text-center w75"><div><span class="content">{GOODS.mass}</span></div></td>
                                            <td data-column="{LANG.unit}" class="text-center w75"><div><span class="content">{GOODS.unit}</span></div></td>
                                            <td data-column="{LANG.desc}" class="text-center w300"><div><span class="content">{GOODS.description}</span></div></td>
                                            <!-- BEGIN: date2 -->
                                            <td data-column="{LANG.place}" class="text-center"><div><span class="content">{GOODS.place}</span></div></td>
                                            <td data-column="{LANG.from_date}" class="text-center"><div><span class="content">{GOODS.fromdate}</span></div></td>
                                            <td data-column="{LANG.to_date}" class="text-center"><div><span class="content">{GOODS.todate}</span></div></td>
                                            <!-- END: date2 -->
                                            <!-- BEGIN: complete_date -->
                                            <td data-column="{LANG.complete_date}" class="text-center"><div><span class="content">{GOODS.complete_date}</span></div></td>
                                            <!-- END: complete_date -->
                                            <td class="text-center scroll-action">
                                                <span class="span__buton">
                                                    <button type="button" class="btn btn-primary btn-radius" data-toggle="modal" data-target=".bd-example-modal-lg" onclick="search_goods('{GOODS.name}','{GOODS.solicitor_id}','{GOODS.id_goi_thau}')">{LANG.reference}</button>
                                                </span>
                                            </td>

                                        </tr>
                                        <!-- END: loop -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="text-center">{ROW.generate_page_hh}</div>
                    <!-- END: goods_ajax -->
            </div>
        </div>
        <!-- END: goods -->
    </div>
    <!-- END: goods_new -->

    <!-- BEGIN: static -->
    <div id="static" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.ptnt}</h2>
        <div>{STATIC}</div>
    </div>
    <!-- END: static -->

    <!-- BEGIN: chance_for_you -->
    <div id="tienich" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.chance_for_you}</h2>
        <div class="chance-cont" id="box_for_you">
            <div class="chance">
                <!-- BEGIN: follow_reg -->
                <div class="panel panel-default">
                    <div class="cont panel-heading">
                        <div class="tl">{LANG.follow}</div>
                        <div class="ct">{FOLLOW.content}</div>
                        <div class="bt">
                            <button type="button" class="btn btn-primary" onclick="click_follow('{DATA.link_follow}', '{CHECKSESS}', this, '{bidNo}', '{USERID}')">{FOLLOW.title}</button>
                        </div>
                    </div>
                </div>
                <!-- END: follow_reg -->
                <!-- BEGIN: receive_email_content -->
                <div class="panel panel-default">
                    <div class="cont panel-heading">
                        <div class="tl">{LANG.receive_email}</div>
                        <div class="ct">{RECEIVE_EMAIL.content}</div>
                        <div class="bt">
                            <a class="btn btn-danger" href="{RECEIVE_EMAIL.reg_link}">{RECEIVE_EMAIL.vip_paket}</a>
                        </div>
                    </div>
                </div>
                <!-- END: receive_email_content -->
            </div>
        </div>
    </div>
    <!-- END: chance_for_you -->
    <!-- BEGIN: view_biddingcosts -->
    <div id="biddingcosts" class="scoll__menu">
        <h2 class="bidding-sub-title">{LANG.bidding_costs}</h2>
        <p>{LANG.bidding_costs_des}</p>
        <div class="bid-cost-point-explain">
            <table class="table text-center table-striped table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">{LANG.cost_type}</th>
                        <th class="text-center w-50">{LANG.fee_level}</th>
                        <th class="text-center">{LANG.cost_applicable}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: view_no_vip -->
                    <tr>
                        <td data-column="{LANG.cost_type}" class="text-left">{LANG.annual_account}</td>
                        <td data-column="{LANG.fee_level}" class="text-center" id="fee_level_1"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                        <td data-column="{LANG.cost_applicable}" class="text-center" id="cost_applicable_1"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.cost_type}" class="text-left">{LANG.bid_submission}</td>
                        <td data-column="{LANG.fee_level}" class="text-center" id="fee_level_2"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                        <td data-column="{LANG.cost_applicable}" class="text-center" id="cost_applicable_2"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.cost_type}" class="text-left">{LANG.application_fee}</td>
                        <td data-column="{LANG.fee_level}" class="text-center" id="fee_level_3"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                        <td data-column="{LANG.cost_applicable}" class="text-center" id="cost_applicable_3"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.cost_type}" class="text-left">{LANG.bid_winning}</td>
                        <td data-column="{LANG.fee_level}" class="text-center" id="fee_level_4"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                        <td data-column="{LANG.cost_applicable}" class="text-center" id="cost_applicable_4"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.cost_type}" class="text-left">{LANG.electronic_bid}</td>
                        <td data-column="{LANG.fee_level}" class="text-center" id="fee_level_5"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                        <td data-column="{LANG.cost_applicable}" class="text-center" id="cost_applicable_5"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.bid_winning_total}" class="text-left"><b>{LANG.bid_winning_total}</b></td>
                        <td colspan="2" data-column="{LANG.fee_level}" class="text-center" id="fee_level_6"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <!-- END: view_no_vip -->
                    <!-- BEGIN: view_is_vip -->
                    <tr>
                        <td data-column="{LANG.cost_type}" class="text-left">{LANG.annual_account}</td>
                        <td data-column="{LANG.fee_level}" class="text-center" id="fee_level_1">{LANG.annual_account_year}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center" id="cost_applicable_1">{COST.account}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.cost_type}" class="text-left">{LANG.bid_submission}</td>
                        <td data-column="{LANG.fee_level}" class="text-center" id="fee_level_2">{LANG.bid_submission_pack}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center" id="cost_applicable_2">{COST.submission}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.cost_type}" class="text-left">{LANG.application_fee}</td>
                        <td data-column="{LANG.fee_level}" class="text-center" id="fee_level_3">{LANG.application_fee_pack}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center" id="cost_applicable_3">{COST.application}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.cost_type}" class="text-left" >{LANG.bid_winning}</td>
                        <td data-column="{LANG.fee_level}" class="text-left" id="fee_level_4">{LANG.unshared_bid}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center" id="cost_applicable_4">{COST.unshared}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.cost_type}" class="text-left">{LANG.electronic_bid}</td>
                        <td data-column="{LANG.fee_level}" class="text-left" id="fee_level_5">{LANG.electronic_bid_estimated}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center" id="cost_applicable_5">{COST.electronic}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.bid_winning_total}" class="text-left"><b>{LANG.bid_winning_total}</b></td>
                        <td colspan="2" data-column="{LANG.fee_level}" class="text-center" id="fee_level_6"><b>{COST.total}</b></td>
                    </tr>
                    <!-- END: view_is_vip -->
                </tbody>
            </table>
            <!-- BEGIN: view_lock_vip -->
            <div class="box_lockvip__detail overlay_bidding_costs">
                <div class="main_lockvip main_bg_bidding_costs">
                    <div class="tw_lockvip bg_bidding_costs">
                        <div class="tw_lockvip_head">
                            <img class="tw_lockvip_head__image">
                        </div>
                        <div class="tw_lockvip__button">
                            <!-- BEGIN: is_user -->
                            <a href="{LINK_REG}" class="btn__lock_login">{LANG.vip_register}</a>
                            <!-- END: is_user -->
                            <!-- BEGIN: is_user_renew -->
                            <a href="{LINK_RENEW}" class="btn__lock_login">{LANG.renew_vip}</a>
                            <!-- END: is_user_renew -->
                            <!-- BEGIN: no_user -->
                            <a href="#" class="btn__lock_login" data-toggle="loginFormShow">{LANG.login}</a>
                            <!-- END: no_user -->
                        </div>
                        <!-- BEGIN: btn_use_point_bidding_costs -->
                        <div>{LANG.or}</div>
                        <div class="tw_lockvip__button">
                            <a href="" class="btn__lock_login btn_point_view_bidding_costs">{LANG.use_point_view}</a>
                        </div>
                        <!-- END: btn_use_point_bidding_costs -->
                        <div class="tw_lockvip__content">
                            <p class="tw_lockvip__content__des">{LANG.view_bidding_costs}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="lock-bidding-costs">{LOCK_BIDDING_COST}</div>
            <!-- END: view_lock_vip -->
        </div>
    </div>
    <!-- END: view_biddingcosts -->
    <!-- BEGIN: btn_use_point_view_bidding_costs_modal -->
    <div class="modal fade modal-center" tabindex="-1" role="dialog" id="view-bidding-costs-confirm" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <div class="modal-title h4">{LANG.notice}</div>
                </div>
                <div class="modal-body">
                    <p id="confirm_view_bidding_costs"></p>

                </div>
                <div class="modal-footer">
                    <button type="button" id="view-bidding-costs-submit" class="btn btn-primary btn-fw">{LANG.ok}</button>
                    <button type="button" class="btn btn-default btn-fw" id="view-bidding-costs-cancel" data-dismiss="modal">{LANG.cancel}</button>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        $('.btn_point_view_bidding_costs').on('click', () => {
            $.ajax({
                url: window.location.href,
                method: 'POST',
                dataType: 'json',
                data: {
                    check_point_view_bidding_costs: 1,
                    checkss: '{CHECK_POINT_BIDDING_COST}'
                },
                success: (data) => {
                    if (data.res == 'success') {
                        $('#confirm_view_bidding_costs').text(data.mess);
                        $('#view-bidding-costs-submit').removeClass('hidden');
                    } else {
                        alertDiv = '<div class="alert alert-warning">' + data.mess + '</div>';
                        $('#confirm_view_bidding_costs').html(alertDiv);
                        $('#view-bidding-costs-submit').addClass('hidden');
                    }
                    $('#view-bidding-costs-confirm').modal('show');
                }
            });
        });
        $('#view-bidding-costs-submit').on('click', () => {
            $.ajax({
                url: window.location.href,
                method: 'POST',
                dataType: 'json',
                data: {
                    view_bidding_costs: 1,
                    checkss: '{CHECK_VIEW_BIDDING_COST}'
                },
                success: (resp) => {
                    if (resp.status == 'success') {
                        $('#cost_applicable_1').html(resp.data['account']);
                        $('#cost_applicable_2').html(resp.data['submission']);
                        $('#cost_applicable_3').html(resp.data['application']);
                        $('#cost_applicable_4').html(resp.data['unshared']);
                        $('#cost_applicable_5').html(resp.data['electronic']);
                        $('#fee_level_1').html(resp.data['annual_account_year']);
                        $('#fee_level_2').html(resp.data['bid_submission_pack']);
                        $('#fee_level_3').html(resp.data['application_fee_pack']);
                        $('#fee_level_4').html(resp.data['unshared_bid']);
                        $('#fee_level_5').html(resp.data['electronic_bid_estimated']);
                        $('#fee_level_6').html('<b>' + resp.data['total'] + '</b>');
                        $('#fee_level_4, #fee_level_5').removeClass('text-center').addClass('text-left');
                        $('.overlay_bidding_costs').addClass('hidden');
                        $('#view-bidding-costs-submit').addClass('hidden');
                        $('.lock-bidding-costs').html('');
                        $('#view-bidding-costs-confirm').modal('hide');
                    } else {
                        alertDiv = '<div class="alert alert-warning">' + resp.mess + '</div>';
                        $('#confirm_view_bidding_costs').html(alertDiv);
                        $('#view-bidding-costs-submit').addClass('hidden');
                        $('#view-bidding-costs-confirm').modal('show');
                    }
                }
            });
        });
    </script>
    <!-- END: btn_use_point_view_bidding_costs_modal -->

    [BLOCK_SUPPORT]

    <div class="margin-top margin-bottom display-flex">
        <div class="h3">
            <span class="label label-primary">{LANG.totalview}: <span class="badge">{DATA.totalview}</span></span>
        </div>
    </div>
</div>

<br />
<!-- START FORFOOTER -->
<div id="myModal" class="modal fade" role="dialog">
    <div class="modal-dialog text-left">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header text-left">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">{LANG.notice}</h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning"></div>
                <p class="confirm-export-excel hidden">{LANG.confirm_download_hh}</p>
                <div id="loading_data"></div>
                <div class="alert alert-success notification_success"></div>
                <div class="alert alert-danger notification_danger"></div>
            </div>
            <div class="modal-footer">
                <div>
                    <div class="text-left no_point"></div>
                    <input type="hidden" id="is_x4" value=""/>
                    <p class="alert alert-danger no-data-goods hidden">{LANG.no_data_goods}</p>
                    <button type="button" class="btn btn-primary" data-fcode="" id="save_excel" name="xuatexcel" value="xuatexcel">{LANG.link_file_normal}</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bd-example-modal-lg modal-goods" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">×</button>
            </div>
            <div class="modal-body">
                <p>{LANG.title_h5}:</p>
                <div class="responsivetb">
                    <table class="table table-bordered table-hover" id="search_good1"></table>
                </div>
                <div class="text-center spin hidden">
                    <i class="text-center fa fa-spin fa-spinner fa-lg"></i>
                </div>
                <blockquote id="quote1">{LANG.quote1}</blockquote>
                <div class="text-center">
                    <a class="hidden" target="_blank" id="link1" href="">{LANG.see_more}...</a>
                </div>
                <p>{LANG.title_h5_2}:</p>
                <div class="responsivetb">
                    <table class="table table-bordered table-hover" id="search_good"></table>
                </div>
                <div class="text-center spin hidden">
                    <i class="text-center fa fa-spin fa-spinner fa-lg"></i>
                </div>
                <blockquote id="quote2">{LANG.quote2}</blockquote>
                <div class="text-center">
                    <a class="hidden" target="_blank" id="link2" href="">{LANG.see_more}...</a>
                </div>

                <p>{LANG.title_h5_3}:</p>
                <div class="responsivetb">
                    <table class="table table-bordered table-hover" id="search_good3"></table>
                </div>
                <div class="text-center spin hidden">
                    <i class="text-center fa fa-spin fa-spinner fa-lg"></i>
                </div>
                <blockquote id="quote3">{LANG.quote3}</blockquote>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{LANG.close}</button>
            </div>
        </div>
    </div>
    <div id="trudiem"></div>
</div>
<!-- END FORFOOTER -->
<script>
    <!-- BEGIN: onclick_alert_check_file -->
    $('.alert-check-file').css('pointer-events', 'auto').click(function(event) {
        event.preventDefault();
        alert('{LANG.alert_check_file}');
    });
    <!-- END: onclick_alert_check_file -->
    function change_my_url(href)
    {
        //Thay đổi url khi click vào các tab trong chi tiết HSMT
        var url = location.href;
        let position = url.search("#");
        var link = url.substr(0,position);
        history.pushState("", "", link + href);
    }

    var checkess='{NV_CHECK_SESSION}';
    const LNG = {read:"{LANG.read}", origlink:"{LANG.origlink}", otherlink:"{LANG.otherlink}", viplink: "{LANG.viplink}", title_errorFile: "{LANG.title_errorFile}", status1: "{LANG.status1}"};

    $(function() {
        <!-- BEGIN: countdown -->startTimer({COUNTDOWN}, $(".countdown"));<!-- END: countdown -->
        $(".is_other .is_ie").parents("a").addClass("disable-link");
        link_reformat();
        link_reformat_dt();
        function link_reformat_dt() {
            $(".is_points #quyet_dinh_normal").not(".is_ie").each(function() {
                var a = $(this).parent(),
                    c = void 0 !== a.attr("href") ? a.attr("href") : "",
                    f = void 0 !== a.attr("onclick") ? a.attr("onclick") : "",
                    e = void 0 !== a.attr("target") ? a.attr("target") : "_self",
                    d = $(this).text(),
                    b = a.parent().find('#quyet_dinh_link'),
                    g = '{LANG.viplink}',
                    cl = 'primary',
                    m = 'fa-snowflake-o';
                a.addClass("disable-link");
                if($('.display-flex.list-group-item').hasClass("disable-link")){
                    $(this).addClass("quyet_dinh_child");
                    $('.display-flex.list-group-item').removeClass("disable-link");
                } else {
                    if($('.display-flex.list-group-item').hasClass("download_normal")){
                        $(this).addClass("span");
                        $(this).is(".is_fast") ? b.prepend(addA(cl, c, e, g, m)) : b.prepend(addA("default", c, e, '{LANG.origlink}', m));
                    }
                }
            })
        }
        client = JSON.parse('{CLIENT}');
        /*$('a[target="_blank"]').click(function(e) {
            if (0 < $(this).attr("href").indexOf("muasamcong.mpi.gov.vn")) {
                var b = $(window).width(),
                    a = $("span", this).is(".is_file"),
                    c = window.open($(this).attr("href"), a ? "_MyFile" : "_MyWindow", setWinParams(a ? 200 : 500 > b ? b - 50 : b / 2, a ? 200 : 500));
                return a && (c.onblur = function() {
                    return c.close(), !1
                }), c.focus(), !1
            }
        });*/

        // if (!$(this).attr('href').startsWith("https://") && client['browser']['key'] != "explorer") {
        //        $(this).click(function(event) {
        //            event.stopPropagation();
        //                console.log($(this).attr("href"));
        //        });

        li = $(".responsivetb").find('.pagination li');
        for (i = 0; i < li.length; i++) {
            li.eq(i).find('a').attr('data-location', li.eq(i).find('a').attr('data-href'));
        }

    });

    move_to_id();
    function move_to_id(){
        //tự động mở tab chi tiết HSMT khi có url tương ứng
        var url = location.href;
        let position = url.search("#");
        var id = url.substr(position);
        var id_tab = id + "_tab";

        if (position > 0) {
            $(id) .addClass( "active" );
            $(id_tab).parent().addClass( "active" );
            $("html, body").animate({
                scrollTop: $(id).offset().top - 80
            }, 500);
        }else {
            $(".act_dulieu li:first-child" ).addClass( "active" );
            $(".act_dulieu div:first-child" ).addClass( "active" );
        }
    }
    
</script>

<script type="text/javascript">
    $(function() {
        $(".download-file").click(function() {
            var idfile = $(this).attr("id-file");
            var rid = $(this).attr("rid");
            var is_req = $(this).attr("is_req");
            var checkss = $(this).attr("checkss");
            var typefile = $(this).attr('typefile');
            if (idfile !== "undefined" ) {
                $.ajax({
                    url: location.href,
                    type: 'POST',
                    data: {
                        'action': 'download_static',
                        'idfile' : idfile,
                        'bid_id' : {DATA.id},
                        'rid' : rid,
                        'is_req': is_req,
                        'checkss' : checkss,
                        'type_file' : typefile
                    },
                    success: function(data) {
                        if (data['res'] == 'success') {
                            window.open(data['link'], '_blank');
                        } else {
                            alert(data['message']);
                        }
                    }
                })
            }
        });
        
        update_id_checkbox();
        $('[data-toggle="tooltip"]').tooltip();
        // Danh sách thông báo
        DATA_TB = '{DATA_TB}';
        $(".link_change_a:last").hide();

        $(".chk_notification").change(function(){
            var numberOfChecked = $('.chk_notification:checkbox:checked').length;

            if (numberOfChecked >= 2) {
                $('.chk_notification:checkbox').not(':checked').prop("disabled", true);
            } else {
                $('.chk_notification:checkbox').not(':checked').prop("disabled", false);
            }
            update_id_checkbox();
        });

        var title = $("#view_change_tb").parent().attr("data-original-title");
        function update_id_checkbox() {
            var $boxes = $('.chk_notification[type=checkbox]:checked');
            arrid = [];
            $boxes.each(function(v, k){
               arrid.push(k.value);
            });

            $("#view_change_tb").val(arrid.join("-"));
            if ($boxes.length < 2) {
                $("#view_change_tb1").removeClass("damdam");
                $("#view_change_tb1").addClass("momo");
                $("#view_change_tb").parent().attr("data-original-title", title);
                $("#view_change_tb1").show();
                $("#view_change_tb").hide();
            } else {
                $("#view_change_tb1").removeClass("momo");
                $("#view_change_tb1").addClass("damdam");
                $("#view_change_tb").parent().attr("data-original-title", "");
                $("#view_change_tb1").hide();
                $("#view_change_tb").show();
            }
        }

        $("#view_change_tb, .link_change_a a").click(function() {
            id = $(this).attr('data-id');
            if (id === undefined) {
                id = $(this).val();
            }

            // Ẩn chế độ 1 cột trên mobile
            if (window.matchMedia("(max-width: 768px)").matches) {
                $("input[name='sosanhtb'][value='1']").closest('label').hide();
                $("input[name='sosanhtb'][value='2']").click();
            } else {
                $("input[name='sosanhtb'][value='1']").closest('label').show();
                $("input[name='sosanhtb'][value='1']").click();
            }

            $.ajax({
                url: location.href,
                type: 'POST',
                data: {
                    'action': 'view_change_tb',
                    'id' : id
                },
                success: function(data) {
                    $("#outputOriginal tbody").html("");
                    $("#output tbody").html("");
                    if (data['res'] == 'error') {
                        alert(data['data']);
                        $("#showTB").modal('hide');
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                        return false;
                    }

                    compare_highlight_difference("outputOriginal", "output", "outputNew", false, JSON.stringify(data['data']['thongbao']), JSON.stringify(data['data']['thongbao1']));
                    tr_0 = $(".table__tb").eq(0).find('tr');
                    tr_1 = $(".table__tb").eq(1).find('tr');
                    for (i = 0; i < tr_0.length; i++) {
                        tr_0.eq(i).attr('data-row', i+1);
                    }

                    for (i = 0; i < tr_1.length; i++) {
                        tr_1.eq(i).attr('data-row', i+1);
                    }

                    html_old = $("#outputOriginal tbody").html();
                    html1_old = $("#output tbody").html();
                    $(".change_by_line").click(function() {
                        view_change_line(html_old, html1_old);
                    });

                    change_by_line = $(".change_by_line:checked").val();

                    if (change_by_line == '3') {
                        view_change_line(html_old, html1_old);
                    }
                }
            })
        });



        function view_change_line(html_old, html1_old) {
            arr_show = [];
            table = $(".change_by_line").closest('#main_change_notificaion').find('#output').find('table');
            del = table.find('tr').find('td');
            for (i = 0; i < del.length; i++) {
                row = del.eq(i).find('del').length;
                if (!row) {
                    row = del.eq(i).find('ins').length;
                }
                if (row) {
                    data_row = del.eq(i).closest('tr').attr('data-row');
                    arr_show.push(data_row);
                }
            }
            arr_show = [...new Set(arr_show)];

            html = '';
            html1 = '';

            for (i = 0; i < arr_show.length; i++) {
                html += '<tr data-row="' + arr_show[i] +'">' + $('#outputOriginal .table__tb tr[data-row="' + arr_show[i] +'"]').html() + "</tr>";
                html1 += '<tr data-row="' + arr_show[i] +'">' + $('#output .table__tb tr[data-row="' + arr_show[i] +'"]').html() + "</tr>";
            }

            if($(".change_by_line").is(':checked')) {
                $("#outputOriginal tbody").html(html);
                $("#output tbody").html(html1);
            } else {
                $("#outputOriginal tbody").html(html_old);
                $("#output tbody").html(html1_old);
            }
        }

        // $("#linebyline").hide();
        $("input[name='sosanhtb']").change(function() {
            if ($(this).val() == 2) {
                $("#outputOriginal").parent().hide(500);
                $("#output").parent().show(500);
            } else if ($(this).val() == 3) {
                $("#outputOriginal").parent().hide();
                $("#output").parent().hide();

            } else {
                $("#outputOriginal").parent().show(500);
                $("#output").parent().show(500);
            }
        });
    });

    //search_goods
    function search_goods(name,solicitor_id, id) {
        var data = {
            'action_search_goods': 1,
            'name': name,
            'solicitor_id': solicitor_id,
            'id_goi_thau': id
        }
        var key = name.replaceAll(" ", "%20");
        $("#link1").prop("href", '{LINK_HANGHOA}'+'?type_search=1&type=goods_name&key='+key+'&solicitor_id='+solicitor_id);
        $("#link2").prop("href", '{LINK_HANGHOA}'+'?type_search=2&type=goods_name&key='+key+'&solicitor_id='+solicitor_id);
        $('#search_good').html('');
        $('#search_good1').html('');
        $('.spin').removeClass('hidden');
        $('#quote1').addClass('hidden');
        $('#quote2').addClass('hidden');
        $.ajax({
            type: "POST",
            url: window.location.href,
            cache: !1,
            data: data,
            success: function (res) {
                $('.spin').addClass('hidden');
                if (res.html2 != '') {
                    $('#search_good').html(res.html2);
                    $('#quote2').addClass('hidden');
                    $('#link2').removeClass('hidden');
                }else {
                    $('#quote2').removeClass('hidden');
                    $('#link2').addClass('hidden');
                }
                if (res.html1 != '') {
                    $('#search_good1').html(res.html1);
                    $('#quote1').addClass('hidden');
                    $('#link1').removeClass('hidden');
                } else {
                    $('#quote1').removeClass('hidden');
                    $('#link1').addClass('hidden');
                }
                if (res.html3 != '') {
                    $('#search_good3').html(res.html3);
                    $('#quote3').addClass('hidden');
                    $('#link3').removeClass('hidden');
                } else {
                    $('#quote3').removeClass('hidden');
                    $('#link3').addClass('hidden');
                }
                $('.spin').addClass('hidden');
            }
        })
    }
    function view_price(id, stbmt, link_name) {
        $html = '';
        $html += '<td data-column="{LANG.so_tbmt}"> <span class="so_tbmt_' + id +'"></span></td>';
        $html += '<td data-column="{LANG.win_price}"><span class="win_price_' + id +'"></span></td>';
        //console.log($(this));
        $(".view__price" + id).closest('tr').append($html);
        $(".view__price" + id).parent().remove();

        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                'view_price': 1,
                'stbmt': stbmt,
                'link_name': link_name,
                'id': id
            },
            success: function (res) {
                result = JSON.parse(res.mess);
                if (result['khongdudiem'] == 1) {
                    alert("{LANG.point_miss_goods}");
                } else {
                    $(".win_price_" + result['data']['id']).html("<a href=" + result['linkgoithau'] + ">" + result['tbmt'] + "</a>");
                    $(".so_tbmt_" + result['data']['id']).html(result['data']['bid_price']);
                    $("#trudiem").html(result['notifi']);
                    $("#trudiem").slideDown(500);
                    setTimeout(function() {
                        $("#trudiem").slideUp(500);
                    }, 2000);
                }
            }
        })
    }
</script>

<script type="text/javascript">
    $(document).ready(function() {
        $('[data-toggle="tooltip"]').tooltip();
        $(".errorFile").click(function() {
            alert("{LANG.errorFile}");
        });
        function closeme() {
            window.open('', '_self', '');
            window.close();
        }

        if ($(window).width() < 483) {
            // Kiểm tra xem cả hai thẻ đều tồn tại trước khi tiếp tục
            if ($('.crawl_time').length > 0 && $('.bidding-page-btn.m-bottom').length > 0) {
                // Di chuyển phần tử crawl_time ra trên thẻ div chính
                $('.crawl_time').insertBefore($('.bidding-page-btn.m-bottom'));
            }
        }
        $(".notification_success").hide();
        $(".notification_danger").hide();
        $("#save_excel").click(function() {
            if ($("#is_x4").val() == 1) {
                $("#loading_data").html('{LANG.processing_data} <i class="fa fa-spinner fa-spin"></i>');
                $.ajax({
                    url: location.href,
                    type: 'POST',
                    data: {
                        xuatexcel: 1
                    },
                })
                .done(function(res) {
                    if (res['status'] == 'success') {
                        $(".notification_success").hide();
                        $(".notification_danger").hide();
                        $(".notification_success").show(500);
                        $(".notification_success").html(res['messages']);
                    } else {
                        $(".notification_danger").hide();
                        $(".notification_danger").show(500);
                        $(".notification_danger").html(res['messages']);
                    }
                    $("#loading_data").html('');
                });
            } else {
                if (confirm("{LANG.confirm_download_hh}")) {
                    $("#loading_data").html('{LANG.processing_data} <i class="fa fa-spinner fa-spin"></i>');
                    $.ajax({
                        url: location.href,
                        type: 'POST',
                        data: {
                            xuatexcel: 1
                        },
                    })
                    .done(function(res) {
                        if (res['status'] == 'success') {
                            $(".notification_success").hide();
                            $(".notification_danger").hide();
                            $(".notification_success").show(500);
                            $(".notification_success").html(res['messages']);
                        } else {
                            $(".notification_danger").hide();
                            $(".notification_danger").show(500);
                            $(".notification_danger").html(res['messages']);
                        }
                        $("#loading_data").html('');
                    });
                }
            }
        });
    });
    function confirm_export () {
        $("#myModal .alert-warning").html('');
        $(".notification_success").html('');
        $(".notification_danger").html('');
        $(this).attr('disabled', true);
        $("#save_excel").attr('disabled', false);
        $(".confirm-export-excel").addClass("hidden");
        $.ajax({
            url: location.href,
            type: 'POST',
            data: {
                confirm_export_excel: 1
            },
        })
        .done(function(res) {
            if (res['status'] == 'success') {
                $(this).attr('disabled', false);
                $(".notification_success").hide();
                $(".notification_danger").hide();
                $("#is_x4").val(res.is_x4);
                if (res.is_x4 == 1) {
                    if (res.total == 0) {
                        $(".confirm-export-excel").html("{LANG.no_data_goods}");
                        $("#save_excel").attr('disabled', true);
                    }
                    $(".confirm-export-excel").removeClass("hidden");
                    $("#myModal .alert-warning").addClass("hidden");
                } else {
                    if (res.total == 0) {
                        $(".no-data-goods").removeClass("hidden");
                        $("#save_excel").hide();;
                    }
                    $("#save_excel").html(res.icon_download + " {LANG.link_file_normal}");
                    $("#myModal .alert-warning").removeClass("hidden");
                    $("#myModal .alert-warning").html(res.value + res.note__download_novip);
                    if (res.no_point != '') {
                        $(".no_point").html('<div class="alert alert-danger">' + res.no_point + '</div>');
                    }
                    if (res.disable == 1) {
                        $("#save_excel").attr('disabled', true);
                    }
                }
                $('#myModal').modal('show');
            } else {
                $(".notification_danger").hide();
                $(".notification_danger").show(500);
                $(".notification_danger").html(res['messages']);
            }
            $("#loading_data").html('');
        });
    }
</script>

<script>
    var someText = $("#table_html_content").html();
    if (typeof(someText) !== 'undefined') {
        $("#table_html").append(someText);

    }
    function show_httbModal(){
        if (typeof(someText) !== 'undefined') {
        $("#hinh_thuc_thong_bao").modal();
        }
    }
</script>
<style>
    #trudiem {
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background: #d15c18d1;
        padding: 10px;
        color: #ffffff;
        margin: 0;
        box-shadow: 0px 5px 10px rgb(141 136 136 / 60%);
        border-radius: 2px;
        display: none;
    }

    #trudiem p {
        margin: 0;
    }
    .tooltip {
        width: 400px;
    }
    .bnt-active{
        cursor: pointer;
        color: #d61c1d;
    }

    .bnt-active:hover {
        color: #0685d6;
    }

    .item__notifi .tooltip-inner {
        background: red;
        color: #Fff;
    }

    .item__notifi .tooltip-arrow {
        border-top-color: red !important;
    }

    .download-link .span:before {
        font-family: FontAwesome;
        content: " \f0f6 ";
        margin-right: 5px;
    }

    .quyet_dinh_child {
        width: 100%;
    }

    .quyet_dinh_child .list-group-item {
        display: flex;
        justify-content: space-between;
    }

    .quyet_dinh_child .list-group-item a {
        order: 2;
    }

    .quyet_dinh_child .list-group-item span {
        order: 1;
    }
</style>

<script type="text/javascript">
    $('#ychh').addClass('no-before');
    $('#DKCTHT').addClass('no-before');

    /*Phân trang*/
    var table ='#mytable';

    loadPagination();

    function loadPagination(num_default = 100) {
        $(".pagination__main").html('');
        var trnum = 0;
        var maxRows = parseInt(num_default);
        var totalRows = $(table+' tbody tr').length;
        $(table +' tr:gt(0)').each(function() {
            trnum++
            if(trnum > maxRows){
                $(this).hide();
            }else{
                $(this).show();
            }
        });

        if(totalRows > maxRows){
            var pagenum = Math.ceil(totalRows/maxRows);
            for (var i = 1; i <= pagenum;) {
                $(".pagination__main").append('<li data-page="'+i+'">\<a href="javascript:void(0)" class="pagination__link"><span>'+ i++ +'</span><span class="sr-only">(current)</span></span>\</li>').show();
            }
        }
        $('.pagination__main li:first-child').addClass('active');
        $('.pagination__main li:first-child').find('span').addClass('color__white');
        $('.pagination__main li').on('click', function(){
            var pageNum = $(this).attr('data-page');
            var trIndex = 0;
            $('.pagination__main li').removeClass('active');
            $('.pagination__main li').find('span').removeClass('color__white');
            $(this).addClass('active');
            $(this).find('span').addClass('color__white');
            $(table+' tr:gt(0)').each(function() {
                trIndex++;
                if(trIndex > (maxRows*pageNum) || trIndex <= ((maxRows*pageNum) - maxRows)){
                    $(this).hide();
                }else{
                    $(this).show();
                }
            });
        });

        $(".pagination__link").click(function() {
            $("html, body").animate({ scrollTop: $("#mytable").offset().top - 30}, 200);
        });
    }
</script>
<script type="text/javascript">
    function syncScrollbars() {
        const scrollTop = document.querySelector(".scrollbar-top");
        const scrollContent = document.querySelector(".scroll-content");
        if (scrollTop && scrollContent) {
            scrollContent.addEventListener("scroll", function () {
                scrollTop.scrollLeft = scrollContent.scrollLeft;
            });

            scrollTop.addEventListener("scroll", function () {
                scrollContent.scrollLeft = scrollTop.scrollLeft;
            });
        }
    }

    // Khi tải trang
    document.addEventListener("DOMContentLoaded", syncScrollbars);
</script>
<!-- BEGIN: json_ld -->
<script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "NewsArticle",
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://google.com/article"
        },
        "headline": "{ARR_LD.title}",
        "image": ["{SCHEMA_IMAGE}"],
        "datePublished": "{ARR_LD.datePublished}",
        "dateModified": "{ARR_LD.dateModified}",
        "author": {
            "@type": "Person",
            "name": "{ARR_LD.brand_name}",
            "url": "{ARR_LD.url}"
        },
        "publisher": {
            "@type": "Organization",
            "name": "{ARR_LD.site_name}",
            "logo": {
                "@type": "ImageObject",
                "url": "{ARR_LD.url_logo}"
            }
        },
        "offers": {
            "@type": "Offer",
            "url": "{ARR_LD.url}",
            "validFrom": "{ARR_LD.validFrom}",
            "validThrough":"{ARR_LD.validThrough}"
        }
    }
</script>
<!-- END: json_ld -->
{FILE "modal_log.tpl"}
<!-- END: main -->
