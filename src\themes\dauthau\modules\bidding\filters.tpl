<!-- BEGIN: main -->

<script src="{NV_STATIC_URL}themes/{TEMPLATE}/js/filter-preview.js"></script>

<div class="filter_permission">
    <div class="border-bidding form-inline">
        <span>{LANG.filter_manage}</span>
    </div>
    <!-- BEGIN: permission -->
    <div class="permission_customs block">
        <span>{LANG.view_filter_acount}: </span> <select class="form-control view_customs" name="view_customs">
            <option value="0">{LANG.personal}</option>
            <!-- BEGIN: loop -->
            <option value="{PERMISSION.id}"{PERMISSION.selected}>{PERMISSION.title}</option>
            <!-- END: loop -->
        </select>
    </div>
    <!-- END: permission -->
</div>

<script type="text/javascript">
$(function() {
    $('.form-control.view_customs').select2();
    $('.view_customs').on('change', function() {
        var view_customs = $('.view_customs').val();
        location.href = nv_base_siteurl + "index.php?" + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=bidding&' + nv_fc_variable + '=filters&view_customs=' + view_customs;
    });
});
</script>
<!-- BEGIN: view -->
<!-- BEGIN: alert -->
<div class="alert alert-info">{ALERT}</div>
<!-- END: alert -->
<div class=" margin-bottom-lg">
    <a href="#" title="{LANG.add_filter}" class="btn btn-primary open_form {ClassDisabled}">{LANG.add_filter}</a> <a href="{NV_BASE_SITEURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}=filterlog&amp;userid={USERID}" title="{LANG.filterlog}" class="btn btn-primary">{LANG.filterlog}</a> <a href="{NV_BASE_SITEURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}=export-bidding" title="{LANG.export_excel_manage}" class="btn btn-primary">{LANG.export_excel_manage}</a>
</div>
<form class="filters-form" action="{NV_BASE_SITEURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post" data-sendmail-confirm="{LANG.send_mail_confirm}" data-unsendmail-confirm="{LANG.unsend_mail_confirm}" data-url-points="{URL_POINTS}" data-not-points="{NOT_POINT}" data-add-points-confirm="{LANG_NOT_POINT}" data-sendmail-points-confirm="{POINT_CONFIRM}" data-sendmail-points-ok="{LANG.point_confirm_ok}" data-sendmail-points-err="{LANG.point_confirm_err}" data-add-points-excel-confirm="{LANG_NOT_POINT}">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th width="10%" class="text-center">{LANG.STT}</th>
                    <th>{LANG.title}</th>
                    <th style="width: 2%" class="text-center text-nowrap">{LANG.feature}</th>
                    <th style="width: 5%" class="text-center">{LANG.thaotac}</th>
                </tr>
            </thead>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center" colspan="5">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="text-center">
                        <select class="form-control" id="FILTER{VIEW.id}" onchange="nv_change_weight('#FILTER{VIEW.id}', {VIEW.id}, {VIEW.userid})">
                            <!-- BEGIN: show_stt -->
                            <option value="{FILTER.key}"{FILTER.selected}>{FILTER.key}</option>
                            <!-- END: show_stt -->
                        </select>
                    </td>
                    <td>
                        <!-- BEGIN: link_detail -->
                        <p class="title__filter">
                            <a title="{VIEW.title}" href="{VIEW.link_search_detail}">{VIEW.title}</a>
                        </p>
                        <p>
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_detail}" title="{LANG.find_tbmt}"><em class="icon-filter"></em> {LANG.find_tbmt}</a>
                        </p>
                        <!-- END: link_detail -->
                        <!-- BEGIN: link_plans -->
                        <p class="title__filter">
                            <a class="" title="{VIEW.title}" href="{VIEW.link_search_plans}">{VIEW.title}</a>
                        </p>

                        <p>
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_plans}" title="{LANG.find_khlcnt}"><em class="icon-filter"></em> {LANG.find_plan}</a>
                        </p>
                        <!-- END: link_plans -->
                        <!-- BEGIN: link_tbmtqt -->
                        <p class="title__filter">
                            <a title="{VIEW.title}" href="{VIEW.link_search_tbmtqt}">{VIEW.title}</a>
                        </p>
                        <p>
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_tbmtqt}" title="{LANG.find_tbmtqt}"><em class="icon-filter"></em> {LANG.find_tbmtqt}</a>
                        </p>
                        <!-- END: link_tbmtqt -->
                        <!-- BEGIN: link_plansqt -->
                        <p class="title__filter">
                            <a title="{VIEW.title}" href="{VIEW.link_search_plansqt}">{VIEW.title}</a>
                        </p>
                        <p>
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_plansqt}" title="{LANG.find_plansqt}"><em class="icon-filter"></em> {LANG.find_plansqt}</a>
                        </p>
                        <!-- END: link_plansqt -->
                        <!-- BEGIN: link_vip5 -->
                        <p class="title__filter">
                            <a title="{VIEW.title}" href="{VIEW.link_search_tbmtvk}">{VIEW.title}</a>
                        </p>
                        <p class="title__filter">
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_tbmtvk}" title="{LANG.find_tbmtvt}"><em class="icon-filter"></em> {LANG.find_tbmtvt}</a>
                        </p>
                        <!-- END: link_vip5 -->
                        <!-- BEGIN: link_vip4 -->
                        <p class="title__filter">{VIEW.title}</p>
                        <p>
                            <!-- BEGIN: find_tbmt -->
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_detail}" title="{LANG.vip4_find_tbmt}"><em class="icon-filter"></em> {LANG.vip4_find_tbmt}</a>
                            <!-- END: find_tbmt -->
                            <!-- BEGIN: find_cbdmda -->
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_project}" title="{LANG.vip4_find_cbdmda}"><em class="icon-filter"></em> {LANG.vip4_find_cbdmda}</a>
                            <!-- END: find_cbdmda -->
                            <!-- BEGIN: find_khlcndt -->
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_plans}" title="{LANG.vip4_find_khlcndt}"><em class="icon-filter"></em> {LANG.vip4_find_khlcndt}</a>
                            <!-- END: find_khlcndt -->
                            <!-- BEGIN: find_kqlcndt -->
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_project_result}" title="{LANG.vip4_find_kqlcndt}"><em class="icon-filter"></em> {LANG.vip4_find_kqlcndt}</a>
                            <!-- END: find_kqlcndt -->
                            <!-- BEGIN: find_tbmst -->
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_project_tbmst}" title="{LANG.vip4_find_tbmst}"><em class="icon-filter"></em> {LANG.vip4_find_tbmst}</a>
                            <!-- END: find_tbmst -->
                            <!-- BEGIN: find_kqst -->
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_project_kqst}" title="{LANG.vip4_find_kqst}"><em class="icon-filter"></em> {LANG.vip4_find_kqst}</a>
                            <!-- END: find_kqst -->
                        </p>
                        <!-- END: link_vip4 -->
                        <!-- BEGIN: link_vip6 -->
                        <p class="title__filter">
                            <a title="{VIEW.title}" href="{VIEW.link_search_daugia}">{VIEW.title}</a>
                        </p>
                        <p>
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_daugia}" title="{LANG.find_daugia}"><em class="icon-filter"></em> {LANG.find_daugia}</a>
                        </p>
                        <!-- END: link_vip6 -->
                        <!-- BEGIN: link_vip7 -->
                        <p class="title__filter">
                            <a title="{VIEW.title}" href="{VIEW.link_search_kqlcnt}">{VIEW.title}</a>
                        </p>
                        <p>
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_kqlcnt}" title="{LANG.find_kqlcnt}"><em class="icon-filter"></em> {LANG.find_kqlcnt}</a>
                        </p>
                        <!-- END: link_vip7 -->
                        <!-- BEGIN: link_vip8 -->
                        <p class="title__filter">
                            <a title="{VIEW.title}" href="{VIEW.link_search_ycbg}">{VIEW.title}</a>
                        </p>
                        <p>
                            <a class="btn btn-default btn-sm" href="{VIEW.link_search_ycbg}" title="{LANG.find_ycbg}"><em class="icon-filter"></em> {LANG.find_ycbg}</a>
                        </p>
                        <!-- END: link_vip8 -->
                        <!-- BEGIN: mail1 -->
                        <div class="small thuocvip">{VIEW.thuocvip}</div>
                        <!-- END: mail1 -->
                        <!-- BEGIN: mail_points1 -->
                        <div class="small thuocvip">{LANG.user__point}: {POINT_MAIL.vip}, {LANG.user_point} {POINT_MAIL.dateexpired}</div>
                        <!-- END: mail_points1 -->
                         <!-- BEGIN: excel_points1 -->
                        <div class="small thuocvip">{LANG.user_point_excel} {POINT_EXCEL_MAIL.dateexpired}</div>
                        <!-- END: excel_points1 -->
                        <!-- BEGIN: link_use_point1 -->
                        <div class="small thuocvip">{USER_POINT}</div>
                        <!-- BEGIN: expried -->
                        <span class="text-danger">{EXPRIED_MESSAGE}.</span>
                        <!-- END: expried -->
                        <!-- END: link_use_point1 -->

                        <!-- BEGIN: link_use_point_excel1 -->
                        <!-- BEGIN: expried -->
                        <span class="text-danger">{EXPRIED_MESSAGE}</span>
                        <!-- END: expried -->
                        <!-- END: link_use_point_excel1 -->

                        <!-- BEGIN: disabled_action -->
                        <p class="red content_renewal">{VIEW.title_use_vip}</p>
                        <!-- END: disabled_action -->
                    </td>

                    <td class="text-nowrap">
                        <!-- BEGIN: mail -->
                        <label class="custom-checkbox-inline toggle"><input type="checkbox" name="status" value="1" onclick="nv_change_status(this, {VIEW.id}, {VIEW.userid})" {CHECK} /><span class="txt"><span class="sr-only">{LANG.active_send_mail}</span> {LANG.active_send_mail} </span></label> <br />
                        <!-- END: mail -->

                        <!-- BEGIN: mail_points -->
                        <label class="custom-checkbox-inline toggle"><input type="checkbox" name="status" value="{VIEW.id}" onclick="nv_change_status(this, {VIEW.id}, {VIEW.userid})" {CHECK} /><span class="txt"><span class="sr-only">{LANG.active_send_mail}</span> {LANG.active_send_mail} </span></label> <br />
                        <!-- BEGIN: renew_off -->
                        <div>
                            <a href="#" data-toggle="renewalautofilter" data-id="{VIEW.id}" data-userid="{VIEW.userid}" data-checksess="{NV_CHECK_SESSION}" data-tip="filtertip" data-title="{LANG.filter_renewal_note}" data-trigger="hover">{LANG.filter_renewal_auto0}</a>
                        </div>
                        <!-- END: renew_off -->
                        <!-- BEGIN: renew_on -->
                        <div>
                            <a href="#" data-toggle="renewalautofilter" data-id="{VIEW.id}" data-userid="{VIEW.userid}" data-checksess="{NV_CHECK_SESSION}">{LANG.filter_renewal_auto1}</a>
                        </div>
                        <!-- END: renew_on -->
                        <!-- END: mail_points -->
                         <!-- BEGIN: excel_point -->
                        <label class="custom-checkbox-inline toggle"><input type="checkbox" name="status" value="1" onclick="nv_change_status_advance(this, {VIEW.id}, {VIEW.userid})" {CHECK_EXCEL} />
                            <span class="txt">
                                <span class="sr-only">{LANG.active_send_file_excel}</span> {LANG.active_send_file_excel} 
                            </span>
                        </label> 
                        <!-- BEGIN: renew_off -->
                        <div>
                            <a href="#" data-toggle="renewalautofilter" data-id="{VIEW.id}" data-userid="{VIEW.userid}" data-checksess="{NV_CHECK_SESSION}" data-tip="filtertip" data-title="{LANG.filter_renewal_note}" data-trigger="hover">{LANG.filter_renewal_auto0}</a>
                        </div>
                        <!-- END: renew_off -->
                        <!-- BEGIN: renew_on -->
                        <div>
                            <a href="#" data-toggle="renewalautofilter" data-id="{VIEW.id}" data-userid="{VIEW.userid}" data-checksess="{NV_CHECK_SESSION}">{LANG.filter_renewal_auto1}</a>
                        </div>
                        <!-- END: renew_on -->
                        <!-- END: excel_point -->
                        <!-- BEGIN: link_vip -->
                        <a class="btn btn-default btn-sm" href="{LINK_VIP}"><em class="icon-vip"></em> {LANG.register}</a>
                        <!-- END: link_vip -->
                        <!-- BEGIN: link_uservip -->
                        <a class="btn btn-default btn-sm" href="{LINK_VIP}"><em class="icon-dollar"></em> {LANG.title__vip}</a>
                        <!-- END: link_uservip -->
                        <!-- BEGIN: link_use_point -->
                        <a class="btn btn-default btn-sm" href="#" data-tip="filtertip" data-title="{LANG.mail_use_point_title}" onclick="click_use_point('{VIEW.id}', '{VIEW.userid}', '{CHECKSESS}', '{VIEW.title}', '{VIEW.vip_use}')"><em class="icon-dollar"></em> {LANG.use_point} </a>
                        <!-- END: link_use_point -->
                        <!-- BEGIN: link_use_point_excel -->
                        <a class="btn btn-default btn-sm" href="#" data-tip="filtertip" data-title="{LANG.mail_excel_use_point_title}" onclick="click_use_point_excel('{VIEW.id}', '{VIEW.userid}', '{CHECKSESS}', '{VIEW.title}', '{VIEW.vip_use}')">
                            <em class="icon-dollar"></em> {LANG.use_point_excel} 
                        </a>
                        <!-- END: link_use_point_excel --> 
                        <!-- BEGIN: excel -->
                        <div>
                            <label class="custom-checkbox-inline toggle"><input type="checkbox" name="status" value="1" onclick="nv_change_status_advance(this, {VIEW.id}, {VIEW.userid})" {CHECK_EXCEL} /><span class="txt"><span class="sr-only">{LANG.active_send_file_excel}</span> {LANG.active_send_file_excel} </span></label> <br />
                        </div>
                        <!-- END: excel -->
                        <!-- BEGIN: link_advance_feature -->
                        <div>
                            <a class="btn btn-default btn-sm" href="{LINK_ADVANCE}"><em class="icon-dollar"></em> {LANG.advanced_feature}</a>
                        </div>
                        <!-- END: link_advance_feature -->
                    </td>

                    <td class="text-center text-nowrap">
                        <a href="{VIEW.link_history}" title="{LANG.history_filter}" class="btn btn-default btn-sm"> <i class="fa fa-history" aria-hidden="true"></i>
                        </a> <a class="btn btn-default btn-sm" href="{VIEW.link_edit}"><em class="icon-edit"></em> {LANG.edit}</a> <a class="btn btn-default btn-sm" href="{VIEW.link_delete}" onclick="return delete_confirm(this);"><em class="icon-delete"></em> {LANG.delete} </a>
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
</form>

<!-- END: view -->
<!-- BEGIN: error -->
<div class="alert alert-warning">{ERROR}</div>
<!-- END: error -->
<div class="panel panel-default" id="add_filter"
    <!-- BEGIN: add_filter_hide -->
    style="display:none"
    <!-- END: add_filter_hide -->
    >
    <div class="panel-heading">{LANG.add_filter}</div>
    <div class="panel-body">
        <form id="filter-form" class="form-horizontal" method="post">
            <input type="hidden" name="id" value="{ROW.id}" />
            <input type="hidden" name="userid" value="{ROW.userid}" />
            <input type="hidden" name="view_customs" value="{VIEW_CUSTOMS}" />
            <div class="form-group">
                <label class="control-label col-sm-6 col-md-6">{LANG.title_filters}:</label>
                <div class="col-sm-18 col-md-18">
                    <input class="form-control required" type="text" name="title" value="{ROW.title}" />
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-6 col-md-6">{LANG.type_search}</label>
                <div class="col-sm-18 col-md-18">
                    <select class="form-control" id="search_info" onchange="change_search_info()" name="search_info">
                        <option value="1"{type_search1}>{LANG.type_search1}</option>
                        <option value="2"{type_search2}>{LANG.type_search2}</option>
                        <option value="3"{type_search3}>{LANG.type_search3}</option>
                    </select>
                </div>
            </div>
            <!-- BEGIN: note_vip4 -->
            <div class="alert alert-warning" id="note_vip4">{LANG.note_vip4}</div>
            <!-- END: note_vip4 -->
            <!-- BEGIN: note_vip6 -->
            <div class="alert alert-warning" id="note_vip6">{NOTE_VIP6}</div>
            <!-- END: note_vip6 -->
            <!-- BEGIN: vip_use -->
            <div class="form-group" id="vip_use_panel">
                <label class="control-label col-sm-6 col-md-6">{LANG.filters_for}:</label>
                <div class="col-sm-18 col-md-18">
                    <select class="form-control" id="vip_use" name="vip_use" onchange="change_type_filter()">
                        <!-- BEGIN: list_vip -->
                        <option value="{LIST_VIP.key}" data-title='{LIST_VIP.title_reward}' data-vip="{LIST_VIP.status}"{LIST_VIP.selected}>{LIST_VIP.title}</option>
                        <!-- END: list_vip -->
                    </select>
                </div>
            </div>
            <!-- END: vip_use -->
            <!-- BEGIN: arr_info2 -->
            <div class="form-group" id="vip_use_panel2">
                <label class="control-label col-sm-6 col-md-6">{LANG.filters_for}:</label>
                <div class="col-sm-18 col-md-18">
                    <select class="form-control" id="vip_use2" name="vip_use2" onchange="change_type_filter2()">
                        <!-- BEGIN: list_vip -->
                        <option value="{ARR_INFO2.key}" {ARR_INFO2.selected} data-vip="{ARR_INFO2.status}" data-title='{ARR_INFO2.title_reward}'>{ARR_INFO2.title}</option>
                        <!-- END: list_vip -->
                    </select>
                </div>
            </div>
            <!-- END: arr_info2 -->
            <!-- BEGIN: arr_info3 -->
            <div class="form-group" id="vip_use_panel3">
                <label class="control-label col-sm-6 col-md-6">{LANG.filters_for}:</label>
                <div class="col-sm-18 col-md-18">
                    <select class="form-control" id="vip_use3" name="vip_use3" onchange="change_type_filter3()">
                        <!-- BEGIN: list_vip -->
                        <option value="{ARR_INFO3.key}" {ARR_INFO3.selected} data-vip="{ARR_INFO3.status}" data-title='{ARR_INFO3.title_reward}'>{ARR_INFO3.title}</option>
                        <!-- END: list_vip -->
                    </select>
                </div>
            </div>
            <!-- END: arr_info3 -->
            <div class="form-group">
                <label class="control-label col-sm-6 col-md-6">{LANG.s_key}:</label>
                <div class="col-sm-18 col-md-18">
                    <input class="form-control required" id="ls_key" type="text" name="key" value="{ROW.key_search}" />
                    <em class="help-block">{LANG.note_key}</em>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-18 col-md-18 col-sm-push-6 col-md-push-6">
                    <div id="par_search">
                        <label class="custom-checkbox toggle par_search" data-par-search="{LANG.par_search}" data-par-search2="{LANG.par_search2}"><input class="form-control" type="checkbox" name="par_search" value="1"{ROW.par_search}><span class="txt text-bold">{ROW.lang_par_search}</span></label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-18 col-md-18 col-sm-push-6 col-md-push-6">
                    <div id="search_type_content">
                        <label class="custom-checkbox toggle"><input class="form-control" type="checkbox" name="search_type_content" value="1"{ROW.search_type}><span class="txt text-bold">{LANG.search_type_sign}</span></label>
                    </div>
                </div>
            </div>
            <div class="form-group" id="searchkind">
                <label class="control-label col-sm-6 col-md-6">{LANG.searchkind}:</label>
                <div class="col-sm-18 col-md-18">
                    <!-- BEGIN: kind -->
                    <label class="custom-radio"><input type="radio" name="searchkind" value="{KIND.key}"{KIND.checked}><span class="txt">{KIND.title}</span></label>
                    <!-- END: kind -->
                </div>
            </div>
            <div class="form-group advance_search_field">
                <label class="control-label col-sm-6 col-md-6">{LANG.s_key2}:</label>
                <div class="col-sm-18 col-md-18">
                    <input class="form-control" id="ls_key2" type="text" name="key2" value="{ROW.key_search2}" />
                    <em class="help-block">{LANG.note_key2}</em>
                    <div class="row margin-top-sm">
                        <div class="col-xs-24">
                            <label class="custom-checkbox toggle"> <input class="form-control" type="checkbox" name="search_one_key" value="1" data-default="false" {ROW.search_one_key} /><span class="txt">{LANG.one_key}</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group advance_search_field">
                <label class="control-label col-sm-6 col-md-6">{LANG.without_key}:</label>
                <div class="col-sm-18 col-md-18">
                    <input class="form-control" id="without_key" type="text" name="without_key" value="{ROW.without_key}" />
                    <em class="help-block">{LANG.note_withoutkey}</em>
                </div>
            </div>
            <!-- BEGIN: show_phanmuc -->
            <div class="form-group vip_use_panel2_i vip_plan">
                <label class="control-label col-xs-12 col-sm-6">{LANG.idregion}</label>
                <div class="col-xs-12 col-sm-18">
                    <select class="form-control selRegionsFilter" name="idregion" style="width: 100%">
                        <!-- BEGIN: loopregions -->
                        <option value="{REGION.key}" data-province-ids="{REGION.province_ids}"{REGION.selected}>{REGION.title}</option>
                        <!-- END: loopregions -->
                    </select>
                </div>
            </div>
            <div class="form-group vip_use_panel2_i vip_plan" id="provinceList">
                <label class="control-label col-xs-12 col-sm-6">{LANG.idprovince}:</label>
                <div class="col-xs-12 col-sm-18">
                    <select class="form-control fselect2 selected_phanmuc" id=selProvinces name="idprovince[]" multiple="multiple" style="display: inline-block; width: 100%">
                        <!-- BEGIN: loopidprovince -->
                        <option value="{PROVINCE.key}"{PROVINCE.selected}>{PROVINCE.title}</option>
                        <!-- END: loopidprovince -->
                    </select>
                </div>
            </div>
            <div class="form-group vip_use_panel2_i">
                <label class="control-label col-xs-12 col-sm-6">{LANG.phan_muc}:</label>
                <div class="col-xs-12 col-sm-18">
                    <select class="form-control fselect2 selected_phanmuc" name="phanmucid[]" multiple="multiple" style="width: 100%">
                        <!-- BEGIN: loopphanmuc1 -->
                        <optgroup label="{PHANMUC_MAIN.title}">
                            <!-- BEGIN: loopphanmuc2 -->
                            <option value="{PHANMUC_SUB.key}"{PHANMUC_SUB.selected}>{PHANMUC_SUB.title}</option>
                            <!-- END: loopphanmuc2 -->
                        </optgroup>
                        <!-- END: loopphanmuc1 -->
                    </select>
                </div>
            </div>
            <div class="form-group vip_use_panel2_i">
                <label class="control-label col-xs-12 col-sm-6">{LANG.vsic}:</label>
                <div class="col-xs-12 col-sm-18">
                    <select class="form-control fselect2 selected_phanmuc" name="vsic[]" multiple="multiple" style="width: 100%">
                        <!-- BEGIN: loopvsic -->
                        <option value="{VSIC.code}"{VSIC.selected}><!-- BEGIN: is_code -->{VSIC.code} - <!-- END: is_code -->{VSIC.title}</option>
                        <!-- END: loopvsic -->
                    </select>
                </div>
            </div>

            <!-- END: show_phanmuc -->

            <!-- BEGIN: vip7 -->
            <div class="form-group htduthau_filter">
                <label class="control-label col-sm-6 col-md-6">{LANG.cat}:</label>
                <div class="col-sm-18 col-md-18">
                    <select class="form-control" id="ls_cat" name="cat_htduthau">
                        <option value="0">{LANG.undefined}</option>
                        <!-- BEGIN: cat -->
                        <option value="{CAT_7.key}"{CAT_7.selected}>{CAT_7.title}</option>
                        <!-- END: cat -->
                    </select>
                </div>
            </div>
            <div class="form-group type_kqlcnt_filter">
                <label class="control-label col-sm-6 col-md-6">{LANG.title_type_kqlcnt_search}:</label>
                <div class="col-sm-18 col-md-18">
                    <select class="form-control" id="type_kqlcnt" name="type_kqlcnt">
                        <option value="0">{LANG.all_kqlcnt}</option>
                        <!-- BEGIN: type_kqlcnt_link -->
                        <option value="{KQLCNT.key}"{KQLCNT.selected}>{KQLCNT.title}</option>
                        <!-- END: type_kqlcnt_link -->
                    </select>
                </div>
            </div>
            <div class="form-group htluachon_filter">
                <label class="control-label col-sm-6 col-md-6">{LANG.type_choose_invest}:</label>
                <div class="col-sm-18 col-md-18">
                    <select class="form-control" id="ls_cat" name="type_choose_id">
                        <option value="0">{LANG.all_htlc}</option>
                        <!-- BEGIN: htluachon -->
                        <option value="{HTLC.key}"{HTLC.selected}>{HTLC.title}</option>
                        <!-- END: htluachon -->
                    </select>
                </div>
            </div>
            <!-- END: vip7 -->
            <div id="bidding-filter"
                <!-- BEGIN: show_bidding -->
                style="display:none"
                <!-- END: show_bidding -->
                >
                <div>
                    <!-- BEGIN: vip5_open -->
                    <div class="form-group vip5_open other_good">
                        <label class="control-label col-sm-6 col-md-6">{LANG.vip5_open}:</label>
                        <div class="col-sm-18 col-md-18">
                            <div class="col-xs-24">
                                <input type="checkbox" id="vip5_open" name="vip5_open" value="1"{ROW.vip5_open}>
                                <em class="help-block">{LANG.note_vip5_open}</em>
                            </div>
                        </div>
                    </div>
                    <!-- END: vip5_open -->
                    <div class="form-group" id="goods_search">
                        <label class="control-label col-sm-6 col-md-6">{LANG.goods_search}:</label>
                        <div class="col-sm-18 col-md-18">
                            <!-- BEGIN: goods -->
                            <label class="custom-radio"><input type="radio" name="goods_search" value="{GOODS.key}"{GOODS.checked}><span class="txt">{GOODS.title}</span></label>
                            <!-- END: goods -->
                        </div>
                    </div>
                    <div class="form-group ls_cat_filter other_good">
                        <label class="control-label col-sm-6 col-md-6">{LANG.cat}:</label>
                        <div class="col-sm-18 col-md-18">
                            <select class="form-control" id="ls_cat" name="cat">
                                <option value="0">{LANG.undefined}</option>
                                <!-- BEGIN: cat -->
                                <option value="{CAT.key}"{CAT.selected}>{CAT.title}</option>
                                <!-- END: cat -->
                            </select>
                        </div>
                    </div>
                    <div class="form-group htluachon_filter other_good htlcnt_tbmt">
                        <label class="control-label col-sm-6 col-md-6">{LANG.type_choose_invest}:</label>
                        <div class="col-sm-18 col-md-18">
                            <select class="form-control" id="ls_cat" name="type_choose_id">
                                <option value="0">{LANG.all_htlc}</option>
                                <!-- BEGIN: htluachon -->
                                <option value="{HTLC.key}"{HTLC.selected}>{HTLC.title}</option>
                                <!-- END: htluachon -->
                            </select>
                        </div>
                    </div>

                    <div class="form-group other_good">
                        <label class="control-label col-sm-6 col-md-6">{LANG.field}:</label>
                        <div class="col-sm-18 col-md-18">
                            <!-- BEGIN: field -->
                            <label class="custom-checkbox ccb_field_{FIELD.key}"><input type="checkbox" id="ccb_field_{FIELD.key}" name="field[]" class="ccb_field_filter" value="{FIELD.key}"{FIELD.checked}><span class="txt">{FIELD.title}</span></label>
                            <!-- END: field -->
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-sm-6 col-md-6">{LANG.phuong_thuc}:</label>
                        <div class="col-sm-18 col-md-18">
                            <!-- BEGIN: phuongthuc -->
                            <div id="phuong_thuc_{PHUONGTHUC.key}">
                                <label class="custom-checkbox"> <input type="checkbox" id="phuong_thuc_{PHUONGTHUC.key}" name="phuong_thuc[]" class="phuong_thuc" value="{PHUONGTHUC.key}"{PHUONGTHUC.checked}> <span class="txt">{PHUONGTHUC.title}</span>
                                </label>
                            </div>
                            <!-- END: phuongthuc -->
                        </div>
                    </div>

                    <!-- Trường lĩnh vực của bảng Kết quả LCNT -->
                    <div id="bidfieid_group" class="form-group">
                        <label class="control-label col-sm-6 col-md-6">{LANG.field}:</label>
                        <div class="col-sm-18 col-md-18">
                            <!-- BEGIN: bidfieid -->
                            <div id="kqlcnt_field_{BIDFIEID.key}">
                                <label class="custom-checkbox"> <input type="checkbox" id="kqlcnt_field_{BIDFIEID.key}" name="bidfieid[]" class="bidfieid" value="{BIDFIEID.key}"{BIDFIEID.checked}> <span class="txt">{BIDFIEID.title}</span>
                                </label>
                            </div>
                            <!-- END: bidfieid -->
                        </div>
                    </div>


                </div>
                <div class="form-group type_org_filter other_good">
                    <label class="control-label col-sm-6 col-md-6">{LANG.type_org}:</label>
                    <div class="col-sm-18 col-md-18">
                        <!-- BEGIN: org -->
                        <label class="custom-radio"><input type="radio" name="type_org" value="{ORG.key}"{ORG.checked}><span class="txt">{ORG.title}</span></label>
                        <!-- END: org -->
                    </div>
                </div>
                <div class="form-group other_good">
                    <label class="control-label col-sm-6 col-md-6">{LANG.moneybid}:</label>
                    <div class="col-sm-18 col-md-18">
                        <div class="row">
                            <div class="col-xs-12">
                                <input class="form-control money-format" id="money_from" placeholder="{LANG.from}" type="text" name="money_from" onkeyup="this.value=FormatMoney(this.value);" value="{ROW.money_from}" />
                            </div>
                            <div class="col-xs-12">
                                <input class="form-control money-format" id="money_to" placeholder="{LANG.to}" type="text" name="money_to" onkeyup="this.value=FormatMoney(this.value);" value="{ROW.money_to}" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group other_good">
                    <label class="control-label col-sm-6 col-md-6">{LANG.price_bid}:</label>
                    <div class="col-sm-18 col-md-18">
                        <div class="row">
                            <div class="col-xs-12">
                                <input class="form-control money-format" id="price_from" placeholder="{LANG.from}" type="text" onkeyup="this.value=FormatMoney(this.value);" name="price_from" value="{ROW.price_from}" />
                            </div>
                            <div class="col-xs-12">
                                <input class="form-control money-format" id="price_to" placeholder="{LANG.to}" type="text" onkeyup="this.value=FormatMoney(this.value);" name="price_to" value="{ROW.price_to}" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="plan-filter"
                <!-- BEGIN: show_plan -->
                style="display:none"
                <!-- END: show_plan -->
                >
                <div class="form-group price_invest">
                    <label class="control-label col-sm-6 col-md-6">{LANG.total_invest}:</label>
                    <div class="col-sm-18 col-md-18">
                        <div class="row">
                            <div class="col-xs-12">
                                <input class="form-control money-format" id="invest_from" placeholder="{LANG.from}" type="text" name="invest_from" onkeyup="this.value=FormatMoney(this.value);" value="{ROW.invest_from}" />
                            </div>
                            <div class="col-xs-12">
                                <input class="form-control money-format" id="invest_to" placeholder="{LANG.to}" type="text" name="invest_to" onkeyup="this.value=FormatMoney(this.value);" value="{ROW.invest_to}" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group price_contract">
                    <label class="control-label col-sm-6 col-md-6">{LANG.price_contract}:</label>
                    <div class="col-sm-18 col-md-18">
                        <div class="row">
                            <div class="col-xs-12">
                                <input class="form-control money-format" id="price_plan_from" placeholder="{LANG.from}" type="text" onkeyup="this.value=FormatMoney(this.value);" name="price_plan_from" value="{ROW.price_plan_from}" />
                            </div>
                            <div class="col-xs-12">
                                <input class="form-control money-format" id="price_plan_to" placeholder="{LANG.to}" type="text" onkeyup="this.value=FormatMoney(this.value);" name="price_plan_to" value="{ROW.price_plan_to}" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group win_price">
                    <label class="control-label col-sm-6 col-md-6">{LANG.win_price}:</label>
                    <div class="col-sm-18 col-md-18">
                        <div class="row">
                            <div class="col-xs-12">
                                <input class="form-control money-format" id="win_price_from" placeholder="{LANG.from}" type="text" onkeyup="this.value=FormatMoney(this.value);" name="win_price_from" value="{ROW.win_price_from}" />
                            </div>
                            <div class="col-xs-12">
                                <input class="form-control money-format" id="win_price_to" placeholder="{LANG.to}" type="text" onkeyup="this.value=FormatMoney(this.value);" name="win_price_to" value="{ROW.win_price_to}" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="dau-gia-filter"
                <!-- BEGIN: show_dau_gia -->
                style="display:none"
                <!-- END: show_dau_gia -->
                >
                <div class="form-group" id="gr_organization">
                    <label class="control-label col-sm-6 col-md-6">{LANG.organization}</label>
                    <div class="col-sm-18 col-md-18">
                        <select name="keyword_id_bidder" class="form-control">
                            <option value="">{LANG.all}</option>
                            <!-- BEGIN: bidder -->
                            <option value="{BIDDER.key}"{BIDDER.selected}>{BIDDER.name_bidder}</option>
                            <!-- END: bidder -->
                        </select>
                    </div>
                </div>

                <div class="form-group" id="gr_price">
                    <label class="control-label col-sm-6 col-md-6">{LANG.price_dau_gia}</label>
                    <div class="col-sm-9 col-md-9 mt-1">
                        <div class="input-group">
                            <span class="input-group-addon"><span>{LANG.from}:</span></span>
                            <input class="form-control money-format" id="keyword_min_bid_prices" type="text" data-name="keyword_min_bid_prices" name="keyword_min_bid_prices" value="{ROW.price_from}" data-default="" maxlength="20" />
                        </div>
                    </div>
                    <div class="col-sm-9 col-md-9 mt-1">
                        <div class="input-group">
                            <span class="input-group-addon"><span>{LANG.to}:</span></span>
                            <input name="keyword_max_bid_prices" class="form-control money-format" id="keyword_max_bid_prices" type="text" data-name="keyword_max_bid_prices" value="{ROW.price_to}" data-default="" maxlength="20" />
                        </div>
                    </div>
                </div>

                <div class="form-group row " id="gr_location">
                    <label class="control-label col-sm-6 col-md-6">{LANG.location}</label>
                    <div class="col-sm-9 col-md-9 mt-1">
                        <select disabled="disabled" data-selected="{ROW.id_province}" name="keyword_id_province" id="keyword_id_province_filter" class="form-control" data-default="{LANG.idprovince}" data-timestamp="{TIMESTAMP}">
                            <option value="0">{LANG.idprovince}</option>
                        </select>
                    </div>
                    <div class="col-sm-9 col-md-9 mt-1">
                        <select disabled="disabled" data-selected="{ROW.id_district}" name="keyword_id_district" id="keyword_id_district_filter" class="form-control" data-default="{LANG.iddistrict}" data-timestamp="{TIMESTAMP}">
                            <option value="0">{LANG.iddistrict}</option>
                        </select>
                    </div>
                </div>
            </div>

            <div id="request-quote">
                <div class="form-group" id="rq_form_value">
                    <label class="control-label col-sm-6 col-md-6">{LANG.rq_form_value_search}:</label>
                    <div class="col-sm-18 col-md-18">
                        <!-- BEGIN: rq_form_value -->
                        <label class="custom-radio"><input type="radio" name="rq_form_value" value="{FORM_VALUE.key}" data-default="0" {FORM_VALUE.checked}><span class="txt">{FORM_VALUE.title}</span></label>
                        <!-- END: rq_form_value -->
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-sm-6 col-md-6">{LANG.rq_investor}:</label>
                    <div class="col-sm-18 col-md-18">
                        <select class="form-control" id="rq_investor" multiple="multiple" name="rq_investor[]" style="width: 100%">
                            <!-- BEGIN: loop_ycbg -->
                            <option value="{RQ_INVESTOR.key}"{RQ_INVESTOR.selected}>{RQ_INVESTOR.title}</option>
                            <!-- END: loop_ycbg -->
                        </select>
                        <em class="help-block">{LANG.note_investor}</em>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-sm-6 col-md-6">{LANG.location}:</label>
                    <div class="col-sm-18 col-md-18">
                        <div class="row">
                            <div class="col-sm-8 col-md-8">
                                <select disabled="disabled" data-selected="{ROW.id_province}" name="keyword_id_province" id="rq_province" class="form-control" data-default="{LANG.pleaseselect}" data-timestamp="{TIMESTAMP}" data-placeholder="{LANG.pleaseselect}" data-no_title="{LANG.no_title}">
                                    <option value="-1">{LANG.pleaseselect}</option>
                                </select>
                            </div>
                            <div class="col-sm-8 col-md-8" id="district_container" style="display: none;">
                                <select disabled="disabled" data-selected="{ROW.id_district}" name="keyword_id_district" id="rq_district" class="form-control" data-default="{LANG.pleaseselect}" data-timestamp="{TIMESTAMP}" data-placeholder="{LANG.pleaseselect}" data-no_title="{LANG.no_title}">
                                    <option value="0">{LANG.pleaseselect}</option>
                                </select>
                            </div>
                            <div class="col-sm-8 col-md-8" id="ward_container" style="display: none;">
                                <select disabled="disabled" data-selected="{ROW.id_ward}" name="keyword_id_ward" id="rq_ward" class="form-control" data-default="{LANG.pleaseselect}" data-timestamp="{TIMESTAMP}" data-placeholder="{LANG.pleaseselect}" data-no_title="{LANG.no_title}">
                                    <option value="0">{LANG.pleaseselect}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group text-center">
                <a id="preview-button" class="btn btn-default">{LANG.preview_filter}<i class="fa fa-external-link"></i></a>
                <input class="btn btn-primary btnSubmit" name="submit" type="submit" value="{LANG.save_filter}" />
                <div class="red text_reward"></div>
            </div>
        </form>
        
        <form id="preview-form" target="_blank"></form>
    </div>
</div>
<script type="text/javascript">
var fobj = $("form.filters-form");
function change_search_info() {
    var type = $('#search_info').val();
    var par_search = $(".par_search").data("par-search");
    var par_search2 = $(".par_search").data("par-search2");
    if (type == 2) {
        $('#vip_use_panel2').css("display", "block");
        $('.vip_use_panel2_i').css("display", "none");
        $('#vip_use_panel').css("display", "none");
        $('#vip_use_panel3').css("display", "none");
        $('#note_vip4').css("display", "block");
        $('#note_vip6').css("display", "none");
        $('#search_type_content').css("display", "none");
        $('#searchkind').css("display", "none");
        $('#goods_search').css("display", "none");
        $(".par_search .txt").text(par_search);
        $('#request-quote').css("display", "none");
        change_type_filter2();
    } else if (type == 3) {
        $('#vip_use_panel3').css("display", "block");
        $('#vip_use_panel').css("display", "none");
        $('#vip_use_panel2').css("display", "none");
        $('.vip_use_panel2_i').css("display", "none");
        $('#note_vip6').css("display", "block");
        $('#note_vip4').css("display", "none");
        $('#search_type_content').css("display", "block");
        $('#searchkind').css("display", "block");
        $('#goods_search').css("display", "none");
        $(".par_search .txt").text(par_search2);
        $('#request-quote').css("display", "none");
        change_type_filter3();
    } else {
        $('#vip_use_panel').css("display", "block");
        $('#vip_use_panel2').css("display", "none");
        $('#vip_use_panel2_i').css("display", "block");
        $('#vip_use_panel3').css("display", "none");
        $('#note_vip4').css("display", "none");
        $('#note_vip6').css("display", "none");
        $('#search_type_content').css("display", "block");
        $('#searchkind').css("display", "block");
        $('#goods_search').css("display", "block");
        $(".par_search .txt").text(par_search);
        change_type_filter();
    }
    remove_field_filter();
}

function remove_field_filter() {
    var search_info = $('#search_info').val();
    if (search_info == 2) {
        $(".ccb_field_1").css("display", "none");
        $(".ccb_field_2").css("display", "none");
        $(".ccb_field_3").css("display", "none");
        $(".ccb_field_4").css("display", "none");
        $(".ccb_field_5").css("display", "none");

        $(".ccb_field_7").css("display", "block");
        $(".ccb_field_8").css("display", "block");

        $('.type_org_filter').css("display", "none");
        $('.ls_cat_filter').css("display", "none");
    } else if (search_info == 3) {
        $('.htduthau_filter').css("display", "none");
        $('.type_kqlcnt_filter').css("display", "none");
        $('.htluachon_filter').css("display", "none");
    } else {
        $(".ccb_field_7").css("display", "none");
        $(".ccb_field_8").css("display", "none");

        $(".ccb_field_1").css("display", "block");
        $(".ccb_field_2").css("display", "block");
        $(".ccb_field_3").css("display", "block");
        $(".ccb_field_4").css("display", "block");
        $(".ccb_field_5").css("display", "block");

        $('.type_org_filter').css("display", "block");
        $('.ls_cat_filter').css("display", "block");
    }
    <!-- BEGIN: remove_field -->
    $(".ccb_field_filter").prop('checked', false);
    <!-- END: remove_field -->
}

function change_type_filter3() {
    var type = $('#vip_use3').val();
    $('#bidding-filter').css("display", "none");
    $('#plan-filter').css("display", "none");
    $('.price_contract').css("display", "none");
    $('#dau-gia-filter').css("display", "block");
    if (type == 2) {
        $('#gr_location').css("display", "block");
        $('#gr_price').css("display", "block");
        $('#gr_organization').css("display", "none");
    } else {
        $('#gr_location').css("display", "block");
        $('#gr_price').css("display", "block");
        $('#gr_organization').css("display", "block");
    }
    checkBtnSubmit("#vip_use3");
}

function change_type_filter2() {
    var type = $('#vip_use2').val();
    $('#dau-gia-filter').css("display", "none");
    
    // ẩn một số trường tìm kiếm nâng cao
    if ([1, 3, 6, 8].indexOf(parseInt(type)) > -1) {
        $('.advance_search_field').css("display", "none");
    }
    
    if (type == 2) {
        $('#bidding-filter').css("display", "block");
        $('#plan-filter').css("display", "none");
    } else if (type == 4) {
        $('#bidding-filter').css("display", "none");
        $('#plan-filter').css("display", "block");
        $('.price_contract').css("display", "none");
    } else {
        $('#bidding-filter').css("display", "none");
        $('#plan-filter').css("display", "none");
    }
    checkBtnSubmit("#vip_use2");
}

function nv_change_weight(obj, id, userid) {
    var a = $(obj).val();
    $.post(script_name + "?" + nv_name_variable + "=" + nv_module_name + "&" + nv_fc_variable + "=filters&nocache=" + (new Date).getTime(), "ajax_action=1&userid=" + userid + "&id=" + id + "&new_vid=" + a, function(a) {
        window.location.href = window.location.href;
    })
}

function nv_change_status(ob, id, userid) {
    var b = $(ob).is(":checked") ? !0 : !1,
        a = b ? fobj.attr("data-sendmail-confirm") : fobj.attr("data-unsendmail-confirm");
    if ($(ob).closest('tr').find('.content_renewal').length > 0) {
        modalShow('', $(ob).closest('tr').find('.content_renewal').html());
        return false;
    }

    bootbox.confirm(a, function(a){
      a ? ($(ob).prop("disabled", !0), $.post(script_name + "?" + nv_name_variable + "=" + nv_module_name + "&" + nv_fc_variable + "=filters&nocache=" + (new Date).getTime(), "change_status=1&userid=" + userid + "&id=" + id + "&view_customs={VIEW_CUSTOMS}", function(res) {
        $(ob).prop("disabled", !1);
        if (res != 'OK') {
            bootbox.alert(res);
            $(ob).prop("checked", 0);
        }
        location.reload();
      })) : $(ob).prop("checked", b ? !1 : !0)
    })
}

function nv_change_status_advance(ob, id, userid) {
    var b = $(ob).is(":checked") ? !0 : !1,
        a = b ? '{LANG.send_excel_confirm}' : '{LANG.unsend_excel_confirm}';
    bootbox.confirm(a, function(a){
      a ? ($(ob).prop("disabled", !0), $.post(script_name + "?" + nv_name_variable + "=" + nv_module_name + "&" + nv_fc_variable + "=filters&nocache=" + (new Date).getTime(), "change_status_advance=1&userid=" + userid + "&id=" + id + "&view_customs={VIEW_CUSTOMS}", function(res) {
        $(ob).prop("disabled", !1);
        if (res != 'OK') {
            bootbox.alert(res);
            $(ob).prop("checked", 0);
        }
      })) : $(ob).prop("checked", b ? !1 : !0)
    })
}

function change_type_filter() {
    $('#dau-gia-filter').css("display", "none");
    $('.htduthau_filter').css("display", "none");
    $('.htluachon_filter').css("display", "none");
    $('.type_kqlcnt_filter').css("display", "none");
    var type = $('#vip_use').val();
    $('.price_invest').css("display", "block");
    $('.win_price').css("display", "none");
    $('.vip_use_panel2_i').css("display", "none");
    $('.other_good').css("display", "block");
    $('#request-quote').css("display", "none");

    if (type == 1 || type == 11 || type == 5 || type == 19) {
        if (type != 5) {
            $('.vip5_open').css("display", "none");
            if(type == 1 || type == 19){
                $('.vip_use_panel2_i').css("display", "block");
            }
        } else {
            $('.vip5_open').css("display", "block");
        }

        $('#bidding-filter').css("display", "block");
        $('#plan-filter').css("display", "none");
    } else {
        //Show tỉnh
        $('#bidding-filter').css("display", "none");
        $('#plan-filter').css("display", "block");
        $('.vip_plan').css("display", "block");
    }

    if (type == 11 || type == 21) {
        $('#ls_key').removeClass('required');
    } else {
        $('#ls_key').addClass('required');
    }
    if (type == 7) {
        //Show tỉnh
        $('#provinceList').css("display", "block");

        //Chỉ show hàng hóa
        $('#bidding-filter').css("display", "block");
        $('.other_good').css("display", "none");
        $('.price_invest').css("display", "none");
        $('.htduthau_filter').css("display", "block");
        $('.htluachon_filter').css("display", "block");
        $('.htlcnt_tbmt').css("display", "none");
        $('.win_price').css("display", "block");
        $('.type_kqlcnt_filter').css("display", "block");
        $('#bidfieid_group').css("display", "block");
    } else {
        $('#bidfieid_group').css("display", "none");
    }

    if (type == 8) {
        $('#bidding-filter').css("display", "none");
        $('#plan-filter').css("display", "none");
        $('#provinceList').css("display", "none");
        $('.vip_plan').css("display", "none");
        $('#request-quote').css("display", "block");
        $('#search_type_content').css("display", "none");
    } else {
        $('#search_type_content').css("display", "block");
    }

    checkBtnSubmit("#vip_use");
}


function checkBtnSubmit(id) {
    row = '{ROW.id}';
    if (parseInt($('option:selected', id).attr('data-vip')) != 1 && row == 0) {
        $(".btnSubmit").attr("disabled", true);
        if ($('option:selected', id).attr('data-title') != '') {
            $(".text_reward").html(JSON.parse($('option:selected', id).attr('data-title')));
        }
    } else {
        $(".btnSubmit").attr("disabled", false);
        $(".text_reward").html("");
    }
}

function click_use_point(id, userid, checksess, filter_title, vip_use) {
    if (parseInt(fobj.attr("data-not-points")) == 1) {
       bootbox.confirm(fobj.attr("data-add-points-confirm"), function(result){
         if (result) {
            window.location.href = fobj.attr("data-url-points")
         }
       })
    } else {
        bootbox.confirm(fobj.attr("data-sendmail-points-confirm") + '<br/><input style="width:100px" type="number" name="month_point" id="month_point" value="1" min="1" max="12" > {LANG.point_confirm_month}', function(result){
        var month_point = $('#month_point').val();
        if (result) {
            if (({CURRENT_POINT}/{MAIL_POINT}) < month_point) {
                bootbox.alert(fobj.attr("data-sendmail-points-err"))
                return false;
            } else {
                $.post(script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=filters&nocache=' + new Date().getTime(), 'point=1&userid=' + userid + '&id=' + id + '&filter_title=' + filter_title + '&checksess=' + checksess + '&vip_use=' + vip_use+'&month_point=' + month_point, function(res) {
                    if (res != 'OK') {
                        bootbox.alert(fobj.attr("data-sendmail-points-err"))
                        return false;
                    } else {
                        bootbox.alert({
                            message: fobj.attr("data-sendmail-points-ok"),
                            onHidden: function(e) {
                                location.reload()
                            }
                        })
                    }
                });
            }
         }
       })
    }
    return false;
}

function click_use_point_excel(id, userid, checksess, filter_title, vip_use) {
    if ("{NOT_POINT_EXCEL}" == 1) {
       bootbox.confirm("{LANG_NOT_POINT_EXCEL}", function(result){
         if (result) {
            window.location.href = fobj.attr("data-url-points")
         }
       })
    } else {
        bootbox.confirm("{POINT_EXCEL_CONFIRM}" + '<br/><input style="width:100px" type="number" name="month_point" id="month_point" value="1" min="1" max="12" > {LANG.point_confirm_month}', function(result){
        var month_point = $('#month_point').val();
        if (result) {
            if (({CURRENT_POINT}/{EXCEL_POINT}) < month_point) {
                bootbox.alert(fobj.attr("{LANG.point_excel_confirm_err}"))
                return false;
            } else {
                $.post(script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=filters&nocache=' + new Date().getTime(), 'point_excel=1&userid=' + userid + '&id=' + id + '&filter_title=' + filter_title + '&checksess=' + checksess + '&vip_use=' + vip_use+'&month_point=' + month_point, function(res) {
                    if (res != 'OK') {
                        bootbox.alert('{LANG.point_excel_confirm_err}')
                        return false;
                    } else {
                        bootbox.alert({
                            message: "{LANG.point_excel_confirm_ok}",
                            onHidden: function(e) {
                                location.reload()
                            }
                        })
                    }
                });
            }
         }
       })
    }
    return false;
}

function filler_point(userid, checksess, vip, is_vip){
    var not_point = '{NOT_POINT_FILTER}';
    if (not_point == 1) {
        if (confirm('{LANG_NOT_POINT_FILTER}')) {
          window.location.href = '{URL_POINTS}';
      }
    } else {
        if (confirm('{POINT_CONFIRM_FILTER}')) {
            $.post(script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=filters&nocache=' + new Date().getTime(), 'point_filter=1&userid='+userid+'&checksess='+checksess +'&vip='+vip+'&is_vip='+is_vip+'&view_customs={VIEW_CUSTOMS}', function(res) {
                if (res != 'OK') {
                    alert('{LANG.point_confirm_err}');
                    return false;
                } else {
                    alert('{LANG.point_filter_confirm_ok}');
                    window.location.href = window.location.href;
                }
            });
      }
    }
    return false;
 }

function add_filter_show(){
    $('#add_filter').css("display", "block");
    $('html, body').animate({
      scrollTop: $("#add_filter").offset().top
    }, 800);
}
$(function() {
    change_search_info();
    $('#from_time_btn').click(function() {
        $("#ls_from").datepicker('show');
    });
    $('#to_time_btn').click(function() {
        $("#ls_to").datepicker('show');
    });
    $('.open_form').click(function(e) {
        add_filter_show();
    });
    <!-- BEGIN: add_filter_show -->add_filter_show();<!-- END: add_filter_show -->
});


for (i = 0; i < $(".dateexpired_old").length; i++) {
    if($(".dateexpired_old").eq(i).length > 0) {
        $(".dateexpired_old").eq(i).parent().parent().find(".dateexpired_new").html("<span class='text__hethan'> - {LANG.email_sending_time} " + $(".dateexpired_old").text() + "</span>");
        $(".dateexpired_old").eq(i).remove();

        $(".text__hethan").css({
            'background' : '#ffffff',
            'margin-left' : '5px',
            'border-left' : '2px solid #ffc800',
            'padding' : '5px 6px'
        })
    }
}

$(document).ready(function() {
    function initLocationSelectors(provinceSelector, districtSelector, wardSelector) {
        if (!$(provinceSelector).length) {
            return;
        }

        var selProvince = $(provinceSelector);
        var selDistrict = districtSelector ? $(districtSelector) : null;
        var selWard = wardSelector ? $(wardSelector) : null;

        // Load ra tỉnh
        function loadProvince() {
            selProvince.prop('disabled', true);
            if (selDistrict) selDistrict.prop('disabled', true);
            if (selWard) selWard.prop('disabled', true);

            $.ajax({
                url: domain_load_remote + 'data/config/location-province-' + nv_lang_interface + '.json?t=' + selProvince.data('timestamp'),
                cache: true,
                dataType: 'json',
                method: 'GET',
                success: function(json) {
                    var html = '<option value="0">' + selProvince.data('default') + '</option>';

                    $.each(json, function(id, title) {
                        html += '<option value="' + id + '"' + (id == selProvince.data('selected') ? ' selected="selected"' : '') + '>' + title + '</option>';
                    });

                    selProvince.html(html);
                    selProvince.prop('disabled', false);
                    selProvince.select2({
                        width: '100%',
                        placeholder: selProvince.data('placeholder'),
                        allowClear: true,
                        language: nv_lang_interface
                    });
                    loadDistrict();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR, textStatus, errorThrown);
                }
            });
        }

        // Load ra huyện
        function loadDistrict() {
            if (!selDistrict) return;

            selDistrict.prop('disabled', true);
            if (selWard) selWard.prop('disabled', true);

            if (selWard && $('#ward_container').length) {
                $('#ward_container').hide();
            }

            if (selProvince.val() <= 0) {
                selDistrict.data('selected', 0);

                if ($('#district_container').length) {
                    $('#district_container').hide();
                }

                selDistrict.prop('disabled', false);
                selDistrict.html('<option value="0">' + selDistrict.data('default') + '</option>').select2({
                    width: '100%',
                    placeholder: selDistrict.data('placeholder'),
                    allowClear: true,
                    language: nv_lang_interface
                });
                loadWard();
                return;
            }

            if ($('#district_container').length) {
                $('#district_container').show();
            }

            $.ajax({
                url: domain_load_remote + 'data/config/location-district-' + nv_lang_interface + '.json?t=' + selDistrict.data('timestamp'),
                cache: true,
                dataType: 'json',
                method: 'GET',
                success: function(json) {
                    var html = '<option value="0">' + selDistrict.data('default') + '</option>';
                    if (typeof json[selProvince.val()] != 'undefined') {
                        json = json[selProvince.val()];
                        $.each(json, function(id, title) {
                            html += '<option value="' + id + '"' + (id == selDistrict.data('selected') ? ' selected="selected"' : '') + '>' + title + '</option>';
                        });
                    }
                    selDistrict.prop('disabled', false);
                    selDistrict.html(html).select2({
                        width: '100%',
                        placeholder: selDistrict.data('placeholder'),
                        allowClear: true,
                        language: nv_lang_interface
                    });
                    loadWard();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR, textStatus, errorThrown);
                }
            });
        }

        // Load ra xã
        function loadWard() {
            if (!selWard) return;

            selWard.prop('disabled', true);

            if (selDistrict.val() == 0) {
                selWard.data('selected', 0);

                if ($('#ward_container').length) {
                    $('#ward_container').hide();
                }

                selWard.prop('disabled', false);
                selWard.html('<option value="0">' + (selWard.data('default')) + '</option>').select2({
                    width: '100%',
                    placeholder: selWard.data('placeholder'),
                    allowClear: true,
                    language: nv_lang_interface
                });
                return;
            }

            if ($('#ward_container').length) {
                $('#ward_container').show();
            }

            $.ajax({
                url: domain_load_remote + 'data/config/location-ward-' + nv_lang_interface + '.json?t=' + selWard.data('timestamp'),
                cache: true,
                dataType: 'json',
                method: 'GET',
                success: function(json) {
                    var html = '<option value="0">' + (selWard.data('default')) + '</option>';
                    if (typeof json[selDistrict.val()] != 'undefined') {
                        json = json[selDistrict.val()];
                        $.each(json, function(id, title) {
                            html += '<option value="' + id + '"' + (id == selWard.data('selected') ? ' selected="selected"' : '') + '>' + title + '</option>';
                        });
                    }
                    selWard.prop('disabled', false);
                    selWard.html(html).select2({
                        width: '100%',
                        placeholder: selWard.data('placeholder'),
                        allowClear: true,
                        language: nv_lang_interface
                    });
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR, textStatus, errorThrown);
                }
            });
        }

        // Khởi tạo
        if (selProvince.length && (!selDistrict || selDistrict.length) && (!selWard || selWard.length)) {
            loadProvince();

            // Kiểm tra nếu đã có giá trị được chọn khi tải trang
            if (selProvince.data('selected') > 0) {
                if ($('#district_container').length) {
                    $('#district_container').show();
                }
            }

            if (selDistrict && selDistrict.data('selected') > 0) {
                if ($('#ward_container').length) {
                    $('#ward_container').show();
                }
            }
        }

        // Xử lý sự kiện thay đổi
        selProvince.on('change', function() {
            loadDistrict();
        });

        if (selDistrict) {
            selDistrict.on('change', function() {
                loadWard();
            });
        }
    }

    // Khởi tạo cho keyword_id_province_filter (phần cũ)
    initLocationSelectors('#keyword_id_province_filter', '#keyword_id_district_filter', null);

    // Khởi tạo cho rq_province (phần mới)
    initLocationSelectors('#rq_province', '#rq_district', '#rq_ward');

    $('.selRegionsFilter').on('change', () => {
        let provinceIds = $('.selRegionsFilter').find(":selected").attr('data-province-ids');
        if (provinceIds) {
            let provinceArray = JSON.parse("[" + provinceIds + "]");
            $('#selProvinces').val(provinceArray).trigger('change');
        } else {
            $('#selProvinces').val([]).trigger('change');
        }
    })

    // Nếu chọn tìm theo tên hoặc mã thì không tìm theo hàng hóa và ngược lại
    $("[name=par_search]").click(function() {
        if ($(this).is(':checked') && $('[name=goods_search]:checked').val() > 0) {
            $("[name=goods_search][value=0]").prop("checked", true);
        }
        if ($(this).is(':checked') && $('[name=rq_form_value]:checked').val() > 0) {
            $("[name=rq_form_value][value=0]").prop("checked", true);
        }
    });
    $("[name=goods_search]").click(function() {
        if ($(this).val() > 0 && $('[name=par_search]').is(':checked')) {
            $("[name=par_search]").prop("checked", false);
        }
    });
    $("[name=rq_form_value]").click(function() {
        if ($(this).val() > 0 && $('[name=par_search]').is(':checked')) {
            $("[name=par_search]").prop("checked", false);
        }
    });

    $('[data-tip="filtertip"]').tooltip();

    $('#rq_investor').select2({
        tags: true,
        placeholder: '',
        allowClear: true,
        createTag: function(params) {
            // Kiểm tra nếu giá trị của thẻ không được rỗng
            if ($.trim(params.term) === '') {
                return null; // Không tạo thẻ nếu giá trị rỗng
            }

            // Kiểm tra độ dài của thẻ không vượt quá 200 ký tự
            if (params.term.length > 200) {
                alert('{LANG.rq_investor_length_alert}');
                return null; // Ngăn không cho tạo thẻ nếu vượt quá giới hạn
            }

            // Kiểm tra nếu đã có 5 thẻ được tạo
            var existingTags = $('#rq_investor').select2('data');
            if (existingTags.length >= 5) {
                alert('{LANG.rq_investor_max_num_alert}');
                return null; // Ngăn không cho tạo thêm thẻ
            }

            return {
                id: params.term,
                text: params.term,
                newTag: true
            };
        }
    });
    
    window.Action = {
        khlcnt: '{ACTION_KHLCNT}',
        kqlcnt: '{ACTION_KQLCNT}',
        ycbg: '{ACTION_YCBG}',
        tbmt: '{ACTION_TBMT}',
        
        duan: '{ACTION_DUAN}',
        tbmdt: '{ACTION_TBMDT}',
        mstndt: '{ACTION_MSTNDT}',
        khlcndt: '{ACTION_KHLCNDT}',
        kqlcndt: '{ACTION_KQLCNDT}',
        kqstndt: '{ACTION_KQSTNDT}',
        
        tbdg: '{ACTION_TB_DG}',
        tbtcdg: '{ACTION_TB_TCDG}',
    }
});
</script>
<!-- END: main -->
