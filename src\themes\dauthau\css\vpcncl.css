#vpcncl .card-img-top {
    width: 100%;
    margin-bottom: 10px;
}

#vpcncl .card {
    margin-bottom: 20px;
}

.table-orgs .stt {
    width: 50px;
}

.table-orgs {
    border-collapse: collapse;
}

.table-orgs th{
    background-color: #0685d6;
    color: #fff;
    text-transform: uppercase;
    font-weight: normal;
}

.table-orgs th,
.table-orgs td {
    padding: 10px;
    border: 1px #e6e6e6 solid;
}

.table-orgs th,
.table-orgs td:first-child,
.table-orgs td:last-child {
    text-align: center;
}

#list-search-orgs.filter-vicas .host,
#list-search-orgs.filter-vias .linhvuc,
#list-search-orgs.filter-vias .host{
    display: none;
}

#list-search-orgs .select2-selection{
    min-height: 38px;
}

#list-search-orgs textarea.select2-search__field{
    margin-left: 10px;
    margin-top: 10px;
    font-family: inherit;
    height: 22px;
    display: inline;
}

#list-search-orgs input[type="submit"]{
    max-width: 200px;
}

#list-search-orgs h1{
    margin-bottom: 15px;
}

.table-orgs .hide-on-mobile{
    display: table-cell;
}

.table-orgs .show-on-mobile{
    display: none;
}

#org-content{
    overflow: auto;
}

@media screen and (max-width: 768px) {
    .table-orgs .hide-on-mobile{
        display: none;
    }
    .table-orgs .show-on-mobile{
        display: block;
        margin-top: 10px;
    }
}

.select2-dropdown {
    z-index: 999 !important;
}

.organization_label {
    user-select: none;
    text-transform: uppercase;
}

#list-search-orgs .select2-container {
    width: 100% !important;
}

#list-search-orgs .form-group .select2-container--default .select2-selection--single,
#list-search-orgs .form-group .select2-container--default .select2-selection--multiple {
    width: 100% !important;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

#list-search-orgs .select2-container .select2-selection--single {
    height: calc(1.5em + 0.75rem + 2px);
}

#list-search-orgs .select2-container--default .select2-selection--single .select2-selection__placeholder {
    line-height: calc(1.5em + 0.75rem);
}

.title__tab_heading span {
    border-bottom: 4px #0685d6 solid;
    text-transform: uppercase;
    font-size: 20px;
    color: #0685d6;
    font-weight: 500;
    padding-bottom: 3px;
    display: inline-block;
}

.title__tab_heading span::before {
    display: inline-block;
    font-family: FontAwesome;
    font-size: 14px;
    line-height: 1;
    vertical-align: middle;
    margin-right: 5px;
    content: " ";
    color: #d61c1d;
}
