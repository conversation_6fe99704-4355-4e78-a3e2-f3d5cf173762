#!/bin/bash

# Tạo file upload_file_server.ip lưu IP của server
# Tr<PERSON>ớ<PERSON> khi chạy scp cần chạy đoạn sau để copy SSH key (IP_SERVER = IP của server)
# ssh-copy-id -i ~/.ssh/id_rsa dauthau@IP_SERVER

# Chặn lưu lịch sử các lệnh trong này
set +o history

SOURCE="${BASH_SOURCE[0]}"
while [ -h "$SOURCE" ]; do
    TARGET="$(readlink "$SOURCE")"
    if [[ $TARGET == /* ]]; then
        #echo "SOURCE '$SOURCE' is an absolute symlink to '$TARGET'"
        SOURCE="$TARGET"
    else
        DIR="$(dirname "$SOURCE")"
        #echo "SOURCE '$SOURCE' is a relative symlink to '$TARGET' (relative to '$DIR')"
        SOURCE="$DIR/$TARGET"
    fi
done
#echo "SOURCE is '$SOURCE'"
RDIR="$(dirname "$SOURCE")"
DIR="$(cd -P "$(dirname "$SOURCE")" >/dev/null 2>&1 && pwd)"
#if [ "$DIR" != "$RDIR" ]; then
#echo "DIR '$RDIR' resolves to '$DIR'"
#fi
#echo "DIR is '$DIR'"

cd "$DIR/../"
DIR_PATH=$PWD

# Hỏi pull về không?
GIT_PULL=0
while true; do
    read -p "Pull về không? [y(mặc định)/n]? " yn
    if [[ "$yn" = "y" || "$yn" = "Y" || "$yn" = "" ]]; then
        GIT_PULL=1
        break
    elif [[ "$yn" = "n" || "$yn" = "N" ]]; then
        break
    else
        echo "Vui lòng nhập y hoặc n hoặc để trống"
    fi
done
if [[ "$GIT_PULL" -eq 1 ]]; then
    git pull
    if [[ $? -gt 0 ]]; then
        echo "Lỗi pull kiểm tra lại"
        read -p ""
        exit
    fi
fi

if [ -f "$DIR_PATH/vinades/upload_file_server.txt" ]; then
    GIT_ID_OLD=$(cat "$DIR_PATH/vinades/upload_file_server.txt")
else
    echo "NOT GIT_ID_OLD"
    read -p ""
    exit
fi
SERVER_IP=$(cat "$DIR_PATH/vinades/upload_file_server.ip")
echo "SERVER_IP: $SERVER_IP"

GIT_BRANCH=$(git branch --show-current)
if [[ $GIT_BRANCH != "master" ]]; then
    echo "Cần chạy tool ở nhánh master"
    exit
fi

GIT_ID_NEW=$(git log --format="%H" -n 1)
if [[ $GIT_ID_OLD == $GIT_ID_NEW ]]; then
    echo "GIT NOT CHANGE"
    exit
else
    rm -rf ./update/
    mkdir ./update
    git diff-tree -r --name-only --diff-filter=ACMRT $GIT_ID_OLD $GIT_ID_NEW | xargs tar -rf ./update/update.tar
    cd "$DIR_PATH/update"
    tar -xvf update.tar
    cd "$DIR_PATH"
    rm -f ./update/update.tar

    git diff-tree -r --name-only --diff-filter=D $GIT_ID_OLD $GIT_ID_NEW >"$DIR_PATH/vinades/upload_file_server.delete"

    # Nén các file js
    for file in $(find "$DIR_PATH/update" -name "*.js" -type f); do
        # Bỏ qua các file trong trình soạn thảo
        if [[ "$file" =~ assets\/editors || "$file" =~ assets\/js\/pdf.js || "$file" =~ themes\/admin_future\/modules\/upload\/upload.js ]]; then
            echo "Skip compile ${file}"
            continue
        fi

        echo "Compile ${file}"

        compiler=$( (java -jar $DIR_PATH/vinades/closure-compiler.jar --js $file --js_output_file ${file}.min) 2>&1)
        iserror=$( (echo $compiler | grep -i "[0-9] error(s)") 2>&1)
        if [[ $compiler =~ ([0-9]+)[[:space:]]error\(s\), ]]; then
            # Biên dịch lỗi
            if [[ ${BASH_REMATCH[1]} > 0 ]]; then
                echo "Biên dịch lỗi file $filename"
                echo $compiler
                read -p ""
                exit
            fi
        elif [[ $compiler =~ ^Error: ]]; then
            # Không tìm thấy file jar
            echo "Lỗi không tìm thấy file closure-compiler.jar"
            read -p ""
            exit
        elif [[ $compiler =~ command[[:space:]]not[[:space:]]found$ ]]; then
            # Không tìm thấy file jar
            echo "Lỗi chưa có java hoặc chưa add vào path"
            read -p ""
            exit
        fi

        rm -f $file
        mv "${file}.min" $file
    done

    read -p 'Enter để bắt đầu upload code'

    i=0
    scp_err=0
    if [ -d "$DIR_PATH/update/src/dev" ]; then
        cd "$DIR_PATH/src/dev"
        echo "Build CSS"
        sass theme.dauthau/scss/dauthau.responsive.scss:../themes/dauthau/css/dauthau.responsive.css theme.dauthau/scss/dauthau.non-responsive.scss:../themes/dauthau/css/dauthau.non-responsive.css --style compressed
        if [[ $? > 0 ]]; then
            echo "Build CSS Error"
            exit
        fi
        scp -r $DIR_PATH/src/themes/dauthau/css/dauthau.* dauthau@$SERVER_IP:/home/<USER>/public_html/themes/dauthau/css/
        if [ $? -eq 1 ]; then
            #$? - Trạng thái của câu lệnh thực hiện gần nhất ( 0 -> true , 1 -> false )
            scp_err=$((scp_err + 1))
        fi
        cd "$DIR_PATH"
        i=$((i + 1))
        echo "END Build CSS"

    fi

    if [ -d "$DIR_PATH/update/src/admin" ]; then
        scp -r $DIR_PATH/update/src/admin/* dauthau@$SERVER_IP:/home/<USER>/public_html/addtasia/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi
    if [ -d "$DIR_PATH/update/src/api-dauthau.net" ]; then
        scp -r $DIR_PATH/update/src/api-dauthau.net/* dauthau@$SERVER_IP:/home/<USER>/public_html/api-dauthau.net/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -d "$DIR_PATH/update/src/assets" ]; then
        scp -r $DIR_PATH/update/src/assets/* dauthau@$SERVER_IP:/home/<USER>/public_html/assets/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi
    if [ -d "$DIR_PATH/update/src/crawls" ]; then
        scp -r $DIR_PATH/update/src/crawls/* dauthau@$SERVER_IP:/home/<USER>/public_html/crawls/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi
    if [ -d "$DIR_PATH/update/src/includes" ]; then
        php "$DIR_PATH/vinades/upload_file_fix.php"
        scp -r $DIR_PATH/update/src/includes/* dauthau@$SERVER_IP:/home/<USER>/public_html/includes/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -d "$DIR_PATH/update/src/install" ]; then
        scp -r $DIR_PATH/update/src/install/* dauthau@$SERVER_IP:/home/<USER>/public_html/install/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -d "$DIR_PATH/update/src/modules" ]; then
        scp -r $DIR_PATH/update/src/modules/* dauthau@$SERVER_IP:/home/<USER>/public_html/modules/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -d "$DIR_PATH/update/src/webhook" ]; then
        scp -r $DIR_PATH/update/src/webhook/* dauthau@$SERVER_IP:/home/<USER>/public_html/webhook/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -d "$DIR_PATH/update/src/sso" ]; then
        scp -r $DIR_PATH/update/src/sso/* dauthau@$SERVER_IP:/home/<USER>/public_html/sso/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi
    if [ -d "$DIR_PATH/update/src/themes" ]; then
        scp -r $DIR_PATH/update/src/themes/* dauthau@$SERVER_IP:/home/<USER>/public_html/themes/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi
    if [ -d "$DIR_PATH/update/src/vendor" ]; then
        scp -r $DIR_PATH/update/src/vendor/* dauthau@$SERVER_IP:/home/<USER>/public_html/vendor/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -f "$DIR_PATH/update/src/index.php" ]; then
        scp -r $DIR_PATH/update/src/index.php dauthau@$SERVER_IP:/home/<USER>/public_html/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -f "$DIR_PATH/update/src/error.php" ]; then
        scp -r $DIR_PATH/update/src/error.php dauthau@$SERVER_IP:/home/<USER>/public_html/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -f "$DIR_PATH/update/src/api.php" ]; then
        scp -r $DIR_PATH/update/src/api.php dauthau@$SERVER_IP:/home/<USER>/public_html/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -f "$DIR_PATH/update/src/mload.php" ]; then
        scp -r $DIR_PATH/update/src/mload.php dauthau@$SERVER_IP:/home/<USER>/public_html/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -f "$DIR_PATH/update/src/sload.php" ]; then
        scp -r $DIR_PATH/update/src/sload.php dauthau@$SERVER_IP:/home/<USER>/public_html/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -f "$DIR_PATH/update/src/login-inspector.php" ]; then
        scp $DIR_PATH/update/src/login-inspector.php dauthau@$SERVER_IP:/home/<USER>/public_html/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -d "$DIR_PATH/update/src/tool" ]; then
        scp -r $DIR_PATH/update/src/tool/* dauthau@$SERVER_IP:/home/<USER>/public_html/tool/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi
    if [ -d "$DIR_PATH/update/src/private" ]; then
        scp -r $DIR_PATH/update/src/private/* dauthau@$SERVER_IP:/home/<USER>/private/
        if [ $? -eq 1 ]; then
            scp_err=$((scp_err + 1))
        fi
        i=$((i + 1))
    fi

    if [ -d "$DIR_PATH/update/src/data" ]; then
        read -p 'Có thư mục data cần upload, cần kiểm tra trước khi upload Y/N: ' cpdata
        if [[ $cpdata == "y" || $cpdata == "Y" ]]; then
            scp -r $DIR_PATH/update/src/data/* dauthau@$SERVER_IP:/home/<USER>/public_html/data/
            if [ $? -eq 1 ]; then
                scp_err=$((scp_err + 1))
            fi
            i=$((i + 1))
        fi
    fi

    rm -f ./update.zip
    echo "$GIT_ID_NEW" >"$DIR_PATH/vinades/upload_file_server.txt"

    for linedelete in $(cat "$DIR_PATH/vinades/upload_file_server.delete"); do
        if [ ! -f "$DIR_PATH/$linedelete" ]; then
            prefix=${linedelete:0:4}
            path=${linedelete:4}
            if [[ "$prefix" == "src/" && "$path" != "config.php" && "$path" != "data/config/config_global.php" ]]; then
                echo "Delete $path"
                ssh dauthau@$SERVER_IP -p22 "rm -f /home/<USER>/public_html/$path"
                if [ $? -eq 1 ]; then
                    scp_err=$((scp_err + 1))
                fi
                i=$((i + 1))
            fi
        fi
    done

    if [[ $i -gt 0 && $scp_err -eq 0 ]]; then
        ssh dauthau@$SERVER_IP -p22 "find /home/<USER>/public_html/data/cache -type f -name '*.cache' | xargs /bin/rm -f"
        php "$DIR_PATH/vinades/upload_file_server_clearcache.php"
        git add vinades/upload_file_server.txt

        echo "git commit & push"
        git commit -m "Cập nhật id commit lên site chính"
        git push origin master
    else
        echo "Lỗi trong quá trình upload, hoặc không có file"
    fi

    read -p 'Thực hiện xong, có xóa thư mục update Y/N: ' delupdate
    if [[ $delupdate == "n" || $delupdate == "N" ]]; then
        echo
        echo "Thoát không xóa thư mục update"
        exit
    fi
    rm -rf ./update/
fi

#scp -r /d/xampp/htdocs/dauthau.info/vendor/* dauthau@$SERVER_IP:/home/<USER>/public_html/vendor/
