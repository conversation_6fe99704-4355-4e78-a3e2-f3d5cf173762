<!-- BEGIN: main -->
<!-- BEGIN: popup_login -->
{POPUP_LOGIN}
<!-- END: popup_login -->
<!-- BEGIN: recaptcha -->
<div id="captchaModal" class="modal fade auto-width auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <p class="modal-title">{LANG.recapcha_title}</p>
            </div>
            <div class="modal-body">
                <div>{LANG.recapcha_body}</div>
                <div data-toggle="recaptcha" data-callback="click_update" id="{RECAPTCHA_ELEMENT}"></div>
            </div>
        </div>
    </div>
</div>
<!-- END: recaptcha -->

<div class="bidding-detail-wrapper bidding-detail-wrapper-result">
    <h1 class="bidding-name">{ROW.title}</h1>
    <div class="bidding-page-btn flex_end">
        <!-- BEGIN: show_download -->
        <p>
            <a rel="nofollow" href="{ROW.url_download}" target="blank" id="downloadLink" class="btn btn-primary"><i class="fa fa-file-word-o" aria-hidden="true"></i> {LANG.export_doc}</a>
            <span class="text-muted"><i class="fa fa-download" aria-hidden="true"></i> {NUM_DOWNLOAD}</span>
        </p>
        <!-- END: show_download -->
        <div class="bidding-page-btn-right">
            <div class="btn-share-group">
                <span>{LANG.share} </span>
                <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_fb_share();" title="{LANG.fb_share}">
                    <span class="icon-facebook"></span>
                </a>
                <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_tw_share('', '{DATA.title}');" title="{LANG.tweet}">
                    <span class="icon-twitter"></span>
                </a>
                <a href="javascript:void(0)" class="btn-share btn-copy-link" title="{LANG.copy_link}">
                    <em class="fa fa-link"></em>
                    <span class="tip" style="display: none;">{LANG.link_copy_successfully}</span>
                </a>
            </div>
            <!-- BEGIN: update -->
            <div class="text-right m-bottom">
                <div class="small">
                    {LANG.updated_at}: <strong>{ROW.update_at}</strong>
                </div>
                <div class="margin-top-sm m-bottom">
                    <span class="small">{ROW.update_info}</span> <a id="reupdate" class="btn btn-default btn-xs active" onclick="show_captcha()" href="javascript:void(0)" data-id="{ROW.id_bid}" data-check="{CHECKSESS_UPDATE}">{LANG.reupdate}</a><span id="show_error" class="text-danger margin-left" style="display: none"></span>
                </div>
            </div>
            <!-- END: update -->
        </div>
    </div>

    <div class="bidding-detail">
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.note_status}</div>
                <div class="c-val">{ROW.note}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.post_time}</div>
                <div class="c-val">{ROW.re_post}</div>
            </div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.date_public}</div>
            <div class="c-val">{ROW.date_bid_format}</div>
        </div>

        <!-- BEGIN: thongbao_lq -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.tb_lien_quan}</div>
            <div class="c-val">
                <!-- BEGIN: loop -->
                    <!-- BEGIN: list -->
                    <p>
                        <label>
                            <input type="checkbox" {VALUE.is_active} value="{VALUE.id_bid}" class="chk_notification">
                            <span class="tblq__span">
                                <a href="{VALUE.url_parent}"> {VALUE.re_post} </a> {VALUE.label_active} {VALUE.is_public_one}
                                <!-- BEGIN: view_change --> <a href="javascript:void(0)" data-id="{VALUE.id_view}" data-toggle="modal" class="view_change_da" data-target="#showTB" target="#showTB">{LANG.title_change_tb}</a>
                                <!-- END: view_change -->
                            </span>
                        </label>
                    </p>
                    <!-- END: list -->

                    <!-- BEGIN: list_duplicate -->
                    <p>
                        <label>
                            <input type="checkbox" {VALUE.is_active} value="{VALUE.id_bid}" class="chk_notification">
                            <span class="tblq__span">
                                <a href="{VALUE.url_parent}"> {VALUE.re_post} </a> {VALUE.label_active} {VALUE.is_public_one}
                                <span class="text-warning"> {LANG.dupicate_notification}</span>
                            </span>
                        </label>
                    </p>
                    <!-- END: list_duplicate -->
                <!-- END: loop -->
                <!-- BEGIN: show_btn -->
                <button class="btn btn-primary btn-xs" id="btn_change" data-toggle="modal" data-target="#showTB">{LANG.title_change_tb}</button>
                <!-- END: show_btn -->
            </div>
        </div>
        <!-- END: thongbao_lq -->

        <p class="bidding-sub-title">{LANG.info_propertys}:</p>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.name_propertys}</div>
            <div class="c-val title-strong">{ROW.name_owner}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.address}</div>
            <div class="c-val">{ROW.address_owner}</div>
        </div>

        <p class="bidding-sub-title">{LANG.info_tcdg}:</p>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.name_tcdg}</div>
            <div class="c-val title-strong">
                <a href="{ROW.link_bidder}">{ROW.name_bidder}</a>
            </div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.address}</div>
            <div class="c-val">{ROW.address_bidder}</div>
        </div>
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.phone}</div>
                <div class="c-val">{ROW.phone}</div>
            </div>
            <!-- BEGIN: fax -->
            <div>
                <div class="c-tit">{LANG.fax}</div>
                <div class="c-val">{ROW.fax}</div>
            </div>
            <!-- END: fax -->
        </div>

        <p class="bidding-sub-title">{LANG.info_sale}:</p>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.time_sale}</div>
            <div class="c-val">{ROW.opening_bid_format}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.location}</div>
            <div class="c-val">{ROW.address}</div>
        </div>
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.time_start}</div>
                <div class="c-val">{ROW.opening_reg_format}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.time_end}</div>
                <div class="c-val">{ROW.closing_reg_format}</div>
            </div>
        </div>

        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.time_pay_er}</div>
                <div class="c-val">{ROW.opening_deposit_format}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.time_pay_end}</div>
                <div class="c-val">{ROW.closing_deposit_format}</div>
            </div>
        </div>
        <!-- BEGIN: link_detail -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.link_detail}</div>
            <div class="c-val">{ROW.link_detail}</div>
        </div>
         <!-- END: link_detail -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.source}</div>
            <div class="c-val">{ROW.view_type}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.content}</div>
            <div class="c-val">{ROW.detail}</div>
        </div>
        <hr>
        <h2 class="bidding-sub-title text-uppercase">{LANG.property_list}</h2>
        <table class="bidding-table">
            <thead>
                <tr>
                    <th>{LANG.stt}</th>
                    <th>{LANG.name_property}</th>
                    <th>{LANG.no}</th>
                    <th>{LANG.location_pro}</th>
                    <th>{LANG.start_price}</th>
                    <th>{LANG.down_payment}</th>
                    <th>{LANG.note}</th>
                </tr>
            </thead>
            <tbody row="rowgroup">
                <!-- BEGIN: asset -->
                <tr role="row">
                    <td data-column="STT"><div>{ASSET.stt}</div></td>
                    <td data-column="Tên tài sản"><div>{ASSET.name_asset}</div></td>
                    <td data-column="Số lượng"><div>{ASSET.qty}</div></td>
                    <td data-column="Nơi có tài sản"><div>{ASSET.address_asset}</div></td>
                    <td data-column="Giá khởi điểm"><div>{ASSET.min_bid_prices}</div></td>
                    <td data-column="Tiền đặt trước"><div>{ASSET.deposit}</div></td>
                    <td data-column="Ghi chú"><div>{ASSET.note}</div></td>
                </tr>
                <!-- END: asset -->
            </tbody>
        </table>
    </div>

    <div class="docfile">{ROW.doc}</div>
    <p class="bidding-sub-title">{LANG.chance_for_you}:</p>
    <div class="chance-cont" id="box_for_you">
        <div class="chance">
            <div class="panel panel-default">
                <div class="cont panel-heading">
                    <div class="tl">{LANG.filter}</div>
                    <div class="ct">{FILTER.content}</div>
                    <div class="bt">
                        <a class="btn btn-primary" href="{FILTER.reg_link}">{FILTER.btn_title}</a>
                    </div>
                </div>
            </div>
            <!-- BEGIN: receive_email_content -->
            <div class="panel panel-default">
                <div class="cont panel-heading">
                    <div class="tl">{LANG.receive_email}</div>
                    <div class="ct">{RECEIVE_EMAIL.content}</div>
                    <div class="bt">
                        <a class="btn btn-danger" href="{RECEIVE_EMAIL.reg_link}">{RECEIVE_EMAIL.vip_paket}</a>
                    </div>
                </div>
            </div>
            <!-- END: receive_email_content -->
            <!-- BEGIN: show_panel_meeymap -->
            <div class="panel panel-default">
                <div class="cont panel-heading">
                    <div class="tl">{MEEYMAP_DATA.title}</div>
                    <div class="ct">{MEEYMAP_DATA.content}</div>
                    <div class="bt">
                        <a class="btn btn-primary" href="{MEEYMAP_DATA.link}">{MEEYMAP_DATA.register_now}</a>
                    </div>
                </div>
            </div>
            <!-- END: show_panel_meeymap -->
        </div>
    </div>

    <!-- BEGIN: view_auctioncosts -->
    <div id="biddingcosts" class="biddingcosts scoll__menu">
        <h2 class="bidding-sub-title">{LANG.auction_costs}</h2>
        <p>{LANG.auction_costs_desc}</p>
        <h3 class="margin-bottom-sm">{LANG.auctioncosts_desc}</h3>
        <div class="bid-cost-point-explain">
            <!-- BEGIN: view_land_use -->
            <p>{LANG.land_use_desc}</p>
            <p>{LANG.auction_costs_desc_1}</p>
            <table class="table text-center table-striped table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">{LANG.land_use}</th>
                        <th class="text-center w-50">{LANG.fee_level}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: view_qsdd_no_vip -->
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.less_200}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_1"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_200_to_500}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_2"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.over_500}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_3"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left"><b>{LANG.fee}</b></td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_4"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <!-- END: view_qsdd_no_vip -->
                    <!-- BEGIN: view_qsdd_is_vip -->
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.less_200}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_1">{COST.less_200}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_200_to_500}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_2">{COST.from_200_to_500}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.over_500}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_3">{COST.over_500}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left"><b>{LANG.fee}</b></td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_4"><b>{COST.total}</b></td>
                    </tr>
                    <!-- END: view_qsdd_is_vip -->
                </tbody>
            </table>
            <p>{LANG.auction_costs_desc_2}</p>
            <table class="table text-center table-striped table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">{LANG.land_use}</th>
                        <th class="text-center w-50">{LANG.fee_level}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: view_no_vip -->
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.less_200}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_5"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_200_to_500}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_7"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_50_to_100}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_8"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.over_10_bill}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_9"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left"><b>{LANG.fee}</b></td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_10"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <!-- END: view_no_vip -->
                    <!-- BEGIN: view_is_vip -->
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.less_200}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_5">{COST.less_5}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_200_to_500}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_7">{COST.from_one_to_five}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_50_to_100}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_8">{COST.from_50_to_100}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.over_10_bill}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_9">{COST.over_10_bill}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left"><b>{LANG.fee}</b></td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_10"><b>{COST.total_b}</b></td>
                    </tr>
                    <!-- END: view_is_vip -->
                </tbody>
            </table>
            <!-- END: view_land_use -->

            <!-- BEGIN: view_land_use_b -->
            <p>{LANG.auction_costs_desc_3}</p>
            <table class="table text-center table-striped table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">{LANG.land_use}</th>
                        <th class="text-center w-50">{LANG.fee_level}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: view_no_vip -->
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.less_200}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_5"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_200_to_500}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_7"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_50_to_100}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_8"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.over_10_bill}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_9"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left"><b>{LANG.fee}</b></td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_10"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <!-- END: view_no_vip -->
                    <!-- BEGIN: view_is_vip -->
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.less_200}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_5">{COST.less_5}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_200_to_500}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_7">{COST.from_one_to_five}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_50_to_100}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_8">{COST.from_50_to_100}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.over_10_bill}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_9">{COST.over_10_bill}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left"><b>{LANG.fee}</b></td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_10"><b>{COST.total_b}</b></td>
                    </tr>
                    <!-- END: view_is_vip -->
                </tbody>
            </table>
            <!-- END: view_land_use_b -->

            <!-- BEGIN: view_other_assets -->
            <p>{LANG.other_assets_desc}</p>
            <table class="table text-center table-striped table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">{LANG.price_asset}</th>
                        <th class="text-center w-50">{LANG.fee_level}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: view_no_vip -->
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.less_20}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_1"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_20_to_50}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_2"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_200_to_500}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_3"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_50_to_100}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_4"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.over_10_bill}</td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_5"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left"><b>{LANG.fee}</b></td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_6"><div class="blur_filter"><i class="fa fa-lock" aria-hidden="true"></i></div></td>
                    </tr>
                    <!-- END: view_no_vip -->
                    <!-- BEGIN: view_is_vip -->
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.less_20}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_1">{COST.less_20}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_20_to_50}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_2">{COST.from_20_to_50}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_200_to_500}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_3">{COST.from_200_to_500}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.from_50_to_100}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_4">{COST.from_50_to_100}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left">{LANG.over_10_bill}</td>
                        <td data-column="{LANG.cost_applicable}" class="text-center w-50" id="fee_level_5">{COST.over_10_bill}</td>
                    </tr>
                    <tr>
                        <td data-column="{LANG.land_use}" class="text-left"><b>{LANG.fee}</b></td>
                        <td data-column="{LANG.fee_level}" class="text-center w-50" id="fee_level_6"><b>{COST.total}</b></td>
                    </tr>
                    <!-- END: view_is_vip -->
                </tbody>
            </table>
            <!-- END: view_other_assets -->

            <!-- BEGIN: view_lock_vip -->
            <div class="box_lockvip__detail overlay_bidding_costs">
                <div class="main_lockvip main_bg_auction_costs">
                    <div class="tw_lockvip bg_auction_costs">
                        <div class="tw_lockvip_head">
                            <img class="tw_lockvip_head__image">
                        </div>
                        <div class="tw_lockvip__button">
                            <!-- BEGIN: is_user -->
                            <a href="{LINK_REG}" class="btn__lock_login">{LANG.vip_register}</a>
                            <!-- END: is_user -->
                            <!-- BEGIN: is_user_renew -->
                            <a href="{LINK_RENEW}" class="btn__lock_login">{LANG.renew_vip}</a>
                            <!-- END: is_user_renew -->
                            <!-- BEGIN: no_user -->
                            <a href="#" class="btn__lock_login" data-toggle="loginFormShow">{LANG.login}</a>
                            <!-- END: no_user -->
                        </div>
                        <!-- BEGIN: btn_use_point_auction_costs -->
                        <div>{LANG.or}</div>
                        <div class="tw_lockvip__button">
                            <a href="" class="btn__lock_login btn_point_view_bidding_costs">{LANG.use_point_view}</a>
                        </div>
                        <!-- END: btn_use_point_auction_costs -->
                        <div class="tw_lockvip__content">
                            <p class="tw_lockvip__content__des">{LANG.view_auction_costs}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="lock-bidding-costs">{LOCK_AUCTION_COST}</div>
            <!-- END: view_lock_vip -->
        </div>
    </div>
    <!-- END: view_auctioncosts -->
    <!-- BEGIN: btn_use_point_view_auction_costs_modal -->
    <div class="modal fade modal-center" tabindex="-1" role="dialog" id="view-bidding-costs-confirm" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <div class="modal-title h4">{LANG.notice}</div>
                </div>
                <div class="modal-body">
                    <p id="confirm_view_bidding_costs"></p>

                </div>
                <div class="modal-footer">
                    <button type="button" id="view-bidding-costs-submit" class="btn btn-primary btn-fw">{LANG.ok}</button>
                    <button type="button" class="btn btn-default btn-fw" id="view-bidding-costs-cancel" data-dismiss="modal">{LANG.cancel}</button>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        $('.btn_point_view_bidding_costs').on('click', () => {
            $.ajax({
                url: window.location.href,
                method: 'POST',
                dataType: 'json',
                data: {
                    check_point_view_auction_costs: 1,
                    checkss: '{CHECK_POINT_AUCTION_COST}'
                },
                success: (data) => {
                    if (data.res == 'success') {
                        $('#confirm_view_bidding_costs').text(data.mess);
                        $('#view-bidding-costs-submit').removeClass('hidden');
                    } else {
                        alertDiv = '<div class="alert alert-warning">' + data.mess + '</div>';
                        $('#confirm_view_bidding_costs').html(alertDiv);
                        $('#view-bidding-costs-submit').addClass('hidden');
                    }
                    $('#view-bidding-costs-confirm').modal('show');
                }
            });
        });
        $('#view-bidding-costs-submit').on('click', () => {
            $.ajax({
                url: window.location.href,
                method: 'POST',
                dataType: 'json',
                data: {
                    view_auction_costs: 1,
                    checkss: '{CHECK_VIEW_AUCTION_COST}'
                },
                success: (resp) => {
                    if (resp.status == 'success') {
                        if (resp.data['is_land_use'] == 1) {
                            $('#fee_level_1').html(resp.data['less_200']);
                            $('#fee_level_2').html(resp.data['from_200_to_500']);
                            $('#fee_level_3').html(resp.data['over_500']);
                            $('#fee_level_4').html('<b>' + resp.data['total'] + '</b>');
                            $('#fee_level_5').html(resp.data['less_one_bill']);
                            $('#fee_level_6').html(resp.data['from_20_to_50']);
                            $('#fee_level_7').html(resp.data['from_one_to_five']);
                            $('#fee_level_8').html(resp.data['from_50_to_100']);
                            $('#fee_level_9').html(resp.data['over_10_bill']);
                            $('#fee_level_10').html('<b>' + resp.data['total_b'] + '</b>');

                        } else {
                            $('#fee_level_1').html(resp.data['less_20']);
                            $('#fee_level_2').html(resp.data['from_20_to_50']);
                            $('#fee_level_3').html(resp.data['from_200_to_500']);
                            $('#fee_level_4').html(resp.data['from_50_to_100']);
                            $('#fee_level_5').html(resp.data['over_10_bill']);
                            $('#fee_level_6').html('<b>' + resp.data['total'] + '</b>');
                        }

                        $('.overlay_bidding_costs').addClass('hidden');
                        $('#view-bidding-costs-submit').addClass('hidden');
                        $('.lock-bidding-costs').html('');
                        $('#view-bidding-costs-confirm').modal('hide');
                    } else {
                        alertDiv = '<div class="alert alert-warning">' + resp.mess + '</div>';
                        $('#confirm_view_bidding_costs').html(alertDiv);
                        $('#view-bidding-costs-submit').addClass('hidden');
                        $('#view-bidding-costs-confirm').modal('show');
                    }
                }
            });
        });
    </script>
    <!-- END: btn_use_point_view_auction_costs_modal -->

    <div class="news_column row">
        <div class="panel-body ">
            <div class="col-md-18">
                <span class="label label-primary">{LANG.totalview}: <span class="badge">{ROW.viewcount}</span></span>
            </div>
            <div class="col-md-6 pull-right">
                <div class="socialicon clearfix col-md-16">
                    <div class="fb-like" data-href="{SELFURL}" data-layout="button_count" data-action="like" data-show-faces="false" data-share="true">&nbsp;</div>
                </div>
                <div class="socialicon clearfix col-md-8">
                    <div class="g-plusone" data-size="medium"></div>
                    <a href="http://twitter.com/share" class="twitter-share-button">{LANG.tweet}</a>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- <div class="news_column panel panel-default">
    <div class="panel-body">[FACEBOOK_COMMENT]</div>
</div> -->

<div id="showTB" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <p class="modal-title">{LANG.title_ssdaugia}</p>
            </div>
            <div class="modal-body">
                <div id="main_change_notificaion">
                    <div class="change_notificaion">
                        <div class="row">
                            <div class="col-md-24">
                                <label>{LANG.kieusosanh}: </label> <label><input type="radio" name="sosanhtb" checked value="1">&nbsp;{LANG.title_muti}</label> <label><input type="radio" name="sosanhtb" value="2">&nbsp;{LANG.title_one}</label>
                            </div>
                            <div class="col-md-24">
                                <div class="card">
                                    <label><input type="checkbox" class="change_by_line" value="3">&nbsp;{LANG.title_linebyline}</label>
                                    <div class="row_compare">
                                        <div class="col">
                                            <div class="card" id="outputOriginal"></div>
                                        </div>
                                        <div class="col">
                                            <div class="card" id="output"></div>
                                        </div>
                                        <div class="col" id="new_version">
                                            <div class="card" id="outputNew"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{LANG.close}</button>
            </div>
        </div>

    </div>
</div>

<!-- BEGIN: json_ld -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "NewsArticle",
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://google.com/article"
        },
        "headline": "{ARR_LD.title}",
        "image": ["{SCHEMA_IMAGE}"],
        "datePublished": "{ARR_LD.datePublished}",
        "dateModified": "{ARR_LD.dateModified}",
        "author": {
            "@type": "Person",
            "name": "{ARR_LD.brand_name}",
            "url": "{ARR_LD.url}"
        },
        "publisher": {
            "@type": "Organization",
            "name": "{ARR_LD.site_name}",
            "logo": {
                "@type": "ImageObject",
                "url": "{ARR_LD.url_logo}"
            }
        },
        "offers": {
            "@type": "Offer",
            "url": "{ARR_LD.url}",
            "validFrom": "{ARR_LD.validFrom}",
            "validThrough":"{ARR_LD.validThrough}"
        }
    }
    </script>
<!-- END: json_ld -->

<style>
    .text__ellipsis {
        display: block;
        display: -webkit-box;
        line-height: 1.3;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .chk_notification {
        margin-top: 0px !important;
    }

    .tblq__span {
        padding: 0px 8px;
    }

    .label__flex {
        display: flex;
        justify-content: space-around;
        align-items: center;
    }

    .damdam {
        opacity: 1;
        filter: grayscale(0);
    }
</style>
<script type="text/javascript">
    $(document).ready(function() {
        update_id_checkbox();

        $(".chk_notification").change(function(){
            var numberOfChecked = $('.chk_notification:checkbox:checked').length;
            if (numberOfChecked >= 2) {
                $('.chk_notification:checkbox').not(':checked').prop("disabled", true);
            } else {
                $('.chk_notification:checkbox').not(':checked').prop("disabled", false);
            }
            update_id_checkbox();
        });

        function update_id_checkbox() {
            var $boxes = $('.chk_notification[type=checkbox]:checked');
            arrid = [];
            $boxes.each(function(v, k){
               arrid.push(k.value);
            });

            $("#btn_change").val(arrid.join("-"));
            if ($boxes.length < 2) {
                $("#btn_change").removeClass("damdam");
                $("#btn_change").addClass("momo");
                // $("#view_change_tb").parent().attr("data-original-title", title);
                $("#btn_change").show();
                $("#btn_change").attr('disabled', true);
                // $("#view_change_tb").hide();
            } else {
                $("#btn_change").removeClass("momo");
                $("#btn_change").addClass("damdam");
                // $("#view_change_tb").parent().attr("data-original-title", "");
                $("#btn_change").attr('disabled', false);
                $("#btn_change").show();
                // $("#view_change_tb").show();
            }
        }

        $("#btn_change, .view_change_da").click(function() {
            if (window.matchMedia("(max-width: 768px)").matches) {
                $("input[name='sosanhtb'][value='1']").closest('label').hide();
                $("input[name='sosanhtb'][value='2']").click();
            } else {
                $("input[name='sosanhtb'][value='1']").closest('label').show();
                $("input[name='sosanhtb'][value='1']").click();
            }

            id = $(this).attr('data-id');
            if (id === undefined) {
                id = $(this).val();
            }
            $.ajax({
                url: location.href,
                type: 'POST',
                data: {
                    'action': 'view_change_tb',
                    'id' : id
                },
                success: function(data) {
                    if (data['res'] == 'error') {
                        alert(data['data']);
                        return false;
                    }
                    compare_highlight_difference("outputOriginal", "output", "outputNew", false, JSON.stringify(data['data']['daugia']), JSON.stringify(data['data']['daugia1']));

                    tr_0 = $(".table__tb").eq(0).find('tr');
                    tr_1 = $(".table__tb").eq(1).find('tr');
                    tr2_0 = $(".table__tb2").eq(0).find('tr');
                    tr2_1 = $(".table__tb2").eq(1).find('tr');
                    for (i = 0; i < tr_0.length; i++) {
                        tr_0.eq(i).attr('data-row', i+1);
                    }

                    for (i = 0; i < tr_1.length; i++) {
                        tr_1.eq(i).attr('data-row', i+1);
                    }

                    for (i = 0; i < tr2_0.length; i++) {
                        tr2_0.eq(i).attr('data-row', i+1);
                    }

                    for (i = 0; i < tr2_1.length; i++) {
                        tr2_1.eq(i).attr('data-row', i+1);
                    }

                    $(".change_by_line").click(function() {
                        view_change_line();
                    });

                    change_by_line = $(".change_by_line:checked").val();
                    if (change_by_line == '3') {
                        view_change_line();
                    }
                }
            })
        });


        function view_change_line() {
            arr_hide = [];
            arr_hide_gt = [];
            table = $(".change_by_line").closest('#main_change_notificaion').find('#output').find('.table__tb');

            del = table.find('tr').find('td');
            for (i = 0; i < del.length; i++) {
                row = del.eq(i).find('del').length;
                if (!row) {
                    data_row = del.eq(i).closest('tr').attr('data-row');
                    arr_hide.push(data_row);
                }
            }

            if($(".change_by_line").is(':checked')) {
                for (i = 0; i < arr_hide.length; i++) {
                    tr = $(".table__tb").find('tr[data-row="' + arr_hide[i] +'"]');
                    tr.fadeOut(0);
                }

                for (i = 0; i < arr_hide_gt.length; i++) {
                    bid_detail = $(".row_compare").find('.bidding-detail[data-row="' + arr_hide_gt[i] +'"]');
                    bid_detail.fadeOut(0);
                }
            } else {
                for (i = 0; i < arr_hide.length; i++) {
                    tr = $(".table__tb").find('tr[data-row="' + arr_hide[i] +'"]');
                    tr.fadeIn(0);
                }
            }

            // Bảng số 2
            table2 = $(".change_by_line").closest('#main_change_notificaion').find('#output').find('.table__tb2');
            arr_hide = [];
            arr_hide_gt = [];
            del = table2.find('tr').find('td');
            for (i = 0; i < del.length; i++) {
                row = del.eq(i).find('del').length;
                if (!row) {
                    data_row = del.eq(i).closest('tr').attr('data-row');
                    arr_hide.push(data_row);
                }
            }

            if (arr_hide.length == $(".table__tb2").eq(1).find('td').length) {
                $(".tb2").hide()
            } else {
                $(".tb2").show()
            }

            if($(".change_by_line").is(':checked')) {
                for (i = 0; i < arr_hide.length; i++) {
                    tr = $(".table__tb2").find('tr[data-row="' + arr_hide[i] +'"]');
                    tr.fadeOut(0);
                }

            } else {
                for (i = 0; i < arr_hide.length; i++) {
                    tr = $(".table__tb2").find('tr[data-row="' + arr_hide[i] +'"]');
                    tr.fadeIn(0);
                }
            }
        }

        $("input[name='sosanhtb']").change(function() {
            if ($(this).val() == 2) {
                $("#outputOriginal").parent().hide(500);
                $("#output").parent().show(500);
            } else if ($(this).val() == 3) {
                $("#outputOriginal").parent().hide();
                $("#output").parent().hide();

            } else {
                $("#outputOriginal").parent().show(500);
                $("#output").parent().show(500);
            }
        });
    });
</script>
<!-- END: main -->
