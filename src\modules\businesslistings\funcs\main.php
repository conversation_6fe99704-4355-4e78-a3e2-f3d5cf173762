<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_IS_MOD_BUSINESSLISTINGS'))
    die('Stop!!!');
use NukeViet\Point\Point;
use NukeViet\Point\Url;

$page_title = $nv_Lang->getModule('title_funs_main');
$description = $nv_Lang->getModule('main_description');
$key_words = $module_info['keywords'];
$page = 1;
$per_page = $global_array_config['show_list_number'];
if (isset($array_op[0]) and preg_match('/^page\-([0-9]+)$/', $array_op[0], $m)) {
    $page = $m[1];
}

if ($page > 1) {
    $page_title = $page_title . NV_TITLEBAR_DEFIS . $nv_Lang->getModule('page_num', $page);
}

$arr_beackcolumn = [];
// Province (Tỉnh/Thành phố)
$sql_province = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province";
$province_list = $nv_Cache->db($sql_province, 'id', 'location');
$province_list[-1] = array(
    'id' => -1,
    'title' => $nv_Lang->getModule('undefined'),
    'alias' => strtolower(change_alias($nv_Lang->getModule('undefined')))
);

// District (Quận/Huyện)
$sql_district = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_district";
$district_list = $nv_Cache->db($sql_district, 'id', 'location');
$district_list[-1] = array(
    'id' => -1,
    'title' => $nv_Lang->getModule('undefined'),
    'alias' => strtolower(change_alias($nv_Lang->getModule('undefined')))
);

// Ward (Phường/Xã)
$sql_ward = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_ward";
$ward_list = $nv_Cache->db($sql_ward, 'id', 'location');
$ward_list[-1] = array(
    'id' => -1,
    'title' => $nv_Lang->getModule('undefined'),
    'alias' => strtolower(change_alias($nv_Lang->getModule('undefined')))
);

$list_compare = $nv_Request->get_title('lstcpctt', 'cookie', true);

$list_compare = json_decode($list_compare, true);
!is_array($list_compare) && $list_compare = [];

if (isset($array_op[1]) and preg_match('/^([a-z0-9\-]+)$/i', $array_op[1])) {
    $id = explode('-', $array_op[1]);
    $id = end($id);
    $alias = strtolower(str_replace('-' . $id, '', $array_op[1], $count));
    if (isset($province_list[$id]['alias']) and strtolower($province_list[$id]['alias']) == $alias) {
        $arr_beackcolumn[] = array(
            "id" => $province_list[$id]['id'],
            "title" => $province_list[$id]['title'],
            "alias" => $province_list[$id]['alias']
        );
        $province = $id;
    }
    if (isset($district_list[$id]['alias']) and strtolower($district_list[$id]['alias']) == $alias) {
        $arr_beackcolumn[] = array(
            "id" => $district_list[$id]['id'],
            "title" => $district_list[$id]['title'],
            "alias" => $district_list[$id]['alias']
        );
        $district = $id;
    }
    if (isset($ward_list[$id]['alias']) and strtolower($ward_list[$id]['alias']) == $alias) {
        $arr_beackcolumn[] = array(
            "id" => $ward_list[$id]['id'],
            "title" => $ward_list[$id]['title'],
            "alias" => $ward_list[$id]['alias']
        );
        $ward = $id;
    }
}

//Thêm tìm kiếm theo ngành nghề dkkd
$industry_seacrh = [];
$_where = [];
$industry1 = '';
$industry2 = '';
$industry3 = '';
$industry4 = '';
//tìm kiếm theo alias
if (isset($array_op[0]) and preg_match('/^([A-Za-z0-9\-]+)\-([A-Za-z0-9]+)$/', $array_op[0], $m)) {
    if ($m[1] != 'page') {

        $_link_page = '';
        if (isset($array_op[1]) and preg_match('/^page\-([0-9]+)$/', $array_op[1], $n)) {
            $page = $n[1];
            $_link_page = '/page-' . $page . '/';
        }
        if (strlen($m[2]) >= 6) {
            $industry_fix = substr($m[2], 0, 5);
            $check_alias['alias'] = isset($industry_list[$industry_fix]) ? $industry_list[$industry_fix]['alias_' . NV_LANG_DATA] : '';
            nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . '/' . $check_alias['alias'] . '-' . $industry_fix . '/');
        }
        //KiỂM TRA XEM ĐÚNG ALIAS và code KHÔNG
        // $check_alias = $db->query("SELECT t1.alias as alias_vi, t1.title as title_vi, t2.alias as alias_en, t2.title as title_en FROM " . $db_config['prefix'] . "_vi_industry t1 INNER JOIN " . $db_config['prefix'] . "_en_industry t2 ON t1.code=t2.code WHERE t1.code = " . $db->quote($m[2]) . ' LIMIT 1')->fetch();
        $check_alias = $industry_list[$m[2]];
        $link_rew = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . '/' . $check_alias['alias_' . NV_LANG_DATA] . '-' . $m[2];
        $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&" . NV_NAME_VARIABLE . "=" . $module_name . '/' . $check_alias['alias_' . $other_lang] . '-' . $m[2];
        $page_title = sprintf($nv_Lang->getModule('nganhnghe_2'), $check_alias['title_' . NV_LANG_DATA]);
        $canonicalUrl = $link_rew;

        if (!empty($check_alias)) {
            if (strtolower($check_alias['alias_' . NV_LANG_DATA]) === strtolower($m[1])) {
                $array_industry_code = array_keys($industry_list);
                $array_mod_title[] = [
                    'title' => $check_alias['title_' . NV_LANG_DATA],
                    'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '/' . $array_op[0]
                ];
                $str_l = strlen($m[2]);
                if ($str_l == 1) {
                    $industry1 = $m[2];

                    foreach ($array_industry_code as $_value) {
                        if (preg_match('/^(' . $industry1 . ')/', $_value)) {
                            if ($industry_list[$_value]['level'] > 1 && $industry_list[$_value]['level'] <=4) {

                                $_where[] = " FIND_IN_SET(" . $db->quote($_value) . ", industry_dkkd)";
                                $industry_seacrh['should'][] = [
                                    'match' => [
                                        'industry_dkkd' => [
                                            'query' => $_value
                                        ]
                                    ]
                                ];
                            }
                        }
                    }

                    $where[] = '(FIND_IN_SET(' . $db->quote($industry1) . ', industry1) OR FIND_IN_SET(' . $db->quote($industry1) . ', industry_dkkd)' . (!empty($_where) ? ' OR ' . implode(' OR ', $_where) : '') . ')';
                } elseif ($str_l == 3) {
                    $industry1 = $m[2][0];
                    $industry2 = $m[2];
                    foreach ($array_industry_code as $_value) {
                        if (preg_match('/^(' . $industry2 . ')/', $_value)) {
                            if ($industry_list[$_value]['level'] > 2 && $industry_list[$_value]['level'] <=4) {

                                $_where[] = " FIND_IN_SET(" . $db->quote($_value) . ", industry_dkkd)";
                                $industry_seacrh['should'][] = [
                                    'match' => [
                                        'industry_dkkd' => [
                                            'query' => $_value
                                        ]
                                    ]
                                ];
                            }
                        }
                    }

                    $where[] = '(FIND_IN_SET(' . $db->quote($industry2) . ', industry2) OR FIND_IN_SET(' . $db->quote($industry2) . ', industry_dkkd)' . (!empty($_where) ? ' OR ' . implode(' OR ', $_where) : '') . ')';
                } elseif ($str_l == 4) {
                    $industry1 = $m[2][0];
                    $industry2 = substr($m[2], 0, 3);
                    $industry3 = $m[2];
                    foreach ($array_industry_code as $_value) {
                        if (preg_match('/^(' . $industry3 . ')/', $_value)) {
                            if ($industry_list[$_value]['level'] == 4) {
                                $_where[] = " FIND_IN_SET(" . $db->quote($_value) . ", industry_dkkd)";
                                $industry_seacrh['should'][] = [
                                    'match' => [
                                        'industry_dkkd' => [
                                            'query' => $_value
                                        ]
                                    ]
                                ];
                            }
                        }
                    }

                    $where[] = '(FIND_IN_SET(' . $db->quote($industry3) . ', industry3) OR FIND_IN_SET(' . $db->quote($industry3) . ', industry_dkkd)' . (!empty($_where) ? ' OR ' . implode(' OR ', $_where) : '') . ')';
                } elseif ($str_l == 5) {
                    $industry1 = $m[2][0];
                    $industry2 = substr($m[2], 0, 3);
                    $industry3 = substr($m[2], 0, 4);
                    $industry4 = substr($m[2], 0, 5);
                    $where[] = '(FIND_IN_SET(' . $db->quote($industry4) . ', industry4) OR FIND_IN_SET(' . $db->quote($industry4) . ', industry_dkkd))';
                } elseif ($str_l >= 6) {
                    $industry4 = substr($m[2], 0, 5);
                    nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . '/' . $check_alias['alias_' . NV_LANG_DATA] . '-' . $industry4 . '/');
                } else {
                    nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
                }
            } else {
                nv_redirect_location($link_rew . $_link_page);
            }
        } else {
            nv_redirect_location(nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name, true));
        }
    }
}

if (!empty($province)) {
    $where[] = 'province = ' . $province;
}
if (!empty($district)) {
    $where[] = 'district = ' . $district;
}
if (!empty($ward)) {
    $where[] = 'ward = ' . $ward;
}
$where[] = 'active = 1';
$arr_data = array();

// Kiểm tra xem tk có là VIP hay không
$arr_customs = [];
if (!empty($global_array_vip[89])) {
    define('NV_IS_VIP_X2', true);
    $arr_customs = $global_array_vip[89];
}

// nếu page < 1
if ($page < 1) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_wrong_page') . $btn, 'danger');

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

// lưu csdl các request
$array_param = [
    'keyword' => '',
    'industry1' => '',
    'industry2' => '',
    'industry3' => '',
    'industry4' => '',
    'province' => 0,
    'district' => 0,
    'ward' => 0,
    'userid' => $user_info['userid'],
    'businesstype' => 0,
    'lvkd' => 0,
    'fee' => 0,
    'sfrom_business' => 0,
    'sto_business' => 0,
];

$sort_type_cookie = $nv_Request->get_title('businesslistings_sort', 'cookie', 'default');
$sort_type = $nv_Request->get_title('sort', 'get', $sort_type_cookie);
$sort_type = in_array($sort_type, $array_sort_options) ? $sort_type : 'default';

if ($sort_type !== $sort_type_cookie) {
    $nv_Request->set_Cookie('businesslistings_sort', $sort_type, NV_LIVE_COOKIE_TIME);
}

if ($config_bidding['elas_use']) {

    // kết nối tới ElasticSearh
    $nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($config_bidding['elas_host'], $config_bidding['elas_port'], 'dauthau_businesslistings', $config_bidding['elas_user'], $config_bidding['elas_pass']);
    $array_query_elastic = array();
    $array_query_elastic['track_total_hits'] = 'true';
    $array_query_elastic['size'] = $per_page;
    $array_query_elastic['from'] = ($page - 1) * $per_page;
    $array_query_elastic['sort'] = [
        [
            "id" => [
                "order" => "desc"
            ]
        ]
    ];
    if ($sort_type == 'num_total_desc') {
        $array_query_elastic['sort'] = [
            [
                'num_total' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'num_result_desc') {
        $array_query_elastic['sort'] = [
            [
                'num_result' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'num_false_desc') {
        $array_query_elastic['sort'] = [
            [
                'num_false' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'ability_point_desc') {
        $array_query_elastic['sort'] = [
            [
                'ability_point' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'independent_contractor') {
        $array_query_elastic['sort'] = [
            [
                'total_win_inde' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'joint_contractor') {
        $array_query_elastic['sort'] = [
            [
                'total_win_partner' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'direct_contractor') {
        $array_query_elastic['sort'] = [
            [
                'total_chidinhthau' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'other_contractor') {
        $array_query_elastic['sort'] = [
            [
                '_script' => [
                    'type' => 'number',
                    'script' => ['source' => "doc['total_win_inde'].value + doc['total_win_partner'].value - doc['total_chidinhthau'].value"],
                    'order' => 'desc'
                ]
            ],
            ['id' => ['order' => 'desc']]
        ];
    } elseif ($sort_type == 'total_by_role') {
        $array_query_elastic['sort'] = [
            [
                '_script' => [
                    'type' => 'number',
                    'script' => ['source' => "doc['total_win_inde'].value + doc['total_win_partner'].value"],
                    'order' => 'desc'
                ]
            ],
            ['id' => ['order' => 'desc']]
        ];
    } elseif ($sort_type == 'total_by_type') {
        $array_query_elastic['sort'] = [
            [
                '_script' => [
                    'type' => 'number',
                    'script' => ['source' => "doc['total_chidinhthau'].value + (doc['total_win_inde'].value + doc['total_win_partner'].value - doc['total_chidinhthau'].value)"],
                    'order' => 'desc'
                ]
            ],
            ['id' => ['order' => 'desc']]
        ];
    } elseif ($sort_type == 'total_revenue') {
        $array_query_elastic['sort'] = [
            [
                '_script' => [
                    'type' => 'number',
                    'script' => [
                        'source' => "(doc['total_win_inde'].value + doc['total_win_partner'].value) + (doc['total_chidinhthau'].value + (doc['total_win_inde'].value + doc['total_win_partner'].value - doc['total_chidinhthau'].value))"
                    ],
                    'order' => 'desc'
                ]
            ],
            ['id' => ['order' => 'desc']]
        ];
    } elseif ($sort_type == 'default' || empty($sort_type)) {
        $array_query_elastic['sort'] = [
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    }
    if (NV_CURRENTTIME >= $close_time_dauthau and NV_CURRENTTIME <= $open_maunal_time_dauthau and ($type_user == 2 or $type_user == 3)) {
        // view chỉ xem dc tin cũ
        $search_elastic['must'][]['range']['dateestablished'] = [
            "lte" => $close_time_dauthau
        ];
    }

    if ($industry4 != "") {
        $industry_seacrh['should'][] = [
            'match' => [
                'industry4' => [
                    'query' => $industry4
                ]
            ]
        ];
        $industry_seacrh['should'][] = [
            'match' => [
                'industry_dkkd' => [
                    'query' => $industry4
                ]
            ]
        ];
    } elseif ($industry3 != "") {
        $industry_seacrh['should'][] = [
            'match' => [
                'industry3' => [
                    'query' => $industry3
                ]
            ]
        ];
        $industry_seacrh['should'][] = [
            'match' => [
                'industry_dkkd' => [
                    'query' => $industry3
                ]
            ]
        ];
    } elseif ($industry2 != "") {
        $industry_seacrh['should'][] = [
            'match' => [
                'industry2' => [
                    'query' => $industry2
                ]
            ]
        ];
        $industry_seacrh['should'][] = [
            'match' => [
                'industry_dkkd' => [
                    'query' => $industry2
                ]
            ]
        ];
    } elseif ($industry1 != "") {
        $industry_seacrh['should'][] = [
            'match' => [
                'industry1' => [
                    'query' => $industry1
                ]
            ]
        ];
        $industry_seacrh['should'][] = [
            'match' => [
                'industry_dkkd' => [
                    'query' => $industry1
                ]
            ]
        ];
    }

    if (!empty($industry_seacrh)) {
        $search_elastic['must'][] = [
            'bool' => $industry_seacrh
        ];
    }

    if (!empty($search_elastic)) {
        $array_query_elastic['query']['bool'] = $search_elastic;
    }

    $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_info', $array_query_elastic);
    $all_page = $num_items = $response['hits']['total']['value'];

    // xác định điểm và số dn trên mỗi block
    $total_point_download = 0;
    $config_bidding = $module_config['bidding'];
    $num_point_block = $config_bidding['num_point_block_notx2'];
    $num_business_block = $config_bidding['num_business_block_notx2'];
    if (defined('NV_IS_USER')) {
        if (defined('NV_IS_VIP_X2')) {
            $num_point_block = $config_bidding['num_point_block_x2'];
            $num_business_block = $config_bidding['num_business_block_x2'];
        }
    }
    $total_point_download = ceil($num_items/$num_business_block) * $num_point_block;
    require_once NV_ROOTDIR . '/modules/businesslistings/handle_download/handle_download.php';
    // page > 100 thì thông báo
    if ($num_items > 0 && $page > 100) {
        $nv_BotManager->setPrivate();

        $link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation";
        $beackcolumn = "";
        foreach ($arr_beackcolumn as $item) {
            $beackcolumn .= "<a href=\"" . $link . "/" . $item['alias'] . "-" . $item['id'] . "\">" . $item['title'] . "</a>, ";
        }
        $beackcolumn = empty($beackcolumn) ? $page_title : rtrim(trim($beackcolumn), ',');

        if (!empty($link_rew)) {
            $base_url = $link_rew;
            $page_url = $base_url;
            if ($page > 1) {
                $page_url .= '/page-' . $page;
                $global_lang_url .= '/page-' . $page;
            }
            $urlappend = '/page-';
        } else {
            $base_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name;
            $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&" . NV_NAME_VARIABLE . "=" . $module_name;
            $page_url = $base_url;
    
            if ($page > 1) {
                $page_url .= '&amp;' . NV_OP_VARIABLE . '=page-' . $page;
                $description .= '-' . sprintf($nv_Lang->getModule('page_num'), $page);
            }
            $urlappend = '&amp;' . NV_OP_VARIABLE . '=page-';
        }
        $canonicalUrl = getCanonicalUrl($page_url);
        // $urlappend = '&amp;' . NV_OP_VARIABLE . '=page-';
        betweenURLs($page, ceil($all_page / $per_page), $base_url, $urlappend, $prevPage, $nextPage);
        $pages_businesslistings = nv_alias_page($page_title, preg_replace('/\/$/', '', $base_url), $all_page, $per_page, $page);

        $contents = nv_theme_businesslistings_listbusinesslistings([], $beackcolumn, $pages_businesslistings, $all_page, $num_items, $total_point_download);
        include NV_ROOTDIR . '/includes/header.php';
        echo nv_site_theme($contents);
        include NV_ROOTDIR . '/includes/footer.php';
    }

    foreach ($response['hits']['hits'] as $value) {
        if (!empty($value['_source'])) {
            $view = $value['_source'];
            if (in_array($view['id'], $list_compare)) {
                $view['is_added_cmp'] = 1;
            } else {
                $view['is_added_cmp'] = 0;
            }
            $view['checkremvcp'] = md5(NV_CHECK_SESSION . '_checkremvcp_' . $view['id']);
            $view['checkaddcp'] = md5(NV_CHECK_SESSION . '_checkaddcp_' . $view['id']);
            $arr_data[$view['id']] = $view;
        }
    }
} else {
    $db->sqlreset()
        ->select('SQL_CALC_FOUND_ROWS *')
        ->from(BUSINESS_PREFIX_GLOBAL . '_info');

    if ($sort_type == 'num_total_desc') {
        $db->order('num_total DESC, id DESC');
    } elseif ($sort_type == 'num_result_desc') {
        $db->order('num_result DESC, id DESC');
    } elseif ($sort_type == 'num_false_desc') {
        $db->order('num_false DESC, id DESC');
    } elseif ($sort_type == 'ability_point_desc') {
        $db->order('ability_point DESC, id DESC');
    } elseif ($sort_type == 'independent_contractor') {
        $db->order('total_win_inde DESC, id DESC');
    } elseif ($sort_type == 'joint_contractor') {
        $db->order('total_win_partner DESC, id DESC');
    } elseif ($sort_type == 'direct_contractor') {
        $db->order('total_chidinhthau DESC, id DESC');
    } elseif ($sort_type == 'other_contractor') {
        $db->order('(total_win_inde + total_win_partner - total_chidinhthau) DESC, id DESC');
    } elseif ($sort_type == 'default' || empty($sort_type)) {
        $db->order('id DESC');
    }

    $db->limit($per_page)
        ->offset(($page - 1) * $per_page);

    if (NV_CURRENTTIME >= $close_time_dauthau and NV_CURRENTTIME <= $open_maunal_time_dauthau and ($type_user == 2 or $type_user == 3)) {
        $where[] = 'dateestablished <= ' . $close_time_dauthau;
    }
    if (!empty($where)) {
        $db->where(implode(' AND ', $where));
    }
    $sth = $db->prepare($db->sql());
    $sth->execute();
    $result_page = $db->query("SELECT FOUND_ROWS()");
    list($numf) = $result_page->fetch(3);
    $all_page = ($numf) ? $numf : 1;
    $num_items = $numf;

    // xác định điểm và số dn trên mỗi block
    $total_point_download = 0;
    $config_bidding = $module_config['bidding'];
    $num_point_block = $config_bidding['num_point_block_notx2'];
    $num_business_block = $config_bidding['num_business_block_notx2'];
    if (defined('NV_IS_USER')) {
        if (defined('NV_IS_VIP_X2')) {
            $num_point_block = $config_bidding['num_point_block_x2'];
            $num_business_block = $config_bidding['num_business_block_x2'];
        }
    }
    $total_point_download = ceil($num_items/$num_business_block) * $num_point_block;
    require_once NV_ROOTDIR . '/modules/businesslistings/handle_download/handle_download.php';
    // nếu page > 100 thì thông báo
    if ($num_items > 0 && $page > 100) {
        $nv_BotManager->setPrivate();

        $link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation";
        $beackcolumn = "";
        foreach ($arr_beackcolumn as $item) {
            $beackcolumn .= "<a href=\"" . $link . "/" . $item['alias'] . "-" . $item['id'] . "\">" . $item['title'] . "</a>, ";
        }
        $beackcolumn = empty($beackcolumn) ? $page_title : rtrim(trim($beackcolumn), ',');

        if (!empty($link_rew)) {
            $base_url = $link_rew;
            $page_url = $base_url;
            if ($page > 1) {
                $page_url .= '/page-' . $page;
                $global_lang_url .= '/page-' . $page;
            }
            $urlappend = '/page-';
        } else {
            $base_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name;
            $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&" . NV_NAME_VARIABLE . "=" . $module_name;
            $page_url = $base_url;
    
            if ($page > 1) {
                $page_url .= '&amp;' . NV_OP_VARIABLE . '=page-' . $page;
                $description .= '-' . sprintf($nv_Lang->getModule('page_num'), $page);
            }
            $urlappend = '&amp;' . NV_OP_VARIABLE . '=page-';
        }
        $canonicalUrl = getCanonicalUrl($page_url);
        // $urlappend = '&amp;' . NV_OP_VARIABLE . '=page-';
        betweenURLs($page, ceil($all_page / $per_page), $base_url, $urlappend, $prevPage, $nextPage);
        $pages_businesslistings = nv_alias_page($page_title, preg_replace('/\/$/', '', $base_url), $all_page, $per_page, $page);

        $contents = nv_theme_businesslistings_listbusinesslistings([], $beackcolumn, $pages_businesslistings, $all_page, $num_items, $total_point_download);
        include NV_ROOTDIR . '/includes/header.php';
        echo nv_site_theme($contents);
        include NV_ROOTDIR . '/includes/footer.php';
    }

    while ($_row = $sth->fetch()) {
        if (in_array($_row['id'], $list_compare)) {
            $_row['is_added_cmp'] = 1;
        } else {
            $_row['is_added_cmp'] = 0;
        }
        $_row['checkremvcp'] = md5(NV_CHECK_SESSION . '_checkremvcp_' . $_row['id']);
        $_row['checkaddcp'] = md5(NV_CHECK_SESSION . '_checkaddcp_' . $_row['id']);
        $row['total_other'] = $row['total_win_inde'] + $row['total_win_partner'] - $row['total_chidinhthau'];
        $row['total_other'] = intval($row['total_win_inde']) + intval($row['total_win_partner']) - intval($row['total_chidinhthau']);
        $row['total_by_type'] = intval($row['total_chidinhthau']) + intval($row['total_other']);
        $row['total_by_role'] = intval($row['total_win_inde']) + intval($row['total_win_partner']);
        $row['total_revenue'] = intval($row['total_by_role']) + intval($row['total_by_type']);
        $arr_data[$_row['id']] = $_row;
    }


}
$link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation";
$beackcolumn = "";
foreach ($arr_beackcolumn as $item) {
    $beackcolumn .= "<a href=\"" . $link . "/" . $item['alias'] . "-" . $item['id'] . "\">" . $item['title'] . "</a>, ";
}
$beackcolumn = empty($beackcolumn) ? $page_title : rtrim(trim($beackcolumn), ',');

if (!empty($link_rew)) {
    $base_url = $link_rew;
    $page_url = $base_url;
    if ($page > 1) {
        $page_url .= '/page-' . $page;
        $global_lang_url .= '/page-' . $page;
    }
    $urlappend = '/page-';
} else {
    $base_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name;
    $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&" . NV_NAME_VARIABLE . "=" . $module_name;
    $page_url = $base_url;
    if ($page > 1) {
        $page_url .= '&amp;' . NV_OP_VARIABLE . '=page-' . $page;
        $description .= '-' . sprintf($nv_Lang->getModule('page_num'), $page);
    }
    $urlappend = '&amp;' . NV_OP_VARIABLE . '=page-';
}
$canonicalUrl = getCanonicalUrl($page_url);
// $urlappend = '&amp;' . NV_OP_VARIABLE . '=page-';
betweenURLs($page, ceil($all_page / $per_page), $base_url, $urlappend, $prevPage, $nextPage);
$pages_businesslistings = nv_alias_page($page_title, preg_replace('/\/$/', '', $base_url), $all_page, $per_page, $page);

$arr_content = array();
$link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=detail";

foreach ($arr_data as $row) {
    $row['addressfull'] = array();
    if ($row['address'] != "") {
        $row['addressfull'][] = handle_address_ward($row['address'], $row['ward']);
    }
    if (isset($ward_list[$row['ward']])) {
        $row['addressfull'][] = $ward_list[$row['ward']]['title'];
    }
    if (isset($district_list[$row['district']])) {
        $row['addressfull'][] = $district_list[$row['district']]['title'];
    }
    $row['addressfull'] = implode(', ', $row['addressfull']);
    $row['addressfull'] = str_replace(',,', ',', $row['addressfull']);
    if (empty($row['addressfull'])) {
        $row['addressfull'] = '';
    }
    if (isset($province_list[$row['province']])) {
        $row['location'] = $province_list[$row['province']]['title'];
        $row['link_location'] = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation/T-" . $province_list[$row['province']]['alias'] . '-' . $province_list[$row['province']]['id'];
    } else {
        $row['location'] = '';
        $row['link_location'] = '';
    }
    if (!empty($row['website']) and !preg_match('/^http[s]*\:\/\//i', $row['website'])) {
        $row['website'] = 'http://' . $row['website'];
    }
    $row['website'] = (!empty($row['website'])) ? "<a target='_blank' href='" . $row['website'] . "'>" . $row['website'] . "</a>" : "";
    (NV_LANG_DATA != 'vi' && !empty(trim($row['officialname']))) && $row['companyname'] = $row['officialname'];
    $row['link'] = $link . "/" . change_alias($row['companyname']) . "-" . $row['id'];
    if ($row['logo'] != "") {
        $row['logo'] = NV_BASE_SITEURL . NV_UPLOADS_DIR . "/" . $module_upload . "/" . $row['logo'];
    } else {
        $row['logo'] = NV_BASE_SITEURL . "themes/" . $module_info['template'] . "/images/" . $module_file . "/noimages.png";
    }
    $row['chartercapital'] = ConvertPriceTextSort($row['chartercapital']);
    $row['businesstype_text'] = isset($global_array_businesstype[$row['businesstype']]) ? $global_array_businesstype[$row['businesstype']]['title'] : '';
    $row['businesstype_link'] = isset($global_array_businesstype[$row['businesstype']]) ? $global_array_businesstype[$row['businesstype']]['title'] : '';
    $row['phone'] = !empty($row['phone']) ? $row['phone'] : '';
    $row['fax'] = !empty($row['fax']) ? $row['fax'] : '';
    $row['represent_phone'] = !empty($row['represent_phone']) ? $row['represent_phone'] : '';
    $arr_content[] = $row;
}

$contents = nv_theme_businesslistings_listbusinesslistings($arr_content, $beackcolumn, $pages_businesslistings, $all_page, $num_items, $total_point_download);

include(NV_ROOTDIR . "/includes/header.php");
echo nv_site_theme($contents);
include(NV_ROOTDIR . "/includes/footer.php");
