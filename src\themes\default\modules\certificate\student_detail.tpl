<!-- BEGIN: main -->
<!-- BEGIN: popup_login -->
{POPUP_LOGIN}
<!-- END: popup_login -->
<div class="border-bidding">
    <h2 class="title__tab_heading"><span>{COURSES.student_name}</span></h2>
</div>
<div class="bidding-detail">
    <div class="bidding-detail-item">
        <div class="c-tit">{LANG.certificate_code}</div>
        <div class="c-val">{COURSES.certificate_code}</div>
    </div>
    <div class="bidding-detail-item">
        <div class="c-tit">{LANG.place}</div>
        <div class="c-val"><a href="{COURSES.link_place}">{COURSES.place}</a></div>
    </div>
    <div class="bidding-detail-item">
        <div class="c-tit">{LANG.result}</div>
        <div class="c-val"><a href="{COURSES.link_result}">{COURSES.result}</a></div>
    </div>
    <div class="courses_detail_price">
        <!-- BEGIN: view3 -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.birthday}</div>
            <div class="c-val">{COURSES.birthday}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.identify_code}</div>
            <div class="c-val">{COURSES.identify_code}</div>
        </div>
        <!-- END: view3 -->
        <!-- BEGIN: view2 -->
        <div class="bidding-detail-item courses_detail_point">
            <div class="c-tit c-point">
                <div class="">
                    {LANG.birthday}
                </div>
                <div class="">
                    {LANG.identify_code}
                </div>
            </div>
            <div class="c-val">
                <a href="javascript:void(0)" class="view__price" data-id="{COURSES.id}">{TB_POINT}</a>
                <p>
                    {LINKVIP}
                    <!-- BEGIN: popup_login -->
                    {POPUP_LOGIN}
                    <!-- END: popup_login -->
                </p>
            </div>
        </div>
        <!-- END: view2 -->
        <!-- BEGIN: view4 -->
        <div class="bidding-detail-item courses_detail_point">
            <div class="c-tit c-point">
                <div class="">
                    {LANG.birthday}
                </div>
                <div class="">
                    {LANG.identify_code}
                </div>
            </div>
            <div class="c-val">
                <a href="javascript:void(0)" class="view__price" data-id="{COURSES.id}">{TB_POINT}</a>
                <p>
                    {LINKVIP_RENEW}
                </p>
            </div>
        </div>
        <!-- END: view4 -->
        
        <!-- BEGIN: view -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.birthday}</div>
            <div class="c-val">{COURSES.birthday}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.identify_code}</div>
            <div class="c-val">{COURSES.identify_code}</div>
        </div>
        <!-- END: view -->
    </div>
</div>
<div class="border-bidding">
    <h2 class="title__tab_heading"><span>{LANG.course_information}</span></h2>
</div>
<div class="bidding-detail">
    <div class="bidding-detail-item">
        <div class="c-tit">{LANG.co_so_dao_tao}</div>
        <div class="c-val"><a href="{ROW.link_training}">{ROW.name_tranning}</a></div>
    </div>
    <div class="bidding-detail-item">
        <div class="c-tit">{LANG.name_courses}</div>
        <div class="c-val"><a href="{ROW.link_courses}">{ROW.name_courses}</a></div>
    </div>
    <div class="bidding-detail-item">
        <div class="c-tit">{LANG.teacher}</div>
        <div class="c-val">{ROW.teacher}</div>
    </div>
    <div class="bidding-detail-item">
        <div class="c-tit">{LANG.certificate_singer}</div>
        <div class="c-val">{ROW.certificate_singer}</div>
    </div>
    <!-- BEGIN: data_courses -->
    <div class="bidding-detail-item">
        <div class="c-tit">{LANG.time_traning}</div>
        <div class="c-val">{ROW.start_date} - {ROW.end_date}</div>
    </div>
    <div class="bidding-detail-item">
        <div class="c-tit">{LANG.certificate_date}</div>
        <div class="c-val">{ROW.certificate_date}</div>
    </div>
    <div class="bidding-detail-item">
        <div class="c-tit">{LANG.role}</div>
        <div class="c-val">{ROW.role}</div>
    </div>
    <!-- END: data_courses -->
    
</div>
<div id="trudiem"></div>
<style>
    #trudiem {
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background: #d15c18d1;
        padding: 10px;
        color: #ffffff;
        margin: 0;
        box-shadow: 0px 5px 10px rgb(141 136 136 / 60%);
        border-radius: 2px;
        display: none;
    }

    #trudiem p {
        margin: 0;
    }
    .tooltip {
        width: 400px;
    }
</style>
<script type="text/javascript">
    $(document).ready(function($) {
        $(".view__price").click(function() {
            let button = $(this); // Lưu trữ tham chiếu đến nút
            let id = button.attr('data-id');
            
            // Dữ liệu HTML để thêm khi AJAX thành công
            let $html = '';
            $html += '<div class="bidding-detail-item"><div class="c-tit">{LANG.birthday}</div><div class="c-val"><span class="birthday_' + id +'"></span></div></div>';
            $html += '<div class="bidding-detail-item"><div class="c-tit">{LANG.identify_code}</div><div class="c-val"><span class="identify_code_' + id +'"></span></div></div>';

            $.ajax({
                url: location.href,
                type: 'POST',
                data: {
                    'view_price': 1,
                    'id': id
                },
            })
            .done(function(res) {
                try {
                    let result = JSON.parse(res);
                    
                    if (result['khongdudiem'] == 1) {
                        alert("{LANG.point_miss_goods}");
                    } else if (result['khongdudiem'] == 2) {
                        alert("{LANG.point_goods_err}");
                    } else {
                        // Chỉ thực hiện thao tác DOM khi có kết quả thành công
                        button.closest('.courses_detail_price').append($html);
                        button.closest('.courses_detail_point').remove();

                        $(".birthday_" + result['data']['id']).html(result['data']['birthday']);
                        $(".identify_code_" + result['data']['id']).html(result['data']['identify_code']);

                        if (!$("#trudiem").length) {
                            $("body").append('<div id="trudiem"></div>');
                        }

                        $("#trudiem").html(result['notifi']);
                        $("#trudiem").slideDown(500);
                        setTimeout(function() {
                            $("#trudiem").slideUp(500);
                        }, 2000);
                    }
                } catch (error) {
                    console.error("Lỗi xử lý dữ liệu trả về ", error);
                }
            });
        });
    });
</script>

<!-- END: main -->