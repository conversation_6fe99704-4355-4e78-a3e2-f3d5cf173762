<!-- BEGIN: main -->
<div class="investor-competitors">
    <div class="border-bidding">
        <span>{DATA.title_op}</span>
    </div>

    <div class="investor-competitors-content">
        <div class="bidding-list-body">
            <!-- BEGIN: show_note -->
            <p class="alert alert-info">{SHOW_NOTE}</p>
            <br>
            <!-- END: show_note -->

            <table class="bidding-table" id="table_one">
                <thead>
                    <tr>
                        <th>{LANG.number}</th>
                        <th>{LANG.name_investor}</th>
                        <th>{LANG.goithau_trung}</th>
                        <th>{LANG.goithau_truot}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: list_competiors -->
                    <!-- BEGIN: loop -->
                    <tr>
                        <td class="text-center" data-column="{LANG.number}"><span>{COMPETIORS.stt}</span></td>
                        <td data-column="{LANG.name_investor}">
                            <span class="ml-bobile-1">
                                <a href="{COMPETIORS.link_investor_detail}" title="{COMPETIORS.bidder_name}"><span class="bidding-code">{COMPETIORS.orgcode}</span>{COMPETIORS.bidder_name}</a>
                            </span>
                        </td>

                        <!-- BEGIN: lock -->
                        <td data-column="{LANG.goithau_trung}" class="text-center">
                            <div class="coating">
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                                <div class="coating {COMPETIORS.hidden_logo}">
                                    {COMPETIORS.bg}
                                </div>
                            </div>
                        </td>

                        <td data-column="{LANG.goithau_truot}" class="text-center">
                            <div class="coating">
                                <span class="ml-bobile-1">
                                    <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                                </span>
                            </div>
                        </td>
                        <!-- END: lock -->

                        <!-- BEGIN: unlock -->
                        <td class="text-center" data-column="{LANG.time_post_sort}">
                            <span class="ml-bobile-1">
                                {COMPETIORS.num_win}
                            </span>
                        </td>

                        <td class="text-center" data-column="{LANG.so_bo_tong_chi_phi_th_da}">
                            <span class="ml-bobile-1">
                                {COMPETIORS.num_lost}
                            </span>
                        </td>
                        <!-- END: unlock -->
                    </tr>
                    <!-- END: loop -->
                    <!-- END: list_competiors -->
                </tbody>
            </table>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function () {
        var otable = $('#table_one').DataTable({
            // "ordering": false,
            "timeout": 300000,
            "language": {
                "lengthMenu": "{LANG.datatable_lengthMenu}",
                "search": "{LANG.datatable_search}",
                "info": "{LANG.datatable_info}",
                "zeroRecords": "{LANG.datatable_paginate_zeroRecords}",
                "infoFiltered": "{LANG.datatable_paginate_infoFiltered}",
                "infoEmpty": "{LANG.datatable_paginate_infoEmpty}",
                "paginate": {
                    "first": "{LANG.datatable_paginate_first}",
                    "last": "{LANG.datatable_paginate_last}",
                    "next": "{LANG.datatable_paginate_next}",
                    "previous": "{LANG.datatable_paginate_previous}",
                },
            }
        });

        $("select[name='table_one_length']").addClass("form-control");
        $("input[aria-controls='table_one']").addClass("form-control");
    });
</script>
<style type="text/css">
    .modal-lg {
        width: 93% !important;
    }

    #table_one_filter {
        display: inline-block;
        float: right;
    }

    #table_one_length {
        display: inline-block;
    }
</style>
<!-- END: main -->
