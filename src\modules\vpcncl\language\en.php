<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */


if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

$lang_translator['author'] = '';
$lang_translator['createdate'] = '';
$lang_translator['copyright'] = '';
$lang_translator['info'] = '';
$lang_translator['langtype'] = 'lang_module';

// Page title
$lang_module['vilas'] = 'List of VILAS accredited laboratories';
$lang_module['vicas'] = 'List of VICAS certified organizations';
$lang_module['vias'] = 'List of VIAS inspected organizations';
$lang_module['vilas-med'] = 'List of VILAS-MED accredited medical laboratories';
$lang_module['vipas'] = 'VIPAS proficiency testing providers';
$lang_module['viras'] = 'VIRAS reference material producers';
// Page description
$lang_module['vilas_desc'] = 'List of laboratories accredited under the VILAS (Vietnam Laboratory Accreditation Scheme) with detailed information including registration number, organization name, field, status, etc.';
$lang_module['vicas_desc'] = 'The most detailed and complete list of organizations certified by the Vietnam Certification Accreditation Scheme (VICAS).';
$lang_module['vias_desc'] = 'DauThau.info provides a comprehensive list of inspection organizations accredited under VIAS, one of the accreditation programs of the Bureau of Accreditation (BoA).';
$lang_module['vilas-med_desc'] = 'DauThau.info provides a comprehensive list of medical laboratories accredited by VILAS/VILAS-MED with complete information by province, field, and status.';
$lang_module['vipas_desc'] = 'DauThau.info provides a comprehensive list of proficiency testing providers accredited under VIPAS with complete information by province, field, and status.';
$lang_module['viras_desc'] = 'DauThau.info provides a comprehensive list of reference material producers accredited under VIRAS with complete information by province, field, status, registration number, and organization name.';

// Page detail description
$lang_module['vilas_detail_desc'] = 'Detailed information on VILAS laboratories updated by DauThau.info with the latest and most complete information along with clear system documents.';
$lang_module['vicas_detail_desc'] = 'Detailed information on organizations certified by VICAS with complete information by field, location (domestic and international) updated to the latest time.';
$lang_module['vias_detail_desc'] = 'Detailed information on inspection organizations accredited by VIAS (one of BoA\'s programs) with complete information by field, location, and the latest time updates.';
$lang_module['vilas-med_detail_desc'] = 'Detailed information on medical laboratories accredited by VILAS/VILAS-MED with complete information and updated documents according to the latest time.';
$lang_module['vipas_detail_desc'] = 'Detailed information on proficiency testing providers accredited by VIPAS with complete information and documents updated to the latest time.';
$lang_module['viras_detail_desc'] = 'Detailed information on reference material producers accredited by VIRAS with complete information and documents updated to the latest time.';


$lang_module['no_results'] = 'No Results!';
$lang_module['wrong_filter'] = 'Wrong filter';
$lang_module['keyword'] = 'Keywork';
$lang_module['num'] = 'Number';
$lang_module['org_name'] = 'Organization Name';
$lang_module['host_name'] = 'Management unit';
$lang_module['specialize'] = 'Field';
$lang_module['province'] = 'Province';
$lang_module['search'] = 'Search';
$lang_module['accreditation_location'] = 'Accreditation location';
$lang_module['content'] = 'Content';
$lang_module['status'] = 'Status';
$lang_module['dinh_chi'] = 'Suspended';
$lang_module['hoat_dong'] = 'Activity';
$lang_module['huy_bo'] = 'Withdraw';
$lang_module['file_download'] = 'File Download';
$lang_module['date'] = 'Date';
$lang_module['status_select'] = 'Select status';
$lang_module['stt'] = 'STT';
$lang_module['tra_cuu'] = 'Search';
$lang_module['plz_select'] = 'Please select';
$lang_module['select_hint'] = 'Hold Ctrl to select consecutively';

$lang_module['cap_nhat_lai'] = '(Refresh)';
$lang_module['update_err_new'] = 'New organization is posted to the system. You can update again after 30 minutes.';
$lang_module['update_ok'] = 'Update successful. Please wait about 10 minutes for the information to be updated.';
$lang_module['login_popup'] = 'Please <strong><a href="javascript:void(0)" data-toggle="loginFormShow" title="To view full information please Login or Register" >Login</a></strong> or <strong><a href="%s" title="To view full information please Login or Register">Register</a></strong > members to request the system to update the latest data';
$lang_module['update_err_user_last'] = 'Please come back later %s';
$lang_module['effective_date'] = 'Effective date';
$lang_module['cancellation_date'] = 'Cancellation date';
$lang_module['last_update'] = 'Time Updated';
$lang_module['time_query'] = '<br />Search in: %s - Number of results: %s';
$lang_module['crawls_numbers'] = 'Updated %s.';
$lang_module['view_by_tt'] = 'List of %s by %s';
$lang_module['no_title'] = 'Uncategorized';
$lang_module['back'] = 'Return';
$lang_module['notice'] = 'Notify';
$lang_module['note_wrong_page'] = 'Where did you find this link? I am the bearer, can not handle your request!';
$lang_module['note_max_searchpage'] = 'The number of search results is exceeding the software&#39;s display limit of 100 pages. Please use the search engine to narrow your search or return to the previous page.';
