<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 02:25:16 GMT
 */

if (!defined('NV_IS_MOD_LABORATORY')) {
    die('Stop!!!');
}

if (empty($ma_ptn)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
}

$db->sqlreset()
   ->select('*')
   ->from(NV_PREFIXLANG . '_phongtn')
   ->where('ma_so_phongtn = ' . $db->quote($ma_ptn))
   ->order('id_url DESC');
$sth = $db->prepare($db->sql());
$sth->execute();
$array_data_phongtn = [];
while ($row = $sth->fetch()) {
    $row['thoi_gian_cong_nhan'] = '';
    if (preg_match('/([0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4})/', $row['tieu_de_ngay_chung_nhan'], $m)) {
        $row['thoi_gian_cong_nhan'] = $m[1];
    }
    $array_data_phongtn[] = $row;
}

// Sắp xếp theo thời gian chứng nhận từ mới -> củ
$thoi_gian_cong_nhan = array_column($array_data_phongtn, 'thoi_gian_cong_nhan');
array_multisort($thoi_gian_cong_nhan, SORT_DESC, $array_data_phongtn);

$data_detail_phongtn = $array_data_phongtn[0];
$id = $data_detail_phongtn['id'];
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;

if (empty($data_detail_phongtn)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
}

// Đếm số lượt xem
$sesion_name = str_replace("-", "", $module_data . '_' . $op . '_' . $id);
if (!$nv_Request->isset_request($sesion_name, 'session')) {
    $nv_Request->set_Session($sesion_name, NV_CURRENTTIME);
    /*
    $cur_month = intval(date('n', NV_CURRENTTIME));
    if (!empty($data_detail_phongtn['month_view'])) {
        $data_detail_phongtn['month_view'] = explode('.', $data_detail_phongtn['month_view']);
        if ($data_detail_phongtn['month_view'][0] != $cur_month) {
            $data_detail_phongtn['month_view'] = [$cur_month, 1];
        } else {
            $data_detail_phongtn['month_view'][1]++;
        }
    } else {
        $data_detail_phongtn['month_view'] = [$cur_month, 1];
    }
    $db->query("UPDATE " . $db_config['prefix'] . "_phongtn SET totalview=totalview+1, month_view=" . $db->quote(implode('.', $data_detail_phongtn['month_view'])) . " WHERE id=" . $id);
    */
    file_put_contents(NV_ROOTDIR . '/data/setview/' . gmdate('Ymd', NV_CURRENTTIME) . '.' . $db_config['prefix'] . '_phongtn.log', gmdate('H:i:s', NV_CURRENTTIME) . "\t" . $id . "\n", FILE_APPEND);
}

if (!empty($data_detail_phongtn['ma_so_thue'])) {
    // kiểm tra xem phòng thí nghiệm xây dựng có phải là bên mời thầu k bằng mã số thuế
    $solicitor = $db->query("SELECT * FROM " . BID_PREFIX_GLOBAL . "_solicitor_detail as tb1 INNER JOIN " . BID_PREFIX_GLOBAL . "_solicitor as tb2 ON tb1.id = tb2.id WHERE tb1.no_business_licence = " . $db->quote($data_detail_phongtn['ma_so_thue']) . '')->fetch();

    // kiểm tra xem phòng thí nghiệm xây dựng có phải là bên nhà thầu không bằng mã số thuế
    $contractor = $db->query("SELECT * FROM " . BUSINESS_PREFIX_GLOBAL . "_info WHERE active = 1 AND code = " . $db->quote($data_detail_phongtn['ma_so_thue']))->fetch();
}

$xtpl = new XTemplate('detaillaboratory.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$_userid = defined('NV_IS_USER') ? intval($user_info['userid']) : 0;
$xtpl->assign('CHECKSESS_UPDATE', md5($_userid . $data_detail_phongtn['id'] . NV_CACHE_PREFIX . $client_info['session_id']));
$xtpl->assign('URL_UPDATE', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['giaychungnhan'] . '/' . strtolower(change_alias($data_detail_phongtn['tieu_de'])) . '-' . $data_detail_phongtn['id'] . $global_config['rewrite_exturl'], true));

if (defined("NV_IS_USER")) {
    if ($module_captcha == 'recaptcha' and  $global_config['recaptcha_ver'] == 2) {
        $xtpl->assign('RECAPTCHA_ELEMENT', 'recaptcha' . nv_genpass(8));
        $xtpl->assign('N_CAPTCHA', $nv_Lang->getGlobal('securitycode1'));
        $xtpl->parse('main.update.recaptcha');
    }
    $xtpl->parse('main.update');
} else {
    $link_login = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=login&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
    $link_register = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=register&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
    $link = vsprintf($nv_Lang->getModule('log_in_up'), array(
        $link_login,
        $link_register
    ));
    $xtpl->assign('LOGIN', $link);
    $xtpl->parse('main.login');
}

if (preg_match('/([0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4})/', $data_detail_phongtn['tieu_de_ngay_chung_nhan'], $m)) {
    $data_detail_phongtn['thoi_gian_cong_nhan'] = $m[1];
}
if (preg_match('/(Danh mục chỉ tiêu .*giấy chứng nhận này.*?\<\/p\>)/ius', $data_detail_phongtn['noi_dung_html'], $m)) {
    $data_detail_phongtn['noi_dung'] = $m[1];
}
if (!empty($data_detail_phongtn['thoi_gian_dinh_chi'])) {
    $data_detail_phongtn['thoi_gian_dinh_chi'] = date('d/m/Y', $data_detail_phongtn['thoi_gian_dinh_chi']);
    $xtpl->assign('POSTPONED_DATE', $data_detail_phongtn['thoi_gian_dinh_chi']);
    $xtpl->parse('main.postponed');
}

$data_detail_phongtn['title'] = $nv_Lang->getModule('laboratorys_1') . ' ' . $data_detail_phongtn['ma_so_phongtn'];
$data_detail_phongtn['thoi_gian_boc'] = nv_date('d/m/Y', $data_detail_phongtn['thoi_gian_boc']);
$data_detail_phongtn['so_lan_boc_tin'] = ($data_detail_phongtn['so_lan_boc_tin'] == 1) ? $nv_Lang->getModule('cap_nhat_lan_dau') : sprintf($nv_Lang->getModule('cap_nhat_lan_thu'), $data_detail_phongtn['so_lan_boc_tin']);

if (defined('NV_IS_USER')) {
    if (!empty($arr_customs_permission) || !empty($global_array_vip)) {
        define('NV_IS_VIP', true);
    }
}

//Nếu là bên mời thầu
if (!empty($solicitor)) {
    // alias đa ngôn ngữ
    $solicitor['english_name'] = !empty($solicitor['english_name']) ? trim($solicitor['english_name']) : '';
    if (NV_LANG_DATA != 'vi' && !empty($solicitor['english_name'])) {
        $solicitor['alias'] = change_alias($solicitor['english_name']);
        $solicitor['title'] = $solicitor['english_name'];
    }
    $url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['solicitor'] . '/' . $solicitor['alias'] . '-' . $solicitor['id']);
    $xtpl->assign('IS_SOCLICTOR', sprintf($nv_Lang->getModule('is_solicitor'), $url, $solicitor['title'], $solicitor['num_khlcnt'], $solicitor['num_contract'], $solicitor['num_total'], $solicitor['num_tbmt'], $solicitor['num_tbmst'], $solicitor['num_result'], $solicitor['num_cancel'], $solicitor['num_notcode']));
    $xtpl->parse('main.solicitor');
}

//Nếu là nhà thầu
if (!empty($contractor)) {
    $contractor['companyname'] = NV_LANG_DATA == 'vi' ? $contractor['companyname'] : (trim($contractor['officialname'] ?? '') ?: $contractor['companyname']);
    $url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($contractor['companyname']) . '-' . $contractor['id'], true);
    $filedir = NV_ROOTDIR . '/data/business-cache/';
    if (!is_dir($filedir)) {
        mkdir($filedir);
    }

    $cache_file = 'detail_business_' . $contractor['code'] . '_' . NV_LANG_DATA . '-' . (defined('NV_IS_VIP') ? 'vip' : 'user') . '.cache';
    if (file_exists($filedir . $cache_file)) {
        $contents_business_info = file_get_contents($filedir . $cache_file);
    } else {
        $request = [
            'id' => $contractor['id'],
        ];
        $_data = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'GetDataContractor', $request, 'businesslistings', 1);
        if ($_data['status'] == 'success') {
            $contractor['content_static'] = $_data['main'];
            $contractor['content_static'] = str_replace([
                '#openalias#',
                '#viewopenalias#',
                '#resultalias#',
                '#viewalias#',
                '#nv_base_siteurl#',
                '#nv_lang_data#',
                '#lang_companyname_alias#'
            ], [
                $site_mods['bidding']['alias']['open'],
                $site_mods['bidding']['alias']['viewopen'],
                $site_mods['bidding']['alias']['result'],
                $site_mods['bidding']['alias']['view'],
                NV_BASE_SITEURL,
                NV_LANG_DATA,
                change_alias($contractor['companyname'])
            ], $contractor['content_static']);
        } else {
            $contents_business_info = '';
        }
    }
    if (!empty($contents_business_info) && preg_match('/<div\sclass\=\"static\"\>(.*?)\<\/div\>/is', $contents_business_info, $m)) {
        if (isset($m[1])) {
            if (preg_match('/\<blockquote\sclass\=list\>(.*?)\<\/blockquote\>/is', $m[1], $_m)) {
                if (isset($_m[0])) {
                    $contractor['content_static'] = preg_replace('/(\<a href=\"\#.*?\>)/i','',$_m[0]);
                    $contractor['content_static'] = preg_replace('/\<\/a\>/','',$contractor['content_static']);
                    $contractor['content_static'] = str_replace('</strong>', '</strong></a>', $contractor['content_static']);
                    $contractor['content_static'] = str_replace('), <a', ')</a>, <a', $contractor['content_static']);
                    $contractor['content_static'] = preg_replace('/(\d)(\))(\<\/li\>)/','$1$2</a>$3',$contractor['content_static']);
                }
            }
        }
    }
    if (!empty($contractor['content_static'])) {
        $lock_symbol = '<i class="fa fa-lock" aria-hidden="true"></i>';
        if (!defined('NV_IS_VIP')) {
            $contractor['content_static'] = preg_replace('/\<lockvip\>(.*?)\<\/lockvip\>/', $lock_symbol, $contractor['content_static']);
        } else {
            $contractor['content_static'] = preg_replace('/\<lockvip\>(.*?)\<\/lockvip\>/', '$1', $contractor['content_static']);
        }
        // Sửa link không rewrite từ bên API được
        if (preg_match('/(' . str_replace('/', '\/', preg_quote(NV_BASE_SITEURL)) . 'index\.php.+?op=resultdetail)\//', $contractor['content_static'], $m)) {
            $link_result_detail = nv_url_rewrite($m[1], true);
            $contractor['content_static'] = preg_replace('/' . str_replace('/', '\/', preg_quote(NV_BASE_SITEURL)) . 'index.php.+?op=resultdetail\//', $link_result_detail, $contractor['content_static']);
        }
        $xtpl->assign('IS_CONTRACTOR', sprintf($nv_Lang->getModule('is_contractor'), $url, $contractor['companyname']));
        $xtpl->assign('CONTRACTOR_INFO', $contractor['content_static']);
        if (!defined('NV_IS_VIP')) {
            if (defined('NV_IS_USER')) {
                $xtpl->assign('CONTRACTOR_LOCK', $nv_Lang->getModule('sub_for_any_vip'));
            } else {
                $xtpl->assign('CONTRACTOR_LOCK', sprintf($nv_Lang->getModule('log_in_up'), NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']), NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl'])));
            }
        }
        $xtpl->parse('main.contractor');
    }

    $xtpl->assign('CONTRACTOR', $contractor);
}

//Nếu có dữ liệu liên kết với hồ sơ doanh nghiệp trên dauthau.net
if ($data_detail_phongtn['update_tab_ptnxd'] > 0 and !empty($data_detail_phongtn['dtnet_alias'])) {
    $xtpl->assign('DTNET_LINK_BUTTON', 'https://dauthau.net/vi/dn/' . $data_detail_phongtn['dtnet_alias']);
    $xtpl->parse('main.management_unit_info.dtnet_link_button');
}

$xtpl->assign('DETAIL', $data_detail_phongtn);

//  Hiển thị thông tin đơn vị quản lý
if (!empty($contractor)) {
    $xtpl->parse('main.management_unit_info');
}

$stt = 1;
foreach ($array_data_phongtn as $ptn) {
    $ptn['stt'] = $stt++;
    $ptn['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $site_mods['las']['alias']['giaychungnhan'] . '/' . strtolower(change_alias($ptn['tieu_de'])) . '-' . $ptn['id'] . $global_config['rewrite_exturl'];
    $xtpl->assign('CHUNG_NHAN', $ptn);
    $xtpl->parse('main.loop');
}


$xtpl->parse('main');
$contents = $xtpl->text('main');

$base_url = $base_url . '/' . process_ma_ptn($data_detail_phongtn['ma_so_phongtn']);
$page_url = $base_url;
$canonicalUrl = getCanonicalUrl($page_url);
$page_title = $data_detail_phongtn['tieu_de'];
$global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '/' . process_ma_ptn($data_detail_phongtn['ma_so_phongtn']);
$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('laboratorys_1') . ' ' . $data_detail_phongtn['ma_so_phongtn'],
    'link' => $base_url
);
// Không cho đánh tùy ý alias
$base_url_rewrite = nv_url_rewrite($base_url, true);
$base_url_check = str_replace('&amp;', '&', $base_url_rewrite);
$request_uri = rawurldecode($_SERVER['REQUEST_URI']);
if (isset($array_op[2]) or (strpos($_SERVER['REQUEST_URI'], $base_url_check) !== 0 and strpos(NV_MY_DOMAIN . $_SERVER['REQUEST_URI'], $base_url_check) !== 0)) {
    nv_redirect_location($base_url_rewrite);
}
include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
