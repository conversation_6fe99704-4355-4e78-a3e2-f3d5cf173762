<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css" rel="stylesheet" />
<form action="{FORM_ACTION}" method="get">
    <!-- BEGIN: no_rewrite -->
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
    <!-- END: no_rewrite -->
    <div class="row mt-1">
        <div class="form-row">
            <div class="form-group col-md-5">
                <select id="location_tochuc" class="form-control fselect2">
                    <option selected>{LANG.province_select}</option>
                    <!-- BEGIN: province -->
                    <option value="{PROVINCE.alias}" {selected_location}>{PROVINCE.title}</option>
                    <!-- END: province -->
                </select>
            </div>
            <div class="form-group col-md-15">
                <input type="text" class="form-control" name="q" value="{Q}" placeholder="{LANG.keyword}">
            </div>
            <div class="form-group col-md-4">
                <input class="btn btn-primary btn-block" type="submit" value="{LANG.search}">
            </div>
        </div>
    </div>
</form>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        var selects = $('.fselect2');
        for (let i = 0; i < selects.length; i++) {
            $(selects[i]).select2({
                placeholder: $(selects[i]).attr('title'),
                language: nv_lang_interface,
                width: 'resolve',
                dropdownAutoWidth: true,
                dropdownParent: $('body'),
                containerCssClass: 'select2-full-width',
                dropdownCssClass: 'select2-full-width'
            });
        }
        $('.select2-search__field').addClass('form-control');

        $('<style>.select2-full-width{min-width:200px;}.select2-results__options{max-height:500px!important;}</style>').appendTo('head');
    });
</script>
<script type="text/javascript">
    $("form").submit(function() {
        var formAction = $(this).attr("action");
        var location_alias = $("#location_tochuc").find(':selected').val();
        var location_index = $("#location_tochuc option:selected").index();
        if (location_index != 0) {      // nếu không chọn gì cả thì bỏ qua
            $(this).attr("action", formAction + location_alias + '/');
        }
    });
</script>
<!-- END: main -->
