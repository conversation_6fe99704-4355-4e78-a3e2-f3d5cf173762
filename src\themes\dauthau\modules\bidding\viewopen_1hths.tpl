<!-- BEGIN: main -->
<div id="popup_not_dismiss" class="modal fade auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body"></div>
        </div>
    </div>
</div>
<div class="msgshow" id="msgshow"></div>
<!-- BEGIN: popup_login -->
    {POPUP_LOGIN}
<!-- END: popup_login -->

<!-- BEGIN: popup_not_point -->
<script>
$(function() {
    var mess = '{LANG.info_point_not_enought}';
    mess += '<p class="text-center"><a class="btn btn-danger" href="https://id.dauthau.net/{NV_LANG_DATA}/points/#muadiem" target="_blank">{LANG.buy_points}</a></p>';

    $("#popup_not_dismiss").find(".modal-body").html(mess);
    $("#popup_not_dismiss").modal({
        backdrop: "static",
        keyboard: false
    });
});
</script>
<!-- END: popup_not_point -->
<!-- BEGIN: msgshow -->
<script>
alert_msg('{LANG.message_point_view_suss}');
</script>
<!-- END: msgshow -->
<!-- BEGIN: recaptcha -->
<div id="captchaModal" class="modal fade auto-width auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <p class="modal-title">{LANG.recapcha_title}</p>
            </div>
            <div class="modal-body">
                <div>{LANG.recapcha_body}</div>
                <div data-toggle="recaptcha" data-callback="verify_captcha" id="{RECAPTCHA_ELEMENT}"></div>
            </div>
        </div>
    </div>
</div>
<script>
    function verify_captcha(e) {
        click_update();
    }
</script>
<!-- END: recaptcha -->
<div class="bidding-wrapper">
    <div class="bidding-title">
        <!-- <div class="subtl">{LANG.listopendetailt}</div> -->
        <h1 class="tl wrap__text">{VIEW.goi_thau}</h1>
    </div>

    <div class="margin-bottom">
        <div class="prb_container">
            <div class="prb clearfix">
                <!-- BEGIN: mess_item -->
                <span class="prb-progressbar">{MESS}</span>
                <!-- END: mess_item -->
            </div>
            <div class="prb clearfix">
                <!-- BEGIN: prb_item -->
                <!-- BEGIN: if_a -->
                <a class="item" href="{PROCESS.url}"><span class="icn {PROCESS.classes}" title="{PROCESS.title}"></span><span class="tl">{PROCESS.title}</span></a>
                <!-- END: if_a -->
                <!-- BEGIN: if_span -->
                <span class="item"><span class="icn {PROCESS.classes}" title="{PROCESS.title}"></span><span class="tl">{PROCESS.title}</span></span>
                <!-- END: if_span -->
                <!-- END: prb_item -->
            </div>
        </div>
    </div>
    <div class="panel-body btn-share-group">
        <span>Chia sẻ </span>
        <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_fb_share();" title="Chia sẻ lên facebook">
            <span class="icon-facebook"></span>
        </a>
        <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_tw_share('', '{VIEW.goi_thau}');" title="Tweet">
            <span class="icon-twitter"></span>
        </a>
        <a href="javascript:void(0)" class="btn-share btn-copy-link" title="Copy link">
            <em class="fa fa-link"></em>
            <span class="tip" style="display: none;">Copy link thành công</span>
        </a>
    </div>
    <div class="text-right">
        {FILE "button_show_log.tpl"}
    </div>

    <!-- BEGIN: update -->
    <div class="text-right m-bottom">
        <div class="small">
            {LANG.crawl_time}: <strong>{VIEW.fget_time}</strong>
        </div>
        <div class="margin-top-sm m-bottom">
            <span class="small">{VIEW.update_info}</span> <a style="margin-left: auto" id="reupdate" class="btn btn-default btn-xs active" onclick="show_captcha()" href="javascript:void(0)" data-id="{DATA.so_tbmt}" data-check="{CHECKSESS_UPDATE}">{LANG.reupdate}</a><img id="update_wait" style="display: none" src="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/images/load_bar.gif" /><span id="show_error" class="text-danger margin-left" style="display: none"></span>
            <!-- BEGIN: crawl_request_history_button -->
            <a style="margin-left: auto" id="crawl_request_history" class="btn btn-default btn-xs active" href="javascript:void(0)">{LANG.crawl_request_history}</a>
            <!-- END: crawl_request_history_button -->
        </div>
        {FILE "crawl_request_history_list.tpl"}
    </div>
    <!-- END: update -->

    <!-- BEGIN: bienban -->
    <div class="border-bidding data-tab">
        <ul class="nav nav-pills display-flex">
            <li class="active"><h2 class="title__tab_heading"><a id="back-to-normal" class="text-size-base" data-toggle="pill" href="javascript:void(0)">{LANG.back_to_normal_open}</a></h2></li>
            <li><h2 class="title__tab_heading"><a class="btn-ehsdxkt text-size-base" data-toggle="pill" href="javascript:void(0)">{LANG.bb_ehsdxkt}</a></h2></li>
            <!-- BEGIN: dsntdkt--><li><h2 class="title__tab_heading"><a class="btn-dsntdkt text-size-base" data-toggle="pill" href="javascript:void(0)">{LANG.dsntdkt}</a></h2></li><!-- END: dsntdkt-->
            <!-- BEGIN: ehsdxtc--><li><h2 class="title__tab_heading"><a class="btn-ehsdxtc text-size-base" data-toggle="pill" href="javascript:void(0)">{LANG.bb_ehsdxtc}</a></h2></li><!-- END: ehsdxtc-->
        </ul>
    </div>
    <!-- END: bienban -->

    <div class="bidding-detail main-part">
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.so_tbmt}</div>
                <div class="c-val">
                    <!-- BEGIN: tbmt_yes_link -->
                    <a href="{VIEW.link_row}" title="{VIEW.goi_thau}"><span class="bd-code">{VIEW.so_tbmt}</span></a>
                    <!-- END: tbmt_yes_link -->
                    <!-- BEGIN: tbmt_no_link -->
                    <span class="bd-code">{VIEW.so_tbmt}</span>
                    <!-- END: tbmt_no_link -->
                </div>
            </div>
            <div>
                <div class="c-tit">{LANG.trang_thai}</div>
                <div class="c-val">{VIEW.trang_thai_title}</div>
            </div>
        </div>

        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.goi_thau}</div>
            <div class="c-val">{VIEW.goi_thau}</div>
        </div>

        <!-- BEGIN: chu_dau_tu -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.chu_dau_tu}</div>
            <div class="c-val">{VIEW.chu_dau_tu}</div>
        </div>
        <!-- END: chu_dau_tu -->

        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.ben_moi_thau}</div>
            <div class="c-val">
                <!-- BEGIN: bmt_yes_link -->
                    <a class="bidding_link" href="{VIEW.link_solicitor}" title="{VIEW.ben_moi_thau}">{VIEW.ben_moi_thau}</a>
                <!-- END: bmt_yes_link -->
                <!-- BEGIN: bmt_no_link -->
                {VIEW.ben_moi_thau}
                <!-- END: bmt_no_link -->
            </div>
        </div>

        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.id_loai}</div>
                <div class="c-val">{VIEW.id_loai}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.htdt}</div>
                <div class="c-val">{VIEW.hinh_thuc_nhan_hs}</div>
            </div>
        </div>

        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.dia_diem_mo_thau}</div>
            <div class="c-val">
                Website: <a href="http://muasamcong.mpi.gov.vn" target="_blank" rel="nofollow">http://muasamcong.mpi.gov.vn</a>
            </div>
        </div>

        <!-- BEGIN: phuongthuc -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.phuongthuc}</div>
            <div class="c-val">{VIEW.phuongthuc}</div>
        </div>
        <!-- END: phuongthuc -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.ma_khlcnt}</div>
            <div class="c-val"><a href="{VIEW.url_khlcnt}">{VIEW.id_khlcnt}</a></div>
        </div>

        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.ten_khlcnt}</div>
            <div class="c-val">{VIEW.title_khlcnt}</div>
        </div>

        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.t_thhop}</div>
            <div class="c-val">{VIEW.contract_period} {LANG.days}</div>
        </div>

        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.funding_source}</div>
            <div class="c-val">{VIEW.nguon_von}</div>
        </div>

        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.hinh_thuc_lua_chon}</div>
                <div class="c-val">{VIEW.hinh_thuc_lc}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.hinh_thuc_hop_dong}</div>
                <div class="c-val">{VIEW.hinh_thuc_hd}</div>
            </div>
        </div>
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.thoi_diem_mo_thau}</div>
                <div class="c-val">{VIEW.thoi_diem_mo_thau}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.thoi_diem_hoan_thanh}</div>
                <div class="c-val">{VIEW.dxtc_finish_time}</div>
            </div>
        </div>

        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.gia}</div>
                <div class="c-val">{VIEW.gia_goi_thau_format}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.num_business}</div>
                <div class="c-val">{VIEW.so_luong_nha_thau}</div>
            </div>
        </div>

        <!-- BEGIN: estimate_price -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.gia_goi_thau}</div>
            <div class="c-val">{VIEW.bid_estimate_price}</div>
        </div>
        <!-- END: estimate_price -->

        <!-- BEGIN: danhgia_gia -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.danhgia_gia}</div>
            <div class="c-val">{VIEW.danhgia_gia}</div>
        </div>
        <!-- END: danhgia_gia -->
        <!-- BEGIN: danhgia_kt -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.danhgia_kt}</div>
            <div class="c-val">{VIEW.danhgia_kt}</div>
        </div>
        <!-- END: danhgia_kt -->
        <!-- BEGIN: ly_do_huy -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.ly_do_huy}</div>
            <div class="c-val">{VIEW.ly_do_huy}</div>
        </div>
        <!-- END: ly_do_huy -->

        <!-- BEGIN: file_baocao -->
        <div class="bidding-detail-item">
            <div class="c-tl">File báo cáo kết quả</div>
            <div class="c-vl">
                {VIEW.file_baocao}
            </div>
            <form name=ebidForm>
                <input type=hidden name=bidNo value='{VIEW.bidNo}'>
                <input type=hidden name=bidTurnNo value='{VIEW.bidTurnNo}'>
                <input type=hidden name=bidCls value='0'>
                <input type=hidden name=rebidNo value='0'>
                <input type=hidden name=fileName>
                <input type=hidden name=cmd value='download'>
                <input type=hidden name=evalType>
                <input type=hidden name=flag>
                <input type=hidden name=reportType>
            </form>

            <script type="text/javascript">
                function getAttach(evalType, reportName) {
                    document.ebidForm.fileName.value = reportName;
                    document.ebidForm.evalType.value = evalType;
                    document.ebidForm.method='post';
                    document.ebidForm.target='_self';
                    document.ebidForm.action='http://muasamcong.mpi.gov.vn:8082/servlet/GC/EP_COV_GCE202';
                    document.ebidForm.submit();
                    return;
                }
            </script>
        </div>
        <!-- END: file_baocao -->

    </div>
    <!-- BEGIN: bb_ehsdxkt -->
    <div class="bidding-detail bb-ehsdxkt bb-ehsdxtc hidden">
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.so_tbmt}</div>
            <div class="c-val">
                <!-- BEGIN: tbmt_yes_link -->
                <a href="{VIEW.link_row}" title="{VIEW.goi_thau}"><span class="bd-code">{VIEW.so_tbmt}</span></a>
                <!-- END: tbmt_yes_link -->
                <!-- BEGIN: tbmt_no_link -->
                <span class="bd-code">{VIEW.so_tbmt}</span>
                <!-- END: tbmt_no_link -->
            </div>
        </div>

        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.ten_goi_thau}</div>
            <div class="c-val">{VIEW.goi_thau}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.gia}</div>
            <div class="c-val">{VIEW.gia_goi_thau_format}</div>
        </div>
        <!-- BEGIN: estimate_price -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.gia_goi_thau}</div>
            <div class="c-val">{VIEW.bid_estimate_price}</div>
        </div>
        <!-- END: estimate_price -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.ben_moi_thau}</div>
            <div class="c-val">
                <!-- BEGIN: bmt_yes_link -->
                    <a class="bidding_link" href="{VIEW.link_solicitor}" title="{VIEW.ben_moi_thau}">{VIEW.ben_moi_thau}</a>
                <!-- END: bmt_yes_link -->
                <!-- BEGIN: bmt_no_link -->
                {VIEW.ben_moi_thau}
                <!-- END: bmt_no_link -->
            </div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.hinh_thuc_lua_chon}</div>
            <div class="c-val">{VIEW.hinh_thuc_lc}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.hinh_thuc_hop_dong}</div>
            <div class="c-val">{VIEW.hinh_thuc_hd}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.num_business}</div>
            <div class="c-val">{VIEW.so_luong_nha_thau}</div>
        </div>
        <div class="bidding-detail-item bb-ehsdxkt">
            <div class="c-tit">{LANG.thoi_diem_hoan_thanh}</div>
            <div class="c-val">{VIEW.thoi_diem_hoan_thanh}</div>
        </div>
        <div class="bidding-detail-item bb-ehsdxtc">
            <div class="c-tit">{LANG.thoi_diem_hoan_thanh}</div>
            <div class="c-val">{VIEW.dxtc_finish_time}</div>
        </div>
    </div>
    <!-- END: bb_ehsdxkt -->

    <!-- BEGIN: dsntdkt -->
    <div class="bb-dsntdkt hidden">
        <div class="bidding-detail">
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.so_tbmt}</div>
                <div class="c-val">
                    <!-- BEGIN: tbmt_yes_link -->
                    <a href="{VIEW.link_row}" title="{VIEW.goi_thau}"><span class="bd-code">{VIEW.so_tbmt}</span></a>
                    <!-- END: tbmt_yes_link -->
                    <!-- BEGIN: tbmt_no_link -->
                    <span class="bd-code">{VIEW.so_tbmt}</span>
                    <!-- END: tbmt_no_link -->
                </div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.status_dsntdkt}</div>
                <div class="c-val">{LANG.posted}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.ben_moi_thau}</div>
                <div class="c-val">
                    <!-- BEGIN: bmt_yes_link -->
                        <a class="bidding_link" href="{VIEW.link_solicitor}" title="{VIEW.ben_moi_thau}">{VIEW.ben_moi_thau}</a>
                    <!-- END: bmt_yes_link -->
                    <!-- BEGIN: bmt_no_link -->
                    {VIEW.ben_moi_thau}
                    <!-- END: bmt_no_link -->
                </div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.chu_dau_tu}</div>
                <div class="c-val">{VIEW.chu_dau_tu}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.ten_goi_thau}</div>
                <div class="c-val">{VIEW.goi_thau}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.gia}</div>
                <div class="c-val">{VIEW.gia_goi_thau_format}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.hinh_thuc_hop_dong}</div>
                <div class="c-val">{VIEW.hinh_thuc_hd}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.hinh_thuc_lua_chon}</div>
                <div class="c-val">{VIEW.hinh_thuc_lc}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.phuongthuc}</div>
                <div class="c-val">{VIEW.phuongthuc}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.field}</div>
                <div class="c-val">{VIEW.id_loai}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit">{LANG.funding_source}</div>
                <div class="c-val">{VIEW.nguon_von}</div>
            </div>
        </div>
        <div class="bidding-sub-title display-flex">
            <div>{LANG.quyet_dinh_phe_duyet}:</div>
        </div>
        <div class="bidding-detail">
            <div class="bidding-detail-item">
                <div class="c-tit c-tit-lg">{LANG.dsntdkt_approval_date}</div>
                <div class="c-val c-tit-lg">{VIEW.approval_date}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit c-tit-lg">{LANG.dsntdkt_approval_no}</div>
                <div class="c-val c-tit-lg">{VIEW.decision_no}</div>
            </div>
            <div class="bidding-detail-item">
                <div class="c-tit c-tit-lg">{LANG.dsntdkt_approval_agent}</div>
                <div class="c-val c-tit-lg">{VIEW.decision_agent}</div>
            </div>
            <!-- BEGIN: decision_file -->
            <div class="bidding-detail-item">
                <div class="c-tit c-tit-lg">{LANG.dsntdkt_approval_file}</div>
                <div class="c-val">
                    <p id="tai_quyet_dinh_kh">{DOWNLOAD_MESS}</p>
                    <!-- BEGIN: point_or_t0 -->
                    <div class="container-download">
                        <div class="column-download">
                            <div class="button-container">
                                <div class="center-buttons">
                                    <button class="btn btn-primary" onclick="buy_fastlink(this)" data-id="{VIEW.id}" data-confirm="{LANG.down_point_confirm}">{LANG.link_file_fast}</button>
                                </div>
                                <p>{info_T0}</p>
                            </div>
                        </div>
                        <div class="column-download">
                            <div class="button-container">
                                <div class="center-buttons">
                                    <button class="btn btn-primary" onclick="redirect_link('{link_T0}')">{LANG.buy_TO}</button>
                                </div>
                                <p>{LANG.show_info_down_t0_2227}</p>
                            </div>
                        </div>
                    </div>
                    <!-- END: point_or_t0 -->
                    <!-- BEGIN: vip_size_info -->
                    <p>
                        <em class="fa fa-bell"></em> {VIPSIZE_MESS}
                    </p>
                    <!-- END: vip_size_info -->

                    <div class="tab-content download {ICON_PLANE}">
                        <div class="tab-pane fade{HOME_ACTIVE} in " id="quyet_dinh_nav_first" role="tabpanel" aria-labelledby="quyet_dinh_nav_first_tab">
                            <div class="list-group-item display-flex"><span id="quyet_dinh_normal">{QUYET_DINH_NORMAL}</span></div>

                        </div>
                        <!-- BEGIN: is_point_down_show2 -->
                        <div class="tab-pane fade {POINT_ACTIVE} in list-group download-link is_points{IS_OTHER_BROWSER}" id="quyet_dinh_nav_second" role="tabpanel" aria-labelledby="quyet_dinh_nav_second_tab">{QUYET_DINH}</div>
                        <!-- END: is_point_down_show2 -->
                    </div>
                    <!-- BEGIN: if_ie_down -->
                    <small><i class="fa fa-paper-plane-o"></i> {note_ie_down}</small>
                    <br>
                    <small><i class="fa fa-exclamation-circle"></i> {LANG.note_ehsmt} </small>
                    <!-- END: if_ie_down -->
                </div>
            </div>
            <!-- END: decision_file -->
        </div>
    </div>

    <!-- END: dsntdkt -->

    <!-- BEGIN: business -->

    <div class="row text-right">
        <div class="col-md-18"></div>
        <div class="col-md-6">
            <select id="view" class="form-control">
                <option value="1" class="{HIDDEN}">{LANG.option_open_1}</option>
                <option value="2" selected>{LANG.option_open_2}</option>
            </select>
        </div>
    </div>
    <div id="subdiv">
    <!-- BEGIN: show_list_subdiv -->
        <div class="bidding-sub-title display-flex">
            <div>{LANG.thongtinphanlo}:</div>
        </div>
        <div class="bidding-detail-item">
            <div class="box__table">
                <table class="table table-bordered table-striped bidding-table table-sticky-head">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>{LANG.subdivision_code}</th>
                            <th>{LANG.subdivision_name}</th>
                            <th>{LANG.madinhdanh}</th>
                            <th>{LANG.bidder_name}</th>
                            <th class="main-part bb-ehsdxkt">{LANG.hieuluce}</th>
                            <th class="main-part bb-ehsdxkt">{LANG.giatribd}</th>
                            <th class="main-part bb-ehsdxkt">{LANG.hieuluc_bddt}</th>
                            <th class="bb-dsntdkt hidden">{LANG.thoi_gian_thuc_hien}</th>
                            <th class="bb-dsntdkt hidden">{LANG.reason_choose}</th>
                            <th class="bb-ehsdxtc hidden">{LANG.tender_price}</th>
                            <th class="bb-ehsdxtc hidden">{LANG.tender_price_discount_percent}</th>
                            <th class="bb-ehsdxtc hidden">{LANG.tender_price_discount}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- BEGIN: loop -->

                            <tr>
                                <!-- BEGIN: show_td -->
                                <td data-column="#" <!-- BEGIN: rowspan --> rowspan="{VALUE.rowspan}"<!-- END: rowspan -->><div>{VALUE.stt}</div></td>
                                <td data-column="{LANG.subdivision_code}" <!-- BEGIN: rowspan2 --> rowspan="{VALUE.rowspan}"<!-- END: rowspan2 -->><div>{VALUE.lotno}</div></td>
                                <td  data-column="{LANG.subdivision_name}"<!-- BEGIN: rowspan3 --> rowspan="{VALUE.rowspan}"<!-- END: rowspan3 -->><div>{VALUE.lotname}</div></td>
                                <!-- END: show_td -->

                                <td data-column="{LANG.madinhdanh}"><div>{BSSUB.orgcode}</div></td>
                                <td data-column="{LANG.bidder_name}">
                                    <div>
                                        <!-- BEGIN: yes_link -->
                                        <a href="{BSSUB.link}">{BSSUB.contractorname}</a>
                                        <!-- END: yes_link -->

                                        <!-- BEGIN: no_link -->
                                        {BSSUB.contractorname}
                                        <!-- END: no_link -->
                                    </div>
                                </td>
                                <td data-column="{LANG.hieuluce}" class="main-part bb-ehsdxkt"><div>{BSSUB.bidvaliditynum}</div></td>
                                <td data-column="{LANG.giatribd}" class="main-part bb-ehsdxkt"><div>{BSSUB.bidguarantee}</div></td>
                                <td data-column="{LANG.hieuluc_bddt}" class="main-part bb-ehsdxkt"><div>{BSSUB.bidguaranteevalidity}</div></td>
                                <td data-column="{LANG.thoi_gian_thuc_hien}" class="bb-dsntdkt hidden"><div>{BSSUB.cperiod}</div></td>
                                <td data-column="{LANG.reason_choose}" class="bb-dsntdkt hidden"><div>{BSSUB.reason}</div></td>
                                <td data-column="{LANG.tender_price}" class="bb-ehsdxtc hidden"><div>{BSSUB.gia_du_thau}</div></td>
                                <td data-column="{LANG.tender_price_discount_percent}" class="bb-ehsdxtc hidden"><div>{BSSUB.tile_giamgia}</div></td>
                                <td data-column="{LANG.tender_price_discount}" class="bb-ehsdxtc hidden"><div>{BSSUB.gia_sau_giam}</div></td>
                            </tr>
                        <!-- END: loop -->
                    </tbody>
                </table>
            </div>
        </div>
        <div class="text-center">
            {GENERATE_PAGE_SUB}
        </div>
    <!-- END: show_list_subdiv -->
    </div>

    <div id="theonhathau">
        <!-- BEGIN: DXKT -->
        <div class="bidding-sub-title display-flex">
            <div>{LANG.ttnttd}:</div>
            <div style="margin-left: auto">{LANG.num_business}: {VIEW.so_luong_nha_thau}</div>
        </div>

        <!-- BEGIN: loop -->
        <div class="bidding-detail">
            <div class="bidding-detail-item">
                <div class="cc-tit">
                    <div class="box_flex">
                        <div>
                            <span class="label label-default">#{BUSINESS.STT}</span>
                            <!-- BEGIN: vip -->
                            <span class="vip-code" title="{BUSINESS.vip_list}">VIP</span>
                            <!-- END: vip -->
                            <!-- BEGIN: ten_nha_thau_yes_link -->
                            <a href="{BUSINESS.link}">{BUSINESS.ten_nha_thau}</a>
                            <!-- END: ten_nha_thau_yes_link -->

                            <!-- BEGIN: ten_nha_thau_no_link -->
                            {BUSINESS.ten_nha_thau}
                            <!-- END: ten_nha_thau_no_link -->
                        </div>

                        <div class="box_flex">
                            <!-- BEGIN: add_cmp_btn -->
                            <a href="#" class="btn-compare-actt{HIDDEN_ADD_CMP} btn-cmp-normal" data-id="{BUSINESS.bid}" onclick="addCttCompare({BUSINESS.bid}, '{BUSINESS.checkaddcp}');"><em class="fa fa-plus-circle">&nbsp;</em>{LANG.add_compare}</a>
                            <a href="#" class="btn-compare-rctt{HIDDEN_REM_CMP} btn-cmp-normal" data-id="{BUSINESS.bid}" onclick="removeCttCompare({BUSINESS.bid}, '{BUSINESS.checkremvcp}');"><em class="fa fa-minus-circle">&nbsp;</em>{LANG.remove_compare}</a>
                            &nbsp;
                            <!-- END: add_cmp_btn -->
                            <!-- BEGIN: ghichu -->
                            <span class="main-part bb-dsntdkt bb-ehsdxtc ghichu {BUSINESS.class}">{BUSINESS.ghichu}</span>
                            <!-- END: ghichu -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- BEGIN: lien_danh -->
            <div class="bidding-detail-item col-four">
                <div>
                    <div class="c-tl">{LANG.joint_venture}</div>
                    <div class="c-vl">{BUSINESS.ten_lien_danh}</div>
                </div>
                <div>
                    <div class="c-tl">{LANG.partnership}</div>
                    <div class="c-vl">{BUSINESS.title_partnership}</div>
                </div>
            </div>
            <!-- END: lien_danh -->
            <div class="bidding-detail-item col-four">
                <div>
                    <div class="c-tl">{LANG.org_code}</div>
                    <div class="c-vl">{BUSINESS.so_dkkd}</div>
                </div>
                <div>
                    <div class="c-tl">{LANG.province}</div>
                    <div class="c-vl">{BUSINESS.province}</div>
                </div>
            </div>
            <!-- BEGIN: dambao -->
            <div class="bidding-detail-item col-four main-part bb-ehsdxkt">
                <div>
                    <div class="c-tl">{LANG.hieu_luc_hskt}</div>
                    <div class="c-vl">{BUSINESS.hieu_luc_hskt} {LANG.days}</div>
                </div>
                <div>
                    <div class="c-tl">{LANG.hieu_luc_bd}</div>
                    <div class="c-vl">{BUSINESS.hieu_luc_bao_dam} {LANG.days}</div>
                </div>
            </div>
            <div class="bidding-detail-item col-four main-part bb-ehsdxkt">
                <div>
                    <div class="c-tit">{LANG.thoi_gian_thuc_hien}</div>
                    <div class="c-val">{BUSINESS.thoi_gian_thuc_hien}</div>
                </div>
                <div>
                    <div class="c-tit">{LANG.bid_security}</div>
                    <div class="c-val">{BUSINESS.bao_dam}</div>
                </div>
            </div>
            <!-- END: dambao -->

            <!-- BEGIN: no_dambao -->
            <div class="bidding-detail-item col-four main-part bb-ehsdxkt">
                <div>
                    <div class="c-tl">{LANG.hieu_luc_hskt}</div>
                    <div class="c-vl">{BUSINESS.hieu_luc_hskt} {LANG.days}</div>
                </div>
                <div>
                    <div class="c-tit">{LANG.thoi_gian_thuc_hien}</div>
                    <div class="c-val">{BUSINESS.thoi_gian_thuc_hien}</div>
                </div>
            </div>
            <!-- END: no_dambao -->

            <!-- BEGIN: tech_ok -->
            <div class="bidding-detail-item bb-dsntdkt hidden">
                <div class="c-tit">{LANG.thoi_gian_thuc_hien}</div>
                <div class="c-val">{BUSINESS.thoi_gian_thuc_hien}</div>
            </div>
            <div class="bidding-detail-item main-part bb-dsntdkt">
                <div class="c-tl">{LANG.reason_choose}</div>
                <div class="c-vl">{LANG.tech_ok_detail}</div>
            </div>
            <!-- END: tech_ok -->
            <!-- BEGIN: tech_ng -->
            <div class="bidding-detail-item bb-dsntdkt hidden">
                <div class="c-tit">{LANG.thoi_gian_thuc_hien}</div>
                <div class="c-val">{BUSINESS.thoi_gian_thuc_hien}</div>
            </div>
            <div class="bidding-detail-item main-part bb-dsntdkt">
                <div class="c-tl">{LANG.tech_ng_reason}</div>
                <div class="c-vl">{BUSINESS.ng_reason}</div>
            </div>
            <!-- END: tech_ng -->
            <!-- BEGIN: price -->
            <div class="bidding-detail-item col-four main-part bb-ehsdxtc">
                <div>
                    <div class="c-tl">{LANG.tender_price}</div>
                    <div class="c-vl">{BUSINESS.gia_du_thau}</div>
                </div>
                <div>
                    <div class="c-tl">{LANG.tender_price_discount_percent}</div>
                    <div class="c-vl">{BUSINESS.tyle_giamgia_old}</div>
                </div>
            </div>
            <div class="bidding-detail-item col-four main-part bb-ehsdxtc">
                <div>
                    <div class="c-tl">{LANG.tender_price_discount}</div>
                    <div class="c-vl">{BUSINESS.gia_sau_giam}</div>
                </div>
                <div>
                    <div class="c-tl">{LANG.point}</div>
                    <div class="c-vl">{BUSINESS.diem}</div>
                </div>
            </div>
            <!-- END: price -->
            <!-- BEGIN: num_tbmt -->
            <div class="bidding-detail-item col-four">
                <div>
                    <div class="c-vl"><strong>{LANG.so_phan_gt_thamdu}:  <span class="label label-success">{BUSINESS.num_tbmt}</span></strong></div>
                </div>
            </div>

            <div class="bidding-detail-item">
                <div class="list_tbtm">
                    <!-- BEGIN: loop -->
                    <p class="list_tbtm__item"><strong>{ROW.stt}</strong>. {ROW.lotNo} - {ROW.lotName}</p>
                    <!-- END: loop -->
                </div>
            </div>
            <!-- END: num_tbmt -->

            <!-- BEGIN: show_list_business -->
            <div class="bidding-detail-item col-four">
                <div>
                    <div class="c-vl"><strong>{LANG.list_liendanh}</strong></div>
                </div>
            </div>

            <div class="bidding-detail-item col-four">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th class="text-center">#</th>
                            <th>{LANG.bidder_name}</th>
                            <th>{LANG.partnership}</th>
                            <th>{LANG.compare}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- BEGIN: loop_liendanh -->
                        <tr>
                            <th class="text-center">{VALUE.stt}</th>
                            <td>

                                <!-- BEGIN: ten_nha_thau_yes_link -->
                                <a href="{VALUE.link}">{VALUE.ten_nha_thau}</a>
                                <!-- END: ten_nha_thau_yes_link -->

                                <!-- BEGIN: ten_nha_thau_no_link -->
                                {VALUE.ten_nha_thau}
                                <!-- END: ten_nha_thau_no_link -->
                            </td>
                            <td>{VALUE.title_partnership}</td>
                            <td>
                                <a href="#" class="btn-compare-actt{HIDDEN_ADD_CMP} btn-cmp-normal" data-id="{VALUE.bid}" onclick="addCttCompare({VALUE.bid}, '{VALUE.checkaddcp}');"><em class="fa fa-plus-circle">&nbsp;</em>{LANG.add_compare}</a>
                                <a href="#" class="btn-compare-rctt{HIDDEN_REM_CMP} btn-cmp-normal" data-id="{VALUE.bid}" onclick="removeCttCompare({VALUE.bid}, '{VALUE.checkremvcp}');"><em class="fa fa-minus-circle">&nbsp;</em>{LANG.remove_compare}</a>
                            </td>
                        </tr>
                        <!-- END: loop_liendanh -->
                    </tbody>
                </table>
            </div>
            <!-- END: show_list_business -->
        </div>
        <!-- END: loop -->
        <div class="text-center">
            {GENERATE_PAGE_BUSINESS}
        </div>
        <!-- END: DXKT -->
        <!-- END: business -->
    </div>

    <div class="margin-top margin-bottom display-flex">
        <div class="h3">
            <span class="label label-primary">{LANG.totalview}: <span class="badge">{VIEW.totalview}</span></span>
        </div>
    </div>
</div>
<br />
{FILE "modal_log.tpl"}
<div id="confirm" class="modal fade auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body"></div>
            <div class="modal-footer">
                <span class="button"></span>
                <button type="button" class="ok btn btn-primary">{LANG.ok}</button>
                <button type="button" data-dismiss="modal" class="btn">{LANG.close}</button>
            </div>
        </div>
    </div>
</div>
<script>
    var checkess='{NV_CHECK_SESSION}';
    const LNG = {read:"{LANG.read}", origlink:"{LANG.origlink}", otherlink:"{LANG.otherlink}", viplink: "{LANG.viplink}"};
</script>
<script type="text/javascript">
    <!-- BEGIN: countdown -->startTimer({COUNTDOWN}, $(".countdown"));<!-- END: countdown -->
    $("#subdiv").hide();
    $(document).ready(function($) {
        $(".btn__open").click(function(event) {
            $.ajax({
                url: window.location.href,
                type: 'POST',
                data: {
                    'action':'open_record',
                    'status': $(this).val()
                },
                success:function(data) {
                    $(".open_record_viewopen > #myModal").html(data['html'])
                }
            })
        });

        $("#view").change(function() {
            if ($(this).val() == 1) {
                $("#subdiv").css("visibility", "visible")
                $("#subdiv").show(400);
                $("#theonhathau").hide();
            }

            if ($(this).val() == 2) {
                $("#theonhathau").css("visibility", "visible")
                $("#theonhathau").show(400);
                $("#subdiv").hide();
            }
        });

        // $('.btn-ehsdxkt, .btn-dsntdkt, .btn-ehsdxtc, #back-to-normal').click(function() {
        //     $('#view').val(2);
        //     $('#view').trigger('change');
        // });

        $('.btn-ehsdxkt').click(function() {
            $('.main-part').addClass('hidden');
            $('.bb-dsntdkt').addClass('hidden');
            $('.bb-ehsdxtc').addClass('hidden');

            $('.business-fail').parents('.bidding-detail').removeClass('hidden');

            $('.bb-ehsdxkt').removeClass('hidden');

            window.location.hash = '#ehsdxkt';
        });

        $('.btn-dsntdkt').click(function() {
            $('.main-part').addClass('hidden');
            $('.bb-ehsdxkt').addClass('hidden');
            $('.bb-ehsdxtc').addClass('hidden');

            $('.business-fail').parents('.bidding-detail').removeClass('hidden');

            $('.bb-dsntdkt').removeClass('hidden');
            window.location.hash = '#dsntdkt';
        });

        $('.btn-ehsdxtc').click(function() {
            $('.main-part').addClass('hidden');
            $('.bb-ehsdxkt').addClass('hidden');
            $('.bb-dsntdkt').addClass('hidden');

            $('.business-fail').parents('.bidding-detail').addClass('hidden');

            $('.bb-ehsdxtc').removeClass('hidden');
            window.location.hash = '#ehsdxtc';
        });

        $('#back-to-normal').click(function() {
            $('.bb-ehsdxkt').addClass('hidden');
            $('.bb-dsntdkt').addClass('hidden');
            $('.bb-ehsdxtc').addClass('hidden');

            $('.business-fail').parents('.bidding-detail').removeClass('hidden');

            $('.main-part').removeClass('hidden');
            window.location.hash = '#tonghop';
        });
        show_as_step();
    });
    function nv_open_page_business(page) {
        num_page = page == "" ? 1 : page.substr(6);
        $.post('', {ajax_business: 1, page: num_page}, (res) => {
            $('#theonhathau').html(res);
            show_as_step();
        });
    }
    function nv_open_page_sub(page) {
        num_page = page == "" ? 1 : page.substr(6);
        $.post('', {ajax_sub: 1, page: num_page}, (res) => {
            $('#subdiv').html(res);
            show_as_step();
        });
    }
    function show_as_step() {
        switch (window.location.hash) {
            case '#ehsdxkt':
                $('.btn-ehsdxkt').trigger('click');
                break;
            case '#dsntdkt':
                $('.btn-dsntdkt').trigger('click');
                break;
            case '#ehsdxtc':
                $('.btn-ehsdxtc').trigger('click');
                break;
            case '#tonghop':
                $('#tonghop').trigger('click');
                break;
        }
    }
    link_reformat();
    $(document).on("click", ".download-file",function(){
        var idfile = $(this).attr("id-file");
        var checkss = $(this).attr("checkss");
        if (idfile !== "undefined" ) {
            $.ajax({
                url: location.href,
                type: 'POST',
                data: {
                    'action': 'download_static',
                    'idfile' : idfile,
                    'open_id' : {VIEW.id},
                    'checkss' : checkss
                },
                success: function(data) {
                    if (data['res'] == 'success') {
                        window.open(data['link'], '_blank');
                    } else {
                        alert(data['message']);
                    }
                }
            })
        }
    });
</script>

<!-- END: main -->
<!-- BEGIN: error -->
<div class="alert alert-danger">{LANG.error_data}</div>
<!-- END: error -->
