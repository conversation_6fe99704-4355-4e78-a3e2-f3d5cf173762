<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2014 VINADES., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 3/9/2010 23:25
 */

if (!defined('NV_MAINFILE'))
    die('Stop!!!');

if (!nv_function_exists('list_lab')) {
    /**
     * list_industry()
     *
     * @param mixed $block_config
     * @return
     */
    function list_lab($block_config)
    {
        global $db, $nv_Cache, $nv_Request, $array_op, $global_config, $module_info, $module_name, $my_head, $site_mods, $module_file, $nv_Lang;
        $module = $block_config['module'];
        $q = $nv_Request->get_title('q', 'post,get');
        $location_alias = '';
        if (isset($array_op[0])) {
            $location_alias = strtolower($array_op[0]);
        }

        if (file_exists(NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file . "/block_search_lab.tpl")) {
            $block_theme = $module_info['template'];
        } else {
            $block_theme = "default";
        }
        $db->sqlreset()
            ->select('id, title, alias')
            ->from(NV_PREFIXLANG . '_' . 'location_province')
            ->order('title ASC');
        $array_data['province'] = $nv_Cache->db($db->sql(), '', 'location_province');
        // Thêm giá trị chưa phân loại vào tim kiếm
        $new_item = array(
            'id' => 0,
            'title' => $nv_Lang->getModule('no_title'),
            'alias' => change_alias($nv_Lang->getModule('no_title'))
        );
        array_unshift($array_data['province'], $new_item);

        $location = '';
        foreach($array_data['province'] as $value) {
            $_tmp = strtolower($value['alias']);
            if (strcmp($location_alias, $_tmp) == 0) {
                $location = $_tmp;
            }
        }

        $xtpl = new XTemplate("block_search_lab.tpl", NV_ROOTDIR . "/themes/" . $block_theme . "/modules/" . $module_file);
        $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
        if ($global_config['rewrite_enable']) {
                $xtpl->assign('FORM_ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name, true));
            } else {
            $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php');
            $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
            $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
            $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
            $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
            $xtpl->assign('MODULE_NAME', $module_name);
            $xtpl->parse('main.no_rewrite');
        }
        foreach ($array_data['province'] as $province) {
            $xtpl->assign('PROVINCE', $province);
            $xtpl->assign('selected_location', (strcmp(strtolower($province['alias']), $location) == 0) ? 'selected' : '');
            $xtpl->parse('main.province');
        }
        $xtpl->assign('Q', $q);
        $xtpl->parse('main');
        return $xtpl->text('main');
    }
}

if (defined('NV_SYSTEM')) {
    $content = list_lab($block_config);
}
