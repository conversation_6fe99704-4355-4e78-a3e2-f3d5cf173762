<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 10 Nov 2020 06:56:08 GMT
 */
if (!defined('NV_IS_MOD_DAU_GIA')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('thong_bao_to_chuc_dau_gia');
$key_words = $module_info['keywords'];

$module_config_view = $module_config[$module_name];
$home_page = $nv_Request->get_int('home_page', 'get', 0);
$perpage = ($home_page == 1) ? 10 : 20;
$page = $nv_Request->get_page('page', 'get', 1);
if ($page < 1) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_wrong_page') . $btn, 'danger');

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

/**
 * thao: cho sửa dùm anh nếu page > 100 báo lỗi:
 * Vui lòng thay đổi tiêu chí tìm kiếm để có thông tin bạn cần
 */
if ($page > 100) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_max_searchpage') . $btn);

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}
$error = [];
// get các trường tìm kiếm
$q = $nv_Request->get_title('q', 'post,get'); // Từ khóa chính
$l = $nv_Request->get_int('l', 'post', -1);
$key_search2 = $nv_Request->get_title('q2', 'post,get'); // Từ khóa bổ sung
$search_one_key = $nv_Request->get_int('search_one_key', 'post,get', 0); // Một trong các từ khóa bổ sung là bắt buộc
$without_key = $nv_Request->get_title('without_key', 'post,get'); // Từ khóa loại trừ
$is_advance = $nv_Request->get_int('is_advance', 'get', 0);
$search_type_content = $nv_Request->get_int('search_type_content', 'post,get', 0); // 0. Tương đối; 1. Tuyệt đối
$par_search = $nv_Request->get_int('par_search', 'post,get', 0);
$search_kind = $nv_Request->get_int('searchkind', 'post,get', 0);
$type_info3 = $nv_Request->get_int('type_info3', 'post,get', 0);
$type_search = $nv_Request->get_int('type_search', 'post,get', 3);
$kqlc_tc_dgts = $nv_Request->get_int('ketqua_luachon_tochuc_dgts', 'post,get', 0);
$price_from = $nv_Request->get_string('keyword_min_bid_prices', 'get', ''); // giáss
$price_from = floatval(str_replace(',', '', $price_from));
$price_to = $nv_Request->get_string('keyword_max_bid_prices', 'get', '');
$price_to = floatval(str_replace(',', '', $price_to));

$keyword_id_bidder = $nv_Request->get_int('keyword_id_bidder', 'get', 0);
$sprovince = $nv_Request->get_int('keyword_id_province', 'get', 0);
$sdistrict = $nv_Request->get_int('keyword_id_district', 'get', 0);

$sfrom = nv_substr($nv_Request->get_title('sfrom', 'get', ''), 0, 10); // thời gian
$sto = nv_substr($nv_Request->get_title('sto', 'get', ''), 0, 10);

if (!empty($q) and $sfrom == '' and $sto == '') {
    if (preg_match("/^20([0-9]{2})([0-9]{2})([0-9]+)/", $q, $m)) {
        $today = mktime(0, 0, 0, $m[2], date('d', NV_CURRENTTIME), ($m[1] + 2000));
        $sfrom = strtotime('-3 month', $today);
        $sto = strtotime('+3 month', $today);
        // $sto = $sto > NV_CURRENTTIME ? NV_CURRENTTIME : $sto;
        $sfrom = nv_date('d/m/Y', $sfrom);
        $_GET['sfrom'] = $sfrom;
        $sto = nv_date('d/m/Y', $sto);
        $_GET['sto'] = $sto;
    }
}

$is_elas = ($bidding_config['elas_use']) ? true : false;
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sto, $m)) {
    $sto1 = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} elseif (preg_match('/^(20[0-9]{2})([0-9]{2})([0-9]{5})/', $q, $_m)) {
    $number = cal_days_in_month(CAL_GREGORIAN, $_m[2], $_m[1]);
    $sto1 = mktime(0, 0, 0, $_m[2], $number, $_m[1]);
} else {
    $sto1 = NV_CURRENTTIME;
}
$sto1 > NV_CURRENTTIME && $sto1 = NV_CURRENTTIME;

$_m = [];
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sfrom, $m)) {
    $sfrom1 = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $sfrom1 = 0;
}
$sfrom1 > NV_CURRENTTIME && $sfrom1 = NV_CURRENTTIME;
$sto2 = $sto1 - 3600;
$sfrom1 > $sto2 && $sfrom1 = $sto2;

if ($is_elas) {
    $search_mindate = "31/12/2010";
    $search_mindate = explode("/", $search_mindate);
    $sfrom_check = mktime(0, 0, 0, $search_mindate[1], $search_mindate[0], $search_mindate[2]);
    if (!defined('NV_IS_USER')) {
        $sfrom_check = strtotime(date("Y-m-d", $sto1) . " -12 month");
    }
} else {
    $search_mysql_range = 12;
    if ($search_mysql_range < 1)
        $search_mysql_range = 1;
    if (defined('NV_IS_ADMIN'))
        $search_mysql_range = 24;

    $_search_mysql_range = $search_mysql_range == 1 ? "-" . $search_mysql_range . " month" : "-" . $search_mysql_range . " months";
    $sfrom_check = strtotime(date("Y-m-d", $sto1) . $_search_mysql_range);
}

if ($sfrom1 <= $sfrom_check) {
    $sfrom1 = $sfrom_check;
    if ($is_elas and !defined('NV_IS_USER')) {
        $link_login = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        isset($_GET['sfrom']) && $error[] = sprintf($nv_Lang->getModule('notification_tb'), 12, nv_date("d/m/Y", $sfrom1), nv_date("d/m/Y", $sto1), $link_login);
    } else {
        isset($_GET['sfrom']) && $search_mysql_range && $error[] = sprintf($nv_Lang->getModule('sql_rangetime_error'), $search_mysql_range, nv_date("d/m/Y", $sfrom1), nv_date("d/m/Y", $sto1));
    }
}

if (NV_CURRENTTIME >= $close_time_dauthau and NV_CURRENTTIME <= $open_maunal_time_dauthau and ($type_user == 2 or $type_user == 3)) {
    // view chỉ xem dc tin cũ
    $sto1 = $close_time_dauthau;
    if ($sfrom1 > $sto1) {
        $sfrom1 = $sto1 - (86400 * 30);
    }
}

$_GET['sfrom'] = nv_date('d/m/Y', $sfrom1);
$_GET['sto'] = nv_date('d/m/Y', $sto1);

$_userid = defined('NV_IS_USER') ? $user_info['userid'] : 0;
$userid = $nv_Request->get_int('userid', 'get', $_userid);
$vip = $nv_Request->get_int('vip', 'get', 0);
if (!defined('NV_IS_MODADMIN')) {
    $userid = $_userid;
}
$arr_unexist_key = [];
if ($userid > 0) {
    // kiểm tra vip
    if (isset($global_array_vip[6])) {
        define('NV_IS_VIP', true);
    }
    $key_search = $q;
    if (defined("NV_IS_VIP") && !empty($key_search)) {
        $key_search = explode(',', $key_search);
        $key_search = array_map('trim', $key_search); // Từ khóa nhập vào

        $db->sqlreset()
            ->select('key_search')
            ->from('' . BID_PREFIX_GLOBAL . '_filter')
            ->where('userid = ' . $userid);
        $sth = $db->prepare($db->sql());
        $sth->execute();
        $arr_key_filter = array(); // Từ khóa lấy từ bộ lọc đã lưu trong CSDL.
        while ($key_filter = $sth->fetch()) {
            $keys = explode(',', $key_filter['key_search']);
            if ($search_type_content == 0) {
                foreach ($keys as $k => $key) {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = strtolower($key);
                    $keys[$k] = trim($key);
                }
            }
            $arr_key_filter = array_merge($arr_key_filter, $keys);
        }
        $arr_key_filter = array_map('trim', $arr_key_filter);
        $arr_key_filter = array_unique($arr_key_filter);
        // So sánh hai mảng để lấy key không có ở bộ lọc
        foreach ($key_search as $keys) {
            if ($search_type_content == 0) {
                $key = str_replace('-', ' ', change_alias($keys));
                $key = strtolower($key);
                $key = trim($key);
            } else {
                $key = $keys;
            }
            if (!in_array($key, $arr_key_filter)) {
                $arr_unexist_key[] = $keys;
            }
        }
    }
}

$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
$location_only = false;
$base_url_location = '';

// Kiểm tra tất cả các tham số tìm kiếm một lần
$has_search_params = (
    !empty($q) || !empty($key_search2) || !empty($key_search2_root) ||
    !empty($without_key) || !empty($price_from) || !empty($price_to) ||
    !empty($keyword_id_bidder) || $search_one_key > 0 || $search_type_content > 0 || $search_kind > 0 ||
    $par_search > 0 || ($vip != 0) ||
    (!empty($sfrom) && $sfrom != nv_date('d/m/Y', $sfrom_check)) ||
    (!empty($sto) && $sto != nv_date('d/m/Y', $sto1))
);

if (!$has_search_params) {
    $province_id = $district_id = 0;

    if (preg_match('/^([TH])\-([a-zA-Z0-9\-]+)\-([0-9]+)$/i', ($array_op[1] ?? ''), $m)) {
        $location_type = $m[1];
        $location_id = intval($m[3]);

        if ($location_type == 'H') {
            $district_id = $location_id;
        } elseif ($location_type == 'T') {
            $province_id = $location_id;
        }
    } elseif ($sdistrict > 0) {
        $district_id = $sdistrict;
    } elseif ($sprovince > 0) {
        $province_id = $sprovince;
    }

    if ($district_id > 0) {
        $sql = "SELECT d.id, d.alias, d.idprovince, d.title, p.title as province_title FROM " . NV_PREFIXLANG . "_location_district d LEFT JOIN " . NV_PREFIXLANG . "_location_province p ON d.idprovince = p.id WHERE d.status=1 AND d.id = " . $db->quote($district_id);
        $result = $db->query($sql);
        if ($row = $result->fetch()) {
            $sdistrict = $row['id'];
            $sprovince = $row['idprovince'];
            $base_url_location = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/H-' . $row['alias'] . '-' . $sdistrict;
            $page_title = $nv_Lang->getModule('thong_bao_to_chuc_dau_gia') . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'] . ', ' . $row['province_title'];

            if (!empty($module_info['funcs'][$op]['description'])) {
                $description = $module_info['funcs'][$op]['description'] . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'] . ', ' . $row['province_title'];
                $meta_property['og:description'] = $description;
            }
        }
    } elseif ($province_id > 0) {
        $sql = "SELECT id, alias, title FROM " . NV_PREFIXLANG . "_location_province WHERE status=1 AND id = " . $db->quote($province_id);
        $result = $db->query($sql);
        if ($row = $result->fetch()) {
            $sprovince = $row['id'];
            $base_url_location = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/T-' . $row['alias'] . '-' . $sprovince;
            $page_title = $nv_Lang->getModule('thong_bao_to_chuc_dau_gia') . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'];

            if (!empty($module_info['funcs'][$op]['description'])) {
                $description = $module_info['funcs'][$op]['description'] . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'];
                $meta_property['og:description'] = $description;
            }
        }
    }

    if (!empty($base_url_location)) {
        $_GET['is_advance'] = 1;
        $_GET['keyword_id_province'] = $sprovince;
        $_GET['keyword_id_district'] = $sdistrict;
        $_GET['type_search'] = 3;
        $_GET['type_info3'] = 2;
        $location_only = true;
    }
}

$array_data = [];
if ($is_elas) {
    require NV_ROOTDIR . '/modules/' . $module_name . '/search/organization_elastic.php';
} else {
    require NV_ROOTDIR . '/modules/' . $module_name . '/search/organization_mysql.php';
}
if (!empty($type_info3)) {
    $base_url .= '&type_info3=' . $type_info3;
}
if (!empty($kqlc_tc_dgts)) {
    $base_url .= '&ketqua_luachon_tochuc_dgts=' . $kqlc_tc_dgts;
}
if (!empty($type_search) and $type_search != 3) {
    $base_url .= '&type_search=' . $type_search;
}
if (!empty($search_type_content)) {
    $base_url .= '&search_type_content=' . $search_type_content;
}
if (!empty($par_search)) {
    $base_url .= '&par_search=' . $par_search;
}

$filter_url = DAUTHAU_INFO_DOMAIN . '/filters/';
$filter_url .= '?search_info=3';
if (!empty($arr_unexist_key)) {
    $filter_url .= "&add=1";
    $filter_key = implode(',', $arr_unexist_key);
    $filter_url .= "&key=" . $filter_key;
}
$filter_url .= '&vip_use2=' . $type_info3;
$filter_url .= '&search_type_content=' . $search_type_content;
$filter_url .= '&par_search=' . $par_search;
$filter_url .= '&searchkind=' . $search_kind;

if (!empty($q)) {
    $base_url .= '&q=' . $q;
}
if (!empty($without_key)) {
    $base_url .= '&without_key=' . $without_key;
    $filter_url .= '&without_key=' . $without_key;
}
if (!empty($key_search2)) {
    $base_url .= '&key2=' . $key_search2;
    $filter_url .= '&key2=' . $key_search2;
}
if (!empty($search_one_key)) {
    $base_url .= '&search_one_key=' . $search_one_key;
    $filter_url .= '&search_one_key=' . $search_one_key;
}
if ($is_advance) {
    $base_url .= "&is_advance=1";
}
if (!empty($price_from)) {
    $base_url .= '&price_from=' . $price_from;
    $filter_url .= '&keyword_min_bid_prices=' . $price_from;
}
if (!empty($sfrom)) {
    $base_url .= '&sfrom=' . $sfrom;
    $filter_url .= '&sfrom=' . $sfrom;
}
if (!empty($sto)) {
    $base_url .= '&sto=' . $sto;
    $filter_url .= '&sto=' . $sto;
}
if (!empty($price_to)) {
    $base_url .= '&price_to=' . $price_to;
    $filter_url .= '&keyword_max_bid_prices=' . $price_to;
}
if (!empty($keyword_id_bidder)) {
    $base_url .= '&keyword_id_bidder=' . $keyword_id_bidder;
    $filter_url .= '&keyword_id_bidder=' . $keyword_id_bidder;
}

if (!empty($sprovince)) {
    $base_url .= '&keyword_id_province=' . $sprovince;
    $filter_url .= '&keyword_id_province=' . $sprovince;
}

if (!empty($sdistrict)) {
    $base_url .= '&keyword_id_district=' . $sdistrict;
    $filter_url .= '&keyword_id_district=' . $sdistrict;
}

if ($location_only && !empty($base_url_location)) {
    $base_url = $base_url_location;
}

// Xử lý tìm kiếm ajax từ module seek
if ($nv_Request->isset_request('ajaxsearch', 'post')) {
    $respon = [];
    if ($nv_Request->get_title('quicksearchform', 'get', '') === NV_CHECK_SESSION) {
        $index = 0;
        $respon['num_items'] = number_format($total, 0, '', '.');
        foreach ($array_data as $key => $value) {
            $respon['data'][$index]['link'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $value['alias'] . '-' . $value['id_bid'] . '.html', true);
            $value = array_map(function ($a) use ($l, $q) {
                if (is_string($a)) {
                    return BoldKeywordInStr($a, $q, (isset($l) && $l === 0) ? 'OR' : 'AND');
                } else {
                    return $a;
                }
            }, $value);
            $respon['data'][$index]['title'] = $value['title'];
            $respon['data'][$index]['content'] = '<b>' . $nv_Lang->getModule('name_propertys') . '</b>' . ': ' . $value['name_owner'] . ' - ' . '<b>' . $nv_Lang->getModule('date_bid') . '</b>' . ': ' . nv_date('H:m d/m/Y', $value['opening_bid_format']) . ' - ' . '<b>' . $nv_Lang->getModule('time_nop_hs') . '</b>' . ': ' . nv_date('d/m/Y ', $value['opening_reg_format']) . $nv_Lang->getModule('to') . nv_date(' d/m/Y', $value['closing_reg_format']);
            $index += 1;
            if ($index == 3) {
                break;
            }
        }
        $respon['view_full_page'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=bidorganization&q=' . urlencode($q), true) . ((isset($l) && $l === 0) ? '&searchkind=1&is_advance=1' : '');
    }
    nv_jsonOutput($respon);
}
$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('thong_bao_to_chuc_dau_gia'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op, true)
);

$generate_page = nv_generate_page($base_url, $total, $perpage, $page);
// lấy page_url
$page_url = $base_url;
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}
// #2415: Thêm hằng chặn index
$query_params = [];
parse_str(parse_url($page_url, PHP_URL_QUERY), $query_params);
// Danh sách các tham số cần xóa
$params_to_remove = [
    'amp;' . NV_NAME_VARIABLE,
    'amp;' . NV_OP_VARIABLE,
    NV_LANG_VARIABLE
];
if (!empty($sprovince)) {
    $params_to_remove[] = 'keyword_id_province';
} elseif (!empty($sprovince)) {
    $params_to_remove[] = 'keyword_id_district';
}
$query_params = array_diff_key($query_params, array_flip($params_to_remove));
$has_other_query_params = false;
foreach ($query_params as $key => $value) {
    if ($key !== 'amp;page') {
        $has_other_query_params = true;
        break;
    }
}
if (!empty($q) || empty($array_data) || $has_other_query_params) {
    $nv_BotManager->setFollow()->setNoIndex();
}
// Xử lý title, description phân trang
if ($page > 1) {
    $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
    if (!empty($description)) {
        $description .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
        $meta_property['og:description'] = $description;
    } elseif (!empty($module_info['funcs'][$op]['description'])) {
        $description = $module_info['funcs'][$op]['description'] . NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
        $meta_property['og:description'] = $description;
    }
}
$canonicalUrl = getCanonicalUrl($page_url);
$urlappend = '&amp;page=';
// Kiểm tra đánh số trang
betweenURLs($page, ceil($total / $perpage), $base_url, $urlappend, $prevPage, $nextPage);

$contents = nv_theme_dau_gia_bidorganization($array_data, $generate_page, $module_config_view, $arr_unexist_key, $filter_url, $error, $total);

// Schema: CollectionPage
$my_schema_items = [];
$pos = 1;
foreach ($array_data as $row) {
    $row['schema_about'] = $row['title'];
    if (isset($row['title'])) {
        $parts = explode(':', $row['title'], 2);
        if (count($parts) == 2) {
            $row['schema_about'] = trim($parts[1]);
        }
    }
    $my_schema_items[] = [
        '@type' => 'ListItem',
        'position' => $pos,
        'item' => [
            '@type' => 'CreativeWork',
            'name' => $row['title'],
            'url' => DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/' . $op . '/' . $row['alias'] . '-' . $row['id_bid'] . '.html',
            'creator' => [
                '@type' => 'Organization',
                'name' => $row['name_owner']
            ],
            'datePublished' => nv_date("Y-m-d\TH:i:sP", $row['opening_bid']),
            'temporalCoverage' => nv_date("Y-m-d", $row['opening_reg']) . '/' . nv_date("Y-m-d", $row['closing_reg']),
            'about' => $row['schema_about'],
            'inLanguage' => 'vi',
            'additionalProperty' => [
                [
                    '@type' => 'PropertyValue',
                    'name' => $nv_Lang->getModule('result'),
                    'value' => !empty($row['chosen_org_id']) ? $nv_Lang->getModule('has_result') : $nv_Lang->getModule('no_result')
                ]
            ]
        ]
    ];
    $pos++;
}
$nv_schemas[] = [
    '@context' => 'https://schema.org',
    '@type' => 'CollectionPage',
    'name' => ($page > 1) ? $nv_Lang->getModule('bidorganization_schema_page_title') . NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page : $nv_Lang->getModule('bidorganization_schema_page_title'),
    'url' => $canonicalUrl,
    'mainEntity' => [
        '@type' => 'ItemList',
        'name' => ($page > 1) ? $nv_Lang->getModule('bidorganization_schema_page_title_list') . NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page : $nv_Lang->getModule('bidorganization_schema_page_title_list'),
        'itemListElement' => $my_schema_items
    ]
];

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
