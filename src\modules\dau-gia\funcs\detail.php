<?php

/**
 *
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 10 Nov 2020 06:56:08 GMT
 */
if (!defined('NV_IS_MOD_DAU_GIA')) {
    die('Stop!!!');
}

use NukeViet\Point\Point;
use NukeViet\Dauthau\Share;

// Chỉ thành viên hoặc Bot mới vào đây được
/*
 * if (!(defined('NV_IS_USER') or $client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
 * $url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=users&amp;' . NV_OP_VARIABLE . '=login&amp;nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']), true);
 * $contents = nv_theme_alert($nv_Lang->getModule('info'), $nv_Lang->getModule('info_login'), 'info', $url, $nv_Lang->getModule('info_redirect_click'), 5);
 * include NV_ROOTDIR . '/includes/header.php';
 * echo nv_site_theme($contents);
 * include NV_ROOTDIR . '/includes/footer.php';
 * }
 *
 * // thành viên thì hiển thị thông báo đóng cửa, chỉ cho view, vip xem chi tiết
 * if ($type_user == 1 and !$client_info['is_bot']) {
 * $contents = '<div class="alert alert-warning"><div style="text-align: justify;"><strong>1.&nbsp;Từ ngày 27/04/2021</strong>, DauThau.info sẽ <a href="/news/tin-tuc/thong-bao-ngung-cung-cap-dich-vu-tra-cuu-thong-tin-thau-mien-phi-cho-cac-tai-khoan-dang-ky-mien-phi-367.html"><strong>ngừng cung cấp dịch vụ tra cứu thông tin thầu miễn phí</strong></a> cho các tài khoản đăng ký miễn phí trên dauthau.asia <strong>để tránh bị quy kết là cung cấp thông tin không đầy đủ</strong>. Nhà thầu&nbsp;muốn xem thông tin gói thầu cần <strong><a href="/vip/?plan=1">đăng ký mua gói phần mềm VIEWEB hoặc gói phần mềm VIP</a></strong>.<br><strong>2. Từ ngày 06/05/2021</strong>, DauThau.info sẽ áp dụng bảng giá mới do&nbsp;thay đổi về chính sách của cơ quan quản lý nhà nước không cho quét dữ liệu tự động dẫn tới các thay đổi về chi phí&nbsp;bảo trì và phát triển Phần mềm “săn” thông tin thầu DauThau.info. Chi tiết&nbsp;xem <a href="/news/blog/nang-cap-chuyen-sang-nhap-lieu-thu-cong-bang-gia-moi-369.html"><strong>tại đây</strong></a>.</div></div>';
 * include NV_ROOTDIR . '/includes/header.php';
 * echo nv_site_theme($contents);
 * include NV_ROOTDIR . '/includes/footer.php';
 * }
 */

$arr_view = [];
$sql = "SELECT config_name, config_value FROM " . $db_config['prefix'] . "_config WHERE module = 'dau-gia'";
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_view[$row['config_name']] = $row;
}

$page_title = $module_info['site_title'];
$key_words = $module_info['keywords'];
$space = ', ';

$array_data = [];
$array_data_asset = [];
$array_data_craws = [];
$db->sqlreset()
    ->select('*')
    ->from($db_config['prefix'] . '_dau_gia_bid')
    ->where('id_bid = ' . intval($id_bid));
$sql = $db->sql();
$array_data = $db->query($sql)->fetch();

if (!empty($array_data)) {
    // Gọi hàm confirm update crawl
    Share::submitConfirmCrawl();

    // Update
    if (defined('NV_IS_USER') && $nv_Request->isset_request('update', 'post,get')) {
        $id = $nv_Request->get_int('id_update', 'post, get', 0);
        $check = $nv_Request->get_title('check', 'post, get', '');
        if (!($id > 0 and $check == md5(intval($user_info['userid']) . $id . NV_CACHE_PREFIX . $client_info['session_id']))) {
            nv_jsonOutput([
                'res' => 'error',
                'mess' => $nv_Lang->getModule('update_err'),
                'time' => 5 * 60,
                'line' => 38
            ]);
        }

        // Đoạn này kiểm tra xem tin này đã được cập nhật chưa, nếu tin này đã được người dùng trước bấm cập nhật rồi, thì người dùng sau phải đợi 10p mới được bấm tiếp
        $last = [];
        if (!empty($module_config['bidding']['up_request_user_inteval'])) {
            $last = $db->query('SELECT last_reload FROM ' . BID_PREFIX_GLOBAL . '_update_user WHERE userid=' . $user_info['userid'] . ' AND auction_notice_id = ' . $id . ' ORDER BY last_reload DESC LIMIT 1')->fetch();
            if (!empty($last)) {
                // Nếu cập nhật rồi thực hiện kiểm tra xem: Thời gian hiện tại - Thời gian bấm cập nhật - thời gian cấu hình
                // 1743561774 - 1743561094 - 600
                $pas = NV_CURRENTTIME - $last['last_reload'] - $module_config['bidding']['up_request_user_inteval'] * 60;
                if ($pas < 0) {
                    nv_jsonOutput([
                        'res' => 'error',
                        'mess' => sprintf($nv_Lang->getModule('update_err_user_last'), nv_convertfromSec(abs($pas))),
                        'time' => abs($pas),
                        'line' => 50
                    ]);
                }
            }
        }

        // Kiểm tra xem có dữ liệu chi tiết của thông báo đấu giá kh
        if (empty($array_data) && $array_data['id_bid'] != $id) {
            nv_jsonOutput([
                'res' => 'error',
                'mess' => $nv_Lang->getModule('data_not_exited')
            ]);
        }

        $url_detail = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $array_data['alias'] . '-' . $array_data['id_bid'] . $global_config['rewrite_exturl'], true);
        // Đoạn này kiểm tra chỉ phép cập nhật lại tin sau 30p
        if (!empty($module_config['bidding']['up_request_inteval'])) {
            $pas = NV_CURRENTTIME - $array_data['update_at'] - $module_config['bidding']['up_request_inteval'] * 60;
            if ($pas < 0) {
                nv_jsonOutput([
                    'res' => 'error',
                    'mess' => sprintf($nv_Lang->getModule('update_err_new'), nv_convertfromSec(abs($pas))),
                    'time' => abs($pas)
                ]);
            }
        }

        $info = [
            'res' => 'error',
            'mess' => $nv_Lang->getModule('update_err_unknown'),
            'time' => 60,
            'line' => 75
        ];

        // Kiểm tra xem khách hàng còn điểm không?
        $customs_points = Point::getMyPoint();
        $sodiem = Point::getAllConfig()['point_crawl_mintus'];
        $sodiem = (isset($sodiem) ? $sodiem : 0);

        // Thông báo nếu khách hàng không còn điểm
        if ($customs_points['point_total'] < $sodiem) {
            nv_jsonOutput([
                'res' => 'error',
                'mess' => Share::langErrorUserNoPoint()
            ]);
        }

        // Thực hiện kiểm tra bảng bóc tin
        $check_crawl = $db->query("SELECT * FROM " . $db_config['prefix'] . "_dau_gia_craws WHERE id_source = " . $array_data['id_source'])->fetch();

        if (empty($check_crawl)) {
            // Thêm mới
            $db->query("INSERT INTO " . $db_config['prefix'] . "_dau_gia_craws (id_source, title, publish_time, auc_time, time_start, time_end) VALUES (" . $array_data['id_source'] . ", " . $db->quote($array_data['title']) . ", " . $array_data['date_bid'] . ", " . $array_data['opening_bid'] . "," . $array_data['opening_reg'] . ", " . $array_data['closing_reg'] . ")");
        } else {
            // Update
            $db->query("UPDATE " . $db_config['prefix'] . "_dau_gia_craws SET time_crawl = 0 WHERE id_source = " . $array_data['id_source']);
        }

        $info = [
            'res' => 'ok',
            'mess' => $nv_Lang->getModule('update_status_1') . ". " . $nv_Lang->getModule('browser_refresh_5'),
            'time' => 300
        ];

        $data = [
            'title' => $array_data['title'],
            'link' => urlRewriteWithDomain($url_detail, NV_MY_DOMAIN)
        ];

        $data = json_encode($data);

        if (!empty($last)) {
            $exec = $db->exec("UPDATE " . BID_PREFIX_GLOBAL . "_update_user SET last_reload = " . NV_CURRENTTIME . " WHERE userid = " . $user_info['userid'] . " AND auction_notice_id = " . $id);
        } else {
            $message = [
                'vi' => sprintf(get_lang('vi', 'title_minus_points'), $sodiem, sprintf(get_lang('vi', 'title_note_tbdg'), $array_data['title'])),
                'en' => sprintf(get_lang('en', 'title_minus_points'), $sodiem, sprintf(get_lang('en', 'title_note_tbdg'), $array_data['title']))
            ];
            $res = Share::insertBiddingUpdateUser($id, 'auction_notice_id', $data, $sodiem, json_encode($message));
            if ($res == -1) {
                nv_jsonOutput([
                    'res' => 'error',
                    'mess' => Share::langErrorTransactionPoint()
                ]);
            }
        }

        nv_jsonOutput($info);
    }

    if (defined('NV_IS_USER') and $nv_Request->get_title('download', 'GET') == md5($user_info['userid'] . '_' . NV_CHECK_SESSION)) {
        if ($nv_Request->get_int($module_data . '_' . $op . '_download_' . $array_data['id_bid'], 'session', 0) > 0) {
            $nv_Request->set_Session($module_data . '_' . $op . '_download_' . $array_data['id_bid'], NV_CURRENTTIME);
            $db->exec('UPDATE ' . $db_config['prefix'] . '_dau_gia_bid SET view_download = view_download + 1 WHERE id_bid = ' . $array_data['id_bid']);
        }
        nv_redirect_location('https://dgts.moj.gov.vn/portal/exportWord?aucInfoId=' . $array_data['id_source']);
    }

    if (NV_CURRENTTIME >= $close_time_dauthau and NV_CURRENTTIME <= $open_maunal_time_dauthau and $type_user == 2) {
        // view k dc xem các tin lớn hơn ngày close, chỉ xem dc tin cũ
        // vip dc xem do gửi qua mail
        if ($array_data['date_bid'] > $close_time_dauthau) {
            nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
        }
    }

    if (defined('NV_IS_USER')) {
        // kiểm tra vip
        $arr_vip = [];
        if (!empty($global_array_vip)) {
            $arr_vip = $global_array_vip;
        }
        if (!empty($arr_vip)) {
            define('NV_IS_VIP', true);
            if (!isset($arr_vip[99])) {
                define('NV_IS_VIP_NO_VIEWEB', true);
            } else if (isset($arr_vip[99]) and count($arr_vip) > 1) {
                define('NV_IS_VIP_IS_VIEWEB', true);
            } else {
                $vip_renew = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id=' . $user_info['userid'] . ' AND status != 1 AND vip != 99 AND prefix_lang = ' . BID_LANG_DATA)
                    ->fetch();
                if (!empty($vip_renew)) {
                    define('NV_IS_VIP_RENEW_NO_VIEWEB', true);
                }
            }
            if (isset($arr_vip[6])) {
                define('NV_IS_VIP6', true);
            }
        } else {
            $vip_renew = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id=' . $user_info['userid'] . ' AND status != 1 AND vip != 99 AND prefix_lang = ' . BID_LANG_DATA)
                ->fetch();
            if (!empty($vip_renew)) {
                define('NV_IS_VIP_RENEW_NO_VIEWEB', true);
            }
        }
        if (!defined('NV_IS_VIP6')) {
            $is_vip6_renew = false;
            // Kiểm tra nếu gói VIP 6 hết hạn
            $vip6_renew = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id=' . $user_info['userid'] . ' AND status != 1 AND vip =6 AND prefix_lang = ' . BID_LANG_DATA)->fetch();
            if (!empty($vip6_renew)) {
                $is_vip6_renew = true;
            }
        }
    }

    if ($alias != $array_data['alias']) {
        // Kiểm tra lại url có đúng với CSDL, nếu không đúng điều hướng lại
        $url_Permanently = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $array_data['alias'] . '-' . $array_data['id_bid'] . '.html');
        nv_redirect_location($url_Permanently);
    }

    $page_title = $array_data['title'];
    $array_mod_title[] = array(
        'title' => $nv_Lang->getModule('thong_bao_dau_gia'),
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=main', true)
    );
    $array_mod_title[] = array(
        'title' => $array_data['title'],
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $array_data['alias'] . '-' . $array_data['id_bid'] . '.html', true)
    );

    if (!empty($array_data['file_detail'])) {
        $array_data['file_detail'] = json_decode($array_data['file_detail'], true);
    } else {
        $array_data['file_detail'] = [];
    }
    if (!empty($array_data['id_bid_parent'])) {
        $db->sqlreset()
            ->select('alias')
            ->from($db_config['prefix'] . '_dau_gia_bid')
            ->where('id_bid = ' . $array_data['id_bid_parent']);
        $sql = $db->sql();

        $array_data['alias_parent'] = $db->query($sql)->fetch();
    }
    /*
     * Xử lý địa chỉ người sở hữu tài sản
     */
    if ($array_data['id_district_owner'] != 0 && $array_data['id_province_owner'] != 0) {
        $sql = "SELECT title, alias FROM " . $db_config['prefix'] . "_vi_location_district WHERE id =" . $array_data['id_district_owner'];
        $result = $db->query($sql);
        if ($row = $result->fetch()) {
            $array_data['address_owner'] .= $space;
            $district_url = NV_BASE_SITEURL . NV_LANG_DATA . '/H-' . $row['alias'] . '-' . $array_data['id_district_owner'] . '/';
            $array_data['address_owner'] .= '<a href="' . $district_url . '">' . $row['title'] . '</a>';
        }

        $sql = "SELECT title, alias FROM " . $db_config['prefix'] . "_vi_location_province WHERE id =" . $array_data['id_province_owner'];
        $result = $db->query($sql);
        if ($row = $result->fetch()) {
            $array_data['address_owner'] .= $space;
            $province_url = NV_BASE_SITEURL . NV_LANG_DATA . '/T-' . $row['alias'] . '-' . $array_data['id_province_owner'] . '/';
            $array_data['address_owner'] .= '<a href="' . $province_url . '">' . $row['title'] . '</a>';
        }
    } else {
        $array_data['address_owner'] = $array_data['address_owner_source'] ?: $array_data['address_owner'];
    }

    $result = $db->query("SELECT * FROM " . $db_config['prefix'] . "_dau_gia_bidder WHERE id_bidder =" . $array_data['id_bidder']);
    if ($array_bidder = $result->fetch()) {
        $array_data['address_bidder'] = build_full_address($array_bidder);
        $array_data['phone'] = $array_bidder['phone'];
        $array_data['fax'] = $array_bidder['fax'];

        $array_data['name_bidder'] = $array_bidder['name_bidder'];
        $array_data['link_bidder'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=organization/' . change_alias($array_bidder['alias']) . '-' . $array_bidder['id_bidder'] . '.html';
    }

    // lấy dữ liệu từ bảng nv4_dau_gia_asset
    $db->sqlreset()
        ->select('*')
        ->from($db_config['prefix'] . '_dau_gia_asset')
        ->where($db_config['prefix'] . '_dau_gia_asset.id_bid = ' . $array_data['id_source']); // thay id_bid = id_source do bóc tin _dau_gia_asset cũ sử dụng như này
    $sql = $db->sql();

    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $array_data_asset[$row['id_asset']] = $row;
        // Xử lý địa chỉ nơi có tài sản
        // Nối số nhà với phường, xã
        $array_data_asset[$row['id_asset']]['address_asset'] .= $space;
        $db->sqlreset()
            ->select('title')
            ->from($db_config['prefix'] . '_vi_location_district')
            ->where('id =' . $array_data_asset[$row['id_asset']]['id_district']);
        $sql2 = $db->sql();
        $result2 = $db->query($sql2);
        $array_data_province = $result2->fetchAll();
        foreach ($array_data_province as $row2) {
            $array_data_asset[$row['id_asset']]['address_asset'] .= $row2['title'];
            $array_data_asset[$row['id_asset']]['address_asset'] .= $space;
        }
        $db->sqlreset()
            ->select('title')
            ->from($db_config['prefix'] . '_vi_location_province')
            ->where('id =' . $array_data_asset[$row['id_asset']]['id_province']);
        $sql2 = $db->sql();
        $result2 = $db->query($sql2);
        $array_data_province = $result2->fetchAll();
        foreach ($array_data_province as $row2) {
            $array_data_asset[$row['id_asset']]['address_asset'] .= $row2['title'];
        }
    }

    // lấy dữ liệu từ bảng nv4_dau_gia_craws
    $db->sqlreset()
        ->select('*')
        ->from($db_config['prefix'] . '_dau_gia_craws')
        ->where($db_config['prefix'] . '_dau_gia_craws.id_source = ' . $array_data['id_source']);
    $sql = $db->sql();

    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $array_data_craws[$row['id_source']] = $row;
    }

    /*
     * Đếm số lượt xem
     */
    if (!$nv_Request->isset_request($module_data . '_' . $op . '_' . $id_bid, 'session')) {
        $nv_Request->set_Session($module_data . '_' . $op . '_' . $id_bid, NV_CURRENTTIME);
        $array_data['viewcount'] += 1;
        $db->query('UPDATE ' . $db_config['prefix'] . '_dau_gia_bid SET viewcount = viewcount + 1 WHERE id_bid=' . $id_bid);
    }
    /*
     * Hiển thị nguồn tin
     */
    if ($arr_view['view_type']['config_value'] != '1') {
        $array_data['view_type']['name'] = $nv_Lang->getModule('ctt');
        if ($arr_view['view_type']['config_value'] == '3') {
            $array_data['view_type']['link'] = 'https://dgts.moj.gov.vn/thong-bao-cong-khai-viec-dau-gia/' . change_alias($array_data_craws[$id_bid]['title']) . '-' . $array_data_craws[$id_bid]['id_source'] . '.html';
        }
    }

    if ($arr_view['view_obj']['config_value'] == '3') {
        if (!defined('NV_IS_ADMIN') || !defined('NV_IS_SPADMIN')) {
            $array_data['view_type'] = '';
        }
    } elseif ($arr_view['view_obj']['config_value'] == '2') {
        if (!defined('NV_IS_VIP')) {
            $array_data['view_type'] = '';
        }
    }
    $array_data['vip'] = "true";
    if ($arr_view['view_vip']['config_value'] == '2') {
        if (!defined('NV_IS_VIP6')) {
            if (defined('NV_IS_USER') && $is_vip6_renew) {
                $array_data['vip'] = "renew";
            } else {
                $array_data['vip'] = "false";
            }
        }
    }

    $arrCheckRePost = [];
    // lấy các thông báo liên quan
    if ($array_data['id_bid_parent'] > 0) {
        $sql = "SELECT alias, id_bid, re_post, date_bid, title FROM " . $db_config['prefix'] . "_dau_gia_bid WHERE id_bid_parent = " . $array_data['id_bid_parent'];
        $result = $db->query($sql);
        while ($row = $result->fetch()) {
            $row['is_public_one'] = '';
            if ($row['re_post'] == 1) {
                $row['is_public_one'] = sprintf($nv_Lang->getModule('is_public_one'), date(' H:i d/m/Y', $row['date_bid']));
            } else {
                $row['is_public_one'] = sprintf($nv_Lang->getModule('is_public_da'), $row['re_post'], date(' H:i d/m/Y', $row['date_bid']));
            }
            $array_data['other'][] = $row;
        }

        foreach ($array_data['other'] as $k => $row) {
            $array_data['other'][$k]['is_active'] = '';
            $array_data['other'][$k]['label_active'] = '';
            if (($array_data['id_bid'] == $row['id_bid'])) {
                $array_data['other'][$k]['is_active'] = 'checked';
                $array_data['other'][$k]['label_active'] = $nv_Lang->getModule('title_label_active');
            }
        }

        $arrNew = [];
        foreach ($array_data['other'] as $k => $row) {
            $arrNew[$row['re_post']][] = $row;
        }

        // Thực hiện lấy id tương ứng với lần thông báo
        foreach ($arrNew as $k => $v) {
            $arrCheckRePost[$v[0]['id_bid']] = $k;
        }
        $array_data['other'] = $arrNew;
    }

    // Tính toán chi phí tham gia đấu giá - tài sản quyền sử dụng đất
    $customs_points = [];
    if (defined('NV_IS_USER')) {
        $customs_points = Point::getMyPoint();
    }
    $_name_asset_alias = strtolower(change_alias($array_data['name_asset']));
    $_dt_view_auction_costs = [];
    if ((preg_match('/qsdd|QSD-dat|bat-dong-san|quyen-su-dung-dien-tich|quyen-su-dung(.*)dat|thuoc-thua-dat-so|[0-9]+-thua-dat/i', $_name_asset_alias) and !preg_match('/khong-bao-gom-quyen-su-dung-dat/i', $_name_asset_alias))
        || preg_match('/nha-dat|nha-va-dat|quyen-su-dung-tan-so-vo-tuyen|gan-lien-voi-dat/i', $_name_asset_alias)) {

        // Trường hợp các thông báo đấu giá về quyền sử dụng đất
        $is_land_use = 2;
        if (preg_match('/qsdd|QSD-dat|bat-dong-san|quyen-su-dung-dien-tich|quyen-su-dung(.*)dat|thuoc-thua-dat-so|[0-9]+-thua-dat/i', $_name_asset_alias) and !preg_match('/khong-bao-gom-quyen-su-dung-dat/i', $_name_asset_alias)) {
            $is_land_use = 1;
        }
        $_total_price = $_total_price_b = 0;
        if ($array_data['min_bid_prices'] <= 1000000000) {
            $_total_price = 100000;
            $_total_price_b = 200000;
        } elseif ($array_data['min_bid_prices'] > 1000000000 and $array_data['min_bid_prices'] <= 5000000000) {
            $_total_price = 200000;
            $_total_price_b = 400000;
        } elseif ($array_data['min_bid_prices'] > 5000000000) {
            $_total_price = 300000;
            if ($array_data['min_bid_prices'] > 5000000000 and $array_data['min_bid_prices'] <= 10000000000) {
                $_total_price_b = 600000;
            } else {
                $_total_price_b = 1000000;
            }            
        }
        $less_200 = 100000;
        $_two_hund = 200000;
        $over_500 = 300000;
        $from_one_to_five = 400000;
        $from_50_to_100 = 600000;
        $over_10_bill = 1000000;
        $_dt_view_auction_costs = [
            'less_200' => nv_number_format($less_200),
            'from_200_to_500' => nv_number_format($_two_hund),
            'over_500' => nv_number_format($over_500),
            'less_5' => nv_number_format($_two_hund),
            'from_20_to_50' => nv_number_format($_two_hund),
            'from_50_to_100' => nv_number_format($from_50_to_100),
            'over_10_bill' => nv_number_format($over_10_bill),
            'from_one_to_five' => nv_number_format($from_one_to_five),
            'less_one_bill' => nv_number_format($_two_hund),
            'total' => nv_number_format($_total_price),
            'total_b' => nv_number_format($_total_price_b),
            'is_land_use' => $is_land_use
        ];
    } else {
        // Các loại tài sản khác - https://dauthau.asia/van-ban-dau-thau/detail/Thong-tu-48-2017-TT-BTC-ve-quy-dinh-che-do-tai-chinh-trong-hoat-dong-dau-gia-tai-san-156/?tab=body
        $less_20 = 50000;
        $from_20_to_50 = 100000;
        $from_200_to_500 = 200000;
        $from_50_to_100 = 500000;
        $over_10_bill = 1000000;
        if ($array_data['min_bid_prices'] <= 100000000) {
            $_total_price = $less_20;
        } elseif ($array_data['min_bid_prices'] > 100000000 and $array_data['min_bid_prices'] <= 1000000000) {
            $_total_price = $from_20_to_50;
        } elseif ($array_data['min_bid_prices'] > 1000000000 and $array_data['min_bid_prices'] <= 5000000000) {
            $_total_price = $from_200_to_500;
        } elseif ($array_data['min_bid_prices'] > 5000000000 and $array_data['min_bid_prices'] <= 10000000000) {
            $_total_price = $from_50_to_100;
        } elseif ($array_data['min_bid_prices'] > 10000000000) {
            $_total_price = $over_10_bill;
        }
        $_dt_view_auction_costs = [
            'less_20' => nv_number_format($less_20),
            'from_20_to_50' => nv_number_format($from_20_to_50),
            'from_200_to_500' => nv_number_format($from_200_to_500),
            'from_50_to_100' => nv_number_format($from_50_to_100),
            'over_10_bill' => nv_number_format($over_10_bill),
            'total' => nv_number_format($_total_price),
            'is_land_use' => 0
        ];

    }
    // Kiểm tra có đủ điểm xem chi phí tham gia đấu giá không?
    if ($nv_Request->isset_request('check_point_view_auction_costs', 'post')) {
        $checkss = $nv_Request->get_title('checkss', 'post', '');
        $_check_point_cost = md5('point_auction_costs' . $user_info['userid'] . $array_data['id_bid'] . NV_CACHE_PREFIX . $client_info['session_id']);
        if (empty($global_array_vip)) {
            // Nếu không dki gói vip nào
            $_point_view = $module_config['bidding']['point_view_auction_costs'];
        } elseif (isset($global_array_vip[99])) {
            // Nếu có dki gói vip VIEWEB
            $_point_view = $module_config['bidding']['point_view_auction_costs']/2;
        }

        if (!defined('NV_IS_USER') || $checkss != $_check_point_cost) {
            nv_jsonOutput([
                'res' => 'error',
                'mess' => $nv_Lang->getModule('some_error')
            ]);
        }
        if ($customs_points['point_total'] < $_point_view) {
            nv_jsonOutput([
                'res' => 'error',
                'mess' => $nv_Lang->getModule('error_point_auction_costs', $_point_view, 'https://id.dauthau.net/' . NV_LANG_DATA . '/points/#muadiem')
            ]);
        }
        nv_jsonOutput([
            'res' => 'success',
            'mess' => $nv_Lang->getModule('confirm_view_auction_costs', $_point_view, nv_number_format($customs_points['point_total']))
        ]);
    }

    // Xem chi phí tham gia đấu giá
    if ($nv_Request->isset_request('view_auction_costs', 'post')) {
        $checkss = $nv_Request->get_title('checkss', 'post', '');
        $checkss_view_cost = md5('view_auction_costs' . $user_info['userid'] . $array_data['id_bid'] . NV_CACHE_PREFIX . $client_info['session_id']);
        $_point_view = 0;
        if (empty($global_array_vip)) {
            // Nếu không dki gói vip nào
            $_point_view = $module_config['bidding']['point_view_auction_costs'];
        } elseif (isset($global_array_vip[99])) {
            // Nếu có dki gói vip VIEWEB
            $_point_view = $module_config['bidding']['point_view_auction_costs'] / 2;
        }
        if (!defined('NV_IS_USER') || $checkss != $checkss_view_cost) {
            nv_jsonOutput([
                'status' => 'error',
                'mess' => $nv_Lang->getModule('some_error')
            ]);
        }
        if ($customs_points['point_total'] < $_point_view) {
            nv_jsonOutput([
                'status' => 'error',
                'mess' => $nv_Lang->getModule('error_point_auction_costs', $_point_view, 'https://id.dauthau.net/' . NV_LANG_DATA . '/points/#muadiem')
            ]);
        }

        $message = [
            'vi' => sprintf(get_lang('vi', 'auction_costs_message'), $array_data['id_bid']),
            'en' => sprintf(get_lang('en', 'auction_costs_message'), $array_data['id_bid']),
        ];

        $check = Point::subtractPoint($_point_view, $user_info['userid'], json_encode($message), 0, 0, 0, 0, 27);
        if ($check['status'] != 'SUCCESS') {
            $info = [
                'status' => 'error',
                'mess' => Share::langErrorTransPoint()
            ];
            nv_jsonOutput($info);
        }
        $nv_Request->set_Cookie('view_auction_costs_point_' . $array_data['id_bid'], md5($user_info['userid'] . '_' . $array_data['id_bid']), NV_LIVE_COOKIE_TIME);
        nv_jsonOutput([
            'status' => 'success',
            'data' => $_dt_view_auction_costs
        ]);
    }


    if ($nv_Request->get_title('action', 'post') == 'view_change_tb') {
        $strID = $nv_Request->get_title('id', 'post', '');
        if ($strID != '') {
            $arrID = explode('-', trim($strID));
            if (intval($arrID[1]) > 0 and intval($arrID[0]) > 0) {
                // Kiểm tra xem lần nào nhỏ hơn thì cho hiển thị so sánh lên đầu
                if ($arrCheckRePost[$arrID[1]] > $arrCheckRePost[$arrID[0]]) {
                    $data = [
                        'daugia' => getDauGia($arrID[0]),
                        'daugia1' => getDauGia($arrID[1])
                    ];
                } else {
                    $data = [
                        'daugia' => getDauGia($arrID[1]),
                        'daugia1' => getDauGia($arrID[0])
                    ];
                }
                nv_jsonOutput(array(
                    'res' => 'success',
                    'data' => $data
                ));
            } else {
                nv_jsonOutput(array(
                    'res' => 'error',
                    'data' => $nv_Lang->getModule('error_check_notifi_ver')
                ));
            }
        } else {
            nv_jsonOutput(array(
                'res' => 'error',
                'data' => $nv_Lang->getModule('error_compare_daugia')
            ));
        }
    }

    $array_data['link_site'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA;

    $array_data['link__detail'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $array_data['alias'] . '-' . $array_data['id_bid'] . '.html', true);

    if (defined('NV_IS_USER')) {
        $array_data['url_download'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $array_data['alias'] . '-' . $array_data['id_bid'] . '.html', true) . '?download=' . md5($user_info['userid'] . '_' . NV_CHECK_SESSION);
    }

    $page_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $array_data['alias'] . '-' . $array_data['id_bid'] . '.html';
    $canonicalUrl = getCanonicalUrl($page_url);

    // Kiểm tra xem thời gian góc lần đầu hay k
    if ($array_data['create_at'] != $array_data['update_at']) {
        $array_data['update_info'] = sprintf($nv_Lang->getModule('update_info_last'), nv_datetime_format($array_data['update_at'], 1));
    } else {
        $array_data['update_info'] = $nv_Lang->getModule('first_update_info');
    }
    $array_data['update_at'] = nv_datetime_format($array_data['update_at'], 1);
    
    $contents = nv_theme_dau_gia_detail($array_data, $array_data_asset, $array_data_craws, $_dt_view_auction_costs);
} else {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
    die();
}
include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';

function getDauGia($id_bid, $check = false) {
    global $module_data, $db, $module_name, $xtpl, $module_info, $module_file, $global_config, $TBMT, $nv_Request, $db_config, $arr_vip, $type, $client_info, $module_config, $user_info, $global_array_vip, $nv_Lang, $space, $arr_view;
    $array_data = [];
    $db->sqlreset()
        ->select('*')
        ->from($db_config['prefix'] . '_dau_gia_bid')
        ->where('id_bid = ' . intval($id_bid));
    $sql = $db->sql();
    $array_data = $db->query($sql)->fetch();

    if (!empty($array_data)) {
        if (defined('NV_IS_USER')) {
            // kiểm tra vip
            $arr_vip = [];
            if (!empty($global_array_vip)) {
                $arr_vip = $global_array_vip;
            }
            if (!empty($arr_vip)) {
                define('NV_IS_VIP', true);
                if (isset($arr_vip[6])) {
                    define('NV_IS_VIP6', true);
                }
            }
            if (!defined('NV_IS_VIP6')) {
                $is_vip6_renew = false;
                // Kiểm tra nếu gói VIP 6 hết hạn
                $vip3_renew = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id=' . $user_info['userid'] . ' AND status != 1 AND vip =6')->fetch();
                if (!empty($vip3_renew)) {
                    $is_vip6_renew = true;
                }
            }
        }

        if (!empty($array_data['file_detail'])) {
            $array_data['file_detail'] = json_decode($array_data['file_detail'], true);
        } else {
            $array_data['file_detail'] = [];
        }

        if (!empty($array_data['id_bid_parent'])) {
            $db->sqlreset()
                ->select('alias')
                ->from($db_config['prefix'] . '_dau_gia_bid')
                ->where('id_bid = ' . $array_data['id_bid_parent']);
            $sql = $db->sql();

            $array_data['alias_parent'] = $db->query($sql)->fetch();
        }
        /*
         * Xử lý địa chỉ người sở hữu tài sản
         */
        $sql = "SELECT title FROM " . $db_config['prefix'] . "_vi_location_district WHERE id =" . $array_data['id_district_owner'];
        $result = $db->query($sql);
        if ($row = $result->fetch()) {
            $pos = strpos(strtolower($array_data['address_owner']), strtolower($row['title']));
            if ($pos !== false) {
                $str = substr($array_data['address_owner'], 0, $pos);
                $array_data['address_owner'] = rtrim($str, ', ');
            }
            $array_data['address_owner'] .= $space;
            $array_data['address_owner'] .= '<a href="' . nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=main&keyword_id_district=' . $array_data['id_district_owner'] . '&keyword_id_province=' . $array_data['id_province_owner'] . '&is_advance=1&type_search=3&type_info3=1') . '">' . $row['title'] . '</a>';
        }
        $sql = "SELECT title FROM " . $db_config['prefix'] . "_vi_location_province WHERE id =" . $array_data['id_province_owner'];
        $result = $db->query($sql);
        if ($row = $result->fetch()) {

            $pos = strpos(nv_strtolower($array_data['address_owner']), nv_strtolower($row['title']));
            $pos_1 = strpos(nv_strtolower($array_data['address_owner']), 'tỉnh');
            $pos_2 = strpos(nv_strtolower($array_data['address_owner']), 'thành phố');
            if ($pos_1 !== false) {
                $str = substr($array_data['address_owner'], 0, $pos_1);
                $array_data['address_owner'] = rtrim($str, ', ');
            } else if ($pos_2 !== false) {
                $str = substr($array_data['address_owner'], 0, $pos_2);
                $array_data['address_owner'] = rtrim($str, ', ');
            }
            if ($pos !== false) {
                $str = substr($array_data['address_owner'], 0, $pos);
                $array_data['address_owner'] = rtrim($str, ', ');
            }
            $array_data['address_owner'] .= $space;
            $array_data['address_owner'] .= '<a href="' . nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=main&keyword_id_province=' . $array_data['id_province_owner'] . '&is_advance=1&type_search=3&type_info3=1') . '">' . $row['title'] . '</a>';
        }

        // Lấy thông tin đơn vị tổ chức DGTS
        $result = $db->query("SELECT * FROM " . $db_config['prefix'] . "_dau_gia_bidder WHERE id_bidder =" . $array_data['id_bidder']);
        if ($array_bidder = $result->fetch()) {
            $array_data['phone'] = $array_bidder['phone'];
            $array_data['fax'] = $array_bidder['fax'];
            $array_data['address_bidder'] = build_full_address($array_bidder);
            $array_data['name_bidder'] = $array_bidder['name_bidder'];
            $array_data['link_bidder'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=organization/' . change_alias($array_bidder['alias']) . '-' . $array_bidder['id_bidder'] . '.html';
        }

        // lấy dữ liệu từ bảng nv4_dau_gia_asset
        $db->sqlreset()
            ->select('*')
            ->from($db_config['prefix'] . '_dau_gia_asset')
            ->where($db_config['prefix'] . '_dau_gia_asset.id_bid = ' . $array_data['id_source']);
        $sql = $db->sql();
        $result = $db->query($sql);
        while ($row = $result->fetch()) {
            $array_data_asset[$row['id_asset']] = $row;
            // Xử lý địa chỉ nơi có tài sản
            // Nối số nhà với phường, xã
            $array_data_asset[$row['id_asset']]['address_asset'] .= $space;
            $db->sqlreset()
                ->select('title')
                ->from($db_config['prefix'] . '_vi_location_district')
                ->where('id =' . $array_data_asset[$row['id_asset']]['id_district']);
            $sql2 = $db->sql();
            $result2 = $db->query($sql2);
            $array_data_province = $result2->fetchAll();
            foreach ($array_data_province as $row2) {
                $array_data_asset[$row['id_asset']]['address_asset'] .= $row2['title'];
                $array_data_asset[$row['id_asset']]['address_asset'] .= $space;
            }
            $db->sqlreset()
                ->select('title')
                ->from($db_config['prefix'] . '_vi_location_province')
                ->where('id =' . $array_data_asset[$row['id_asset']]['id_province']);
            $sql2 = $db->sql();
            $result2 = $db->query($sql2);
            $array_data_province = $result2->fetchAll();
            foreach ($array_data_province as $row2) {
                $array_data_asset[$row['id_asset']]['address_asset'] .= $row2['title'];
            }
        }

        // lấy dữ liệu từ bảng nv4_dau_gia_craws
        $db->sqlreset()
            ->select('*')
            ->from($db_config['prefix'] . '_dau_gia_craws')
            ->where($db_config['prefix'] . '_dau_gia_craws.id_source = ' . $array_data['id_source']);
        $sql = $db->sql();

        $result = $db->query($sql);
        while ($row = $result->fetch()) {
            $array_data_craws[$row['id_source']] = $row;
        }

        /*
         * Hiển thị nguồn tin
         */
        if ($arr_view['view_type']['config_value'] != '1') {
            $array_data['view_type']['name'] = $nv_Lang->getModule('ctt');
            if ($arr_view['view_type']['config_value'] == '3') {
                $array_data['view_type']['link'] = 'https://dgts.moj.gov.vn/thong-bao-cong-khai-viec-dau-gia/' . change_alias($array_data_craws[$id_bid]['title']) . '-' . $array_data_craws[$id_bid]['id_source'] . '.html';
            }
        }

        if ($arr_view['view_obj']['config_value'] == '3') {
            if (!defined('NV_IS_ADMIN') || !defined('NV_IS_SPADMIN')) {
                $array_data['view_type'] = '';
            }
        } elseif ($arr_view['view_obj']['config_value'] == '2') {
            if (!defined('NV_IS_VIP')) {
                $array_data['view_type'] = '';
            }
        }
        $array_data['vip'] = "true";
        if ($arr_view['view_vip']['config_value'] == '2') {
            if (!defined('NV_IS_VIP6')) {
                if (defined('NV_IS_USER') && $is_vip6_renew) {
                    $array_data['vip'] = "renew";
                } else {
                    $array_data['vip'] = "false";
                }
            }
        }

        // lấy các thông báo liên quan
        if ($array_data['id_bid_parent'] > 0) {
            $sql = "SELECT alias, id_bid, re_post, date_bid, title FROM " . $db_config['prefix'] . "_dau_gia_bid WHERE id_bid_parent = " . $array_data['id_bid_parent'];
            $result = $db->query($sql);
            while ($row = $result->fetch()) {
                $row['is_active'] = '';
                $row['label_active'] = '';
                if ($array_data['id_bid'] == $row['id_bid']) {
                    $row['is_active'] = 'checked';
                    $row['label_active'] = $nv_Lang->getModule('title_label_active');
                }

                $row['is_public_one'] = '';
                if ($row['re_post'] == 1) {
                    $row['is_public_one'] = $nv_Lang->getModule('is_public_one');
                }

                $row['date_bid'] = date('d/m/Y H:i', $row['date_bid']);
                $array_data['other'][$row['id_bid']] = $row;
            }
        }

        $array_data['link_site'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA;

        $array_data['link__detail'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $array_data['alias'] . '-' . $array_data['id_bid'] . '.html', true);
        $html = getDataDauGia($array_data, $array_data_asset, $array_data_craws);
        return $html;
    }
}

function getDataDauGia($row, $array_data_asset, $array_data_craws) {
    global $module_info, $op, $module_name, $client_info, $global_config, $module_config, $module_file, $nv_Lang;
    $xtpl = new XTemplate('daugia.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);
    $xtpl->assign('SELFURL', $client_info['selfurl']);

    // Thực hiện đổ dữ liệu tại đây
    $html = '';
    $file_detail_html = '';

    $b = 0;
    $reg_link = DAUTHAU_INFO_DOMAIN . '/vip/?form=1&amp;vip=6';
    $renew_link = DAUTHAU_INFO_DOMAIN . '/vip/?renewal=1&amp;vip=6';
    if ($row['vip'] === 'renew') {
        $redirect = sprintf($nv_Lang->getModule('vip6_renew_link'), $renew_link);
    } else {
        $redirect = sprintf($nv_Lang->getModule('vip6_form_link'), $reg_link);
    }
    if (!empty($array_data_asset)) {
        $i = 1;
        foreach ($array_data_asset as $asset) {
            $asset['stt'] = $i;
            $asset['min_bid_prices'] = str_replace(',', '.', number_format($asset['min_bid_prices']));
            $asset['min_bid_prices'] .= $nv_Lang->getModule('currency');
            $html2 = '';
            $abc = [];
            $b = 0;
            if (($asset['deposit']) != 0) {
                if ($asset['deposit'] < 30) {
                    $html2 = $asset['deposit'] . '%';
                } else {
                    $html2 = str_replace(',', '.', number_format($asset['deposit'])) . $nv_Lang->getModule('currency');
                }
                $asset['deposit'] = $html2;
            }
            if (empty($asset['qty'])) {
                $asset['qty'] = '';
            }
            if ($row['vip'] == 'false') {
            }
            $xtpl->assign('ASSET', $asset);
            $xtpl->parse('main.asset');
            $i++;
        }
    }
    if (!empty($array_data_craws)) {
        foreach ($array_data_craws as $crawl) {
            $crawl['time_crawl_format'] = nv_date("H:i d/m/Y", $crawl['time_crawl']);
            $xtpl->assign('CRAWL', $crawl);
            $xtpl->parse('main.crawl');
        }
    }

    $row['date_bid_c'] = $row['date_bid'];
    $row['closing_deposit_c'] = $row['closing_deposit'];
    $row['opening_reg_c'] = $row['opening_reg'];
    $row['closing_reg_c'] = $row['closing_reg'];
    $row['date_bid_format'] = $row['date_bid'] > 0 ? nv_date("H:i d/m/Y", $row['date_bid']) : '';
    $row['opening_bid_format'] = $row['opening_bid'] > 0 ? nv_date("H:i d/m/Y", $row['opening_bid']) : '';
    $row['opening_reg_format'] = $row['opening_reg'] > 0 ? nv_date("H:i d/m/Y", $row['opening_reg']) : '';
    $row['closing_reg_format'] = $row['closing_reg'] > 0 ? nv_date("H:i d/m/Y", $row['closing_reg']) : '';
    $row['closing_deposit_format'] = $row['closing_deposit'] > 0 ? nv_date("H:i d/m/Y", $row['closing_deposit']) : '';
    $row['opening_deposit_format'] = $row['opening_deposit'] > 0 ? nv_date("H:i d/m/Y", $row['opening_deposit']) : '';
    if (!empty($row['file_detail'])) {
        $file_detail_html = '';
        $file_detail_html = "";
        foreach ($row['file_detail'] as $file) {
            $file_detail_html .= '<div><p><b><a rel="nofollow" href="' . str_replace(' ', '+', 'https://dgts.moj.gov.vn/common/download?name=' . $file['fileName'] . '&path=' . $file['linkFile']) . '">' . $file['fileName'] . '</a></b></p></div>';
        }
        $row['link_detail'] = $file_detail_html;
    }
    if (!empty($row['view_type'])) {
        $_view_type = '';
        if ($row['view_type']['name']) {
            $_view_type = "";
            if (!empty($row['view_type']['link'])) {
                $_view_type .= '<div><p><b><a rel="nofollow" href="' . $row['view_type']['link'] . '">' . $row['view_type']['name'] . '</a></b></p></div>';
            } else {
                $_view_type .= '<div><p><b>' . $row['view_type']['name'] . '</b></p></div>';
            }
        }
        $row['view_type'] = $_view_type;
    }

    $row['doc_link'] = 'https://dgts.moj.gov.vn/portal/exportWord?aucInfoId=' . $row['id_bid'];
    if ($row['vip'] != 'true') {
        $row['detail'] = $redirect;
        $row['address'] = $redirect;
        $row['phone'] = $redirect;
        $row['fax'] = $redirect;
        $row['link_detail'] = '';
        $row['doc'] = '';
    }

    if (!defined('NV_IS_VIP6')) {
        $receive_email = [];
        $receive_email['content'] = sprintf($nv_Lang->getModule('receive_email_content'), $row['title'], ($row['vip'] === 'renew' ? $renew_link : $reg_link), ($row['vip'] === 'renew' ? $nv_Lang->getModule('renew') : $nv_Lang->getModule('register')), 'VIP6');
        $receive_email['reg_link'] = $row['vip'] === 'renew' ? $renew_link : $reg_link;
        $receive_email['vip_paket'] = $row['vip'] === 'renew' ? $nv_Lang->getModule('vip6_renew') : $nv_Lang->getModule('vip6_form');
        $xtpl->assign('RECEIVE_EMAIL', $receive_email);
        $xtpl->parse('main.receive_email_content');
    }

    $filter = [];
    $filter['content'] = sprintf($nv_Lang->getModule('filter_content'), $row['title']);
    if (!defined('NV_IS_USER')) {
        $filter['reg_link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=register&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
        $filter['btn_title'] = $nv_Lang->getModule('btn_register');
    } else {
        $filter['reg_link'] = DAUTHAU_INFO_DOMAIN . '/filters/';
        $filter['btn_title'] = $nv_Lang->getModule('btn_filter');
    }
    $xtpl->assign('FILTER', $filter);
    $xtpl->assign('ROW', $row);

    if (!empty($row['other'])) {
        foreach ($row['other'] as $other) {
            $other['url_parent'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $other['alias'] . '-' . $other['id_bid'] . '.html';
            $xtpl->assign('VALUE', $other);
            $other['link'] = $other['url_parent'];
            $xtpl->parse('main.thongbao_lq.loop');
        }

        if (sizeof($row['other']) > 1) {
            $xtpl->parse('main.thongbao_lq.show_btn');
        }
        $xtpl->parse('main.thongbao_lq');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}
