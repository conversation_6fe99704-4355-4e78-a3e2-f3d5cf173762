
<table class="table table-striped table-bordered detail_scoll__menu m-top" id="info_contractor">
    <tbody>
        <tr>
            <td id="bigger-in-report" width="150px">{LANG.companyname}</td>
            <!-- BEGIN: companyname -->
            <td>{ROW.companyname}</td>
            <!-- END: companyname -->
            <!-- BEGIN: h1companyname -->
            <td>
                {ROW.companyname}
            </td>
            <!-- END: h1companyname -->
            <td id="detail-logo" width="130px" rowspan="4">
                <img border="0" class="image" src="{ROW.logo}" width="130" alt="" title="{ROW.companyname}" />
            </td>
        </tr>
        <!-- BEGIN: officialname -->
        <tr>
            <td>{LANG.officialname}</td>
            <td>{ROW.officialname}</td>
        </tr>
        <!-- END: officialname -->
        <tr class="hide-in-report">
            <td>{LANG.totalview}</td>
            <td>{ROW.totalview}</td>
        </tr>
        <tr>
            <td>{LANG.taxcode}</td>
            <td>{ROW.code}</td>
        </tr>
        <!-- BEGIN: taxdate -->
        <tr>
            <td>{LANG.taxdate_new}</td>
            <td colspan="2">{TAXDATE_CONVERT}</td>
        </tr>
        <!-- END: taxdate -->
        <!-- BEGIN: tax_nation -->
        <tr>
            <td>{LANG.tax_nation}</td>
            <td colspan="2">{ROW.tax_nation_title}</td>
        </tr>
        <!-- END: tax_nation -->
        <!-- BEGIN: orgcode -->
        <tr>
            <td>{LANG.orgcode}</td>
            <td colspan="2">{ROW.orgcode}</td>
        </tr>
        <!-- END: orgcode -->
        <!-- BEGIN: short -->
        <tr>
            <td>{LANG.short}</td>
            <td colspan="2">{ROW.short}</td>
        </tr>
        <!-- END: short -->

        <!-- BEGIN: office_address -->
        <tr>
            <td>{LANG.address}</td>
            <td colspan="2">{office_address}</td>
        </tr>
        <!-- END: office_address -->

        <!-- BEGIN: trading_address -->
        <tr>
            <td>{LANG.trading_address}</td>
            <td colspan="2">{trading_address}</td>
        </tr>
        <!-- END: trading_address -->

        <tr>
            <td>{LANG.addresstruso}</td>
            <td colspan="2">{ROW.addressfull}</td>
        </tr>

        <!-- BEGIN: phone -->
        <tr class="business_phone">
            <td>{LANG.phone}</td>
            <td colspan="2">{ROW.phone}</td>
        </tr>
        <!-- END: phone -->
        <!-- BEGIN: represent_phone -->
        <tr class="represent_phone">
            <td>{LANG.represent_phone}</td>
            <td colspan="2">{ROW.represent_phone}</td>
        </tr>
        <!-- END: represent_phone -->
        <!-- BEGIN: fax -->
        <tr class="business_fax">
            <td>{LANG.fax}</td>
            <td colspan="2">{ROW.fax}</td>
        </tr>
        <!-- END: fax -->
        <!-- BEGIN: email -->
        <tr class="business_email">
            <td>{LANG.email}</td>
            <td colspan="2">{ROW.email}</td>
        </tr>
        <!-- END: email -->
        <!-- BEGIN: represent_email -->
        <tr class="represent_email">
            <td>{LANG.represent_email}</td>
            <td colspan="2">{ROW.represent_email}</td>
        </tr>
        <!-- END: represent_email -->
        <!-- BEGIN: website -->
        <tr>
            <td>{LANG.website}</td>
            <td class="wrap__text" colspan="2">{ROW.website}</td>
        </tr>
        <!-- END: website -->
        <!-- BEGIN: linh_vuc_kinh_doanh -->
        <tr>
            <td>{LANG.industry1}</td>
            <td colspan="2">{ROW.linh_vuc_kinh_doanh}</td>
        </tr>
        <!-- END: linh_vuc_kinh_doanh -->
        <!-- BEGIN: businesstype -->
        <tr>
            <td>{LANG.businesstype}</td>
            <td colspan="2">
                <a href="{ROW.link_businesstype}">{ROW.businesstype}</a>
            </td>
        </tr>
        <!-- END: businesstype -->
        <!-- BEGIN: dateestablished -->
        <tr>
            <td>{LANG.dateestablished}</td>
            <td colspan="2">{ROW.dateestablished}</td>
        </tr>
        <!-- END: dateestablished -->
        <!--tr>
            <td>{LANG.businesslicenses}</td>
            <td colspan="2">{ROW.businesslicenses}</td>
        </tr-->
        <!-- BEGIN: registrationtime -->
        <tr>
            <td>{LANG.registrationtime}</td>
            <td colspan="2">{ROW.registrationtime}</td>
        </tr>
        <!-- END: registrationtime -->
        <!-- BEGIN: representative -->
        <tr>
            <td>{LANG.representative}</td>
            <td colspan="2">{ROW.representative}</td>
        </tr>
        <tr>
            <td>{LANG.addressrepresentative}</td>
            <td colspan="2">{ROW.addressrepresentative}</td>
        </tr>
        <!-- END: representative -->
        <!-- BEGIN: von_dieu_le -->
        <tr>
            <td>{LANG.chartercapital}</td>
            <td colspan="2">{ROW.von_dieu_le}</td>
        </tr>
        <!-- END: von_dieu_le -->
        <!-- BEGIN: so_nhan_vien -->
        <tr>
            <td>{LANG.num_worker}</td>
            <td colspan="2">{ROW.so_nhan_vien}</td>
        </tr>
        <!-- END: so_nhan_vien -->
        <!--tr>
            <td>{LANG.taxcode}</td>
            <td colspan="2">{ROW.taxcode}</td>
        </tr-->
        <!-- BEGIN: about -->
        <tr>
            <td>{LANG.about}</td>
            <td colspan="2">{ROW.about}</td>
        </tr>
        <!-- END: about -->
        <!-- BEGIN: currentstatus -->
        <tr>
            <td>{LANG.currentstatus}</td>
            <td colspan="2">{ROW.currentstatus}</td>
        </tr>
        <!-- END: currentstatus -->
        <tr>
            <td>{LANG.ngay_phe_duyet}</td>
            <td colspan="2">{ROW.ngay_phe_duyet}</td>
        </tr>
        <!-- BEGIN: thoi_han -->
        <tr>
            <td>{LANG.nop_phi}</td>
            <td colspan="2">
                {ROW.thoi_han}
                <div class="hide-in-report">
                    <br /> {LANG.fee_payment_information}
                    <!-- BEGIN: status_dtnet_title -->
                    <br /> {STATUS_DTNET_TITLE}
                    <!-- END: status_dtnet_title -->
                </div>
            </td>
        </tr>
        <!-- END: thoi_han -->
        <!-- BEGIN: rep_name -->
        <tr>
            <td>{LANG.rep_name}</td>
            <td colspan="2">{ROW.rep_name}</td>
        </tr>
        <!-- END: rep_name -->
        <!-- BEGIN: rep_position -->
        <tr>
            <td>{LANG.rep_position}</td>
            <td colspan="2">{ROW.rep_position}</td>
        </tr>
        <!-- END: rep_position -->
        <!-- BEGIN: is_stock -->
        <tr class="hide-in-report">
            <td colspan="3">
                <strong>{LANG.is_stock}</strong>
            </td>
        </tr>

        <tr class="hide-in-report">
            <td width="150px">{LANG.stocks_code}</td>
            <td colspan="2">
                <a href="{ROW.stocks.link_hsx}" rel="noopener noreferrer nofollow" target="_blank">{ROW.stocks.code}
            </td>
        </tr>
        <tr class="hide-in-report">
            <td>{LANG.stocks_exchange}</td>
            <td colspan="2">
                <a href="{ROW.stocks.link_exchange}">{ROW.stocks.exchange}</a>
            </td>
        </tr>
        <tr class="hide-in-report">
            <td>{LANG.list_date}</td>
            <td colspan="2">{ROW.stocks.list_date}</td>
        </tr>
        <!-- END: is_stock -->
        <!-- BEGIN: att_file -->
        <tr>
            <td>{LANG.att_file}</td>
            <td colspan="2">
                <div class="tab-content download">
                    <div class="tab-pane fade active in">
                        <div class="list-group download-link">
                            <<ATT_FILE>>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        <!-- END: att_file -->
        <!-- BEGIN: org_scale -->
        <tr>
            <td>{LANG.org_scale}</td>
            <td colspan="2">{ORG_SCALE_TITLE}</td>
        </tr>
        <!-- END: org_scale -->
        <!-- BEGIN: industry -->
        <tr id="business_industry" class="detail_scoll__menu">
            <td colspan="2"><h2>{NNKD_INDUSTRY}</h2></td>
        </tr>
        <tr class="hide-in-report">
            <td colspan="3">
                <ul class="nav nav-tabs">
                    <!-- BEGIN: tab_active_dkkd -->
                    <li class="active"><a data-toggle="tab" href="#industry_dkkd">{LANG.industry_registration}</a></li>
                    <!-- END: tab_active_dkkd -->
                    <!-- BEGIN: tab_active_msc -->
                    <li class="{TAB_ACTIVE}"><a data-toggle="tab" href="#industry_msc">{LANG.industry_msc}</a></li>
                    <!-- END: tab_active_msc -->
                    <!-- BEGIN: tab_active_icb -->
                    <li class="{TAB_ACTIVE_ICB}"><a data-toggle="tab" href="#industry_icb">{LANG.industry_icb}</a></li>
                    <!-- END: tab_active_icb -->
                </ul>
                <div class="tab-content">
                    <!-- BEGIN: table_dkkd -->
                    <div id="industry_dkkd" class="tab-pane fade in active">
                        <div class="{EXPAND_DKKD} expand-wrap">
                            <div class="expand-content">
                                <table class="table res-table table-striped table-bordered">
                                    <!-- BEGIN: loop_dkkd -->
                                    <tr>
                                        <td class="col-md-2 col-sm-3 col-xs-4">{INDUSTRY_KEY}</td>
                                        <td>{INDUSTRY}</td>
                                    </tr>
                                    <!-- END: loop_dkkd -->
                                </table>
                                <div class="gradient"></div>
                            </div>
                            <a href="#industry_dkkd" class="btn-expand text-center">{LANG.expand}</a>
                        </div>
                    </div>
                    <!-- END: table_dkkd -->
                    <!-- BEGIN: table_msc -->
                    <div id="industry_msc" class="tab-pane fade {TAB_ACTIVE_IN}">
                        <div class="{EXPAND_MSC} expand-wrap">
                            <div class="expand-content">
                                <table class="table res-table table-striped table-bordered">
                                    <!-- BEGIN: loop -->
                                    <tr>
                                        <td class="col-md-2 col-sm-3 col-xs-4">{INDUSTRY_KEY}</td>
                                        <td>
                                            {INDUSTRY}
                                            <!-- BEGIN: main_industry -->
                                            <strong>{MAIN_INDUSTRY}</strong>
                                            <!-- END: main_industry -->
                                        </td>
                                    </tr>
                                    <!-- END: loop -->
                                </table>
                                <div class="gradient"></div>
                            </div>
                            <a href="#industry_msc" class="btn-expand text-center">{LANG.expand}</a>
                        </div>
                    </div>
                    <!-- END: table_msc -->

                    <!-- BEGIN: table_icb -->
                    <div id="industry_icb" class="tab-pane fade {TAB_ACTIVE_IN_ICB}">
                        <div class="{EXPAND_ICB} expand-wrap">
                            <div class="expand-content">
                                <table class="table res-table table-striped table-bordered">
                                    <!-- BEGIN: loop -->
                                    <tr>
                                        <td class="text-center" width="5%">{VALUE.stt}</td>
                                        <td>
                                            <a href="{VALUE.link}">{VALUE.code}-{VALUE.title}</a>
                                        </td>
                                    </tr>
                                    <!-- END: loop -->
                                </table>
                                <div class="gradient"></div>
                            </div>
                            <a href="#industry_icb" class="btn-expand text-center">{LANG.expand}</a>
                        </div>
                    </div>
                    <!-- END: table_icb -->
                </div>
            </td>
        </tr>
        <!-- END: industry -->
        <!-- BEGIN: gmaps -->
        <tr class="hide-in-report">
            <td colspan="3">{LANG.gmaps}</td>
        </tr>
        <tr class="hide-in-report">
            <td colspan="3" height="300px">
                <script src="https://maps.googleapis.com/maps/api/js?key={CONFIG.Google_Maps_API_Key}&libraries=places" type="text/javascript"></script>
                <div id="googlemap" style="height: 300px"></div>
                <script type="text/javascript" language="javascript">
                    googlemapload({CONFIG.gmap_lat}, {CONFIG.gmap_lng}, {CONFIG.gmap_z});
                </script>
            </td>
        </tr>
        <!-- END: gmaps -->
    </tbody>
</table>
<script>
    $(".btn-expand").click(function(){
        $(this).parent().find( ".expand-content" ).toggleClass('active');
        $(this).text(function(i, text){
            return text === "{LANG.expand}" ? "{LANG.collapse}" : "{LANG.expand}";
        });
    });
</script>
