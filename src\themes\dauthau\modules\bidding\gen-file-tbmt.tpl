<!-- BEGIN: main -->
    <!-- BEGIN: loop_yckn -->
    <div class="clarify-yckn">
        <div>
            <table class="table table-striped table-bordered ">
                <tbody>

                    <tr>
                        <th>#{STT}: {YCKN_REQUEST.req_name}</th>
                    </tr>
                    <!-- BEGIN: request -->
                    <tr>
                        <td>
                            <strong>{LANG.petition_request_date}: </strong>{YCKN_REQUEST.req_date}<br />
                            <strong>{LANG.petition_content}:</strong> {YCKN_REQUEST.req_content} <br />

                            <strong>{LANG.attached_file_is_the_petition_content}:</strong>
                            <div class="download_normal">
                                <div class="list-group-item display-flex">                        
                                    <a href="#"><span typefile="yckn_file" checkss= "{YCKN_REQUEST.checkss_req}" is_req="1" rid="{YCKN_REQUEST.id}" id-file="{YCKN_REQUEST.req_file_id}" class="is_vip">{YCKN_REQUEST.file_name}</span></a>
                                    <div class="text-nowrap" style="margin-left:auto"></div>
                                </div>                                
                            </div>
                        </td>
                    </tr>
                    <!-- BEGIN: reply -->
                    <tr>
                        <td>
                            <strong>{LANG.petition_reply_date}:</strong> {YCKN_REQUEST.res_date} <br />
                            <strong>{LANG.petition_reply}:</strong> {YCKN_REQUEST.res_content} <br />
                            <strong>{LANG.attached_file_is_the_petition_reply_content}:</strong>
                            <div class="download_normal">
                                <div class="list-group-item display-flex">                        
                                    <a href="#"><span typefile="yckn_file" checkss= "{YCKN_REQUEST.checkss_resp}" is_req="0" rid="{YCKN_REQUEST.id}" id-file="{YCKN_REQUEST.res_file_id}" class="is_vip">{YCKN_REQUEST.res_file_name}</span></a>
                                    <div class="text-nowrap" style="margin-left:auto"></div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <!-- END: reply -->
                    <!-- END: request -->
                    <!-- BEGIN: continues -->
                    <tr>
                        <td>
                            {LANG.continues_num} {YCKN_CONTINUES.stt} <br />
                            <strong>{LANG.petition_reply_date}:</strong> {YCKN_CONTINUES.res_date} <br />
                            <strong>{LANG.petition_reply}:</strong> {YCKN_CONTINUES.res_content} <br />
                            <strong>{LANG.attached_file_is_the_petition_reply_content}:</strong>
                            <div class="download_normal">
                                <div class="list-group-item display-flex">                        
                                    <a href="#"><span typefile="yckn_file" checkss= "{YCKN_CONTINUES.checkss_resp}" is_req="0" rid="{YCKN_CONTINUES.id}" id-file="{YCKN_CONTINUES.res_file_id}" class="is_vip">{YCKN_CONTINUES.res_file_name}</span></a>
                                    <div class="text-nowrap" style="margin-left:auto"></div>
                                </div> 
                            </div>                                    
                        </td>
                    </tr>
                    <!-- END: continues -->
                </tbody>
            </table>
        </div>
    </div>
    <!-- END: loop_yckn -->
    
    <!-- BEGIN: loop_clarify -->
    <div class="clarify-yckn">
        <div>{LANG.clarify_name}: <b>{CLARIFY.req_name}</b></div>
        <div>
            <div>
                <table class="table table-striped table-bordered ">
                    <thead>
                        <tr>
                            <th>{LANG.clarify_subjectname}</th>
                            <th>{LANG.clarify_question}</th>
                            <th>{LANG.clarify_response}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- BEGIN: loop_content -->
                        <tr>
                            <td>{CLA_CONTENT.subjectName}</td>
                            <td>{CLA_CONTENT.question}</td>
                            <td>{CLA_CONTENT.response}</td>
                        </tr>
                        <!-- END: loop_content -->
                    </tbody>
                </table>
            </div>
            <div>                        
                <div class="download plane-o">
                    <p><b>{LANG.clarify_file_name}: </b></p>
                    <!-- BEGIN: clarify_file_name -->
                    <div class="list-group-item display-flex">                        
                        <a href="#"><span typefile="clarify_file" checkss= "{CLARIFY.checkss_req}" is_req="1" rid="{CLARIFY.id}" id-file="{CLARIFY.clarify_file_id}" class="is_vip">{CLARIFY.clarify_file_name}</span></a>
                        <div class="text-nowrap" style="margin-left:auto"></div>
                    </div>
                    <!-- END: clarify_file_name -->
                </div>
                <div class="download plane-o margin-top-sm">
                    <p><b>{LANG.clarify_res_file_name}: </b></p>
                    <!-- BEGIN: clarify_res_file_name -->
                    <div class="list-group-item display-flex">
                        <a href="#"><span typefile="clarify_file" checkss= "{CLARIFY.checkss_resp}" is_req="0" rid="{CLARIFY.id}" id-file="{CLARIFY.clarify_res_file_id}" class="is_vip">{CLARIFY.clarify_res_file_name}</span></a>
                        <div class="text-nowrap" style="margin-left:auto"></div>
                    </div>
                    <!-- END: clarify_res_file_name -->
                </div>
                <div><b>{LANG.clarify_sign_res_date}: </b> {CLARIFY.sign_res_date}</div>
            </div>
        </div>
        <hr/>
    </div>
    <!-- END: loop_clarify -->

    <!-- BEGIN: lamro_kiennghi -->
    <div class="qdht_yclr">
        <p id="tai_ho_so">{DOWNLOAD_MESS}</p>
        <!-- BEGIN: point_or_t0 -->
        <div class="container-download">
            <div class="column-download">
                <div class="button-container">
                    <div class="center-buttons">
                        <button class="btn btn-primary" onclick="buy_fastlink_qd(this)" data-type="{TYPE}" data-id="{id}" data-confirm="{LANG.down_point_confirm}">{LANG.link_file_fast}</button>
                    </div>
                    <p>{info_T0}</p>
                </div>
            </div>
            <div class="column-download">
                <div class="button-container">
                    <div class="center-buttons">
                        <button class="btn btn-primary" onclick="redirect_link('{link_T0}')">{LANG.buy_TO}</button>
                    </div>
                    <p>{LANG.show_info_down_t0_2227}</p>
                </div>
            </div>
        </div>
        <!-- END: point_or_t0 -->
        <!-- BEGIN: vip_size_info -->
        <p>
            <em class="fa fa-bell"></em> {VIPSIZE_MESS}
        </p>
        <!-- END: vip_size_info -->  
        <div class="tab-content download {ICON_PLANE}">
            <div class="tab-pane fade{HOME_ACTIVE} in" id="nav_first" role="tabpanel" aria-labelledby="nav_first_tab">
                {CLARIFY_HTML}
            </div>
            <!-- BEGIN: is_point_down_show2 -->
            <div class="tab-pane fade {POINT_ACTIVE} in list-group download-link is_points{IS_OTHER_BROWSER}" id="nav_second" role="tabpanel" aria-labelledby="nav_second_tab">{CLARIFY_HTML}</div>
            <!-- END: is_point_down_show2 -->
        </div>

        
        <!-- BEGIN: if_ie_down -->
        <small><i class="fa fa-paper-plane-o"></i> {note_ie_down}</small> <br>
        <small><i class="fa fa-exclamation-circle"></i> {LANG.note_ehsmt} </small>
        <!-- END: if_ie_down -->
    </div>
    <!-- END: lamro_kiennghi -->
    <!-- BEGIN: quyet_dinh -->
    <div class="bidding-detail-item flex-direction-column qdht_yclr">
        <div class="c-tit">{title}</div>
        <div class="c-val">
            <p id="tai_ho_so">{DOWNLOAD_MESS}</p>
            <!-- BEGIN: point_or_t0 -->
            <div class="container-download">
                <div class="column-download">
                    <div class="button-container">
                        <div class="center-buttons">
                            <button class="btn btn-primary" onclick="buy_fastlink_qd(this)" data-type="{TYPE}" data-id="{id}" data-confirm="{LANG.down_point_confirm}">{LANG.link_file_fast}</button>
                        </div>
                        <p>{info_T0}</p>
                    </div>
                </div>
                <div class="column-download">
                    <div class="button-container">
                        <div class="center-buttons">
                            <button class="btn btn-primary" onclick="redirect_link('{link_T0}')">{LANG.buy_TO}</button>
                        </div>
                        <p>{LANG.show_info_down_t0_2227}</p>
                    </div>
                </div>
            </div>
            <!-- END: point_or_t0 -->
            <!-- BEGIN: vip_size_info -->
            <p>
                <em class="fa fa-bell"></em> {VIPSIZE_MESS}
            </p>
            <!-- END: vip_size_info -->
            <div class="tab-content download {ICON_PLANE}">
                <div class="tab-pane fade{HOME_ACTIVE} in" id="nav_first" role="tabpanel" aria-labelledby="nav_first_tab">
                    <div class="list-group download-link{IS_OTHER_BROWSER} tl wrap__text">
                        <div class="list-group-item display-flex">
                            <span>{NORMAL}</span>
                        </div>
                    </div>
                </div>
                <!-- BEGIN: is_point_down_show2 -->
                <div class="tab-pane fade {POINT_ACTIVE} in list-group download-link is_points{IS_OTHER_BROWSER}" id="nav_second" role="tabpanel" aria-labelledby="nav_second_tab">{QD}</div>
                <!-- END: is_point_down_show2 -->
            </div>
            <!-- BEGIN: if_ie_down -->
            <small><i class="fa fa-paper-plane-o"></i> {note_ie_down}</small> <br>
            <small><i class="fa fa-exclamation-circle"></i> {LANG.note_ehsmt} </small>
            <!-- END: if_ie_down -->
        </div>
    </div>
    <!-- END: quyet_dinh -->
    <!-- BEGIN: show_countdown -->
    <script>
        $(function() {
            startTimer({COUNTDOWN_NUM}, $(".qdht_yclr .countdown"));
        });
    </script>
    <!-- END: show_countdown -->
<!-- END: main -->
