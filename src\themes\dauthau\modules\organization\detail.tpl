<!-- BEGIN: main -->
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/js/bidding.js"></script>
<link type="text/css" rel="stylesheet" href="{NV_BASE_SITEURL}themes/dauthau/css/bidding.css"></link>
<!-- BEGIN: popup_login -->
{POPUP_LOGIN}
<!-- END: popup_login -->
<!-- BEGIN: recaptcha -->
<div id="captchaModal" class="modal fade auto-width auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <p class="modal-title">{LANG.recapcha_title}</p>
            </div>
            <div class="modal-body">
                <div>{LANG.recapcha_body}</div>
                <div data-toggle="recaptcha" data-callback="verify_captcha" id="{RECAPTCHA_ELEMENT}"></div>
            </div>
        </div>
    </div>
</div>
<script>
    function verify_captcha(e) {
        $('#captchaModal').modal('hide');
        confirm_crawl_detail()
    }
</script>
<!-- END: recaptcha -->

<div class="bidding-detail-wrapper bidding-detail-wrapper-result detail-orang">
    <h1 class="bidding-name">{DETAIL.ten_to_chuc}</h1>
    <div class="row">
        <!-- BEGIN: dtnet_link_button -->
        <div class="pull-right btn-hsdn-link">
            <a href="{DTNET_LINK_BUTTON}" class="btn btn-primary btn-xs" title="{LANG.profile_dtnet_title}"><i class="fa fa-link" aria-hidden="true"></i> {LANG.profile_dtnet}</a>
        </div>
        <!-- END: dtnet_link_button -->
        <div class="col-xs-24 btn-share-group">
            <span>{LANG.share} </span>
            <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_fb_share();" title="{LANG.fb_share}">
                <span class="icon-facebook"></span>
            </a>
            <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_tw_share('', '{DETAIL.title}');" title="{LANG.tweet}">
                <span class="icon-twitter"></span>
            </a>
            <a href="javascript:void(0)" class="btn-share btn-copy-link" title="{LANG.copy_link}">
                <em class="fa fa-link"></em>
                <span class="tip" style="display: none;">{LANG.link_copy_successfully}</span>
            </a>
        </div>
    </div>
    <div class="m-bottom text-right">
        <div class="text-right crawl_time mg-bt-5">
            <div class="small">
                {LANG.crawl_time}: <strong>{DETAIL.thoi_gian_boc}</strong>
            </div>
            <!-- BEGIN: update -->
            <div class="margin-top-sm">
                <span class="small">{UPDATE_INFO}</span>
                <a style="margin-left: auto" id="reupdate" class="btn btn-default btn-xs active" onclick="show_capcha()" href="javascript:void(0)" data-id="{DETAIL.id}" data-check="{CHECKSESS_UPDATE}">{LANG.cap_nhat_lai}</a>
                <img id="update_wait" style="display: none" src="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/images/load_bar.gif" />
                <!-- BEGIN: crawl_request_history_button -->
                <a style="margin-left: auto" id="crawl_request_history" class="btn btn-default btn-xs active" href="javascript:void(0)">{LANG.crawl_request_history}</a>
                <!-- END: crawl_request_history_button -->
            </div>
            <!-- END: update -->
        </div>
    </div>
    <!-- BEGIN: crawl_request_history_list -->
    <div class="m-bottom" style="display: none;width: 100%;" id="crawl_request_history_list">
        <table class="bidding-table">
            <thead>
                <tr>
                    <th>{LANG.stt}</th>
                    <th>{LANG.username}</th>
                    <th>{LANG.request_time}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td data-column="{LANG.stt}">{CR.stt}</td>
                    <td data-column="{LANG.username}">{CR.username}</td>
                    <td data-column="{LANG.request_time}">{CR.last_reload}</td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
    <!-- END: crawl_request_history_list -->
    <!-- BEGIN: warning_duplicate -->
    <div class="alert alert-warning">{WARNING_DUPLICATE}</div>
    <!-- END: warning_duplicate -->
    <div class="bidding-detail">
        <!-- BEGIN: ten_viet_tat -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.sort_name}</div>
            <div class="c-val">{DETAIL.ten_viet_tat}</div>
        </div>
        <!-- END: ten_viet_tat -->
        <div class="bidding-detail-item col-four{HIDDEN_MCC}">
            <!-- BEGIN: ma_chung_chi -->
            <div>
                <div class="c-tit">{LANG.ma_chung_chi_nang_luc}</div>
                <div class="c-val">{DETAIL.ma_chung_chi}</div>
            </div>
            <!-- END: ma_chung_chi -->
            <!-- BEGIN: tinh -->
            <div>
                <div class="c-tit">{LANG.tinh}</div>
                <div class="c-val"><a href="{DETAIL.link_province}">{DETAIL.tinh}</a></div>
            </div>
            <!-- END: tinh -->
        </div>
        <div class="bidding-detail-item col-four{HIDDEN_DC}">
            <!-- BEGIN: dia_chi_tru_so -->
            <div>
                <div class="c-tit">{LANG.dia_chi_tru_so}</div>
                <div class="c-val">{DETAIL.dia_chi_tru_so}</div>
            </div>
            <!-- END: dia_chi_tru_so -->
            <!-- BEGIN: text_tinh -->
            <div>
                <div class="c-tit">{LANG.moderator}</div>
                <div class="c-val"><a href="{DETAIL.link_dvkd}">{DETAIL.text_tinh}</a></div>
            </div>
            <!-- END: text_tinh -->
        </div>
        <div class="bidding-detail-item col-four{HIDDEN_NDD}">
            <!-- BEGIN: nguoi_dai_dien -->
            <div>
                <div class="c-tit">{LANG.nguoi_dai_dien}</div>
                <div class="c-val">{DETAIL.nguoi_dai_dien}</div>
            </div>
            <!-- END: nguoi_dai_dien -->
            <!-- BEGIN: chuc_vu -->
            <div>
                <div class="c-tit">{LANG.chuc_vu}</div>
                <div class="c-val">{DETAIL.chuc_vu}</div>
            </div>
            <!-- END: chuc_vu -->
        </div>
        <!-- BEGIN: dia_chi_vp -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.dia_chi_vp}</div>
            <div class="c-val">{DETAIL.dia_chi_vp}</div>
        </div>
        <!-- END: dia_chi_vp -->
        <!-- BEGIN: ma_so_thue -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.ma_so_thue}</div>
            <div class="c-val">{DETAIL.ma_so_thue}</div>
        </div>
        <!-- END: ma_so_thue -->

        <div class="bidding-detail-item col-four{HIDDEN_NC}">
            <!-- BEGIN: ngay_cap -->
            <div>
                <div class="c-tit">{LANG.ngay_cap}</div>
                <div class="c-val">{DETAIL.ngay_cap}</div>
            </div>
            <!-- END: ngay_cap -->
            <!-- BEGIN: co_quan_cap -->
            <div>
                <div class="c-tit">{LANG.co_quan_cap}</div>
                <div class="c-val">{DETAIL.co_quan_cap}</div>
            </div>
             <!-- END: co_quan_cap -->
        </div>

        <!-- BEGIN: history_change_update -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.updates}</div>
            <div class="c-val link-icon">
                <!-- BEGIN: loop -->
                <p>
                    <label><input type="checkbox" {RELATE.checked} value="{RELATE.ver}" class="chk_notification"> {RELATE.date_update}</label><span> {RELATE.title_first}</span><span class="info_change_tb" data-tb='{RELATE.key}'>{RELATE.text_changetb}</span> <span class="link_change_a">(<a href="javascript:void(0)" data-id="{RELATE.data_id_tbmt}" data-toggle="modal" data-target="#showTB">{LANG.title_change_tb}</a>)
                    </span>
                </p>
                <!-- END: loop -->
                <span data-toggle="tooltip" data-placement="right" title="{LANG.tooltip_tb}">
                    <button class="btn btn-primary btn-xs" id="view_change_tb" data-toggle="modal" data-target="#showTB">{LANG.title_change_tb}</button>
                    <button class="btn btn-primary btn-xs" id="view_change_tb1">{LANG.title_change_tb}</button>
                </span>
            </div>
        </div>
        <!-- Modal -->
        <div id="showTB" class="modal fade" role="dialog">
            <div class="modal-dialog modal-lg">
                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <p class="modal-title h4">{LANG.title_organ}</p>
                    </div>
                    <div class="modal-body">
                        <div id="main_change_notificaion">
                            <div class="change_notificaion">
                                <div class="row">
                                    <div class="col-md-24">
                                        <label>{LANG.kieusosanh}: </label> <label><input type="radio" name="sosanhtb" checked value="1">&nbsp;{LANG.title_muti}</label> <label><input type="radio" name="sosanhtb" value="2">&nbsp;{LANG.title_one}</label>
                                    </div>
                                    <div class="col-md-24">
                                        <div class="card">
                                            <label><input type="checkbox" class="change_by_line" value="3">&nbsp;{LANG.title_linebyline}</label>
                                            <div class="row_compare">
                                                <div class="col">
                                                    <div class="card" id="outputOriginal"></div>
                                                </div>
                                                <div class="col">
                                                    <div class="card" id="output"></div>
                                                </div>
                                                <div class="col" id="new_version">
                                                    <div class="card" id="outputNew"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">{LANG.close}</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- END: history_change_update -->
    </div>
</div>
<div class="bidding-simple">
<div class="border-bidding">
    <h2 class="title__tab_heading"><span>{LANG.linh_vuc_hoat_dong}</span></h2>
</div>

<div style="margin-top: 10px">
    <div class="box__table">
        <table class="bidding-table table-sticky-head">
            <thead>
                <tr>
                    <th>{LANG.stt}</th>
                    <th>{LANG.so_chung_chi}</th>
                    <th class="w250">{LANG.linh_vuc}</th>
                    <th class="w250">{LANG.linh_vuc_mo_rong}</th>
                    <th>{LANG.hang}</th>
                    <th>{LANG.ngay_het_han}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td data-column="{LANG.stt}" class="text-center"><div>{LINHVUC.stt}</div></td>
                    <td data-column="{LANG.so_chung_chi}" class="text-center"><div>{LINHVUC.so_chung_chi}</div></td>
                    <td data-column="{LANG.linh_vuc}"><div>{LINHVUC.linh_vuc}</div></td>
                    <td data-column="{LANG.linh_vuc_mo_rong}"><div>{LINHVUC.linh_vuc_mo_rong_cap_1}<br/><i>{LINHVUC.linh_vuc_mo_rong_cap_2}</i></div></td>
                    <td data-column="{LANG.hang}" class="text-center"><div>{LINHVUC.hang}</div></td>
                    <td data-column="{LANG.ngay_het_han}" class="text-center"><div>{LINHVUC.ngay_het_han}</div></td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
</div>
</div>
<!-- BEGIN: contractor -->
<div>
    {IS_CONTRACTOR}
    {CONTRACTOR_INFO}
    {CONTRACTOR_LOCK}
</div>
<!-- END: contractor -->
<!-- BEGIN: solicitor -->
<div>
    {IS_SOCLICTOR}
</div>
<!-- END: solicitor -->
<script type="text/javascript">
    function show_capcha() {
        $("#captchaModal").modal();
    }
    function confirm_crawl_detail() {
        $.ajax({
            url: window.location.href,
            type: "POST",
            dataType: "json",
            data: {
                'confirm_crawl': 1
            },
            success: function(a) {
                modalShow("", a.mess);
                $("#accept_crawl").click(function() {
                    text = $(this).attr('data-text');
                    $("#accept_crawl").prop("disabled", true).html('<i class="fa fa-spinner fa-spin"></i> ' + text);
                    setTimeout(function() {
                        click_update_org();
                    }, 800)
                });
            }
        });
    }
    function click_update_org() {
        $.ajax({
            type : "POST",
            url : "{URL_UPDATE}",
            data : {
                check : '{CHECKSESS_UPDATE}',
                update : '1'
            },
            success : function(res) {
                alert(res);
                window.location.href = window.location.href;
            }
        });
    }
    $(function() {
        $(".link_change_a:first").hide();
        update_id_checkbox();
        $('[data-toggle="tooltip"]').tooltip();

        $(".chk_notification").change(function(){
            var numberOfChecked = $('.chk_notification:checkbox:checked').length;

            if (numberOfChecked >= 2) {
                $('.chk_notification:checkbox').not(':checked').prop("disabled", true);
            } else {
                $('.chk_notification:checkbox').not(':checked').prop("disabled", false);
            }
            update_id_checkbox();
        });

        var title = $("#view_change_tb").parent().attr("data-original-title");
        function update_id_checkbox() {
            var $boxes = $('.chk_notification[type=checkbox]:checked');
            arrid = [];
            $boxes.each(function(v, k){
               arrid.push(k.value);
            });

            $("#view_change_tb").val(arrid.join("-"));
            if ($boxes.length < 2) {
                $("#view_change_tb1").removeClass("damdam");
                $("#view_change_tb1").addClass("momo");
                $("#view_change_tb").parent().attr("data-original-title", title);
                $("#view_change_tb1").show();
                $("#view_change_tb").hide();
            } else {
                $("#view_change_tb1").removeClass("momo");
                $("#view_change_tb1").addClass("damdam");
                $("#view_change_tb").parent().attr("data-original-title", "");
                $("#view_change_tb1").hide();
                $("#view_change_tb").show();
            }
        }

        $("#view_change_tb, .link_change_a a").click(function() {
            id = $(this).attr('data-id');
            if (id === undefined) {
                id = $(this).val();
            }

            // Ẩn chế độ 1 cột trên mobile
            if (window.matchMedia("(max-width: 768px)").matches) {
                $("input[name='sosanhtb'][value='1']").closest('label').hide();
                $("input[name='sosanhtb'][value='2']").click();
            } else {
                $("input[name='sosanhtb'][value='1']").closest('label').show();
                $("input[name='sosanhtb'][value='1']").click();
            }

            $.ajax({
                url: location.href,
                type: 'POST',
                data: {
                    'action': 'view_change_tb',
                    'id' : id
                },
                success: function(data) {
                    compare_highlight_difference("outputOriginal", "output", "outputNew", false, JSON.stringify(data['data']['thongbao']), JSON.stringify(data['data']['thongbao1']));
                    tr_0 = $(".table__tb").eq(0).find('tr');
                    tr_1 = $(".table__tb").eq(1).find('tr');
                    for (i = 0; i < tr_0.length; i++) {
                        tr_0.eq(i).attr('data-row', i+1);
                    }

                    for (i = 0; i < tr_1.length; i++) {
                        tr_1.eq(i).attr('data-row', i+1);
                    }

                    bid_0 = $("#outputOriginal").find('.bidding-detail');
                    for (i = 0; i < bid_0.length; i++) {
                        bid_0.eq(i).attr('data-row', i+1);
                    }

                    bid_1 = $("#output").find('.bidding-detail');
                    for (i = 0; i < bid_1.length; i++) {
                        bid_1.eq(i).attr('data-row', i+1);
                    }

                    $(".change_by_line").click(function() {
                        view_change_line();
                    });

                    change_by_line = $(".change_by_line:checked").val();
                    if (change_by_line == '3') {
                        view_change_line();
                    }
                }
            })
        });

        function view_change_line() {
            arr_hide = [];
            arr_hide_gt = [];
            table = $(".change_by_line").closest('#main_change_notificaion').find('#output').find('table');
            del = table.find('tr').find('td');
            for (i = 0; i < del.length; i++) {
                row = del.eq(i).find('del').length;
                if (!row) {
                    row = del.eq(i).find('ins').length;
                }
                if (!row) {
                    data_row = del.eq(i).closest('tr').attr('data-row');
                    arr_hide.push(data_row);
                }
            }

            bidding_detail = $('#output').find(".bidding-detail");

            for (i = 0; i < bidding_detail.length; i++) {
                row = bidding_detail.eq(i).closest('.bidding-detail').find('ins').length;
                if (!row) {
                    row = bidding_detail.eq(i).closest('.bidding-detail').find('del').length;
                }
                if (!row) {
                    data_row = bidding_detail.eq(i).closest('.bidding-detail').attr('data-row');
                    arr_hide_gt.push(data_row);
                }
            }

            if($(".change_by_line").is(':checked')) {
                for (i = 0; i < arr_hide.length; i++) {
                    tr = $(".table__tb").find('tr[data-row="' + arr_hide[i] +'"]');
                    tr.fadeOut(0);
                }

                for (i = 0; i < arr_hide_gt.length; i++) {
                    bid_detail = $(".row_compare").find('.bidding-detail[data-row="' + arr_hide_gt[i] +'"]');
                    bid_detail.fadeOut(0);
                }
            } else {
                for (i = 0; i < arr_hide.length; i++) {
                    tr = $(".table__tb").find('tr[data-row="' + arr_hide[i] +'"]');
                    tr.fadeIn(0);
                }

                for (i = 0; i < arr_hide_gt.length; i++) {
                    bid_detail = $(".row_compare").find('.bidding-detail[data-row="' + arr_hide_gt[i] +'"]');
                    bid_detail.fadeIn(0);
                }
            }
        }

        $("input[name='sosanhtb']").change(function() {
            if ($(this).val() == 2) {
                $("#outputOriginal").parent().hide(500);
                $("#output").parent().show(500);
            } else if ($(this).val() == 3) {
                $("#outputOriginal").parent().hide();
                $("#output").parent().hide();

            } else {
                $("#outputOriginal").parent().show(500);
                $("#output").parent().show(500);
            }
        });

    });
</script>
<!-- END: main -->
