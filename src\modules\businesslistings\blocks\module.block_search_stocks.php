<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2014 VINADES., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 3/9/2010 23:25
 */

if (!defined('NV_MAINFILE'))
    die('Stop!!!');

if (!nv_function_exists('list_stocks')) {
    /**
     * list_industry()
     *
     * @param mixed $block_config
     * @return
     */
    function list_stocks($block_config)
    {
        global $db, $nv_Cache, $nv_Request, $global_config, $module_info, $module_name, $module_file, $nv_Request, $nv_Lang, $global_array_vip, $user_info, $site_mods, $array_op;
        $module = $block_config['module'];
        $cache_file = $module . '-block-static-stocks' . NV_CACHE_PREFIX . '.cache';
        $q = $nv_Request->get_title('q', 'post,get');
        $location = $nv_Request->get_int('province', 'post,get', -1);
        $exchange = $nv_Request->get_int('exchange', 'post,get', -1);
        $industry = $nv_Request->get_title('industry', 'post,get', -1);
        $is_advance = $nv_Request->get_int('is_advance', 'get', 0);
        $order = $nv_Request->get_int('order', 'post,get', 0);
        $bid_year_search = $nv_Request->get_int('bid_year_search', 'post,get', 0);
        $bid_from = nv_substr($nv_Request->get_title('sfrom', 'get', '01/2000'), 0, 7);
        $bid_to = nv_substr($nv_Request->get_title('sto', 'get', nv_date('m/Y', NV_CURRENTTIME)), 0, 7);
        if (file_exists(NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file . "/block_search_stocks.tpl")) {
            $block_theme = $module_info['template'];
        } else {
            $block_theme = "default";
        }

        $list_industry = $nv_Cache->db('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_industry WHERE status = 1', 'code', $module_name);
        $is_vip_32 = false;
        if (defined('NV_IS_USER')) {
            if (!empty($global_array_vip[32]) || defined('NV_IS_ADMIN')) {
                $is_vip_32 = true;
            } else {
                $check_32 = $db->query("SELECT COUNT(*) FROM " . BID_PREFIX_GLOBAL . '_customs WHERE user_id = ' . $user_info['userid'] . ' AND vip = 32 AND prefix_lang = ' . (NV_LANG_DATA == 'vi' ? 0 : 1))->fetchColumn();
            }  
        }
        !$is_vip_32 && $bid_year_search = 0;

        $current_i_lev = $list_industry[$industry]['lev'] ?? 0;
        $arr_par_i[$current_i_lev] = $list_industry[$industry];
        $cur_code = $industry;
        for ($i = $current_i_lev - 1; $i >= 0; $i--) {
            foreach ($list_industry as $c_i => $v_i) {
                if ($v_i['id'] == $list_industry[$cur_code]['parentid']) {
                    $arr_par_i[$i] = $v_i;
                    $cur_code = $c_i;
                    break;
                }
            }
        }

        $array_data['exchange'] = [
            ['id' => 0, 'title' => 'HOSE'],
            ['id' => 1, 'title' => 'HNX'],
            ['id' => 2, 'title' => 'UPCOM'],
        ];
        $array_order = [
            0 => $nv_Lang->getModule('stocks_code'),
            1 => $nv_Lang->getModule('goithau_join'),
            2 => $nv_Lang->getModule('goithau_trung'),
            3 => $nv_Lang->getModule('goithau_tonggiatrijoin'),
            4 => $nv_Lang->getModule('goithau_tonggiatritrung_inde'),
            5 => $nv_Lang->getModule('goithau_tonggiatritrung')
        ];
        if (!empty($array_op[2])) {
            if ($array_op[1] == $nv_Lang->getModule('linkprovince')) {
                if ($array_op[2] == 'Chua-phan-loai' || $array_op[2] == 'unknown') {
                    $location = -1;
                } else if ($array_op[2] == change_alias($nv_Lang->getModule('alias_vn_out_territory'))) {
                    $location = 825;
                } else if ($array_op[2] == change_alias($nv_Lang->getModule('alias_nationwide'))) {
                    $location = 824;
                } else {
                    $_temp = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_location_province WHERE alias = ' . $db->quote($array_op[2]))
                        ->fetchColumn();
                    if (!empty($_temp)) {
                        $location = $_temp;
                    }
                }
            } 
        }
        if (($cache = $nv_Cache->getItem($module, $cache_file, 3600)) != false) {
            $cache_data = unserialize($cache);
            foreach ($cache_data as $key => $val) {
                $$key = $val;
            }
        } else {
            $count_all = 0;
            $count = $db->query('SELECT COUNT(*) as c, exchange FROM ' . BUSINESS_PREFIX_GLOBAL . '_stocks WHERE is_contractor = 1 GROUP BY exchange')->fetchAll();
            foreach ($count as $c) {
                $exchange_count[$c['exchange']] = $c['c'];
                $count_all += $c['c'];
            }
            $count_all = nv_number_format($count_all);
            $exchange_count = array_map('nv_number_format', $exchange_count);
            $cache_data = serialize([
                'count_all' => $count_all,
                'exchange_count' => $exchange_count
            ]);
            $nv_Cache->setItem($module, $cache_file, $cache_data);
        }
        $static = $nv_Lang->getModule('block_static_stocks', $count_all, ...$exchange_count);

        $xtpl = new XTemplate("block_search_stocks.tpl", NV_ROOTDIR . "/themes/" . $block_theme . "/modules/" . $module_file);
        $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
        $xtpl->assign('STATIC', $static);
        if ($global_config['rewrite_enable']) {
            $is_provinceBuss = 0;
            $xtpl->assign('ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=stocks', true));
            if (!empty($array_op[1])) {
                $is_provinceBuss = 1;
                if ($array_op[1] == $nv_Lang->getModule('linkprovince')) {
                    $xtpl->assign('ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=stocks' . '/' . $nv_Lang->getModule('linkprovince') . '/' . $array_op[2], true));
                }
            }
            $xtpl->assign('FORM_ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=stocks', true));
            $xtpl->assign('FORM_ACTION16', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=stocks' . '/' . $nv_Lang->getModule('linkprovince'), true));
        } else {
            $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php');
            $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
            $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
            $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
            $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
            $xtpl->assign('MODULE_NAME', $module_name);
            $xtpl->parse('main.no_rewrite');
        }

        foreach ($array_data['exchange'] as $e) {
            $xtpl->assign('EXCHANGE', $e);
            $xtpl->assign('selected_exchange', ($e['id'] == $exchange) ? 'selected' : '');
            $xtpl->parse('main.exchange');
        }

        // Đổ dữ liệu ngành hàng
        foreach ($list_industry as $v) {
            if ($v['lev'] > 0) {
                continue;
            }
            $xtpl->assign('VALUE', $v);
            $xtpl->assign('sl_industry', (in_array($v['code'], array_column($arr_par_i, 'code'))) ? 'selected="selected"' : '');
            $xtpl->parse('main.industry');
        }
        for ($i = 1; $i <= 3; $i++) {
            $xtpl->assign('LVL', $i);
            $xtpl->assign('N_LVL', $i + 1);
            if (isset($arr_par_i[$i - 1])) {
                foreach ($list_industry as $v) {
                    if ($v['parentid'] != $arr_par_i[$i - 1]['id']) {
                        continue;
                    }
                    $xtpl->assign('VALUE', $v);
                    $xtpl->assign('sl_industry', (in_array($v['code'], array_column($arr_par_i, 'code'))) ? 'selected="selected"' : '');
                    $xtpl->parse('main.child_industry.child_sel.industry');
                }
                $xtpl->parse('main.child_industry.child_sel');
            } else {
                $xtpl->parse('main.child_industry.hidden');
            }
            $xtpl->parse('main.child_industry');
        }
        foreach ($array_order as $key => $value) {
            $xtpl->assign('ORDER', [
                'value' => $key,
                'title' => $value,
                'selected' => $key == $order ? ' selected="selected"' : '',
            ]);
    
            $xtpl->parse('main.order');
        }

        $advance_show = !empty($is_advance);
        if (!$advance_show) {
            $xtpl->parse('main.advance_bl_hide');
        }

        if ($advance_show) {
            $xtpl->assign('LANG_ADVANCE', $nv_Lang->getModule('search_simple'));
            $xtpl->assign('ADVANCE', 1);
            $xtpl->parse('main.advance_icon_1');
        } else {
            $xtpl->assign('LANG_ADVANCE', $nv_Lang->getModule('search_advance'));
            $xtpl->assign('ADVANCE', 0);
            $xtpl->parse('main.advance_icon_0');
        }

        $xtpl->assign('provinceBuss', $is_provinceBuss);
        $xtpl->assign('Q', $q);
        $xtpl->assign('ADVANCE', $is_advance);
        $xtpl->assign('SEARCH_PROVINCE', $location);
        $xtpl->assign('TIMESTAMP', $global_config['timestamp']);
        $xtpl->assign('SFROM', $bid_from);
        $xtpl->assign('STO', $bid_to);
        $xtpl->assign('BID_YEAR_DISABLED', $bid_year_search ? '' : ' disabled');
        $xtpl->assign('BID_YEAR_CHECKED', $bid_year_search ? ' checked' : '');
        $xtpl->assign('SFROM_DEFAULT', '01/2000');
        $xtpl->assign('STO_DEFAULT', date('m/Y', NV_CURRENTTIME));
        $xtpl->assign('INDUSTRY', $industry);
        
        if (defined('NV_IS_USER')) {
            if (!$is_vip_32) {
                if ($check_32) {
                    $xtpl->assign('PLP_TOOLTIP', $nv_Lang->getModule('plp_report_renewvip_feature') . ' ' . $nv_Lang->getModule('click_to_action'));
                    $xtpl->assign('LINK_VIP_32', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=vip&amp;renewal=1&amp;vip=32');
                } else {
                    $xtpl->assign('PLP_TOOLTIP', $nv_Lang->getModule('plp_report_notvip_feature') . ' ' . $nv_Lang->getModule('click_to_action'));
                    $xtpl->assign('LINK_VIP_32', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=vip&amp;form=1&amp;vip=32');
                }
                $xtpl->assign('BID_YEAR_USABLE', ' disabled');
                $xtpl->parse('main.bid_year_tooltip');
                $xtpl->assign('SORT_USABLE', ' disabled');
                $xtpl->parse('main.sort_tooltip');
            }
        } else {
            $xtpl->assign('PLP_TOOLTIP', $nv_Lang->getModule('login_require') . ' ' . $nv_Lang->getModule('click_to_action'));
            $xtpl->assign('LINK_VIP_32', 'javascript:void(0)');
            $xtpl->assign('BID_YEAR_USABLE', ' disabled');
            $xtpl->parse('main.bid_year_tooltip.login_form_show');
            $xtpl->parse('main.bid_year_tooltip');
            $xtpl->assign('SORT_USABLE', ' disabled');
            $xtpl->parse('main.sort_tooltip.login_form_show');
            $xtpl->parse('main.sort_tooltip');
        }

        $xtpl->parse('main');
        return $xtpl->text('main');
    }
}

if (defined('NV_SYSTEM')) {
    $content = list_stocks($block_config);
}
