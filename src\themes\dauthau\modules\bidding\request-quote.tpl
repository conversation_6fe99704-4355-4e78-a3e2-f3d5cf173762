<!-- BEGIN: main -->
<div class="border-bidding" id="bodycontent">
    <h2 class="title__tab_heading"><span>{LANG.ycbg}</span></h2>
</div>
<!-- BEGIN: empty -->
<div class="alert alert-warning">{LANG.empty_result}</div>
<!-- END: empty -->
 <!-- BEGIN: add_filter -->
<div class="alert alert-success">{LINK_ADD}</div>
<!-- END: add_filter -->

<!-- BEGIN: error -->
<p class="alert alert-danger">{ERROR}</p>
<!-- END: error -->
<!-- BEGIN: view -->
<table class="bidding-table">
    <thead>
        <tr>
            <th>{LANG.ycbg}</th>
            <th>{LANG.investor_ycbg}</th>
            <th>{LANG.public_date}</th>
            <th>{LANG.reception_date_to}</th>
        </tr>
    </thead>
    <tbody>
        <!-- BEGIN: loop -->
        <tr>
            <td class="order-header" data-column="{LANG.ycbg}">
                <div class="wrap__text">
                    <a href="{VIEW.link}" title="{VIEW.request_quote_name}"><span class="bidding-code">{VIEW.request_quote_code}</span> {VIEW.request_quote_name}</a>
                </div>
            </td>
            <td data-column="{LANG.investor}">
                <div>
                    <!-- BEGIN: link_investor -->
                        <a title="{VIEW.investor_name}" href="{VIEW.link_investor}">
                            <!-- BEGIN: investor_code -->
                            <span class="solicitor-code">{VIEW.investor_code}</span> <!-- END: investor_code -->{VIEW.investor_name}
                        </a>
                    <!-- END: link_investor -->
                    <!-- BEGIN: no_link_investor -->
                    <!-- BEGIN: investor_code -->
                    <span class="solicitor-code">{VIEW.investor_code}</span>
                    <!-- END: investor_code -->
                    {VIEW.investor_name}
                    <!-- END: no_link_investor -->
                </div>
            </td>
            <td class="txt-center" data-column="{LANG.public_date}">
                <div>
                    {VIEW.public_date}
                </div>
            </td>
            <td class="txt-center" data-column="{LANG.reception_date_to}">
                <div>{VIEW.reception_date_to}</div>
                <!-- BEGIN: time_left -->
                <span class="label label-primary" title="{LANG.ycbg_time_left}">{LANG.ycbg_time_left}</span>
                <!-- END: time_left -->
                <!-- BEGIN: time_expired -->
                <span class="label label-warning" title="{LANG.ycbg_time_expired}">{LANG.ycbg_time_expired}</span>
                <!-- END: time_expired -->
                <!-- BEGIN: canceled -->
                <span class="label label-danger" title="{LANG.ycbg_canceled}">{LANG.ycbg_canceled}</span>
                <!-- END: canceled -->
            </td>
            
        </tr>
        <!-- END: loop -->
    </tbody>
</table>
<!-- BEGIN: generate_page -->
<div class="text-center">{NV_GENERATE_PAGE}</div>
<!-- END: generate_page -->
<!-- END: view -->
<script type="text/javascript">
    
</script>
<!-- END: main -->
