<?php

/**
 * @Project dauthau.asia
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_BIDDING'))
    die('Stop!!!');

use NukeViet\Dauthau\Url;

$where = ' (public_date >= ' . $sfrom1 . ' AND public_date <= ' . $sto1 . ')';

$search_fields = [];

if ($par_search) {
    $search_fields = [
        'request_quote_code',
        'request_quote_name_insen'
    ];
} else {
    $search_fields = [
        'content'
    ];
}

// Tìm kiếm hàng hóa/dịch vụ
if ($rq_form_value == 1 and $is_advance) {
    $search_fields[] = 'rq_category';
} elseif ($rq_form_value == 2 and $is_advance) {
    $search_fields = [
        'rq_category'
    ];
}

if (!empty($q)) {
    $arr_key = explode(',', $q);
    $search_q = [];
    foreach ($search_fields as $f) {
        foreach ($arr_key as $key) {
            if ($search_kind >= 1) { // 1. Khớp từ hoặc một số từ 2. Khớp tất cả từ
                $arr_key_temp = explode(' ', $key);
                $tmp_search_elastic = [];
                foreach ($arr_key_temp as $_key_temp) {
                    $_key_temp = str_replace('-', ' ', change_alias($_key_temp));
                    $_key_temp = trim($_key_temp);
                    if (empty($_key_temp)) {
                        continue;
                    }
                    if (!preg_match('/[0-9]/', $_key_temp) && $f == 'request_quote_code') {
                        continue;
                    }

                    if ($search_kind == 1) {
                        $search_q[] = $f . ' LIKE ' . $db->quote('%' . $_key_temp . '%');
                    } else {
                        $tmp_search_elastic[] = $f . ' LIKE ' . $db->quote('%' . $_key_temp . '%');
                    }
                }
                if (!empty($tmp_search_elastic)) {
                    $search_q[] = '(' . implode(' AND ', $tmp_search_elastic) . ')';
                }
            } else { // 0.Khớp chính xác cụm từ
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                if (empty($key)) {
                    continue;
                }
                if (!preg_match('/[0-9]/', $key) && $f == 'request_quote_code') {
                    continue;
                }
                $search_q[] = $f . ' LIKE ' . $db->quote('%' . $key . '%');
            }
        }
    }
    if (!empty($search_q)) {
        $where .= ' AND (' . implode(' OR ', $search_q) . ')';
    }
}

if (!empty($key_search2) and $is_advance) {
    // Tìm theo Từ khóa bổ sung
    $arr_key = explode(',', $key_search2);
    if (sizeof($search_fields) > 1) {
        // Nếu số lượng trường tìm kiếm > 1 thì sẽ dùng bool must từng từ khóa
        // mỗi từ khóa thì lại should trong từng trường
        $search_bosung = [];
        foreach ($arr_key as $key) {
            $key = str_replace('-', ' ', change_alias($key));
            $key = trim($key);
            if (empty($key)) {
                continue;
            }
            foreach ($search_fields as $f) {
                if (!preg_match('/[0-9]/', $key) && $f == 'request_quote_code') {
                    continue;
                }
                $search_bosung[] = " " . $f . " COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . "";
            }
        }
        if (!empty($search_bosung)) {
            $where .= ' AND (' . implode(' OR ', $search_bosung) . ')';
        }
    } else {
        foreach ($search_fields as $f) {
            foreach ($arr_key as $key) {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                if (empty($key)) {
                    continue;
                }
                if (!preg_match('/[0-9]/', $key) && $f == 'request_quote_code') {
                    continue;
                }
                $where .= " AND " . $f . " LIKE " . $db->quote('%' . $key . '%');
            }
        }
    }
}

// Tìm theo từ khóa loại trừ
if (!empty($without_key) and $is_advance) {
    $arr_key = explode(',', $without_key);
    foreach ($search_fields as $f) {
        foreach ($arr_key as $key) {
            $key = str_replace('-', ' ', change_alias($key));
            $key = trim($key);
            if (empty($key)) {
                continue;
            }
            if (!preg_match('/[0-9]/', $key) && $f == 'request_quote_code') {
                continue;
            }
            $where .= " AND " . $f . " COLLATE utf8mb4_unicode_ci NOT LIKE " . $db->quote('%' . $key . '%') . '';
        }
    }
}

// Tìm theo chủ đầu tư
$search_investor = [];
if (!empty($rq_investor) and $is_advance) {
    foreach ($rq_investor as $key) {
        $arr_key_temp = explode(' ', $key);
        $check_search_code = count($arr_key_temp) == 1 ? true : false;
        if ($search_kind > 0) { // 1. Khớp từ hoặc một số từ 2. Khớp tất cả từ
            $tmp_search_elastic = [];
            foreach ($arr_key_temp as $_key_temp) {
                $_key_temp = str_replace('-', ' ', change_alias($_key_temp));
                $_key_temp = trim($_key_temp);
                $name_field = 'investor_name_insen';
                if (empty($_key_temp)) {
                    continue;
                }

                if ($search_kind == 1) {// 1. Khớp từ hoặc một số từ
                    $search_investor[] = $name_field . ' LIKE ' . $db->quote('%' . $_key_temp . '%');

                } else { // 2. Khớp tất cả từ
                    $tmp_search_elastic[] = $name_field . ' LIKE ' . $db->quote('%' . $_key_temp . '%');
                }
                // tìm theo mã chủ đầu tư
                if ($check_search_code) {
                    $search_investor[] = 'investor_code = ' . $db->quote($_key_temp);
                }
            }
            if (!empty($tmp_search_elastic)) {
                $search_investor[] = '(' . implode(' AND ', $tmp_search_elastic) . ')';
            }
        } else { // 0.Khớp chính xác cụm từ
            $key = str_replace('-', ' ', change_alias($key));
            $key = trim($key);
            $name_field = 'investor_name_insen';
            if (empty($key)) {
                continue;
            }

            $search_investor[] = $name_field . ' LIKE ' . $db->quote('%' . $key . '%');
            // tìm theo mã chủ đầu tư
            if ($check_search_code) {
                $search_investor[] = 'investor_code = ' . $db->quote($key);
            }
        }
    }
    if (!empty($search_investor)) {
        $where .= ' AND (' . implode(' OR ', $search_investor) . ')';
    }
}
// Tìm kiếm theo thành phố, quận, phường
if (!empty($rq_province) and $is_advance) {
    $where .= ' AND FIND_IN_SET (' . $rq_province . ', id_province)';
}

if (!empty($rq_district) and $is_advance) {
    $where .= ' AND FIND_IN_SET (' . $rq_district . ', id_district)';
}

if (!empty($rq_ward) and $is_advance) {
    $where .= ' AND FIND_IN_SET (' . $rq_ward . ', id_ward)';
}
$order = 'public_date DESC';

$db->sqlreset()
    ->select('COUNT(*)')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_ycbg')
    ->where($where);
$sth = $db->prepare($db->sql());

$sth->execute();
$num_items = $sth->fetchColumn();
$db->select('id, request_quote_name, request_quote_code, investor_name, investor_code, public_date, reception_date_to, status, alias')
    ->order($order)
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());
$sth->execute();

while ($view = $sth->fetch()) {
    $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['request-quote'] . '/' . $view['alias'] . '-' . $view['request_quote_code'] . $global_config['rewrite_exturl'];
    $view['link_investor'] = '';
    $view['investor_id'] = 0;
    $view['investor_title'] = $view['investor_name'];
    $array_data[$view['request_quote_code']] = $view;
}
