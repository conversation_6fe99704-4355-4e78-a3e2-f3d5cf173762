<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_SYSTEM')) {
    die('Stop!!!');
}

require NV_ROOTDIR . '/modules/businesslistings' . '/global.functions.php';

global $arr_unset;
$arr_unset = [
    "type_bid_id",
    "name_search",
    "last_crawl",
    "content_full",
    "elasticsearch",
    "exported_sitemap",
    "log_change",
    "name_staff",
    "content",
    "url",
    "upcount",
    "totalview",
    "update_leads",
    "array_profile",
    "log_change",
    "data_process",
    "alias",
    "id",
    "totalview",
    "update_data",
    "type_url",
    "plan_id_msc",
    "catid",
    "ho_so_point",
    "msc_typeid",
    "is_vip5",
    "sourceurl",
    "time_hide_end",
    "hide_vip3",
    "update_leads",
    "array_profile",
    "userid",
    "gmaps",
    "url_run",
    "time_url",
    "date_crawl",
    "last_crawl",
    "gettime",
    "get_time",
    "fgettime",
    "url",
    "quyet_dinh_point",
    "vip_del_time",
    "updatebussiness",
    "ho_so_del_time",
    "point",
    "phone_search"
];
// kiểm tra loại tài khoản tại đây luôn
// 0, 1: thành viên, khách ẩn toàn bộ thông tin
// 2: view: xem dc các tin cũ
// 3, vip :k ẩn
use NukeViet\Point\Point;
use NukeViet\Dauthau\Share;

if (defined('NV_IS_USER')) {
    $type_user = 1;

    if (!empty($global_array_vip)) {
        foreach ($global_array_vip as $key => $value) {
            if ($key != 99 && $key != 77) {
                $type_user = 3;
                break;
            } else {
                $type_user = 2;
            }
        }
    }
    // kiểm tra có các tk dc phân quyền hay không
    if (!empty($arr_customs_permission_view_detail)) {
        $type_user = 4;
    }
} else {
    $type_user = 0;
}
$arr_op = [
    'detail',
    'main',
    'search'
];
if ((NV_CURRENTTIME >= $close_time_dauthau and NV_CURRENTTIME <= $open_maunal_time_dauthau and ($type_user == 0 or $type_user == 1)) and !defined('NV_IS_AJAX') and in_array($op, $arr_op) and !$client_info['is_bot']) {
    $contents = "<div class=\"alert alert-warning\">Website hiện tại đang trong quá trình nâng cấp để chuyển đổi từ bóc dữ liệu tự động sang đăng dữ liệu thủ công. Quá trình nâng cấp này có thể mất vài ngày. Mong quý khách thông cảm.</div>";
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

$other_lang = NV_LANG_DATA == 'en' ? 'vi' : 'en';
$other_lang_prefix = $db_config['prefix'] . '_' . $other_lang;
$other_lang_module_info = nv_site_mods($other_lang)[$module_name];
$arr_title_province = [
    815,//Tp cần thơ
    501,//đà nẵng
    101,//hà nội
    103,//hải phòng
    701//HCM
];
// LẤY PROVINCE
$sql = "SELECT id, title, alias, region_id FROM " . NV_PREFIXLANG . "_location_province ORDER BY id ASC";
$province_list = $nv_Cache->db($sql, 'id', 'location_bidding');
$province_list[0] = array(
    'id' => 0,
    'title' => $nv_Lang->getModule('no_title'),
    'alias' => 'Chua-phan-loai',
    'region_id' => 0
);

$array_round_format = NV_LANG_DATA == 'vi' ? [',', '.'] : ['.', ','];

// Data fix cứng của site dauthau.info
$array_lvkd = [
    1 => $nv_Lang->getModule('array_lvkd_1'),
    2 => $nv_Lang->getModule('array_lvkd_2'),
    3 => $nv_Lang->getModule('array_lvkd_3'),
    4 => $nv_Lang->getModule('array_lvkd_4'),
    5 => $nv_Lang->getModule('array_lvkd_5')
];

/**
 * Mảng các tùy chọn sắp xếp doanh nghiệp
 * - default: Sắp xếp mặc định theo ID
 * - num_total_desc: Sắp xếp theo tổng số gói thầu
 * - num_result_desc: Sắp xếp theo số lượng trúng thầu
 * - num_false_desc: Sắp xếp theo số lượng trượt thầu
 * - ability_point_desc: Sắp xếp theo điểm năng lực
 * - total_revenue': Sắp xếp theo doanh số nhà thầu
 * - total_by_role: Sắp xếp theo vai trò 
 * - total_by_type: Sắp xếp theo hình thức
 */
$array_sort_options = [
    'default',
    'num_total_desc',
    'num_result_desc', 
    'num_false_desc',
    'ability_point_desc',
    'total_revenue',
    'total_by_role',
    'total_by_type',
    'independent_contractor',
    'joint_contractor',
    'direct_contractor',
    'other_contractor'
];

$config_bidding = $module_config['bidding'];
$elas_max = 1000; // Số lượng điều kiện nhiều nhất trong 1 query elasticsearch
                  // Xác định cấu hình module
$global_array_config = array();

// Các thiết lập mặc định
$global_array_config['show_list_type'] = 0;
$global_array_config['show_list_number'] = 10;
$global_array_config['show_detail_type'] = 0;

$aray_category = array(
    'PT' => $nv_Lang->getModule('array_type_violate_pt'),
    'CD' => $nv_Lang->getModule('array_type_violate_cd'),
    'CC' => $nv_Lang->getModule('array_type_violate_cc'),
    'CT' => $nv_Lang->getModule('array_type_violate_ct'),
    'OTHER' => $nv_Lang->getModule('array_type_violate_other'),
    'CPPP' => $nv_Lang->getModule('array_type_violate_cppp'),
    'CD,MSG0018' => $nv_Lang->getModule('array_type_violate_cdmsg')
);

$sql = "SELECT * FROM " . BUSINESS_PREFIX_GLOBAL . "_config";
$list = $nv_Cache->db($sql, 'name', $module_name);
foreach ($list as $row) {
    if (preg_match('/^arr\_(dis|req)\_(ad|ur)\_([a-zA-Z0-9\_\-]+)$/', $row['name'], $m)) {
        if (!isset($global_array_config[$m[1]])) {
            $global_array_config[$m[1]] = array();
        }
        if (!isset($global_array_config[$m[1]][$m[2]])) {
            $global_array_config[$m[1]][$m[2]] = array();
        }
        $global_array_config[$m[1]][$m[2]][$m[3]] = $row['value'];
    } elseif (preg_match('/^(coefficient)\_([a-p])$/', $row['name'], $m)) {
        $global_array_config[$m[1]][$m[2]] = $row['value'];
    } else {
        $global_array_config[$row['name']] = $row['value'];
    }
}

/*
 * Lấy thông tin doanh nghiệp từ mã số thuế
 */
if ($nv_Request->isset_request('getprofdata', 'post')) {
    $request = $respon = [];
    $request['taxcode'] = $nv_Request->get_title('getprofdata', 'post', '');
    $request['t'] = $nv_Request->get_absint('t', 'post', 0);
    $request['ip'] = $nv_Request->get_title('ip', 'post', '');
    $request['checksum'] = $nv_Request->get_title('checksum', 'post', '');

    $respon['message'] = '';
    $respon['data'] = false;

    if (!taxcodecheck2($request['taxcode'])) {
        $respon['message'] = 'Invalid TaxCode!';
    } elseif ($request['checksum'] === sha1($request['ip'] . $request['t'] . $global_array_config['bidprof_checksum_key'])) {
        $respon['message'] = 'Success!';

        $sql = "SELECT * FROM " . BUSINESS_PREFIX_GLOBAL . "_info tb1
        LEFT JOIN " . BUSINESS_PREFIX_GLOBAL . "_addinfo tb2 ON tb1.id=tb2.id
        WHERE tb1.active=1 AND tb1.code=" . $db->quote($request['taxcode']);
        $result = $db->query($sql);
        $row = $result->fetch();
        if ($row) {
            /*
             * FIXME
             * MAP loại hình doanh nghiệp, fix cứng
             */
            $array_map_type = [
                3 => 1, // Doanh nghiệp Tư nhân
                4 => 2, // Công ty TNHH
                5 => 3, // Công ty Cổ phần
                6 => 4, // Công ty Hợp danh
                7 => 5, // Hợp tác xã
                8 => 6, // Công ty Liên doanh
                9 => 7 // Công ty 100% vốn nước ngoài
            ];
            $respon['data'] = [];

            // Loại hình doanh nghiệp
            if (isset($array_map_type[$row['businesstype']])) {
                $respon['data']['business_type'] = $array_map_type[$row['businesstype']];
            } else {
                $respon['data']['business_type'] = 0;
            }
            $respon['data']['prof_name'] = $row['companyname']; // Tên hồ sơ
            $respon['data']['prof_sortname'] = $row['short']; // Tên viết tắt
            $respon['data']['prof_enname'] = $row['officialname']; // Tên quốc tế
            $respon['data']['idprovince'] = $row['province']; // Tỉnh
            $respon['data']['iddistrict'] = $row['district']; // Huyện
            $respon['data']['idward'] = $row['ward']; // Xã
            $respon['data']['prof_address'] = $row['address']; // Địa chỉ theo đăng ký kinh doanh
            $respon['data']['info_capital'] = intval($row['von_dieu_le']);
            $respon['data']['info_phone'] = $row['phone'];
            $respon['data']['info_fax'] = $row['fax'];
            $respon['data']['info_email'] = $row['email'];
            $respon['data']['info_website'] = $row['website'];
            $respon['data']['established_time'] = $row['dateestablished'];
            $respon['data']['info_employees'] = $row['so_nhan_vien'];
            $respon['data']['represent_name'] = $row['representative'];
            $respon['data']['represent_address'] = $row['addressrepresentative'];
            $respon['data']['industry'] = false;

            // Lấy ngành nghề
            $row['industry4'] = array_filter(array_unique(array_map('trim', explode(',', $row['industry4']))));
            if (!empty($row['industry4'])) {
                $sql = "SELECT title FROM " . NV_PREFIXLANG . "_industry WHERE level=4 AND status=1 AND code IN('" . implode("','", $row['industry4']) . "')";
                $result = $db->query($sql);
                $industry = $result->fetchAll(PDO::FETCH_COLUMN);
                if (!empty($industry)) {
                    $respon['data']['industry'] = $industry;
                }
            }
        }
    } else {
        $respon['message'] = 'Wrong checksum!';
    }

    nv_jsonOutput($respon);
}

// Báo lỗi nếu module ngành nghề doanh nghiệp không có
if (!empty($global_array_config['module_industry']) and !isset($site_mods[$global_array_config['module_industry']]) and !defined('NV_MOD_LOAD')) {
    trigger_error('Error: No module ' . $global_array_config['module_industry'] . ', please setup before!!!', 256);
}

// Ngành nghề
$sql = 'SELECT t1.id, t1.code, t1.level, t1.alias as alias_vi, t1.title as title_vi, t2.alias as alias_en, t2.title as title_en FROM ' . $db_config['prefix'] . '_vi_industry t1 INNER JOIN ' . $db_config['prefix'] . '_en_industry t2 ON t1.code=t2.code';
$industry_list = $nv_Cache->db($sql, 'code', 'businesslistings');

// $sql = "SELECT t1.id, t1.level, t1.alias as alias_vi, t1.title as title_vi, t2.alias as alias_en, t2.title as title_en FROM " . $db_config['prefix'] . "_vi_industry t1 INNER JOIN " . $db_config['prefix'] . "_en_industry t2 ON t1.code=t2.code";
// $industry_list_multi_lang = $nv_Cache->db($sql, 'code', 'businesslistings');

// Lỗi nếu như chưa cài module location
if (!isset($site_mods['location']) and !defined('NV_MOD_LOAD')) {
    trigger_error('Error: No module location, please setup before!!!', 256);
}

// Danh sách loại doanh nghiệp
$sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_businesstype ORDER BY weight ASC";
$global_array_businesstype = $nv_Cache->db($sql, 'id', $module_name);

// Thông tin quy mô doanh nghiệp
$org_size_query = 'SELECT * FROM ' . BUSINESS_PREFIX_GLOBAL . '_org_size';
$arr_org_size = $nv_Cache->db($org_size_query, 'code', 'org_size');

$catid = 0;
$title_cat = "";

if (isset($array_op[1]) and $array_op[0] == "listbusinesstype") {
    $alias = $array_op[1];
    $sql = "SELECT id, title FROM " . NV_PREFIXLANG . "_" . $module_data . "_businesstype WHERE alias = " . $db->quote($alias);
    $result = $db->query($sql);
    if ($result->rowCount() == 1) {
        list ($catid, $title_cat) = $result->fetch(3);
        $op = 'listbusinesstype';
    } else {
        nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
    }
}

function nv_insert_business_info($code, $userid, $industry1, $industry2, $industry3, $industry4, $industry5, $province, $district, $ward, $address, $gmaps, $businesstype, $logo, $companyname, $officialname, $short, $phone, $fax, $email, $website, $dateestablished, $registrationtime, $representative, $addressrepresentative, $taxcode, $taxdate, $chartercapital, $about, $currentstatus, $active, $allowmail)
{
    global $db, $module_name, $module_data, $db_config;

    $query = "INSERT INTO " . BUSINESS_PREFIX_GLOBAL . "_info (id ,code ,userid ,industry1 ,industry2, industry3,industry4, industry5,province ,
        district ,ward ,address ,gmaps ,businesstype ,logo ,companyname ,officialname ,short ,phone ,fax ,
        email ,website ,dateestablished, registrationtime ,representative ,addressrepresentative, taxcode, taxdate,
        chartercapital ,about ,currentstatus, totalview, active, allowmail )
        VALUES (NULL," . $db->quote($code) . ", " . intval($userid) . ", " . $db->quote($industry1) . ", " . $db->quote($industry2) . "," . $db->quote($industry3) . "," . $db->quote($industry4) . "," . $db->quote($industry5) . ", " . intval($province) . ",
        " . intval($district) . ", " . intval($ward) . ", " . $db->quote($address) . ", " . $db->quote($gmaps) . ",
        " . intval($businesstype) . ", " . $db->quote($logo) . ", " . $db->quote($companyname) . ", " . $db->quote($officialname) . ",
        " . $db->quote($short) . ", " . $db->quote($phone) . ", " . $db->quote($fax) . ", " . $db->quote($email) . ",
        " . $db->quote($website) . ", " . intval($dateestablished) . "," . $db->quote($registrationtime) . ", " . $db->quote($representative) . ",
        " . $db->quote($addressrepresentative) . ", " . $db->quote($taxcode) . ", " . intval($taxdate) . ", " . doubleval($chartercapital) . ", " . $db->quote($about) . ", " . intval($currentstatus) . ", 0, " . intval($active) . ", " . intval($allowmail) . ")";

    if ($db->query($query)) {
        return "OK";
    } else {
        return "NOT";
    }
}

function nv_update_business_info($id, $industry1, $industry2, $industry3, $industry4, $industry5, $province, $district, $ward, $address, $gmaps, $businesstype, $logo, $companyname, $officialname, $short, $phone, $fax, $email, $website, $dateestablished, $registrationtime, $representative, $addressrepresentative, $taxcode, $taxdate, $chartercapital, $about, $currentstatus, $active, $allowmail)
{
    global $db, $module_name, $module_data, $db_config;

    $logid = $db->insert_id("INSERT INTO " . BUSINESS_PREFIX_GLOBAL . "_logs (id ,code ,userid ,industry1 ,industry2, industry3,industry4, industry5,province ,
        district ,ward ,address ,gmaps ,businesstype ,logo ,companyname ,officialname ,short ,phone ,fax ,
        email ,website ,dateestablished ,registrationtime ,representative ,addressrepresentative,taxcode,taxdate,
        chartercapital ,about ,currentstatus, totalview, active, allowmail ) SELECT id ,code ,userid ,industry1 ,industry2, industry3,industry4, industry5,province ,
        district ,ward ,address ,gmaps ,businesstype ,logo ,companyname ,officialname ,short ,phone ,fax ,
        email ,website ,dateestablished ,registrationtime ,representative ,addressrepresentative,taxcode,taxdate,
        chartercapital ,about ,currentstatus, totalview, active, allowmail FROM " . BUSINESS_PREFIX_GLOBAL . "_info WHERE id=" . $id);

    $query = "UPDATE " . BUSINESS_PREFIX_GLOBAL . "_info SET industry1 =  " . $db->quote($industry1) . ", industry2 =  " . $db->quote($industry2) . ", industry3 =  " . $db->quote($industry3) . ", industry4 =  " . $db->quote($industry4) . ", industry5 =  " . $db->quote($industry5) . ", province = " . intval($province) . ",
        district = " . intval($district) . ", ward = " . intval($ward) . ", address = " . $db->quote($address) . ", gmaps = " . $db->quote($gmaps) . ",
        businesstype = " . intval($businesstype) . ", logo = " . $db->quote($logo) . ", companyname = " . $db->quote($companyname) . ", officialname = " . $db->quote($officialname) . ",
        short = " . $db->quote($short) . ", phone = " . $db->quote($phone) . ", fax = " . $db->quote($fax) . ", email = " . $db->quote($email) . ",
        website = " . $db->quote($website) . ", dateestablished = " . intval($dateestablished) . ", registrationtime = " . $db->quote($registrationtime) . ", representative = " . $db->quote($representative) . ",
        addressrepresentative = " . $db->quote($addressrepresentative) . ", taxcode=" . $db->quote($taxcode) . ", taxdate=" . $taxdate . ",
        chartercapital = " . doubleval($chartercapital) . ", about = " . $db->quote($about) . ", currentstatus = " . intval($currentstatus) . ", active = " . intval($active) . ", allowmail= " . intval($allowmail) . " WHERE id = " . $id . "";
    if ($db->query($query)) {
        $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_logs SET edittime = " . intval(NV_CURRENTTIME) . " WHERE logid = " . $logid);
        return "OK";
    } else {
        return "NOT";
    }
}

function ConvertPriceText($price, $type)
{
    global $nv_Lang;

    $priceTy = floor($price / 1000000000);
    $priceTrieu = floor(($price - ($priceTy * 1000000000)) / 1000000);
    $priceNgan = floor((($price - ($priceTy * 1000000000)) - ($priceTrieu * 1000000)) / 1000);
    $priceDong = floor(((($price - ($priceTy * 1000000000)) - ($priceTrieu * 1000000)) - ($priceNgan * 1000)));
    $strTextPrice = "";

    if ($price == "" || $price == "0")
        $strTextPrice = "N/A";
    if ($priceTy > 0)
        $strTextPrice = $strTextPrice . "<strong>" . $priceTy . "</strong> " . $nv_Lang->getModule('ty');
    if ($priceTrieu > 0)
        $strTextPrice = $strTextPrice . " <strong>" . $priceTrieu . "</strong> " . $nv_Lang->getModule('trieu');
    if ($priceNgan > 0)
        $strTextPrice = $strTextPrice . " <strong>" . $priceNgan . "</strong>  " . $nv_Lang->getModule('nghin');
    if ($type == "VND") {
        if ($priceTy > 0 || $priceTrieu > 0 || $priceNgan > 0 || $priceDong > 0)
            $strTextPrice = $strTextPrice . " " . $type;
    }
    if ($type == "SJC") {
        if ($priceDong > 0)
            $strTextPrice = $strTextPrice + $priceDong;
        if ($priceTy > 0 || $priceTrieu > 0 || $priceNgan > 0 || $priceDong > 0)
            $strTextPrice = number_format($price, 0, ' ', ' ') . " " . $type;
    }
    if ($type == "USD") {
        if ($priceDong > 0)
            $strTextPrice = $strTextPrice + $priceDong;
        if ($priceTy > 0 || $priceTrieu > 0 || $priceNgan > 0 || $priceDong > 0)
            $strTextPrice = number_format($price, 0, ' ', ' ') . " " . $type;
    }
    return $strTextPrice;
}

/**
 * ConvertPriceTextSort()
 *
 * Chuyển đổi số sang text hiển thị
 * xx tỉ
 * xx triệu
 * nếu nhỏ hơn trả về nguyên số
 *
 * @param mixed $price
 * @param string $emptyString
 * @return
 */
function ConvertPriceTextSort($price, $emptyString = '-')
{
    global $nv_Lang, $array_round_format;

    if ($price <= 0) {
        return $emptyString;
    }
    $textNumber = '';
    $textNumberUnit = '';
    if ($price >= 1000000000) {
        $textNumber = number_format($price / 1000000000, 2, ...$array_round_format);
        $textNumberUnit = ' ' . $nv_Lang->getModule('ty');
    } elseif ($price >= 1000000) {
        $textNumber = number_format($price / 1000000, 2, ...$array_round_format);
        $textNumberUnit = ' ' . $nv_Lang->getModule('trieu');
    } else {
        $textNumber = number_format($price, 2, ...$array_round_format);
        $textNumberUnit = '';
    }
    $textNumber = rtrim($textNumber, '0');
    $textNumber = rtrim($textNumber, ',');
    return $textNumber . $textNumberUnit;
}

function convert_vnd($text)
{
    // Convert tiền tổng đầu tư sang dạng số
    $invest = strtolower(trim_space($text));

    if ($pos = strpos($invest, 'vnd')) {
        $invest = trim(substr($invest, 0, $pos));
    }
    if ($pos = strpos($invest, ',')) {
        $invest = trim(substr($invest, 0, $pos));
    }
    $invest = str_replace('.', '', $invest);
    // $invest = str_replace(',', '', $invest);
    $invest = floatval($invest);
    return $invest;
}

function array_msort(&$array, $key, $order)
{
    $sorter = array();
    $ret = array();
    reset($array);
    foreach ($array as $ii => $va) {
        $sorter[$ii] = $va[$key];
    }
    if ($order == 'asc') {
        asort($sorter);
    } else {
        arsort($sorter);
    }
    foreach ($sorter as $ii => $va) {
        $ret[$ii] = $array[$ii];
    }
    $array = $ret;
}

function get_log_crawls($id, $key)
{
    /*
     * Tham số key
     * BUSINESS: Nhà thầu
     */
    global $db, $arr_unset;
    $data_log_crawls = [];
    if (defined('NV_IS_ADMIN')) {
        $sth = $db->prepare("SELECT * FROM nv4_vi_logs_crawls_" . strtolower($key) . " WHERE obj_id = :obj_id ORDER BY log_time_new DESC LIMIT 30");
        $sth->bindParam(':obj_id', $id, PDO::PARAM_INT);
        $sth->execute();
        while ($row = $sth->fetch()) {
            $tmp_log = json_decode($row['log_data'], true);
            foreach ($arr_unset as $key => $value) {
                if (isset($tmp_log[$value])) {
                    unset($tmp_log[$value]);
                }
            }
            empty($tmp_log) && $tmp_log = ['new' => ['no_change' => 'Không có thay đổi']];
            if (isset($tmp_log['new']['no_change'])) {
                $no_change = $tmp_log['new']['no_change'];
                unset($tmp_log['old']);
                unset($tmp_log['new']);
                $tmp_log['log_time_new'] = nv_date('H:i:s d/m/Y', $row['log_time_new']);
                $tmp_log['log_time_old'] = nv_date('H:i:s d/m/Y', $row['log_time_old']);
                $tmp_log['no_change'] = $no_change;
                $data_log_crawls[] = $tmp_log;
            } else {
                if (!empty($tmp_log)) {
                    if ($row['log_time_old'] >= $row['log_time_new']) {
                        $row['log_time_old'] = $row['log_time_new'];
                    }
                    $tmp_log['log_time_new'] = nv_date('H:i d/m/Y', $row['log_time_new']);
                    $tmp_log['log_time_old'] = nv_date('H:i d/m/Y', $row['log_time_old']);
                    $data_log_crawls[] = $tmp_log;
                }
            }
        }
    }
    return $data_log_crawls;
}

function g_linhvuckinhdoanh($data)
{}

function f_industry($data)
{
    $data['old'] = !empty($data['old']) ? g_industry($data['old']) : '';
    $data['new'] = !empty($data['new']) ? g_industry($data['new']) : '';
    return $data;
}

function g_industry($string_industry)
{
    global $db, $industry_list;
    $_html_industry = "";
    $arr_code_industry = explode(',', $string_industry);
    foreach ($arr_code_industry as $code_industry) {
        if (isset($industry_list[$code_industry])) {
            $_html_industry .= "<p>" . $industry_list[$code_industry]['title_' . NV_LANG_DATA] . "</p>";
        }
    }
    return $_html_industry;
}

function f_date($data)
{
    $data['old'] = !empty($data['old']) ? nv_date('H:i d/m/Y', $data['old']) : '';
    $data['new'] = !empty($data['new']) ? nv_date('H:i d/m/Y', $data['new']) : '';
    return $data;
}

function f_location($data, $type)
{
    $_location_format = [];
    if (!empty($data)) {
        $_location_format['old'] = g_location($data['old'], $type);
        $_location_format['new'] = g_location($data['new'], $type);
    }
    return $_location_format;
}

function f_loaidoanhnghiep($data)
{
    global $global_array_businesstype;
    $data['old'] = (!empty($data['old']) and isset($global_array_businesstype[$data['old']]['title'])) ? $global_array_businesstype[$data['old']]['title'] : '';
    $data['new'] = (!empty($data['new']) and $global_array_businesstype[$data['new']]['title']) ? $global_array_businesstype[$data['new']]['title'] : '';
    return $data;
}

function g_location($id, $type)
{
    global $db;
    $table = $result = "";
    switch ($type) {
        case 'T':
            $table = "" . NV_PREFIXLANG . "_location_province";
            break;
        case 'H':
            $table = "" . NV_PREFIXLANG . "_location_district";
            break;
        case 'X':
            $table = "" . NV_PREFIXLANG . "_location_ward";
            break;
        default:
            return $table;
    }
    if (!empty($id) and $table) {
        $result = $db->query("SELECT title FROM " . $table . " WHERE id = " . $id)->fetch();
    }
    if (!empty($result)) {
        return $result['title'];
    }
    return $result;
}

function PopupLogin($mess, $page_url = '')
{
    global $module_info, $global_config, $module_name, $op, $nv_Lang;
    $xtpl = new XTemplate('popup_login.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/businesslistings');
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('POPUP_LOGIN', $mess);
    if ($page_url != '') {
        $xtpl->assign('PAGE_URL', nv_redirect_encrypt(urlRewriteWithDomain($page_url, NV_MY_DOMAIN)));
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

define('NV_IS_MOD_BUSINESSLISTINGS', true);

/**
 * position_to_english()
 * Dịch chức vụ sang tiếng anh. Trả về chính nó nếu đang là tiếng Việt hoặc không dịch được
 *
 * @param string $pos Tên chức vụ
 * @return string
 */
function position_to_english($pos)
{
    global $db;
    if (NV_LANG_DATA == 'vi' || empty($pos)) {
        return trim($pos);
    }

    $pos_en = $db->query('SELECT position_en FROM ' . BUSINESS_PREFIX_GLOBAL . '_position WHERE position_vi = ' . $db->quote(trim($pos)))->fetchColumn();
    if (!empty($pos_en)) {
        return $pos_en;
    } else {
        return trim($pos);
    }
}

/**
 * num_of_time()
 * Số đếm theo tiếng anh 1st, 2nd, 3rd,...
 * Trả về chính nó nếu là tiếng Việt
 *
 * @param mixed $num
 * @return string
 */
function num_of_time($num) {
    $num = (int) $num;
    if (NV_LANG_DATA == 'vi') {
        return $num;
    } else {
        if ($num % 10 == 1 && $num % 100 != 11) {
            return ($num . '<sup>st</sup>');
        } elseif ($num % 10 == 2 && $num % 100 != 12) {
            return ($num . '<sup>nd</sup>');
        } elseif ($num % 10 == 3 && $num % 100 != 13) {
            return ($num . '<sup>rd</sup>');
        } else {
            return ($num . '<sup>th</sup>');
        }
    }
}

$dm_htlcnt = [];
$dm_htlcnt['DTRR'] = $nv_Lang->getModule('type_lc_17');
$dm_htlcnt['CHCT'] = $nv_Lang->getModule('type_lc_12');
$dm_htlcnt['CHCTRG'] = $nv_Lang->getModule('type_lc_11');
$dm_htlcnt['DTHC'] = $nv_Lang->getModule('type_lc_16');
$dm_htlcnt['MSTT'] = $nv_Lang->getModule('type_lc_19');
$dm_htlcnt['CDT'] = $nv_Lang->getModule('type_lc_14');
$dm_htlcnt['CDTRG'] = $nv_Lang->getModule('type_lc_13');
$dm_htlcnt['TTH'] = $nv_Lang->getModule('type_lc_23');
$dm_htlcnt['LCNT_DB'] = $nv_Lang->getModule('type_lc_30');
$dm_htlcnt['TVCN'] = $nv_Lang->getModule('type_lc_31');
$dm_htlcnt['TCTVCN'] = $nv_Lang->getModule('type_lc_28');
$dm_htlcnt['DPCT'] = $nv_Lang->getModule('type_lc_32');
$dm_htlcnt['QCBS'] = $nv_Lang->getModule('type_lc_33');
$dm_htlcnt['DPG'] = $nv_Lang->getModule('type_lc_15');
$dm_htlcnt['QBS'] = $nv_Lang->getModule('type_lc_34');
$dm_htlcnt['FBS'] = $nv_Lang->getModule('type_lc_35');
$dm_htlcnt['LCS'] = $nv_Lang->getModule('type_lc_36');
$dm_htlcnt['CQS'] = $nv_Lang->getModule('type_lc_37');
$dm_htlcnt['SSS'] = $nv_Lang->getModule('type_lc_38');
$dm_htlcnt['TGTC'] = $nv_Lang->getModule('type_lc_39');
$dm_htlcnt['NHBD'] = $nv_Lang->getModule('type_lc_40');
$dm_htlcnt['TVCT'] = $nv_Lang->getModule('type_lc_41');
$dm_htlcnt['TGTHCD'] = $nv_Lang->getModule('type_lc_20');

function vnd_to_words($amount, $end = true)
{
    if ($amount <= 0) {
        return $textnumber = "Tiền phải là số nguyên dương lớn hơn số 0";
    }
    if (NV_LANG_DATA == 'vi') {
        $Text = array(
            "không",
            "một",
            "hai",
            "ba",
            "bốn",
            "năm",
            "sáu",
            "bảy",
            "tám",
            "chín"
        );
        $TextLuythua = array(
            "",
            "nghìn",
            "triệu",
            "tỷ",
            "nghìn",
            "triệu",
            "tỷ"
        );
        // Phai de cac ham replace theo dung thu tu nhu the nay
        $thaythe = array(
            "không mươi" => "lẻ",
            "lẻ không" => "",
            "mươi không" => "mươi",
            "một mươi" => "mười",
            "mươi năm" => "mươi lăm",
            "mươi một" => "mươi mốt",
            "mười năm" => "mười lăm"
        );

        $textnumber = "";
        $length = strlen($amount);

        for ($i = 0; $i < $length; $i++)
            $unread[$i] = 0;

        for ($i = 0; $i < $length; $i++) {
            $so = substr($amount, $length - $i - 1, 1);

            if (($so == 0) && ($i % 3 == 0) && ($unread[$i] == 0)) {
                for ($j = $i + 1; $j < $length; $j++) {
                    $so1 = substr($amount, $length - $j - 1, 1);
                    if ($so1 != 0)
                        break;
                }

                if (intval(($j - $i) / 3) > 0) {
                    for ($k = $i; $k < intval(($j - $i) / 3) * 3 + $i; $k++)
                        $unread[$k] = 1;
                }
            }
        }

        for ($i = 0; $i < $length; $i++) {
            $so = substr($amount, $length - $i - 1, 1);
            if ($unread[$i] == 1)
                continue;

            if (($i % 3 == 0) && ($i > 0))
                $textnumber = $TextLuythua[$i / 3] . " " . $textnumber;

            if ($i % 3 == 2)
                $textnumber = 'trăm ' . $textnumber;

            if ($i % 3 == 1)
                $textnumber = 'mươi ' . $textnumber;

            $textnumber = $Text[$so] . " " . $textnumber;
        }

        foreach ($thaythe as $k => $v) {
            $textnumber = str_replace($k, $v, $textnumber);
        }

        return ucfirst(trim($textnumber) . ($end ? " đồng chẵn" : ''));
    } else {
        $amount = (int) $amount;
        $words = [];
        $list1 = [
            '',
            'one',
            'two',
            'three',
            'four',
            'five',
            'six',
            'seven',
            'eight',
            'nine',
            'ten',
            'eleven',
            'twelve',
            'thirteen',
            'fourteen',
            'fifteen',
            'sixteen',
            'seventeen',
            'eighteen',
            'nineteen'
        ];
        $list2 = [
            '',
            'ten',
            'twenty',
            'thirty',
            'forty',
            'fifty',
            'sixty',
            'seventy',
            'eighty',
            'ninety',
            'hundred'
        ];
        $list3 = [
            '',
            'thousand',
            'million',
            'billion',
            'trillion',
            'quadrillion',
            'quintillion',
            'sextillion',
            'septillion',
            'octillion',
            'nonillion',
            'decillion',
            'undecillion',
            'duodecillion',
            'tredecillion',
            'quattuordecillion',
            'quindecillion',
            'sexdecillion',
            'septendecillion',
            'octodecillion',
            'novemdecillion',
            'vigintillion'
        ];
        $amount_length = strlen($amount);
        $levels = (int) (($amount_length + 2) / 3);
        $max_length = $levels * 3;
        $amount = substr('00' . $amount, -$max_length);
        $amount_levels = str_split($amount, 3);
        for ($i = 0; $i < count($amount_levels); $i++) {
            $levels--;
            $hundreds = (int) ($amount_levels[$i] / 100);
            $hundreds = ($hundreds ? ' ' . $list1[$hundreds] . ' hundred' . ' ' : '');
            $tens = (int) ($amount_levels[$i] % 100);
            $singles = '';
            if ($tens < 20) {
                $tens = ($tens ? ' ' . $list1[$tens] . ' ' : '');
            } else {
                $tens = (int) ($tens / 10);
                $tens = ' ' . $list2[$tens] . ' ';
                $singles = (int) ($amount_levels[$i] % 10);
                $singles = ' ' . $list1[$singles] . ' ';
            }
            $words[] = $hundreds . $tens . $singles . (($levels && (int) ($amount_levels[$i])) ? ' ' . $list3[$levels] . ' ' : '');
        } // end for loop
        $commas = count($words);
        if ($commas > 1) {
            $commas = $commas - 1;
        }
        $textnumber = preg_replace('/\s+/', ' ', trim(implode(' ', $words)));
        return ucfirst($textnumber . ($end ? ' dong' : ''));
    }
}

/**
 * get_lang($lang, $index)
 * Lấy lịch sử bấm cập nhật lại của người dùng
 *
 * @param $lang string
 *          Ngôn ngữ vi/en
 * @param $index string
 *          index của lang muốn lấy
 *
 * @return string|bool
 *
 */
function get_lang ($lang, $index)
{
    global $module_file, $nv_Lang;

    if (!in_array($lang, ['vi', 'en'])) {
        return false;
    }
    if ($lang != NV_LANG_INTERFACE) {
        $nv_Lang->changeLang($lang);
        $nv_Lang->loadModule($module_file, false, true);
    }
    $value = $nv_Lang->getModule($index);
    if ($lang != NV_LANG_INTERFACE) {
        $nv_Lang->changeLang(NV_LANG_INTERFACE);
    }

    return $value;
}

/**
 * Thực hiện chuyển hướng URL vĩnh viễn (301 Moved Permanently) từ dạng URL cũ sang dạng mới:
 * 
 * URL cũ:
 * Tiếng Việt:
 * - https://dauthau.asia/businesslistings/static/?select_top=0
 * - https://dauthau.asia/businesslistings/static/?select_top=1  
 * - https://dauthau.asia/businesslistings/static/?select_top=2
 * 
 * Tiếng Anh:
 * - https://dauthau.asia/en/businesslistings/static/?select_top=0
 * - https://dauthau.asia/en/businesslistings/static/?select_top=1
 * - https://dauthau.asia/en/businesslistings/static/?select_top=2
 * 
 * URL mới:
 * Tiếng Việt:
 * - https://dauthau.asia/businesslistings/top10/duthau.html
 * - https://dauthau.asia/businesslistings/top10/trungthau.html
 * - https://dauthau.asia/businesslistings/top10/truotthau.html
 * 
 * Tiếng Anh: 
 * - https://dauthau.asia/en/businesslistings/top10/bidding.html
 * - https://dauthau.asia/en/businesslistings/top10/winning.html
 * - https://dauthau.asia/en/businesslistings/top10/failed.html
 */
if (!empty($array_op)) {
    if ($array_op[0] == 'static') {
        $select_top_request = $nv_Request->get_int('select_top', 'get', -1);
        
        if ($select_top_request !== -1) {
            $redirect_map = array();
            for ($i = 0; $i <= 2; $i++) {
                $alias = $nv_Lang->getModule('static_type_' . $i);
                $redirect_map[NV_LANG_DATA][$i] = $alias;
            }

            if (isset($redirect_map[NV_LANG_DATA]) && isset($redirect_map[NV_LANG_DATA][$select_top_request])) {
                $url_Permanently = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['static'] . '/' . $redirect_map[NV_LANG_DATA][$select_top_request] . $global_config['rewrite_exturl'], true);
                header("HTTP/1.1 301 Moved Permanently");
                header('Location:' . $url_Permanently);
                exit();
            }
        } else {
            $url_Permanently = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['static'] . $global_config['rewrite_exturl'], true);
            header("HTTP/1.1 301 Moved Permanently");
            header('Location:' . $url_Permanently);
            exit();
        }
    }
}

$vnr_industry_vi = [
    "3" => "Trồng trọt",
    "4" => "Chăn nuôi",
    "5" => "Hoạt động nông nghiệp khác",
    "7" => "Lâm nghiệp và hoạt động dịch vụ liên quan",
    "9" => "Khai thác, nuôi trồng thủy sản",
    "11" => "Sản xuất, kinh doanh thức ăn chăn nuôi",
    "12" => "Hoạt động hỗ trợ nông nghiệp khác",
    "15" => "Sản xuất, kinh doanh vật liệu xây dựng",
    "16" => "Xây dựng",
    "17" => "Kinh doanh bất động sản",
    "18" => "Hoạt động xây dựng khác",
    "20" => "Khai thác, kinh doanh than và hoạt động hỗ trợ",
    "21" => "Thăm dò, khai thác dầu mỏ và hoạt động hỗ trợ",
    "22" => "Kinh doanh xăng dầu và các sản phẩm liên quan",
    "23" => "Khai thác, chế biến, kinh doanh quặng kim loại và hoạt động hỗ trợ",
    "24" => "Khai khoáng khác và kinh doanh các sản phẩm khác từ khai khoáng",
    "26" => "Sản xuất, chế biến và kinh doanh sữa, sản phẩm từ sữa",
    "27" => "Sản xuất, chế biến và kinh doanh đường",
    "28" => "Sản xuất, chế biến và kinh doanh bánh kẹo",
    "29" => "Sản xuất, chế biến và kinh doanh gia vị, nước chấm",
    "30" => "Sản xuất, chế biến và kinh doanh dầu ăn, mỡ động thực vật",
    "31" => "Sản xuất, chế biến và kinh doanh thực phẩm đóng gói, ăn liền, đồ hộp",
    "32" => "Sản xuất, chế biến và kinh doanh thực phẩm tươi sống, đông lạnh",
    "33" => "Sản xuất, chế biến và kinh doanh gạo, ngũ cốc, sản phẩm nghiền, đồ khô",
    "34" => "Sản xuất, chế biến và kinh doanh thực phẩm khác",
    "35" => "Sản xuất, chế biến và kinh doanh đồ uống có cồn",
    "36" => "Sản xuất, chế biến và kinh doanh đồ uống không cồn",
    "37" => "Sản xuất, chế biến và kinh doanh thuốc lá",
    "39" => "Sản xuất, kinh doanh sợi, sản phẩm dệt",
    "40" => "Sản xuất, kinh doanh trang phục",
    "41" => "Sản xuất, kinh doanh sản phẩm da giầy",
    "43" => "Sản xuất, kinh doanh giấy và các sản phẩm từ giấy",
    "44" => "In ấn, xuất bản",
    "46" => "Sản xuất, kinh doanh hóa chất cơ bản",
    "47" => "Sản xuất, kinh doanh phân bón, thuốc trừ sâu",
    "48" => "Sản xuất, kinh doanh nhựa và sản phẩm từ nhựa",
    "49" => "Sản xuất, kinh doanh cao su và sản phẩm từ cao su",
    "50" => "Sản xuất, kinh doanh hóa mỹ phẩm",
    "51" => "Sản xuất, kinh doanh hóa chất khác",
    "53" => "Sản xuất dược phẩm, thiết bị y tế",
    "54" => "Kinh doanh, phân phối dược phẩm, thiết bị y tế",
    "56" => "Sản xuất, gia công kim loại, sắt thép, kim khí…",
    "57" => "Kinh doanh kim loại, sắt thép, kim khí…",
    "59" => "Sản xuất, kinh doanh ô tô, xe máy, xe có động cơ và phụ kiện",
    "60" => "Công nghiệp đóng tàu và thuyền",
    "61" => "Sản xuất, kinh doanh thiết bị vận tải khác",
    "62" => "Sản xuất, kinh doanh máy móc, sản phẩm cơ khí khác",
    "64" => "Sản xuất, truyền tải, phân phối điện",
    "65" => "Sản xuất, kinh doanh thiết bị điện",
    "66" => "Sản xuất, kinh doanh thiết bị điện tử, điện lạnh, công nghệ thông tin, viễn thông…",
    "68" => "Cung cấp, kinh doanh nước",
    "69" => "Xử lý rác, nước thải",
    "70" => "Sản xuất, kinh doanh gỗ, sản phẩm từ gỗ và nội thất",
    "71" => "Sản xuất, kinh doanh đồ dùng gia đình",
    "72" => "Hoạt động công nghiệp khác",
    "75" => "Ngân hàng",
    "76" => "Chứng khoán",
    "77" => "Dịch vụ tài chính khác",
    "78" => "Bảo hiểm",
    "80" => "Bán lẻ hàng tiêu dùng nhanh trong siêu thị, cửa hàng tiện lợi, chợ truyền thông",
    "81" => "Bán lẻ điện tử, điện lạnh, viễn thông, công nghệ thông tin",
    "82" => "Bán lẻ sách, báo, tạp chí, văn phòng phẩm",
    "83" => "Bán lẻ vàng, bạc và kim loại quý khác",
    "84" => "Bán lẻ khác",
    "86" => "Vận tải đường bộ",
    "87" => "Vận tải đường sắt, đường ống",
    "88" => "Vận tải đường thủy nội địa",
    "89" => "Vận tải đường biển",
    "90" => "Vận tải hàng không",
    "91" => "Bưu chính, chuyển phát",
    "92" => "Kho bãi, bốc xếp hàng hóa",
    "93" => "Hoạt động khác hỗ trợ cho vận tải",
    "95" => "Viễn thông",
    "96" => "Truyền hình, phát thanh, phim ảnh",
    "97" => "Quảng cáo và nghiên cứu thị trường",
    "98" => "Hoạt động thông tin khác",
    "100" => "Du lịch",
    "101" => "Lưu trú",
    "103" => "Dịch vụ vui chơi, giải trí",
    "104" => "Ẩm thực",
    "105" => "Dịch vụ khác"
];

$vnr_industry_en = [
    "3"  => "Crop Cultivation",
    "4"  => "Animal Husbandry",
    "5"  => "Other Agricultural Activities",
    "7"  => "Forestry and Related Services",
    "9"  => "Extraction and Aquaculture",
    "11" => "Production and Trading of Animal Feed",
    "12" => "Other Agricultural Support Activities",
    "15" => "Manufacturing and Trading of Construction Materials",
    "16" => "Construction",
    "17" => "Real Estate Trading",
    "18" => "Other Construction Activities",
    "20" => "Coal Mining, Trading, and Support Activities",
    "21" => "Exploration, Extraction of Oil and Support Activities",
    "22" => "Trading of Gasoline, Petroleum, and Related Products",
    "23" => "Mining, Processing, and Trading of Metallic Ores and Support Activities",
    "24" => "Other Mining and Trading of Mining Products",
    "26" => "Production, Processing and Trading of Milk and Dairy Products",
    "27" => "Production, Processing and Trading of Sugar",
    "28" => "Production, Processing and Trading of Confections",
    "29" => "Production, Processing and Trading of Spices and Dips",
    "30" => "Production, Processing and Trading of Cooking Oils and Fats",
    "31" => "Production, Processing and Trading of Packaged, Instant, and Canned Foods",
    "32" => "Production, Processing and Trading of Fresh and Frozen Foods",
    "33" => "Production, Processing and Trading of Rice, Cereals, Milled and Dried Products",
    "34" => "Production, Processing and Trading of Other Foods",
    "35" => "Production, Processing and Trading of Alcoholic Beverages",
    "36" => "Production, Processing and Trading of Non-Alcoholic Beverages",
    "37" => "Production, Processing and Trading of Tobacco",
    "39" => "Manufacturing and Trading of Fibers and Textile Products",
    "40" => "Manufacturing and Trading of Garments",
    "41" => "Manufacturing and Trading of Leather and Shoe Products",
    "43" => "Manufacturing and Trading of Paper and Paper Products",
    "44" => "Printing and Publishing",
    "46" => "Manufacturing and Trading of Basic Chemicals",
    "47" => "Manufacturing and Trading of Fertilizers and Pesticides",
    "48" => "Manufacturing and Trading of Plastics and Plastic Products",
    "49" => "Manufacturing and Trading of Rubber and Rubber Products",
    "50" => "Manufacturing and Trading of Cosmetic Chemicals",
    "51" => "Manufacturing and Trading of Other Chemicals",
    "53" => "Pharmaceutical and Medical Device Manufacturing",
    "54" => "Trading and Distribution of Pharmaceuticals and Medical Devices",
    "56" => "Manufacturing and Processing of Metals, Steel, and Hardware…",
    "57" => "Trading of Metals, Steel, and Hardware…",
    "59" => "Manufacturing and Trading of Cars, Motorcycles, and Vehicle Accessories",
    "60" => "Shipbuilding and Boat Manufacturing",
    "61" => "Manufacturing and Trading of Other Transport Equipment",
    "62" => "Manufacturing and Trading of Machinery and Other Mechanical Products",
    "64" => "Electricity Generation, Transmission, and Distribution",
    "65" => "Manufacturing and Trading of Electrical Equipment",
    "66" => "Manufacturing and Trading of Electronics, Refrigeration, IT, and Telecommunications…",
    "68" => "Supply and Trading of Water",
    "69" => "Waste and Wastewater Treatment",
    "70" => "Manufacturing and Trading of Wood, Wood Products, and Furniture",
    "71" => "Manufacturing and Trading of Household Items",
    "72" => "Other Industrial Activities",
    "75" => "Banking",
    "76" => "Securities",
    "77" => "Other Financial Services",
    "78" => "Insurance",
    "80" => "Retail of FMCG in Supermarkets, Convenience Stores, and Traditional Markets",
    "81" => "Retail of Electronics, Electrical Appliances, Telecommunications, and IT",
    "82" => "Retail of Books, Newspapers, Magazines, and Stationery",
    "83" => "Retail of Gold, Silver, and Other Precious Metals",
    "84" => "Other Retail",
    "86" => "Road Transport",
    "87" => "Rail and Pipeline Transport",
    "88" => "Inland Waterway Transport",
    "89" => "Maritime Transport",
    "90" => "Air Transport",
    "91" => "Postal Services and Courier",
    "92" => "Warehousing and Cargo Handling",
    "93" => "Other Transport Support Activities",
    "95" => "Telecommunications",
    "96" => "Television, Radio, and Film",
    "97" => "Advertising and Market Research",
    "98" => "Other Information Activities",
    "100" => "Tourism",
    "101" => "Accommodation",
    "103" => "Recreational and Entertainment Services",
    "104" => "Culinary Services",
    "105" => "Other Services"
];
