<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2021 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 19 Jan 2021 07:43:38 GMT
 */
if (!defined('NV_IS_MOD_PROJECT_INVESTMENT')) {
    die('Stop!!!');
}
use NukeViet\Dauthau\Share;
$page_title = $module_info['site_title'];
$key_words = $module_info['keywords'];
// $canonicalUrl = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name, true);
$base_url_dtnet_project = URL_DTNET_SITE . NV_LANG_DATA . '/' . $dtnet_module_bids . '/' . $dtnet_module_bids_project . '/?type_info=1&amp;is_advance=0&amp;post_type=dang-cong-khai';
$home_page = $nv_Request->get_int('home_page', 'get', 0);
$perpage = ($home_page == 1) ? 10 : 20;
$page = $nv_Request->get_page('page', 'get', 1);
if ($page < 1) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_wrong_page') . $btn, 'danger');

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

/**
 * thao: cho sửa dùm anh nếu page > 100 báo lỗi:
 * Vui lòng thay đổi tiêu chí tìm kiếm để có thông tin bạn cần
 */
if ($page > 100) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_max_searchpage') . $btn);

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}
$q = $nv_Request->get_title('q', 'post,get'); // Từ khóa chính

$key_search2 = $nv_Request->get_title('q2', 'post,get'); // Từ khóa bổ sung
$search_one_key = $nv_Request->get_int('search_one_key', 'post,get', 0); // Một trong các từ khóa bổ sung là bắt buộc
$without_key = $nv_Request->get_title('without_key', 'post,get'); // Từ khóa loại trừ
$is_advance = $nv_Request->get_int('is_advance', 'get', 0);
$search_type_content = $nv_Request->get_int('search_type_content', 'post,get', 0); // 0. Tương đối; 1. Tuyệt đối
$par_search = $nv_Request->get_int('par_search', 'post,get', 0);
$search_kind = $nv_Request->get_int('searchkind', 'post,get', 0);
$solicitor_id = $nv_Request->get_int('solicitor_id', 'post,get', 0);

$total_investment_from = $nv_Request->get_string('invest_from', 'get', ''); // giá
$total_investment_from = floatval(str_replace(',', '', $total_investment_from));
$total_investment_to = $nv_Request->get_string('invest_to', 'get', '');
$total_investment_to = floatval(str_replace(',', '', $total_investment_to));
$max_vaule_price = Share::MAX_VALUE_PRICE;
$error_money = ($total_investment_from > $max_vaule_price || $total_investment_to > $max_vaule_price) ? $nv_Lang->getModule('error_money_from') : '';
if ($total_investment_from > $max_vaule_price) {
    $total_investment_from = $max_vaule_price;
}
if ($total_investment_to > $max_vaule_price) {
    $total_investment_to = $max_vaule_price;
}
$sfrom = nv_substr($nv_Request->get_title('sfrom', 'get', ''), 0, 10); // thời gian
$sto = nv_substr($nv_Request->get_title('sto', 'get', ''), 0, 10);
$oda = $nv_Request->get_int('oda', 'get', 0);
$khlcnt = $nv_Request->get_int('khlcnt', 'get', 0);
$idprovince = $nv_Request->get_int('idprovince', 'get,post', -1); // Tỉnh thành

if (!empty($q) and $sfrom == '' and $sto == '') {
    if (preg_match("/^20([0-9]{2})([0-9]{2})([0-9]+)/", $q, $m)) {
        $today = mktime(0, 0, 0, $m[2], date('d', NV_CURRENTTIME), ($m[1] + 2000));
        $sfrom = strtotime('-3 month', $today);
        $sto = strtotime('+3 month', $today);
        // $sto = $sto > NV_CURRENTTIME ? NV_CURRENTTIME : $sto;
        $sfrom = nv_date('d/m/Y', $sfrom);
        $_GET['sfrom'] = $sfrom;
        $sto = nv_date('d/m/Y', $sto);
        $_GET['sto'] = $sto;
    }
}

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sto, $m)) {
    $sto1 = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} elseif (preg_match('/^(20[0-9]{2})([0-9]{2})([0-9]{5})/', $q, $_m)) {
    $number = cal_days_in_month(CAL_GREGORIAN, $_m[2], $_m[1]);
    $sto1 = mktime(0, 0, 0, $_m[2], $number, $_m[1]);
} else {
    $sto1 = NV_CURRENTTIME;
}
$sto1 > NV_CURRENTTIME && $sto1 = NV_CURRENTTIME;

$_m = [];
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sfrom, $m)) {
    $sfrom1 = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $sfrom1 = 0;
}
$sfrom1 > NV_CURRENTTIME && $sfrom1 = NV_CURRENTTIME;
$sto2 = $sto1 - 3600;
$sfrom1 > $sto2 && $sfrom1 = $sto2;

$search_mysql_range = intval($config_bidding['search_mysql_range']);
if (!isset($search_mysql_range) or $search_mysql_range < 1) {
    $search_mysql_range = 1;
}

if (defined('NV_IS_ADMIN')) {
    $search_mysql_range = 24;
}

// $_search_mysql_range = $search_mysql_range == 1 ? "-" . $search_mysql_range . " month" : "-" . $search_mysql_range . " months";
// $sfrom_check = strtotime(date("Y-m-d", $sto1) . $_search_mysql_range);

// if ($sfrom1 < $sfrom_check) {
//     $sfrom1 = $sfrom_check;
//     isset($_GET['sfrom']) && $search_mysql_range && $error[] = sprintf($nv_Lang->getModule('sql_rangetime_error'), $search_mysql_range, nv_date("d/m/Y", $sfrom1), nv_date("d/m/Y", $sto1));
// }

if (NV_CURRENTTIME >= $close_time_dauthau and NV_CURRENTTIME <= $open_maunal_time_dauthau and ($type_user == 2 or $type_user == 3)) {
    // view chỉ xem dc tin cũ
    $sto1 = $close_time_dauthau;
    if ($sfrom1 > $sto1) {
        $sfrom1 = $sto1 - (86400 * 30);
    }
}
$_GET['sfrom'] = nv_date('d/m/Y', $sfrom1 ?: $sto1 - (13 * 86400));
$_GET['sto'] = nv_date('d/m/Y', $sto1);

$is_elas = $config_bidding['elas_use'] ? true : false;
list ($sfrom2, $sto2) = setMySqlRangeTimes($is_elas, $_GET['sfrom'], $_GET['sto'], $q, $error);

$array_data = $arr_solicitor_id = [];
$array_data_project = [];

// kiểm tra sử dụng elastic search hay mysql
if ($is_elas) {
    require NV_ROOTDIR . '/modules/' . $module_file . '/search/detailelastic.php';
} else {
    require NV_ROOTDIR . '/modules/' . $module_file . '/search/detailmysql.php';
}

$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;
$global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;
// $base_url .= '&type_search=1';
// $base_url .= '&is_advance=' . $is_advance;
if (!empty($q)) {
    $base_url .= '&q=' . $q;
    $global_lang_url .= '&q=' . $q;
    $base_url_dtnet_project .= '&amp;q=' . $q;
}

if (!empty($without_key)) {
    $base_url .= '&without_key=' . $without_key;
    $global_lang_url .= '&without_key=' . $without_key;
    $base_url_dtnet_project .= '&amp;without_key=' . $without_key;
}

if (!empty($search_type_content)) {
    $base_url .= '&search_type_content=' . $search_type_content;
    $global_lang_url .= '&search_type_content=' . $search_type_content;
}

if (!empty($par_search)) {
    $base_url .= '&par_search=' . $par_search;
    $global_lang_url .= '&par_search=' . $par_search;
}

if (!empty($search_kind)) {
    $base_url .= '&searchkind=' . $search_kind;
    $global_lang_url .= '&searchkind=' . $search_kind;
}
if ($idprovince >= 0) {
    $base_url .= '&idprovince=' . $idprovince;
    $global_lang_url .= '&idprovince=' . $idprovince;
}

if (!empty($solicitor_id)) {
    $base_url .= '&solicitor_id=' . $solicitor_id;
    $global_lang_url .= '&solicitor_id=' . $solicitor_id;
}

if (!empty($sfrom)) {
    $base_url .= "&sfrom=" . $sfrom;
    $global_lang_url .= "&sfrom=" . $sfrom;
    $base_url_dtnet_project .= "&amp;sfrom=" . $sfrom;
}
if (!empty($sto)) {
    $base_url .= "&sto=" . $sto;
    $global_lang_url .= "&sto=" . $sto;
    $base_url_dtnet_project .= "&amp;sto=" . $sto;
}
if ($total_investment_from > 0 && $total_investment_from <= $max_vaule_price) {
    $base_url .= '&invest_from=' . intval($total_investment_from);
    $global_lang_url .= '&invest_from=' . intval($total_investment_from);
}
if ($total_investment_to > 0 && $total_investment_to <= $max_vaule_price) {
    $base_url .= '&invest_to=' . intval($total_investment_to);
    $global_lang_url .= '&invest_to=' . intval($total_investment_to);
}

if ($oda > 0) {
    $base_url .= '&oda=1';
    $global_lang_url .= '&oda=1';
}

if ($khlcnt > 0) {
    $base_url .= '&khlcnt=1';
    $global_lang_url .= '&khlcnt=1';
}

if (!empty($key_search2)) {
    $base_url .= '&q2=' . $key_search2;
    $global_lang_url .= '&q2=' . $key_search2;
    $base_url_dtnet_project .= '&amp;q2=' . $key_search2;
}

if (!empty($search_one_key)) {
    $base_url .= '&search_one_key=' . $search_one_key;
    $global_lang_url .= '&search_one_key=' . $search_one_key;
}

if ($home_page > 0) {
    $base_url .= '&home_page=' . $home_page;
    $global_lang_url .= '&home_page=' . $home_page;
}

if ($is_advance) {
    $base_url .= "&is_advance=1";
    $global_lang_url .= "&is_advance=1";
}

$page_url =  $base_url;
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
    $global_lang_url .= '&amp;page=' . $page;
}
// #2415: Thêm hằng chặn index: DADTPT
$query_params = [];
parse_str(parse_url($page_url, PHP_URL_QUERY), $query_params);
// Danh sách các tham số cần xóa
$params_to_remove = [
    'amp;' . NV_NAME_VARIABLE,
    'amp;' . NV_OP_VARIABLE,
    NV_LANG_VARIABLE
];
if ($idprovince >-1) {
    $params_to_remove[] = 'idprovince';
}
$query_params = array_diff_key($query_params, array_flip($params_to_remove));
$has_other_query_params = false;
foreach ($query_params as $key => $value) {
    if ($key !== 'amp;page') {
        $has_other_query_params = true;
        break;
    }
}
if (!empty($q) || empty($array_data) || $has_other_query_params) {
    $nv_BotManager->setFollow()->setNoIndex();
}

$canonicalUrl = getCanonicalUrl($page_url);
betweenURLs($page, ceil($total / $perpage), $base_url, '&amp;page=', $prevPage, $nextPage);

$list_solicitor = [];
if (!empty($array_data) and !empty($arr_solicitor_id)) {
    $sql = "SELECT id,alias,solicitor_code, english_name FROM " . BID_PREFIX_GLOBAL . "_solicitor WHERE id IN(" . implode(',', $arr_solicitor_id) . ")";
    $query = $db->query($sql);
    while ($result = $query->fetch()) {
        $list_solicitor[$result['id']] = [
            $result['alias'],
            $result['solicitor_code'],
            $result['english_name']
        ];
    }
}

$project_ids = [];
foreach ($array_data_project as $key => $value) {
    if (!empty($value['profile_id'])) {
        $profile_ids[$value['profile_id']] = $value['profile_id'];
    }
}

if (!empty($profile_ids)) {
    $search_profile = new NukeViet\ElasticSearch\Functions($config_bidding['elas_result_host'], $config_bidding['elas_result_port'], 'bids_profile', $config_bidding['elas_result_user'], $config_bidding['elas_result_pass']);

    $search_esdtnet_profile = [];
    $search_esdtnet_profile['must'][] = [
        'terms' => [
            'id' => array_keys($profile_ids)
        ]
    ];

    $array_query_elastic_dtnet_profile = [];
    if (!empty($search_esdtnet_profile)) {
        $array_query_elastic_dtnet_profile['query']['bool'] = $search_esdtnet_profile;
    }

    $response = $search_profile->search_data($db_config['prefix'] . '_bids_profile', $array_query_elastic_dtnet_profile);

    $arr_profile = [];
    foreach ($response['hits']['hits'] as $v) {
        if (!empty($v['_source'])) {
            $view = $v['_source'];
            $view['link_invector'] = URL_DTNET_SITE . NV_LANG_DATA . '/' . $dtnet_module_profile . '/' . $view['prof_alias'] . '/';
            $arr_profile[$view['id']] = $view;
        }
    }
}

foreach ($array_data_project as $key => $value) {
    if (!empty($value['project_id']) && isset($arr_profile[$value['project_id']])) {
        $profile = $arr_profile[$value['profile_id']];
        $array_data_project[$key]['prof_code'] = $profile['prof_code'];
        $array_data_project[$key]['prof_name'] = $profile['prof_name'];
        $array_data_project[$key]['prof_alias'] = $profile['prof_alias'];
        $array_data_project[$key]['link_invector'] = $profile['link_invector'];

    } else {
        unset($array_data_project[$key]);
        continue;
    }
    $array_data_project[$key]['link_project'] = URL_DTNET_SITE . NV_LANG_DATA . '/' . $dtnet_module_bids . '/' . $dtnet_module_bids_project . '/' . $value['alias'] . '-' . $value['project_code'] . $global_config['rewrite_exturl'];
    $array_data_project[$key]['link_see_more'] = $base_url_dtnet_project;
}

$generate_page = nv_generate_page($base_url, $total, $perpage, $page);
$contents = nv_theme_project_investment($array_data, $list_solicitor, $generate_page, $total, $error_money, $array_data_project);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
