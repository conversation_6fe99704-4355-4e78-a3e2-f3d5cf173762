<!-- BEGIN: main -->
<div class="border-bidding">
    <span>{LANG.title_search_contractor}</span>
</div>
<form action="{FORM_ACTION}" method="get" onsubmit="return buss_checkSearchForm(this);" class="form-horizontal form-search form-bussiness-search" id="bussSearchBlock" data-bussaction="{FORM_ACTION2}" data-bussaction1="{FORM_ACTION1}" data-bussaction3="{FORM_ACTION3}">
    <!-- BEGIN: no_rewrite -->
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"/>
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}"/>
    <input type="hidden" name="{NV_OP_VARIABLE}" value="search"/>
    <!-- END: no_rewrite -->
    <div class="form-group margin-bottom-sm">
        <label class="control-label col-md-5">{LANG.tukhoa}:</label>
        <div class="col-md-19">
            <input class="form-control" placeholder="{LANG.search_key_title}" type="text" value="{keyword}" name="q" data-default=""/>
            <em class="help-block margin-bottom-sm">{MAX_KEYWORD}</em>
        </div>
    </div>
    <div class="row">
        <div class="col-md-19 col-md-offset-5">
            <input type="hidden" name="is_bussp" value="{ISBUSSP}" />
            <input type="hidden" name="is_advance" value="{ADVANCE}" />
            <input id="fsearch" type="submit" value="{LANG.search}" class="btn btn-primary bussubmit_search"/>
            <a class="btn-search-advance btn btn-default" href="javascript:void(0);" data-search-simple="{LANG.search_simple}" data-search-advance="{LANG.search_advance}" data-icon-search-simple="icon-chevron-down" data-icon-search-advance="icon-chevron-up">
                <em class="<!-- BEGIN: advance_icon_0 -->icon-chevron-down <!-- END: advance_icon_0 -->
                <!-- BEGIN: advance_icon_1 -->icon-chevron-up <!-- END: advance_icon_1 -->margin-right-sm"></em>
                <strong class="txt">{LANG_ADVANCE}</strong>
            </a>
        </div>
    </div>
    <div class="advance-search-content" <!-- BEGIN: advance_bl_hide -->style="display: none"<!-- END: advance_bl_hide -->>
        <div class="form-group">
            <label class="control-label col-md-5">{LANG.nganhnghe}:</label>
            <div class="col-md-19 col-industry1">
                <select id="industry1" name="industry1" data-default="{LANG.pleaseselect}" onchange="getValueIndustry_block('industry1', 2, 'industry2', '', '');" class="form-control">
                    <option value="">{LANG.pleaseselect}</option>
                    <!-- BEGIN: industry -->
                    <option value="{key_industry}" {sl_industry}>{val_industry}</option>
                    <!-- END: industry -->
                </select>
                <span id="industry2"></span>
                <span id="industry3"></span>
                <span id="industry4"></span>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-5">{LANG.nation}:</label>
            <div class="col-md-19">
                <select name="nation" id="nation" class="form-control" data-flag="1" data-default="{LANG.pleaseselect}">
                    <option value="0">{LANG.pleaseselect}</option>
                    <!-- BEGIN: nation -->
                    <option value="{NATION.key}" data-alias="{NATION.alias}" {NATION.selected}>{NATION.title}</option>
                    <!-- END: nation -->
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-5">{LANG.diadiem}:</label>
            <div class="col-md-19" id="bu-location-search">
                <select disabled="disabled" data-selected="{SEARCH_PROVINCE}" name="province" id="province" class="form-control" data-flag="1" data-default="{LANG.pleaseselect}" data-unclassified="{LANG.unclassified}" data-timestamp="{TIMESTAMP}">
                    <option value="-1">{LANG.pleaseselect}</option>
                </select>
                <div class="margin-top-lg{CLASS_DISTRICT}">
                    <select disabled="disabled" data-selected="{SEARCH_DISTRICT}" name="district" id="district" class="form-control" data-flag="1" data-default="{LANG.pleaseselect}" data-timestamp="{TIMESTAMP}">
                        <option value="0">{LANG.pleaseselect}</option>
                    </select>
                </div>
                <div class="margin-top-lg{CLASS_WARD}">
                    <select disabled="disabled" data-selected="{SEARCH_WARD}" name="ward" id="ward" class="form-control" data-default="{LANG.pleaseselect}" data-timestamp="{TIMESTAMP}">
                        <option value="0">{LANG.pleaseselect}</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-5">{LANG.doanhnghiep}:</label>
            <div class="col-md-19">
                <select id="businesstype" name="businesstype" class="form-control">
                    <option value="0">{LANG.pleaseselect}</option>
                    <!-- BEGIN: businesstype -->
                    <option value="{key_businesstype}" {sl_businesstype}>{val_businesstype}</option>
                    <!-- END: businesstype -->
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-5">{LANG.lvkd}:</label>
            <div class="col-md-19">
                <select name="lvkd" id="lvkd" class="form-control">
                    <option value="0">{LANG.pleaseselect}</option>
                    <!-- BEGIN: lvkd -->
                    <option value="{LVKD.key}"{LVKD.selected}>{LVKD.title}</option>
                    <!-- END: lvkd -->
                </select>
            </div>
        </div>
        <!-- BEGIN: fee -->
        <div class="form-group">
            <label class="control-label col-md-5">{LANG.fee_status}:</label>
            <div class="col-md-19">
                <select name="fee" id="fee" class="form-control">
                    <!-- BEGIN: fee_select -->
                    <option value="{FEE.key}"{FEE.selected}>{FEE.title}</option>
                    <!-- END: fee_select -->
                </select>
            </div>
        </div>
        <!-- END: fee -->
        <div class="form-group">
            <label class="control-label col-md-5">{LANG.order_by}:</label>
            <div class="col-md-19">
                <select name="sort_visual" id="sort" class="form-control">
                    <option value="default"{CURRENT_SORT_DEFAULT}>{LANG.sort_type_default}</option>
                    <option value="num_total_desc"{CURRENT_SORT_NUM_TOTAL}>{LANG.sort_type_num_total_desc}</option>
                    <option value="num_result_desc"{CURRENT_SORT_NUM_RESULT}>{LANG.sort_type_num_result_desc}</option>
                    <option value="num_false_desc"{CURRENT_SORT_NUM_FALSE}>{LANG.sort_type_num_false_desc}</option>
                    <option value="ability_point_desc"{CURRENT_SORT_ABILITY_POINT}>{LANG.sort_type_ability_point_desc}</option>
                    <option value="total_revenue"{CURRENT_SORT_TOTAL_REVENUE}>{LANG.sort_type_total_revenue}</option>
                </select>
                <input type="hidden" name="sort" id="sort_hidden" value="default">
            </div>
        </div>
        <div class="form-group margin-top-lg sort-sub-options" id="sort-role-options" style="display: none;">
            <label class="control-label col-md-5">{LANG.role_label}:</label>
            <div class="col-md-19">
                <select name="sort_role" id="sort_role" class="form-control">
                    <option value="all_roles">{LANG.all_roles}</option>
                    <option value="independent_contractor">{LANG.sort_type_independent_contractor}</option>
                    <option value="joint_contractor">{LANG.sort_type_joint_contractor}</option>
                </select>
            </div>
        </div>
        <div class="form-group margin-top-lg sort-sub-options" id="sort-type-options" style="display: none;">
            <label class="control-label col-md-5">{LANG.type_label}:</label>
            <div class="col-md-19">
                <select name="sort_type" id="sort_type" class="form-control">
                    <option value="all_types">{LANG.all_types}</option>
                    <option value="direct_contractor">{LANG.sort_type_direct_contractor}</option>
                    <option value="other_contractor">{LANG.sort_type_other_contractor}</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-5">{LANG.filter_ngay_phe_duyet}:</label>
            <div class="col-md-7">
                <input type="hidden" name="sfrom_business" value="{SFROM}" data-default="">
                <input type="hidden" name="sto_business" value="{STO}" data-default="">
                <input placeholder="dd/mm/yyyy - dd/mm/yyyy" class="form-control search_range_business" type="text" value="{SFROM} - {STO}">
            </div>
        </div>
        <!-- BEGIN: advanced_search -->
        <div class="form-group">
            <label class="control-label col-md-5">{LANG.bidding_start_time}:</label>
            <div class="col-md-7">
                <div class="input-group input-daterange" id="bid-year-range">
                    <div class="input-group-addon">{LANG.year}</div>
                    <input type="text" class="form-control" name="bid_from" id="bid_from" value="{BID_FROM}" data-default="">
                    <div class="input-group-addon"><i class="fa fa-arrow-right fa-fw"></i></div>
                    <input type="text" class="form-control" name="bid_to" id="bid_to" value="{BID_TO}" data-default="">
                </div>
            </div>
            <div class="col-md-10">
                    {LANG.note_search_x2}
            </div>
        </div>
        <!-- END: advanced_search -->
        <div class="row">
            <div class="col-md-19 col-md-offset-5">
                <input id="fsearch-submit" type="submit" value="{LANG.search}" class="btn btn-primary bussubmit_search"/>
                <input class="btn btn-default form-reset" type="button" value="{LANG.reset}" onclick="resetFormSearch();" />
            </div>
        </div>
    </div>
</form>

<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<script type="text/javascript">
    function updateSortValue() {
        var sortRoleValue = $("#sort_role").val();
        var sortTypeValue = $("#sort_type").val();
        var sortOptions = [];

        if (sortRoleValue !== 'all_roles') {
            sortOptions.push(sortRoleValue);
        }

        if (sortTypeValue !== 'all_types') {
            sortOptions.push(sortTypeValue);
        }

        if (sortOptions.length === 0) {
            $("#sort_hidden").val('total_revenue');
        } else {
            $("#sort_hidden").val(sortOptions.join(','));
        }
    }

    var formObject_business = $("[id=bussSearchBlock]");
    function buss_checkSearchForm(data) {
        var is_bussp = parseInt($('[name=is_bussp]').val());
        if (is_bussp === 1) {
            return false;
        } else {
            return true;
        }
    }
    function resetFormSearch() {
        $(".has-error", formObject_business).removeClass("has-error");
        $("[data-default]", formObject_business).each(function() {
            if ($(this).is("input[type=text], input[type=hidden]")) {
                $(this).val($(this).attr("data-default"))
            } else if ($(this).is("select")) {
                $(this).val($(this).attr("data-default"));
                $('#industry1').val('').trigger('change.select2');
                $('#nation').val('0').trigger('change');
                $('#province').val(-1).trigger('change.select2');
                $('#district').val(0).trigger('change.select2').parent().addClass('hidden');
                $('#ward').val(0).trigger('change.select2').parent().addClass('hidden');
                $('#businesstype').val(0);
                $('#lvkd').val(0);
                $('#fee').val(0);
                $('#sort').val('default').trigger('change');
                $("#sort_role").val('all_roles');
                $("#sort_type").val('all_types');
                $('select[name="sort_visual"] option:first').text("{LANG.sort_type_default}");
                $('.sort-sub-options').hide();
            }
        });

        $("#sort_hidden").val('default');
        bl_setDaterangepicker_business({ 'startDate' : '', 'endDate' : '' });
        parseInt($('input[name="is_advance"]').val()) && $(".btn-search-advance", formObject_business).trigger('click');
        $('html, body').animate({ scrollTop : formObject_business.offset().top }, 800);
    }

    function bl_setDaterangepicker_business(_options) {

        // Menu khoảng tìm kiếm
        var ranges_business = {};
        ranges_business['{LANG.this_month}'] = [moment().startOf('month'), moment().endOf('month')];
        ranges_business['{LANG.last_3_months}'] = [moment().subtract(3, 'months'), moment()];
        ranges_business['{LANG.last_6_months}'] = [ moment().subtract(6, 'months'), moment() ];
        ranges_business['{LANG.this_year}'] = [moment().startOf('year'), moment().endOf('year')];
        ranges_business['{LANG.none}'] = [null, null];

        var calendar_options_business = {
            showDropdowns: true,
            locale: {customRangeLabel: '{LANG.custom_range}', format: 'DD/MM/YYYY', help: ''},
            ranges: ranges_business,
            startDate: moment().subtract(14, 'days'),
            endDate: moment(),
            opens: 'right',
            drops: "auto",
            alwaysShowCalendars: false,
            maxDate: moment(),
            minYear: 1971
        };

        $.extend(calendar_options_business, _options);

        $(".search_range_business", formObject_business).daterangepicker(calendar_options_business, function (start, end, label) {
            if (!start.isValid() || !end.isValid()) {

                $("[name=sfrom_business]", formObject_business).val('');
                $("[name=sto_business]", formObject_business).val('')
            } else {
                $("[name=sfrom_business]", formObject_business).val(start.format('DD/MM/YYYY'));
                $("[name=sto_business]", formObject_business).val(end.format('DD/MM/YYYY'))
            }

        }).on("apply.daterangepicker", function (e, picker) {
            if (!picker.startDate.isValid() || !picker.endDate.isValid()) {
                picker.element.val('');
            } else {
                picker.element.val(picker.startDate.format(picker.locale.format) + ' - ' + picker.endDate.format(picker.locale.format));
            }
        }).on("showCalendar.daterangepicker", function (e, picker) {
            if (!picker.startDate.isValid() || !picker.endDate.isValid()) {
                bl_setDaterangepicker_business({
                    startDate: moment().format('DD/MM/YYYY'),
                    endDate: moment().format('DD/MM/YYYY')
                });
                $(".search_range_business").trigger('click');
            }
        })
    }

    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
        bl_setDaterangepicker_business({
            startDate: $("[name=sfrom_business]", formObject_business).val(),
            endDate: $("[name=sto_business]", formObject_business).val()
        });

        var sortConfig = {
            'default': [],
            'total_revenue': ['sort-role-options', 'sort-type-options'],
            'num_total_desc': [],
            'num_result_desc': [],
            'num_false_desc': [],
            'ability_point_desc': []
        };

        function updateSortOptions(sortValue) {
            $('.sort-sub-options').hide();

            if (sortConfig[sortValue]) {
                sortConfig[sortValue].forEach(function(optionId) {
                    $('#' + optionId).show();
                });
            }

            $("#sort_hidden").val(sortValue);
        }

        $("#sort").on('change', function() {
            var selectedValue = $(this).val();
            updateSortOptions(selectedValue);
            checkSelectTemplate("select[name=sort_visual]", 'default');

            if (selectedValue !== 'total_revenue') {
                $("#sort_role").val('all_roles');
                $("#sort_type").val('all_types');
            }
        });

        $("#sort_role, #sort_type").on('change', function() {
            updateSortValue();
            checkSelectTemplate($(this).attr('name') === 'sort_role' ? "select[name=sort_role]" : "select[name=sort_type]", $(this).attr('name') === 'sort_role' ? "all_roles" : "all_types");
        });

        $("#nation").on('change', function() {
            var selectedNation = $(this).val();
            var locationBlock = $("#bu-location-search").closest('.form-group');

            $('#province').val(-1).prop('disabled', false).find('option[value="-1"]').text("{LANG.pleaseselect}").end().trigger('change.select2');
            $('#district').val(0).prop('disabled', true).find('option[value="0"]').text("{LANG.pleaseselect}").end().trigger('change.select2').parent().addClass('hidden');
            $('#ward').val(0).prop('disabled', true).find('option[value="0"]').text("{LANG.pleaseselect}").end().trigger('change.select2').parent().addClass('hidden');

            // Nếu chọn quốc gia Việt Nam (id=244)
            if (selectedNation == '244') {
                locationBlock.show();
                $("#bu-location-search").addClass('show');

                $('#province').attr("data-flag", 1);
                $('#district').attr("data-flag", 1);
                $('#ward').attr("data-flag", 1);

                sleep_promise(100).then(() => {
                    $('#province').trigger('change.select2');
                });
            } else {
                locationBlock.hide();
                $("#bu-location-search").removeClass('show');
            }

            updateFormActionByNation();
        });

        var initialNation = $("#nation").val();
        if (initialNation == '244') {
            $("#bu-location-search").closest('.form-group').show();
            $("#bu-location-search").addClass('show');
        } else {
            $("#bu-location-search").closest('.form-group').hide();
            $("#bu-location-search").removeClass('show');
        }

        if ($("[name=sfrom_business]", formObject_business).val() == '' || $("[name=sto_business]", formObject_business).val() == '' || $(".search_range_business").val() == 'Invalid date - Invalid date') {
            $(".search_range_business").val('');
        }
        $(".search_range_business").on('change', function() {
            if ($("[name=sfrom_business]", formObject_business).val() == '' || $("[name=sto_business]", formObject_business).val() == '' || $(".search_range_business").val() == 'Invalid date - Invalid date') {
                $(".search_range_business").val('');
            }
        });

        <!-- BEGIN: advanced_search_js -->
        // Thời gian tham dự thầu
        $('#bid-year-range input').each(function () {
            $(this).datepicker({
                autoclose: true,
                format: "mm/yyyy",
                viewMode: "months",
                minViewMode: "months",
                startDate : '01/2010',
                endDate : new Date(),
                maxViewMode: 2
            });

            if ($(this).val() == '0') {
                $(this).val('');
            }
        });

        if ($('#bid_from').val() != '0') {
            let startDate = $('#bid_from').val();
            $('#bid_to').data('datepicker').setStartDate(startDate);
        }
        if ($('#bid_to').val() != '0') {
            let endDate = $('#bid_to').val();
            $('#bid_from').data('datepicker').setEndDate(endDate);
        }

        $('#bid_to').on('change', function () {
            let endDate = $('#bid_to').val();
            if (endDate != '') {
                $('#bid_from').data('datepicker').setEndDate(endDate);
            } else {
                $('#bid_from').data('datepicker').setEndDate(new Date());
            }
        });
        $('#bid_from').on('change', function () {
            let startDate = $('#bid_from').val();
            if (startDate != '') {
                $('#bid_to').data('datepicker').setStartDate(startDate);
            } else {
                $('#bid_to').data('datepicker').setStartDate('01/2010');
            }
        });
        <!-- END: advanced_search_js -->

        $(".btn-search-advance", formObject_business).click(function(a) {
            a.preventDefault();
            is_advance = $('input[name="is_advance"]', formObject_business).val();
            if(parseInt(is_advance) == 1) {
                $(".advance-search-content", formObject_business).not(":hidden") && $(".advance-search-content", formObject_business).slideUp();
                $('input[name="is_advance"]', formObject_business).val(0);
                $(".btn-search-advance em", formObject_business).removeClass($(".btn-search-advance", formObject_business).data("icon-search-advance")).addClass($(".btn-search-advance", formObject_business).data("icon-search-simple"));
                $(".btn-search-advance .txt", formObject_business).text($(".btn-search-advance", formObject_business).data("search-advance"))
            } else {
                $(".advance-search-content", formObject_business).is(":hidden") && $(".advance-search-content", formObject_business).slideDown();
                $('input[name="is_advance"]', formObject_business).val(1);
                $(".btn-search-advance em", formObject_business).removeClass($(".btn-search-advance", formObject_business).data("icon-search-simple")).addClass($(".btn-search-advance", formObject_business).data("icon-search-advance"));
                $(".btn-search-advance .txt", formObject_business).text($(".btn-search-advance", formObject_business).data("search-simple"))
            }
        });
        var $bussSearchBlock = $('#bussSearchBlock');
        // Hàm lấy slug của các địa điểm đã chọn
        function getSelectedLocationSlug() {
            var provinceSlug = $bussSearchBlock.find('#province option:selected').data('alias');
            var districtSlug = $bussSearchBlock.find('#district option:selected').data('alias');
            var wardSlug = $bussSearchBlock.find('#ward option:selected').data('alias');

            return { provinceSlug, districtSlug, wardSlug };
        }

        // Hàm thiết lập URL hành động của form
        function setFormActionbu(actionUrlB,is_bussp) {
            $bussSearchBlock.attr("action", actionUrlB);
            $('input[name="is_bussp"]').val(is_bussp);
        }

        // Hàm lấy giá trị của tỉnh/thành phố đã chọn
        function getSelectedLocation() {
            var province = $bussSearchBlock.find('#province option:selected').val();
            return province;
        }

        // Hàm lấy giá trị của trường form dựa trên name
        function getFormValueb(name) {
            return $bussSearchBlock.find('[name=' + name + ']').val();
        }

        // Hàm lấy giá trị của ngành nghề đã chọn
        function getSelectedIndustry() {
            var industry = $bussSearchBlock.find('#industry1 option:selected').val();
            return industry;
        }
        // Hàm lấy giá trị của loại hình doanh nghiệp đã chọn
        function getSelectedBusinessType() {
            var businessType = $bussSearchBlock.find('#businesstype option:selected').val();
            return businessType;
        }
        // Hàm lấy giá trị của lĩnh vực kinh doanh đã chọn
        function getSelectedBusinessField() {
            var businessField = $bussSearchBlock.find('#lvkd option:selected').val();
            return businessField;
        }

        // Hàm lấy giá trị của tình trạng nộp phí đã chọn
        function getSelectedFeeStatus() {
            var feeElement = $bussSearchBlock.find('#fee');
            if (feeElement.length > 0) {  // Kiểm tra xem #fee có tồn tại không
                return feeElement.find('option:selected').val();
            } else {
                return null;  // Hoặc có thể trả về một giá trị mặc định khác
            }
        }

        // Hàm lấy giá trị của khoảng thời gian phê duyệt đã chọn
        function getApprovalDateRange() {
            var sfromBusiness = $bussSearchBlock.find('[name="sfrom_business"]').val();
            var stoBusiness = $bussSearchBlock.find('[name="sto_business"]').val();
            return { sfromBusiness, stoBusiness };
        }

        // Hàm cập nhật URL hành động của form dựa trên địa điểm đã chọn và từ khóa
        function updateFormActionByLocation() {
            var slugs = getSelectedLocationSlug();  // Lấy slug của các địa điểm
            var province = getSelectedLocation();   // Lấy giá trị của tỉnh/thành phố
            var id_province = $bussSearchBlock.find('#province option:selected').val(); // Lấy giá trị của quận/huyện
            var district = $bussSearchBlock.find('#district option:selected').val(); // Lấy giá trị của quận/huyện
            var ward = $bussSearchBlock.find('#ward option:selected').val();         // Lấy giá trị của phường/xã
            var tuq = getFormValueb('q');               // Lấy giá trị của trường q
            var industry = getSelectedIndustry();   // Lấy giá trị của ngành nghề
            var businessType = getSelectedBusinessType(); // Lấy giá trị của loại hình doanh nghiệp
            var businessField = getSelectedBusinessField(); // Lấy giá trị của lĩnh vực kinh doanh
            var feeStatus = getSelectedFeeStatus(); // Lấy giá trị của tình trạng nộp phí
            //alert(feeStatus);
            var { sfromBusiness, stoBusiness } = getApprovalDateRange(); // Lấy giá trị của khoảng thời gian phê duyệt
            var sortValue = $("#sort_hidden").val();
            let actionUrlB = $bussSearchBlock.data("bussaction"); // Giá trị mặc định là URL tìm kiếm cơ bản
            // Kiểm tra nếu tỉnh/thành phố đã được chọn và từ khóa rỗng
            if (province !== "-1" && tuq === '' && industry === "" && businessType === "0" && businessField === "0" && (feeStatus ? feeStatus === "0" : true) && sfromBusiness === "" && stoBusiness === "" && sortValue === "default") {
                if (district === "0" && ward === "0") {
                    // Chỉ chọn province
                    actionUrlB = `${$bussSearchBlock.data("bussaction1")}T-${encodeURIComponent(slugs.provinceSlug)}-${encodeURIComponent(id_province)}`;
                } else if (district !== "0" && ward === "0") {
                    // Chọn province và district, nhưng không chọn ward
                    actionUrlB = `${$bussSearchBlock.data("bussaction1")}H-${encodeURIComponent(slugs.districtSlug)}-${encodeURIComponent(district)}`;
                } else if (district !== "0" && ward !== "0") {
                    // Chọn cả province, district và ward
                    actionUrlB = `${$bussSearchBlock.data("bussaction1")}X-${encodeURIComponent(slugs.wardSlug)}-${encodeURIComponent(ward)}`;
                }
                setFormActionbu(actionUrlB, 1);
            } else {
                setFormActionbu(actionUrlB, 0);
            }
        }

        // Hàm xử lý Quốc gia
        function updateFormActionByNation() {
            var nation = $('#nation option:selected').val();
            var nationAlias = $('#nation option:selected').data('alias');
            var tuq = getFormValueb('q');
            var industry = getSelectedIndustry();
            var businessType = getSelectedBusinessType();
            var businessField = getSelectedBusinessField();
            var feeStatus = getSelectedFeeStatus();
            var { sfromBusiness, stoBusiness } = getApprovalDateRange();
            var sortValue = $("#sort_hidden").val();
            let actionUrlB = $bussSearchBlock.data("bussaction");

            // Kiểm tra nếu chỉ chọn quốc gia và không có điều kiện nào khác
            if (nation !== "0" && tuq === '' && industry === "" && businessType === "0" && businessField === "0" && (feeStatus ? feeStatus === "0" : true) && sfromBusiness === "" && stoBusiness === "" && sortValue === "default") {
                actionUrlB = `${$bussSearchBlock.data("bussaction3")}${encodeURIComponent(nationAlias)}`;
                setFormActionbu(actionUrlB, 1);
            } else {
                setFormActionbu(actionUrlB, 0);
            }
        }

        // Gán sự kiện cho các thay đổi trong select box và input
        $bussSearchBlock.find('#province').on('change', updateFormActionByLocation);
        $bussSearchBlock.find('#district').on('change', updateFormActionByLocation);
        $bussSearchBlock.find('#ward').on('change', updateFormActionByLocation);
        $bussSearchBlock.find('[name=q]').on('input', updateFormActionByLocation);
        $bussSearchBlock.find('#industry1').on('change', updateFormActionByLocation);
        $bussSearchBlock.find('#businesstype').on('change', updateFormActionByLocation);
        $bussSearchBlock.find('#fee').on('change', updateFormActionByLocation);
        $bussSearchBlock.find('#lvkd').on('change', updateFormActionByLocation);
        $bussSearchBlock.find('.search_range_business').on('change', updateFormActionByLocation);
        $bussSearchBlock.find('#nation').on('change', function() {
            checkSelectTemplate("select[name=nation]", '0');
            updateFormActionByNation();
        });
        $bussSearchBlock.find('#sort').on('change', function() {
            updateFormActionByLocation();
            updateFormActionByNation();
        });
        $('.bussubmit_search').click(function(e) {
            var is_bussp = parseInt($('[name=is_bussp]').val());
            var actionUrlB = $bussSearchBlock.attr("action");
            if (is_bussp === 1) {
                window.location.href = actionUrlB;
            }
        });
    });
</script>
<script type="text/javascript">
//<![CDATA[

$("#bussSearchBlock").submit(function(e) {
    var industry1 = $("select[name=industry1]").val();
    var industry2 = $("select[name=industry2]").val();
    var industry3 = $("select[name=industry3]").val();
    var industry4 = $("select[name=industry4]").val();
    var province = $("select[name=province]").val();
    var nation = $("select[name=nation]").val();
    var businesstype = $("select[name=businesstype]").val();
    var district = $("select[name=district]").val();
    var ward = $("select[name=ward]").val();
    var keyword = $("input[name=q]").val();
    var lvkd = $('#lvkd').val();
    var fee = $("#fee").val();
    var sfrom_business = $('input[name="sfrom_business"]').val();
    var sto_business = $('input[name="sto_business"]').val();
    var mainSort = $("#sort").val();

    if (mainSort === 'total_revenue') {
        updateSortValue();
    }

    if ($('#bid_from').length == 0 && $('#bid_to').length == 0) {
        if (industry1 == "" && province == "-1" && nation == "0" && businesstype == "0" && keyword == "" && (fee ? fee == "0" : true) && lvkd == "0" && sfrom_business == "" && sto_business == "" && $("#sort_hidden").val() == "default") {
            alert('{LANG.chondk}');
            e.preventDefault();
        }
    }
});

//]]>
</script>
<script type="text/javascript">
    // chuyển default option vui lòng chọn thành bỏ chọn khi select form
    function checkSelectTemplate(selector, value) {
        var $this_select = $(selector);
        var selectedValue = $this_select.val();

        if (selector === "select[name=sort_role]" && selectedValue === "all_roles") {
            $this_select.find('option:first').text("{LANG.all_roles}");
            return;
        }

        if (selector === "select[name=sort_type]" && selectedValue === "all_types") {
            $this_select.find('option:first').text("{LANG.all_types}");
            return;
        }

        if (selector === "select[name=sort_visual]" && selectedValue === "default") {
            $this_select.find('option:first').text("{LANG.sort_type_default}");
        } else if (selectedValue == value) {
            $this_select.find('option:first').text("{LANG.pleaseselect}");
        } else {
            $this_select.find('option:first').text("{LANG.deselect}");
        }
    }

    function checkSelect2Template(selector, value, e = null) {
        var $select = $(selector);
        var selectedValue = $select.val();
        if (e != null) {
            $select.select2({
                templateResult: updateSelectTemplate,
                templateSelection: updateSelectTemplate
            });
        }

        if (selectedValue == value) {
            $select.find('option:first').text("{LANG.pleaseselect}");
            if (e != null) {
                $select.trigger('change');
            }
        } else {
            $select.find('option:first').text("{LANG.deselect}");
            if (e != null) {
                $select.trigger('change');
            }
        }
    }

    function updateSelectTemplate(data) {
        var $option = $(data.element);

        return $option.text();
    }

    function sleep_promise(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    function initSortUIFromURL() {
        var urlParams = new URLSearchParams(window.location.search);
        var sortValue = urlParams.get('sort') || 'default';

        $('.sort-sub-options').hide();
        $("#sort_role").val('all_roles');
        $("#sort_type").val('all_types');

        if (sortValue.includes(',')) {
            var sortOptions = sortValue.split(',');
            $("#sort").val('total_revenue');

            $('#sort-role-options').show();
            $('#sort-type-options').show();

            sortOptions.forEach(function(option) {
                if (['independent_contractor', 'joint_contractor'].includes(option)) {
                    $("#sort_role").val(option);
                } else if (['direct_contractor', 'other_contractor'].includes(option)) {
                    $("#sort_type").val(option);
                }
            });

            $("#sort_hidden").val(sortValue);
        } else {
            if (['independent_contractor', 'joint_contractor'].includes(sortValue)) {
                $("#sort").val('total_revenue');
                $('#sort-role-options, #sort-type-options').show();
                $("#sort_role").val(sortValue);
            } else if (['direct_contractor', 'other_contractor'].includes(sortValue)) {
                $("#sort").val('total_revenue');
                $('#sort-role-options, #sort-type-options').show();
                $("#sort_type").val(sortValue);
            } else {
                $("#sort").val(sortValue);

                if (sortValue === 'total_revenue') {
                    $('#sort-role-options, #sort-type-options').show();
                }
            }

            $("#sort_hidden").val(sortValue);
        }
    }

    $(document).ready(function() {
        $("select[name='industry1']").select2();
        // Doanh nghiệp, Lĩnh vực kinh doanh
        $("select[name=businesstype], select[name=lvkd]").on('change', function() {
            checkSelectTemplate("select[name=businesstype]", '0');
            checkSelectTemplate("select[name=lvkd]", '0');
        });

        // Xếp thứ tự
        $("select[name='sort']").on('change', function() {
            checkSelectTemplate("select[name=sort_visual]", 'default');
        });

        // Ngành nghề
        $("select[name='industry1']").on('select2:select', function(e) {
            checkSelect2Template("select[name='industry1']", '', e);
        });

        $("#industry2").on('change', "select" , function(e) {
            checkSelectTemplate("select[name='industry2']", '');
        });
        $("#industry3").on('change', "select" , function(e) {
            checkSelectTemplate("select[name='industry3']", '');
        });
        $("#industry4").on('change', "select" , function(e) {
            checkSelectTemplate("select[name='industry4']", '');
        });

        // Quốc gia
        $("#nation").select2({
            width: '100%',
        });
        $("#nation").on('select2:select', function(e) {
            checkSelect2Template("select[name='nation']", '0', e);
        });

        // Địa điểm
        $("select[name='province']").on('select2:select', function(e) {
            $(this).attr("data-flag", 0);
            checkSelect2Template("select[name='province']", '-1', e);
            var val_district = $("select[name='district']").data('selected');
            sleep_promise(100).then(() => {
                if (val_district == '0') {
                    $("select[name='district']").find('option:first').text("{LANG.pleaseselect}");
                    $("select[name='district']").trigger('change.select2');
                } else {
                    $("select[name='district'] option[value='0']").text("{LANG.deselect}");
                    $("select[name='district']").trigger('change.select2');

                    var val_ward = $("select[name='ward']").data('selected');
                    sleep_promise(100).then(() => {
                        if (val_ward == '0') {
                            $("select[name='ward']").find('option:first').text("{LANG.pleaseselect}");
                            $("select[name='ward']").trigger('change.select2');
                        } else {
                            $("select[name='ward'] option[value='0']").text("{LANG.deselect}");
                            $("select[name='ward']").trigger('change.select2');
                        }
                    });
                }
            });
        });

        $("select[name='district']").on('select2:select', function(e) {
            $(this).attr("data-flag", 0);
            checkSelect2Template("select[name='district']", '0', e);
        });
        $("select[name='ward']").on('select2:select', function(e) {
            checkSelect2Template("select[name='ward']", '0', e);
        });

        initSortUIFromURL();
    });

    $(window).on('load', function() {
        // chuyển default option vui lòng chọn thành bỏ chọn khi search bằng link trực tiếp
        checkSelectTemplate("select[name=businesstype]", '0');
        checkSelectTemplate("select[name=lvkd]", '0');

        checkSelect2Template("select[name='industry1']", '');
        checkSelectTemplate("select[name='industry2']", '');
        checkSelectTemplate("select[name='industry3']", '');
        checkSelectTemplate("select[name='industry4']", '');

        checkSelect2Template("select[name='province']", '-1');
        checkSelect2Template("select[name='district']", '0');
        checkSelect2Template("select[name='ward']", '0');
    });
</script>
<!-- END: main -->
