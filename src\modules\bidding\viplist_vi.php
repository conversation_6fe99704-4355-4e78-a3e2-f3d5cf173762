<?php

/**
 * @Project NUKEVIET 5.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 2/7/2020, 21:42
 */

$viplist = [];
$viplist['vip69'] = [
  'code' => 'vip69',
  'number' => 69,
  'name' => 'Gói SIEUVIP',
  'subtitle' => 'Siêu công cụ dành cho nhà thầu Việt Nam',
  'price' => [49000000, 'Năm'],
    'old_price' => [103000000, 'Năm'],
  'desc' => [
    'Tất cả trong 1 (VIP1 + VIP2 + VIP3 + X1 + T0 + VIP8).',
    'VIP1: “Săn” thông báo mời thầu mua sắm công và mua sắm tư nhân.',
    'VIP2: “Săn” kế hoạch lựa chọn nhà thầu mua sắm công và mua sắm tư nhân.',
    'VIP3: <PERSON><PERSON><PERSON> vị, phân tích đối thủ cạnh tranh và bên mời thầu.',
    'T0: Tải không giới hạn hồ sơ thầu trên mọi trình duyệt.',
    'X1: Xuất kết quả lựa chọn nhà thầu ra Excel.',
    'VIP8: “Săn” yêu cầu báo giá mua sắm công',
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/tinh-nang-bang-gia-goi-sieuvip.html'],
    'new_icon' => 0
];
$viplist['vip77'] = [
    'code' => 'vip77',
    'number' => 77,
    'name' => 'Gói T0',
    'subtitle' => 'Tải không giới hạn hồ sơ thầu trên mọi trình duyệt',
    'price' => [1000000, 'Năm'],
    'old_price' => [0, 'Năm'],
    'desc' => [
        'Tải về các hồ sơ mời thầu bị Hệ thống mời thầu quốc gia ngăn chặn tiếp cận trên các trình duyệt hiện đại như Google Chrome, FireFox, Opera, Safari (không cần sử dụng Internet Explorer).',
        'Chi phí trọn gói, không bị phát sinh theo dung lượng hồ sơ hoặc số lượng hồ sơ thầu muốn tải.',
        'Có thể yêu cầu phần mềm hỗ trợ tải về những hồ sơ mời thầu đã hết hạn.'
    ],
    'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/tinh-nang-bang-gia-goi-T0.html']
];

$viplist['vip1'] = [
  'code' => 'vip1',
  'number' => 1,
  'name' => 'VIP 1',
  'subtitle' => '“Săn” thông báo mời thầu mua sắm công và mua sắm tư nhân',
  'price' => [9999000, 'Năm'],
  'old_price' => [0, 'Năm'],
  'desc' => [
    'Tìm kiếm thông minh (tìm theo Số TBMT, tên gói thầu, bên mời thầu, Tên dự án, Nội dung chính của gói thầu) trong một ô tìm kiếm duy nhất.',
    'Tạo bộ lọc tìm kiếm thông tin thầu theo yêu cầu và năng lực nhà thầu.',
    'Nhận email <a href="/thongbao/moithau/" target="_blank"><strong>thông báo về gói thầu</strong></a> mới được đăng tải (TBMT) phù hợp điều kiện tìm kiếm mà bạn thiết lập trên phần mềm DauThau.info.',
    '“Soi” giá gói thầu của bất kỳ gói thầu nào, kể cả các gói thầu bị ẩn giá trong thông báo mời thầu (kể cả báo đấu thầu)',
    'Tìm kiếm và xem toàn bộ thông tin chi tiết (gồm cả cơ bản và nâng cao) của thông tin dự án, TBMT tư nhân trên DauThau.Net'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/bang-gia-goi-vip1-vip2.html']
];

$viplist['vip2'] = [
  'code' => 'vip2',
  'number' => 2,
  'name' => 'VIP 2',
  'subtitle' => '“Săn” kế hoạch lựa chọn nhà thầu mua sắm công và mua sắm tư nhân',
  'price' => [9999000, 'Năm'],
  'old_price' => [0, 'Năm'],
  'desc' => [
    'Tìm kiếm thông minh (tìm theo Số KHLCNT, tên KHLCNT, tên dự án, bên mời thầu, chủ đầu tư) trong một ô tìm kiếm duy nhất.',
    'Tạo bộ lọc tìm kiếm thông tin thầu theo yêu cầu và năng lực nhà thầu.',
    'Nhận email thông báo về <a href="/listplan/" target="_blank"><strong>kế hoạch lựa chọn nhà thầu</strong></a> mới (KHLCNT) phù hợp điều kiện tìm kiếm mà bạn thiết lập trên phần mềm DauThau.info.',
    '“Soi” giá gói thầu của bất kỳ gói thầu nào, kể cả các gói thầu bị ẩn giá trong thông báo mời thầu (kể cả báo đấu thầu)',
    'Tìm kiếm và xem toàn bộ thông tin chi tiết (gồm cả cơ bản và nâng cao) của thông tin dự án, KHLCN tư nhân trên DauThau.Net'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/bang-gia-goi-vip1-vip2.html']
];

$viplist['vip3'] = [
  'code' => 'vip3',
  'number' => 3,
  'name' => 'VIP 3',
  'subtitle' => 'Đọc vị, phân tích đối thủ cạnh tranh và bên mời thầu',
    'price' => [10000000, 'Năm'],
  'old_price' => [0, 'Năm'],
  'desc' => [
    'Phân tích quan hệ giữa nhà thầu với các bên mời thầu: Xem nhà thầu tham gia những gói thầu nào? Hay quan hệ với các bên mời thầu nào?',
    'Phân tích quan hệ giữa bên mời thầu với các nhà thầu: Danh sách các nhà thầu từng tham gia dự thầu? Tổng số gói thầu mà từng nhà thầu tham gia, số lượng gói thầu trúng, số lượng gói thầu trượt...',
    '“Soi” quan hệ giữa các nhà thầu với nhau: Từng đấu với nhau, hoặc cùng liên danh (liên danh chính, phụ).'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/tinh-nang-bang-gia-goi-vip3.html']
];
$viplist['vip31'] = [
    'code' => 'vip31',
    'number' => 31,
    'name' => 'VIP 3 Plus',
    'subtitle' => 'Tải không giới hạn file báo cáo PDF của các nhà thầu',
    'price' => [34999000, 'Năm'],
    'old_price' => [34999000, 'Năm'],
    'desc' => [
        'Phân tích quan hệ giữa nhà thầu với các bên mời thầu: Xem nhà thầu tham gia những gói thầu nào? Hay quan hệ với các bên mời thầu nào?',
        'Phân tích quan hệ giữa bên mời thầu với các nhà thầu: Danh sách các nhà thầu từng tham gia dự thầu? Tổng số gói thầu mà từng nhà thầu tham gia, số lượng gói thầu trúng, số lượng gói thầu trượt...',
        '“Soi” quan hệ giữa các nhà thầu với nhau: Từng đấu với nhau, hoặc cùng liên danh (liên danh chính, phụ).',
        'Tải không giới hạn báo cáo PDF "Kết quả hoạt động của doanh nghiệp tại thị trường Mua sắm công"'
    ],
    'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/tinh-nang-bang-gia-goi-vip3-plus.html'],
    'new_icon' => 1
];

$viplist['vip99'] = [
  'code' => 'vip99',
  'number' => 99,
  'name' => 'VIEWEB',
  'subtitle' => '“Tra cứu” thông tin thầu',
    'price' => [1600000, 'Năm'],
  'old_price' => [0, 'Năm'],
  'desc' => [
    'Tìm kiếm thông minh (với TBMT: tìm theo Số TBMT, tên gói thầu, bên mời thầu, Tên dự án, Nội dung chính của gói thầu, Địa điểm nhận hồ sơ, Địa điểm thực hiện gói thầu; Với KHLCNT: tìm theo số, tên KHLCNT, tên dự án, bên mời thầu, chủ đầu tư) trong một ô tìm kiếm duy nhất.',
    'Tạo bộ lọc tìm kiếm thông tin thầu theo yêu cầu và năng lực nhà thầu.',
    'Xem đầy đủ thông tin chi tiết của gói thầu theo thông báo mời thầu.'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/bang-gia-goi-vieweb.html']
];

$viplist['vip19'] = [
  'code' => 'vip19',
  'number' => 19,
  'name' => 'PRO 1',
  'subtitle' => 'Phần mềm săn thông tin thầu cơ bản',
  'price' => [7999000, 'Năm'],
  'old_price' => [7999000, 'Năm'],
  'desc' => [
    'Tìm kiếm thông minh (tìm theo Số TBMT, tên gói thầu, bên mời thầu, Tên dự án, Nội dung chính của gói thầu) trong một ô tìm kiếm duy nhất.',
    'Tạo 2 bộ lọc tìm kiếm thông tin thầu theo yêu cầu và năng lực nhà thầu.',
    'Nhận email <a href="/thongbao/moithau/" target="_blank"><strong>thông báo về gói thầu</strong></a> mới được đăng tải (TBMT) phù hợp điều kiện tìm kiếm mà bạn thiết lập trên phần mềm DauThau.info.',
    'Xem toàn bộ thông tin chi tiết (gồm cả cơ bản và nâng cao) của thông tin dự án, TBMT, KHLCN tư nhân trên DauThau.Net'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/bang-gia-goi-vieweb.html'],
  'new_icon' => 1
];

$viplist['vip7'] = [
  'code' => 'vip7',
  'number' => 7,
  'name' => 'VIP 7',
  'subtitle' => 'Phần mềm săn kết quả lựa chọn nhà thầu',
  'price' => [10000000, 'Năm'],
  'old_price' => [10000000, 'Năm'],
  'desc' => [
      'Tạo bộ lọc tìm kiếm thông tin thầu theo yêu cầu và năng lực nhà thầu.',
      'Nhận email thông báo về kết quả lựa chọn nhà thầu phù hợp điều kiện tìm kiếm mà bạn thiết lập trên phần mềm DauThau.info.'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/bang-gia-goi-vip7.html'],
  'new_icon' => 1
];

$viplist['vip5'] = [
  'code' => 'vip5',
  'number' => 5,
  'name' => 'VIP 5',
  'subtitle' => '“Săn” thông báo mời thầu vốn ngoài ngân sách',
    'price' => [********, 'Năm'],
  'old_price' => [0, 'Năm'],
  'desc' => [
    'Chuyên “săn” các Thông báo mời thầu ngoài phạm vi điều chỉnh của luật đấu thầu như các gói thầu sử dụng nguồn tiền từ nguồn ngân sách ODA, Ngân hàng thế giới (WorldBank), Ngân hàng Phát triển châu Á (ADB)… được đăng tải lên mạng đấu thầu quốc gia.',
    'Tùy ý thiết lập bộ lọc tìm kiếm thông tin thầu theo yêu cầu của nhà thầu.',
    'Nhận email thông báo về <strong>thông báo mời thầu</strong> mới theo điều kiện tìm kiếm mà bạn thiết lập.'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/tinh-nang-bang-gia-goi-vip5.html']
];

$viplist['vip11'] = [
  'code' => 'vip11',
  'number' => 11,
  'name' => 'VIP 1 Quốc tế',
  'subtitle' => '“Săn” thông báo mời thầu Quốc tế',
  'price' => [********, 'Năm'],
  'old_price' => [0, 'Năm'],
  'desc' => [
    'Chuyên “săn” các thông báo mời thầu Quốc tế trên hệ thống mời thầu quốc gia.',
    'Tạo bộ lọc tìm kiếm thông tin các gói thầu Quốc tế theo yêu cầu và năng lực nhà thầu.',
    'Nhận email <strong>thông báo về gói thầu Quốc tế</strong> (TBMT) mới được đăng tải phù hợp điều kiện tìm kiếm mà bạn thiết lập trên phần mềm DauThau.info.'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/bang-gia-goi-vip1qt-vip2qt.html']
];

$viplist['vip21'] = [
  'code' => 'vip21',
  'number' => 21,
  'name' => 'VIP 2 Quốc tế',
  'subtitle' => '“Săn” kế hoạch lựa chọn nhà thầu Quốc tế',
  'price' => [********, 'Năm'],
  'old_price' => [0, 'Năm'],
  'desc' => [
    'Chuyên “săn” các Kế hoạch lựa chọn nhà thầu Quốc tế trên hệ thống mời thầu quốc gia.',
    'Tạo bộ lọc tìm kiếm thông tin thầu theo yêu cầu và năng lực nhà thầu.',
    'Nhận email thông báo về <strong>kế hoạch lựa chọn nhà thầu Quốc tế</strong> (KHLCNT) mới được đăng tải phù hợp điều kiện tìm kiếm mà bạn thiết lập trên phần mềm DauThau.info.'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/bang-gia-goi-vip1qt-vip2qt.html']
];

$viplist['vip4'] = [
  'code' => 'vip4',
  'number' => 4,
  'name' => 'VIP 4',
  'subtitle' => 'Cung cấp thông tin lựa chọn nhà đầu tư',
    'price' => [********, 'Năm'],
  'old_price' => [0, 'Năm'],
  'desc' => [
    'Gồm các thông tin: Công bố danh mục dự án, thông báo mời thầu, thông báo mời sơ tuyển, kế hoạch lựa chọn nhà đầu tư, kết quả sơ tuyển, kết quả lựa chọn nhà đầu tư.',
    'Tìm kiếm (tìm kiếm nâng cao đối với TBMT, KH, KQ). Khi tìm cần lựa chọn tìm kiếm theo hình thức lựa chọn nhà thầu hay lựa chọn nhà đầu tư, mỗi hình thức sẽ có các thông tin khác nhau.',
    'Cho phép tạo bộ lọc theo từng thông tin trên. Gửi mail theo bộ lọc, mỗi mail sẽ là một loại thông tin trên.'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/tinh-nang-bang-gia-goi-vip4.html']
];

$viplist['vip6'] = [
    'code' => 'vip6',
    'number' => 6,
    'name' => 'VIP 6',
    'subtitle' => 'Phần mềm săn tài sản đấu giá',
    'price' => [9999999, 'Năm'],
    //'old_price' => [9999000, 'Năm'],
    'desc' => [
        'Tìm kiếm thông minh (tìm theo tên tài sản, người có tài sản, địa chỉ) trong một ô tìm kiếm duy nhất.',
        'Tạo bộ lọc tìm kiếm tài sản theo yêu cầu và khả năng.',
        'Nhận email <a href="/thongbao/moithau/" target="_blank"><strong>thông báo về tài sản đấu giá</strong></a> mới được đăng tải  phù hợp điều kiện tìm kiếm mà bạn thiết lập trên phần mềm DauThau.info.'
    ],
    'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/tinh-nang-bang-gia-goi-vip6.html'],
    'new_icon' => 0
];
$viplist['vip88'] = [
    'code' => 'vip88',
    'number' => 88,
    'name' => 'Gói X1',
    'subtitle' => 'Xuất kết quả lựa chọn nhà thầu ra Excel',
    'price' => [18000000, 'Năm'],
    'price2' => [20000000, 'Lần'],
    'desc' => [
        'Xuất toàn bộ dữ liệu trúng thầu ra file excel, để từ đó thống kê dữ liệu trúng thầu, tổng giá trị gói thầu của các nhà thầu đã trúng... cùng nhiều thông tin khác có trong thông báo mời thầu.',
        'Các nhà thầu sẽ được cấp tài khoản để có quyền trích xuất 24/24h bất kỳ khi nào cần.',
        'Dữ liệu trích xuất, toàn bộ dữ liệu tính đến thời điểm hiện tại.'
    ],
    'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/tinh-nang-bang-gia-goi-x1.html']
];
$viplist['vip89'] = [
    'code' => 'vip89',
    'number' => 89,
    'name' => 'Gói X2',
    'subtitle' => 'Xuất data nhà thầu ra excel',
    'price' => [
        15000000,
        'Năm'
    ],
    //'old_price' => [15000000, 'Năm'],
    'desc' => [
        'Xuất kết quả tìm kiếm nhà thầu ở danh sách nhà thầu trên hệ thống đấu thầu quốc gia',
        'Bao gồm các thông tin cơ bản của nhà thầu',
        'Tính năng nâng cao: Tổng số lượng gói thầu tham gia, Tổng giá trị trúng thầu, Tổng giá trị phát sinh bảo lãnh dự thầu, Tổng giá gói thầu, Tổng giá dự thầu trong từng năm của mỗi nhà thầu',
        'Mặc định 10 điểm/ thông tin 10 nhà thầu (tính theo block 10 nhà thầu 1 lần).'
    ],
    'detail' => [
        'Xem chi tiết bảng giá và chức năng',
        '/page/tinh-nang-bang-gia-goi-x2.html'
    ],
    'new_icon' => 1
];
$viplist['vip66'] = [
  'code' => 'vip66',
  'number' => 66,
  'name' => 'Gói API VIP',
  'subtitle' => 'Phần mềm truy vấn dữ liệu thầu theo thời gian thực',
  'price' => [
      7500000,
      'Năm'
  ],
  //'old_price' => [15000000, 'Năm'],
  'desc' => [
      'API thông báo mời thầu.',
      'API kết quả lựa chọn nhà thầu.',
      'Phí đăng ký, thanh toán định kì hàng năm: 15.000.000 VNĐ/năm.',
      'Phí lưu lượng: 1 điểm/ bản ghi thông tin thầu.'
  ],
  'detail' => [
      'Xem chi tiết bảng giá và chức năng',
      '/page/tinh-nang-bang-gia-goi-api.html'
  ],
  'new_icon' => 1
];

$viplist['vip68'] = [
  'code' => 'vip68',
  'number' => 68,
  'name' => 'Gói API PRO',
  'subtitle' => 'Phần mềm truy vấn dữ liệu thầu theo thời gian thực',
  'price' => [
      0,
      'Năm'
  ],
  'desc' => [
      'API thông báo mời thầu.',
      'API kết quả lựa chọn nhà thầu.',
      'Phí đăng ký, thanh toán định kì hàng năm: 0 VNĐ/Năm.',
      'Phí lưu lượng: 2 điểm/ bản ghi thông tin thầu.'
  ],
  'detail' => [
      'Xem chi tiết bảng giá và chức năng',
      '/page/tinh-nang-bang-gia-goi-api.html'
  ],
  'new_icon' => 1
];

$viplist['vip100'] = [
  'code' => 'vip100',
  'number' => 100,
  'name' => 'Gói T100',
  'subtitle' => 'Tải 100 báo cáo PDF "Kết quả hoạt động của doanh nghiệp tại thị trường mua sắm công"',
  'price' => [
      ********,
      'Năm'
  ],
  'desc' => [
      'Tải 100 lượt báo cáo của các doanh nghiệp là nhà thầu có hoạt động tại thị trường mua sắm công.',
      'Xuất riêng báo cáo phân tích lịch sử đấu thầu của nhà thầu/ bên mời thầu từ 2010 đến nay (update theo thời gian thực).',
      'Phân tích đa chiều, nhiều góc nhìn từ tổng giá trị gói thầu cho đến quan hệ với các bên liên quan.',
      'Dữ liệu được trực quan hóa bằng các bảng biểu dễ nhìn.'
  ],
  'detail' => [
      'Xem chi tiết bảng giá và chức năng',
      '/page/tinh-nang-bang-gia-goi-t100.html'
  ],
  'new_icon' => 1
];

$viplist['vip8'] = [
  'code' => 'vip8',
  'number' => 8,
  'name' => 'VIP 8',
  'subtitle' => '“Săn” yêu cầu báo giá mua sắm công',
  'price' => [7999000, 'Năm'],
  'old_price' => [0, 'Năm'],
  'desc' => [
    'Tìm kiếm thông minh (tìm theo Số YCBG, tên gói thầu, Tên dự án, Tên yêu cầu báo giá) trong một ô tìm kiếm duy nhất.',
    'Tạo bộ lọc tìm kiếm thông tin yêu cầu báo giá theo yêu cầu.',
    'Nhận email <a href="/request-quote/" target="_blank"><strong>thông báo về yêu cầu báo giá</strong></a> mới được đăng tải (YCBG) phù hợp điều kiện tìm kiếm mà bạn thiết lập trên phần mềm DauThau.info.',
    'Tìm kiếm và xem toàn bộ thông tin chi tiết (gồm cả cơ bản và nâng cao) của Yêu cầu báo giá'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/bang-gia-goi-vip8.html'],
  'new_icon' => 1
];

$viplist['vip32'] = [
  'code' => 'vip32',
  'number' => 32,
  'name' => 'PLP Report',
  'subtitle' => 'Báo cáo kết quả hoạt động kinh doanh trên thị trường mua sắm công của các công ty niêm yết trên thị trường chứng khoán',
  'price' => [99000000, 'Năm'],
  'old_price' => [0, 'Năm'],
  'desc' => [
    'Tải báo cáo PDF "Kết quả hoạt động kinh doanh trên thị trường mua sắm công của các công ty niêm yết trên thị trường chứng khoán"',
    'Dữ liệu được cập nhật định kỳ theo tháng/quý/năm, đảm bảo luôn nắm bắt được những thông tin mới nhất.',
    'Dữ liệu được trực quan hóa bằng các bảng biểu dễ nhìn.',
    'Miễn phí 01 năm sử dụng gói VIP3 Plus'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/tinh-nang-bang-gia-goi-plp-report.html'],
  'new_icon' => 1
];

$viplist['vip101'] = [
  'code' => 'vip101',
  'number' => 101,
  'name' => 'X4',
  'subtitle' => 'Trích xuất dữ liệu hàng hóa mua sắm công ra file Excel',
  'price' => [39999000, 'Năm'],
  'old_price' => [0, 'Năm'],
  'desc' => [
    'Trích xuất trọn gói dữ liệu hàng hóa ra file excel theo bộ lọc.',
    'Trích xuất danh mục hàng hóa của một gói thầu cụ thể hoặc một kết quả lựa chọn nhà thầu cụ thể.',
    'Tìm kiếm, xem chi tiết trên giao diện web.'
  ],
  'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/tinh-nang-bang-gia-goi-x4.html'],
  'new_icon' => 1
];

/*
$viplist['vip55'] = [
    'code' => 'vip55',
    'number' => 55,
    'name' => 'TDT',
    'subtitle' => 'TDT (Tools Dự Thầu) - Công cụ xuất nhập dữ liệu thầu vào webform trên mua sắm công',
    'price' => [5000000, 'Năm'],
    'price2' => [2000000, '3 Tháng'],
    'old_price' => [5000000, 'Năm'],
    'old_price2' => [2000000, '3 Tháng'],
    'desc' => [
        'Giúp nhà thầu xuất bảng khối lượng mời thầu của gói thầu ra excel.',
        'Nhập BẢNG TỔNG HỢP GIÁ DỰ THẦU lên webform của hệ thống mua sắm công khi tham gia đấu thầu trực tuyến trên hệ thống mạng đấu thầu quốc gia.',
        'Tùy chọn 1: Thanh toán 1 năm',
        'Tùy chọn 2: Thanh toán 3 tháng'
    ],
    'detail' => ['Xem chi tiết bảng giá và chức năng', '/page/tinh-nang-bang-gia-goi-tdt.html'],
    'new_icon' => 1
]; */
