<?php

if (!defined('NV_IS_MOD_BIDDING')) {
    die('Stop!!!');
}

use NukeViet\Dauthau\Share;
use NukeViet\Point\Point;
use NukeViet\Dauthau\Url as UrlDT;

$base_url = $page_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $array_data['alias'] . '-' . $array_data['id'] . '/competitors';

$array_data['title_op'] = $nv_Lang->getModule('title_competitors', $array_data['org_fullname']);

$array_mod_title[] = [
    'title' => $array_data['title_op'],
    'link' => nv_url_rewrite($base_url, true)
];

$page_title = $description = $array_data['title_op'];

// L<PERSON>y danh nhà đầu tư từng đấu: là danh sách toàn bộ nhà thầu nằm trong bảng nv4_vi_bidding_result_project_business gồm các nhà thầu trúng trượt ở đây => các nhà thầu trong này dều là các nhà đầu tư từng đấu

$list_result_id = $_data_static['arr_row']['list_result_business'] ?? [];

$listBusiness = [];
if (!empty($list_result_id)) {
    // Thực hiện lấy các nhà đầu tư từng đấu
    $listBusiness = $db->query("SELECT orgcode, id, resultid, bidder_name, joint_venture, result_status, partnership FROM " . NV_PREFIXLANG . "_" . $module_data . "_result_project_business WHERE resultid IN (" . implode(",", array_column($list_result_id, 'resultid')) . ") AND orgcode != " . $db->quote($array_data['org_code']))->fetchAll();
}

$arrInvestor = [];
if (!empty($listBusiness)) {
    // Lấy danh sách thông tin nhà đầu tư
    $db->sqlreset()
       ->select('id, org_fullname, alias, org_code')
       ->from(BID_PREFIX_GLOBAL . '_investor_approved')
       ->where('org_code IN (' . implode(',', array_map([$db,'quote'], array_column($listBusiness, 'orgcode'))) . ')');

    $sth = $db->prepare($db->sql());
    $sth->execute();
    $listInvestor = $sth->fetchAll();
    foreach ($listInvestor as $k => $v) {
        $arrInvestor[$v['org_code']] = $v;
    }
}

$arrBusiness = [];
foreach ($listBusiness as $k => $v) {
    if (isset($arrInvestor[$v['orgcode']])) {
        $arrBusiness[$v['orgcode']]['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['investors'] . '/' . $arrInvestor[$v['orgcode']]['alias'] . '-' . $arrInvestor[$v['orgcode']]['id'];
    } else {
        $arrBusiness[$v['orgcode']]['link'] = "javascript:void(0)";
    }
    $arrBusiness[$v['orgcode']]['orgcode'] = $v['orgcode'];
    $arrBusiness[$v['orgcode']]['bidder_name'] = $v['bidder_name'];
    $arrBusiness[$v['orgcode']]['num_tbmt'][$v['resultid']] = $v;
}

if (!defined('NV_IS_USER') || (!isset($global_array_vip[3]) AND !isset($global_array_vip[31]))) {
    // Chưa login chỉ lấy 3 dữ liệu
    $arrBusiness = array_slice($arrBusiness, 0, 3);
}

$vip3_renew = 0;
$link = '';

// Hiển thị link đăng nhập, đăng ký người dùng và đăng ký VIP 3 khi chưa đăng nhập
if (!defined('NV_IS_USER')) {
    $link_login = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    $link = sprintf($nv_Lang->getModule('view_result_one_1'), $link_register, nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3'));
} else {
    // Không Tồn tại gói VIP thì mới kiểm tra tiếp
    if (!(isset($global_array_vip[3]) and !isset($global_array_vip[31]))) {
        $vip3_renew = $db->query('SELECT id FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id=' . $user_info['userid'] . ' AND status != 1 AND (vip = 3 OR vip = 31) AND prefix_lang = ' . BID_LANG_DATA)->fetchColumn();
        if (!empty($vip3_renew)) {
            $link = $nv_Lang->getModule('view_result_user_view1_renew', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3', true));
        } else {
            // Không có GÓI VIP nào thì hiện link đăng ký
            $link = $nv_Lang->getModule('view_result_user_view1', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=vip&form=1&vip=3', true));
        }
    }
}

$canonicalUrl = getCanonicalUrl($base_url);
$contents = nv_theme_bidding_investors_competiors($array_data, $link, $arrBusiness);

// Kiểm tra nếu gói VIP 3 hết hạn
if (!empty($vip3_renew)) {
    // Nếu hết hạn
    $contents = str_replace("##title__vip##", $nv_Lang->getModule('title_renew_vip'), $contents);
    $contents = str_replace("##link__vip##", NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3', $contents);
} else {
    $contents = str_replace("##title__vip##", $nv_Lang->getModule('title_resg_vip'), $contents);
    $contents = str_replace("##link__vip##", NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3', $contents);
}

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
