<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_BIDDING')) {
    die('Stop!!!');
}

use NukeViet\Dauthau\Url as UrlDT;
$array_data = $list_investor_arr = array();
$id = 0;
if (isset($array_op[1])) {
    if (isset($array_op[1]) and preg_match('/^([a-zA-Z0-9\-]+)\-([0-9]+)$/i', $array_op[1], $m)) {
        $id = intval($m[2]);
        $alias = $m[1];
    }
}

if (!empty($id)) {
    // Lấy dữ liệu đánh giá sơ bộ NL,KN
    $array_data = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_nlkn_sdd WHERE id = ' . $id)->fetch();
    if (!empty($array_data)) {
        $rs_investor = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_list_investor_nlkn WHERE pcode = ' . $db->quote($array_data['pcode']));
        $list_investor = [];
        while ($_item = $rs_investor->fetch()) {
            $_arr = explode(';', $_item['list_investor_name']);
            $k = 0;
            if (!empty($_item['list_investor_name'])) {
                foreach ($_arr as $r) {
                    $_item['result'] = $_item['result'] == 1 ? $nv_Lang->getModule('result_nkkn_1') : $nv_Lang->getModule('result_nkkn_0');
                    $_item['bidder_name'] = $_arr[$k];
                    $_item['org_code'] = !empty($_item['joint_venture_code']) ? $_item['joint_venture_code'] : (sizeof($_arr) > 1 ? explode(';', $_item['org_code'])[0] : $_item['org_code']);
                    $list_investor[] = $_item;
                    $k++;
                }
            } else {
                $_item['result'] = $_item['result'] == 1 ? $nv_Lang->getModule('result_nkkn_1') : $nv_Lang->getModule('result_nkkn_0');
                $_item['org_code'] = !empty($_item['joint_venture_code']) ? $_item['joint_venture_code'] : (sizeof($_arr) > 1 ? explode(';', $_item['org_code'])[0] : $_item['org_code']);
                $list_investor[] = $_item;
            }
        }
        $rs_investor->closeCursor();
        array_map(function($item) use (&$list_investor_arr) {
            $key = $item['joint_venture_code'] ?: $item['org_code'];
            $list_investor_arr[$key][] = $item;
        }, $list_investor);
        
    }else {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=listevalexperience');
        die();
    }
    $page_title = $array_data['pname'];
} else {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=listevalexperience');
    die();
}

$permision = 0;
// Chỉ thành viên hoặc Bot mới vào đây được
if (!(defined('NV_IS_USER') or $client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
    $permision = 1; // popup yêu cầu đăng nhập
}

// Kiểm tra URL, không cho đánh tùy ý phần alias
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['eval-experience'] . '/' . $alias . '-' . $id . $global_config['rewrite_exturl'];
$base_url_rewrite = nv_url_rewrite($base_url, true);
$base_url_check = str_replace('&amp;', '&', $base_url_rewrite);
$request_uri = rawurldecode($_SERVER['REQUEST_URI']);
if (strpos($request_uri, $base_url_check) !== 0 and strpos(NV_MY_DOMAIN . $request_uri, $base_url_check) !== 0) {
    nv_redirect_location($base_url_rewrite);
}
$array_data['link'] = NV_MAIN_DOMAIN . $base_url_rewrite;
$page_url = $base_url;
$canonicalUrl = getCanonicalUrl($page_url);
$array_data['relative_link'] = $base_url_rewrite;

$other_lang_data = $db->query('SELECT id, pname, pcode FROM ' . $other_lang_prefix . '_' . $module_data . '_nlkn_sdd WHERE id = ' . $db->quote($array_data['id']))->fetch();
if (!empty($other_lang_data)) {
    $global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $other_lang_module_info['alias']['eval-experience'] . '/' . strtolower(change_alias($other_lang_data['pname'])) . '-' . $other_lang_data['id'] . $global_config['rewrite_exturl'];
}

$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('dgsbnlkn_title'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['listevalexperience'], true)
);
$array_mod_title[] = [
    'title' => $array_data['pname'],
    'link' => $array_data['relative_link']
];

$array_data_process[0]['tbkssqt'] = [
    'act' => false,
    'is_current' => false,
    'url' => false,
    'classes' => 'tbkssqt',
    'title' => $nv_Lang->getModule('listtbkssqt')
];
$array_data_process[0]['dapt'] = [
    'act' => true,
    'is_current' => false,
    'url' => false,
    'classes' => 'devproject',
    'title' => $nv_Lang->getModule('project')
];
$array_data_process[0]['dgsbnlkn'] = [
    'act' => true,
    'is_current' => true,
    'url' => false,
    'classes' => 'icn dgsbnlkn current active',
    'title' => $nv_Lang->getModule('dgsbnlkn')
];
$array_data_process[0]['khlcnt'] = [
    'act' => true,
    'is_current' => true,
    'url' => false,
    'classes' => 'khlcnt',
    'title' => $nv_Lang->getModule('ke_hoach_lcndt')
];

$array_data_process[0]['tbmt'] = [
    'act' => true,
    'is_current' => false,
    'url' => false,
    'classes' => 'tbmt',
    'title' => $nv_Lang->getModule('pagetitle_tbmt_type2')
];
$array_data_process[0]['kqlcnt'] = [
    'act' => true,
    'is_current' => false,
    'url' => false,
    'classes' => 'kqlcnt',
    'title' => $nv_Lang->getModule('kqlcndt')
];

$_project = $db->query('SELECT id, ten_du_an, code, alias FROM ' . NV_PREFIXLANG . '_' . $module_data . '_project_proposal WHERE code =' . $db->quote($array_data['pcode']))->fetch();
if (!empty($_project)) {
    $array_data_process[0]['dapt']['url'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['projectdetail'] . '/' . $_project['alias'] . '-' . $_project['code'] . $global_config['rewrite_exturl'];
    $array_data_process[0]['dapt']['classes'] .= " active";
    $pno = explode('-', $_project['code'])[0];

    // Lấy TBMĐT nếu có
    $tbmt = $db->query('SELECT t1.id, t1.so_tbmt, t1.goi_thau FROM ' . NV_PREFIXLANG . '_' . $module_data . '_project_row as t1
            INNER JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_project_detail as t2 ON t1.id=t2.id
            WHERE t1.id_project =' . $_project['id'] . ' ORDER BY t1.id DESC LIMIT 1')->fetch();
    if (!empty($tbmt)) {
        $array_data_process[0]['tbmt']['url'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['view'] . '/' . UrlDT::getTBMDT() . '/' . strtolower(change_alias($tbmt['goi_thau'])) . '-' . $tbmt['id'] . $global_config['rewrite_exturl'];
        $array_data_process[0]['tbmt']['classes'] .= " active";
    }

    // KHLCNĐT
    $khlcnt = $db->query('SELECT id, title, code, alias FROM ' . NV_PREFIXLANG . '_' . $module_data . '_plans_project WHERE project_no =' . $db->quote($_project['code']))->fetch();
    if (!empty($khlcnt)) {
        $array_data_process[0]['khlcnt']['url'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['plans'] . '/' . UrlDT::getKHLCNDT() . '/' . $khlcnt['alias'] . '-' . $khlcnt['id'] . $global_config['rewrite_exturl'];
        $array_data_process[0]['khlcnt']['classes'] .= " active";
    } else {
        $khlcnt = $db->query('SELECT id, title, code, alias FROM ' . NV_PREFIXLANG . '_' . $module_data . '_plans_project WHERE project_no LIKE ' . $db->quote(explode('-', $_project['code'])[0] . '%'))->fetch();
        if (!empty($khlcnt)) {
            $array_data_process[0]['khlcnt']['url'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['plans'] . '/' . UrlDT::getKHLCNDT() . '/' . $khlcnt['alias'] . '-' . $khlcnt['id'] . $global_config['rewrite_exturl'];
            $array_data_process[0]['khlcnt']['classes'] .= " active";
        }
    }

    if (!empty($khlcnt) and empty($tbmt)) {
        $tbmt = $db->query('SELECT t1.id, t1.so_tbmt, t1.goi_thau FROM ' . NV_PREFIXLANG . '_' . $module_data . '_project_row as t1
                INNER JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_project_detail as t2 ON t1.id=t2.id
                WHERE t2.khlcnt_code =' . $db->quote($khlcnt['code']) . ' ORDER BY t1.id DESC LIMIT 1')
            ->fetch();
        if (!empty($tbmt)) {
            $array_data_process[0]['tbmt']['url'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['view'] . '/' . UrlDT::getTBMDT() . '/' . strtolower(change_alias($tbmt['goi_thau'])) . '-' . $tbmt['id'] . $global_config['rewrite_exturl'];
            $array_data_process[0]['tbmt']['classes'] .= " active";
        }
    }
    // KQLCNĐT
    $kqlcndt = $db->query('SELECT id, title, code FROM ' . NV_PREFIXLANG . '_' . $module_data . '_result_project WHERE pno =' . $db->quote($pno))->fetch();
    if (!empty($kqlcndt)) {
        $array_data_process[0]['kqlcnt']['url'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . UrlDT::getKHLCNDT() . '/' . strtolower(change_alias($kqlcndt['title'])) . '-' . $kqlcndt['id'] . $global_config['rewrite_exturl'];
        $array_data_process[0]['kqlcnt']['classes'] .= " active";
    }
}

$contents = nv_theme_bidding_detail_eval($array_data, $list_investor_arr, $permision, $array_data_process);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
