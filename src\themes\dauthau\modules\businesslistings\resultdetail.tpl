<!-- BEGIN: main -->
<script>
    window.page_url = '{PAGE_URL}';
</script>
<br>
<h1 class="margin-bottom title__view"><span>{TITLE}</span></h1>
<!-- BEGIN: bidding -->
<div class="bidding-simple wrap__text">
    <!-- BEGIN: show_order -->
    <div class="row">
        <div class="pull-right margin-right margin-top">
            <select class="form-control" id="order" onchange="change_order('order')" name="order" >
                <!-- BEGIN: order -->
                <option value="{ORDER.key}" {ORDER.selected}>{ORDER.value}</option>
                <!-- END: order -->
            </select>
        </div>
        <div class="pull-right margin-right margin-top">
            <select class="form-control" id="prov" onchange="change_order('prov')" name="prov" >
                <option value="-1">-- {LANG.province} --</option>
                <!-- BEGIN: province -->
                <option value="{PROV.key}"{PROV.selected}>{PROV.value}</option>
                <!-- END: province -->
            </select>
        </div>
        <div class="pull-right margin-right margin-top">
            <select name="package_type" onchange="buildChartNew();" class="form-control">
                <!-- BEGIN: package -->
                <option value="{PACKAGE.key}"{PACKAGE.selected}>{PACKAGE.value}</option>
                <!-- END: package -->
            </select>
        </div>
        <div class="pull-right margin-right margin-top">
            <select class="form-control" id="view_chart" onchange="buildChartNew();" name="view_chart" >
                <option value="1">{LANG.view_chart_7_years}</option>
                <option value="2">{LANG.view_chart_full}</option>
            </select>
        </div>
    </div>
    <!-- END: show_order -->
    <!-- BEGIN: value_chart -->
    <div class="row chart">
        <div class="col-md-24 text-center col-sm-24 col-xs-24">
            <div class="main_chart_history">
                <div id="value_chart" style="padding-right: 5px;">
                </div>
            </div>
            <p class="text-left">{chart_linkview}</p>
        </div>
        <div class="col-md-24 text-center col-sm-24 col-xs-24 des-value-chart">
            {LANG.des_value_desire}
        </div>
    </div>
    <!-- END: value_chart -->

    <section class="block-bidding " style="margin-top: 10px">
        <div class="bidding-list bidding-list-detail">
            <div class="bidding-list-header">
                <div class="c-stt">#</div>
                <div class="c-name">{LANG.goi_thau}</div>
                <div class="c-author">{LANG.investor}</div>
                <div class="c-pub">{LANG.win_price}</div>
                <div class="c-pub">{LANG.finish_time}</div>
                <div class="c-pub">{LANG.result}</div>
                <div class="c-pub">{LANG.vai_tro}</div>
                <div class="c-pub">{LANG.province}</div>
             </div>
            <div class="bidding-list-body" id="load_data_bidding">
                <!-- BEGIN: loop -->
                    <!-- BEGIN: show_all -->
                    <div class="item">
                        <div class="c-stt">
                            <span class="label-name">{LANG.STT}: </span>{RESULT.stt}</div>
                        <div class=" c-name">
                        <span class="label-name">{LANG.goi_thau}: </span>
                            <h3><a class="bidding_link" href="{RESULT.link}" title="{RESULT.title}">{RESULT.title}</a></h3></div>
                            <div class="c-author">
                                <span class="label-name">{LANG.investor}: </span>
                                <a class="bidding_link" href="{RESULT.link_solicitor}" title="{RESULT.investor}">{RESULT.investor}</a>
                            </div>
                        <div class="c-pub text-center">
                            <span class="label-name">{LANG.win_price}: </span>{RESULT.win_price}</div>
                           <div class="c-pub">
                            <span class="label-name">{LANG.finish_time}: </span>{RESULT.finish_time}</div>

                           <!-- BEGIN: result1 -->
                           <div class="c-pub">
                            <span class="label-name">{LANG.result}: </span>
                            <a class="bidding_link" href="{RESULT.link_result}" title="{RESULT.trung_thau_lang}">{RESULT.trung_thau_lang}</a>
                        </div>

                        <!-- END: result1 -->
                        <!-- BEGIN: result0 -->
                        <div class="c-pub">
                            <span class="label-name">{LANG.result}: </span>{RESULT.trung_thau_lang}</div>
                        <!-- END: result0 -->

                           <div class="c-pub">
                            <span class="label-name">{LANG.vai_tro}: </span>{RESULT.partnership}</div>
                        <div class="c-pub">
                            <span class="label-name">{LANG.province}: </span>{RESULT.province}</div>
                    </div>
                    <!-- END: show_all -->

                    <!-- BEGIN: show_one -->
                    <div class="item">
                        <div class="c-stt">
                            <span class="label-name">{LANG.STT}: </span>{RESULT.stt}</div>
                        <div class=" c-name">
                        <span class="label-name">{LANG.goi_thau}: </span>
                            <h3><a class="bidding_link" href="{RESULT.link}" title="{RESULT.title}">{RESULT.title}</a></h3></div>
                            <div class="c-author">
                                <span class="label-name">{LANG.investor}: </span>
                                <a class="bidding_link" href="{RESULT.link_solicitor}" title="{RESULT.investor}">{RESULT.investor}</a>
                            </div>
                        <div class="c-pub text-center coating">
                            <span class="label-name">{LANG.win_price}: </span>{LOCK}
                            {BACKGROUND}
                        </div>
                        <div class="c-pub coating">
                            <span class="label-name">{LANG.finish_time}: </span>{LOCK}
                        </div>

                        <div class="c-pub coating">
                            <span class="label-name">{LANG.result}: </span>{LOCK}</div>

                        <div class="c-pub coating">
                            <span class="label-name">{LANG.vai_tro}: </span>{LOCK}</div>
                        <div class="c-pub coating">
                            <span class="label-name">{LANG.province}: </span>
                            {LOCK}
                        </div>
                    </div>
                    <!-- END: show_one -->
                <!-- END: loop -->
                <!-- BEGIN: generate_page -->
                <div class="bidding-list-pagenav">
                    {NV_GENERATE_PAGE}
                </div>
                <!-- END: generate_page -->
            </div>
        </div>
        <p class="text-center"><i id="fa-order" class="fa fa-spinner fa-spin fa-lg hidden text-center" aria-hidden="true"></i></p>
    </section>
    <p id="number_result">{NUMBER_RESULT}</p>
    <!-- BEGIN: show_note -->
    <p class="alert alert-info">{SHOW_NOTE}</p>
    <!-- END: show_note -->
</div>
<script src="{LINK_JS}js/apexcharts.js"></script>
<script type="text/javascript">
    var type = $("select[name='package_type']").val();
    if (type) {
        buildChart('', type);
    } else {
        buildChart('', 1);
    }

    function buildChartNew() {

        var type = $("select[name='package_type']").val();
        var order_val = $('#order').val();
        var prov_val = $('#prov').val();
        var view_chart = $('#view_chart').val();
        $("#load_data_bidding").empty();
        $("#fa-order").removeClass("hidden");
        $.post("{LINK}", { package: type, order: order_val, prov: prov_val, page: "{PAGE}", ajax: "1", view_chart: view_chart }, function (res) {
            $("#value_chart").html("");
            buildChart('prov', type, res.data_desire, res.data_real, res.data_inde);
            $("#fa-order").addClass("hidden");
            $("#load_data_bidding").html(res.content);
            $("#number_result").html(res.content_prov);
        })
    }
    function buildChart(action, type, data_desire_1 = [], data_real_1 = [], data_inde_1 = []) {
        var data_desire = '';
        var data_real = '';
        var data_inde = '';

        if (action == 'prov') {
            data_desire = data_desire_1;
            data_real = data_real_1;
            data_inde = data_inde_1;
        } else if (type == 1) {
            data_desire = {DATA_DESIRE};
            data_real = {DATA_REAL};
            data_inde = {DATA_INDE};
        } else if (type == 2) {
            data_desire = {DATA_DESIRE_CDT};
            data_real = {DATA_REAL_CDT};
            data_inde = {DATA_INDE_CDT};
        } else if (type == 3) {
            data_desire = {DATA_DESIRE_NOT_TBMT};
            data_real = {DATA_REAL_NOT_TBMT};
            data_inde = {DATA_INDE_NOT_TBMT};
        }

        var options = {
            series: [{
                name: '{LANG.value_desire}',
                data: data_desire
            },
            {
                name: '{LANG.value_real}',
                data: data_real
            },
            {
                name: '{LANG.value_inde}',
                data: data_inde
            }],
            chart: {
                type: 'area',
                stacked: false,
                height: 350,
                zoom: {
                    enabled: true,
                },
                offsetY: 10,
                offsetX: 5,
            },
            stroke: {
                curve: 'smooth'
            },
            dataLabels: {
                enabled: false
            },
            tooltip: {
                enabled: {CHECK_USER},
                x: {
                    format: 'MM/yyyy'
                },
                y: {
                    formatter: function(val, index) {
                        return new Intl.NumberFormat('vi-VN', {
                            maximumFractionDigits: 0,
                        }).format(val) + ' VND';
                    }
                },
                tooltip: {
                    share: true
                }
            },
            title: {
                text: '{LANG.value_chart_title}',
                align: 'center',
                margin: 10,
                offsetX: 0,
                offsetY: 0,
                floating: false,
                style: {
                fontSize:  '14px',
                fontWeight:  'bold',
                fontFamily:  'Helvetica, Arial, sans-serif',
                color:  '#263238'
                },
            },
            colors : ['#4576b5', '#ff9f43', '#ff6b6b'],
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    inverseColors: false,
                    opacityFrom: 0.7,
                    opacityTo: 0.2,
                    stops: [0, 50, 100]
                },
            },
            xaxis: {
                type: 'datetime',
                labels: {
                    datetimeUTC: false,
                    datetimeFormatter: {
                        year: 'yyyy',
                        month: 'MM/yyyy',
                        day: 'dd/MM/yyyy',
                        hour: 'HH:mm'
                    }
                },
                title: {
                    text: '{LANG.statistics_time}',
                    offsetY: 5,
                    style: {
                        fontSize: '14px',
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        colors: ['#333'],
                        fontWeight: 600
                    }
                }
            },
            yaxis: {
                show: {CHECK_USER},
                labels: {
                    formatter: function(val, index) {
                        val = val / 1000000;
                        return new Intl.NumberFormat('vi-VN', {
                            maximumFractionDigits: 0,
                        }).format(val);
                    }
                },
                title: {
                    text: '{LANG.title_value_chart}',
                    offsetX: 2,
                    style: {
                        fontSize: '14px',
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        colors: ['#333'],
                        fontWeight: 600
                    }
                }
            }, legend: {
                position: 'top',
                horizontalAlign: "center"
            }
        };
        var chart = new ApexCharts(document.querySelector("#value_chart"), options);
        chart.render();
    }

    function change_order(action){
        var order_val = $('#order').val();
        var prov_val = $('#prov').val();
        var type = $("select[name='package_type']").val();
        var view_chart = $('#view_chart').val();
        $("#load_data_bidding").empty();
        $("#fa-order").removeClass("hidden");
        $.post("{LINK}", { package: type, order: order_val, prov: prov_val, page: "{PAGE}", ajax: "1", view_chart: view_chart }, function (res) {
            $("#fa-order").addClass("hidden");
            $("#load_data_bidding").html(res.content);
            $("#number_result").html(res.content_prov);
            $("#value_chart").html("");
            buildChart('prov', type, res.data_desire, res.data_real, res.data_inde);
        })
        return false;
    }

</script>
<!-- END: bidding -->
<!-- END: main -->


<!-- BEGIN: ajax -->
    <!-- BEGIN: loop -->
    <div class="item">
        <div class="c-stt">
            <span class="label-name">{LANG.STT}: </span>{RESULT.stt}</div>
        <div class=" c-name">
        <span class="label-name">{LANG.goi_thau}: </span>
            <h3><a class="bidding_link" href="{RESULT.link}" title="{RESULT.title}">{RESULT.title}</a></h3></div>
            <div class="c-author">
                <span class="label-name">{LANG.investor}: </span>
                <a class="bidding_link" href="{RESULT.link_solicitor}" title="{RESULT.investor}">{RESULT.investor}</a>
            </div>
        <div class="c-pub text-center">
            <span class="label-name">{LANG.win_price}: </span>{RESULT.win_price}</div>
        <div class="c-pub">
            <span class="label-name">{LANG.finish_time}: </span>{RESULT.finish_time}</div>

        <!-- BEGIN: result1 -->
        <div class="c-pub">
            <span class="label-name">{LANG.result}: </span>
            <a class="bidding_link" href="{RESULT.link_result}" title="{RESULT.trung_thau_lang}">{RESULT.trung_thau_lang}</a>
        </div>

        <!-- END: result1 -->
        <!-- BEGIN: result0 -->
        <div class="c-pub">
            <span class="label-name">{LANG.result}: </span>{RESULT.trung_thau_lang}</div>
        <!-- END: result0 -->

        <div class="c-pub">
            <span class="label-name">{LANG.vai_tro}: </span>{RESULT.partnership}</div>
        <div class="c-pub">
            <span class="label-name">{LANG.province}: </span>{RESULT.province}</div>
    </div>
    <!-- END: loop -->
    <!-- BEGIN: generate_page -->
    <div class="bidding-list-pagenav">
        {NV_GENERATE_PAGE}
    </div>
    <!-- END: generate_page -->
    <input type="hidden" value="{DATA_DESIRE}" id = "data_desire_input" />
    <input type="hidden" value="{DATA_REAL}" id = "data_real_input" />
    <input type="hidden" value="{DATA_INDE}" id = "data_inde_input" />
<!-- END: ajax -->
