<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2022 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Wed, 11 May 2022 09:11:42 GMT
 */
if (!defined('NV_IS_MOD_GOV_AGC')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('main_title');
$description = $nv_Lang->getModule('main_desc');
$key_words = $module_info['keywords'];

$array_data = [];

$cache_management_agency_list = 'cache_management_agency_list_' . NV_CACHE_PREFIX . '.cache';

if (($management_agency_list = $nv_Cache->getItem($module_name, $cache_management_agency_list, 604800)) != false) {
    $management_agency_list = unserialize($management_agency_list);
} else {
    // <PERSON><PERSON><PERSON> danh sách management_agency theo DMDC
    $sql_management_agency = "SELECT * FROM " . NV_PREFIXLANG . "_management_agency";
    $management_agency_list = $nv_Cache->db($sql_management_agency, 'id', $module_name);

    $nv_Cache->setItem($module_name, $cache_management_agency_list, serialize($management_agency_list), 604800);
}

$classify = $nv_Request->get_title('classify', 'post,get', 'msc'); // mặc định hiển thị theo cách phân loại của MSC
$round = $nv_Request->get_int('round', 'post,get', 0);
$page = $nv_Request->get_page('page', 'post,get', 1);
$sort = $nv_Request->get_int('sort', 'post,get', 0);
$source = $nv_Request->get_int('source', 'post,get', 0);
$management_type = $nv_Request->get_int('management_type', 'post,get', 1); // Mặc định là tất cả
$type = $nv_Request->get_int('type', 'post,get', 5); // mặc định hiển thị bộ, ngành, địa phương

// lấy từ cookie trước
$t_range = intval($nv_Request->get_string('tc_range', 'cookie', date('Y', NV_CURRENTTIME)));
$st_range = $nv_Request->get_int('t_range', 'get,post', $t_range);
if ($t_range != $st_range) {
    $nv_Request->set_Cookie('tc_range', $st_range);
}


$agency = $nv_Request->get_typed_array('agency', 'string', []);
$agency = implode(',', $agency);

$agency_management_filter = $nv_Request->get_typed_array('agency_management_filter', 'string', []);
$agency_management_filter = implode(',', $agency_management_filter);

$per_page = 20;
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;

if ($management_type) {
    $base_url .= '&amp;management_type=' . $management_type;
}

if ($classify == 'dmdc') {
    $base_url .= '&amp;classify=' . $classify;
}

$round > 2 && $round = 0;
if ($round > 0) {
    $base_url .= '&amp;round=' . $round;
}
$page_url = $base_url;
$page > 1 && $page_url .= '&amp;page=' . $page;

if ($st_range == 3 || $st_range == 6) {
    $_cur_month = intval(date('m', NV_CURRENTTIME));
    $_y = intval(date('Y', NV_CURRENTTIME));
    
    if ($_cur_month >= $st_range) {
        $where = '(year = ' . $_y . ' AND month <= ' . $_cur_month . ' AND month > ' . ($_cur_month - $st_range) . ')';
    } else {
        $_dif = $st_range - $_cur_month;
        $where = '((year = ' . $_y . ' AND month <= ' . $_cur_month . ' AND month >= 1)';
        $where .= ' OR (year = ' . ($_y-1) . ' AND month <= 12 AND month > ' .  (12-$_dif) . '))';
    }
} else {
    $where = 'year = ' . $st_range;
}
$base_url .= '&amp;t_range=' . $st_range;

if (!in_array($source, array_keys($nv_Lang->getModule('source_list')))) {
    $source = 0;
}
if (!in_array($type, array_keys($nv_Lang->getModule('type_list')))) {
    $type = 0;
}
if (!empty($source)) {
    $where .= ' AND id_classify = ' . $source;
    $base_url .= '&amp;source=' . $source;
}

$base_url .= '&amp;type=' . $type;

if ($classify == 'msc') {
    if ($type == 6) {
        $where .= ' AND agency_id = 0';
    } else {
        if ($type == 5) {
            $agency_type_query = $db->query("SELECT id FROM " . NV_PREFIXLANG . "_bidding_agency WHERE type IN (1,2)");
        } else {
            $agency_type_query = $db->query("SELECT id FROM " . NV_PREFIXLANG . "_bidding_agency WHERE type=" . $type);
        }
        $agency_type_arr = [];
        while ($agency_type = $agency_type_query->fetch()) {
            $agency_type_arr[$agency_type['id']] = $agency_type['id'];
        }
        if (!empty($agency_type_arr)) {
            // tìm intersection array ở đây
            if (!empty($agency)) {
                $intersect_agencies = array_intersect($agency_type_arr, explode(',', $agency));
                if (!empty($intersect_agencies)) {
                    $intersect_str = implode(',', $intersect_agencies);
                    $where .= ' AND agency_id IN (' . $intersect_str . ')';
                    $base_url .= '&amp;agency=' . $intersect_str;
                } else {
                    $where .= ' AND agency_id IN (-1)';
                    $base_url .= '&amp;agency=-1';
                }
            } else {
                $where .= ' AND agency_id IN (' . implode(',', $agency_type_arr) . ')';
            }
        }
    }
}

// Dùng cách phân loại theo DMSC
if ($classify == 'dmdc') {
    if ($management_type == 4) {
        $where .= ' AND agency_id = 0';
    } else {
        $man_agency_type_query = $db->query("SELECT id FROM " . NV_PREFIXLANG . "_management_agency WHERE agency_type=" . $management_type);
        $man_agency_type_arr = [];
        while ($man_agency_type = $man_agency_type_query->fetch()) {
            $man_agency_type_arr[$man_agency_type['id']] = $man_agency_type['id'];
        }
        if (!empty($man_agency_type_arr)) {
            // tìm intersection array ở đây
            if (!empty($agency_management_filter)) {
                $intersect_man_agencies = array_intersect($man_agency_type_arr, explode(',', $agency_management_filter));
                if (!empty($intersect_man_agencies)) {
                    $intersect_str = implode(',', $intersect_man_agencies);
                    $where .= ' AND agency_id IN (' . $intersect_str . ')';
                    $base_url .= '&amp;agency_management_filter=' . $intersect_str;
                } else {
                    $where .= ' AND agency_id IN (-1)';
                    $base_url .= '&amp;agency_management_filter=-1';
                }
            } else {
                $where .= ' AND agency_id IN (' . implode(',', $man_agency_type_arr) . ')';
            }
        }
    }
}

$link_sort = [
    'bid' => $base_url . '&amp;sort=' . 1,
    'invest' => $base_url . '&amp;sort=' . 3,
    'total_bidding' => $base_url . '&amp;sort=' . 7,
    'win' => $base_url . '&amp;sort=' . 5
];
if ($sort > 0 && $sort < 9) {
    $base_url .= '&amp;sort=' . $sort;
} else {
    $sort = 0;
}

switch ($sort) {
    case 1:
        $db->order('num_bid DESC');
        $link_sort['bid'] = $base_url . '&amp;sort=' . 2;
        break;
    case 2:
        $db->order('num_bid ASC');
        $link_sort['bid'] = $base_url . '&amp;sort=' . 1;
        break;
    case 5:
        $link_sort['win'] = $base_url . '&amp;sort=' . 6;
        break;
    case 6:
        $link_sort['win'] = $base_url . '&amp;sort=' . 5;
        break;
}

$page == 0 && $page = 1;

$sum = [
    'num_investor' => 0,
    'num_projects' => 0,
    'num_completed' => 0,
    'num_not_complete' => 0,
    'num_bid' => 0,
];

if (defined('NV_IS_USER')) {
    $db->sqlreset();
    $db->select("agency_id, SUM(total_project_in_progress) AS total_project_in_progress, SUM(total_project_completed) AS total_project_completed, SUM(total_bid) AS total_bid, SUM(total_project) AS total_project, SUM(total_cdt_bmt) AS total_cdt_bmt")
    ->from("nv4_gov_agencies_statistics")
    ->where($where)
    ->group("agency_id");
    
    $result = $db->query($db->sql());
    
    $count = 0;
    while ($row = $result->fetch()) {
        ++$count;
        $row['num_not_complete'] = nv_number_format($row['total_project_in_progress']);
        $row['num_completed'] = nv_number_format($row['total_project_completed']);
        $row['num_bid'] = nv_number_format($row['total_bid']);
        $row['num_projects'] = nv_number_format($row['total_project']);
        $row['num_investor'] = nv_number_format($row['total_cdt_bmt']);
        
        $row['title'] = $agency_list[$row['agency_id']]['title'];
        $row['alias'] = $agency_list[$row['agency_id']]['alias'];
        $row['weight'] = $agency_list[$row['agency_id']]['weight'];
        
        $row['link_agency'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $row['alias'] . '-' . $row['agency_id'] . (!empty($source) ? '&amp;source=' . $source : '') . '&t_range=' . $st_range, true);
        $row['link_solicitor'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $row['alias'] . '-' . $row['agency_id'] . '/chudautu'. (!empty($source) ? '&amp;source=' . $source : '') .'&t_range=' . $st_range, true);
        $array_data[$row['agency_id']] = $row;
    }
    $result->closeCursor();
    
    $array_data = array_slice($array_data, ($page - 1) * $per_page, $per_page, true);
    
    if (!empty($array_data)) {
        // Tính lại total_cdt_bmt vì trường này chỉ distinct trong khoảng 1 tháng
        $agency_ids = array_keys($array_data);
        
        if ($st_range == 3 || $st_range == 6) {
            list ($d, $m, $y) = explode('/', date('j/n/Y', NV_CURRENTTIME));
            $time1 = strtotime('-'.$st_range.' months', mktime(0, 0, 0, intval($m), intval($d), intval($y)));
            $time2 = NV_CURRENTTIME;
        } else {
            $time1 = mktime(0, 0, 0, 1, 1, $st_range);
            $time2 = mktime(23, 59, 59, 12, 31, $st_range);
        }
        
        $sql = "SELECT id_agency, count(DISTINCT id_soclitor) as c, count(*) as _pj, SUM(total_bid) AS _tb, COUNT(CASE WHEN (date_end > 0) THEN 1 ELSE NULL END) AS _cp  FROM nv4_vi_gov_agency_static WHERE date_post <= " . $time2 . " AND date_post >= "  . $time1 . ($source > 0 ? (" AND id_classify = " . $source) : "" ) . " AND id_agency IN (". implode(",", $agency_ids) .") GROUP BY id_agency";
        
        $result = $db->query($sql);
        
        while ($row = $result->fetch()) {
            $array_data[$row['id_agency']]['num_distinct_investor'] = nv_number_format($row['c']);
            $array_data[$row['id_agency']]['num_projects'] = nv_number_format($row['_pj']);
            $array_data[$row['id_agency']]['num_bid'] = nv_number_format($row['_tb']);
            $array_data[$row['id_agency']]['num_completed'] = nv_number_format($row['_cp']);
            $array_data[$row['id_agency']]['num_not_complete'] = nv_number_format($row['_pj'] - $row['_cp']);
        }
    }
    
    
} else {
    $array_data = array_slice($agency_list, 0, $per_page, true);
    foreach ($array_data as $k => $v) {
        $array_data[$k]['link_agency'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $array_data[$k]['alias'] . '-' . $array_data[$k]['id'], true);
    }
}

$num_items = $count?: 0;

betweenURLs($page, ceil($num_items / $per_page), $base_url, '&amp;page=', $prevPage, $nextPage);

$canonicalUrl = getCanonicalUrl($page_url);

$generate_page = '';
if ($num_items > $per_page) {
    $generate_page = nv_generate_page(nv_url_rewrite($base_url, true), $num_items, $per_page, $page);
}

// pr($array_data);

$contents = nv_theme_gov_agc_main($classify, $array_data, $sum, $generate_page, $page, $per_page, $round, $sort, $link_sort, $agency, $agency_list, $source, $type, $management_type, $agency_management_filter, $management_agency_list, $st_range);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
