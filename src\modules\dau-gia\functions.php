<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 10 Nov 2020 06:56:08 GMT
 */
if (!defined('NV_SYSTEM')) {
    die('Stop!!!');
}

define('NV_IS_MOD_DAU_GIA', true);

if (isset($module_config['bidding'])) {
    $bidding_config = $module_config['bidding'];
} else {
    $bidding_config['elas_host'] = '127.0.0.1';
    $bidding_config['elas_port'] = '9200';
    $bidding_config['elas_pass'] = '';
    $bidding_config['elas_use'] = 'elastic';
    $bidding_config['elas_user'] = '0'; // 0: k sử dụng elastic, 1 sử dụng
}

// kiểm tra loại tài khoản tại đây luôn
// 0, 1: thành viên, khách ẩn toàn bộ thông tin
// 2: view: xem dc các tin cũ
// 3, vip :k ẩn
$type_user = 0;
if (defined('NV_IS_USER')) {
    $type_user = 1;
    if (!empty($global_array_vip)) {
        foreach ($global_array_vip as $key => $value) {
            if ($key != 99 && $key != 77) {
                $type_user = 3;
                break;
            } else {
                $type_user = 2;
            }
        }
    }
}

$arr_op = [
    'detail',
    'main',
    'bidorganization',
    'bidorganizationdetail',
    'resultorganization',
    'resultorganizationdetail'
];
if ((NV_CURRENTTIME >= $close_time_dauthau and NV_CURRENTTIME <= $open_maunal_time_dauthau and ($type_user == 0 or $type_user == 1)) and !defined('NV_IS_AJAX') and in_array($op, $arr_op) and !$client_info['is_bot']) {
    $contents = $nv_Lang->getModule('maintenance');
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

if ($op == 'main') {
    if (!empty($array_op) and preg_match('/^([a-z0-9\-]+)\-([0-9]+)$/', $array_op[0], $m)) {
        $op = 'detail';
        $alias = $m[1];
        $id_bid = $m[2];
    }
} else if ($op == 'bidorganization') {
    if (!empty($array_op) and preg_match('/^(?!T-|H-|X-)([a-zA-Z0-9\-]+)\-([0-9]+)$/', $array_op[1], $m)) {
        $op = 'bidorganizationdetail';
        $alias = $m[1];
        $id_bid = $m[2];
    }
} else if ($op == 'resultorganization') {
    if (!empty($array_op) and preg_match('/^(?!T-|H-|X-)([a-zA-Z0-9\-]+)\-([0-9]+)$/', $array_op[1], $m)) {
        $op = 'resultorganizationdetail';
        $alias = $m[1];
        $id_result = $m[2];
    }
} else if ($op == 'organization') {
    if (!empty($array_op) and preg_match('/^(?!T-|H-|X-)([a-zA-Z0-9\-]+)\-([0-9]+)$/', $array_op[1], $m)) {
        $op = 'organizationdetail';
        $alias = $m[1];
        $id_bidder = $m[2];
    }
} else if ($op == 'department') {
    if (!empty($array_op) and preg_match('/^(?!T-|H-|X-)([a-zA-Z0-9\-]+)\-([0-9]+)$/', $array_op[1], $m)) {
        $op = 'departmentdetail';
        $alias = $m[1];
        $id_dep = $m[2];
    }
} else if ($op == $module_info['alias']['auctioneer']) {
    if (!empty($array_op) and preg_match('/^(?!T-|H-|X-)([a-zA-Z0-9\-]+)\-([0-9]+)$/', $array_op[1], $m)) {
        $op = 'auctioneerdetail';
        $alias = $m[1];
        $id_dgv = $m[2];
    }
}

function nv_substr_clean($string, $mode = 'lr')
{
    $strlen = nv_strlen($string);
    $pos_bg = nv_strpos($string, ' ') + 1;
    $pos_en = nv_strrpos($string, ' ');
    if ($mode == 'l') {
        $string = '...' . nv_substr($string, $pos_bg, $strlen - $pos_bg);
    } elseif ($mode == 'r') {
        $string = nv_substr($string, 0, $strlen - $pos_en) . '...';
    } elseif ($mode == 'lr') {
        $string = '...' . nv_substr($string, $pos_bg, $pos_en - $pos_bg) . '...';
    }

    return $string;
}

function BoldKeywordInStr($str, $keyword, $logic)
{
    $str = nv_br2nl($str);
    $str = nv_nl2br($str, ' ');
    $str = nv_unhtmlspecialchars(strip_tags(trim($str)));

    if (empty($keyword)) {
        return nv_clean60($str, 300);
    }

    if ($logic == 'AND') {
        $array_keyword = array(
            $keyword,
            nv_EncString($keyword)
        );
    } else {
        $keyword .= ' ' . nv_EncString($keyword);
        $array_keyword = explode(' ', $keyword);
        $array_keyword = array_unique($array_keyword);
    }
    $pos = false;
    $pattern = [];
    foreach ($array_keyword as $k) {
        $_k = function_exists('searchPatternByLang') ? searchPatternByLang(nv_preg_quote($k)) : nv_preg_quote($k);
        $pattern[] = $_k;
        if (!$pos and preg_match('/^(.*?)' . $_k . '/isu', $str, $matches)) {
            $strlen = nv_strlen($str);
            $kstrlen = nv_strlen($k);
            $residual = $strlen - 300;
            if ($residual > 0) {
                $lstrlen = nv_strlen($matches[1]);
                $rstrlen = $strlen - $lstrlen - $kstrlen;

                $medium = round((300 - $kstrlen) / 2);
                if ($lstrlen <= $medium) {
                    $str = nv_clean60($str, 300);
                } elseif ($rstrlen <= $medium) {
                    $str = nv_substr($str, $residual, 300);
                    $str = nv_substr_clean($str, 'l');
                } else {
                    $str = nv_substr($str, $lstrlen - $medium, $strlen - $lstrlen + $medium);
                    $str = nv_substr($str, 0, 300);
                    $str = nv_substr_clean($str, 'lr');
                }
            }

            $pos = true;
        }
    }
    if (!$pos) {
        return nv_clean60($str, 300);
    }

    $pattern = '/(' . implode('|', $pattern) . ')/isu';

    return preg_replace($pattern, '<span class="keyword">$1</span>', $str);
}


function PopupLogin($mess, $page_url = '')
{
    global $module_info, $global_config, $module_name, $op, $nv_Lang;
    $xtpl = new XTemplate('popup_login.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/dau-gia');
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('POPUP_LOGIN', $mess);
    if ($page_url != '') {
        $xtpl->assign('PAGE_URL', nv_redirect_encrypt(urlRewriteWithDomain($page_url, NV_MY_DOMAIN)));
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function build_full_address($address_data, $row_data = null)
{
    global $db, $db_config, $module_name;

    $final_address = '';
    $space = ', ';

    $address_data['address_bidder'] = $address_data['address_bidder'] ?? ($row_data['address_bidder'] ?? '');
    $address_data['id_ward_bidder'] = $address_data['id_ward_bidder'] ?? ($row_data['id_ward_bidder'] ?? 0);
    $address_data['id_district_bidder'] = $address_data['id_district_bidder'] ?? ($row_data['id_district_bidder'] ?? 0);
    $address_data['id_province_bidder'] = $address_data['id_province_bidder'] ?? ($row_data['id_province_bidder'] ?? 0);

    if (!empty($address_data['address_bidder'])) {
        $final_address .= $address_data['address_bidder'];
    }

    $base_link = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization';

    $link = $base_link;
    if ($address_data['id_ward_bidder'] > 0) {
        $db->sqlreset()
            ->select('title, alias')
            ->from(NV_PREFIXLANG . '_location_ward')
            ->where('id =' . $address_data['id_ward_bidder']);
        $sql = $db->sql();
        $result = $db->query($sql);
        $row = $result->fetch();
        if (!empty($row)) {
            $ward_url = NV_BASE_SITEURL . NV_LANG_DATA . '/organization/X-' . $row['alias'] . '-' . $address_data['id_ward_bidder'] . '/';
            $final_address .= $space . '<a href="' . $ward_url . '">' . $row['title'] . '</a>';
        }
    }
    $link = $base_link;
    if ($address_data['id_district_bidder'] > 0) {
        $db->sqlreset()
            ->select('title, alias')
            ->from(NV_PREFIXLANG . '_location_district')
            ->where('id =' . $address_data['id_district_bidder']);
        $sql = $db->sql();
        $result = $db->query($sql);
        $row = $result->fetch();
        if (!empty($row)) {
            $district_url = NV_BASE_SITEURL . NV_LANG_DATA . '/organization/H-' . $row['alias'] . '-' . $address_data['id_district_bidder'] . '/';
            $final_address .= $space . '<a href="' . $district_url . '">' . $row['title'] . '</a>';
        }
    }
    $link = $base_link;
    if ($address_data['id_province_bidder'] > 0) {
        $db->sqlreset()
            ->select('title, alias')
            ->from(NV_PREFIXLANG . '_location_province')
            ->where('id =' . $address_data['id_province_bidder']);
        $sql = $db->sql();
        $row = $db->query($sql)->fetch();
        if (!empty($row)) {
            $province_url = NV_BASE_SITEURL . NV_LANG_DATA . '/organization/T-' . $row['alias'] . '-' . $address_data['id_province_bidder'] . '/';
            $final_address .= $space . '<a href="' . $province_url . '">' . $row['title'] . '</a>';
        }
    }

    return trim($final_address, " ,");
}

/**
 * get_lang($lang, $index)
 * Lấy lịch sử bấm cập nhật lại của người dùng
 *
 * @param $lang string
 *          Ngôn ngữ vi/en
 * @param $index string
 *          index của lang muốn lấy
 *
 * @return string|bool
 *
 */
function get_lang($lang, $index)
{
    global $module_file, $nv_Lang;

    if (!in_array($lang, ['vi', 'en'])) {
        return false;
    }
    if ($lang != NV_LANG_INTERFACE) {
        $nv_Lang->changeLang($lang);
        $nv_Lang->loadModule($module_file, false, true);
    }
    $value = $nv_Lang->getModule($index);
    if ($lang != NV_LANG_INTERFACE) {
        $nv_Lang->changeLang(NV_LANG_INTERFACE);
    }

    return $value;
}