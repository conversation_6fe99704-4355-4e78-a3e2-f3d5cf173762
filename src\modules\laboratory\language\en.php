<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */


if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

$lang_translator['author'] = '';
$lang_translator['createdate'] = '';
$lang_translator['copyright'] = '';
$lang_translator['info'] = '';
$lang_translator['langtype'] = 'lang_module';

$lang_module['ma_so_phongtn'] = 'Lab code';
$lang_module['tieu_de'] = 'Title';
$lang_module['ten_phongtn'] = 'Lab Name';
$lang_module['nguon'] = 'Source';
$lang_module['ma_so_thue'] = 'Tax code';
$lang_module['ma_so_ten_ptn'] = 'Code - Laboratory name';
$lang_module['laboratorys_list'] = 'Las - XD construction laboratories list';
$lang_module['tinh_thanh'] = 'Province/City';
$lang_module['search'] = 'Search';
$lang_module['laboratory'] = 'Accreditation of specialized construction laboratories';
$lang_module['laboratorys'] = 'List of accredited construction laboratories';
$lang_module['stt'] = 'No.';
$lang_module['note_max_searchpage'] = 'The number of search results is exceeding the software&#39;s display limit of 100 pages. Please use the search engine to narrow your search or return to the previous page.';
$lang_module['note_wrong_page'] = 'Where did you find this link? I am the bearer, can not handle your request!';
$lang_module['notice'] = 'Notify';
$lang_module['back'] = 'Return';
$lang_module['khong_co_ket_qua'] = 'The requested results were not found, please change the search parameters.';
$lang_module['time_query'] = '<br />Search in: %s - Number of results: %s';
$lang_module['info'] = 'System information';
$lang_module['info_redirect_click'] = 'Click here if the wait is long';
$lang_module['info_login'] = 'You need to login or register an account to get access to this area. The system will redirect you to the login section in a moment.';
$lang_module['cap_nhat_lan_cuoi'] = 'Last update';
$lang_module['cap_nhat_lai'] = '(Refresh)';
$lang_module['cap_nhat_lan_dau'] = 'First update';
$lang_module['cap_nhat_lan_thu'] = '%s update.';
$lang_module['update_err'] = 'Update failed.';
$lang_module['update_err_new'] = 'New organization is posted to the system. You can update again after 30 minutes.';
$lang_module['update_ok'] = 'Update successful. Please wait about 10 minutes for the information to be updated.';
$lang_module['update_err_user_last'] = 'Please come back later %s';
$lang_module['tax_code'] = 'Tax code';
$lang_module['address'] = 'Address';
$lang_module['lab_address'] = 'Laboratory address';
$lang_module['lab_code'] = 'Lab code';
$lang_module['lab_name'] = 'Lab Name';
$lang_module['allow_date'] = 'Recognition date';
$lang_module['allow_content'] = 'Recognition content';
$lang_module['attached_file'] = 'Attached files';
$lang_module['source'] = 'Source';
$lang_module['postponed_date'] = 'Suspension date';
$lang_module['profile_dtnet'] = 'Business Profile';
$lang_module['profile_dtnet_title'] = 'Business profile on DauThau.Net';
$lang_module['province_select'] = 'Select Province';
$lang_module['keyword'] = 'Keyword';
$lang_module['confirm_not_user1'] = 'To view information please <strong><a href="javascript:void(0)" data-toggle="loginFormShow">Login</a></strong> or < strong><a href="%s">Register</a></strong> if you don\'t have an account.
Signing up is simple and completely free.';
$lang_module['description_title'] = 'This is the latest list of provincial construction laboratories %s updated by DauThau.asia';
$lang_module['location_initial'] = 'in';

$lang_module['title_resg_vip'] = 'Sign up for the VIP3 package';
$lang_module['title_renew_vip'] = 'Renew VIP3 package';
$lang_module['title_view_info'] = "To view full information";
$lang_module['sub_for_any_vip'] = 'You need to <strong><a href="/en/vip/?plan=1">subscribe for any software package</a></strong> to view all informations.';
$lang_module['log_in_up'] = 'You need to <strong><a href="%s">Login</a></strong> or <strong><a href="%s">Sign up</a></strong> to view all informations.';
$lang_module['laboratorys_1'] = 'Laboratory';
$lang_module['decision_name'] = 'Decision name';
$lang_module['certification_period'] = 'Certification period';
$lang_module['certificate_list_1'] = 'List of certificates';
$lang_module['laboratory_info'] = 'Laboratory information';
$lang_module['is_contractor'] = '<p>This lab belongs to the contractor. The results of data analysis for contractor <a href="%s"><strong>%s</strong></a> are as follows:</p>';
$lang_module['is_solicitor'] = 'This laboratory belongs to the bidding party. The results of data analysis for the bidding party <a href="%s"> <strong>%s</strong> </a> are as follows: <blockquote class=list><ul><li>Announced Contractor selection plan of <strong>%s</strong> project with total <strong>%s</strong> bidding packages.</li><li>Inviting bids <strong>%s has been carried out </strong> package (with <strong>%s</strong> invitation to bid notice), pre-qualification of <strong>%s</strong> package is carried out.</li><li>Results announced of <strong>%s</strong> package, cancel the bid for <strong>%s</strong> package (among the packages above).</li><li>Yes <strong>%s</strong > package with results without TBMT, TBMST.</li></ul><footer>DauThau.info software synthesizes and analyzes information from the national bidding database</footer></ blockquote>';
$lang_module['no_title'] = 'Uncategorized';

$lang_module['management_unit_info'] = 'Management Unit Info';
