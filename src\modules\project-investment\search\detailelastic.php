<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_PROJECT_INVESTMENT'))
    die('Stop!!!');

// kết nối tới ElasticSearh
$nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($config_bidding['elas_host'], $config_bidding['elas_port'], NV_LANG_ELASTIC . 'dauthau_project_investment', $config_bidding['elas_user'], $config_bidding['elas_pass']);

$search_elastic = [];
// tìm kiếm theo khoảng thời gian
if ($sfrom1 > 0 and $sto1 > 0) {
    $search_elastic['must'][]['range']['date_post'] = [
        "gte" => $sfrom1,
        "lte" => $sto1
    ];
}

if ($search_type_content == 1) {
    if ($par_search) {
        $search_fields = ['code', 'name'];
    } else {
        $search_fields = ['content_full'];
    }
} else {
    if ($par_search) {
        $search_fields = ['code', 'name_search'];
    } else {
        $search_fields = ['content'];
    }
}

// tìm kiếm theo từ khóa chính
if (!empty($q)) {
    $arr_key = explode(',', $q);
    if (!empty($search_kind)) {
        $arr_key = array_map(function ($a) {return explode(' ', $a);}, $arr_key);
        $arr_key = new RecursiveIteratorIterator(new RecursiveArrayIterator($arr_key));
    }
    $keyword_type = $search_kind <= 1 ? 'should' : 'must';
    foreach($search_fields as $f) {
        foreach ($arr_key as $key) {
            if ($search_type_content == 0) {
                $key = str_replace('-', ' ', change_alias($key));
            }
            $key = trim($key);
            if (empty($key)){
                continue;
            }
            if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                continue;
            }
            if (preg_match('/^[0-9]+?$/', $key)) { // Nếu keyword chỉ là dãy số thì tìm wildcard
                $search_elastic[$keyword_type][] = [
                    "wildcard" => [
                        $f => [
                            "value" => '*' . $key . '*'
                        ]
                    ]
                ];
            } else {
                $search_elastic[$keyword_type][] = [
                    "match_phrase" => [
                        $f => $key
                    ]
                ];
            }
        }
    }
}

// Tìm theo Từ khóa bổ sung
if (!empty($key_search2) and $is_advance) {
    $arr_key = explode(',', $key_search2);
    $search_bosung_should = [];
    foreach($search_fields as $f) {
        foreach ($arr_key as $key) {
            if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                continue;
            }
            if (empty($key)){
                continue;
            }
            if (!empty($search_one_key)) {
                //Một trong các từ khóa là điều kiện bắt buộc
                if ($search_type_content == 1) {
                    $key = trim($key);
                    $search_bosung_should[] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = trim($key);
                    $search_bosung_should[] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                }
            } else {
                if ($search_type_content == 1) {
                    $key = trim($key);
                    $search_elastic['must'][] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = trim($key);
                    $search_elastic['must'][] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                }
            }
        }
    }
    if (!empty($search_bosung_should)) {
        $search_elastic['must'][] = [
            "bool" => [
                "should" => $search_bosung_should,
                "minimum_should_match" => 1
            ]
        ];
    }
}

// từ khóa loại trừ
if (!empty($without_key) and $is_advance) {
    $arr_key = explode(',', $without_key);
    foreach($search_fields as $f) {
        foreach ($arr_key as $key) {
            if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                continue;
            }
            if (empty($key)){
                continue;
            }
            if ($search_type_content == 1) {
                $key = trim($key);
                $search_elastic['must_not'][] = [
                    "match_phrase" => [
                        $f => $key
                    ]
                ];
            } else {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                $search_elastic['must_not'][] = [
                    "match_phrase" => [
                        $f => $key
                    ]
                ];
            }
        }
    }
}
// Tìm kiếm theo tỉnh thành
if ($idprovince >= 0) {
    $search_elastic['must'][] = [
        "match" => [
            "province_id" => [
                'query' => $idprovince
            ]
        ]
    ];
}

if ($solicitor_id > 0) {
    $search_elastic['must'][] = [
        'match' => [
            'solicitor_id' => [
                'query' => $solicitor_id
            ]
        ]
    ];
}
if ($oda > 0 and $is_advance) {
    $search_elastic['must'][] = [
        'match' => [
            'oda' => [
                'query' => $oda == 1 ? "1" : "0"
            ]
        ]
    ];
}
if ($khlcnt == 1 and $is_advance) {
    $search_elastic['must'][] = [
        'match' => [
            'num_khlcnt' => [
                'query' => 1
            ]
        ]
    ];
}
if ($khlcnt == 2 and $is_advance) {
    $search_elastic['must'][] = [
        'match' => [
            'num_khlcnt' => [
                'query' => 0
            ]
        ]
    ];
}
if ($total_investment_from > 0 and $total_investment_to > 0 and $is_advance) {
    $search_elastic['must'][]['range']['total_investment'] = [
        "gte" => $total_investment_from,
        "lte" => $total_investment_to
    ];
} else {
    if ($total_investment_from > 0) {
        $search_elastic['must'][]['range']['total_investment'] = [
            "gte" => $total_investment_from
        ];
    }

    if ($total_investment_to > 0) {
        $search_elastic['must'][]['range']['total_investment'] = [
            "lte" => $total_investment_to
        ];
    }
}

if (!empty($search_elastic['should'])) {
    $search_elastic['minimum_should_match'] = '1';
    $search_elastic['boost'] = '1.0';
}

$array_query_elastic = array();
if (!empty($search_elastic)) {
    $array_query_elastic['query']['bool'] = $search_elastic;
}
$array_query_elastic['track_total_hits'] = 'true';
$array_query_elastic['size'] = $perpage;
$array_query_elastic['from'] = ($page - 1) * $perpage;
$array_query_elastic['sort'] = [
    [
        "date_post" => [
            "order" => "desc"
        ]
    ]
];
//print_r($array_query_elastic);die(json_encode($array_query_elastic));
$response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data, $array_query_elastic);
$total = $response['hits']['total']['value'];

foreach ($response['hits']['hits'] as $value) {
    if (!empty($value['_source'])) {
        $view = $value['_source'];

        $view['total_investment'] = number_format($view['total_investment']) . ' VNĐ';
        $view['date_post_format'] = nv_date("d/m/Y", $view['date_post']);
        $view['url_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $view['alias'] . '-' . $view['id'] . '.html';
        $array_data[$view['id']] = $view;
        $arr_solicitor_id[$view['solicitor_id']] = $view['solicitor_id'];
    }
}

if ((!empty($q) or !empty($key_search2) or !empty($without_key)) and $page == 1) {
    $nukeVietElasticSearh2 =  new NukeViet\ElasticSearch\Functions($config_bidding['elas_result_host'], $config_bidding['elas_result_port'], 'bids_projects', $config_bidding['elas_result_user'], $config_bidding['elas_result_pass']);

    // Khởi tạo truy vấn Elasticsearch cho bids_result_open
    $array_query_elastic_project = array();
    $array_query_elastic_project['query']['bool'] = [
        'must' => []
    ];

    // Tìm kiếm theo từ khóa chính
    if (!empty($q)) {
        $num = 0;
        $arr_key = explode(',', $q);
        foreach ($arr_key as $key) {
            if ($search_type_content == 1) {
                $key = trim($key);
                $array_query_elastic_project['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_full' => $key
                    ]
                ];
            } else {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                $array_query_elastic_project['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_simple' => $key
                    ]
                ];
            }
        }
    }

    // Tìm theo Từ khóa bổ sung
    if (!empty($key_search2)) {
        $arr_key = explode(',', $key_search2);
        foreach ($arr_key as $key) {
            if ($search_type_content == 1) {
                $key = trim($key);
                $array_query_elastic_project['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_full' => $key
                    ]
                ];
            } else {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                $array_query_elastic_project['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_simple' => $key
                    ]
                ];
            }
        }
    }

    // Tìm theo từ khóa loại trừ
    if (!empty($without_key)) {
        $arr_key = explode(',', $without_key);
        foreach ($arr_key as $key) {
            if ($search_type_content == 1) {
                $key = trim($key);
                $array_query_elastic_project['query']['bool']['must_not'][] = [
                    'match_phrase' => [
                        'searchtext_full' => $key
                    ]
                ];
            } else {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                $array_query_elastic_project['query']['bool']['must_not'][] = [
                    'match_phrase' => [
                        'searchtext_simple' => $key
                    ]
                ];
            }
        }
    }

    $array_query_elastic_project['query']['bool']['must'][] = [
        'term' => [
            'status_test' => 0
        ]
    ];

    if ($sfrom1 > 0 and $sto1 > 0) {
        $array_query_elastic_project['query']['bool']['must'][] = [
            'range' => [
                'add_time' => [
                    "gte" => $sfrom1,
                    "lte" => $sto1
                ]
            ]
        ];
    }

    // Điều kiện pub_time <= thời gian hiện tại
    $array_query_elastic_project['query']['bool']['must'][] = [
        'range' => [
            'pub_time' => [
                'lte' => NV_CURRENTTIME
            ]
        ]
    ];

    $array_query_elastic_project['query']['bool']['must'][] = [
        'term' => [
            'del_time' => 0
        ]
    ];

    // Điều kiện status = 1 hoặc status = 2
    $array_query_elastic_project['query']['bool']['should'][] = [
        'term' => [
            'status' => 1
        ]
    ];
    $array_query_elastic_project['query']['bool']['should'][] = [
        'term' => [
            'status' => 2
        ]
    ];

    $array_query_elastic_project['query']['bool']['must'][] = [
        'term' => [
            'is_last' => 1
        ]
    ];

    $array_query_elastic_project['size'] = 5;
    $array_query_elastic_project['sort'] = [
        [
            "add_time" => [
                "order" => "desc"
            ]
        ]
    ];

    try {
        // Thực hiện truy vấn Elasticsearch
        $response_project = $nukeVietElasticSearh2->search_data($db_config['prefix'] . '_bids_projects', $array_query_elastic_project);
    } catch (Exception $e) {
        file_put_contents(NV_ROOTDIR . '/data/logs/detailelastic_log_bids_project_' . date('Ymd') . '.txt', "\n\n ---------- [" . date('d-m-Y H:i:s') . "] Lỗi detailelastic_log_bids_project: \n" . print_r($e, true), FILE_APPEND);
        file_put_contents(NV_ROOTDIR . '/data/logs/detailelastic_log_bids_project_' . date('Ymd') . '.txt', "\n array_query_elastic_project: " . print_r($array_query_elastic_project, true), FILE_APPEND);
        trigger_error(print_r($e, true));
    }

    // Kết hợp kết quả từ Elasticsearch
    foreach ($response_project['hits']['hits'] as $value) {
        if (!empty($value['_source'])) {
            $view = $value['_source'];
            $array_data_project[$view['project_id']] = $view;
        }
    }
}
