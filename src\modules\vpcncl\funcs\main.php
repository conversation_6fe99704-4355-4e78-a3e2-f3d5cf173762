<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */

 if (!defined('NV_IS_MOD_VPCNCL')) {
    die('Stop!!!');
}

$page = $nv_Request->get_page('page', 'get', 1);
$query = $nv_Request->get_title('query', 'get', '');
$org_number = $nv_Request->get_title('org_number', 'get', '');
$lab_name = $nv_Request->get_title('lab_name', 'get', '');
$host_name = $nv_Request->get_title('host_name', 'get', '');
$status= $nv_Request->get_int('status', 'get', 100);
$id_lv = $nv_Request->get_typed_array('linhvuc', 'get', []);
$id_province = $nv_Request->get_typed_array('province', 'get', []);

// <PERSON><PERSON>u input tìm số hiệu nhập dạng abc-1 thì tách lấy số để search
if (preg_match('/\-([\d]+)/', $org_number, $matches)) {
    $org_number = $matches[1];
}

// Xử lý chặn trang
if ($page < 1) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_wrong_page') . $btn, 'danger');

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

if ($page > 100) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_max_searchpage') . $btn);

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

$page_title =  $nv_Lang->getModule($module_name);
$description = $nv_Lang->getModule($module_name . '_desc');
$key_words = $module_info['keywords'];

$where = 'organization_name != "" AND type=' . $type_id;
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;
/**
 * Xem danh sách tổ chức theo Lĩnh vực hoặc tỉnh thành
 * Trường hợp nếu chỉ select 1 tỉnh thành hoặc 1 lĩnh vực thì sẽ đưa về phân loại lv- hoặc tt-
 * Phân loại theo tỉnh thành: tt-
 * Phân loại theo lĩnh vực: lv-
 */

if (sizeof($id_lv) == 0 && isset($is_filter_province)) {
    $page_title = sprintf($nv_Lang->getModule('view_by_tt'), $module_info['site_title'], $location_id_map[$view_by_id]['title']);
    $id_province[] = $view_by_id;
} elseif (sizeof($id_province) == 0 && isset($is_filter_linhvuc)) {
    $page_title = sprintf($nv_Lang->getModule('view_by_tt'), $module_info['site_title'], $linhvuc_id_map[$view_by_id]['title']);
    $id_lv[] = $view_by_id;
}

$where_conditions = array();

// Kiểm tra hợp lệ của $id_lv và $id_province
$all_ids = array_merge($id_lv, $id_province);
if (!empty($all_ids)) {
    foreach ($all_ids as $v) {
        if (!is_numeric($v)) {
            nv_redirect_location($base_url);
        }
    }
}
// Kiểm tra $type_id có phải là 2 hoặc 5 không
if (in_array($type_id, $show_specializes)) {
    if (!empty($id_lv)) {
        $unique_linhvuc = array_unique($id_lv);
        $where_conditions[] = '(' . implode(' OR ', array_map(function($value) {
            return 'FIND_IN_SET(' . $value . ', specialize_id)';
        }, $unique_linhvuc)) . ')';

        if (sizeof($id_lv) == 1 && sizeof($id_province) == 0) {
            $base_url .= '&amp;' . NV_OP_VARIABLE . '=lv-' . $linhvuc_id_map[$id_lv[0]]['alias'];
        } else {
            foreach ($unique_linhvuc as $value) {
                $base_url .= '&amp;linhvuc[]=' . $value;
            }
        }
    }
}

if (!empty($id_province)) {
    $unique_province = array_unique($id_province);
    $where_conditions[] = '(' . implode(' OR ', array_map(function($value) {
        return 'FIND_IN_SET(' . $value . ', province_id)';
    }, $unique_province)) . ')';

    if (sizeof($id_province) == 1 && sizeof($id_lv) == 0) {
        $base_url .= '&amp;' . NV_OP_VARIABLE . '=tt-' . $location_id_map[$id_province[0]]['alias'];
    } else {
        foreach ($unique_province as $value) {
            $base_url .= '&amp;province[]=' . $value;
        }
    }
}

if (in_array($status, [-1, 0, 1])) {
    $where_conditions[] = '(status=' . $status . ')';
    $base_url .= '&amp;status=' . $status;
}

if (!empty($lab_name)) {
    $where_conditions[] = '(organization_name LIKE "%' . $db->dblikeescape($lab_name) . '%")';
    $base_url .= '&amp;lab_name=' . urlencode($lab_name);
}

if (!empty($host_name)) {
    $where_conditions[] = '(host_unit LIKE "%' . $db->dblikeescape($host_name) . '%")';
    $base_url .= '&amp;host_name=' . urlencode($host_name);
}

if (isset($org_number) && is_numeric($org_number)) {
    $where_conditions[] = '(organization_number =' . $db->dblikeescape($org_number) . ')';
    $base_url .= '&amp;org_number=' . $org_number;
}

// Tìm kiếm ngẫu nhiên
if (!empty($query)) {
    $query_escaped = $db->dblikeescape($query);
    $whereOr = [
        '(organization_name LIKE "%' . $query_escaped . '%")',
        '(host_unit LIKE "%' . $query_escaped . '%")',
        '(organization_number LIKE "%' . $query_escaped . '%")'
    ];
    $where_conditions[] = '(' . implode(' OR ', $whereOr) . ')';
    $base_url .= '&amp;query=' . urlencode($query);
}

// Nối tất cả các điều kiện với AND
if (!empty($where_conditions)) {
    $where_conditions_string = implode(' AND ', $where_conditions);
    $where .= ' AND ' . $where_conditions_string;
}

$db->sqlreset()
    ->select('COUNT(organization_number)')
    ->from(NV_PREFIXLANG . '_vpcncl_to_chuc')
    ->where($where);
$sth = $db->prepare($db->sql());
$sth->execute();
$num_items = $sth->fetchColumn();

$home_page = $nv_Request->get_int('home_page', 'get', 0);
$per_page = ($home_page == 1) ? 10 : 20;
$urlappend = '&amp;' . NV_OP_VARIABLE . '=page-';
betweenURLs($page, ceil($num_items/$per_page), $base_url, $urlappend, $prevPage, $nextPage);
$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
$page_url = $base_url;
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}
$canonicalUrl = getCanonicalUrl($page_url);

/*  Xử lý hiển thị dữ liệu
    **********************
*/
$db->select('*');
$db->order('organization_number ASC');
$db->limit($per_page);
$db->offset(($page - 1) * $per_page);
$orgs = $db->query($db->sql())->fetchAll();

// #2415: Thêm hằng chặn index
$query_params = [];
parse_str(parse_url($page_url, PHP_URL_QUERY), $query_params);
// Danh sách các tham số cần xóa
$params_to_remove = [
    'amp;' . NV_NAME_VARIABLE,
    'amp;' . NV_OP_VARIABLE,
    NV_LANG_VARIABLE
];
$query_params = array_diff_key($query_params, array_flip($params_to_remove));
$has_other_query_params = false;
foreach ($query_params as $key => $value) {
    if ($key !== 'amp;page') {
        $has_other_query_params = true;
        break;
    }
}
if (!empty($query) || !empty($org_number) || !empty($lab_name) || !empty($host_name) || empty($orgs) || $has_other_query_params) {
    $nv_BotManager->setFollow()->setNoIndex();
}

$xtpl = new XTemplate('main.tpl', get_module_tpl_dir('main.tpl'));
$xtpl->assign('TEMPLATE', $module_info['template']);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('PAGE_TITLE', $page_title);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('Q', $query);
$xtpl->assign('ORG_NUMBER', $org_number);
$xtpl->assign('LAB_NAME', $lab_name);
$xtpl->assign('HOST_NAME', $host_name);

if (!$global_config['rewrite_enable']) {
    $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php');
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('OP', $op);
    $xtpl->parse('main.no_rewrite');
} else {
    $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
}
foreach ($array_data['province'] as $province) {
    $xtpl->assign('PROVINCE', $province);
    $xtpl->assign('selected_location', in_array($province['id'], $id_province) ? 'selected' : '');
    $xtpl->parse('main.search_province.province');
}
$xtpl->parse('main.search_province');

foreach (ORG_STATUSES as $key => $value) {
    $xtpl->assign('STATUS', ['key' => $key, 'value' => $value]);
    $xtpl->assign('selected_status', $key == $status ? 'selected' : '');
    $xtpl->parse('main.status');
}

if (!in_array($type_id, $hiden_host_unit)) {
    $xtpl->parse('main.search_chuquan');
}

if (in_array($type_id, $show_specializes)) {
    foreach ($array_data['linhvuc'] as $lv) {
        $xtpl->assign('LINHVUC', $lv);
        $xtpl->assign('selected_linhvuc', in_array($lv['id'], $id_lv) ? 'selected' : '');
        $xtpl->parse('main.search_linhvuc.linhvuc');
    }
    $xtpl->parse('main.search_linhvuc');
}

$index = $per_page * ($page-1) + 1;
foreach ($orgs as $org) {
    if (empty($org['organization_number'])) {
        continue;
    }
    $org['stt'] = $index;
    $org['organization_number'] = strtoupper($module_name) . ' - ' . $org['organization_number'];
    $org['status'] = ORG_STATUSES[$org['status']];
    $org['accreditation_location'] = preg_replace("/(?<=\w)\|<>\|(?=\w)/", '<br><br><i class="fa fa-map-marker" aria-hidden="true"></i> ', $org['accreditation_location']);
    $org['accreditation_location'] = str_replace("|<>|", '', $org['accreditation_location']);

    // xử lý lĩnh vực
    if ($type_id != 2 || $type_id != 5) {
        $org['specialize'] = process_linhvuc($org['specialize_id'], $linhvuc_id_map);
    }
    // xử lý tỉnh thành
    $org['province'] = process_province($org['province_id'], $location_id_map);
    $org['map_host_unit'] = process_donvichuquan($org['host_unit'], $org['solicitor_id'], $org['contractor_id']);
    $org['detail_link'] = $page_url . '&amp;' .  NV_OP_VARIABLE . '=' . $org['alias'] . '-' . $org['organization_id'] . $global_config['rewrite_exturl'];
    $xtpl->assign('ORG', $org);
    // Xử lý đơn vị chủ quản
    if ($type_id == 0 || $type_id == 3) {
        $xtpl->parse('main.org.host_unit');
        $xtpl->parse('main.org.mobile_host_unit');
    }
    // Xử lý lĩnh vực
    if (in_array($type_id, $show_specializes)) {
        $xtpl->parse('main.org.specialize');
        $xtpl->parse('main.org.mobile_specialize');
    }
    $xtpl->parse('main.org');
    $index++;
}

if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}

if (!empty($orgs)) {
    // Đơn vị chủ quản chỉ có ở VILAS và VILAS-MED
    if ($type_id == 0 || $type_id == 3) {
        $xtpl->parse('main.org_results.show_host_unit');
    }
    // Lĩnh vực chỉ có ở VILAS
    if (in_array($type_id, $show_specializes)) {
        $xtpl->parse('main.org_results.show_specialize');
    }
    $xtpl->parse('main.org_results');
} else {
    $xtpl->parse('main.no_results');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
$contents .= sprintf($nv_Lang->getModule('time_query'), number_format((microtime(true) - NV_START_TIME), 3, '.', ''), number_format($num_items, 0, ",", "."));

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
