<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */

if (!defined('NV_IS_MOD_LABORATORY')) {
    die('Stop!!!');
}

$array_data = $error = $where = [];
$q = $nv_Request->get_title('q', 'post,get');
$location_alias = '';
if (isset($array_op[0])) {
    $location_alias = strtolower($array_op[0]);
}
$page = $nv_Request->get_page('page', 'post,get', 1);
$home_page = $nv_Request->get_int('home_page', 'get', 0);
$per_page = ($home_page == 1) ? 10 : 20;
$id = 0;

// Giói hạn số trang tìm kiếm
if ($page < 1) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_wrong_page') . $btn, 'danger');
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}
if ($page > 100) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_max_searchpage') . $btn);
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;
$global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;

// Fetch Limit
$db->select('id, title, alias')
    ->from(NV_PREFIXLANG . '_location_province')
    ->order('title ASC');
$province_list = $nv_Cache->db($db->sql(), '', 'location');
// Thêm giá trị chưa phân loại
$new_item = array(
    'id' => 0,
    'title' => $nv_Lang->getModule('no_title'),
    'alias' => change_alias($nv_Lang->getModule('no_title'))
);
array_push($province_list, $new_item);
$province_lists = [];
foreach ($province_list as $value) {
    $province_lists[$value['id']] = [
        'id' => $value['id'],
        'alias' => $value['alias'],
        'title' => $value['title']
    ];
}
$location_check = [];
$location = -1;

foreach($province_list as $value) {
    if (preg_match('/hồ chí minh/iu', $value['title'])) {
        $location_check[$value['id']] = 'Hồ Chí Minh';
    } else {
        $location_check[$value['id']] = $value['title'];
    }
    if (strcmp($location_alias, strtolower($value['alias'])) == 0) {
        $location = $value['id'];
    }
}

$laboratorys_list = $nv_Lang->getModule('laboratorys_list');
$description = $nv_Lang->getModule('laboratory') . ' | ' . $nv_Lang->getModule('laboratorys_list') . NV_TITLEBAR_DEFIS . urlRewriteWithDomain($base_url, NV_MY_DOMAIN);

if ($location >= 0) {
    $base_url .= '/' . $location_alias;
    $global_lang_url .= '/' . $location_alias;
    $location_initial = ' ' . $nv_Lang->getModule('location_initial') . ' ';    // dùng chung cho tỉnh hoặc thành phố

    $location_title = $location_check[$location];

    $laboratorys_list .=  $location_initial . $location_title;
    $description = sprintf($nv_Lang->getModule('description_title'), $location_title);
    // $where[] = '(dia_chi LIKE ' . $db->quote('%' . $location_title . '%') . ' OR dia_chi_phongtn LIKE' . $db->quote('%' . $location_title . '%') . ')';
    // #2468 Chỉnh sửa điều kiện chỉ tìm theo dia_chi_phongtn
    $where[] = '(province_id =' . $location . ')';
}

if (!empty($q)) {
    $base_url .= '&amp;q=' . urlencode($q);
    $global_lang_url .= '&amp;q=' . urlencode($q);
    $description = $nv_Lang->getModule('laboratory') . ' | ' . $nv_Lang->getModule('laboratorys_list') . NV_TITLEBAR_DEFIS . $nv_Lang->getModule('search') . NV_TITLEBAR_DEFIS . $q . NV_TITLEBAR_DEFIS . urlRewriteWithDomain($base_url, NV_MY_DOMAIN);
    $where[] = "(tieu_de LIKE " . $db->quote('%' . $q . '%') . " OR ten_phongtn LIKE " . $db->quote('%' . $q . '%') . " OR ma_so_thue LIKE " . $db->quote('%' . $q . '%') . " OR ma_so_phongtn LIKE " . $db->quote('%' . $q . '%') . " OR nguon LIKE " . $db->quote('%' . $q . '%') . ')';
}

$page_title = $laboratorys_list;

if (!empty($where)) {
    $where = implode(' AND ', $where);
}

$db->sqlreset()
    ->select('COUNT(id)')
    ->from(NV_PREFIXLANG . '_phongtn')
    ->where($where);
$sth = $db->prepare($db->sql());
$sth->execute();
$num_items = $sth->fetchColumn();

$db->select('*')
    ->order('id_url DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$array_data = $db->query($db->sql())->fetchAll();
$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
$page_url = $base_url;
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}
// #2415: Thêm hằng chặn index
$query_params = [];
parse_str(parse_url($page_url, PHP_URL_QUERY), $query_params);
// Danh sách các tham số cần xóa
$params_to_remove = [
    'amp;' . NV_NAME_VARIABLE,
    'amp;' . NV_OP_VARIABLE,
    NV_LANG_VARIABLE
];
$query_params = array_diff_key($query_params, array_flip($params_to_remove));
$has_other_query_params = false;
foreach ($query_params as $key => $value) {
    if ($key !== 'amp;page') {
        $has_other_query_params = true;
        break;
    }
}
if (!empty($q) || empty($array_data) || $has_other_query_params) {
    $nv_BotManager->setFollow()->setNoIndex();
}
$canonicalUrl = getCanonicalUrl($page_url);
betweenURLs($page, ceil($num_items/$per_page), $base_url, '&amp;page=', $prevPage, $nextPage);
// Không cho đánh alias tùy ý
$base_url_rewrite = nv_url_rewrite($base_url, true);
$base_url_check = str_replace('&amp;', '&', $base_url_rewrite);
$request_uri = rawurldecode($_SERVER['REQUEST_URI']);

if (($nv_Request->isset_request('q', 'get') and empty($q)) or isset($array_op[2]) or (strpos($_SERVER['REQUEST_URI'], $base_url_check) !== 0 and strpos(NV_MY_DOMAIN . $_SERVER['REQUEST_URI'], $base_url_check) !== 0)) {
    nv_redirect_location($base_url_rewrite);
}

$xtpl = new XTemplate('main.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('laboratorys_list', $laboratorys_list);
if (!$global_config['rewrite_enable']) {
    $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php');
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);
    $xtpl->parse('main.no_rewrite');
} else {
    $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
}
if (empty($array_data)) {
    $xtpl->parse('main.view.empty');
} else {
    $stt = (($page - 1) * $per_page) + 1;
    foreach ($array_data as $view) {
        $view['stt'] = $stt++;
        $view['province'] = $province_lists[$view['province_id']]['title'];
        $view['link_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . process_ma_ptn($view['ma_so_phongtn']);
        $view['ma_so_phongtn'] = trim(strip_tags($view['ma_so_phongtn'])) . (!empty($view['ma_so_phongtn_mr']) ? ' ' . $view['ma_so_phongtn_mr'] : '');
        $view['link_province'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $province_lists[$view['province_id']]['alias'];
        $view['ma_so_ten_ptn'] = $view['ma_so_phongtn'] . ' - ' . $view['ten_phongtn'];
        $xtpl->assign('VIEW', $view);
        $xtpl->parse('main.loop');
    }
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
    }
}
if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
$contents .= sprintf($nv_Lang->getModule('time_query'), number_format((microtime(true) - NV_START_TIME), 3, '.', ''), number_format($num_items, 0, ",", "."));
include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
