<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 10 Nov 2020 06:56:08 GMT
 */
if (!defined('NV_IS_MOD_DAU_GIA')) {
    die('Stop!!!');
}

$query = $db->query('SELECT * FROM nv4_dau_gia_bidder_dgv WHERE id=' . $db->quote($id_dgv));
$array_data = $query->fetch();

if (!empty($array_data)) {
    $page_title = $nv_Lang->getModule('auctioneer_info') . $array_data['name'];
    $array_mod_title[] = array(
        'title' => $nv_Lang->getModule('auctioneer'),
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['auctioneer'], true)
    );
    $array_mod_title[] = array(
        'title' => $page_title,
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['auctioneer'] . '/' . $array_data['alias'] . '-' . $array_data['id'] . $global_config['rewrite_exturl'], true)
    );

    $space = ', ';
    $base_link = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['auctioneer'];
    if ($array_data['id_ward'] > 0 && $array_data['id_district'] > 0 && $array_data['id_province'] > 0) {
        // Xử lý địa chỉ xã/phường với URL đẹp
        if ($array_data['id_ward'] > 0) {
            $db->sqlreset()
                ->select('title, alias')
                ->from($db_config['prefix'] . '_vi_location_ward')
                ->where('id =' . $array_data['id_ward']);
            $sql = $db->sql();
            $result = $db->query($sql);
            $row = $result->fetch();
            if (!empty($row)) {
                // URL cho xã/phường sử dụng dạng URL đẹp X-alias-id
                $link = $base_link . '/X-' . $row['alias'] . '-' . $array_data['id_ward'];
                $array_data['address'] = '<a href="' . $link . '">' . $row['title'] . '</a>';
            }
        }

        // Xử lý địa chỉ quận/huyện với URL đẹp
        if ($array_data['id_district'] > 0) {
            $db->sqlreset()
                ->select('title, alias')
                ->from($db_config['prefix'] . '_vi_location_district')
                ->where('id =' . $array_data['id_district']);
            $sql = $db->sql();
            $result = $db->query($sql);
            $row = $result->fetch();
            if (!empty($row)) {
                // URL cho quận/huyện sử dụng dạng URL đẹp H-alias-id
                $link = $base_link . '/H-' . $row['alias'] . '-' . $array_data['id_district'];
                $array_data['address'] = isset($array_data['address']) ? $array_data['address'] . $space : '';
                $array_data['address'] .= '<a href="' . $link . '">' . $row['title'] . '</a>';
            }
        }

        // Xử lý địa chỉ tỉnh/thành phố với URL đẹp
        if ($array_data['id_province'] > 0) {
            $db->sqlreset()
                ->select('title, alias')
                ->from($db_config['prefix'] . '_vi_location_province')
                ->where('id =' . $array_data['id_province']);
            $sql = $db->sql();
            $result = $db->query($sql);
            $row = $result->fetch();
            if (!empty($row)) {
                // URL tỉnh/thành phố sử dụng dạng URL đẹp T-alias-id
                $link = $base_link . '/T-' . $row['alias'] . '-' . $array_data['id_province'];
                $array_data['address'] = isset($array_data['address']) ? $array_data['address'] . $space : '';
                $array_data['address'] .= '<a href="' . $link . '">' . $row['title'] . '</a>';
            }
        }
    }

    // lấy các thông tin khác
    $query = $db->query('SELECT * FROM nv4_dau_gia_bidder WHERE id_bidder=' . $array_data['id_bidder'] . '');
    $arr_bidder = [];
    while ($row = $query->fetch()) {
        $row['full_address'] = build_full_address($row);
        $arr_bidder[$row['id_bidder']] = $row;
    }

    $page_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['auctioneer'] . '/' . $array_data['alias'] . '-' . $array_data['id'] . $global_config['rewrite_exturl'];
    $canonicalUrl = getCanonicalUrl($page_url);

    // kiểm tra xem có đấu giá viên nào bị trùng hay không
    $arrCheckDGV = $db->query('SELECT * FROM nv4_dau_gia_bidder_dgv WHERE name = ' . $db->quote($array_data['name']) . ' AND id != ' . $array_data['id'])->fetchAll();

    $contents = nv_theme_dau_gia_auctioneerdetail($array_data, $arr_bidder, $arrCheckDGV);

    // Schema.org cho chi tiết đấu giá viên
    $schema = [
        "@context" => "https://schema.org",
        "@type" => "Person",
        "name" => $array_data['name'],
        "jobTitle" => $nv_Lang->getModule('auctioneer'),
        "identifier" => [
            [
                "@type" => "PropertyValue",
                "name" => $nv_Lang->getModule('dgv_number_cchn'),
                "value" => $array_data['number_cchn']
            ],
            [
                "@type" => "PropertyValue",
                "name" => $nv_Lang->getModule('dgv_number_card'),
                "value" => $array_data['num_dgv']
            ]
        ],
        "birthDate" => $array_data['birthday'],
        "address" => strip_tags($array_data['address'])
    ];
    $nv_schemas[] = $schema;

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
} else {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['auctioneer']);
    die();
}
