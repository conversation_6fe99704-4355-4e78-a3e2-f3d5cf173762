<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_BIDDING'))
    die('Stop!!!');

// kết nối tới ElasticSearh
$nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_result_host'], $module_config[$module_name]['elas_result_port'], 'dauthau_result_goods', $module_config[$module_name]['elas_result_user'], $module_config[$module_name]['elas_result_pass']);
$page_n = $page;
if ($page > 100) {
    $page_n = 1;
}
$search_elastic = [];
$search_elastic['must'][] = [
    'match' => [
        'type_info' => [
            'query' => 0
        ]
    ]
];
if (!empty($key)) {
    if (!isset($is_five_keyword)) {
        $key = trim($key);
        foreach ($array_type_hh as $_key => $title) {
            if ($_key == $array_key['type'] || $array_key['type'] == 'all') {
                if (isset($l) && $l === 0) {
                    $key_split = preg_split("/[\s,.;]+/", $key);
                    foreach ($key_split as $k) {
                        $search_elastic['should'][] = [
                            "match_phrase" => [
                                $_key => $k
                            ]
                        ];
                    }
                } else {
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            $_key => $key
                        ]
                    ];
                }
            }
        }
    } else {
        $arr_key = explode(',', $key);
        foreach ($array_type_hh as $_key => $title) {
            if ($_key == $array_key['type'] || $array_key['type'] == 'all') {
                foreach ($arr_key as $keyword) {
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            $_key => $keyword
                        ]
                    ];
                }
            }
        }
    }
} else {
    $search_elastic['must_not'][] = [
        "match_phrase" => [
            "goods_name.keyword" => ''
        ]
    ];
}
// result_id
if (!empty($result)) {
    foreach ($result as $_result) {
        $search_elastic['must'][] = [
            "match_phrase" => [
                "result_id" => $_result
            ]
        ];
    }
}

if (!empty($_arr_sfrom) && !empty($_arr_sto)) {
    $_gte = mktime(0, 0, 0, $_arr_sfrom[1], $_arr_sfrom[0], $_arr_sfrom[2]);
    $_lte = mktime(23, 59, 59, $_arr_sto[1], $_arr_sto[0], $_arr_sto[2]);

    $search_elastic['must'][]['range']['finishtime'] = [
        "gte" => $_gte,
        "lte" => $_lte
    ];
}
if (!empty($key)) {
    $search_elastic['minimum_should_match'] = '1';
    $search_elastic['boost'] = '1.0';
}
$temp_query = array();
$array_query_elastic = array();
if (!empty($array_points_goods)) {
    $temp_query = [
        "terms" => [
            "id" => array_values($array_points_goods)
        ]
    ];
    $search_elastic_buy = $search_elastic;
    $search_elastic_buy['filter']['terms']['id'] = $array_points_goods;
    $array_query_elastic['query']['bool'] = $search_elastic_buy;
    $array_query_elastic['track_total_hits'] = 'true';
    $array_query_elastic['size'] = $per_page;
    $array_query_elastic['from'] = ($page_n - 1) * $per_page;
    $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_result_goods', $array_query_elastic);
    $num_items_buy = $response['hits']['total']['value'] - ($page_n - 1) * $per_page;
    foreach ($response['hits']['hits'] as $value) {
        if (!empty($value['_source'])) {
            $view = $value['_source'];
            $_goods[] = $view;
        }
    }
    unset($response);
}
$array_query_elastic = array();
if (!empty($search_elastic)) {
    $array_query_elastic['query']['bool'] = $search_elastic;
}
$array_query_elastic['track_total_hits'] = 'true';
$response = array();
$response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_result_goods', $array_query_elastic);
$num_items = $response['hits']['total']['value'];
unset($response);
$goods = [];
if ($num_items_buy < $per_page) {
    $array_query_elastic['from'] = ($page_n - 1) * $per_page;

    if (!empty($temp_query) && $array_query_elastic['from'] < $num_items_buy) {
        //Nếu số lượng hàng hoá đã mua ít hơn $array_query_elastic['from'] thì không cần câu lệnh này
        $array_query_elastic['query']['bool']['filter']['bool']['must_not'][] = $temp_query;
    }

    $num_items_buy = $num_items_buy > 0 ? $num_items_buy : 0;
    $array_query_elastic['size'] = $per_page - $num_items_buy;
    $array_query_elastic['sort'] = [
        [
            "finishtime" => [
                "order" => "desc"
            ]
        ]
    ];
    $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_result_goods', $array_query_elastic);
    foreach ($response['hits']['hits'] as $value) {
        if (!empty($value['_source'])) {
            $view = $value['_source'];
            $goods[] = $view;
        }
    }
    $goods = !empty($_goods) ? array_merge($_goods, $goods) : $goods;
} else {
    $goods = $_goods;
}

// Lấy thêm dữ liệu bên result để tương đương với bên goods_mysql
if (!empty($goods)) {
    unset($nukeVietElasticSearh);
    $nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_result_host'], $module_config[$module_name]['elas_result_port'], 'dauthau_result', $module_config[$module_name]['elas_result_user'], $module_config[$module_name]['elas_result_pass']);

    $array_query_elastic = $search_elastic = [];
    $search_elastic['filter']['terms']['id'] = array_values(array_filter(array_unique(array_column($goods, 'result_id'))));
    $array_query_elastic['query']['bool'] = $search_elastic;
    $array_query_elastic['size'] = sizeof($goods);
    
    $response = array();

    $response = $nukeVietElasticSearh->search_data('', $array_query_elastic);
    foreach ($response['hits']['hits'] as $value) {
        if (!empty($value['_source'])) {
            $view = $value['_source'];
            foreach ($goods as $kg => $vg) {
                if ($vg['result_id'] == $view['id']) {
                    foreach ($view as $k => $v) {
                        if (in_array($k, array_keys($vg))) {
                            continue;
                        }
                        $goods[$kg][$k] = $v;
                    }
                }
            }
        }
    }
}
foreach ($goods as &$good) {
    $good['bid_price'] = money2number($good['bid_price']);
}
unset($good);
// Kiểm tra hàng hóa từ dữ liệu bóc về của bộ y tế
if (isset($flag)) {

    $nukeVietElasticSearh_1 = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_host'], $module_config[$module_name]['elas_port'], 'dauthau_medical_equipment', $module_config[$module_name]['elas_user'], $module_config[$module_name]['elas_pass']);

    $search_byt = [];
    $byt_list = [];
    if (!empty($key)) {
        $key = trim($key);
        $search_byt['should'][] = [
            "match_phrase" => [
                "name" => $key
            ]
        ];
    }else{
        $search_byt['must_not'][] = [
            "match_phrase" => [
                "name" => ''
            ]
        ];
    }

    if (!empty($key)) {
        $search_byt['minimum_should_match'] = '1';
        $search_byt['boost'] = '1.0';
    }

    $array_query = array();
    if (!empty($search_byt)) {
        $array_query['query']['bool'] = $search_byt;
    }
    $array_query['track_total_hits'] = 'true';

    $array_query['size'] = $per_page;
    $array_query['from'] = ($page_n - 1) * $per_page;
    $response = $nukeVietElasticSearh_1->search_data('nv4_thiet_bi_byt', $array_query);
    $num_items_byt = $response['hits']['total']['value'];
    foreach ($response['hits']['hits'] as $value) {
        if (!empty($value['_source'])) {
            $view = $value['_source'];
            $view['price_bid'] = money2number($view['price_bid']);
            $byt_list[] = $view;
        }
    }
}
