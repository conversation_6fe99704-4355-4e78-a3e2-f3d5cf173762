﻿/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */


/* GLOBAL */

a.titleprojetc,
.titleprojetc {
    font-weight: bold;
    color: #0066FF
}

a.titleprojetc:hover {
    color: #0066CC
}

span.pdatetime {
    color: #999999;
    font-size: 11px
}

.center {
    text-align: center
}

.infoerror {
    color: red;
    font-weight: bold
}

span.keyword {
    background-color: #FFFF66
}

.hr {
    clear: both;
    margin: 10px 0px 10px 0px;
    border-bottom: 1px #CCCCCC solid
}

.boxpro {
    border: 1px #DADADA solid;
    display: block;
    box-shadow: 0px 0px 2px 0px #dadada;
    border-radius: 2px;
    margin-top: 10px;
    margin-bottom: 10px;
    padding: 5px
}

.aright {
    text-align: right
}

tfoot .pagination {
    margin: 0;
}

table a.btn-default {
    color: #333;
}

.w90 {
    width: 90px;
}

.w10 {
    width: 10px;
}

.w150 {
    width: 150px;
}

.w200 {
    width: 200px;
}

.m-bottom {
    margin-bottom: 10px;
}

.m-right {
    margin-right: 10px;
}

.list_item_block {}

.list_item_block>.items_block {
    margin-bottom: 7px;
    padding-bottom: 7px;
    border-bottom: 1px #DADADA solid;
}

.list_item_block>.items_block:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: 0;
}

.listing-summary .org-code::before {
    content: none;
}

.listing-summary .star {
    margin-left: 1px;
}

.listing-summary .website {
    clear: both;
    color: #808080;
    float: left;
    font-size: 0.9em;
    margin-bottom: 3px;
    vertical-align: top;
}

.listing-summary .fields {
    clear: both;
}

.listing-summary .fieldRow {
    display: inline;
    font-size: 0.9em;
    height: 22px;
    line-height: 1.5em;
    padding: 0 2px 0 0;
}

.listing-summary h3 {
    margin: 5px 0 10px 0;
}


/* BOX */

.alboxw {
    padding: 0;
    margin: 0px 0px 6px 0px;
    display: block;
}

.alwrap {
    border: 1px #DADADA solid;
    display: block;
    box-shadow: 0px 0px 2px 0px #dadada;
    border-radius: 2px
}

.alheader {
    height: 30px;
    overflow: hidden;
    display: block;
    background-image: url('../images/onlinetest/cat-header-bg.png');
    background-repeat: repeat-x;
    line-height: 30px;
    padding-left: 10px;
    border-bottom: 1px #DADADA solid
}

.alheader span {
    font-weight: bold;
    color: #0066FF
}

.alcontent {
    display: block;
    padding: 5px;
    margin: 0px
}


/* SEARCH */

.pro-search {
    padding: 0;
    margin: 0
}

.pro-search table {
    padding: 0;
    margin: 0
}


/* GEN PAGE */

.generate_page {
    margin: 5px;
    text-align: right;
    font-weight: bold
}


/* FROM - TABLE */

.table-search {
    border-collapse: collapse
}

.table-search td {
    vertical-align: top
}

.table-search .txt-full {
    border: 1px #dadada solid;
    color: #666666;
    width: 99%
}

.table-search .txt-half {
    border: 1px #dadada solid;
    color: #666666;
    width: 30%
}

.table-search input[type=button],
.table-search input[type=submit] {
    background-color: #f5f5f5;
    border: 1px #DADADA solid;
    border-radius: 3px;
    margin-right: 5px;
    color: #333;
    cursor: pointer;
    box-shadow: 0px 0px 2px 0px #dadada
}

.table-search input[type=button]:hover,
.table-search input[type=submit]:hover {
    box-shadow: 0px 0px 4px 0px #ccc
}

.table-search input[type=submit]:disabled {
    color: #CCCCCC
}

.inline-form {
    display: inline;
}

.buss-tool .btn {
    margin-bottom: 10px;
}

.buss-tool .btn:last-child {
    margin-bottom: 0;
}

.m-top {
    margin-top: 10px;
}


/* Danh sách doanh nghiệp kiểu hiện đại hơn */

.ellipsis {
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.bumlist-wraper {
    margin-bottom: 15px;
}

.bumlist-header-content,
.bumlist-item-content {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    flex-direction: row;
    justify-content: space-between;
    align-items: stretch;
    align-content: stretch;
}

.bumlist-header-content {
    background-color: #f2f3f4;
    color: #666;
    border-bottom: 1px solid #eee;
}

.bumlist-column {
    padding: 15px 0 14px 0;
}

.bumlist-column:not(.bumlist-column-company) {
    flex: 0 0 150px;
}

.bumlist-column.bumlist-column-amount {
    flex: 0 0 90px;
}

.bumlist-column.bumlist-column-company {
    flex-grow: 1;
    min-width: 0;
    padding-left: 15px;
}

.bumlist-column span.h {
    display: none;
}

.bumlist-item-content {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.bumlist-item-content .bumlist-column {
    padding-right: 5px;
}

.bumlist-item-content .photo {
    box-shadow: 0 1px 4px rgba(0, 0, 0, .05);
    width: 37px;
    height: 37px;
    line-height: 35px;
    vertical-align: middle;
    border-radius: 4px;
    overflow: hidden;
    border-bottom: 1px solid #eee;
    border-right: 1px solid #e9eaec;
    border-left: 1px solid #e9eaec;
    border-top: 1px solid #f7f8f9;
    margin-right: 12px;
    float: left;
}

.bumlist-item-content .img {
    max-height: 37px;
    max-width: 37px;
    min-height: inherit;
    width: 100%;
    border: none;
    padding: 0;
}

.butip-wrap .tooltip-inner {
    width: 180px!important;
}

.butip-item>img {
    float: left;
    width: 160px;
    height: auto;
    margin-right: 10px;
}

.chart_bids, .chart_price {
    position: absolute;
    z-index: 1;
}
.chart_wrap span {
    font-size: 13px;
    color: #333;
    font-weight: 700;
}
#province_name {
    font-size: 13px;
    color: #333;
    font-weight: 700;
    display: block;
}
.map_province_wrapper.pad-bottom {
    padding-bottom: 150px;
}
.map_province_wrapper .apexcharts-legend-marker {
    flex-shrink: 0;
}
.map_province_wrapper .apexcharts-legend {
    justify-content: left !important;
}
.map_province_wrapper .province_mobile {
    width: 60%;
    padding: 20px 0 10px;
    margin: 0 auto;
    position: relative;
}
.map_province_wrapper {
    position: relative;
}
.chart_price .apexcharts-title-text {
    opacity: 0 !important;
}
.apexcharts-legend-text {
    font-size: 13px !important;
}
.interest_chart_wrapp .apexcharts-legend-series:nth-child(1) .apexcharts-legend-marker,
.interest_chart_wrapp .apexcharts-legend-series:nth-child(2) .apexcharts-legend-marker,
.interest_chart_wrapp .apexcharts-legend-series:nth-child(3) .apexcharts-legend-marker,
.interest_chart_wrapp .apexcharts-legend-series:nth-child(4) .apexcharts-legend-marker,
.interest_chart_wrapp .apexcharts-legend-series:nth-child(5) .apexcharts-legend-marker,
.interest_chart_wrapp .apexcharts-legend-series:nth-child(6) .apexcharts-legend-marker {
    border-radius: 2px !important;
}
.interest_chart_wrapp .apexcharts-legend-series:nth-child(7) .apexcharts-legend-marker,
.interest_chart_wrapp .apexcharts-legend-series:nth-child(8) .apexcharts-legend-marker{
    height: 4px !important;
    width: 30px !important;
    border-radius: 2px !important;
}
.apexcharts-text tspan {
    font-size: 13px;
}
.interest_chart_wrapp .apexcharts-legend{
    display: inline-block !important;
    width: 90% !important;
    left: 10% !important;
}

.province-conent .map_province_wrapper .tooltip_truongsa {
    top: 700px;
    width: auto;
    left: 360px;
}
.province-conent .map_province_wrapper .tooltip_hoangsa{
    width: auto;
    top: 44%;
}
.tooltip_wr_chart {
    display: inline-flex;
}
.tooltip_chart {
    position: absolute;
    background-color: #fff;
    border-radius: 5px;
    color: #000;
    padding: 5px;
    display: none;
    z-index: 10;
    border: 1px solid #ccc;
    opacity: 1;
    text-align: center;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px 0 rgba(0, 0, 0, .06);
}
@media (max-width:767px) {
    .bumlist-header {
        display: none;
    }
    .bumlist-item-content {
        display: block;
    }
    .bumlist-column:not(.bumlist-column-company) {
        background-color: #fff;
        padding: 0 5px 5px 5px;
    }
    .bumlist-column.bumlist-column-company {
        background-color: #f2f3f4;
    }
    .bumlist-column.bumlist-column-address {
        padding-top: 5px;
    }
    .bumlist-column:before,
    .bumlist-column:after {
        content: " ";
        display: table;
    }
    .bumlist-column:after {
        clear: both;
    }
    .bumlist-column span.h {
        display: inline-block;
        color: #666;
    }
    .bumlist-column span.t {
        float: right;
    }
}

.tableresult .thead td {
    background-color: #0685d6 !important;
    vertical-align: middle;
    min-height: 1px;
    padding: 20px 8px;
    text-align: center;
    text-transform: uppercase;
    color: #fff;
    border-right: 1px #fff solid;
}

.tableresult .item_result {
    align-items: stretch;
    padding: 8px;
    border-right: 1px #ddd solid;
}

.tableresult .item_result h3 {
    font-weight: 400;
}

.blur_filter {
    filter: blur(5px);
    -o-filter: blur(5px);
    -webkit-filter: blur(5px);
    -ms-filter: blur(5px);
    -moz-filter: blur(5px);
}

.res-table {
    table-layout: fixed!important;
    word-break: break-all!important;
}

@media only screen and (max-width: 600px) {
    .char-responsive {
        margin-bottom: 40px;
        border-bottom: 1px dotted #ccc;
        padding: 40px 0;
    }
}

.chart__businesstype {
    position: relative;
}

.chart__businesstype>.bottom_note {
    font-size: 14px;
    font-family: "Segoe UI", "Roboto";
    display: flex;
    justify-content: center;
}

.chart__businesstype>.bottom_note div {
    margin-right: 20px;
}

.motodn i {
    color: #26a0fc;
}

@media only screen and (min-width: 768px) {
    #chart .apexcharts-data-labels {
        transform: translateX(10px);
    }
}

.apexcharts-legend {
    text-align: left;
}

.apexcharts-yaxis-label {
    cursor: pointer;
}

.apexcharts-yaxis-label:hover {
    text-decoration: underline;
}

.chart {
    min-height: 365px;
    padding: 10px;
    box-shadow: 0px 4px 10px 0px rgb(197 197 197 / 62%);
    margin-bottom: 30px;
    margin-top: 20px;
    position: relative;
    z-index: 2;
}

.apexcharts-toolbar {
    display: none !important;
}

.text_rotate {
    background: red;
    color: #fff;
    padding-left: 20px;
}

.apexcharts-tooltip-text-y-value,
.apexcharts-tooltip-text-goals-value,
.apexcharts-tooltip-text-z-value {
    margin-left: 0 !important;
}

.main_chart_history {
    position: relative;
}

.text__static {
    position: absolute;
    bottom: 0;
    left: 16px;
    width: 100%;
    height: 100%;
}

.text--static-vi {
    left: 0px;
}

.text--static-en {
    left: 16px;
}

.text_tong_win {
    position: absolute;
    right: 2%;
    font-size: 12px;
    bottom: 251px;
    font-weight: bold;
    width: 40px;
    text-align: center;
    margin-bottom: 0;
}

.text_tong_order {
    position: absolute;
    right: 2%;
    font-size: 12px;
    bottom: 177px;
    font-weight: bold;
    text-align: center;
    width: 40px;
    margin-bottom: 0;
}

.text_tong_joint_venture {
    position: absolute;
    right: 2%;
    font-size: 12px;
    bottom: 104px;
    font-weight: bold;
    text-align: center;
    width: 40px;
    margin-bottom: 0;
}
.value-chart-title {
    font-size: 14px;
    font-family: Helvetica, Arial, sans-serif;
    color: #333;
    font-weight: 600;
    text-align: center;
}

@media only screen and (max-width: 468px) {
    .text_tong_win,
    .text_tong_order,
    .text_tong_joint_venture {
        right: 5px !important;
    }
    .text_tong_win {
        bottom: 234px;
    }
    .text_tong_order {
        bottom: 167px;
    }
    .text_tong_joint_venture {
        bottom: 102px;
    }
}
@media only screen and (max-width: 768px) {
    .text_tong_win,
    .text_tong_order,
    .text_tong_joint_venture {
        right: 10px;
    }
}


/* Landscape*/

@media only screen and (min-width: 1024px) and (max-width: 1024px) {
    .text_tong_win,
    .text_tong_order,
    .text_tong_joint_venture {
        right: 16.5%;
    }
}

.blur__text {
    position: relative;
    display: block;
    cursor: not-allowed;
}


/* .blur__text::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    text-align: center;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(4px);
    background: rgba(255, 255, 255, 0.2);
}   */

@-webkit-keyframes bounceIn {
    0% {
        opacity: 0;
        -webkit-transform: scale(.3);
    }
    50% {
        opacity: 1;
        -webkit-transform: scale(1.05);
    }
    70% {
        -webkit-transform: scale(.9);
    }
    100% {
        -webkit-transform: scale(1);
    }
}

.title__view_relationship {
    margin: 20px 0;
    text-transform: uppercase;
}

.title__view {
    margin: 20px 0;
    text-transform: uppercase;
    border-bottom: 1px #0685d6 solid;
}

.title__view span {
    border-bottom: 4px #0685d6 solid;
    text-transform: uppercase;
    font-size: 19px;
    color: #0685d6;
    font-weight: 500;
    padding-bottom: 3px;
    display: inline-block;
}

.title__view span:before {
    display: inline-block;
    font-family: FontAwesome;
    font-size: 14px;
    line-height: 1;
    vertical-align: middle;
    margin-right: 5px;
    content: " ";
    color: #d61c1d;
}

#bussSearchBlock {
    margin-bottom: 10px;
}
.form-bussiness-search {
    margin-bottom: 30px !important;
    margin-top: 25px;
}
.detail-list-btn {
    display: flex;
    align-items: center;
}
.detail-wrapper .crawl_time {
    margin-left: auto;
}
/* ----------------------------------------------- * Timeline * --------------------------------------------- */

.timeline {
    list-style: none;
    padding: 10px 0;
    position: relative;
    display: grid;
    grid-template-columns: 1fr 1fr;
}

.timeline:before {
    top: 0;
    bottom: 0;
    position: absolute;
    content: " ";
    width: 2px;
    background: #0685d6;
    left: 50%;
    margin-left: -1.5px;
}

.timeline>li {
    margin-bottom: 20px;
    position: relative;
    width: 100%;
    float: left;
    clear: left;
}

.timeline>li:before,
.timeline>li:after {
    content: " ";
    display: table;
}

.timeline>li:after {
    clear: both;
}

.timeline>li:before,
.timeline>li:after {
    content: " ";
    display: table;
}

.timeline>li:after {
    clear: both;
}

.timeline>li>.timeline-panel {
    width: calc(100% - 25px);
    width: -moz-calc(100% - 25px);
    width: -webkit-calc(100% - 25px);
    float: left;
    border: 1px solid #dcdcdc;
    background: #fff;
    position: relative;
}

.timeline>li>.timeline-panel:before {
    position: absolute;
    top: 26px;
    right: -15px;
    display: inline-block;
    border-top: 15px solid transparent;
    border-left: 15px solid #dcdcdc;
    border-right: 0 solid #dcdcdc;
    border-bottom: 15px solid transparent;
    content: " ";
}

.timeline>li>.timeline-panel:after {
    position: absolute;
    top: 27px;
    right: -14px;
    display: inline-block;
    border-top: 14px solid transparent;
    border-left: 14px solid #fff;
    border-right: 0 solid #fff;
    border-bottom: 14px solid transparent;
    content: " ";
}

.timeline>li>.timeline-badge {
    width: 24px;
    height: 24px;
    line-height: 25px;
    text-align: center;
    position: absolute;
    top: 30px;
    right: -12px;
    z-index: 100;
    background: #ffffff;
}

.fa-lg-vt {
    font-size: 1.6em;
    line-height: .75em;
    vertical-align: -15%;
}

.timeline>li.timeline-inverted>.timeline-panel {
    float: right;
}

.timeline>li.timeline-inverted>.timeline-panel:before {
    border-left-width: 0;
    border-right-width: 15px;
    left: -15px;
    right: auto;
}

.timeline>li.timeline-inverted>.timeline-panel:after {
    border-left-width: 0;
    border-right-width: 14px;
    left: -14px;
    right: auto;
}

.timeline-badge>a {
    color: #0685d6 !important;
}

.timeline-badge a:hover {
    color: #0685d6 !important;
}

.timeline-title {
    margin-top: 0;
    color: inherit;
}

.timeline-heading h4 {
    font-weight: 400;
    padding: 0 15px;
    color: #4679bd;
}

.timeline-body>p,
.timeline-body>ul {
    padding: 10px 15px;
    margin-bottom: 0;
}

.timeline-footer {
    padding: 5px 15px;
    background-color: #f4f4f4;
}

.timeline-footer p {
    margin-bottom: 0;
}

.timeline-footer>a {
    cursor: pointer;
    text-decoration: none;
}

.timeline>li.timeline-inverted {
    float: right;
    clear: right;
}

.timeline>li.timeline-inverted {
    margin-top: 60px;
}

.timeline>li.timeline-inverted>.timeline-badge {
    left: -12px;
}

.no-float {
    float: none !important;
}

.tt_nganhhang {
    margin-top: 8px;
    display: block;
}

@media (max-width: 767px) {
    .timeline {
        grid-template-columns: none;
    }
    ul.timeline:before {
        left: 40px;
    }
    ul.timeline>li {
        margin-bottom: 0px;
        position: relative;
        width: 100%;
        float: left;
        clear: left;
    }
    ul.timeline>li>.timeline-panel {
        width: calc(100% - 65px);
        width: -moz-calc(100% - 65px);
        width: -webkit-calc(100% - 65px);
    }
    ul.timeline>li>.timeline-badge {
        left: 28px;
        margin-left: 0;
        top: 30px;
    }
    ul.timeline>li>.timeline-panel {
        float: right;
    }
    ul.timeline>li>.timeline-panel:before {
        border-left-width: 0;
        border-right-width: 15px;
        left: -15px;
        right: auto;
    }
    ul.timeline>li>.timeline-panel:after {
        border-left-width: 0;
        border-right-width: 14px;
        left: -14px;
        right: auto;
    }
    .timeline>li.timeline-inverted {
        float: left;
        clear: left;
        margin-top: 30px;
        margin-bottom: 30px;
    }
    .timeline>li.timeline-inverted>.timeline-badge {
        left: 28px;
    }
    .tt_nganhhang {
        margin-top: 14px !important;
        display: block;
    }
}

.i-unverify {
    display: inline-block;
    height: 20px;
    width: 86px;
    background-size: 100%;
    background-image: url(../images/unverify.png);
}

.bidding-detail-wrapper>.i-unverify {
    position: absolute;
    top: 5px;
    left: 250px;
}

.alert__no_mr {
    margin: 0;
}

.marked_wrong {
    background-color: #fffacd !important;
}

.bidding_vp .item {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    font-size: 16px;
    background: #fff;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgb(0 0 0 / 3%);
    border: 1px solid #f5f5f5;
}

.bidding_vp .item:nth-child(odd) {
    background: rgba(230, 247, 255, 0.5);
}

.bidding_vp .item:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.bidding_vp .item:last-child {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.bidding_vp .item .item_t {
    width: 20%;
}

.bidding_vp .item .nghi_dinh {
    width: 60%;
}

.bidding_vp .item .item_t_n {
    padding: 10px;
    border-bottom: 1px solid #e3e9ef;
}

.bidding_vp .item .nghi_dinh {
    padding: 10px;
    border-left: 1px solid #e3e9ef;
    border-right: 1px solid #e3e9ef;
}

.bidding_vp .item .item_b {
    display: flex;
    align-items: center;
    justify-content: center;
}

.bidding_vp .item .name_lable {
    font-weight: normal;
    font-size: 16px;
    line-height: 22px;
    color: #007aa4;
    margin-bottom: 0;
    border: 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.bidding_vp .item .name {
    font-weight: bold;
    font-size: 18px;
    line-height: 24px;
    color: #007aa4;
    margin-bottom: 0;
}

.bidding_vp .item .js-btn-tooltip {
    background: #fff;
    border: 1px solid #007aa4;
    box-sizing: border-box;
    border-radius: 20px;
    color: #007aa4;
    margin-top: 16px;
    margin-bottom: 16px;
    padding: 2px 5px;
    outline: none;
}

.bidding_vp .item .vp_name {
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
    color: #007aa4;
    margin-bottom: 1.5rem;
}

.bidding_vp .item .name_violate p {
    font-weight: normal;
    font-size: 16px;
    line-height: 22px;
    color: #595959;
    margin-bottom: 0;
}

.bidding_vp .item .blue-btn {
    color: #fff;
    border: 1px solid #007aa4;
    cursor: pointer;
    display: inline-block;
    background: #007aa4;
    box-shadow: 0 4px 20px rgb(0 0 0 / 3%);
    border-radius: 8px;
    font-weight: normal;
    font-size: 14px;
    line-height: 22px;
    padding: 5px 10px;
    box-sizing: border-box;
}

@media (max-width: 991px) {
    .bidding_vp .item .item_t {
        width: 100%;
    }
    .bidding_vp .item .nghi_dinh {
        width: 100%;
        border: 1px solid #e3e9ef;
        border-left: none;
        border-right: none;
    }
    .bidding_vp .item .item_b {
        padding: 10px;
    }
}

.title__tab_heading span {
    border-bottom: 4px #0685d6 solid;
    text-transform: uppercase;
    font-size: 20px;
    color: #0685d6;
    font-weight: 500;
    padding-bottom: 3px;
    display: inline-block;
}

.span_industry {
    display: inline-block;
    text-align: left;
    line-height: 24px;
    font-size: 15px;
}

.bg_detail_detailbusiness {
    top: -20px;
    left: 86px;
}

.bg_list_resultdetail {
    top: -10px;
    left: 100px;
}

.main_bg_list_contractor_province {
    top: -15px;
    left: 115px;
}

.bg_list_viewinvestor,
.main_bg_list_viewinvestor {
    top: -10px;
    left: 130px;
}

.bg_list_viewinvestor {
    left: 100px;
}

.bg_list_detailpartnership {
    top: -10px;
    left: 100px;
}

.main_bg_detail_businesslistings {
    top: -6px;
    left: 165px;
}

.bg_detail_businesslistings {
    top: -6px;
}

.bg_detail_businesslistings_business {
    left: 100px;
    top: -12px;
}

.bg_detail_businesslistings.tw_lockvip__mobile,
.bg_detail_businesslistings_business.tw_lockvip__mobile {
    left: 0;
}

.org-code {
    font-weight: 500;
}

.org-code:before {
    content: "\e955" !important;
}

.flex__custom_info > span {
    margin-right: 12px;
}

.add_content {
    margin-bottom: 4px;
}

.add_content i {
    font-size: 14px;
    width: 12px;
}

@media screen and (max-width: 678px) {
    .bg_list_detailpartnership {
        top: 80px !important;
    }
    .bg_detail_detailbusiness {
        top: 60px !important;
        left: 10%;
    }
    .bg_list_viewinvestor {
        top: 100px !important;
    }
}

@media screen and (max-width: 768px) {
    .bg_detail_detailbusiness {
        top: -7px;
    }
    .bg_list_viewinvestor {
        top: -7px;
    }
    .bg_list_detailpartnership {
        top: -9px;
    }
    .listing-summary .website-company {
        word-break: break-all;
    }

    .flex__custom_info > span {
        display: block;
        margin-bottom: 4px;
    }
}

@media (max-width: 991.98px) {
    .province-conent {display: none;}
}

@media (max-width: 1199.98px) {
    .map_province_wrapper .province_mobile {
        width: 82%;
    }
    .chart_wrap_1 {
        right: 0;
        width: 50%;
        height: 260px;
        top: -6px;
        position: absolute;
    }
    .chart_bids.chart_bids_1 {
        left: 0;
        top: 26px;
    }
    .chart_price.chart_bids_1 {
        right: 0;
        top: 18px;
    }
    .chart_wrap_2 {
        left: 1%;
        width: 25%;
        height: 480px;
        top: 26%;
        position: absolute;
    }
    .chart_bids.chart_bids_2 {
        top: 10px;
        left: 0;
    }
    .chart_price.chart_bids_2 {
        top: 47%;
        left: 0;
    }
    .chart_wrap_3 {
        right: 0;
        width: 25%;
        height: 480px;
        top: 26%;
        position: absolute;
    }
    .chart_bids.chart_bids_3{
        top: 11px;
        right: 0;
    }
    .chart_price.chart_bids_3 {
        top: 48%;
        right: 0;
    }
    .chart_wrap_4 {
        right: 0;
        width: 57%;
        height: 270px;
        bottom: -12px;
        position: absolute;
    }
    .chart_bids.chart_bids_4 {
        left: 0;
        bottom: 6px;
    }
    .chart_price.chart_bids_4 {
        right: 0;
        bottom: 0;
    }
}

@media screen and (min-width: 678px) {
    .box_lockvip__mobile {
        display: none;
    }
}

@media screen and (min-width: 680px) {
    .box_lockvip {
        display: block !important;
    }
}

@media screen and (min-width: 769px) {
    .box_lockvip {
        display: none !important;
    }
    .box_lockvip__mobile {
        display: block !important;
    }
    .coating span,
    .coating i {
        -webkit-filter: blur(5px);
    }
}

@media screen and (min-width: 991px) {
    .box_lockvip__mobile {
        display: none !important;
    }
    .box_lockvip {
        display: block !important;
    }
    .coating span,
    .coating i {
        -webkit-filter: none !important;
    }
    .interest_chart_wrapp .apexcharts-legend {
        display: grid !important;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }

    .interest_chart_wrapp .apexcharts-legend-series:nth-child(1) {
        grid-column: 1;
        grid-row: 1;
    }
    .interest_chart_wrapp .apexcharts-legend-series:nth-child(2) {
        grid-column: 2;
        grid-row: 1;
    }
    .interest_chart_wrapp .apexcharts-legend-series:nth-child(3) {
        grid-column: 1;
        grid-row: 2;
    }
    .interest_chart_wrapp .apexcharts-legend-series:nth-child(4){
        grid-column: 2;
        grid-row: 2;
    }
    .interest_chart_wrapp .apexcharts-legend-series:nth-child(5) {
        grid-column: 1;
        grid-row: 3;
    }
    .interest_chart_wrapp .apexcharts-legend-series:nth-child(6) {
        grid-column: 2;
        grid-row: 3;
    }
    .interest_chart_wrapp .apexcharts-legend-series:nth-child(7) {
        grid-column: 3;
        grid-row: 1;
    }
    .interest_chart_wrapp .apexcharts-legend-series:nth-child(8) {
        grid-column: 3;
        grid-row: 2;
    }
}
@media (min-width: 991.98px) and (max-width: 1199.98px) {
    .interest_chart_wrapp .apexcharts-legend {
        grid-template-columns: repeat(2, 1fr);
    }
    .interest_chart_wrapp .apexcharts-legend-series:nth-child(7) {
        grid-column: 1;
        grid-row: 4;
    }
    .interest_chart_wrapp .apexcharts-legend-series:nth-child(8) {
        grid-column: 2;
        grid-row: 4;
    }
}
@media screen and (min-width: 1200px) {
    .chart_wrap {
        border: 1px solid #ccc;
        position: absolute;
        border-radius: 4px;
    }
    .chart_wrap_1 {
        right: 0;
        width: 45%;
        height: 257px;
        top: -6px;
    }
    .chart_wrap_4 {
        right: 0;
        width: 48%;
        height: 257px;
        bottom: -12px;
    }
    .chart_wrap_2 {
        left: 1%;
        width: 27%;
        height: 500px;
        top: 20%;
    }
    .chart_wrap_3 {
        right: 0;
        width: 25%;
        height: 480px;
        top: 26%;
    }
    .chart_bids.chart_bids_1,
    .chart_bids.chart_bids_4,
    .chart_bids.chart_bids_2 {
        top: 20px;
    }
    .chart_price.chart_bids_1 {
        right: 0;
        top: 10px;
    }
    .chart_bids.chart_bids_3 {
        top: 7px;
    }
    .chart_price.chart_bids_2 {
        top: 48%;
        left: 0;
    }
    .chart_price.chart_bids_3 {
        top: 49%;
        left: 0;
    }
    .chart_price.chart_bids_4 {
        right: 0;
        bottom: -10px;
    }
}

.chart-title-text {
    position: absolute;
    top: 10px;
    left: 0;
    right: 0;
    font-size:  14px;
    font-weight:  bold;
    color: #263238;
}

/* Chart static */
.chart-container {
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
}

.chart-container h2 {
    text-align: center;
    min-height: 55px;
    font-size: 24px;
}

.chart-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin: 0 auto;
    max-width: 1200px;
}

.chart-box {
    flex: 1;
    min-width: 300px;
    max-width: 485px;
    border: 1px solid #ccc;
    border-radius: 10px;
    padding: 15px;
    margin: 0;
}

.chart-box h2 {
    text-align: center;
    min-height: auto;
    margin-bottom: 20px;
}

.chart-box .detail-link {
    text-align: right;
    margin-top: 10px;
}

.chart-full {
    width: 100%;
    max-width: 1200px;
    margin: 20px auto;
    border: 1px solid #ccc;
    border-radius: 10px;
    padding: 15px;
}

.chart-full .detail-link {
    text-align: right;
    margin-top: 10px;
}

.chart-full h2 {
    text-align: center;
    margin-bottom: 20px;
}

.chart-select {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.chart-select label {
    display: inline-block;
    margin: 0;
}

.chart-container h2.chart-title {
    text-align: center;
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 30px;
    padding: 0;
    border: none;
    background: none;
}

.chart-container h2.chart-title span {
    display: inline-block;
    position: relative;
    padding: 0;
    color: #333;
    border: none;
    background: none;
}

.chart-legend-wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.chart-legend-wrapper .legend-title {
    flex: 1;
    min-width: 200px;
}

.chart-legend-wrapper .legend-select {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: auto;
}

.chart-legend-wrapper strong {
    white-space: nowrap;
    color: #333;
    font-size: 16px;
}

.chart-legend-wrapper select.form-control {
    padding: 6px 12px;
    font-size: 16px;
    min-width: 150px;
    height: 38px;
}

@media (max-width: 768px) {
    .chart-legend-wrapper {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .chart-legend-wrapper .legend-title {
        width: 100%;
    }

    .chart-legend-wrapper .legend-select {
        width: 100%;
        justify-content: space-between;
        margin-left: 0;
    }

    .chart-legend-wrapper strong {
        font-size: 15px;
    }

    .chart-legend-wrapper select.form-control {
        flex: 1;
        margin-left: 12px;
        max-width: 200px;
        font-size: 15px;
        padding: 6px 12px;
        height: 36px;
    }
}

.block-bidding .pull-left {
    margin-top: 5px;
}

.chart-order-select {
    width: 120px;
    display: inline-block;
}

.table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.block-bidding .clearfix {
    margin-bottom: 15px;
}

/* CSS này chỉ áp dụng cho biểu đồ doanh số và điểm năng lực */
.bidding-revenue .bidding-list-header .c-stt,
.bidding-capability .bidding-list-header .c-stt,
.bidding-revenue .bidding-list-body .item .c-stt,
.bidding-capability .bidding-list-body .item .c-stt {
    width: 60px !important;
    min-width: 60px !important;
}

.bidding-revenue .bidding-list-header .c-name,
.bidding-capability .bidding-list-header .c-name,
.bidding-revenue .bidding-list-body .item .c-name,
.bidding-capability .bidding-list-body .item .c-name {
    width: 500px !important;
    min-width: 500px !important;
}

.bidding-revenue .bidding-list-header .c-gia,
.bidding-capability .bidding-list-header .c-gia,
.bidding-revenue .bidding-list-body .item .c-gia,
.bidding-capability .bidding-list-body .item .c-gia {
    width: calc(100% - 560px) !important;
    min-width: 200px !important;
    text-align: left;
}

.bidding-revenue .c-name h3,
.bidding-capability .c-name h3 {
    overflow: visible;
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.4;
}

.bidding-revenue .bidding-list-body .item .c-gia .t,
.bidding-capability .bidding-list-body .item .c-gia .t {
    text-align: left;
}

.bidding-revenue .bidding-list-body .item .c-gia .label-name,
.bidding-capability .bidding-list-body .item .c-gia .label-name {
    text-align: left;
}

@media (max-width: 768px) {
    .bidding-revenue .bidding-list-header .c-name,
    .bidding-capability .bidding-list-header .c-name,
    .bidding-revenue .bidding-list-body .item .c-name,
    .bidding-capability .bidding-list-body .item .c-name {
        width: auto !important;
        min-width: 0 !important;
        flex: 1 !important;
    }

    .bidding-revenue .bidding-list-header .c-gia,
    .bidding-capability .bidding-list-header .c-gia,
    .bidding-revenue .bidding-list-body .item .c-gia,
    .bidding-capability .bidding-list-body .item .c-gia {
        width: auto !important;
        min-width: 150px !important;
    }
}

.view-full-list {
    text-align: right;
    margin: 15px 0;
}

.view-full-list .btn {
    font-size: 13px;
    padding: 4px 12px;
    line-height: 1.4;
}

.view-full-list .btn i {
    margin-right: 3px;
    font-size: 12px;
}

.wrap__text {
    word-break: break-word;
}

#bu-location-search {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: max-height, opacity, transform;
    margin: 0;
}

#bu-location-search.show {
    max-height: 500px;
    opacity: 1;
    transform: translateY(0);
}

.vnr-top10-block .filter-options {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.vnr-top10-block .filter-options span {
    color: #333;
    font-size: 14px;
}

.vnr-top10-block .filter-options .form-control {
    width: auto;
    display: inline-block;
}

.vnr-top10-block .legend-title {
    margin-bottom: 15px;
}

.vnr-top10-block .legend-title strong {
    font-size: 16px;
    font-weight: 700;
}

@media (max-width: 767px) {
    .vnr-top10-block .apexcharts-tooltip,
    .vnr-top10-tooltip {
        width: 95vw !important;
        max-width: 95vw !important;
        min-width: 280px !important;
        white-space: normal !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        box-sizing: border-box !important;
        padding: 10px 12px !important;
        position: fixed !important;
        top: 50% !important;
        bottom: auto !important;
        z-index: 1000 !important;
        background-color: #fff !important;
        border: 1px solid #e3e3e3 !important;
        border-radius: 5px !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2) !important;
        margin: 0 auto !important;
        right: auto !important;
        max-height: 300px !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
        -webkit-overflow-scrolling: touch !important;
        pointer-events: auto !important;
        scrollbar-width: thin !important;
        scrollbar-color: rgba(0,0,0,0.3) rgba(0,0,0,0.1) !important;
        opacity: 0;
        visibility: hidden;
    }

    .vnr-top10-block .apexcharts-tooltip-series-group,
    .vnr-top10-tooltip .apexcharts-tooltip-series-group {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        padding: 8px 10px !important;
        word-break: break-word !important;
        margin-bottom: 5px !important;
        border-bottom: 1px solid #f3f3f3 !important;
        pointer-events: auto !important;
    }

    .vnr-top10-block .apexcharts-tooltip-text,
    .vnr-top10-tooltip .apexcharts-tooltip-text {
        display: flex !important;
        flex-direction: column !important;
        flex-wrap: wrap !important;
        white-space: normal !important;
        word-break: break-word !important;
        width: 100% !important;
    }

    .vnr-top10-block .apexcharts-tooltip-title,
    .vnr-top10-tooltip .apexcharts-tooltip-title {
        font-size: 14px !important;
        font-weight: bold !important;
        text-align: center !important;
        white-space: normal !important;
        word-break: break-word !important;
        margin-bottom: 10px !important;
        background-color: #f8f9fa !important;
        border-bottom: 1px solid #e9ecef !important;
        padding: 8px !important;
        border-radius: 4px 4px 0 0 !important;
    }

    .vnr-top10-block .apexcharts-tooltip-y-group,
    .vnr-top10-tooltip .apexcharts-tooltip-y-group {
        padding: 4px 0 !important;
    }

    .vnr-top10-block .apexcharts-tooltip-text-y-label,
    .vnr-top10-tooltip .apexcharts-tooltip-text-y-label {
        margin-right: 8px !important;
        font-weight: 600 !important;
    }

    .vnr-top10-block .apexcharts-tooltip-text-y-value,
    .vnr-top10-tooltip .apexcharts-tooltip-text-y-value {
        font-weight: 600 !important;
    }

    .vnr-top10-block .apexcharts-legend-series {
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
        margin: 5px 0 !important;
        padding: 0 !important;
        text-indent: 0 !important;
        width: 100% !important;
        white-space: normal !important;
        gap: 8px !important;
    }

    .vnr-top10-block .apexcharts-legend-marker {
        width: 12px !important;
        height: 12px !important;
        flex-shrink: 0 !important;
    }

    .vnr-top10-block .apexcharts-legend-text {
        flex-grow: 1 !important;
        font-size: 13px !important;
        word-break: break-word !important;
        white-space: normal !important;
    }

    /* Tùy chỉnh thanh cuộn  */
    .vnr-top10-block .apexcharts-tooltip::-webkit-scrollbar,
    .vnr-top10-tooltip::-webkit-scrollbar {
        width: 6px !important;
        height: 6px !important;
    }

    .vnr-top10-block .apexcharts-tooltip::-webkit-scrollbar-track,
    .vnr-top10-tooltip::-webkit-scrollbar-track {
        background: rgba(0,0,0,0.1) !important;
        border-radius: 10px !important;
    }

    .vnr-top10-block .apexcharts-tooltip::-webkit-scrollbar-thumb,
    .vnr-top10-tooltip::-webkit-scrollbar-thumb {
        background: rgba(0,0,0,0.3) !important;
        border-radius: 10px !important;
    }

    .vnr-top10-block .apexcharts-tooltip::-webkit-scrollbar-thumb:hover,
    .vnr-top10-tooltip::-webkit-scrollbar-thumb:hover {
        background: rgba(0,0,0,0.5) !important;
    }

    .vnr-top10-block .apexcharts-tooltip.apexcharts-active {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    .vnr-top10-block .apexcharts-tooltip-marker {
        width: 12px !important;
        height: 12px !important;
        display: inline-block !important;
        margin-right: 10px !important;
        border-radius: 50% !important;
    }
}

.investor-table-container {
    overflow-x: auto;
}

.investor-table-container .investor-table-header,
.investor-table-container .investor-table-item {
    min-width: 1200px;
}

.c-total-value {
    flex: 0 0 250px;
    max-width: 250px;
    min-width: 250px;
}

@media (max-width: 768px) {
    .investor-table-container .investor-table-header,
    .investor-table-container .investor-table-item {
        min-width: 800px;
    }

    .c-total-value {
        flex: 0 0 150px;
        max-width: 150px;
        min-width: 150px;
    }
}