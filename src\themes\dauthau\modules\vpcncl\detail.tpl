<!-- BEGIN: main -->

<div id="list-search-orgs" class="filter-{MODULE_NAME} bidding-detail-wrapper bidding-detail-wrapper-result">
    <h2 class="bidding-name">{ORG.organization_name}</h2>
    <div class="bidding-detail">
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.num}</div>
            <div class="c-val"><span class="lic-code"> {ORG.organization_number}</span></div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.org_name}</div>
            <div class="c-val">{ORG.organization_name}</div>
        </div>
        <!-- BEGIN: show_host_unit -->
        <div class="bidding-detail-item host">
            <div class="c-tit">{LANG.host_name}</div>
            <div class="c-val">{ORG.map_host_unit}</div>
        </div>
        <!-- END: show_host_unit -->
        <!-- BEGIN: show_specialize -->
        <div class="bidding-detail-item linhvuc">
            <div class="c-tit">{LANG.specialize}</div>
            <div class="c-val">{ORG.linhvuc}</div>
        </div>
        <!-- END: show_specialize -->
        <div class="bidding-detail-item linhvuc">
            <div class="c-tit">{LANG.accreditation_location}</div>
            <div class="c-val">- {ORG.accreditation_location}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.province}</div>
            <div class="c-val">{ORG.province}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.last_update}</div>
            <div class="c-val">
                <div class="col-md-10">{ORG.last_update} - {ORG.crawls_number}</div>
                <!-- BEGIN: update -->
                <div class="col-md-4">
                    <a id="eventUpdateOrganization" href="">{LANG.cap_nhat_lai}</a>
                </div>
                <div class="col-md-16 capcha" style="display: none">
                    <!-- BEGIN: recaptcha -->
                    <div class="form-group">
                        <div class="middle text-center clearfix">
                            <div class="nv-recaptcha-default">
                                <div data-toggle="recaptcha" data-callback="verifyCaptcha" id="{RECAPTCHA_ELEMENT}"></div>
                            </div>
                        </div>
                    </div>
                    <!-- END: recaptcha -->
                </div>
                <!-- END: update -->
                <!-- BEGIN: login -->
                    <div class="col-md-14">{LOGIN}</div>
                <!-- END: login -->
            </div>
        </div>
        <!-- BEGIN: effective_date -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.effective_date}</div>
            <div class="c-val">
                <span> {ORG.effective_date}</span>
            </div>
        </div>
        <!-- END: effective_date -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.status}</div>
            <div class="c-val">{ORG.status}</div>
        </div>
        <!-- BEGIN: cancellation_date -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.cancellation_date}</div>
            <div class="c-val">
                <span> {ORG.cancellation_date}</span>
            </div>
        </div>
        <!-- END: cancellation_date -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.file_download}</div>
            <div class="c-val">{ORG.file_download}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.content}</div>
            <div id="org-content" class="c-val">{ORG.content}</div>
        </div>
    </div>
</div>
<!-- BEGIN: popup_login -->
{POPUP_LOGIN}
<!-- END: popup_login -->
<script type="text/javascript">
    var capcha = 0;
    function showCapcha() {
        if (capcha == 1) {
            $(".capcha").css("display", "none");
            capcha = 0;
        } else {
            $(".capcha").css("display", "block");
            capcha = 1;
        }
    }
    function funcUpdateLab() {
        $.ajax({
            type : "POST",
            url : location.href,
            data : {
                check : '{CHECKSESS_UPDATE}',
                update : '1'
            },
            success : function(res) {
                alert(res);
                window.location.href = window.location.href;
            }
        });
    }
    function verifyCaptcha(e) {
        funcUpdateLab();
    }
    var eventUpdate = document.getElementById('eventUpdateOrganization');
    eventUpdate.addEventListener("click", showCapcha);
</script>
<!-- END: main -->
