<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_IS_MOD_BUSINESSLISTINGS'))
    die('Stop!!!');

function nv_theme_businesslistings_listduplicate($array_data)
{
    global $db, $db_config, $module_name, $module_info, $module_file, $module_data, $nv_Cache, $module_config, $nv_Lang;
    $xtpl = new XTemplate("listduplicate.tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('MODULE_NAME', $module_name);

    $query = 'SELECT * FROM ' . BUSINESS_PREFIX_GLOBAL . '_info as t1 INNER JOIN ' . BUSINESS_PREFIX_GLOBAL . '_addinfo as t2 ON t1.code = t2.so_dkkd WHERE t1.id != ' . $array_data['id'] . ' AND (t1.companyname LIKE ' . $db->quote($array_data['companyname']);
    if (!empty($array_data['phone'])) {
        $query .= ' OR t1.phone LIKE ' . $db->quote($array_data['phone']);
    }
    if (!empty($array_data['code'])) {
        $query .= ' OR t1.code LIKE ' . $db->quote($array_data['code']);
    }
    $query .= ')';
    $_result = $db->query($query);
    $array_result = array();
    $array_idward = array();
    while ($_row = $_result->fetch()) {
        $array_result[] = $_row;
        if ($_row['ward'] > 0 and !in_array($_row['ward'], $array_idward)) {
            $array_idward[] = $_row['ward'];
        }
    }
    $array_id = [];
    if (!empty($array_result)) {
        // province
        $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province";
        $province_list = $nv_Cache->db($sql, 'id', 'location');

        // district
        $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_district";
        $district_list = $nv_Cache->db($sql, 'id', 'location');

        $ward_list = [];
        if (!empty($array_idward)) {
            $_query = $db->query('SELECT id, title, alias FROM ' . NV_PREFIXLANG . '_location_ward WHERE id IN (' . implode(',', $array_idward) . ')');
            while ($_ward = $_query->fetch()) {
                $ward_list[$_ward['id']] = $_ward;
            }
        }
        $i = 0;
        $array_id = [
            $array_data['id']
        ];
        foreach ($array_result as $view) {
            $duplicate_data = [];
            $array_id[] = $view['id'];
            $i++;
            // $duplicate_data[] = mb_strtoupper($array_data['code'], 'UTF-8');
            if ($view['code'] == $array_data['code']) {
                $duplicate_data[] = $nv_Lang->getModule('duplicate_code');
            }
            if (mb_strtoupper($view['companyname'], 'UTF-8') == mb_strtoupper($array_data['companyname'], 'UTF-8')) {
                $duplicate_data[] = $nv_Lang->getModule('duplicate_name');
            }
            if ($view['phone'] == $array_data['phone']) {
                $duplicate_data[] = $nv_Lang->getModule('duplicate_phone');
            }

            (NV_LANG_DATA != 'vi' && !empty(trim($view['officialname']))) && $view['companyname'] = $view['officialname'];

            if (!empty($view['true_code']) and (defined('NV_IS_SPADMIN'))) {
                $view['style'] = 'marked_wrong';
                $view['status'] = '<span class="i-unverify" title="Dữ liệu nhà thầu này đã được DauThau.info gắn nhãn là không chính xác!"></span>';
            } else {
                $view['status'] = '';
            }

            $view['duplicate_data'] = implode('</br>', $duplicate_data);
            $view['ngay_phe_duyet'] = nv_date('d/m/Y', $view['ngay_phe_duyet']);
            $view['num'] = $i;
            $view['link'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($view['companyname']) . '-' . $view['id']);
            // Tình trạng nộp phí
            $view['thoi_han'] = '';
            if (!empty($view['orgcode'])) {
                $nop_phi_type_title = [
                    1 => $nv_Lang->getModule('nop_phi_type_title_1'),
                    2 => $nv_Lang->getModule('nop_phi_type_title_2'),
                    3 => $nv_Lang->getModule('nop_phi_type_title_3')
                ];
                if (!empty($view['nop_phi_type'])) {
                    $view['thoi_han'] = !empty($nop_phi_type_title[$array_data['nop_phi_type']]) ? $nop_phi_type_title[$array_data['nop_phi_type']] : '';
                }
            }
            // $view['thoi_han'] = preg_replace('/\<font color=\"red\"\>(.*)\<\/font\>/ui', '$1', $view['thoi_han']);

            // *** Xử lý địa chỉ ***//
            $base_link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=";
            $view['addressfull'] = array();
            if ($view['address'] != "") {
                $view['addressfull'][] = handle_address_ward($view['address'], $view['ward']);
            }
            if ($view['ward'] > 0) {
                // district
                if (isset($ward_list[$view['ward']])) {
                    $_ward = $ward_list[$view['ward']];
                    $view['addressfull'][] = '<a href="' . $base_link . 'listlocation/X-' . $_ward['alias'] . '-' . $_ward['id'] . '"\>' . $_ward['title'] . '</a>';
                }
            }
            if (isset($district_list[$view['district']])) {
                $view['addressfull'][] = '<a href="' . $base_link . 'listlocation/H-' . $district_list[$view['district']]['alias'] . '-' . $district_list[$view['district']]['id'] . '"\>' . $district_list[$view['district']]['title'] . '</a>';
            }
            if (isset($province_list[$view['province']])) {
                $view['addressfull'][] = '<a href="' . $base_link . 'listlocation/T-' . $province_list[$view['province']]['alias'] . '-' . $province_list[$view['province']]['id'] . '"\">' . $province_list[$view['province']]['title'] . '</a>';
            }

            $view['addressfull'] = implode(', ', $view['addressfull']);
            $view['addressfull'] = str_replace(',,', ',', $view['addressfull']);

            $xtpl->assign('VIEW', $view);
            if (defined('NV_IS_SPADMIN')) {
                $xtpl->parse('main.list.loop.view_status');
            }
            $xtpl->parse('main.list.loop');
        }
        if (defined('NV_IS_SPADMIN')) {
            $xtpl->parse('main.list.view_status_label');
        }
        $xtpl->parse('main.list');
    }
    // Hiển thị button Đánh dấu sai, Gộp dữ liệu
    $q_list_id = implode(',', $array_id);
    $link = [];
    if (empty($array_data['true_code'])) {
        $link['mark_not_used'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . '=filter_duplicate&q_list_id=' . $q_list_id;
        $link['tranfer_contract'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . '=tranfer_contract&id=' . $array_data['id'];
        $xtpl->assign('ACTION', $link);
        $xtpl->parse('main.button_mark');
    } else {
        $link['undo_mark'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . '=undo_duplicate&q_list_id=' . $array_data['id'];
        $xtpl->assign('ACTION', $link);
        $xtpl->parse('main.button_undo');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_detail($array_data, $global_array_config, $dataByYears, $arr_show10, $number_result, $arr_soclocitor3, $num_soclocitor, $array_open_other3, $array_open_other_sum, $num_array_open_other, $array_partnership3, $array_partnership_sum, $num_array_partnership, $solicitor, $list_vipham, $getIndustryICB, $crawl_request_history, $arr_row, $array_difference_price, $number_independent, $number_joint_venture, $contractor_province, $array_bidfields, $vnr_info, $permision = 0)
{
    global $global_config, $module_name, $module_file, $module_config, $module_info, $op, $client_info, $user_info, $site_mods, $module_captcha, $dm_htlcnt, $title_lock, $nv_Lang, $error_api, $sys_mods, $arr_org_size, $array_round_format, $province_list;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);

    // Cập nhật Lang
    $nv_Lang->setModule('info_point_not_enough', $nv_Lang->getModule('info_point_not_enough', $global_array_config['point_view_detail_bussiness']));
    $nv_Lang->setModule('message_point_view_suss', $nv_Lang->getModule('message_point_view_suss', $global_array_config['point_view_detail_bussiness']));

    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('SELFURL', $client_info['selfurl']);
    $xtpl->assign('NNKD_INDUSTRY', $nv_Lang->getModule('title_business_industry_dn', $array_data['companyname']));
    $xtpl->assign('LINK_JS', '/themes/' . $module_info['template'] . '/');
    $_userid = defined('NV_IS_USER') ? intval($user_info['userid']) : 0;
    $xtpl->assign('CHECKSESS_UPDATE', md5($_userid . $array_data['id'] . NV_CACHE_PREFIX . $client_info['session_id']));
    $xtpl->assign('URL_UPDATE', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=detail&bus_id=' . $array_data['id'], true));
    if (!defined("NV_IS_USER")) {
        $link_login = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=login&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
        $link_register = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=register&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
        $link = vsprintf($nv_Lang->getModule('login'), array(
            $link_login,
            $link_register
        ));

        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $pop_up = sprintf($nv_Lang->getModule('login_popup'), $link_register);

        $xtpl->assign('LOGIN', $pop_up);
        $xtpl->parse('main.detail.login');
    }
    if (defined("NV_IS_MODADMIN")) {
        $mss = '';
        if (!empty($array_data['log_redownload']) && $array_data['log_redownload']['status'] < 1) {
            $mss = sprintf($nv_Lang->getModule('redownload_logs'), $array_data['log_redownload']['username'], date('H:i d/m/Y', $array_data['log_redownload']['create_time']));
        }
        if ($array_data['redownload'] > 0) {
            $mss .= sprintf($nv_Lang->getModule('mss_time_download'), $array_data['redownload'], date(' H:i d/m/Y', $array_data['time_ho_so']));
        }

        $disable = "disabled";
        if ($array_data['get_ho_so'] > 99) {
            $disable = "";
        }
        $xtpl->assign('disable', $disable);
        if (!empty($mss)) {
            $xtpl->assign('MSS', $mss);
            $xtpl->parse('main.detail.re_download_warning');
        }
        $xtpl->parse('main.detail.re_download');
    }

    $array_data['update_info'] = $array_data['count_crawl'] == 1 ? $nv_Lang->getModule('first_update_info') : sprintf($nv_Lang->getModule('update_info'), num_of_time($array_data['count_crawl']));

    // Hiển thị nút download pdf - chỉ ở trang tiếng việt
    if (NV_LANG_DATA == 'vi' && !$array_data['hide_info']) {
        // Hiển thị nút download
        $xtpl->parse('main.downloadPDF');
        // Script download
        $xtpl->assign('PDF_LINK', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $sys_mods['businesslistings']['alias']['exportpdf']);
        $xtpl->parse('main.downloadPDFScript');
    }

    // Trừ điểm khi xem chi tiết
    $xtpl->assign('LINK_MUA_DIEM', URL_CRM_SITE . NV_LANG_DATA. '/points/' . (NV_LANG_DATA == 'en' ? '#buy-point' : '#muadiem'));
    $link__popup = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);

    $xtpl->assign('CONFIG', $global_array_config);

    if (!empty($array_data['log_crawls'])) {
        $arr_format_date = [
            'dateestablished',
            'taxdate',
            'update_time',
            'time_hide',
            'time_hide_end',
            'fgettime',
            'trungthau',
            'ngay_phe_duyet',
            'time_crawler',
            'timeupdate'
        ];
        $arr_format_industry = [
            'industry1',
            'industry2',
            'industry3',
            'industry4',
            'industry5'
        ];

        foreach ($array_data['log_crawls'] as $arr_value) {
            foreach ($arr_value as $key => $value) {
                if ($key == "log_time_old" || $key == "log_time_new") {
                    continue;
                }
                if (in_array($key, $arr_format_date)) {
                    $value = f_date($value);
                }
                if (in_array($key, $arr_format_industry)) {
                    $value = f_industry($value);
                }
                if ($key == "province") {
                    $value = f_location($value, "T");
                }
                if ($key == "district") {
                    $value = f_location($value, "H");
                }
                if ($key == "ward") {
                    $value = f_location($value, "X");
                }
                if ($key == "businesstype") {
                    $value = f_loaidoanhnghiep($value);
                }
                if ($key == 'main_industry') {
                    $value = f_industry($value);
                }
                $xtpl->assign('KEY_LOG', $nv_Lang->getModule($key));
                $xtpl->assign('DATA_LOG', $value);
                $xtpl->parse('main.modal_log.loop_row.loop_row_item');
            }
            $xtpl->assign('DATE_OLD', $arr_value['log_time_old']);
            $xtpl->assign('DATE_NEW', $arr_value['log_time_new']);
            $xtpl->parse('main.modal_log.loop_row');
        }
        $xtpl->parse('main.modal_log');
        $xtpl->parse('main.detail.log');
    }
    if (!empty($crawl_request_history)) {
        $stt = 1;
        foreach ($crawl_request_history as $cr) {
            $cr['last_reload'] = date('d/m/Y H:i', $cr['last_reload']);
            $cr['stt'] = $stt++;
            $xtpl->assign('CR', $cr);
            $xtpl->parse('main.detail.crawl_request_history_list.loop');
        }
        $xtpl->parse('main.detail.crawl_request_history_list');
        $xtpl->parse('main.detail.update.crawl_request_history_button');
    }

    if (!empty($array_data['office_address'])) {
        $xtpl->assign('office_address', $array_data['office_address']);
        $xtpl->parse('main.detail.office_address');
    }

    if (!empty($array_data['thong_tin_nganh_nghe']) or !empty($array_data['industry_dkkd']) or !empty($getIndustryICB)) {
        // hiển thị bảng thông tin ngành nghề theo dkkd
        $stt = 0;
        if (!empty($array_data['industry_dkkd'])) {
            foreach ($array_data['industry_dkkd'] as $key => $industry) {
                $stt = $key + 1;
                $xtpl->assign('INDUSTRY_KEY', $key + 1);
                $xtpl->assign('INDUSTRY', $industry);
                $xtpl->parse('main.detail.industry.table_dkkd.loop_dkkd');
            }
            if ($stt > 5) {
                $xtpl->assign('EXPAND_DKKD', 'expand_active');
            }

            $xtpl->parse('main.detail.industry.tab_active_dkkd');
            $xtpl->parse('main.detail.industry.table_dkkd');
        }

        // hiển thị bảng thông tin ngành nghề theo MSC
        if (!empty($array_data['thong_tin_nganh_nghe'])) {
            if (empty($array_data['industry_dkkd'])) {
                $xtpl->assign('TAB_ACTIVE', 'active');
                $xtpl->assign('TAB_ACTIVE_IN', 'active in');
            }
            $xtpl->parse('main.detail.industry.tab_active_msc');
            $stt = 0;
            // Hệ thống msc mới
            if (!empty($array_data['nganh_nghe_kinh_doanh'])) {
                foreach ($array_data['nganh_nghe_kinh_doanh'] as $key => $industry) {
                    $stt = $key + 1;
                    $xtpl->assign('INDUSTRY_KEY', $key + 1);
                    $xtpl->assign('INDUSTRY', $industry['link_view']);
                    if ($industry['main'] == 1) {
                        $xtpl->assign('MAIN_INDUSTRY', '(' . $nv_Lang->getModule('main_industry') . ')');
                    } else {
                        $xtpl->assign('MAIN_INDUSTRY', '');
                    }
                    $xtpl->parse('main.detail.industry.table_msc.loop.main_industry');
                    $xtpl->parse('main.detail.industry.table_msc.loop');
                }
            } else {
                // Hệ thống msc cũ
                foreach ($array_data['thong_tin_nganh_nghe'] as $key => $industry) {
                    $stt = $key + 1;
                    $xtpl->assign('INDUSTRY_KEY', $key + 1);
                    $xtpl->assign('INDUSTRY', $industry);
                    $xtpl->parse('main.detail.industry.table_msc.loop');
                }
            }
            if ($stt > 5) {
                $xtpl->assign('EXPAND_MSC', 'expand_active');
            }

            $xtpl->parse('main.detail.industry.table_msc');
        }

        // Hiển thị ngành nghề ICB
        if (!empty($getIndustryICB)) {
            $i = 1;
            foreach ($getIndustryICB as $k => $v) {
                $v['stt'] = $i++;
                $xtpl->assign('VALUE', $v);
                $xtpl->parse('main.detail.industry.table_icb.loop');
            }
            if ($i > 5) {
                $xtpl->assign('EXPAND_ICB', 'expand_active');
            }
            if (empty($array_data['industry_dkkd']) and empty($array_data['thong_tin_nganh_nghe'])) {
                $xtpl->assign('TAB_ACTIVE_ICB', 'active');
                $xtpl->assign('TAB_ACTIVE_IN_ICB', 'active in');
            }
            $xtpl->parse('main.detail.industry.tab_active_icb');
            $xtpl->parse('main.detail.industry.table_icb');
        }

        $xtpl->parse('main.detail.industry');
    }

    if (!empty($array_data['taxdate'])) {
        $taxdate_convert = nv_date('d/m/Y', $array_data['taxdate']);
        $xtpl->assign('TAXDATE_CONVERT', $taxdate_convert);
        $xtpl->parse('main.detail.taxdate');
    }

    // Tình trạng nộp phí
    if (!empty($array_data['orgcode'])) {
        $nop_phi_type_title = [
            1 => '<font color="red">' . $nv_Lang->getModule('nop_phi_type_title_1') . '</font>',
            2 => '<font color="red">' . $nv_Lang->getModule('nop_phi_type_title_2') . '</font>',
            3 => $nv_Lang->getModule('nop_phi_type_title_3')
        ];
        if (!empty($array_data['nop_phi_type'])) {
            $array_data['thoi_han'] = !empty($nop_phi_type_title[$array_data['nop_phi_type']]) ? $nop_phi_type_title[$array_data['nop_phi_type']] : '';
        }
    }

    $xtpl->assign('ROW', $array_data);

    // if (!empty($array_data['orgcode'])) {
    // if (defined('NV_IS_MODADMIN')) {
    // // email
    // if ($array_data['email'] != "") {
    // $xtpl->parse('main.detail.email');
    // }
    // // phone
    // if ($array_data['phone'] != "") {
    // $xtpl->parse('main.detail.phone');
    // }
    // // fax
    // if ($array_data['fax'] != "") {
    // $xtpl->parse('main.detail.fax');
    // }
    // }
    // } else {
    // // phone
    // if ($array_data['phone'] != "") {
    // $xtpl->parse('main.detail.phone');
    // }
    // // fax
    // if ($array_data['fax'] != "") {
    // $xtpl->parse('main.detail.fax');
    // }
    // }

    /* MSC cũ */
    if (!empty($array_data['officialname'])) {
        $xtpl->parse('main.detail.officialname');
    }
    // email
    if ($array_data['email'] != "") {
        $xtpl->parse('main.detail.email');
    }
    // phone
    if ($array_data['phone'] != "") {
        $xtpl->parse('main.detail.phone');
    }
    // fax
    if ($array_data['fax'] != "") {
        $xtpl->parse('main.detail.fax');
    }
    /* MSC mới */
    if ($array_data['represent_phone'] != "") {
        $xtpl->parse('main.detail.represent_phone');
    }
    if ($array_data['represent_email'] != "") {
        $xtpl->parse('main.detail.represent_email');
    }
    if ($array_data['website'] != "") {
        $xtpl->parse('main.detail.website');
    }
    // att_file
    if (!empty($array_data['att_file'])) {
        $xtpl->parse('main.detail.att_file');
    }
    if ($array_data['gmaps'] != "") {
        $xtpl->parse('main.gmaps');
    }
    if ($array_data['short'] != "") {
        $xtpl->parse('main.detail.short');
    }

    if ($array_data['registrationtime'] != "") {
        $xtpl->parse('main.detail.registrationtime');
    }
    if ($array_data['representative'] != "") {
        $xtpl->parse('main.detail.representative');
    }
    if ($array_data['about'] != "" and $array_data['about'] != "0") {
        $xtpl->parse('main.detail.about');
    }
    if (!empty($array_data['linh_vuc_kinh_doanh'])) {
        $xtpl->parse('main.detail.linh_vuc_kinh_doanh');
    }
    if (!empty($array_data['businesstype'])) {
        $xtpl->parse('main.detail.businesstype');
    }
    if (!empty($array_data['dateestablished'])) {
        $xtpl->parse('main.detail.dateestablished');
    }

    // org_scale
    if ($array_data['org_scale'] != "" && isset($arr_org_size[$array_data['org_scale']])) {
        $org_scale_title = $arr_org_size[$array_data['org_scale']]['name_' . NV_LANG_DATA];
        $xtpl->assign('ORG_SCALE_TITLE', $org_scale_title);
        $xtpl->parse('main.detail.org_scale');
    }

    // Thông báo xác thực hồ sơ trên dauthau.net (#945)
    if (!empty($array_data['thoi_han'])) {
        if ($array_data['status_dtnet'] != 1 && $array_data['profile_id'] > 0 && $array_data['array_profile'] != '') {
            if (!empty($array_data['array_profile']['prof_alias'])) {
                $xtpl->assign('STATUS_DTNET_TITLE', sprintf($nv_Lang->getModule('verify_profile_to_receive_notifications'), $array_data['array_profile']['link']));
            } else {
                $link_profile_dtnet = 'https://dauthau.net/vi/dn/' . change_alias($array_data['companyname']) . '/';
                $xtpl->assign('STATUS_DTNET_TITLE', sprintf($nv_Lang->getModule('verify_profile_to_receive_notifications'), $link_profile_dtnet));
            }

            $xtpl->parse('main.detail.thoi_han.status_dtnet_title');
        }
        $xtpl->parse('main.detail.thoi_han');
    }
    $xtpl->parse('main.detail.h1companyname');
    if (!empty($array_data['tax_nation'])) {
        $xtpl->parse('main.detail.tax_nation');
    }
    if (!empty($array_data['orgcode'])) {
        $xtpl->parse('main.detail.orgcode');
    }
    if (!empty($array_data['rep_name'])) {
        $xtpl->parse('main.detail.rep_name');
    }
    if (!empty($array_data['rep_position'])) {
        $xtpl->parse('main.detail.rep_position');
    }

    if (!empty($array_data['stocks'])) {
        $xtpl->parse('main.detail.is_stock');
    }

    if (!empty($array_data['trading_address'])) {
        $xtpl->assign('trading_address', $array_data['trading_address']);
        $xtpl->parse('main.detail.trading_address');
    }

    if (!empty($array_data['so_nhan_vien'])) {
        $xtpl->parse('main.detail.so_nhan_vien');
    }
    if (!empty($array_data['von_dieu_le'])) {
        $xtpl->parse('main.detail.von_dieu_le');
    }

    if (empty($array_data['orgcode'])) {
        $xtpl->parse('main.detail.currentstatus');
    }

    if (defined('NV_IS_MODADMIN')) {
        $xtpl->assign('ADMINLINK', "<a class=\"btn btn-primary btn-xs btn_edit\" href=\"" . NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=row&amp;id=" . $array_data['id'] . "\"><em class=\"fa fa-edit margin-right\"></em> " . $nv_Lang->getGlobal('edit') . "</a>");
        $xtpl->parse('main.detail.adminlink');
    }

    if ($array_data['hide_info'] != 1 or (defined('NV_IS_VIP3') and $array_data['hide_vip3'] != 1)) {
        $link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=businesslistings&amp;" . NV_OP_VARIABLE . "=resultdetail";
        $link = nv_url_rewrite($link . "/" . change_alias($array_data['lang_companyname']) . "-" . $array_data['id'], true);

        $link_nhathaukhac = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=businesslistings&amp;" . NV_OP_VARIABLE . "=detailbusiness";
        $link_nhathaukhac = nv_url_rewrite($link_nhathaukhac . "/" . change_alias($array_data['lang_companyname']) . "-" . $array_data['id'], true);

        $link_liendanh = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=businesslistings&amp;" . NV_OP_VARIABLE . "=detailpartnership";
        $link_liendanh = nv_url_rewrite($link_liendanh . "/" . change_alias($array_data['lang_companyname']) . "-" . $array_data['id'], true);

        $arrChart = array(
            'char_ratio_win' => array(
                'titleName' => $nv_Lang->getModule('name_ratio_win'),
                'hit_rate' => $number_result['result1'],
                'slip_rate' => $number_result['result2'],
                'no_result' => $number_result['result3'],
                'result_cancel' => $number_result['result4'],
                'tong' => $number_result['result1'] + $number_result['result2'] + $number_result['result3'] + $number_result['result4']
            ),
            'char_ratio_order' => array(
                'titleName' => $nv_Lang->getModule('name_ratio_order'),
                'hit_rate' => $number_independent['result1'],
                'slip_rate' => $number_independent['result2'],
                'no_result' => $number_independent['result3'],
                'result_cancel' => $number_independent['result4'],
                'tong' => $number_independent['result1'] + $number_independent['result2'] + $number_independent['result3'] + $number_independent['result4']
            ),
            'char_ratio_joint_venture' => array(
                'titleName' => $nv_Lang->getModule('name_ratio_joint_venture'),
                'hit_rate' => $number_joint_venture['result1'],
                'slip_rate' => $number_joint_venture['result2'],
                'no_result' => $number_joint_venture['result3'],
                'result_cancel' => $number_joint_venture['result4'],
                'tong' => $number_joint_venture['result1'] + $number_joint_venture['result2'] + $number_joint_venture['result3'] + $number_joint_venture['result4']
            ),
            'title_ressult' => array(
                'label_hit_rate' => $nv_Lang->getModule('label_hit_rate'),
                'label_slip_rate' => $nv_Lang->getModule('label_slip_rate'),
                'label_no_result' => $nv_Lang->getModule('label_no_result'),
                'label_cancel_result' => $nv_Lang->getModule('label_cancel'),
                'tongquan' => $nv_Lang->getModule('tongquan'),
                'dauthaudoclap' => $nv_Lang->getModule('dauthaudoclap'),
                'nhathauliendanh' => $nv_Lang->getModule('nhathauliendanh'),
                'sogoithau' => $nv_Lang->getModule('sogoithau'),
                'thongkedauthau' => $nv_Lang->getModule('thongkedauthau'),
                'goithau' => $nv_Lang->getModule('goithau'),
                'lichsudauthau' => $nv_Lang->getModule('lichsudauthau'),
                'lichsudauthau_byyear' => $nv_Lang->getModule('lichsudauthau_byyear'),
                'tyle' => $nv_Lang->getModule('tyle')
            ),
            'link' => array(
                'link_tongquat' => $link,
                'link_nhathaukhac' => $link_nhathaukhac,
                'link_liendanh' => $link_liendanh
            )
        );

        $arrChartByYear = array(
            'list_year' => $dataByYears['years'],
            'data' => [
                'hit_rate' => $dataByYears['result1'] ?? [],
                'slip_rate' => $dataByYears['result2'] ?? [],
                'no_result_rate' => $dataByYears['result3'] ?? [],
                'cancel_result' => $dataByYears['result4'] ?? [],
                'tong_thau' => $dataByYears['total_bidding'],
            ],
            'title_ressult' => array(
                'label_hit_rate' => $nv_Lang->getModule('label_hit_rate'),
                'label_slip_rate' => $nv_Lang->getModule('label_slip_rate'),
                'label_no_result_rate' => $nv_Lang->getModule('result3'),
                'label_cancel_result' => $nv_Lang->getModule('label_cancel'),
                'label_tongthau' => $nv_Lang->getModule('label_tongthau'),
                'lichsudauthau_byyear' => $nv_Lang->getModule('lichsudauthau_byyear'),
                'goithau' => $nv_Lang->getModule('goithau'),
            ),
        );

        $xtpl->assign('LINK_TRUNGTHAU', $link);
        $xtpl->assign('STRING_CHART_BY_YEAR', htmlentities(json_encode($arrChartByYear, JSON_HEX_QUOT)));
        $xtpl->assign('STRING_CHART', htmlentities(json_encode($arrChart, JSON_HEX_QUOT)));
        $xtpl->assign('SUM_CHART_TITLE', $arrChart['title_ressult']['lichsudauthau']);
        $xtpl->assign('YEAR_CHART_TITLE', $arrChart['title_ressult']['lichsudauthau_byyear']);
        $xtpl->assign('SUM_PACK', sprintf($nv_Lang->getModule('sum_package'), $number_result['total']));
        if (!$error_api) {
            $xtpl->assign('STATIC', sprintf($nv_Lang->getModule('static'), $array_data['lang_companyname']));
            $xtpl->assign('STATIC1', $array_data['static']);
            $xtpl->parse('main.detail.chart.static');
        } else {
            $xtpl->parse('main.detail.chart.error_api');
        }
        if (defined('NV_IS_USER')) {
            $xtpl->parse('main.detail.chart.btn_use_point_ability');
            $xtpl->parse('main.detail.chart.btn_use_point_ability_modal');
        }
        if ($dataByYears['is_show_chart']) {
            $xtpl->parse('main.detail.chart.chart_history_year');
        }
        $_show_chart_history = false;
        if (max($number_result) > 0 or max($number_independent) > 0 or max($number_joint_venture) > 0) {
            $_show_chart_history = true;
            $xtpl->parse('main.detail.chart.chart_history');
        }

        // $data_desire = $data_real = $data_inde = $data_desire_cdt = $data_real_cdt = $data_inde_cdt = $data_desire_not_tbmt = $data_real_not_tbmt = $data_inde_not_tbmt = '[';
        $data_desire_full = $data_real_full = $data_inde_full = $data_desire_cdt_full = $data_real_cdt_full = $data_inde_cdt_full = $data_desire_not_tbmt_full = $data_real_not_tbmt_full = $data_inde_not_tbmt_full = '[';
        $year = date('Y', NV_CURRENTTIME) - 7; // Chỉ hiển thị 7 năm

        $_is_show_value_chart = false;
        foreach (['', '_cdt', '_not_tbmt'] as $suffix) {
            if (sizeof($array_data['value_chart_desire' . $suffix]) > 0) {
                $_is_show_value_chart = true;
            }
            foreach ($array_data['value_chart_desire' . $suffix] as $value_desire) {
                ${'data_desire' . $suffix . '_full'} != '[' && ${'data_desire' . $suffix . '_full'} .= ',';
                ${'data_desire' . $suffix . '_full'} .= '[' . $value_desire[0] . ',' . $value_desire[1] . ']';
            }
            ${'data_desire' . $suffix . '_full'} .= ']';

            foreach ($array_data['value_chart_real' . $suffix] as $value_real) {
                ${'data_real' . $suffix . '_full'} != '[' && ${'data_real' . $suffix . '_full'} .= ',';
                ${'data_real' . $suffix . '_full'} .= '[' . $value_real[0] . ',' . $value_real[1] . ']';
            }
            ${'data_real' . $suffix . '_full'} .= ']';

            foreach ($array_data['value_chart_inde' . $suffix] as $value_inde) {
                ${'data_inde' . $suffix . '_full'} != '[' && ${'data_inde' . $suffix . '_full'} .= ',';
                ${'data_inde' . $suffix . '_full'} .= '[' . $value_inde[0] . ',' . $value_inde[1] . ']';
            }
            ${'data_inde' . $suffix . '_full'} .= ']';
        }
        if ($_is_show_value_chart) {
            $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
            $link_vip3_renew = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3';

            $chart_linkview_1 = sprintf($nv_Lang->getModule('notif_view_dropdown_vip_expired'), $link_vip3_renew);
            $chart_linkview_2 = sprintf($nv_Lang->getModule('notif_view_dropdown_not_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
            $chart_linkview_3 = sprintf($nv_Lang->getModule('notif_view_dropdown_not_user'), $link_register);

            $xtpl->assign('CHART_LINKVIEW_1', $chart_linkview_1);
            $xtpl->assign('CHART_LINKVIEW_2', $chart_linkview_2);
            $xtpl->assign('CHART_LINKVIEW_3', $chart_linkview_3);
            $xtpl->assign('DATA_DESIRE_FULL', $data_desire_full);
            $xtpl->assign('DATA_REAL_FULL', $data_real_full);
            $xtpl->assign('DATA_INDE_FULL', $data_inde_full);
            $xtpl->assign('DATA_DESIRE_CDT_FULL', $data_desire_cdt_full);
            $xtpl->assign('DATA_REAL_CDT_FULL', $data_real_cdt_full);
            $xtpl->assign('DATA_INDE_CDT_FULL', $data_inde_cdt_full);
            $xtpl->assign('DATA_DESIRE_NOT_TBMT_FULL', $data_desire_not_tbmt_full);
            $xtpl->assign('DATA_REAL_NOT_TBMT_FULL', $data_real_not_tbmt_full);
            $xtpl->assign('DATA_INDE_NOT_TBMT_FULL', $data_inde_not_tbmt_full);

            if (defined('NV_IS_VIP')) {
                $xtpl->parse('main.detail.value_chart.dropdown');
            }
            $xtpl->parse('main.detail.value_chart');
        }

        //Biểu đồ khảo sát mức độ chênh lệch về giá gói thầu và giá trúng thầu theo số lượng gói thầu.
        $xtpl->assign('LOCALE', NV_LANG_DATA == 'vi' ? 'vi-VN' : 'en-US');
        $difference_price_data = array_filter($array_difference_price, function($item) {
            return $item['value'] != 0;
        });
        if (sizeof($difference_price_data) > 0) {
            $chartData = [[$nv_Lang->getModule('grouping'), $nv_Lang->getModule('quantity')]];
            $chartColor = [];
            foreach ($difference_price_data as $item) {
                $chartData[] = [$item["label"], $item["value"]];
                $chartColor[] = $item["color"];
            }
            foreach ($array_difference_price as $data) {
                $data['value'] = nv_number_format($data['value']);
                $xtpl->assign('DIFFERENCE_PRICE_DATA', $data);
                $xtpl->parse('main.detail.difference_chart.loop');
            }
            $xtpl->assign('DIFFERENCE_PRICE_TOTAL', nv_number_format(array_sum(array_column($array_difference_price, 'value'))));
            $xtpl->assign('DIFFERENCE_CHART_DATA', json_encode($chartData));
            $xtpl->assign('DIFFERENCE_CHART_COLOR', json_encode($chartColor));
            $xtpl->parse('main.detail.difference_chart');
        }

        // Xử lý dữ liệu biểu đồ thể hiện chênh lệch các tiêu chí của nhà thầu ở các lĩnh vực
        if ($array_bidfields['is_show_chart']) {
            $arrChartByFields = array(
                'list_fields' => $array_bidfields['fields'],
                'data' => [
                    'total_doclap' => $array_bidfields['total_doclap'],
                    'total_win_doclap' => $array_bidfields['total_win_doclap'],
                    'total_liendanh' => $array_bidfields['total_liendanh'],
                    'total_win_liendanh' => $array_bidfields['total_win_liendanh'],
                    'total_slip_doclap' => $array_bidfields['total_slip_doclap'],
                    'total_slip_liendanh' => $array_bidfields['total_slip_liendanh'],
                    'total_price_doclap' => $array_bidfields['total_price_doclap'],
                    'total_price_liendanh' => $array_bidfields['total_price_liendanh']
                ],
                'title' => array(
                    'lb_total_doclap' => $nv_Lang->getModule('total_num_bids_dl'),
                    'lb_total_win_doclap' => $nv_Lang->getModule('total_num_win_dt'),
                    'lb_total_liendanh' => $nv_Lang->getModule('total_num_bids_partnership'),
                    'lb_total_win_liendanh' => $nv_Lang->getModule('total_num_win_partnership'),
                    'lb_total_num_slip_dl' => $nv_Lang->getModule('total_num_slip_dl'),
                    'lb_total_num_slip_ld' => $nv_Lang->getModule('total_num_slip_ld'),
                    'lb_total_price_doclap' => $nv_Lang->getModule('total_price_win_dt'),
                    'lb_total_price_liendanh' => $nv_Lang->getModule('total_price_win_ld'),
                    'goithau' => $nv_Lang->getModule('goithau'),
                    'num_bid_row' => $nv_Lang->getModule('num_bid_row'),
                    'total_win_price_title_y' => $nv_Lang->getModule('total_win_price_title_y'),
                    'interest_bids' => $nv_Lang->getModule('interest_bids')
                ),
            );
            $xtpl->assign('STRING_CHART_BY_FIELDS', htmlentities(json_encode($arrChartByFields, JSON_HEX_QUOT)));
            $xtpl->parse('main.detail.interest_chart');
        }
        if ($dataByYears['is_show_chart'] or $array_bidfields['is_show_chart'] or $_show_chart_history or $_is_show_value_chart or sizeof($difference_price_data) > 0) {
            $xtpl->parse('main.detail.list_charts');
        }
    }
    if (sizeof($arr_show10) > 0 or sizeof($arr_soclocitor3) > 0 or sizeof($array_open_other3) > 0 or sizeof($array_partnership3) > 0 or sizeof($contractor_province) > 0) {
        $xtpl->parse('main.detail.list_table_data');
    }
    if (!empty($solicitor)) {
        if ($array_data['hide_info'] != 1 or (defined('NV_IS_VIP') and $array_data['hide_vip3'] != 1)) {
            (NV_LANG_DATA != 'vi' && !empty($solicitor['english_name'])) && $solicitor['title'] = $solicitor['english_name'];
            $url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['solicitor'] . '/' . $solicitor['alias'] . '-' . $solicitor['id']);
            $xtpl->assign('IS_SOCLICTOR', sprintf($nv_Lang->getModule('is_soclictor'), $url, $solicitor['title'], $solicitor['num_khlcnt'], $solicitor['num_contract'], $solicitor['num_total'], $solicitor['num_tbmt'], $solicitor['num_tbmst'], $solicitor['num_result'], $solicitor['num_cancel'], $solicitor['num_notcode']));
            $xtpl->parse('main.detail.chart.solicitor');
            if (!empty($solicitor['invest_info']) || !empty($solicitor['is_project_owner'])) {
                $invest_info = json_decode($solicitor['invest_info'], true);
                // Thêm thống kê chủ đầu tư
                if (!empty($invest_info)) {
                    $url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['project-owner'] . '/' . $solicitor['alias'] . '-' . $solicitor['id']);
                    $xtpl->assign('INVESTOR_INFO', sprintf($nv_Lang->getModule('is_investor'), $url, $solicitor['title'], $invest_info['num_project'], $invest_info['num_plans']['num'], $invest_info['num_plans']['num'] - $invest_info['num_plans']['num_d'], $invest_info['num_plans']['num_d'], $invest_info['num_bid']['num'], $invest_info['num_bid']['num'] - $invest_info['num_bid']['num_d'], $invest_info['num_bid']['num_d']));
                    $xtpl->parse('main.detail.chart.investor_info');
                }
            }
        }
    }

    if (!empty($array_data['construct_org'])) {
        foreach ($array_data['construct_org'] as $construct_org) {
            $cons_cat_html = '';
            foreach ($construct_org['cat'] as $cons_cat) {
                $cons_cat_html .= '&emsp;&#9900 ';
                $cons_cat_html .= $cons_cat['linh_vuc'] . ': ';
                $cons_cat_html .= $cons_cat['linh_vuc_mo_rong_cap_1'];
                $cons_cat_html .= !empty($cons_cat['linh_vuc_mo_rong_cap_2']) ? ' ' . $cons_cat['linh_vuc_mo_rong_cap_2'] : '';
                $cons_cat_html .= '<br/>';
            }
            $xtpl->assign('CONSTRUCT_DETAIL', sprintf($nv_Lang->getModule('is_construct_org_html'), $construct_org['link'], $construct_org['ten_to_chuc'], $construct_org['ma_chung_chi'], $construct_org['co_quan_cap'], $cons_cat_html));
            $xtpl->parse('main.detail.chart.construct_org.loop');
        }
        $xtpl->parse('main.detail.chart.construct_org');
    }

    // Thông tin phòng thí nghiệm chuyên ngành xây dựng
    if (!empty($array_data['laboratory_data'])) {
        foreach ($array_data['laboratory_data'] as $key => $laboratory) {
            $laboratory_html = '';
            foreach ($laboratory as $lab) {
                $laboratory_html .= '&emsp;&#9900 ';
                $laboratory_html .= $lab['nguon'];
                $laboratory_html .= '<br/>';
            }
            $xtpl->assign('LABORATORY_DETAIL', sprintf($nv_Lang->getModule('is_laboratory_html'), $laboratory[0]['link_detail'], $laboratory[0]['ten_phongtn'], $laboratory[0]['ma_so_phongtn'], $laboratory_html));
            $xtpl->parse('main.detail.chart.laboratory.loop');
        }
        $xtpl->parse('main.detail.chart.laboratory');
    }

    // Thông tin VNR500
    if (!empty($vnr_info)) {
        $min_year_vnr_1 = min(array_keys($vnr_info['rank'][1] ?? [0 => 0]));
        $min_year_vnr_2 = min(array_keys($vnr_info['rank'][2] ?? [0 => 0]));
        for ($i = 1; $i <= 2; $i++) {
            if (!empty(${'min_year_vnr_' . $i})) {
                $min_year_vnr[] = ${'min_year_vnr_' . $i} . ' (' . $nv_Lang->getModule('vnr_type' . $i) . ')';
                $vnr_title[] = $nv_Lang->getModule('vnr_type' . $i . '_lg');
            }
        }
        $min_year = min($min_year_vnr_1 ?: date('Y'), $min_year_vnr_2 ?: date('Y'));
        for ($i = $min_year; $i < date('Y'); $i++) {
            if (!isset($vnr_info['rank'][1][$i]) && !empty($min_year_vnr_1)) {
                $vnr_info['rank'][1][$i] = null;
            }
            if (!isset($vnr_info['rank'][2][$i]) && !empty($min_year_vnr_2)) {
                $vnr_info['rank'][2][$i] = null;
            }
        }
        array_walk($vnr_info['rank'], 'ksort');
        $min_year_vnr = implode(', ', $min_year_vnr);
        $vnr_title = implode(' ' . $nv_Lang->getModule('and') . ' ', array_map(function ($a) {return '<b>' . $a . '</b>';}, $vnr_title));
        $xtpl->assign('STATIC_VNR', $nv_Lang->getModule('vnr_static', $vnr_title, $min_year_vnr, $vnr_info['vnr_type']));
        $xtpl->assign('DATA_VNR_1', json_encode(array_values($vnr_info['rank'][1] ?? [])));
        $xtpl->assign('DATA_VNR_2', json_encode(array_values($vnr_info['rank'][2] ?? [])));
        $xtpl->assign('DATA_KEY', json_encode(array_values(array_keys($vnr_info['rank'][1] ?: $vnr_info['rank'][2]))));
        $xtpl->parse('main.detail.chart.vnr_500');
        $xtpl->parse('main.detail.list_vnr');
    }

    // kiểm tra có kết nối tới dauthau.net không
    if ($array_data['profile_id'] > 0 and isset($array_data['array_profile']) and $array_data['array_profile']['connect_dauthau_info'] == 1) {
        $xtpl->assign('IS_DAUTHAUNET', sprintf($nv_Lang->getModule('is_dauthaunet'), $array_data['array_profile']['link'], NV_LANG_DATA == 'vi' ? $array_data['array_profile']['prof_name'] : $array_data['array_profile']['prof_enname']));
        if ($array_data['array_profile']['num_rows'] > 0 or $array_data['array_profile']['num_plans'] > 0 or $array_data['array_profile']['num_projects'] > 0) {
            $xtpl->assign('IS_DAUTHAUNET_DETAIL', sprintf($nv_Lang->getModule('is_dauthaunet_detail'), $array_data['array_profile']['num_projects'], $array_data['array_profile']['num_plans'], $array_data['array_profile']['num_rows']));
        } else {
            $xtpl->assign('IS_DAUTHAUNET_DETAIL', '');
        }
        $xtpl->parse('main.detail.chart.dauthaunet');
    } else {
        $xtpl->assign('NO_DAUTHAUNET', $nv_Lang->getModule('not_exist_dauthaunet'));
        if (!empty($array_data['true_code'])) {
            $xtpl->assign('MARKED', $nv_Lang->getModule('marked_not_used'));
        }
        $xtpl->parse('main.detail.chart.no_dauthaunet');
    }

    $xtpl->parse('main.detail.chart');
    if (defined("NV_IS_USER") and $module_captcha == 'recaptcha' and $global_config['recaptcha_ver'] == 2) {
        $xtpl->assign('RECAPTCHA_ELEMENT', 'recaptcha' . nv_genpass(8));
        $xtpl->assign('N_CAPTCHA', $nv_Lang->getGlobal('securitycode1'));
        $xtpl->parse('main.detail.recaptcha');
    }
    $xtpl->parse('main.detail.update');
    $xtpl->parse('main.detail');

    $link_vip_table = sprintf($nv_Lang->getModule('view_result_user_table'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
    $link_vip = sprintf($nv_Lang->getModule('view_result_user'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
    $link_login = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    $link_user = vsprintf($nv_Lang->getModule('view_result_client'), array(
        $link_login,
        $link_register
    ));
    $link_user_table = vsprintf($nv_Lang->getModule('view_result_client_table'), array(
        $link_login,
        $link_register
    ));

    // $link_vip_hide = sprintf($nv_Lang->getModule('hide_info'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=page/tinh-nang-bang-gia-goi-vip3.html');
    $link_vip_hide = $nv_Lang->getModule('hide_info');

    if(count($arr_show10) <= 3) {
        $arr_show3 = $arr_show10;
    } else {
        $arr_show3 = [];
        foreach ($arr_show10 as $item) {
            if (count($arr_show3) >= 3) {
                break;
            }
            if ($item['trung_thau'] != 3 and $item['trung_thau'] != 4) {
                $arr_show3[] = $item;
            }
        }

        if (count($arr_show3) < 3) {
            foreach ($arr_show10 as $item) {
                if ($item['trung_thau'] == 3 or $item['trung_thau'] == 4) {
                    $arr_show3[] = $item;
                    if(count($arr_show3) == 3) break;
                }
            }
        }
    }


    if ($array_data['hide_vip3'] != 1) {
        if (!empty($arr_show3) and sizeof($arr_show3) > 0) {
            if ($array_data['hide_info'] == 1 and !defined('NV_IS_VIP')) {
                $xtpl->assign('LINK_VIP_HIDE', $link_vip_hide);
                $xtpl->parse('main.bidding.hide');
            } else {
                $i = 0;
                foreach ($arr_show3 as $result) {
                    $i++;
                    $result['stt'] = $i;

                    if ($result['trung_thau'] == 1) {
                        $result['trung_thau_lang'] = $nv_Lang->getModule('result1');
                    } else if ($result['trung_thau'] == 2) {
                        $result['trung_thau_lang'] = $nv_Lang->getModule('result2');
                    } else if ($result['trung_thau'] == 3) {
                        $result['trung_thau_lang'] = $result['type'] == 'pq' ? $nv_Lang->getModule('result2_pq') : $nv_Lang->getModule('result4');
                    } else {
                        $result['trung_thau_lang'] = $nv_Lang->getModule('label_cancel');
                    }

                    if (isset($result['partnership']) and $result['partnership'] == 1) {
                        $result['partnership'] = $nv_Lang->getModule('vaitro1');
                    } else if (isset($result['partnership']) and $result['partnership'] == 2) {
                        $result['partnership'] = $nv_Lang->getModule('vaitro2');
                    } else {
                        $result['partnership'] = $nv_Lang->getModule('vaitro3');
                    }

                    $result['finish_time'] = $result['finish_time'] > 0 ? date("d/m/Y", $result['finish_time']) : '';
                    $xtpl->assign('RESULT', $result);
                    if ($result['trung_thau'] != 0) {
                        $xtpl->parse('main.bidding.show.loop.result1');
                    } else {
                        $xtpl->parse('main.bidding.show.loop.result0');
                    }

                    $xtpl->parse('main.bidding.show.loop');
                }

                $_result_number = sprintf($nv_Lang->getModule('number_result'), $array_data['lang_companyname'], $number_result['total'], $number_result['result1'], $number_result['result2'], $number_result['result3'], $number_result['result4']);
                if (defined('NV_IS_VIP')) {
                    $link = sprintf($nv_Lang->getModule('view_result_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=resultdetail/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
                    $_mess_result = substr($_result_number, 0, -1) . ', ' . strtolower($link);
                } else if (defined('NV_IS_USER')) {
                    $_mess_result = $_result_number . ' ' . $link_vip;
                } else {
                    $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                    $pop_up = sprintf($nv_Lang->getModule('view_result_client1'), $link_register);
                    $_mess_result = $_result_number . ' ' . $pop_up;
                }
                $xtpl->assign('NUMBER_RESULT', $_mess_result);
                $xtpl->parse('main.bidding.show');
            }

            $xtpl->parse('main.bidding');
        }
        $_link = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=detail/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id'];
        if (!empty($arr_show10) and sizeof($arr_show10) > 0) {
            if ($array_data['hide_info'] == 1 and !defined('NV_IS_VIP')) {
                $xtpl->assign('LINK_VIP_HIDE', $link_vip_hide);
                $xtpl->parse('main.timeline.hide');
            } else {

                $array_type_lc = [
                    11 => [
                        "title" => $nv_Lang->getModule('array_type_lc_11')
                    ],
                    12 => [
                        "title" => $nv_Lang->getModule('array_type_lc_12')
                    ],
                    13 => [
                        "title" => $nv_Lang->getModule('array_type_lc_13')
                    ],
                    14 => [
                        "title" => $nv_Lang->getModule('array_type_lc_14')
                    ],
                    16 => [
                        "title" => $nv_Lang->getModule('array_type_lc_16')
                    ],
                    17 => [
                        "title" => $nv_Lang->getModule('array_type_lc_17')
                    ],
                    19 => [
                        "title" => $nv_Lang->getModule('array_type_lc_19')
                    ],
                    21 => [
                        "title" => $nv_Lang->getModule('array_type_lc_21')
                    ],
                    22 => [
                        "title" => $nv_Lang->getModule('array_type_lc_22')
                    ],
                    23 => [
                        "title" => $nv_Lang->getModule('array_type_lc_23')
                    ],
                    24 => [
                        "title" => $nv_Lang->getModule('array_type_lc_24')
                    ],
                    25 => [
                        "title" => $nv_Lang->getModule('array_type_lc_25')
                    ],
                    28 => [
                        "title" => $nv_Lang->getModule('array_type_lc_28')
                    ]
                ];

                $i = 0;
                array_msort($arr_show10, "finish_time", 'desc');
                foreach ($arr_show10 as $result) {
                    ++$i;
                    if ($i % 2 == 0) {
                        $result['vt'] = 'timeline-inverted';
                        $result['vt2'] = 'invert';
                    } else {
                        $result['vt'] = $result['vt2'] = '';
                    }
                    $result['trung_thau'] = (!empty($result['trung_thau'])) ? $result['trung_thau'] : 0;
                    if ($result['trung_thau'] == 1) {
                        $result['trung_thau_lang'] = $nv_Lang->getModule('result1');
                        $lang = $nv_Lang->getModule('tt');
                        $result['vt2'] .= ' fa fa-check-circle-o';
                    } else if ($result['trung_thau'] == 2) {
                        $result['trung_thau_lang'] = $nv_Lang->getModule('result2');
                        $lang = $nv_Lang->getModule('trt');
                        $result['vt2'] .= ' fa fa-times-circle-o';
                    } else if ($result['trung_thau'] == 4) {
                        $result['trung_thau_lang'] = $nv_Lang->getModule('result_cancel');
                        $lang = $nv_Lang->getModule('tg');
                        $result['link_result'] = (!empty($result['link'])) ? $result['link'] : $result['link_result'];
                        $result['vt2'] .= ' fa fa-stop-circle-o';
                    } else {
                        if (!empty($result['array_pq'])) {
                            if ($result['array_pq'] == 2) {
                                $result['vt2'] .= ' fa fa-check-circle-o';
                            } elseif ($result['array_pq'] == 1) {
                                $result['vt2'] .= ' fa fa-times-circle-o';
                            } else {
                                $result['vt2'] .= ' fa fa-circle-o';
                            }
                        } else {
                            $result['trung_thau_lang'] = $nv_Lang->getModule('result4');
                            $lang = $nv_Lang->getModule('tg');
                            $result['vt2'] .= ' fa fa-circle-o';
                        }
                        $result['link_result'] = $result['link_open'] ?? $result['link'];
                    }
                    if (!empty($result['hinh_thuc_lua_chon'])) {
                        $lang2 = (in_array($result['hinh_thuc_lua_chon'], array_keys($dm_htlcnt)) ? $dm_htlcnt[$result['hinh_thuc_lua_chon']] : $result['hinh_thuc_lua_chon']);
                    } elseif (!empty($result['type_choose_id']) && !empty($array_type_lc[$result['type_choose_id']]['title'])) {
                        $lang2 = $array_type_lc[$result['type_choose_id']]['title'];
                    } else {
                        $lang2 = '';
                    }

                    if (!empty($result['link'])) {
                        $desc_timeline = str_replace('<aaa href="%s">', '<a href="%s">', $nv_Lang->getModule('desc_timeline'));
                        $desc_timeline = str_replace('</aaa>', '</a>', $desc_timeline);

                        $desc_timeline_partnership = str_replace('<aaa href="%s">', '<a href="%s">', $nv_Lang->getModule('desc_timeline_partnership'));
                        $desc_timeline_partnership = str_replace('</aaa>', '</a>', $desc_timeline_partnership);
                    } else {
                        $desc_timeline = str_replace('<aaa href="%s">', '%s', $nv_Lang->getModule('desc_timeline'));
                        $desc_timeline = str_replace('</aaa>', '', $desc_timeline);

                        $desc_timeline_partnership = str_replace('<aaa href="%s">', '%s', $nv_Lang->getModule('desc_timeline_partnership'));
                        $desc_timeline_partnership = str_replace('</aaa>', '', $desc_timeline_partnership);
                    }
                    empty($result['link_solicitor']) && $result['link_solicitor'] = 'javascript:void(0)';
                    if ($result['type'] != 'pq') {
                        if ($result['partnership'] == 1) {
                            $result['desc_timeline'] = sprintf($desc_timeline, $_link, $array_data['lang_companyname'], $lang, $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2);
                        } elseif ($result['partnership'] == 2) {
                            $result['desc_timeline'] = sprintf($desc_timeline_partnership, $_link, $array_data['lang_companyname'], $lang, $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2, $nv_Lang->getModule('ldc'), $result['bidder_name']);
                        } else {
                            $result['desc_timeline'] = sprintf($desc_timeline_partnership, $_link, $array_data['lang_companyname'], $lang, $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2, $nv_Lang->getModule('ldp'), $result['bidder_name']);
                        }
                    } else {
                        if ($result['joint_venture'] == '') {
                            $result['desc_timeline'] = sprintf($desc_timeline, $_link, $array_data['lang_companyname'], $nv_Lang->getModule('st'), $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2);
                        } else {
                            $result['desc_timeline'] = sprintf($desc_timeline_partnership, $_link, $array_data['lang_companyname'], $nv_Lang->getModule('st'), $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2, $nv_Lang->getModule('tv'), $result['joint_venture']);
                        }
                        $result['trung_thau_lang'] = $nv_Lang->getModule('result' . $result['array_pq'] . '_pq');
                        $result['link_result'] = $result['link'];
                    }
                    if (!empty($result['count_ld'])) {
                        $result['desc_timeline'] .= ' ' . sprintf($nv_Lang->getModule('desc_timeline_count_ld'), NV_LANG_DATA == 'en' ? vnd_to_words($result['count_ld'], false) : $result['count_ld']);
                    }
                    $result['kq'] = sprintf($nv_Lang->getModule('kq'), $result['link_result'], $result['trung_thau_lang']);

                    if ($result['finish_time'] > 0) {
                        $result['finish_time'] = date("d/m/Y H:i", $result['finish_time']);
                    } else {
                        $result['finish_time'] = '';
                    }
                    $xtpl->assign('RESULT', $result);
                    if ($result['trung_thau'] != 0) {
                        $xtpl->parse('main.timeline.show.loop.result1');
                    } else {
                        $xtpl->parse('main.timeline.show.loop.result0');
                    }

                    $xtpl->parse('main.timeline.show.loop');
                }
                if (defined('NV_IS_VIP')) {
                    $link = sprintf($nv_Lang->getModule('view_result_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=viewtimeline/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
                    $xtpl->assign('timeline_linkview', $link);
                } else if (defined('NV_IS_USER')) {
                    $xtpl->assign('timeline_linkview', sprintf($nv_Lang->getModule('timeline_view_result_user'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3'));
                } else {
                    $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                    $pop_up = sprintf($nv_Lang->getModule('timeline_result_client1'), $link_register);

                    $xtpl->assign('timeline_linkview', $pop_up);
                }

                $xtpl->parse('main.timeline.show');
            }
            $xtpl->parse('main.timeline');
        }
    }

    $xtpl->assign('BACKGROUND_INVESTOR', getBackgroundIsNotVip('bg_detail_businesslistings'));
    if (!empty($arr_soclocitor3) and sizeof($arr_soclocitor3) > 0 and $array_data['hide_vip3'] != 1) {
        if ($array_data['hide_info'] == 1 and !defined('NV_IS_VIP')) {
            $xtpl->assign('LINK_VIP_HIDE', $link_vip_hide);
            $xtpl->parse('main.investor.hide');
        } else {
            $j = 0;
            foreach ($arr_soclocitor3 as $soclocitor) {
                $j++;
                $soclocitor['stt'] = $j;

                $xtpl->assign('SOCLOCITOR', $soclocitor);
                $xtpl->assign('LOCK', $title_lock);
                if (defined('NV_IS_VIP')) {
                    $xtpl->parse('main.investor.show.loop.vip');
                } else if (defined('NV_IS_USER')) {
                    $xtpl->parse('main.investor.show.loop.user');
                } else {
                    $xtpl->parse('main.investor.show.loop.client');
                }

                $xtpl->parse('main.investor.show.loop');
            }
            $_num_solicitor = sprintf($nv_Lang->getModule('number_soclocitor'), $array_data['lang_companyname'], $num_soclocitor);
            if (defined('NV_IS_VIP')) {
                $link = sprintf($nv_Lang->getModule('view_result_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=viewinvestor/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
                $_mess_solicitor = substr($_num_solicitor, 0, -1) . ', ' . strtolower($link);
            } else if (defined('NV_IS_USER')) {
                $_mess_solicitor = $_num_solicitor . ' ' . $link_vip;
            } else {
                $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                $pop_up = sprintf($nv_Lang->getModule('view_result_client1'), $link_register);
                $_mess_solicitor = $_num_solicitor . ' ' . $pop_up;
            }
            $xtpl->assign('NUMBER_SOCLOCITOR', $_mess_solicitor);
            $xtpl->parse('main.investor.show');
        }

        $xtpl->parse('main.investor');
    }

    // var_dump($list_vipham);die;
    if (!empty($list_vipham)) {
        if ($array_data['hide_info'] == 1 and !defined('NV_IS_VIP')) {
            $xtpl->assign('LINK_VIP_HIDE', $link_vip_hide);
            $xtpl->parse('main.list_vipham.hide');
        } else {
            $i = 1;
            foreach ($list_vipham as $view) {

                if ($i > 1) {
                    break;
                }
                $view['issued_date'] = date('d/m/Y', $view['issued_date']);
                $xtpl->assign('VIEW', $view);
                if (defined('NV_IS_VIP')) {
                    $xtpl->parse('main.list_vipham.show.loop.vip');
                } else if (defined('NV_IS_USER')) {
                    $xtpl->assign('user', $link_vip_table);
                    $xtpl->parse('main.list_vipham.show.loop.user');
                } else {
                    $xtpl->assign('client', $link_user_table);
                    $xtpl->parse('main.list_vipham.show.loop.client');
                }
                $xtpl->parse('main.list_vipham.show.loop');
                $i++;
            }

            if (defined('NV_IS_VIP')) {
                //http://dauthau.vinades.my/businesslistings/viewviolators/CONG-TY-TNHH-PHAT-TAI-18625/
                $link = sprintf($nv_Lang->getModule('view_result_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=viewviolators/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
                $xtpl->assign('view_listvipham', $link);
            } else if (defined('NV_IS_USER')) {
                $xtpl->assign('view_listvipham', $link_vip);
            } else {
                $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                $pop_up = sprintf($nv_Lang->getModule('view_result_client1'), $link_register);
                $xtpl->assign('view_listvipham', $pop_up);
            }
            $numqdxp = sizeof($list_vipham);
            $xtpl->assign('NUMBER_VIPHAM', sprintf($nv_Lang->getModule('number_listvipham'), $numqdxp, $array_data['lang_companyname']));
            $xtpl->parse('main.list_vipham.show');
        }
        $xtpl->parse('main.list_vipham');
    }

    $xtpl->assign('BACKGROUND_BUSINESS', getBackgroundIsNotVip('bg_detail_businesslistings_business'));
    if (!empty($array_open_other3) and sizeof($array_open_other3) > 0 and $array_data['hide_vip3'] != 1) {
        if ($array_data['hide_info'] == 1 and !defined('NV_IS_VIP')) {
            $xtpl->assign('LINK_VIP_HIDE', $link_vip_hide);
            $xtpl->parse('main.business.hide');
        } else {
            $j = 0;
            foreach ($array_open_other3 as $k => $business) {
                $j++;
                $business['stt'] = $j;

                $xtpl->assign('BUSINESS', $business);
                if (!empty($k)) {
                    $xtpl->parse('main.business.show.loop.view_relative');
                }
                $xtpl->assign('LOCK', $title_lock);
                if (defined('NV_IS_VIP')) {
                    $xtpl->parse('main.business.show.loop.vip');
                } else if (defined('NV_IS_USER')) {
                    $xtpl->parse('main.business.show.loop.user');
                } else {
                    $xtpl->parse('main.business.show.loop.client');
                }

                $xtpl->parse('main.business.show.loop');
            }
            $_num_business = sprintf($nv_Lang->getModule('number_business'), $array_data['lang_companyname'], $num_array_open_other, sizeof($array_open_other_sum['so_tbmt']), $array_open_other_sum['result_1'], $array_open_other_sum['result_2'], $array_open_other_sum['result_3'], $array_open_other_sum['result_4']);
            if (defined('NV_IS_VIP')) {
                $link = sprintf($nv_Lang->getModule('view_result_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=detailbusiness/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
                $_mess_business = substr($_num_business, 0, -1) . ', ' . strtolower($link);
            } else if (defined('NV_IS_USER')) {
                $_mess_business = $_num_business . ' ' . $link_vip;
            } else {
                $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                $pop_up = sprintf($nv_Lang->getModule('view_result_client1'), $link_register);
                $_mess_business = $_num_business . ' ' . $pop_up;
            }
            $xtpl->assign('NUMBER_BUSINESS', $_mess_business);
            $xtpl->parse('main.business.show');
        }

        $xtpl->parse('main.business');
    }

    $xtpl->assign('BACKGROUND_PARTENRSHIP', getBackgroundIsNotVip('bg_detail_businesslistings_business'));

    // các nhà thầu cùng liên danh
    if (!empty($array_partnership3) and sizeof($array_partnership3) > 0 and $array_data['hide_vip3'] != 1) {
        if ($array_data['hide_info'] == 1 and !defined('NV_IS_VIP')) {
            $xtpl->assign('LINK_VIP_HIDE', $link_vip_hide);
            $xtpl->parse('main.partnership.hide');
        } else {
            $j = 0;
            foreach ($array_partnership3 as $partnership) {
                $j++;
                $partnership['stt'] = $j;

                $xtpl->assign('PARTNERSHIP', $partnership);
                $xtpl->assign('LOCK', $title_lock);
                if (defined('NV_IS_VIP')) {
                    $xtpl->parse('main.partnership.show.loop.vip');
                } else if (defined('NV_IS_USER')) {
                    $xtpl->parse('main.partnership.show.loop.user');
                } else {
                    $xtpl->parse('main.partnership.show.loop.client');
                }

                $xtpl->parse('main.partnership.show.loop');
            }
            $_num_partnership = sprintf($nv_Lang->getModule('number_partnership'), $array_data['lang_companyname'], $num_array_partnership, sizeof($array_partnership_sum['so_tbmt']), $array_partnership_sum['result_1'], $array_partnership_sum['result_2'], $array_partnership_sum['result_3'], $array_partnership_sum['result_4']);
            if (defined('NV_IS_VIP')) {
                $link = sprintf($nv_Lang->getModule('view_result_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=detailpartnership/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
                $_mess_partnership = substr($_num_partnership, 0, -1) . ', ' . strtolower($link);
            } else if (defined('NV_IS_USER')) {
                $_mess_partnership = $_num_partnership . ' ' . $link_vip;
            } else {
                $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                $pop_up = sprintf($nv_Lang->getModule('view_result_client1'), $link_register);
                $_mess_partnership = $_num_partnership . ' ' . $link_vip;
            }
            $xtpl->assign('NUMBER_PARTNERSHIP', $_mess_partnership);
            $xtpl->parse('main.partnership.show');
        }

        $xtpl->parse('main.partnership');
    }

    // Danh sách các tỉnh/thành phố tham gia thầu
    $province_arr_dt = [];
    foreach ($province_list as $province) {
        !empty($province['id']) && $province_arr_dt[$province['id']] = $province['title'];
    }
    $province[825] = $nv_Lang->getModule('vn_out_territory');
    $province[824] = $nv_Lang->getModule('nationwide');

    $xtpl->assign('BACKGROUND_PROVINCE', getBackgroundIsNotVip('bg_list_contractor_province'));
    array_msort($contractor_province, "num_total", 'desc');
    $_arr_data_province = $contractor_province;
    $num_bid_nationwide = isset($_arr_data_province[824]) ? $_arr_data_province[824]['num_total'] : 0;
    $num_bid_outside = isset($_arr_data_province[825]) ? $_arr_data_province[825]['num_total'] : 0;
    unset($_arr_data_province[824]);
    unset($_arr_data_province[825]);
    unset($_arr_data_province[0]);
    $total_province = count($_arr_data_province);

    $contractor_province = array_slice($contractor_province, 0, 3, true);
    if (!empty($contractor_province) and sizeof($contractor_province) > 0 and $array_data['hide_vip3'] != 1) {
        if ($array_data['hide_info'] == 1 and !defined('NV_IS_VIP')) {
            $xtpl->assign('LINK_VIP_HIDE', $link_vip_hide);
            $xtpl->parse('main.contractor_province.hide');
        } else {
            $j = 0;
            foreach ($contractor_province as $key => $_province) {
                $j++;
                $_province['stt'] = $j;
                $_province['total_win_price'] = number_format($_province['total_win_price'], 0, ',', '.') . ' VND';
                $_province['total_price_partnership'] = number_format($_province['total_win_price_liendanh'], 0, ',', '.') . ' VND';
                $_province['title'] = isset($province_arr_dt[$key]) ? $province_arr_dt[$key] : $nv_Lang->getModule('undefined');
                $_province['link'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=resultdetail/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id'], true) . '?prov=' . $key;
                $xtpl->assign('PROVINCE', $_province);
                $xtpl->assign('LOCK', $title_lock);
                if (defined('NV_IS_VIP')) {
                    $xtpl->parse('main.contractor_province.show.loop.vip');
                } else if (defined('NV_IS_USER')) {
                    $xtpl->parse('main.contractor_province.show.loop.user');
                } else {
                    $xtpl->parse('main.contractor_province.show.loop.client');
                }

                $xtpl->parse('main.contractor_province.show.loop');
            }
            $_num_province = sprintf($nv_Lang->getModule('number_province'), $array_data['lang_companyname'], $total_province, $num_bid_nationwide, $num_bid_outside);
            if (defined('NV_IS_VIP')) {
                $link = sprintf(strtolower($nv_Lang->getModule('view_result_vip')), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=contractor-province/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
                $_mess_province = substr($_num_province, 0, -1) . ', ' . $link;
            } else if (defined('NV_IS_USER')) {
                $_mess_province = $_num_province . ' ' . $link_vip;
            } else {
                $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                $pop_up = sprintf($nv_Lang->getModule('view_result_client1'), $link_register);
                $_mess_province = $_num_province . ' ' . $pop_up;
            }
            $xtpl->assign('NUMBER_PROVINCE', $_mess_province);
            $xtpl->parse('main.contractor_province.show');
        }

        $xtpl->parse('main.contractor_province');
    }

    /*
     * if (!empty($arr_news)) {
     * foreach ($arr_news as $_news) {
     * $_news['publish_date'] = nv_date('h:i d/m/Y', $_news['publish_date']);
     * $xtpl->assign('NEWS', $_news);
     * $xtpl->parse('main.news_api_isset.news_api');
     * }
     * $xtpl->parse('main.news_api_isset');
     * }
     */

    $xtpl->parse('main');
    return $xtpl->text('main');
}


function nv_theme_businesslistings_report($array_data, $global_array_config, $dataByYears, $arr_show10, $number_result, $arr_soclocitor3, $num_soclocitor, $array_open_other3, $array_open_other_sum, $num_array_open_other, $array_partnership3, $array_partnership_sum, $num_array_partnership, $solicitor, $list_vipham, $getIndustryICB, $crawl_request_history, $arr_row,  $array_difference_price, $number_independent, $number_joint_venture, $number_result_for_pdf)
{
    global $global_config, $module_name, $module_file, $module_config, $module_info, $op, $client_info, $user_info, $site_mods, $module_captcha, $global_array_vip, $dm_htlcnt, $title_lock, $nv_Lang, $page_title, $arr_org_size;
    $xtpl = new XTemplate("report-print.tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('TEMPLATE', $module_info['template']);
    $xtpl->assign('FILENAME', change_alias($page_title));
    $xtpl->assign('DATE_REPORT', nv_date_format(1, NV_CURRENTTIME));
    $xtpl->assign(
        'NUMBER_RESULT_FOR_PDF',
        sprintf(
            $nv_Lang->getModule('number_result_i'),
            $array_data['companyname'],
            $number_result_for_pdf['total'],
            $number_result_for_pdf['all'],
            $number_result_for_pdf['result1'],
            $number_result_for_pdf['result2'],
            $number_result_for_pdf['result3'],
            $number_result_for_pdf['result4'],
            $number_result_for_pdf['total_pg'],
            $number_result_for_pdf['total_pg0'],
            $number_result_for_pdf['total_pg1'],
            $number_result_for_pdf['total_pg2']
        )
    );

    $xtpl->assign('SELFURL', preg_replace('/pdf=\d/i', 'pdf=0', $client_info['selfurl']));
    $xtpl->assign('LINK_JS', '/themes/' . $module_info['template'] . '/');


    $xtpl->assign('CONFIG', $global_array_config);
    if ($array_data['gmaps'] != "") {
        $xtpl->parse('main.gmaps');
    }
    if ($array_data['short'] != "") {
        $xtpl->parse('main.detail.short');
    }

    if ($array_data['registrationtime'] != "") {
        $xtpl->parse('main.detail.registrationtime');
    }
    if ($array_data['representative'] != "") {
        $xtpl->parse('main.detail.representative');
    }
    if ($array_data['about'] != "" and $array_data['about'] != "0") {
        $xtpl->parse('main.detail.about');
    }

    if (!empty($array_data['office_address'])) {
        $xtpl->assign('office_address', $array_data['office_address']);
        $xtpl->parse('main.detail.office_address');
    }

    if (!empty($array_data['thong_tin_nganh_nghe']) or !empty($array_data['industry_dkkd']) or !empty($getIndustryICB)) {
        // hiển thị bảng thông tin ngành nghề theo dkkd
        if (!empty($array_data['industry_dkkd'])) {
            foreach ($array_data['industry_dkkd'] as $key => $industry) {
                $xtpl->assign('INDUSTRY_KEY', $key + 1);
                $xtpl->assign('INDUSTRY', $industry);
                $xtpl->parse('main.detail.industry.table_dkkd.loop_dkkd');
            }

            $xtpl->parse('main.detail.industry.tab_active_dkkd');
            $xtpl->parse('main.detail.industry.table_dkkd');
        }

        // hiển thị bảng thông tin ngành nghề theo MSC
        if (!empty($array_data['thong_tin_nganh_nghe'])) {
            if (empty($array_data['industry_dkkd'])) {
                $xtpl->assign('TAB_ACTIVE', 'active');
                $xtpl->assign('TAB_ACTIVE_IN', 'active in');
            }
            $xtpl->parse('main.detail.industry.tab_active_msc');
            // Hệ thống msc mới
            if (!empty($array_data['nganh_nghe_kinh_doanh'])) {
                foreach ($array_data['nganh_nghe_kinh_doanh'] as $key => $industry) {
                    $xtpl->assign('INDUSTRY_KEY', $key + 1);
                    $xtpl->assign('INDUSTRY', $industry['link_view']);
                    if ($industry['main'] == 1) {
                        $xtpl->assign('MAIN_INDUSTRY', '(' . $nv_Lang->getModule('main_industry') . ')');
                    } else {
                        $xtpl->assign('MAIN_INDUSTRY', '');
                    }
                    $xtpl->parse('main.detail.industry.table_msc.loop.main_industry');
                    $xtpl->parse('main.detail.industry.table_msc.loop');
                }
            } else {
                // Hệ thống msc cũ
                foreach ($array_data['thong_tin_nganh_nghe'] as $key => $industry) {
                    $xtpl->assign('INDUSTRY_KEY', $key + 1);
                    $xtpl->assign('INDUSTRY', $industry);
                    $xtpl->parse('main.detail.industry.table_msc.loop');
                }
            }

            $xtpl->parse('main.detail.industry.table_msc');
        }

        // Hiển thị ngành nghề ICB
        if (!empty($getIndustryICB)) {
            $i = 1;
            foreach ($getIndustryICB as $k => $v) {
                $v['stt'] = $i++;
                $xtpl->assign('VALUE', $v);
                $xtpl->parse('main.detail.industry.table_icb.loop');
            }
            if (empty($array_data['industry_dkkd']) and empty($array_data['thong_tin_nganh_nghe'])) {
                $xtpl->assign('TAB_ACTIVE_ICB', 'active');
                $xtpl->assign('TAB_ACTIVE_IN_ICB', 'active in');
            }
            $xtpl->parse('main.detail.industry.tab_active_icb');
            $xtpl->parse('main.detail.industry.table_icb');
        }

        $xtpl->parse('main.detail.industry');
    }

    if (!empty($array_data['taxdate'])) {
        $taxdate_convert = nv_date('d/m/Y', $array_data['taxdate']);
        $xtpl->assign('TAXDATE_CONVERT', $taxdate_convert);
        $xtpl->parse('main.detail.taxdate');
    }

    // Tình trạng nộp phí
    if (!empty($array_data['orgcode'])) {
        $nop_phi_type_title = [
            1 => '<font color="red">' . $nv_Lang->getModule('nop_phi_type_title_1') . '</font>',
            2 => '<font color="red">' . $nv_Lang->getModule('nop_phi_type_title_2') . '</font>',
            3 => $nv_Lang->getModule('nop_phi_type_title_3')
        ];
        if (!empty($array_data['nop_phi_type'])) {
            $array_data['thoi_han'] = !empty($nop_phi_type_title[$array_data['nop_phi_type']]) ? $nop_phi_type_title[$array_data['nop_phi_type']] : '';
        }
    }

    if (isset($array_data['time_crawler'])) {
        $array_data['date_crawler'] = explode(' ', $array_data['time_crawler'])[1];
    }

    $xtpl->assign('ROW', $array_data);

    /* MSC cũ */
    // email
    if ($array_data['email'] != "") {
        $xtpl->parse('main.detail.email');
    }
    // phone
    if ($array_data['phone'] != "") {
        $xtpl->parse('main.detail.phone');
    }
    // fax
    if ($array_data['fax'] != "") {
        $xtpl->parse('main.detail.fax');
    }
    /* MSC mới */
    if ($array_data['represent_phone'] != "") {
        $xtpl->parse('main.detail.represent_phone');
    }
    if ($array_data['represent_email'] != "") {
        $xtpl->parse('main.detail.represent_email');
    }
    if ($array_data['website'] != '') {
        $xtpl->parse('main.detail.website');
    }
    if ($array_data['linh_vuc_kinh_doanh'] != '') {
        $xtpl->parse('main.detail.linh_vuc_kinh_doanh');
    }
    if ($array_data['businesstype'] != '') {
        $xtpl->parse('main.detail.businesstype');
    }

    if (!empty($array_data['thoi_han'])) {
        $xtpl->parse('main.detail.thoi_han');
    }
    $xtpl->parse('main.detail.companyname');
    if (!empty($array_data['tax_nation'])) {
        $xtpl->parse('main.detail.tax_nation');
    }
    if (!empty($array_data['orgcode'])) {
        $xtpl->parse('main.detail.orgcode');
    }
    if (!empty($array_data['rep_name'])) {
        $xtpl->parse('main.detail.rep_name');
    }
    if (!empty($array_data['rep_position'])) {
        $xtpl->parse('main.detail.rep_position');
    }


    if (!empty($array_data['trading_address'])) {
        $xtpl->assign('trading_address', $array_data['trading_address']);
        $xtpl->parse('main.detail.trading_address');
    }

    if (!empty($array_data['so_nhan_vien'])) {
        $xtpl->parse('main.detail.so_nhan_vien');
    }

    if (empty($array_data['orgcode'])) {
        $xtpl->parse('main.detail.currentstatus');
    }

    if ($array_data['org_scale'] != '' && isset($arr_org_size[$array_data['org_scale']])) {
        $org_scale_title = $arr_org_size[$array_data['org_scale']]['name_' . NV_LANG_DATA];
        $xtpl->assign('ORG_SCALE_TITLE', $org_scale_title);
        $xtpl->parse('main.detail.org_scale');
    }

    if ($array_data['hide_info'] != 1 or (defined('NV_IS_VIP') and $array_data['hide_vip3'] != 1)) {
        $link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=businesslistings&amp;" . NV_OP_VARIABLE . "=resultdetail";
        $link = nv_url_rewrite($link . "/" . change_alias($array_data['lang_companyname']) . "-" . $array_data['id'], true);

        $link_nhathaukhac = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=businesslistings&amp;" . NV_OP_VARIABLE . "=detailbusiness";
        $link_nhathaukhac = nv_url_rewrite($link_nhathaukhac . "/" . change_alias($array_data['lang_companyname']) . "-" . $array_data['id'], true);

        $link_liendanh = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=businesslistings&amp;" . NV_OP_VARIABLE . "=detailpartnership";
        $link_liendanh = nv_url_rewrite($link_liendanh . "/" . change_alias($array_data['lang_companyname']) . "-" . $array_data['id'], true);

        $arrChart = array(
            'char_ratio_win' => array(
                'titleName' => $nv_Lang->getModule('name_ratio_win'),
                'hit_rate' => $number_result['result1'],
                'slip_rate' => $number_result['result2'],
                'no_result' => $number_result['result3'],
                'result_cancel' => $number_result['result4'],
                'tong' => $number_result['result1'] + $number_result['result2'] + $number_result['result3'] + $number_result['result4']
            ),
            'char_ratio_order' => array(
                'titleName' => $nv_Lang->getModule('name_ratio_order'),
                'hit_rate' => $number_independent['result1'],
                'slip_rate' => $number_independent['result2'],
                'no_result' => $number_independent['result3'],
                'result_cancel' => $number_independent['result4'],
                'tong' => $number_independent['result1'] + $number_independent['result2'] + $number_independent['result3'] + $number_independent['result4']
            ),
            'char_ratio_joint_venture' => array(
                'titleName' => $nv_Lang->getModule('name_ratio_joint_venture'),
                'hit_rate' => $number_joint_venture['result1'],
                'slip_rate' => $number_joint_venture['result2'],
                'no_result' => $number_joint_venture['result3'],
                'result_cancel' => $number_joint_venture['result4'],
                'tong' => $number_joint_venture['result1'] + $number_joint_venture['result2'] + $number_joint_venture['result3'] + $number_joint_venture['result4']
            ),
            'title_ressult' => array(
                'label_hit_rate' => $nv_Lang->getModule('label_hit_rate'),
                'label_slip_rate' => $nv_Lang->getModule('label_slip_rate'),
                'label_no_result' => $nv_Lang->getModule('label_no_result'),
                'label_cancel_result' => $nv_Lang->getModule('label_cancel'),
                'tongquan' => $nv_Lang->getModule('tongquan'),
                'dauthaudoclap' => $nv_Lang->getModule('dauthaudoclap'),
                'nhathauliendanh' => $nv_Lang->getModule('nhathauliendanh'),
                'sogoithau' => $nv_Lang->getModule('sogoithau'),
                'thongkedauthau' => $nv_Lang->getModule('thongkedauthau'),
                'goithau' => $nv_Lang->getModule('goithau'),
                'lichsudauthau' => $nv_Lang->getModule('lichsudauthau'),
                'tyle' => $nv_Lang->getModule('tyle')
            ),
            'link' => array(
                'link_tongquat' => $link,
                'link_nhathaukhac' => $link_nhathaukhac,
                'link_liendanh' => $link_liendanh
            )
        );

        $arrChartByYear = array(
            'list_year' => $dataByYears['years'],
            'data' => [
                'hit_rate' => $dataByYears['trung_thau'],
                'slip_rate' => $dataByYears['truot_thau'],
                'cancel_result' => $dataByYears['cancel_result'],
                'tong_thau' => $dataByYears['total_bidding'],
            ],
            'title_ressult' => array(
                'label_hit_rate' => $nv_Lang->getModule('label_hit_rate'),
                'label_slip_rate' => $nv_Lang->getModule('label_slip_rate'),
                'label_cancel_result' => $nv_Lang->getModule('label_cancel'),
                'label_tongthau' => $nv_Lang->getModule('label_tongthau'),
                'lichsudauthau_byyear' => $nv_Lang->getModule('lichsudauthau_byyear'),
                'goithau' => $nv_Lang->getModule('goithau'),
            ),
        );



        $xtpl->assign('LINK_TRUNGTHAU', $link);
        $xtpl->assign('STRING_CHART_BY_YEAR', json_encode($arrChartByYear));
        $xtpl->assign('STRING_CHART', json_encode($arrChart));
        $xtpl->assign('SUM_PACK', sprintf($nv_Lang->getModule('sum_package'), $number_result['total']));
        $xtpl->assign('STATIC', sprintf($nv_Lang->getModule('static'), $array_data['lang_companyname']));
        // $number_result['total'],
        // $number_result['result1'],
        // $number_result['result2'],
        // $number_result['result3'],
        // $number_result['result4'],
        // $number_result['total_win_price'],
        // $number_result['total_chidinhthau'],
        // $number_result['num_min_bid'],
        // $number_result['win_on_est'],
        // implode(', ',$array_data['prov_list']),
        // $num_soclocitor,
        // $num_array_open_other,
        // sizeof($array_open_other_sum['so_tbmt']),
        // $array_open_other_sum['result_1'],
        // $array_open_other_sum['result_2'],
        // $array_open_other_sum['result_3'],
        // $array_open_other_sum['result_4'],
        // $num_array_partnership,
        // sizeof($array_partnership_sum['so_tbmt']),
        // $array_partnership_sum['result_1'],
        // $array_partnership_sum['result_2'],
        // $array_partnership_sum['result_3'],
        // $array_partnership_sum['result_4']));
        $xtpl->assign('STATIC1', $array_data['static']);
        $xtpl->parse('main.detail.chart');
        $data_desire = $data_real = $data_inde = $data_desire_cdt = $data_real_cdt = $data_inde_cdt = $data_desire_not_tbmt = $data_real_not_tbmt = $data_inde_not_tbmt = '[';
        foreach ($array_data['value_chart_desire'] as $value_desire) {
            $data_desire .= '[' . $value_desire[0] . ',' . $value_desire[1] . '],';
        }
        $data_desire .= ']';
        foreach ($array_data['value_chart_real'] as $value_real) {
            $data_real .= '[' . $value_real[0] . ',' . $value_real[1] . '],';
        }
        $data_real .= ']';

        foreach ($array_data['value_chart_inde'] as $value_inde) {
            $data_inde .= '[' . $value_inde[0] . ',' . $value_inde[1] . '],';
        }
        $data_inde .= ']';

        foreach ($array_data['value_chart_desire_cdt'] as $value_desire_cdt) {
            $data_desire_cdt .= '[' . $value_desire_cdt[0] . ',' . $value_desire_cdt[1] . '],';
        }
        $data_desire_cdt .= ']';
        foreach ($array_data['value_chart_real_cdt'] as $value_real_cdt) {
            $data_real_cdt .= '[' . $value_real_cdt[0] . ',' . $value_real_cdt[1] . '],';
        }
        $data_real_cdt .= ']';
        foreach ($array_data['value_chart_inde_cdt'] as $value_inde_cdt) {
            $data_inde_cdt .= '[' . $value_inde_cdt[0] . ',' . $value_inde_cdt[1] . '],';
        }
        $data_inde_cdt .= ']';

        foreach ($array_data['value_chart_desire_not_tbmt'] as $value_desire_2) {
            $data_desire_not_tbmt .= '[' . $value_desire_2[0] . ',' . $value_desire_2[1] . '],';
        }
        $data_desire_not_tbmt .= ']';
        foreach ($array_data['value_chart_real_not_tbmt'] as $value_real_2) {
            $data_real_not_tbmt .= '[' . $value_real_2[0] . ',' . $value_real_2[1] . '],';
        }
        $data_real_not_tbmt .= ']';
        foreach ($array_data['value_chart_inde_not_tbmt'] as $value_inde_not_tbmt) {
            $data_inde_not_tbmt .= '[' . $value_inde_not_tbmt[0] . ',' . $value_inde_not_tbmt[1] . '],';
        }
        $data_inde_not_tbmt .= ']';

        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $link_vip3_renew = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3';

        $chart_linkview_1 = sprintf($nv_Lang->getModule('notif_view_dropdown_vip_expired'), $link_vip3_renew);
        $chart_linkview_2 = sprintf($nv_Lang->getModule('notif_view_dropdown_not_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
        $chart_linkview_3 = sprintf($nv_Lang->getModule('notif_view_dropdown_not_user'), $link_register);

        $xtpl->assign('CHART_LINKVIEW_1', $chart_linkview_1);
        $xtpl->assign('CHART_LINKVIEW_2', $chart_linkview_2);
        $xtpl->assign('CHART_LINKVIEW_3', $chart_linkview_3);
        $xtpl->assign('DATA_DESIRE', $data_desire);
        $xtpl->assign('DATA_REAL', $data_real);
        $xtpl->assign('DATA_INDE', $data_inde);
        $xtpl->assign('DATA_DESIRE_CDT', $data_desire_cdt);
        $xtpl->assign('DATA_REAL_CDT', $data_real_cdt);
        $xtpl->assign('DATA_INDE_CDT', $data_inde_cdt);
        $xtpl->assign('DATA_DESIRE_NOT_TBMT', $data_desire_not_tbmt);
        $xtpl->assign('DATA_REAL_NOT_TBMT', $data_real_not_tbmt);
        $xtpl->assign('DATA_INDE_NOT_TBMT', $data_inde_not_tbmt);
        if (defined('NV_IS_VIP')) {
            $xtpl->parse('main.detail.value_chart.dropdown');
        }
        $xtpl->parse('main.detail.value_chart');
    }

    if (!empty($solicitor)) {
        if ($array_data['hide_info'] != 1 or (defined('NV_IS_VIP') and $array_data['hide_vip3'] != 1)) {
            (NV_LANG_DATA != 'vi' && !empty($solicitor['english_name'])) && $solicitor['title'] = $solicitor['english_name'];
            $url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['solicitor'] . '/' . $solicitor['alias'] . '-' . $solicitor['id']);
            $xtpl->assign('IS_SOCLICTOR', sprintf($nv_Lang->getModule('is_soclictor'), $url, $solicitor['title'], $solicitor['num_khlcnt'], $solicitor['num_contract'], $solicitor['num_total'], $solicitor['num_tbmt'], $solicitor['num_tbmst'], $solicitor['num_result'], $solicitor['num_cancel'], $solicitor['num_notcode']));
            $xtpl->parse('main.detail.solicitor');
            if (!empty($solicitor['invest_info']) || !empty($solicitor['is_project_owner'])) {
                $invest_info = json_decode($solicitor['invest_info'], true);
                // Thêm thống kê chủ đầu tư
                if (!empty($invest_info)) {
                    $url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['project-owner'] . '/' . $solicitor['alias'] . '-' . $solicitor['id']);
                    $xtpl->assign('INVESTOR_INFO', sprintf($nv_Lang->getModule('is_investor'), $url, $solicitor['title'], $invest_info['num_project'], $invest_info['num_plans']['num'], $invest_info['num_plans']['num'] - $invest_info['num_plans']['num_d'], $invest_info['num_plans']['num_d'], $invest_info['num_bid']['num'], $invest_info['num_bid']['num'] - $invest_info['num_bid']['num_d'], $invest_info['num_bid']['num_d']));
                    $xtpl->parse('main.detail.investor_info');
                }
            }
        }
    }

    if (!empty($array_data['construct_org'])) {
        foreach ($array_data['construct_org'] as $construct_org) {
            $cons_cat_html = '';
            foreach ($construct_org['cat'] as $cons_cat) {
                $cons_cat_html .= '<span class="lv-bullet"></span>';
                $cons_cat_html .= $cons_cat['linh_vuc'] . ': ';
                $cons_cat_html .= $cons_cat['linh_vuc_mo_rong_cap_1'];
                $cons_cat_html .= !empty($cons_cat['linh_vuc_mo_rong_cap_2']) ? ' ' . $cons_cat['linh_vuc_mo_rong_cap_2'] : '';
                $cons_cat_html .= '<br/>';
            }
            $xtpl->assign('CONSTRUCT_DETAIL', sprintf($nv_Lang->getModule('is_construct_org_html'), $construct_org['link'], $construct_org['ten_to_chuc'], $construct_org['ma_chung_chi'], $construct_org['co_quan_cap'], $cons_cat_html));
            $xtpl->parse('main.detail.construct_org.loop');
        }
        $xtpl->parse('main.detail.construct_org');
    }

    // Thông tin phòng thí nghiệm chuyên ngành xây dựng
    if (!empty($array_data['laboratory_data'])) {
        foreach ($array_data['laboratory_data'] as $key => $laboratory) {
            $laboratory_html = '';
            foreach ($laboratory as $lab) {
                $laboratory_html .= '&emsp;&#9900 ';
                $laboratory_html .= $lab['nguon'];
                $laboratory_html .= '<br/>';
            }
            $xtpl->assign('LABORATORY_DETAIL', sprintf($nv_Lang->getModule('is_laboratory_html'), $laboratory[0]['link_detail'], $laboratory[0]['ten_phongtn'], $laboratory[0]['ma_so_phongtn'], $laboratory_html));
            $xtpl->parse('main.detail.laboratory.loop');
        }
        $xtpl->parse('main.detail.laboratory');
    }

    // kiểm tra có kết nối tới dauthau.net không
    if ($array_data['profile_id'] > 0 and isset($array_data['array_profile']) and $array_data['array_profile']['connect_dauthau_info'] == 1) {
        $xtpl->assign('IS_DAUTHAUNET', sprintf($nv_Lang->getModule('is_dauthaunet'), $array_data['array_profile']['link'], NV_LANG_DATA == 'vi' ? $array_data['array_profile']['prof_name'] : $array_data['array_profile']['prof_enname']));
        if ($array_data['array_profile']['num_rows'] > 0 or $array_data['array_profile']['num_plans'] > 0 or $array_data['array_profile']['num_projects'] > 0) {
            $xtpl->assign('IS_DAUTHAUNET_DETAIL', sprintf($nv_Lang->getModule('is_dauthaunet_detail'), $array_data['array_profile']['num_projects'], $array_data['array_profile']['num_plans'], $array_data['array_profile']['num_rows']));
        } else {
            $xtpl->assign('IS_DAUTHAUNET_DETAIL', '');
        }
        $xtpl->parse('main.detail.dauthaunet');
    } else {
        $xtpl->assign('NO_DAUTHAUNET', $nv_Lang->getModule('not_exist_dauthaunet'));
        if (!empty($array_data['true_code'])) {
            $xtpl->assign('MARKED', $nv_Lang->getModule('marked_not_used'));
        }
        $xtpl->parse('main.detail.no_dauthaunet');
    }

    $xtpl->parse('main.detail');

    $link_vip_table = sprintf($nv_Lang->getModule('view_result_user_table'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
    $link_vip = sprintf($nv_Lang->getModule('view_result_user'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
    $link_login = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    $link_user = vsprintf($nv_Lang->getModule('view_result_client'), array(
        $link_login,
        $link_register
    ));
    $link_user_table = vsprintf($nv_Lang->getModule('view_result_client_table'), array(
        $link_login,
        $link_register
    ));

    $link_vip_hide = $nv_Lang->getModule('hide_info');

    if(count($arr_show10) <= 3) {
        $arr_show3 = $arr_show10;
    } else {
        $arr_show3 = [];
        foreach ($arr_show10 as $item) {
            if (count($arr_show3) >= 3) {
                break;
            }
            if ($item['trung_thau'] != 3 and $item['trung_thau'] != 4) {
                $arr_show3[] = $item;
            }
        }

        if (count($arr_show3) < 3) {
            foreach ($arr_show10 as $item) {
                if ($item['trung_thau'] == 3 or $item['trung_thau'] == 4) {
                    $arr_show3[] = $item;
                    if(count($arr_show3) == 3) break;
                }
            }
        }
    }

    if ($array_data['hide_vip3'] != 1) {
        if (!empty($arr_show3) and sizeof($arr_show3) > 0) {
            if ($array_data['hide_info'] == 1 and !defined('NV_IS_VIP')) {
                $xtpl->assign('LINK_VIP_HIDE', $link_vip_hide);
                $xtpl->parse('main.bidding.hide');
            } else {
                $i = 0;
                foreach ($arr_show3 as $result) {
                    $i++;
                    $result['stt'] = $i;

                    if ($result['trung_thau'] == 1) {
                        $result['trung_thau_lang'] = $nv_Lang->getModule('result1');
                    } else if ($result['trung_thau'] == 2) {
                        $result['trung_thau_lang'] = $nv_Lang->getModule('result2');
                    } else if ($result['trung_thau'] == 3) {
                        $result['trung_thau_lang'] = $result['type'] == 'pq' ? $nv_Lang->getModule('result2_pq') : $nv_Lang->getModule('result4');
                    } else {
                        $result['trung_thau_lang'] = $nv_Lang->getModule('label_cancel');
                    }

                    if (isset($result['partnership']) and $result['partnership'] == 1) {
                        $result['partnership'] = $nv_Lang->getModule('vaitro1');
                    } else if (isset($result['partnership']) and $result['partnership'] == 2) {
                        $result['partnership'] = $nv_Lang->getModule('vaitro2');
                    } else {
                        $result['partnership'] = $nv_Lang->getModule('vaitro3');
                    }

                    $result['finish_time'] = $result['finish_time'] > 0 ? date("d/m/Y", $result['finish_time']) : '';
                    $xtpl->assign('RESULT', $result);
                    if ($result['trung_thau'] != 0) {
                        $xtpl->parse('main.bidding.show.loop.result1');
                    } else {
                        $xtpl->parse('main.bidding.show.loop.result0');
                    }

                    $xtpl->parse('main.bidding.show.loop');
                }

                if (defined('NV_IS_VIP')) {
                    $link = sprintf($nv_Lang->getModule('view_result_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=resultdetail/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
                    $xtpl->assign('vip', $link);
                    $xtpl->parse('main.bidding.show.vip');
                } else if (defined('NV_IS_USER')) {
                    $xtpl->assign('user', $link_vip);
                    $xtpl->parse('main.bidding.show.user');
                } else {
                    $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                    $pop_up = sprintf($nv_Lang->getModule('view_result_client1'), $link_register);
                    $xtpl->assign('client', $pop_up);
                    $xtpl->parse('main.bidding.show.client');
                }
                $xtpl->assign('NUMBER_RESULT', sprintf($nv_Lang->getModule('number_result'), $array_data['lang_companyname'], $number_result['total'], $number_result['result1'], $number_result['result2'], $number_result['result3'], $number_result['result4']));
                $xtpl->parse('main.bidding.show');
            }

            $xtpl->parse('main.bidding');
        }
        $_link = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=detail/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id'];
        if (!empty($arr_show10) and sizeof($arr_show10) > 0) {
            if ($array_data['hide_info'] == 1 and !defined('NV_IS_VIP')) {
                $xtpl->assign('LINK_VIP_HIDE', $link_vip_hide);
                $xtpl->parse('main.timeline.hide');
            } else {

                $array_type_lc = [
                    11 => [
                        "title" => $nv_Lang->getModule('array_type_lc_11')
                    ],
                    12 => [
                        "title" => $nv_Lang->getModule('array_type_lc_12')
                    ],
                    13 => [
                        "title" => $nv_Lang->getModule('array_type_lc_13')
                    ],
                    14 => [
                        "title" => $nv_Lang->getModule('array_type_lc_14')
                    ],
                    16 => [
                        "title" => $nv_Lang->getModule('array_type_lc_16')
                    ],
                    17 => [
                        "title" => $nv_Lang->getModule('array_type_lc_17')
                    ],
                    19 => [
                        "title" => $nv_Lang->getModule('array_type_lc_19')
                    ],
                    21 => [
                        "title" => $nv_Lang->getModule('array_type_lc_21')
                    ],
                    22 => [
                        "title" => $nv_Lang->getModule('array_type_lc_22')
                    ],
                    23 => [
                        "title" => $nv_Lang->getModule('array_type_lc_23')
                    ],
                    24 => [
                        "title" => $nv_Lang->getModule('array_type_lc_24')
                    ],
                    25 => [
                        "title" => $nv_Lang->getModule('array_type_lc_25')
                    ],
                    28 => [
                        "title" => $nv_Lang->getModule('array_type_lc_28')
                    ]
                ];

                $i = 0;
                array_msort($arr_show10, "finish_time", 'desc');
                foreach ($arr_show10 as $result) {
                    ++$i;
                    if ($i % 2 == 0) {
                        $result['vt'] = 'timeline-inverted';
                        $result['vt2'] = 'invert';
                    } else {
                        $result['vt'] = $result['vt2'] = '';
                    }
                    $result['trung_thau'] = (!empty($result['trung_thau'])) ? $result['trung_thau'] : 0;
                    if ($result['trung_thau'] == 1) {
                        $result['trung_thau_lang'] = $nv_Lang->getModule('result1');
                        $lang = $nv_Lang->getModule('tt');
                        $result['vt2'] .= ' fa fa-check-circle-o';
                    } else if ($result['trung_thau'] == 2) {
                        $result['trung_thau_lang'] = $nv_Lang->getModule('result2');
                        $lang = $nv_Lang->getModule('trt');
                        $result['vt2'] .= ' fa fa-times-circle-o';
                    } else if ($result['trung_thau'] == 4) {
                        $result['trung_thau_lang'] = $nv_Lang->getModule('result_cancel');
                        $lang = $nv_Lang->getModule('tg');
                        $result['link_result'] = (!empty($result['link'])) ? $result['link'] : $result['link_result'];
                        $result['vt2'] .= ' fa fa-stop-circle-o';
                    } else {
                        if (!empty($result['array_pq'])) {
                            if ($result['array_pq'] == 2) {
                                $result['vt2'] .= ' fa fa-check-circle-o';
                            } elseif ($result['array_pq'] == 1) {
                                $result['vt2'] .= ' fa fa-times-circle-o';
                            } else {
                                $result['vt2'] .= ' fa fa-circle-o';
                            }
                        } else {
                            $result['trung_thau_lang'] = $nv_Lang->getModule('result4');
                            $lang = $nv_Lang->getModule('tg');
                            $result['vt2'] .= ' fa fa-circle-o';
                        }
                    }
                    if (!empty($result['hinh_thuc_lua_chon'])) {
                        $lang2 = $nv_Lang->getModule('tht') . (in_array($result['hinh_thuc_lua_chon'], array_keys($dm_htlcnt)) ? $dm_htlcnt[$result['hinh_thuc_lua_chon']] : $result['hinh_thuc_lua_chon']);
                    } elseif (!empty($result['type_choose_id']) && !empty($array_type_lc[$result['type_choose_id']]['title'])) {
                        $lang2 = $nv_Lang->getModule('tht') . $array_type_lc[$result['type_choose_id']]['title'];
                    } else {
                        $lang2 = '';
                    }

                    if (!empty($result['link'])) {
                        $desc_timeline = str_replace('<aaa href="%s">', '<a href="%s">', $nv_Lang->getModule('desc_timeline'));
                        $desc_timeline = str_replace('</aaa>', '</a>', $desc_timeline);

                        $desc_timeline_partnership = str_replace('<aaa href="%s">', '<a href="%s">', $nv_Lang->getModule('desc_timeline_partnership'));
                        $desc_timeline_partnership = str_replace('</aaa>', '</a>', $desc_timeline_partnership);
                    } else {
                        $desc_timeline = str_replace('<aaa href="%s">', '%s', $nv_Lang->getModule('desc_timeline'));
                        $desc_timeline = str_replace('</aaa>', '', $desc_timeline);

                        $desc_timeline_partnership = str_replace('<aaa href="%s">', '%s', $nv_Lang->getModule('desc_timeline_partnership'));
                        $desc_timeline_partnership = str_replace('</aaa>', '', $desc_timeline_partnership);
                    }
                    empty($result['link_solicitor']) && $result['link_solicitor'] = 'javascript:void(0)';
                    if ($result['type'] != 'pq') {
                        if ($result['partnership'] == 1) {
                            $result['desc_timeline'] = sprintf($desc_timeline, $_link, $array_data['lang_companyname'], $lang, $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2);
                        } elseif ($result['partnership'] == 2) {
                            $result['desc_timeline'] = sprintf($desc_timeline_partnership, $_link, $array_data['lang_companyname'], $lang, $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2, $nv_Lang->getModule('ldc'), $result['bidder_name']);
                        } else {
                            $result['desc_timeline'] = sprintf($desc_timeline_partnership, $_link, $array_data['lang_companyname'], $lang, $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2, $nv_Lang->getModule('ldp'), $result['bidder_name']);
                        }
                    } else {
                        if ($result['joint_venture'] == '') {
                            $result['desc_timeline'] = sprintf($desc_timeline, $_link, $array_data['lang_companyname'], $nv_Lang->getModule('st'), $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2);
                        } else {
                            $result['desc_timeline'] = sprintf($desc_timeline_partnership, $_link, $array_data['lang_companyname'], $nv_Lang->getModule('st'), $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2, $nv_Lang->getModule('tv'), $result['joint_venture']);
                        }
                        $result['trung_thau_lang'] = $nv_Lang->getModule('result' . $result['array_pq'] . '_pq');
                        $result['link_result'] = $result['link'];
                    }
                    if (!empty($result['count_ld'])) {
                        $result['desc_timeline'] .= sprintf($nv_Lang->getModule('desc_timeline_count_ld'), $result['count_ld']);
                    }
                    $result['kq'] = sprintf($nv_Lang->getModule('kq'), $result['link_result'], $result['trung_thau_lang']);

                    if ($result['finish_time'] > 0) {
                        $result['finish_time'] = date("d/m/Y H:i", $result['finish_time']);
                    } else {
                        $result['finish_time'] = '';
                    }
                    $xtpl->assign('RESULT', $result);
                    if ($result['trung_thau'] != 0) {
                        $xtpl->parse('main.timeline.show.loop.result1');
                    } else {
                        $xtpl->parse('main.timeline.show.loop.result0');
                    }

                    $xtpl->parse('main.timeline.show.loop');
                }
                if (defined('NV_IS_VIP')) {
                    $link = sprintf($nv_Lang->getModule('view_result_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=viewtimeline/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
                    $xtpl->assign('timeline_linkview', $link);
                } else if (defined('NV_IS_USER')) {
                    $xtpl->assign('timeline_linkview', sprintf($nv_Lang->getModule('timeline_view_result_user'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3'));
                } else {
                    $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                    $pop_up = sprintf($nv_Lang->getModule('timeline_result_client1'), $link_register);

                    $xtpl->assign('timeline_linkview', $pop_up);
                }

                $xtpl->parse('main.timeline.show');
            }
            $xtpl->parse('main.timeline');
        }
    }

    $xtpl->assign('BACKGROUND_INVESTOR', getBackgroundIsNotVip('bg_detail_businesslistings'));
    if (!empty($arr_soclocitor3) and sizeof($arr_soclocitor3) > 0 and $array_data['hide_vip3'] != 1) {
        if ($array_data['hide_info'] == 1 and !defined('NV_IS_VIP')) {
            $xtpl->assign('LINK_VIP_HIDE', $link_vip_hide);
            $xtpl->parse('main.investor.hide');
        } else {
            $j = 0;
            foreach ($arr_soclocitor3 as $soclocitor) {
                $j++;
                $soclocitor['stt'] = $j;

                $xtpl->assign('SOCLOCITOR', $soclocitor);
                $xtpl->assign('LOCK', $title_lock);
                if (defined('NV_IS_VIP')) {
                    $xtpl->parse('main.investor.show.loop.vip');
                } else if (defined('NV_IS_USER')) {
                    $xtpl->parse('main.investor.show.loop.user');
                } else {
                    $xtpl->parse('main.investor.show.loop.client');
                }

                $xtpl->parse('main.investor.show.loop');
            }
            if (defined('NV_IS_VIP')) {
                $link = sprintf($nv_Lang->getModule('view_result_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=viewinvestor/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
                $xtpl->assign('investor_linkview', $link);
                $xtpl->parse('main.investor.show.show_note');
            } else if (defined('NV_IS_USER')) {
                $xtpl->assign('investor_linkview', $link_vip);
                $xtpl->parse('main.investor.show.show_note');
            } else {
                $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                $pop_up = sprintf($nv_Lang->getModule('view_result_client1'), $link_register);
                $xtpl->assign('investor_linkview', $pop_up);
                $xtpl->parse('main.investor.show.show_note');
            }
            $xtpl->assign('NUMBER_SOCLOCITOR', sprintf($nv_Lang->getModule('number_soclocitor'), $array_data['lang_companyname'], $num_soclocitor));
            $xtpl->parse('main.investor.show');
        }

        $xtpl->parse('main.investor');
    }

    // var_dump($list_vipham);die;
    if (!empty($list_vipham)) {
        $i = 1;
        foreach ($list_vipham as $view) {

            if ($i > 1) {
                break;
            }
            $view['issued_date'] = date('d/m/Y', $view['issued_date']);
            $xtpl->assign('VIEW', $view);
            if (defined('NV_IS_VIP')) {
                $xtpl->parse('main.list_vipham.show.loop.vip');
            } else if (defined('NV_IS_USER')) {
                $xtpl->assign('user', $link_vip_table);
                $xtpl->parse('main.list_vipham.show.loop.user');
            } else {
                $xtpl->assign('client', $link_user_table);
                $xtpl->parse('main.list_vipham.show.loop.client');
            }
            $xtpl->parse('main.list_vipham.show.loop');
            $i++;
        }

        if (defined('NV_IS_VIP')) {
            //http://dauthau.vinades.my/businesslistings/viewviolators/CONG-TY-TNHH-PHAT-TAI-18625/
            $link = sprintf($nv_Lang->getModule('view_result_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=viewviolators/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
            $xtpl->assign('view_listvipham', $link);
        } else if (defined('NV_IS_USER')) {
            $xtpl->assign('view_listvipham', $link_vip);
        } else {
            $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
            $pop_up = sprintf($nv_Lang->getModule('view_result_client1'), $link_register);
            $xtpl->assign('view_listvipham', $pop_up);
        }
        $numqdxp = sizeof($list_vipham);
        $xtpl->assign('NUMBER_VIPHAM', sprintf($nv_Lang->getModule('number_listvipham'), $numqdxp, $array_data['lang_companyname']));
        $xtpl->parse('main.list_vipham.show');
        $xtpl->parse('main.list_vipham');
    }

    $xtpl->assign('BACKGROUND_BUSINESS', getBackgroundIsNotVip('bg_detail_businesslistings_business'));
    if (!empty($array_open_other3) and sizeof($array_open_other3) > 0 and $array_data['hide_vip3'] != 1) {
        if ($array_data['hide_info'] == 1 and !defined('NV_IS_VIP')) {
            $xtpl->assign('LINK_VIP_HIDE', $link_vip_hide);
            $xtpl->parse('main.business.hide');
        } else {
            $j = 0;
            foreach ($array_open_other3 as $k => $business) {
                $j++;
                $business['stt'] = $j;

                $xtpl->assign('BUSINESS', $business);
                if (!empty($k)) {
                    $xtpl->parse('main.business.show.loop.view_relative');
                }
                $xtpl->assign('LOCK', $title_lock);
                if (defined('NV_IS_VIP')) {
                    $xtpl->parse('main.business.show.loop.vip');
                } else if (defined('NV_IS_USER')) {
                    $xtpl->parse('main.business.show.loop.user');
                } else {
                    $xtpl->parse('main.business.show.loop.client');
                }

                $xtpl->parse('main.business.show.loop');
            }
            if (defined('NV_IS_VIP')) {
                $link = sprintf($nv_Lang->getModule('view_result_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=detailbusiness/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
                $xtpl->assign('business_linkview', $link);
                $xtpl->parse('main.business.show.show_note');
            } else if (defined('NV_IS_USER')) {
                $xtpl->assign('business_linkview', $link_vip);
                $xtpl->parse('main.business.show.show_note');
            } else {
                $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                $pop_up = sprintf($nv_Lang->getModule('view_result_client1'), $link_register);
                $xtpl->assign('business_linkview', $pop_up);
                $xtpl->parse('main.business.show.show_note');
            }
            $xtpl->assign('NUMBER_BUSINESS', sprintf($nv_Lang->getModule('number_business'), $array_data['lang_companyname'], $num_array_open_other, sizeof($array_open_other_sum['so_tbmt']), $array_open_other_sum['result_1'], $array_open_other_sum['result_2'], $array_open_other_sum['result_3'], $array_open_other_sum['result_4']));
            $xtpl->parse('main.business.show');
        }

        $xtpl->parse('main.business');
    }

    $xtpl->assign('BACKGROUND_PARTENRSHIP', getBackgroundIsNotVip('bg_detail_businesslistings_business'));

    // các nhà thầu cùng liên danh
    if (!empty($array_partnership3) and sizeof($array_partnership3) > 0 and $array_data['hide_vip3'] != 1) {
        if ($array_data['hide_info'] == 1 and !defined('NV_IS_VIP')) {
            $xtpl->assign('LINK_VIP_HIDE', $link_vip_hide);
            $xtpl->parse('main.partnership.hide');
        } else {
            $j = 0;
            foreach ($array_partnership3 as $partnership) {
                $j++;
                $partnership['stt'] = $j;

                $xtpl->assign('PARTNERSHIP', $partnership);
                $xtpl->assign('LOCK', $title_lock);
                if (defined('NV_IS_VIP')) {
                    $xtpl->parse('main.partnership.show.loop.vip');
                } else if (defined('NV_IS_USER')) {
                    $xtpl->parse('main.partnership.show.loop.user');
                } else {
                    $xtpl->parse('main.partnership.show.loop.client');
                }

                $xtpl->parse('main.partnership.show.loop');
            }
            if (defined('NV_IS_VIP')) {
                $link = sprintf($nv_Lang->getModule('view_result_vip'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=detailpartnership/' . change_alias($array_data['lang_companyname']) . '-' . $array_data['id']);
                $xtpl->assign('partnership_linkview', $link);
                $xtpl->parse('main.partnership.show.show_note');
            } else if (defined('NV_IS_USER')) {
                $xtpl->assign('partnership_linkview', $link_vip);
                $xtpl->parse('main.partnership.show.show_note');
            } else {
                $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                $pop_up = sprintf($nv_Lang->getModule('view_result_client1'), $link_register);
                $xtpl->assign('partnership_linkview', $pop_up);
                $xtpl->parse('main.partnership.show.show_note');
            }
            $xtpl->assign('NUMBER_PARTNERSHIP', sprintf($nv_Lang->getModule('number_partnership'), $array_data['lang_companyname'], $num_array_partnership, sizeof($array_partnership_sum['so_tbmt']), $array_partnership_sum['result_1'], $array_partnership_sum['result_2'], $array_partnership_sum['result_3'], $array_partnership_sum['result_4']));
            $xtpl->parse('main.partnership.show');
        }
        $xtpl->parse('main.partnership');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_detailbusiness($array_data, $array_business, $number_result_business, $array_order, $generate_page, $page, $ord, $check_user = false, $link_register = "")
{
    global $global_config, $module_name, $module_file, $module_config, $module_info, $op, $client_info, $page_title, $title_lock, $txtSearch, $per_page, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('LINK', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=detailbusiness/' . change_alias($array_data['companyname']) . '-' . $array_data['id']);
    $xtpl->assign('PAGE', $page);
    $bgNoVip = getBackgroundIsNotVip('bg_detail_detailbusiness');
    $xtpl->assign('BACKGROUND', $bgNoVip);

    if ($check_user === false) {
        $xtpl->assign('SHOW_NOTE', $link_register);
        $xtpl->parse('main.business.show_note');
    }

    if (!empty($array_business) and sizeof($array_business) > 0) {
        $j = ($page - 1) * $per_page + 1;
        foreach ($array_business as $k => $business) {
            $business['stt'] = $j++;
            $business['static'] = sprintf($nv_Lang->getModule('number_business1'), $array_data['companyname'], (isset($business['ten_nha_thau']) ? $business['ten_nha_thau'] : ""), $business['total'], $business['result_1'], $business['result_2'], $business['result_3'], $business['result_4']);
            $business['static'] = htmlentities($business['static']);
            $xtpl->assign('BUSINESS', $business);
            if (!empty($k)) {
                $xtpl->parse('main.business.loop.view_relative');
            }
            // Kiểm tra quyền xem hiển thị all hay 1 phần
            if ($check_user === false) {
                $xtpl->assign("LOCK", $title_lock);
                $xtpl->parse('main.business.loop.show_one');
            } else {
                $xtpl->parse('main.business.loop.show_all');
            }

            if (defined('NV_IS_VIP')) {
                $xtpl->parse('main.business.loop.vip');
            } else if (defined('NV_IS_USER')) {
                $link = sprintf($nv_Lang->getModule('view_result_user_table'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
                $xtpl->assign('user', $link);
                $xtpl->parse('main.business.loop.user');
            } else {
                $link_login = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                $link = vsprintf($nv_Lang->getModule('view_result_client_table'), array(
                    $link_login,
                    $link_register
                ));
                $xtpl->assign('client', $link);
                $xtpl->parse('main.business.loop.client');
            }
            $xtpl->parse('main.business.loop');
        }

        if ($check_user) {
            foreach ($array_order as $key => $value) {
                $order = array(
                    'key' => $key,
                    'value' => $value,
                    'selected' => ($key == $ord) ? 'selected="selected"' : ''
                );
                $xtpl->assign('ORDER', $order);
                $xtpl->parse('main.business.show_order.order');
            }
            $xtpl->assign('TXT_SEARCH', $txtSearch);
            $xtpl->parse('main.business.show_order');
        }
        $xtpl->assign('NUMBER_BUSINESS', sprintf($nv_Lang->getModule('number_business'), $array_data['companyname'], $array_data['num_items'], sizeof($number_result_business['so_tbmt']), $number_result_business['result_1'], $number_result_business['result_2'], $number_result_business['result_3'], $number_result_business['result_4']));
        if (!empty($generate_page) && $check_user) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.business.generate_page');
        }

        $xtpl->parse('main.business');
    }
    if (!(defined('NV_IS_USER') or $client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
        global $page_url;
        $link__popup = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
        // Gọi hàm Popuplogin và truyền tham số link vào
        $content_popup = PopupLogin($link__popup, nv_url_rewrite($page_url, true));
        $xtpl->assign('POPUP_LOGIN', $content_popup);
        $xtpl->parse('main.popup_login');
    }
    $link_detail = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($array_data['companyname']) . '-' . $array_data['id'], true);
    // $xtpl->assign('TITLE', sprintf($nv_Lang->getModule('title_detail_viewinvestor'), $link_detail, $array_data['companyname'], $array_business[1]['link'], $array_business[1]['ten_nha_thau']));

    $xtpl->assign('TITLE', $page_title);

    $xtpl->assign('ROW', $array_data);

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_ajax_businesslistings_detailbusiness($array_data, $array_business, $generate_page)
{
    global $global_config, $module_name, $module_file, $module_info, $op, $page, $per_page, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);

    if (!empty($array_business) and sizeof($array_business) > 0) {
        $i = ($page - 1) * $per_page;
        foreach ($array_business as $result) {
            $i++;
            $result['stt'] = $i;
            $resultsiness['static'] = sprintf($nv_Lang->getModule('number_business1'), $array_data['companyname'], $result['ten_nha_thau'], $result['total'], $result['result_1'], $result['result_2'], $result['result_3'], $result['result_4']);
            $xtpl->assign('BUSINESS', $result);
            $xtpl->parse('ajax.loop');
        }
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('ajax.generate_page');
        }
    } else {
        $xtpl->parse('ajax.nodata');
    }

    $xtpl->parse('ajax');
    return $xtpl->text('ajax');
}

function nv_theme_businesslistings_viewinvestor($array_data, $arr_soclocitor, $array_order, $ord, $generate_page, $page, $per_page, $check_user = false, $link_register = "")
{
    global $global_config, $module_name, $module_file, $module_info, $op, $client_info, $page_title, $title_lock, $nv_Lang;
    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('PAGE', $page);
    $xtpl->assign('LINK', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($array_data['companyname']) . '-' . $array_data['id']);

    // Hiển thị đoạn text Bảng dữ liệu được ẩn đi 1 phần....
    $bgNoVip = getBackgroundIsNotVip('bg_list_viewinvestor');
    $xtpl->assign('BACKGROUND', $bgNoVip);

    if ($check_user === false) {
        $xtpl->assign('SHOW_NOTE', $link_register);
        $xtpl->parse('main.investor.show_note');
    }

    $soclocitor = [];
    $soclocitor['title'] = "";
    $soclocitor['link'] = "";
    $num_solicitor = sizeof($arr_soclocitor);
    $arr_soclocitor = array_slice($arr_soclocitor, ($page - 1) * $per_page, $per_page, true);
    if (!empty($arr_soclocitor) and sizeof($arr_soclocitor) > 0) {
        $i = ($page - 1) * $per_page;
        foreach ($arr_soclocitor as $result) {
            $i++;
            $result['stt'] = $i;
            $xtpl->assign('RESULT', $result);
            if ($check_user === false) {
                $xtpl->assign("LOCK", $title_lock);
                $xtpl->parse('main.investor.loop.show_one');
            } else {
                $xtpl->parse('main.investor.loop.show_all');
            }

            $soclocitor['title'] = (isset($result['title']) ? $result['title'] : "");
            $soclocitor['link'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=procuring-entity&amp;' . NV_OP_VARIABLE . '=' . (isset($result['alias']) ? $result['alias'] : "") . '-' . (isset($result['id']) ? $result['id'] : ""), true);
            $xtpl->parse('main.investor.loop');
            if (!$check_user && $i == 3) {
                break;
            }
        }

        if ($check_user) {
            foreach ($array_order as $key => $value) {
                $order = array(
                    'key' => $key,
                    'value' => $value,
                    'selected' => ($key == $ord) ? 'selected="selected"' : ''
                );
                $xtpl->assign('ORDER', $order);
                $xtpl->parse('main.investor.show_order.order');
            }
            $xtpl->parse('main.investor.show_order');
        } else {
            $generate_page = '';
        }
        $xtpl->assign('NUMBER_SOCLOCITOR', sprintf($nv_Lang->getModule('number_soclocitor'), $array_data['companyname'], $num_solicitor));
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.investor.generate_page');
        }
        $xtpl->parse('main.investor');
    }
    // $link_detail = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($array_data['companyname']) . '-' . $array_data['id'], true);
    // $xtpl->assign('TITLE', sprintf($nv_Lang->getModule('title_detail_viewinvestor'), $link_detail, $array_data['companyname'], $soclocitor['link'], $soclocitor['title']));
    $xtpl->assign('TITLE', $page_title);
    $xtpl->assign('ROW', $array_data);
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_ajax_viewinvestor($array_data, $arr_soclocitor, $array_order, $generate_page, $page, $per_page)
{
    global $global_config, $module_name, $module_file, $module_info, $op, $client_info, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $arr_soclocitor = array_slice($arr_soclocitor, ($page - 1) * $per_page, $per_page, true);
    if (!empty($arr_soclocitor) and sizeof($arr_soclocitor) > 0) {
        $i = ($page - 1) * $per_page;
        foreach ($arr_soclocitor as $result) {
            $i++;
            $result['stt'] = $i;

            $xtpl->assign('RESULT', $result);
            $xtpl->parse('ajax.loop');
        }
    }

    $xtpl->assign('ROW', $array_data);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('ajax.generate_page');
    }
    $xtpl->parse('ajax');
    return $xtpl->text('ajax');
}

function nv_theme_businesslistings_resultdetail($array_data, $arr_config, $array_bidding, $number_result, $array_order, $generate_page, $page, $per_page, $ord, $prov, $array_prov_all, $package, $check_user = false, $link = "", $chart_linkview = '')
{
    global $global_config, $module_name, $module_file, $module_config, $module_info, $op, $client_info, $page_title, $province_list, $page_url, $title_lock, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);

    $xtpl->assign('CONFIG', $arr_config);
    $xtpl->assign('LINK', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=resultdetail/' . change_alias($array_data['companyname']) . '-' . $array_data['id']);
    $xtpl->assign('PAGE', $page);
    $xtpl->assign('PAGE_URL', nv_redirect_encrypt(urlRewriteWithDomain($page_url, NV_MY_DOMAIN)));

    $array_package = array();
    $array_package[0] = $nv_Lang->getModule('all_packages');
    $array_package[1] = $nv_Lang->getModule('pg_appointment_contractors');
    $array_package[2] = $nv_Lang->getModule('pg_with_kqlcnt_without_tbmt');
    $bgNoVip = getBackgroundIsNotVip('bg_list_resultdetail');
    $xtpl->assign('BACKGROUND', $bgNoVip);

    // Hiển thị đoạn text Bảng dữ liệu được ẩn đi 1 phần....
    if ($check_user === false) {
        $xtpl->assign('SHOW_NOTE', $link);
        $xtpl->parse('main.bidding.show_note');
        $xtpl->assign('chart_linkview', $chart_linkview);
    }

    if (!empty($array_bidding) and sizeof($array_bidding) > 0) {
        $stt = ($page - 1) * $per_page;
        foreach ($array_bidding as $k => $result) {
            $stt++;
            $result['stt'] = $stt;

            if ($result['trung_thau'] == 1) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result1');
            } else if ($result['trung_thau'] == 2) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result2');
            } elseif ($result['trung_thau'] == 3) {
                $result['trung_thau_lang'] = $result['type'] == 'pq' ? $nv_Lang->getModule('result2_pq') : $nv_Lang->getModule('result3');
            } else {
                $result['trung_thau_lang'] = $nv_Lang->getModule('label_cancel');
            }

            if ($result['partnership'] == 1) {
                $result['partnership'] = $nv_Lang->getModule('vaitro1');
            } else if ($result['partnership'] == 2) {
                $result['partnership'] = $nv_Lang->getModule('vaitro2');
            } else {
                $result['partnership'] = $nv_Lang->getModule('vaitro3');
            }

            // if ($result['win_price_number'] > 0) {
            // $result['win_price_number'] = number_format($result['win_price_number']) . ' VNĐ';
            // }

            if ($result['finish_time'] > 0) {
                $number = substr($result['finish_time'], 8, 2);
                if ($number != '00') {
                    $result['finish_time'] = date("d/m/Y H:i", $result['finish_time']);
                } else {
                    $result['finish_time'] = date("d/m/Y", $result['finish_time']);
                }
            } else {
                $result['finish_time'] = '';
            }

            $xtpl->assign('RESULT', $result);
            if ($result['trung_thau'] != 0) {
                $xtpl->parse('main.bidding.loop.show_all.result1');
            } else {
                $xtpl->parse('main.bidding.loop.show_all.result0');
            }

            // Kiểm tra quyền xem hiển thị all hay 1 phần
            if ($check_user === false) {
                $xtpl->assign("LOCK", $title_lock);
                $xtpl->parse('main.bidding.loop.show_one');
            } else {
                $xtpl->parse('main.bidding.loop.show_all');
            }

            $xtpl->parse('main.bidding.loop');
        }

        if ($prov == -1) {
            $at_prov = $nv_Lang->getModule('on_all_nation');
        } elseif ($prov == 0) {
            $at_prov = sprintf($nv_Lang->getModule('at'), $nv_Lang->getModule('province') . ' ' . $province_list[$prov]['title']);
        } else {
            $at_prov = sprintf($nv_Lang->getModule('at'), $province_list[$prov]['title']);
        }

        $package_text = '';
        if ($package == 2) {
            $package_text = $nv_Lang->getModule('array_type_lc_14') . ' ';
        } elseif ($package == 3) {
            $package_text = $nv_Lang->getModule('pg_with_kqlcnt_without_tbmt_1') . ' ';
        }

        $xtpl->assign('NUMBER_RESULT', sprintf($nv_Lang->getModule('number_result_prov'), $array_data['companyname'], $number_result['total'], $package_text, $at_prov, $number_result['result1'], $number_result['result2'], $number_result['result3'], $number_result['result4']));

        // Kiểm tra nếu khách đã đăng nhập thì hiển thị còn k ẩn hết
        if ($check_user) {
            foreach ($array_order as $key => $value) {
                $order = array(
                    'key' => $key,
                    'value' => $value,
                    'selected' => ($key == $ord) ? 'selected="selected"' : ''
                );
                $xtpl->assign('ORDER', $order);
                $xtpl->parse('main.bidding.show_order.order');
            }
            ksort($province_list);
            foreach ($province_list as $p) {
                if (in_array($p['id'], $array_prov_all)) {
                    $xtpl->assign('PROV', [
                        'key' => $p['id'],
                        'selected' => $prov == $p['id'] ? 'selected="selected"' : '',
                        'value' => $p['title']
                    ]);
                    $xtpl->parse('main.bidding.show_order.province');
                }
            }
            foreach ($array_package as $key => $value) {
                $package_sl = array(
                    'key' => $key + 1,
                    'value' => $value,
                    'selected' => ($key + 1 == $package) ? 'selected="selected"' : ''
                );
                $xtpl->assign('PACKAGE', $package_sl);
                $xtpl->parse('main.bidding.show_order.package');
            }
            $xtpl->parse('main.bidding.show_order');
        }

        if (defined('NV_IS_ANY_VIP')) {
            $xtpl->assign('CHECK_USER', 'true');
        } else {
            $xtpl->assign('CHECK_USER', 'false');
        }

        // Kiểm tra nếu khách đã đăng nhập thì hiển thị còn k ẩn hết
        if ($check_user) {
            if (!empty($generate_page)) {
                $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
                $xtpl->parse('main.bidding.generate_page');
            }
        }

        $data_desire = $data_real = $data_inde = $data_desire_cdt = $data_real_cdt = $data_inde_cdt = $data_desire_not_tbmt = $data_real_not_tbmt = $data_inde_not_tbmt = '[';
        $data_desire_full = $data_real_full = $data_inde_full = $data_desire_cdt_full = $data_real_cdt_full = $data_inde_cdt_full = $data_desire_not_tbmt_full = $data_real_not_tbmt_full = $data_inde_not_tbmt_full = '[';
        $year = date('Y', NV_CURRENTTIME) - 7; // Chỉ hiển thị 7 năm

        foreach ($array_data['value_chart_desire'] as $value_desire) {
            $data_desire_full .= '[' . $value_desire[0] . ',' . $value_desire[1] . '],';
            if (date('Y', $value_desire[0] / 1000) >= $year) {
                $data_desire .= '[' . $value_desire[0] . ',' . $value_desire[1] . '],';
            }
        }
        $data_desire .= ']';
        $data_desire_full .= ']';

        foreach ($array_data['value_chart_real'] as $value_real) {
            $data_real_full .= '[' . $value_real[0] . ',' . $value_real[1] . '],';
            if (date('Y', $value_real[0] / 1000) >= $year) {
                $data_real .= '[' . $value_real[0] . ',' . $value_real[1] . '],';
            }
        }
        $data_real .= ']';
        $data_real_full .= ']';

        foreach ($array_data['value_chart_inde'] as $value_inde) {
            $data_inde_full .= '[' . $value_inde[0] . ',' . $value_inde[1] . '],';
            if (date('Y', $value_inde[0] / 1000) >= $year) {
                $data_inde .= '[' . $value_inde[0] . ',' . $value_inde[1] . '],';
            }
        }
        $data_inde .= ']';
        $data_inde_full .= ']';

        foreach ($array_data['value_chart_desire_cdt'] as $value_desire_cdt) {
            $data_desire_cdt_full .= '[' . $value_desire_cdt[0] . ',' . $value_desire_cdt[1] . '],';
            if (date('Y', $value_desire_cdt[0] / 1000) >= $year) {
                $data_desire_cdt .= '[' . $value_desire_cdt[0] . ',' . $value_desire_cdt[1] . '],';
            }
        }
        $data_desire_cdt .= ']';
        $data_desire_cdt_full .= ']';

        foreach ($array_data['value_chart_real_cdt'] as $value_real_cdt) {
            $data_real_cdt_full .= '[' . $value_real_cdt[0] . ',' . $value_real_cdt[1] . '],';
            if (date('Y', $value_real_cdt[0] / 1000) >= $year) {
                $data_real_cdt .= '[' . $value_real_cdt[0] . ',' . $value_real_cdt[1] . '],';
            }
        }
        $data_real_cdt .= ']';
        $data_real_cdt_full .= ']';

        foreach ($array_data['value_chart_inde_cdt'] as $value_inde_cdt) {
            $data_inde_cdt_full .= '[' . $value_inde_cdt[0] . ',' . $value_inde_cdt[1] . '],';
            if (date('Y', $value_inde_cdt[0] / 1000) >= $year) {
                $data_inde_cdt .= '[' . $value_inde_cdt[0] . ',' . $value_inde_cdt[1] . '],';
            }
        }
        $data_inde_cdt .= ']';
        $data_inde_cdt_full .= ']';

        foreach ($array_data['value_chart_desire_not_tbmt'] as $value_desire_2) {
            $data_desire_not_tbmt_full .= '[' . $value_desire_2[0] . ',' . $value_desire_2[1] . '],';
            if (date('Y', $value_desire_2[0] / 1000) >= $year) {
                $data_desire_not_tbmt .= '[' . $value_desire_2[0] . ',' . $value_desire_2[1] . '],';
            }
        }
        $data_desire_not_tbmt .= ']';
        $data_desire_not_tbmt_full .= ']';

        foreach ($array_data['value_chart_real_not_tbmt'] as $value_real_2) {
            $data_real_not_tbmt_full .= '[' . $value_real_2[0] . ',' . $value_real_2[1] . '],';
            if (date('Y', $value_real_2[0] / 1000) >= $year) {
                $data_real_not_tbmt .= '[' . $value_real_2[0] . ',' . $value_real_2[1] . '],';
            }
        }
        $data_real_not_tbmt .= ']';
        $data_real_not_tbmt_full .= ']';

        foreach ($array_data['value_chart_inde_not_tbmt'] as $value_inde_not_tbmt) {
            $data_inde_not_tbmt_full .= '[' . $value_inde_not_tbmt[0] . ',' . $value_inde_not_tbmt[1] . '],';
            if (date('Y', $value_inde_not_tbmt[0] / 1000) >= $year) {
                $data_inde_not_tbmt .= '[' . $value_inde_not_tbmt[0] . ',' . $value_inde_not_tbmt[1] . '],';
            }
        }
        $data_inde_not_tbmt .= ']';
        $data_inde_not_tbmt_full .= ']';

        $xtpl->assign('DATA_DESIRE', $data_desire);
        $xtpl->assign('DATA_REAL', $data_real);
        $xtpl->assign('DATA_INDE', $data_inde);
        $xtpl->assign('DATA_DESIRE_CDT', $data_desire_cdt);
        $xtpl->assign('DATA_REAL_CDT', $data_real_cdt);
        $xtpl->assign('DATA_INDE_CDT', $data_inde_cdt);
        $xtpl->assign('DATA_DESIRE_NOT_TBMT', $data_desire_not_tbmt);
        $xtpl->assign('DATA_REAL_NOT_TBMT', $data_real_not_tbmt);
        $xtpl->assign('DATA_INDE_NOT_TBMT', $data_inde_not_tbmt);

        $xtpl->assign('DATA_DESIRE_FULL', $data_desire_full);
        $xtpl->assign('DATA_REAL_FULL', $data_real_full);
        $xtpl->assign('DATA_INDE_FULL', $data_inde_full);
        $xtpl->assign('DATA_DESIRE_CDT_FULL', $data_desire_cdt_full);
        $xtpl->assign('DATA_REAL_CDT_FULL', $data_real_cdt_full);
        $xtpl->assign('DATA_INDE_CDT_FULL', $data_inde_cdt_full);
        $xtpl->assign('DATA_DESIRE_NOT_TBMT_FULL', $data_desire_not_tbmt_full);
        $xtpl->assign('DATA_REAL_NOT_TBMT_FULL', $data_real_not_tbmt_full);
        $xtpl->assign('DATA_INDE_NOT_TBMT_FULL', $data_inde_not_tbmt_full);
        $xtpl->assign('LINK_JS', '/themes/' . $module_info['template'] . '/');
        $xtpl->parse('main.bidding.value_chart');

        $xtpl->parse('main.bidding');
    }

    $xtpl->assign('TITLE', $nv_Lang->getModule('listresult'));
    $xtpl->assign('ROW', $array_data);

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_ajax_resultdetail($array_data, $arr_config, $array_bidding, $number_result, $array_order, $page, $per_page, $generate_page)
{
    global $global_config, $module_name, $module_file, $module_config, $module_info, $op, $client_info, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);

    $xtpl->assign('CONFIG', $arr_config);
    $xtpl->assign('LINK', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=resultdetail/' . change_alias($array_data['companyname']) . '-' . $array_data['id']);
    $xtpl->assign('PAGE', $page);

    if (!empty($array_bidding) and sizeof($array_bidding) > 0) {
        $stt = ($page - 1) * $per_page;
        foreach ($array_bidding as $k => $result) {
            $stt++;
            $result['stt'] = $stt;

            if ($result['trung_thau'] == 1) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result1');
            } else if ($result['trung_thau'] == 2) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result2');
            } else if ($result['trung_thau'] == 3) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result3');
            } else {
                $result['trung_thau_lang'] = $nv_Lang->getModule('label_cancel');
            }

            if ($result['partnership'] == 1) {
                $result['partnership'] = $nv_Lang->getModule('vaitro1');
            } else if ($result['partnership'] == 2) {
                $result['partnership'] = $nv_Lang->getModule('vaitro2');
            } else {
                $result['partnership'] = $nv_Lang->getModule('vaitro3');
            }

            // if ($result['win_price'] > 0) {
            // $result['win_price'] = number_format($result['win_price']) . ' VNĐ';
            // }

            if ($result['finish_time'] > 0) {
                $number = substr($result['finish_time'], 8, 2);
                if ($number != '00') {
                    $result['finish_time'] = date("d/m/Y H:i", $result['finish_time']);
                } else {
                    $result['finish_time'] = date("d/m/Y", $result['finish_time']);
                }
            } else {
                $result['finish_time'] = '';
            }

            $xtpl->assign('RESULT', $result);
            if ($result['trung_thau'] != 0) {
                $xtpl->parse('ajax.loop.result1');
            } else {
                $xtpl->parse('ajax.loop.result0');
            }

            $xtpl->parse('ajax.loop');
        }
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('ajax.generate_page');
        }
    }

    $xtpl->assign('ROW', $array_data);

    $xtpl->parse('ajax');
    return $xtpl->text('ajax');
}

function nv_theme_businesslistings_detailinvestor($array_data, $solicitor, $array_bidding, $number_result, $generate_page, $check_user = false, $link = "", $no_name = false, $unlink_link = '')
{
    global $global_config, $module_name, $module_file, $module_config, $module_info, $op, $client_info, $page_title, $page_url, $nv_Lang, $site_mods;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('PAGE_URL', nv_redirect_encrypt(urlRewriteWithDomain($page_url, NV_MY_DOMAIN)));

    // Hiển thị đoạn text Bảng dữ liệu được ẩn đi 1 phần....
    if ($check_user === false) {
        $xtpl->assign('SHOW_NOTE', $link);
        $xtpl->parse('main.bidding.show_note');
    }

    if (!empty($array_bidding) and sizeof($array_bidding) > 0) {
        $i = 0;
        foreach ($array_bidding as $result) {
            $i++;
            $result['stt'] = $i;

            if ($result['trung_thau'] == 1) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result1');
            } else if ($result['trung_thau'] == 2) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result2');
            } elseif ($result['trung_thau'] == 3) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result3');
            } else {
                $result['trung_thau_lang'] = $nv_Lang->getModule('label_cancel');
            }

            $result['finish_time'] = date("d/m/Y", $result['finish_time']);
            $result['vaitro'] = $nv_Lang->getModule('vaitro' . $result['partnership']);
            $xtpl->assign('RESULT', $result);

            if ($result['trung_thau'] != 3) {
                $xtpl->parse('main.bidding.loop.result1');
            } else {
                $xtpl->parse('main.bidding.loop.result0');
            }

            if ($result['link'] != '') {
                $xtpl->parse('main.bidding.loop.show_link');
            } else {
                $xtpl->parse('main.bidding.loop.show_text');
            }

            $xtpl->parse('main.bidding.loop');

            if ($i == 3 and !defined('NV_IS_VIP'))
                break;
        }

        if (defined('NV_IS_USER') and !defined('NV_IS_VIP') and $i > 3) {
            $link = sprintf($nv_Lang->getModule('view_result_user'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
            $xtpl->assign('user', $link);
            $xtpl->parse('main.bidding.user');
        } else if (!defined('NV_IS_USER') and $i > 3) {
            $link_login = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
            $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
            $link = vsprintf($nv_Lang->getModule('view_result_client'), array(
                $link_login,
                $link_register
            ));
            $xtpl->assign('client', $link);
            $xtpl->parse('main.bidding.client');
        }
        if ($no_name) {
            $xtpl->assign('NUMBER_RESULT', sprintf($nv_Lang->getModule('number_detailinvestor_noname'), $number_result['total'], $solicitor['title'], $number_result['result1'], $number_result['result2'], $number_result['result3'], $number_result['result4']));
        } else {
            $xtpl->assign('NUMBER_RESULT', sprintf($nv_Lang->getModule('number_detailinvestor'), $array_data['companyname'], $number_result['total'], $solicitor['title'], $number_result['result1'], $number_result['result2'], $number_result['result3'], $number_result['result4']));
        }
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.bidding.generate_page');
        }
        $xtpl->parse('main.bidding');
    } else {
        $xtpl->parse('main.empty_data');
    }

    $link = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=bidding&amp;" . NV_OP_VARIABLE . "=" . $site_mods['bidding']['alias']['solicitor'] . '/' . $unlink_link . $solicitor['alias'] . '-' . $solicitor['id'], true);
    $page_title = sprintf($nv_Lang->getModule('title_tab'), ($no_name ? $nv_Lang->getModule('no_name') : $array_data['companyname']), $solicitor['title']);
    if ($no_name) {
        $xtpl->assign('TITLE', sprintf($nv_Lang->getModule('title_detail_viewinvestor_noname'), $link, $solicitor['title']));
    } else {
        $link_detail = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($array_data['companyname']) . '-' . $array_data['id'], true);
        $xtpl->assign('TITLE', sprintf($nv_Lang->getModule('title_detail_viewinvestor'), $link_detail, $array_data['companyname'], $link, $solicitor['title']));
    }
    $xtpl->assign('ROW', $array_data);

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_viewrelative($row, $business, $array_bidding_open, $number_result, $generate_page, $check_user = false, $link = "")
{
    global $global_config, $module_name, $module_file, $module_config, $module_info, $op, $client_info, $page_title, $nv_Lang;
    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);

    $xtpl->assign('BUSINESS', $business);
    $xtpl->assign('ROW', $row);

    // Hiển thị đoạn text Bảng dữ liệu được ẩn đi 1 phần....
    if ($check_user === false) {
        $xtpl->assign('SHOW_NOTE', $link);
        $xtpl->parse('main.bidding.show_note');
    }

    if (!empty($array_bidding_open) and sizeof($array_bidding_open) > 0) {
        foreach ($array_bidding_open as $i => $result) {
            $result['stt'] = $i;
            if ($result['result1'] == 1 && $result['result2'] == 1) {
                $result['result1_lang'] = $result['result2_lang'] = $nv_Lang->getModule('result1_partner');
            } else {
                if ($result['result1'] == 1) {
                    $result['result1_lang'] = $nv_Lang->getModule('result1');
                } else if ($result['result1'] == 2) {
                    $result['result1_lang'] = $nv_Lang->getModule('result2');
                } elseif ($result['result1'] == 3) {
                    $result['result1_lang'] = $nv_Lang->getModule('result3');
                } else {
                    $result['result1_lang'] = $nv_Lang->getModule('label_cancel');
                }
                if ($result['result2'] == 1) {
                    $result['result2_lang'] = $nv_Lang->getModule('result1');
                } else if ($result['result2'] == 2) {
                    $result['result2_lang'] = $nv_Lang->getModule('result2');
                } elseif ($result['result2'] == 3) {
                    $result['result2_lang'] = $nv_Lang->getModule('result3');
                } else {
                    $result['result2_lang'] = $nv_Lang->getModule('label_cancel');
                }
            }

            $result['finish_time'] = ($result['finish_time'] > 0) ? date("d/m/Y", $result['finish_time']) : '';

            $xtpl->assign('RESULT', $result);

            if ($result['result1'] != 3) {
                $xtpl->parse('main.bidding.loop.result1');
            } else {
                $xtpl->parse('main.bidding.loop.result0');
            }

            $xtpl->parse('main.bidding.loop');
        }

        if (defined('NV_IS_USER') and !defined('NV_IS_VIP') and $number_result['total'] > 3) {
            $link = sprintf($nv_Lang->getModule('view_result_user'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
            $xtpl->assign('user', $link);
            $xtpl->parse('main.bidding.user');
        } else if (!defined('NV_IS_USER') and $i > 3) {
            $link_login = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
            $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
            $link = vsprintf($nv_Lang->getModule('view_result_client'), array(
                $link_login,
                $link_register
            ));
            $xtpl->assign('client', $link);
            $xtpl->parse('main.bidding.client');
        }
        $xtpl->assign('NUMBER_RESULT', sprintf($nv_Lang->getModule('number_viewrelative'), $row['companyname'], $business['companyname'], $number_result['total'], $number_result['result1'], $number_result['result2'], $number_result['result3'], $number_result['result4']));
        if (!empty($generate_page) && $check_user === true) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.bidding.generate_page');
        }
        $xtpl->parse('main.bidding');
    }
    $page_title = sprintf($nv_Lang->getModule('title_tab'), $row['companyname'], $business['companyname']);

    $link_detail = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($row['companyname']) . '-' . $row['id'], true);
    $link = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($business['companyname']) . '-' . $business['id'], true);
    $xtpl->assign('TITLE', sprintf($nv_Lang->getModule('title_detail_viewrelative'), $link_detail, $row['companyname'], $link, $business['companyname']));

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_listpost($array_data, $generate_page)
{
    global $global_config, $module_name, $module_file, $module_config, $module_info, $op, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    foreach ($array_data as $array_data_i) {
        $xtpl->assign('ROW', $array_data_i);
        $xtpl->parse('main.loop');
    }

    if ($generate_page != "") {
        $xtpl->assign('PAGE', $generate_page);
        $xtpl->parse('main.page');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_listfavoritecat($array_data, $generate_page)
{
    global $global_config, $module_name, $module_file, $module_config, $module_info, $op, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('TEMPLATE', $module_info['template']);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('module', $module_file);

    foreach ($array_data as $array_data_i) {
        $xtpl->assign('ROW', $array_data_i);
        $xtpl->parse('main.loop');
    }

    if ($generate_page != "") {
        $xtpl->assign('PAGE', $generate_page);
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_listfavorite($array_data, $generate_page)
{
    global $global_config, $module_name, $module_file, $module_config, $module_info, $op, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    foreach ($array_data as $array_data_i) {
        $xtpl->assign('ROW', $array_data_i);
        $xtpl->parse('main.loop');
    }

    if ($generate_page != "") {
        $xtpl->assign('PAGE', $generate_page);
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_post($array_data, $edit)
{
    global $global_config, $module_name, $module_captcha, $my_head, $array_config, $module_file, $module_config, $module_info, $op, $nv_Lang;
    global $array_field_key, $global_array_config;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('OP', $op);
    $xtpl->assign('edit', $edit);
    $xtpl->assign('url', NV_BASE_ADMINURL);
    $xtpl->assign('nv', NV_NAME_VARIABLE);
    $xtpl->assign('path', NV_UPLOADS_DIR . '/' . $module_name);
    $xtpl->assign('curentpath', NV_UPLOADS_DIR . '/' . $module_name);

    foreach ($array_field_key as $field) {
        $xtpl->assign(strtoupper('CSS_' . $field), empty($global_array_config['dis']['ad'][$field]) ? ' style="display:none;"' : '');
        $xtpl->assign(strtoupper('REQ_' . $field), !empty($global_array_config['req']['ad'][$field]) ? '<span style="color:#f00">(*)</span>' : '');
    }

    $industry = $array_data['industry'];
    foreach ($industry as $industry_i) {
        $xtpl->assign('sl_industry', $industry_i['sl']);
        $xtpl->assign('key_industry', $industry_i['code']);
        $xtpl->assign('val_industry', $industry_i['title']);
        $xtpl->parse('main.industry');
    }

    $businesstype = $array_data['businesstype'];
    foreach ($businesstype as $businesstype_i) {
        $xtpl->assign('sl_businesstype', $businesstype_i['sl']);
        $xtpl->assign('key_businesstype', $businesstype_i['id']);
        $xtpl->assign('val_businesstype', $businesstype_i['title']);
        $xtpl->parse('main.businesstype');
    }

    $currentstatus = $array_data['currentstatus'];
    foreach ($currentstatus as $currentstatus_i) {

        $xtpl->assign('sl_currentstatus', $currentstatus_i['sl']);
        $xtpl->assign('key_currentstatus', $currentstatus_i['id']);
        $xtpl->assign('val_currentstatus', $currentstatus_i['title']);
        $xtpl->parse('main.currentstatus');
    }
    $province = $array_data['province'];
    foreach ($province as $province_i) {
        $xtpl->assign('sl_province', $province_i['sl']);
        $xtpl->assign('key_province', $province_i['id']);
        $xtpl->assign('val_province', $province_i['title']);
        $xtpl->parse('main.province');
    }
    if ($array_data['error'] != "") {
        $xtpl->assign('ERROR', $array_data['error']);
        $xtpl->parse('main.error');
    }

    // Nếu dùng reCaptcha v3
    if ($module_captcha == 'recaptcha' and $global_config['recaptcha_ver'] == 3) {
        $xtpl->parse('main.recaptcha3');
    } // Nếu dùng reCaptcha v2
    elseif ($module_captcha == 'recaptcha' and $global_config['recaptcha_ver'] == 2) {
        $xtpl->parse('main.recaptcha');
    } elseif ($module_captcha == 'captcha') {
        $xtpl->parse('main.captcha');
    }

    $xtpl->assign('CONFIG', $array_data['config']);
    if ($array_data['row']['logo'] != "") {
        $xtpl->assign('logo', $array_data['row']['logo']);
        $xtpl->parse('main.img');
    }
    $xtpl->assign('ROW', $array_data['row']);
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_listbusinesslistings($array_data, $title, $pages_businesslistings, $all_page, $num_items, $total_point_download = 0, $array_cat_favorite = "", $keyword = "", $count_item = 0, $pop_up = '')
{
    global $global_array_config;
    // Hiển thị danh sách kiểu cổ điển
    if (empty($global_array_config['show_list_type'])) {
        return nv_theme_businesslistings_list0($array_data, $title, $pages_businesslistings, $all_page, $num_items, $keyword, $count_item, $total_point_download, $pop_up, $array_cat_favorite);
    }
    return nv_theme_businesslistings_list1($array_data, $title, $pages_businesslistings, $array_cat_favorite, $all_page, $num_items, $keyword, $count_item, $pop_up);
}

function nv_theme_businesslistings_list0($array_data, $title, $pages_businesslistings, $all_page, $num_items, $keyword, $count_item, $total_point_download = 0, $pop_up = '', $array_cat_favorite = "")
{
    global $global_config, $module_info, $module_name, $module_file, $module_config, $module_info, $op, $client_info, $global_array_vip, $nv_Lang, $array_round_format, $arr_customs_permission_view_detail;

    global $global_array_config, $sys_mods;
    $xtpl = new XTemplate("bu_list0.tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('TEMPLATE', $module_info['template']);
    $xtpl->assign('TITLE', $title);
    $xtpl->assign('NUM_ITEMS', number_format($num_items, 0, ...$array_round_format));

    if (empty($array_data)) {
        if ($num_items > 0 && $all_page > 100) {
            if (defined('NV_IS_USER')) {
                $btn = '<div class="margin-top">
                            <a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a>
                        </div>';

                $contents_tb = nv_theme_alert($nv_Lang->getModule('notice_tb'), $nv_Lang->getModule('note_max_searchpage_x2') . $btn);
            } else {
                $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
                $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
                $contents_tb = nv_theme_alert($nv_Lang->getModule('notice_tb'), $nv_Lang->getModule('note_max_searchpage_not_user', $link_register) . $btn);
            }
            $xtpl->assign('LIMIT_PAGE', $contents_tb);
            $xtpl->parse('main.limit_page');
        } else {
            if (!empty($keyword)) {
                $xtpl->assign('NO_DATA', sprintf($nv_Lang->getModule('no_data'), $keyword, number_format($count_item, 0, ...$array_round_format)));
            } else {
                $xtpl->assign('NO_DATA', sprintf($nv_Lang->getModule('no_data_no_keyword'), number_format($count_item, 0, ...$array_round_format)));
            }

            $xtpl->parse('main.nodata');
        }
    }

    if (defined('NV_IS_USER')) {
        if (!empty($array_cat_favorite)) {
            $taomoi = $nv_Lang->getModule('hoac');
            foreach ($array_cat_favorite as $cat) {
                $xtpl->assign('CAT', $cat);
                $xtpl->parse('main.cat.loop');
            }
            $xtpl->parse('main.cat');
        } else {
            $taomoi = $nv_Lang->getModule('taomoidanhmuc');
        }
        $xtpl->assign('taomoi', $taomoi);
        $listid = "";
        // list number các gói vip
        $bidding_module_file = $sys_mods['bidding']['module_file'];
        include NV_ROOTDIR . '/modules/' . $bidding_module_file . '/viplist.php';
        $config_vip_show_new_detail = [];
        if (!empty($viplist)) {
            foreach ($viplist as $key => $value) {
                // gói VIEWEB/PRO 1: Chỉ thấy SĐT từ MSC Cũ, không thấy sdt msc mới
                if ($key == 'vip99' || $key == 'vip19') {
                    continue;
                }
                $config_vip_show_new_detail[$key] = $value['number'];
            }
        }

        // Ẩn hiện sđt, fax
        $show_old_detail = $show_new_detail = true;
        $config_vip_show_old_detail = explode(',', $global_array_config['list_vip_show_old_msc']);
        $array_keys_vip_user = [];

        if (!empty($global_array_vip) || !empty($arr_customs_permission_view_detail)) {
            // Ẩn dữ liệu nếu không có gói vip nằm trong danh sách show dữ liệu msc cũ
            // các gói vip của user
            $array_keys_vip_user = array_keys($global_array_vip);
            // các gói vip phân quyền
            $array_keys_vip_permission_view_detail = array_keys($arr_customs_permission_view_detail);
            // tất cả các gói vip của user
            $array_keys_vip_user = array_merge($array_keys_vip_user, $array_keys_vip_permission_view_detail);
            $array_keys_vip_user = array_unique($array_keys_vip_user);

            $array_vip_show_old_detail = array_intersect($array_keys_vip_user, $config_vip_show_old_detail);

            if (empty($array_vip_show_old_detail)) {
                $show_old_detail = false;
            }

            // Ẩn dữ liệu nếu không có gói vip nằm trong danh sách show dữ liệu msc mới
            $array_vip_show_new_detail = array_intersect($array_keys_vip_user, $config_vip_show_new_detail);
            if (empty($array_vip_show_new_detail)) {
                $show_new_detail = false;
            }
        } else {
            $show_old_detail = $show_new_detail = false;
        }

        // Hiển thị download dữ liệu nhà thầu
        if ($num_items > 0) {
            // nút download
            $xtpl->assign('TOTAL_POINT_DOWNLOAD', nv_number_format($total_point_download));
            $xtpl->parse('main.downloadExcel');
            // Thông báo đăng ký x2
            if (!defined('NV_IS_VIP_X2')) {
                $x2_alert = sprintf($nv_Lang->getModule('alert_reg_x2_to_decrese_point'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $sys_mods['bidding']['alias']['vip'] . '&amp;form=1&vip=89');
                $xtpl->assign('X2_ALERT', $x2_alert);
                $xtpl->parse('main.x2_alert');
            }

            // script download
            $checksess_download = md5(NV_CHECK_SESSION);
            $xtpl->assign('CHECKSESS_DOWNLOAD', $checksess_download);
            $xtpl->assign('EXPORT_LINK', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $sys_mods['businesslistings']['alias']['export']);
            $xtpl->parse('main.downloadExcelScript');
        }

        foreach ($array_data as $array_data_i) {
            $xtpl->assign('ROW', $array_data_i);
            if ($array_data_i['logo'] != "") {
                $xtpl->parse('main.loop.images');
            }
            $listid .= $array_data_i['id'] . ",";
            $xtpl->parse('main.loop.users');
            if (!empty($array_data_i['true_code'])) {
                $xtpl->parse('main.loop.is_fail');
            }
            // hiển thị dữ liệu msc cũ
            if ((defined('NV_IS_MODADMIN') || $show_old_detail) && ($array_data_i['phone'] !== '')) {
                $xtpl->parse('main.loop.phone');
                $xtpl->parse('main.loop.fax');
            }
            // hiển thị dữ liệu msc mới
            if ((defined('NV_IS_MODADMIN') || $show_new_detail) && ($array_data_i['represent_phone'] !== '')) {
                $xtpl->parse('main.loop.represent_phone');
            }
            if (trim($array_data_i['website']) !== '') {
                $xtpl->parse('main.loop.website');
            }

            $xtpl->assign('HIDDEN_ADD_CMP', $array_data_i['is_added_cmp'] ? ' hidden' : '');
            $xtpl->assign('HIDDEN_REM_CMP', $array_data_i['is_added_cmp'] ? '' : ' hidden');

            $xtpl->parse('main.loop');
        }
        $listid = substr($listid, 0, strlen($listid) - 1);
        $xtpl->assign('listid', $listid);
        $xtpl->parse('main.users');
    } else {
        foreach ($array_data as $array_data_i) {
            $array_data_i['is_fail'] = '222';
            $xtpl->assign('ROW', $array_data_i);
            $xtpl->assign('HIDDEN_ADD_CMP', $array_data_i['is_added_cmp'] ? ' hidden' : '');
            $xtpl->assign('HIDDEN_REM_CMP', $array_data_i['is_added_cmp'] ? '' : ' hidden');
            if ($array_data_i['logo'] != "") {
                $xtpl->parse('main.loop.images');
            }
            if (!empty($array_data_i['true_code'])) {
                $xtpl->parse('main.loop.is_fail');
            }
            $xtpl->parse('main.loop');
        }
    }

    if ($pages_businesslistings != "") {
        $xtpl->assign('PAGE', $pages_businesslistings);
        $xtpl->parse('main.page');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_list1($array_data, $title, $pages_businesslistings, $array_cat_favorite, $all_page, $num_items, $keyword, $count_item, $pop_up = '')
{
    global $module_info, $module_file, $client_info, $global_array_vip, $nv_Lang, $array_round_format;
    global $global_array_config;
    $xtpl = new XTemplate("bu_list1.tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('TITLE', $title);
    $xtpl->assign('NUM_ITEMS', number_format($num_items, 0, ...$array_round_format));

    if (empty($array_data)) {
        $xtpl->assign('NO_DATA', sprintf($nv_Lang->getModule('no_data'), $keyword, number_format($count_item, 0, ...$array_round_format)));
        $xtpl->parse('main.nodata');
    }

    // Ẩn hiện sđt, fax
    $show_old_detail = true;
    $config_vip_show_old_detail = explode(',', $global_array_config['list_vip_show_old_msc']);
    if (!empty($global_array_vip)) {
        // Ẩn dữ liệu nếu không có gói vip nằm trong danh sách show dữ liệu msc cũ
        $array_keys_vip_user = array_keys($global_array_vip);
        $array_vip_show_old_detail = array_intersect($array_keys_vip_user, $config_vip_show_old_detail);

        if (empty($array_vip_show_old_detail)) {
            $show_old_detail = false;
        }
    } else {
        $show_old_detail = false;
    }

    foreach ($array_data as $array_data_i) {
        $array_data_i['addressfull'] = empty($array_data_i['addressfull']) ? '-' : $array_data_i['addressfull'];
        $array_data_i['phone'] = empty($array_data_i['phone']) ? '-' : $array_data_i['phone'];
        $array_data_i['chartercapital'] = empty($array_data_i['chartercapital']) ? '-' : $array_data_i['chartercapital'];
        $xtpl->assign('ROW', $array_data_i);
        if ($array_data_i['logo'] != "") {
            $xtpl->parse('main.loop.images');
        }
        if (!empty($array_data_i['businesstype_link'])) {
            $xtpl->parse('main.loop.businesstype_link');
        } else {
            $xtpl->parse('main.loop.businesstype_text');
        }
        if (!empty($array_data_i['true_code'])) {
            $xtpl->parse('main.loop.is_fail');
        }
        if (defined('NV_IS_MODADMIN') || $show_old_detail) {
            $xtpl->parse('main.loop.phone');
        }
        $xtpl->parse('main.loop');
    }

    if ($pages_businesslistings != "") {
        $xtpl->assign('PAGE', $pages_businesslistings);
        $xtpl->parse('main.page');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_detailpartnership($array_data, $arr_partnership, $number_result_partnership, $array_order, $ord, $generate_page, $page, $per_page, $check_user = false, $link_register = "")
{
    global $global_config, $module_name, $module_file, $module_config, $module_info, $op, $client_info, $page_title, $title_lock, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('LINK', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($array_data['companyname']) . '-' . $array_data['id']);
    $xtpl->assign('PAGE', $page);
    $bgNoVip = getBackgroundIsNotVip('bg_list_detailpartnership');
    $xtpl->assign('BACKGROUND', $bgNoVip);

    if ($check_user === false) {
        $xtpl->assign('SHOW_NOTE', $link_register);
        $xtpl->parse('main.partnership.show_note');
    }

    $partnership__one = [];
    $partnership__one['link'] = "";
    $partnership__one['title'] = "";
    // các nhà thầu cùng liên danh
    $num_partnership = sizeof($arr_partnership ?: []);
    $arr_partnership = array_slice($arr_partnership, ($page - 1) * $per_page, $per_page, true);
    if (!empty($arr_partnership) and sizeof($arr_partnership) > 0) {
        $j = ($page - 1) * $per_page;
        foreach ($arr_partnership as $partnership) {
            $j++;
            $partnership['stt'] = $j;
            $xtpl->assign('PARTNERSHIP', $partnership);
            if ($check_user === false) {
                $xtpl->assign("LOCK", $title_lock);
                $xtpl->parse('main.partnership.loop.show_one');
            } else {
                $xtpl->parse('main.partnership.loop.show_all');
            }

            if ($j == 1) {
                $partnership__one['title'] = $partnership['ten_nha_thau'];
                $partnership__one['link'] = $partnership['link'];
            }
            $xtpl->parse('main.partnership.loop');
            if (!$check_user && $j == 3) {
                break;
            }
        }

        $link_detail = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($array_data['companyname']) . '-' . $array_data['id'], true);
        $xtpl->assign('TITLE', sprintf($nv_Lang->getModule('title_detail_viewinvestor'), $link_detail, $array_data['companyname'], $partnership__one['link'], $partnership__one['title']));
        // $page_title = sprintf($nv_Lang->getModule('title_tab'), $array_data['companyname'], $partnership__one['title']);

        $xtpl->assign('NUMBER_PARTNERSHIP', sprintf($nv_Lang->getModule('number_partnership'), $array_data['companyname'], $num_partnership, sizeof($number_result_partnership['so_tbmt']), $number_result_partnership['result_1'], $number_result_partnership['result_2'], $number_result_partnership['result_3'], $number_result_partnership['result_4']));
        if ($check_user) {
            foreach ($array_order as $key => $value) {
                $order = array(
                    'key' => $key,
                    'value' => $value,
                    'selected' => $key == $ord ? 'selected="selected"' : ''
                );
                $xtpl->assign('ORDER', $order);
                $xtpl->parse('main.partnership.show_order.order');
            }
            $xtpl->parse('main.partnership.show_order');
        }

        // Kiểm tra nếu khách đã đăng nhập thì hiển thị còn k ẩn hết
        if ($check_user) {
            if (!empty($generate_page)) {
                $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
                $xtpl->parse('main.partnership.generate_page');
            }
        }

        $xtpl->parse('main.partnership');
    }
    $page_title = sprintf($nv_Lang->getModule('listpartnership1'), $array_data['companyname']);
    $xtpl->assign('TITLE', $page_title);
    $xtpl->assign('ROW', $array_data);

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_contractor_province($array_data, $contractor_province, $generate_page, $page, $per_page, $check_user = false, $link = "", $chart_linkview = '')
{
    global $module_name, $module_file, $module_info, $op, $title_lock, $nv_Lang, $province_list;
    $nv_Lang->setModule('title_chart_contractor_province', $nv_Lang->getModule('title_chart_contractor_province', $array_data['companyname']));
    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('LINK', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($array_data['companyname']) . '-' . $array_data['id']);
    $xtpl->assign('PAGE', $page);
    $xtpl->assign('LINK_JS', '/themes/' . $module_info['template'] . '/');
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $bgNoVip = getBackgroundIsNotVip('bg_list_contractor_province');
    $xtpl->assign('BACKGROUND', $bgNoVip);
    $xtpl->assign('TITLE', sprintf($nv_Lang->getModule('list_contractor_province_1'), $array_data['companyname']));
    if (defined('NV_IS_VIP3')) {
        $xtpl->assign('VIP3', 1);
    } else {
        $xtpl->assign('VIP3', 0);
    }
    // Hiển thị đoạn text Bảng dữ liệu được ẩn đi 1 phần....
    if ($check_user === false) {
        $xtpl->assign('SHOW_NOTE', $link);
        $xtpl->parse('main.show_note');
    }

    // Xử lý danh sách các tỉnh/thành phố
    $province_arr = [];
    foreach ($province_list as $province) {
        !empty($province['id']) && $province_arr[$province['id']] = $province['title'];
        $active = '';
        if (!empty($array_op[1])) {
            $active = $array_op[1];
        }
        $xtpl->assign('PROVINCE', array(
            'id' => $province['id'],
            'title' => $province['title'],
            'link' => '/' . $nv_Lang->getModule('linktinhthanh') . '/' . $province['alias'] . '/',
            'selected' => $province['alias'] == $active ? ' selected="selected"' : ''
        ));
        $xtpl->parse('main.province.part');

        $id_province = $province['id'];
        $array_province_list[$id_province] = array(
            'id' => $province['id'],
            'title' => $province['title'],
            'region_id' => $province['region_id'],
            'link' => '/' . $nv_Lang->getModule('linktinhthanh') . '/' . $province['alias'] . '/'
        );
        $xtpl->assign('PROVINCE_LIST_' . $id_province, $array_province_list[$id_province]);
    }
    $xtpl->parse('main.province.province_list');

    $_arr_data_province = $contractor_province;
    $num_bid_nationwide = isset($_arr_data_province[824]) ? $_arr_data_province[824]['num_total'] : 0;
    $num_bid_outside = isset($_arr_data_province[825]) ? $_arr_data_province[825]['num_total'] : 0;
    unset($_arr_data_province[824]);
    unset($_arr_data_province[825]);
    unset($_arr_data_province[0]);
    $total_province = count($_arr_data_province);
    $xtpl->assign('NUMBER_PROVINCE', sprintf($nv_Lang->getModule('number_province'), $array_data['lang_companyname'], $total_province, $num_bid_nationwide, $num_bid_outside));

    // Xử lý hiển thị danh sách các tỉnh mà nhà thầu tham gia thầu
    $province_arr[825] = $nv_Lang->getModule('vn_out_territory');
    $province_arr[824] = $nv_Lang->getModule('nationwide');
    array_msort($contractor_province, "num_total", 'desc');
    $contractor_province_dt = $contractor_province;
    $contractor_province = array_slice($contractor_province, ($page - 1) * $per_page, $per_page, true);
    if (!empty($contractor_province) and sizeof($contractor_province) > 0) {
        $j = ($page - 1) * $per_page;
        foreach ($contractor_province as $key => $_province) {
            $j++;
            $_province['stt'] = $j;
            $_province['total_win_price'] = number_format($_province['total_win_price'], 0, ',', '.') . ' VND';
            $_province['total_price_partnership'] = number_format($_province['total_win_price_liendanh'], 0, ',', '.') . ' VND';
            $_province['title'] = isset($province_arr[$key]) ? $province_arr[$key] : $nv_Lang->getModule('undefined');
            $_province['link'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=resultdetail/' . change_alias($array_data['companyname']) . '-' . $array_data['id'], true) . '?prov=' . $key;
            $xtpl->assign('DATA', $_province);
            $xtpl->assign('LOCK', $title_lock);
            if ($check_user === false) {
                $xtpl->assign("LOCK", $title_lock);
                $xtpl->parse('main.loop.show_one');
            } else {
                $xtpl->parse('main.loop.show_all');
            }
            $xtpl->parse('main.loop');
            if (!$check_user && $j == 3) {
                break;
            }
        }

        // Kiểm tra nếu khách đã đăng nhập thì hiển thị còn k ẩn hết
        if ($check_user) {
            if (!empty($generate_page)) {
                $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
                $xtpl->parse('main.generate_page');
            }
        }
    }

    // Xử lý dữ liệu biểu đồ
    if (!empty($contractor_province_dt)) {
        $_class = count($contractor_province_dt) >= 4 ? ' pad-bottom' : '';
        $xtpl->assign('CLASS', $_class);
        $labels = array (
            $nv_Lang->getModule('num_total_bids'),
            $nv_Lang->getModule('trungthau'),
            $nv_Lang->getModule('label_slip_rate'),
            $nv_Lang->getModule('num_total_trung_liendanh')
        );
        $labels_1 = array (
            $nv_Lang->getModule('goithau_tonggiatritrung'),
            $nv_Lang->getModule('num_price_trung_liendanh')
        );
        $j = 0;
        $_arr_data_chart = [];
        foreach ($contractor_province_dt as $key => $_pro_dt) {
            $_value_arr = [$_pro_dt['num_total'], $_pro_dt['result1'], $_pro_dt['result2'], $_pro_dt['num_total_win_liendanh']];
            $_values_price_arr = [$_pro_dt['total_win_price'], $_pro_dt['total_win_price_liendanh']];
            $temp[$key] = array(
                'id' => $key,
                'title' => isset($province_arr[$key]) ? $province_arr[$key] : $nv_Lang->getModule('undefined'),
                'values' => $_value_arr,
                'values_price' => $_values_price_arr,
                'labels' => $labels,
                'labels_1' => $labels_1
            );
            $xtpl->assign('DATA_PROVINCE', htmlentities(json_encode($temp, JSON_HEX_QUOT)));
            if ($j < 4 and !in_array($key, [0,824,825])) {
                $j++;
                $_arr_data_chart[] = array(
                    'id' => $key,
                    'title' => isset($province_arr[$key]) ? $province_arr[$key] : $nv_Lang->getModule('undefined'),
                    'values' => json_encode($_value_arr),
                    'values_price' => json_encode($_values_price_arr),
                    'labels' => json_encode($labels),
                    'labels_1' => json_encode($labels_1),
                    'region_id' => $array_province_list[$key]['region_id']
                );
            }
        }
        array_msort($_arr_data_chart, "region_id", 'asc');
        $tt = 0;
        foreach ($_arr_data_chart as $item) {
            $tt++;
            $item['class'] = ' chart_bids_' . $tt;
            $item['class_w'] = ' chart_wrap_' . $tt;
            $xtpl->assign('DATA_CHART', $item);
            $xtpl->parse('main.province.chart');
            $xtpl->parse('main.province.js_chart');
        }
    }
    $xtpl->parse('main.province');
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_ajax_businesslistings_detailpartnership($arr_partnership, $generate_page, $page, $per_page)
{
    global $global_config, $module_name, $module_file, $module_info, $op, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);

    $arr_partnership = array_slice($arr_partnership, ($page - 1) * $per_page, $per_page, true);
    if (!empty($arr_partnership) and sizeof($arr_partnership) > 0) {
        $i = 0;
        foreach ($arr_partnership as $result) {
            $i++;
            $result['stt'] = $i;
            $xtpl->assign('PARTNERSHIP', $result);
            $xtpl->parse('ajax.loop');
        }
    }

    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('ajax.generate_page');
    }

    $xtpl->parse('ajax');
    return $xtpl->text('ajax');
}

function nv_theme_businesslistings_viewpartnership($row, $business, $array_bidding_open, $number_result, $check_user = false, $link = "")
{
    global $global_config, $module_name, $module_file, $module_config, $module_info, $op, $client_info, $page_title, $nv_Lang;
    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);

    $xtpl->assign('BUSINESS', $business);
    $xtpl->assign('ROW', $row);

    // Hiển thị đoạn text Bảng dữ liệu được ẩn đi 1 phần....
    if ($check_user === false) {
        $xtpl->assign('SHOW_NOTE', $link);
        $xtpl->parse('main.bidding.show_note');
    }

    if (!empty($array_bidding_open) and sizeof($array_bidding_open) > 0) {
        $i = 0;
        foreach ($array_bidding_open as $result) {
            $i++;
            $result['stt'] = $i;

            if ($result['trung_thau'] == 1) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result1');
            } else if ($result['trung_thau'] == 2) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result2');
            } elseif ($result['trung_thau'] == 3) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result3');
            } else {
                $result['trung_thau_lang'] = $nv_Lang->getModule('label_cancel');
            }

            if ($result['partnership'] == 1) {
                $result['partnership'] = $nv_Lang->getModule('vaitro1');
            } else if ($result['partnership'] == 2) {
                $result['partnership'] = $nv_Lang->getModule('vaitro2');
            } else {
                $result['partnership'] = $nv_Lang->getModule('vaitro3');
            }

            $result['finish_time'] = ($result['finish_time'] > 0) ? date("d/m/Y", $result['finish_time']) : '';

            $xtpl->assign('RESULT', $result);

            if ($result['trung_thau'] != 0) {
                $xtpl->parse('main.bidding.loop.result1');
            } else {
                $xtpl->parse('main.bidding.loop.result0');
            }

            $xtpl->parse('main.bidding.loop');

            if ($i == 3 and !defined('NV_IS_VIP'))
                break;
        }

        if (defined('NV_IS_USER') and !defined('NV_IS_VIP') and $number_result['total'] > 3) {
            $link = sprintf($nv_Lang->getModule('view_result_user'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
            $xtpl->assign('user', $link);
            $xtpl->parse('main.bidding.user');
        } else if (!defined('NV_IS_USER') and $i > 3) {
            $link_login = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
            $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
            $link = vsprintf($nv_Lang->getModule('view_result_client'), array(
                $link_login,
                $link_register
            ));
            $xtpl->assign('client', $link);
            $xtpl->parse('main.bidding.client');
        }

        $xtpl->assign('NUMBER_VIEWPARTNERSHIP', sprintf($nv_Lang->getModule('number_viewpartnership'), $row['companyname'], $business['companyname'], $number_result['total'], $number_result['result1'], $number_result['result2'], $number_result['result3']));

        $xtpl->parse('main.bidding');
    }

    $page_title = sprintf($nv_Lang->getModule('title_tab'), $row['companyname'], $business['companyname']);

    $link_detail = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($row['companyname']) . '-' . $row['id'], true);
    $link = !empty($business['id']) ? nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($business['companyname']) . '-' . $business['id'], true): '#';
    $xtpl->assign('TITLE', sprintf($nv_Lang->getModule('title_detail_viewrelative'), $link_detail, $row['companyname'], $link, $business['companyname']));

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_static($array_data, $array_order, $select_top, $ajax, $order)
{
    global $global_config, $module_name, $module_file, $module_info, $op, $nv_Lang, $array_op, $base_url;

    if ($ajax) {
        $xtpl = new XTemplate('static.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);
        $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
        $xtpl->assign('GOITHAU_TOTAL', ($select_top == 0 ? $nv_Lang->getModule('goithau_total') :
        ($select_top == 1 ? $nv_Lang->getModule('goithau_trung') :
        ($select_top == 2 ? $nv_Lang->getModule('goithau_truot') : ''))));

        if (!empty($array_data)) {
            if ($select_top == 4) {
                $merged_data = [];

                if (isset($array_data['doclap'])) {
                    foreach ($array_data['doclap'] as $item) {
                        $key = $item['id'];
                        if (!isset($merged_data[$key])) {
                            $merged_data[$key] = $item;
                            $merged_data[$key]['num_doclap'] = floatval($item['num']);
                            $merged_data[$key]['num_liendanh'] = 0;
                        }
                    }
                }

                if (isset($array_data['liendanh'])) {
                    foreach ($array_data['liendanh'] as $item) {
                        $key = $item['id'];
                        if (isset($merged_data[$key])) {
                            $merged_data[$key]['num_liendanh'] = floatval($item['num']);
                        } else {
                            $merged_data[$key] = $item;
                            $merged_data[$key]['num_doclap'] = 0;
                            $merged_data[$key]['num_liendanh'] = floatval($item['num']);
                        }
                    }
                }

                $i = 0;
                foreach ($merged_data as $data) {
                    $data['STT'] = ++$i;
                    $data['num'] = nv_number_format($data['num_doclap']) . '|' . nv_number_format($data['num_liendanh']);
                    $xtpl->assign('VIEW', $data);
                    $xtpl->parse('ajax.loop');
                }
            } else {
                $sliced_data = array_slice($array_data, 0, 10);
                $i = 0;
                foreach ($sliced_data as $data) {
                    $data['STT'] = ++$i;
                    $data['num'] = nv_number_format($data['num']);
                    $xtpl->assign('VIEW', $data);
                    $xtpl->parse('ajax.loop');
                }
            }
        }
        $xtpl->parse('ajax');
        return $xtpl->text('ajax');
    }

    $xtpl = new XTemplate('static.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('OP', $op);
    $xtpl->assign('LINK_JS', '/themes/' . $module_info['template'] . '/');
    $xtpl->assign('select_top', $select_top);

    if (empty($array_op[1])) {
        // Xử lý cho trang overview
        foreach (['DUTHAU' => 0, 'TRUNGTHAU' => 1, 'TRUOTTHAU' => 2, 'NANGLUC' => 3, 'DOANHSO' => 4] as $link => $i) {
            $xtpl->assign('LINK_' . $link, $base_url . $nv_Lang->getModule('static_type_' . $i) . $global_config['rewrite_exturl']);
        }

        if (!empty($array_data)) {
            foreach (['duthau', 'trungthau', 'truotthau', 'nangluc'] as $type) {
                if (isset($array_data[$type])) {
                    $xtpl->assign('DATA_' . strtoupper($type), json_encode($array_data[$type]));
                }
            }

            $doanhso_data = [];
            if (isset($array_data['doclap'])) {
                foreach ($array_data['doclap'] as $item) {
                    $doanhso_data[$item['companyname']] = [
                        'companyname' => $item['companyname'],
                        'doclap_num' => floatval($item['num']),
                        'liendanh_num' => 0,
                        'chidinhthau_num' => 0,
                        'hinhthuckhac_num' => 0,
                        'total' => floatval($item['num'])
                    ];
                }

                if (isset($array_data['liendanh'])) {
                    foreach ($array_data['liendanh'] as $item) {
                        if (isset($doanhso_data[$item['companyname']])) {
                            $doanhso_data[$item['companyname']]['liendanh_num'] = floatval($item['num']);
                            $doanhso_data[$item['companyname']]['total'] += floatval($item['num']);
                        } else {
                            $doanhso_data[$item['companyname']] = [
                                'companyname' => $item['companyname'],
                                'doclap_num' => 0,
                                'liendanh_num' => floatval($item['num']),
                                'chidinhthau_num' => 0,
                                'hinhthuckhac_num' => 0,
                                'total' => floatval($item['num'])
                            ];
                        }
                    }
                }

                if (isset($array_data['chidinhthau'])) {
                    foreach ($array_data['chidinhthau'] as $item) {
                        if (isset($doanhso_data[$item['companyname']])) {
                            $doanhso_data[$item['companyname']]['chidinhthau_num'] = floatval($item['num']);
                            $doanhso_data[$item['companyname']]['hinhthuckhac_num'] =
                                $doanhso_data[$item['companyname']]['doclap_num'] +
                                $doanhso_data[$item['companyname']]['liendanh_num'] -
                                floatval($item['num']);
                        }
                    }
                }

                $doanhso_data = array_values($doanhso_data);

                usort($doanhso_data, function($a, $b) {
                    return $b['total'] <=> $a['total'];
                });

                $doanhso_data = array_slice($doanhso_data, 0, 10);

                $xtpl->assign('DATA_DOCLAP', json_encode(array_map(function($item) {
                    return ['companyname' => $item['companyname'], 'num' => $item['doclap_num']];
                }, $doanhso_data)));
                $xtpl->assign('DATA_LIENDANH', json_encode(array_map(function($item) {
                    return ['companyname' => $item['companyname'], 'num' => $item['liendanh_num']];
                }, $doanhso_data)));
                $xtpl->assign('DATA_CHIDINHTHAU', json_encode(array_map(function($item) {
                    return ['companyname' => $item['companyname'], 'num' => $item['chidinhthau_num']];
                }, $doanhso_data)));
                $xtpl->assign('DATA_HINHTHUCKHAC', json_encode(array_map(function($item) {
                    return ['companyname' => $item['companyname'], 'num' => $item['hinhthuckhac_num']];
                }, $doanhso_data)));
                $xtpl->assign('DATA_DOANHSO', json_encode($doanhso_data));
            }

            // VNR TOP 10
            if (!empty($array_data['vnr_years'])) {
                foreach ($array_data['vnr_years'] as $year) {
                    $xtpl->assign('YEAR', [
                        'key' => $year,
                        'title' => $nv_Lang->getModule('year') . ' ' . $year,
                        'selected' => ($year == ($array_data['vnr_selected_year'] ?? max($array_data['vnr_years']))) ? ' selected="selected"' : ''
                    ]);
                    $xtpl->parse('main.overview.vnr_top10.year');
                }
                $xtpl->assign('CHART_DATA', json_encode($array_data['chart_data']));
                $xtpl->assign('MAX_YEAR', $array_data['vnr_max_year']['year']);
                $xtpl->assign('SELECTED_TYPE', $array_data['vnr_selected_type']);
                $xtpl->assign('OP_AJAX', $module_info['alias']['static']);
                $xtpl->assign('LINK_VNR500', $base_url . 'vnr500/');

                $xtpl->parse('main.overview.vnr_top10');
            }

            $xtpl->parse('main.overview');
            $xtpl->parse('main.business_scripts.overview_js');
        }
    } else {
            if ($array_op[1] != 'vnr500') {
                // Xử lý cho trang detail
                $xtpl->assign('LINK', $base_url . $array_op[1] . $global_config['rewrite_exturl']);
                $xtpl->assign('GOITHAU_TOTAL', ($select_top == 0 ? $nv_Lang->getModule('goithau_total') : ($select_top == 1 ? $nv_Lang->getModule('goithau_trung') : ($select_top == 2 ? $nv_Lang->getModule('goithau_truot') : ($select_top == 3 ? $nv_Lang->getModule('ability_point_short') : $nv_Lang->getModule('num_revenue'))))));
                $xtpl->assign('lang_static', ($select_top == 1 ? $nv_Lang->getModule('static_num_result') : ($select_top == 2 ? $nv_Lang->getModule('static_num_false') : ($select_top == 3 ? $nv_Lang->getModule('static_num_capability') : ($select_top == 4 ? $nv_Lang->getModule('static_num_revenue'): $nv_Lang->getModule('static_num_total'))))));

                $full_list_base = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name;

                $sort_type = '';
                if($select_top == 0) {
                    $sort_type = 'num_total_desc';
                } elseif($select_top == 1) {
                    $sort_type = 'num_result_desc';
                } elseif($select_top == 2) {
                    $sort_type = 'num_false_desc';
                } elseif($select_top == 3) {
                    $sort_type = 'ability_point_desc';
                } elseif($select_top == 4) {
                    $sort_type = 'total_revenue';
                }

                $full_list_link = nv_url_rewrite($full_list_base, true);
                $full_list_link .= !empty($sort_type) ? '?sort=' . $sort_type : '';

                $xtpl->assign('FULL_LIST_LINK', $full_list_link);

                if (!empty($array_data)) {
                    if ($select_top == 4) {
                        if (isset($array_data['table'])) {
                            $doanhso_data = [];
                            if (isset($array_data['doclap'])) {
                                foreach ($array_data['doclap'] as $item) {
                                    $doanhso_data[$item['companyname']] = [
                                        'companyname' => $item['companyname'],
                                        'link' => $item['link'],
                                        'doclap_num' => floatval($item['num']),
                                        'liendanh_num' => 0,
                                        'chidinhthau_num' => 0,
                                        'hinhthuckhac_num' => 0,
                                        'total' => floatval($item['num'])
                                    ];
                                }
                            }

                            if (isset($array_data['liendanh'])) {
                                foreach ($array_data['liendanh'] as $item) {
                                    if (isset($doanhso_data[$item['companyname']])) {
                                        $doanhso_data[$item['companyname']]['liendanh_num'] = floatval($item['num']);
                                        $doanhso_data[$item['companyname']]['total'] += floatval($item['num']);
                                    } else {
                                        $doanhso_data[$item['companyname']] = [
                                            'companyname' => $item['companyname'],
                                            'link' => $item['link'],
                                            'doclap_num' => 0,
                                            'liendanh_num' => floatval($item['num']),
                                            'chidinhthau_num' => 0,
                                            'hinhthuckhac_num' => 0,
                                            'total' => floatval($item['num'])
                                        ];
                                    }
                                }
                            }

                            if (isset($array_data['chidinhthau'])) {
                                foreach ($array_data['chidinhthau'] as $item) {
                                    if (isset($doanhso_data[$item['companyname']])) {
                                        $doanhso_data[$item['companyname']]['chidinhthau_num'] = floatval($item['num']);
                                        $doanhso_data[$item['companyname']]['hinhthuckhac_num'] =
                                            $doanhso_data[$item['companyname']]['doclap_num'] +
                                            $doanhso_data[$item['companyname']]['liendanh_num'] -
                                            floatval($item['num']);
                                    }
                                }
                            }

                            uasort($doanhso_data, function($a, $b) {
                                return $b['total'] <=> $a['total'];
                            });

                            // Lấy top 10
                            $i = 0;
                            foreach ($doanhso_data as $data) {
                                if ($i >= 10) break;
                                $view = [
                                    'STT' => ++$i,
                                    'companyname' => $data['companyname'],
                                    'link' => $data['link'],
                                    'num' => nv_number_format($data['total']),
                                    'data_values' => $data['doclap_num'] . '|' . $data['liendanh_num'] . '|' . $data['chidinhthau_num'] . '|' . $data['hinhthuckhac_num']
                                ];
                                $xtpl->assign('VIEW', $view);
                                $xtpl->parse('main.detail.loop');
                            }
                            $xtpl->parse('main.detail.revenue');
                        }
                    } else {
                        $sliced_data = array_slice($array_data, 0, 10);
                        $i = 0;
                        foreach ($sliced_data as $data) {
                            $data['STT'] = ++$i;
                            $data['num'] = nv_number_format($data['num']);
                            $xtpl->assign('VIEW', $data);
                            $xtpl->parse('main.detail.loop');
                        }
                    }

                    if ($select_top != 3 & $select_top != 4) {
                        foreach ($array_order as $key => $value) {
                            $xtpl->assign('ORDER', [
                                'key' => $key,
                                'value' => $value,
                                'selected' => $key == $order ? 'selected="selected"' : ''
                            ]);
                            $xtpl->parse('main.detail.select_order.order');
                        }
                        $xtpl->parse('main.detail.select_order');
                    }
                }
                $xtpl->parse('main.detail');
                $xtpl->parse('main.business_scripts.detail_js');
            }
            // VNR TOP 10
            if (!empty($array_op[1] == 'vnr500')) {
                if (!empty($array_data['vnr_years'])) {
                    foreach ($array_data['vnr_years'] as $year) {
                        $xtpl->assign('YEAR', [
                            'key' => $year,
                            'title' => $nv_Lang->getModule('year') . ' ' . $year,
                            'selected' => ($year == ($array_data['vnr_selected_year'] ?? max($array_data['vnr_years']))) ? ' selected="selected"' : ''
                        ]);
                        $xtpl->parse('main.detail2.vnr_top10.year');
                    }

                    if (!empty($array_data['legend'])) {
                        $i = 0;
                        foreach ($array_data['legend'] as $legend) {
                            $xtpl->assign('VIEW', [
                                'companyname' => $legend['companyname'],
                                'total_top10' => $legend['total_top10'],
                                'link' => $legend['link_company'],
                                'rank' => $legend['rank'],
                                'STT' => ++$i
                            ]);
                            $xtpl->parse('main.detail2.vnr_top10.loop');
                        }
                    }

                    $xtpl->assign('CHART_DATA', json_encode($array_data['chart_data']));
                    $xtpl->assign('MAX_YEAR', $array_data['vnr_max_year']['year']);
                    $xtpl->assign('SELECTED_TYPE', $array_data['vnr_selected_type']);
                    $xtpl->assign('OP_AJAX', $module_info['alias']['static'] . '/vnr500');

                    $xtpl->parse('main.detail2.vnr_top10');
                    $xtpl->parse('main.detail2');
                }
            }
        }

    $xtpl->parse('main.business_scripts');
    $xtpl->parse('main');
    return $xtpl->text('main');
}
 
function user_info_exit($info, $error = false)
{
    global $module_info, $module_file;

    $xtpl = new XTemplate('info_exit.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('INFO', $info);

    if ($error) {
        $xtpl->parse('main.danger');
    } else {
        $xtpl->parse('main.info');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_viewtimeline($array_data, $arr_config, $array_bidding, $number_result, $check_vip, $array_type_lc)
{
    global $module_name, $module_file, $module_info, $op, $page_title, $client_info, $dm_htlcnt, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);

    if (defined('NV_IS_SPADMIN')) {
        $del_cache = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=viewtimeline/' . change_alias($array_data['companyname']) . '-' . $array_data['id'] . "&amp;delete_cache=" . $array_data['id'], 'true');
        $xtpl->assign('CACHE', $del_cache);
        $xtpl->parse('main.bidding.cache');
    }

    $xtpl->assign('TIME', date("H:i d/m/Y", $array_data['time_now']));

    $xtpl->assign('CONFIG', $arr_config);
    $xtpl->assign('LINK', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=viewtimeline/' . change_alias($array_data['companyname']) . '-' . $array_data['id']);
    $_link = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=detail/' . change_alias($array_data['companyname']) . '-' . $array_data['id'];

    $xtpl->assign('NUMBER_RESULT', sprintf($nv_Lang->getModule('number_result_i'), $array_data['companyname'], $number_result['total'], $number_result['all'], $number_result['result1'], $number_result['result2'], $number_result['result3'], $number_result['result4'], $number_result['total_pg'], $number_result['total_pg0'], $number_result['total_pg1'], $number_result['total_pg2']));

    $link_login = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);

    if ($check_vip == 1) {
        $xtpl->parse('main.bidding.show1');
        $xtpl->parse('main.bidding.show2');
    } else {
        if ($check_vip == -1) {
            $xtpl->assign('timeline_linkview', sprintf($nv_Lang->getModule('timeline_result_client'), $link_login, $link_register));
        } else {
            if ($check_vip == 2) {
                $xtpl->assign('timeline_linkview', sprintf($nv_Lang->getModule('timeline_view_result_user'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3'));
            } else {
                $xtpl->assign('timeline_linkview', sprintf($nv_Lang->getModule('timeline_view_result_user'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3'));
            }
        }
        $xtpl->parse('main.bidding.linkview');
    }

    array_msort($array_bidding, "finish_time", 'desc');
    $i = 0;
    if (!empty($array_bidding) and sizeof($array_bidding) > 0) {
        foreach ($array_bidding as $k => $result) {
            ++$i;
            if ($i % 2 == 0) {
                $result['vt'] = 'timeline-inverted';
                $result['vt2'] = 'invert';
            } else {
                $result['vt'] = $result['vt2'] = '';
            }

            if ($result['trung_thau'] == 1) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result1');
                $lang = $nv_Lang->getModule('tt');
                $result['vt2'] .= ' fa fa-check-circle-o';
            } else if ($result['trung_thau'] == 2) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result2');
                $lang = $nv_Lang->getModule('trt');
                $result['vt2'] .= ' fa fa-times-circle-o';
            } else if ($result['trung_thau'] == 4) {
                $result['trung_thau_lang'] = $nv_Lang->getModule('result_cancel');
                $lang = $nv_Lang->getModule('tg');
                $result['link_result'] = (!empty($result['link'])) ? $result['link'] : $result['link_result'];
                $result['vt2'] .= ' fa fa-stop-circle-o';
            } else {
                if (!empty($result['array_pq'])) {
                    if ($result['array_pq'] == 2) {
                        $result['vt2'] .= ' fa fa-check-circle-o';
                    } elseif ($result['array_pq'] == 1) {
                        $result['vt2'] .= ' fa fa-times-circle-o';
                    } else {
                        $result['vt2'] .= ' fa fa-circle-o';
                    }
                } else {
                    $result['trung_thau_lang'] = $nv_Lang->getModule('result4');
                    $lang = $nv_Lang->getModule('tg');
                    $result['vt2'] .= ' fa fa-circle-o';
                }
            }
            if (!empty($result['hinh_thuc_lua_chon'])) {
                $lang2 = (in_array($result['hinh_thuc_lua_chon'], array_keys($dm_htlcnt)) ? $dm_htlcnt[$result['hinh_thuc_lua_chon']] : $result['hinh_thuc_lua_chon']);
            } elseif (!empty($result['type_choose_id']) && !empty($array_type_lc[$result['type_choose_id']]['title'])) {
                $lang2 = $array_type_lc[$result['type_choose_id']]['title'];
            } else {
                $lang2 = '';
            }

            if (!empty($result['link'])) {
                $desc_timeline = str_replace('<aaa href="%s">', '<a href="%s">', $nv_Lang->getModule('desc_timeline'));
                $desc_timeline = str_replace('</aaa>', '</a>', $desc_timeline);

                $desc_timeline_partnership = str_replace('<aaa href="%s">', '<a href="%s">', $nv_Lang->getModule('desc_timeline_partnership'));
                $desc_timeline_partnership = str_replace('</aaa>', '</a>', $desc_timeline_partnership);
            } else {
                $desc_timeline = str_replace('<aaa href="%s">', '%s', $nv_Lang->getModule('desc_timeline'));
                $desc_timeline = str_replace('</aaa>', '', $desc_timeline);

                $desc_timeline_partnership = str_replace('<aaa href="%s">', '%s', $nv_Lang->getModule('desc_timeline_partnership'));
                $desc_timeline_partnership = str_replace('</aaa>', '', $desc_timeline_partnership);
            }

            if ($result['type'] != 'pq') {
                if ($result['partnership'] == 1) {
                    $result['desc_timeline'] = sprintf($desc_timeline, $_link, $array_data['companyname'], $lang, $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2);
                } elseif ($result['partnership'] == 2) {
                    $result['desc_timeline'] = sprintf($desc_timeline_partnership, $_link, $array_data['companyname'], $lang, $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2, $nv_Lang->getModule('ldc'), $result['bidder_name']);
                } else {
                    $result['desc_timeline'] = sprintf($desc_timeline_partnership, $_link, $array_data['companyname'], $lang, $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2, $nv_Lang->getModule('ldp'), $result['bidder_name']);
                }
            } else {
                if ($result['joint_venture'] == '') {
                    $result['desc_timeline'] = sprintf($desc_timeline, $_link, $array_data['companyname'], $nv_Lang->getModule('st'), $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2);
                } else {
                    $result['desc_timeline'] = sprintf($desc_timeline_partnership, $_link, $array_data['companyname'], $nv_Lang->getModule('st'), $result['link'], $result['title'], $result['link_solicitor'], $result['investor'], $lang2, $nv_Lang->getModule('tv'), $result['joint_venture']);
                }
                $result['trung_thau_lang'] = $nv_Lang->getModule('result' . $result['array_pq'] . '_pq');
                $result['link_result'] = $result['link'];
            }
            if (!empty($result['count_ld'])) {
                $result['desc_timeline'] .= ' ' . sprintf($nv_Lang->getModule('desc_timeline_count_ld'), NV_LANG_DATA == 'en' ? vnd_to_words($result['count_ld'], false) : $result['count_ld']);
            }
            $result['kq'] = sprintf($nv_Lang->getModule('kq'), $result['link_result'], $result['trung_thau_lang']);

            if ($result['finish_time'] > 0) {
                $result['finish_time'] = date("d/m/Y H:i", $result['finish_time']);
            } else {
                $result['finish_time'] = '';
            }
            $r = [
                'vt' => $result['vt'],
                'vt2' => $result['vt2'],
                'desc_timeline' => $result['desc_timeline'],
                'kq' => $result['kq'],
                'link_result' => $result['link_result'],
                'finish_time' => $result['finish_time']
            ];
            $xtpl->assign('RESULT', $r);
            $xtpl->parse('main.bidding.loop');
        }
        $xtpl->parse('main.bidding');
    }
    $page_title = sprintf($nv_Lang->getModule('listresult1'), $array_data['companyname']);
    $xtpl->assign('TITLE', $page_title);
    $xtpl->assign('ROW', $array_data);

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_stocks($array_data, $page, $per_page, $generate_page, $arr_industry, $error, $static)
{
    global $module_name, $module_file, $module_info, $op, $page_title, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('PAGE_TITLE', $page_title);
    $xtpl->assign('STATIC', $static);

    if ($page > 1) {
        $page_title .= ' - ' . sprintf($nv_Lang->getModule('page_num'), $page);
    }
    if (empty($array_data)) {
        $xtpl->parse('main.empty');
    } else {
        $stt = (($page - 1) * $per_page) + 1;
        foreach ($array_data as $view) {
            $view['stt'] = $stt++;
            $view['list_date'] = date('d/m/Y', $view['list_date']);
            $view['title_icb'] = '';
            if ($view['industry'] != '') {
                $arr_code = explode(',', $view['industry']);
                $arr_title_industry = [];
                for ($i = 0; $i < count($arr_code); $i++) {
                    if (isset($arr_industry[$arr_code[$i]]) && $arr_industry[$arr_code[$i]]['lev'] == 3) {
                        $arr_title_industry[] = $arr_code[$i] . ' - ' . $arr_industry[$arr_code[$i]]['title'];
                    }
                }
                $view['title_icb'] = implode('', $arr_title_industry);
            }
            $xtpl->assign('VIEW', $view);
            $xtpl->parse('main.loop');
        }
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.generate_page');
        }
    }
    if (!empty($error)) {
        $xtpl->assign('ERROR', implode('<br />', $error));
        $xtpl->parse('main.error');
    }

    if (defined('NV_IS_USER')) {
        if (!defined('NV_IS_VIP32')) {
            if (defined('NV_IS_VIP32_RENEW')) {
                $link_vip_32 = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=vip&amp;renewal=1&amp;vip=32';
                $xtpl->assign('PLP_TOOLTIP', $nv_Lang->getModule('plp_report_renewvip_feature') . ' ' . $nv_Lang->getModule('click_to_action'));
                $xtpl->assign('LINK_VIP_32', $link_vip_32);
            } else {
                $link_vip_32 = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=vip&amp;form=1&amp;vip=32';
                $xtpl->assign('PLP_TOOLTIP', $nv_Lang->getModule('plp_report_notvip_feature') . ' ' . $nv_Lang->getModule('click_to_action'));
                $xtpl->assign('LINK_VIP_32', $link_vip_32);
            }
        }
    } else {
        $xtpl->assign('PLP_TOOLTIP', $nv_Lang->getModule('login_require') . ' ' . $nv_Lang->getModule('click_to_action'));
        $xtpl->assign('LINK_VIP_32', 'javascript:void(0)');
        $xtpl->assign('SORT_USABLE', ' disabled');
        $xtpl->parse('main.sort_tooltip.login_form_show');
        $xtpl->parse('main.sort_tooltip');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_businesslistings_violators($list_vipham, $generate_page, $page_title)
{
    global $module_name, $module_file, $module_info, $op, $page_title, $client_info, $module_config, $page_url, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('PAGE_TITLE', $page_title);

    $xtpl->assign('page_title', $page_title);
    $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    if (defined('NV_IS_VIP')) {
        if (!empty($list_vipham)) {
            foreach ($list_vipham as $view) {
                $view['issued_date'] = date('d/m/Y', $view['issued_date']);
                $xtpl->assign('VIEW', $view);
                $xtpl->parse('main.loop');
            }
            if (!empty($generate_page)) {
                $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
                $xtpl->parse('main.view.generate_page');
            }
        }
    } else if (defined('NV_IS_USER')) {
        if (!empty($list_vipham)) {
            $i = 0;
            foreach ($list_vipham as $view) {
                if ($i == 0) {
                    $view['issued_date'] = date('d/m/Y', $view['issued_date']);
                    $xtpl->assign('VIEW', $view);
                    $xtpl->parse('main.loop_vip');
                }

                $i++;
            }
        }
        $link_vip = sprintf($nv_Lang->getModule('view_result_user'), NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3');
        $xtpl->assign('view_listvipham', $link_vip);
        $xtpl->parse('main.view_listvipham');
    } else {
        $link__popup = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
        // Gọi hàm Popuplogin và truyền tham số link vào
        $content_popup = PopupLogin($link__popup, nv_url_rewrite($page_url, true));
        $xtpl->assign('POPUP_LOGIN', $content_popup);
        $xtpl->parse('main.popup_login');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_theme_main_request()
 * hiển thị các request xuất nhà thầu
 *
 * @param mixed $text
 * @param string $type
 * @param string $url
 * @param integer $time
 * @return
 */
function nv_theme_main_request($array_request, $error, $arr_customs, $array_row_request, $generate_page)
{
    global $op, $module_file, $module_info, $module_name, $module_config, $nv_Lang, $array_round_format;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('THEMES', $module_info['template']);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);

    if (!empty($arr_customs)) {
        $cus_info = sprintf($nv_Lang->getModule('customs_info'), nv_date('d/m/Y', $arr_customs['end_time']));

        $arr_adv = [];
        if ($arr_customs['x2_adv_static_bibding'] == 1) {
            $arr_adv[] = $nv_Lang->getModule('x2_adv_static_bibding');
        }
        if ($arr_customs['x2_adv_static_result'] == 1) {
            $arr_adv[] = $nv_Lang->getModule('x2_adv_static_result');
        }
        if ($arr_customs['x2_adv_static_bidsecurity'] == 1) {
            $arr_adv[] = $nv_Lang->getModule('x2_adv_static_bidsecurity');
        }
        if (!empty($arr_adv)) {
            $cus_info .= ' ' . $nv_Lang->getModule('x2_adv') . ' <br>- ' . implode('<br>- ', $arr_adv);
        }

        $xtpl->assign('CUSTOMS', $cus_info);
        $xtpl->parse('main.customs');
    }

    if (!empty($array_row_request)) {
        foreach ($array_row_request as $view) {
            $view['status_id'] = $view['status'];
            $view['status_title'] = $nv_Lang->getModule('status' . $view['status']);
            $view['addtime'] = nv_date('h:i d/m/Y', $view['addtime']);
            $view['num_row'] = number_format($view['num_row'], 0, ...$array_round_format);

            $view['link_down'] = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "&amp;downloadfile=1&requestid=" . $view['id'];
            $xtpl->assign('VIEW', $view);
            if ($view['link_file'] != '') {
                $xtpl->parse('main.view.loop.file');
            } elseif ($view['status'] == 4) {
                $xtpl->parse('main.view.loop.re_download');
            }

            if ($view['status_id'] == 0 or $view['status_id'] == 1) {
                $xtpl->parse('main.view.loop.status0');
            }
            $xtpl->parse('main.view.loop');
        }

        // script download
        $checksess_download = md5(NV_CHECK_SESSION);
        $xtpl->assign('CHECKSESS_DOWNLOAD', $checksess_download);

        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.view.generate_page');
        }
        $xtpl->parse('main.view');
    } else {
        $xtpl->parse('main.view_empty');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}


/**
 * nv_theme_main_exportpdf_request()
 * Hiển thị các request xuất báo cáo lịch sử đấu thầu
 *
 * @param mixed $text
 * @param string $type
 * @param string $url
 * @param integer $time
 * @return
 */
function nv_theme_main_exportpdf_request($array_row_request, $generate_page, $expired_download, $vip_100_download)
{
    global $op, $module_file, $module_info, $module_name, $nv_Lang, $module_config;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('THEMES', $module_info['template']);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
    $xtpl->assign('NV_CHECK_SESSION', NV_CHECK_SESSION);
    if (defined('NV_IS_VIP_T100')) {
        $total_download = $vip_100_download['num_download_pdf_t100_use'];
        $total_num_download_pdf = $vip_100_download['num_download_pdf_t100'];
        $total_no_download =  ($total_num_download_pdf > $total_download) ? ($total_num_download_pdf - $total_download) : 0;
        $xtpl->assign('NUM_DOWNLOAD_PDF', sprintf($nv_Lang->getModule('total_download_pdf'), $total_download));
        $xtpl->assign('NUM_NOT_DOWNLOAD_PDF', sprintf($nv_Lang->getModule('total_no_download_pdf'), $total_no_download));
        $xtpl->assign('EXPIRED_DOWNLOAD_PDF', sprintf($nv_Lang->getModule('expired_download'), $expired_download));
        $xtpl->parse('main.view.static_number');
    }

    if (!empty($array_row_request)) {
        foreach ($array_row_request as $view) {
            $view['status_id'] = $view['status'];
            $view['status'] = ($view['status'] == '-1') ? $nv_Lang->getModule('status_1') : $nv_Lang->getModule('status' . $view['status']);
            $view['addtime'] = nv_date('H:i d/m/Y', $view['addtime']);
            $view['link_down'] = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "&amp;downloadfile=1&requestid=" . $view['id'];
            $xtpl->assign('VIEW', $view);

            if (($view['status_id'] == 2 or $view['status_id'] == 3) and $view['link_file'] != '') {
                $xtpl->parse('main.view.loop.file');
            }
            if ($view['status_id'] == 4 or $view['status_id'] == -1) {
                // File đã xóa thì cho tải lại file
                $xtpl->parse('main.view.loop.file_1');
            }
            if ($view['status_id'] == 0 or $view['status_id'] == 1) {
                $xtpl->parse('main.view.loop.status0');
            }
            $xtpl->parse('main.view.loop');
        }
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.view.generate_page');
        }
        $xtpl->parse('main.view');
    } else {
        $xtpl->parse('main.view_empty');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_info($text, $type = 'info', $url = '', $time = 2)
{
    global $global_config, $module_name, $module_config, $module_info, $nv_Lang;

    $xtpl = new XTemplate("info.tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('TYPE', $type);

    if (!empty($text)) {
        $xtpl->assign('TEXT', $text);

        if (!empty($url)) {
            $xtpl->assign('URL', $url);
            $xtpl->assign('TIME', $time);
            $xtpl->parse('main.text.url');
        }
        $xtpl->parse('main.text');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function getBackgroundIsNotVip($class)
{
    global $module_file, $global_config, $page_url, $nv_Lang, $client_info;
    $xtpl = new XTemplate('overlay_notvip.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('CLASS', $class);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('PAGE_URL', nv_redirect_encrypt($client_info['selfurl']));
    if (defined('NV_IS_USER')) {
        $xtpl->parse('main.is_user');
    } else {
        $xtpl->parse('main.not_user');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_contractor_compare($array_contractors, $size)
{
    global $global_config, $nv_Lang, $module_file, $client_info;
    $nv_Lang->setModule('ls_dauthau', ucfirst($nv_Lang->getModule('ls_dauthau')));

    $link_form_v3 = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3';
    $link_renewal_v3 = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3';

    if (!defined('NV_IS_USER')) {
        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $link = $nv_Lang->getModule('view_result_one', $link_register, nv_url_rewrite($link_form_v3));
    } elseif (defined('NV_IS_VIP3_RENEW')) {
        $link = $nv_Lang->getModule('view_result_user_renew1', $link_renewal_v3);
    } elseif (!defined('NV_IS_VIP3')) {
        $link = $nv_Lang->getModule('view_result_user', $link_form_v3);
    }

    $stpl = new \NukeViet\Template\NVSmarty();
    $stpl->setTemplateDir(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file . '/');
    $stpl->assign('LANG', $nv_Lang);
    $stpl->assign('ARR_CTT', $array_contractors);
    $stpl->assign('LOGIN_REDIRECT', nv_redirect_encrypt($client_info['selfurl']));
    $stpl->assign('LINK_RENEW_VIP3', $link_renewal_v3);
    $stpl->assign('LINK_REG_VIP3', $link_form_v3);
    $stpl->assign('UNLOCK_MESS', $link ?? '');
    $stpl->assign('SIZE', $size ?? '');
    return $stpl->fetch('compare.tpl');
}

function nv_theme_download_excel_script()
{
    global $global_config, $nv_Lang, $module_file, $sys_mods, $module_name;

    $stpl = new XTemplate('download_excel_script.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $stpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    // script download
    $checksess_download = md5(NV_CHECK_SESSION);
    $stpl->assign('CHECKSESS_DOWNLOAD', $checksess_download);
    $stpl->assign('EXPORT_LINK', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $sys_mods['businesslistings']['alias']['export']);
    $stpl->parse('downloadExcelScript');
    return $stpl->text('downloadExcelScript');
}

function nv_theme_plp_download($download_times, $vip_info)
{
    global $global_config, $nv_Lang, $module_file, $user_info;
    $arr_download_times = [];
    $i = 0;
    foreach ($download_times as $k => $v) {
        $i++;
        $arr_download_times[] = [
            'number' => $i,
            'month' => date('m-Y', $k),
            'month_data' => $k,
            'download_left' => $v,
            'check' => md5($vip_info['id'] . '_' . $vip_info['user_id'] . '_' . $k . '_' . NV_CHECK_SESSION)
        ];
    }

    $stpl = new \NukeViet\Template\NVSmarty();
    $stpl->setTemplateDir(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file . '/');
    $stpl->assign('LANG', $nv_Lang);
    $stpl->assign('DOWNLOAD_TIME', $arr_download_times);
    $stpl->assign('URL_ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=businesslistings&' . NV_OP_VARIABLE . '=plpreport', true));
    return $stpl->fetch('plpreport.tpl');
}

function nv_theme_businesslistings_legend($array_data)
{
    global $module_info, $module_file;

    $xtpl = new XTemplate('static.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    if (!empty($array_data['legend'])) {
        $i = 0;
        foreach ($array_data['legend'] as $legend) {
            $xtpl->assign('VIEW', [
                'companyname' => $legend['companyname'],
                'total_top10' => $legend['total_top10'],
                'link' => $legend['link_company'],
                'rank' => $legend['rank'],
                'STT' => ++$i
            ]);
            $xtpl->parse('ajax2.loop');
        }
    }
    
    $xtpl->parse('ajax2');
    return $xtpl->text('ajax2');
}
