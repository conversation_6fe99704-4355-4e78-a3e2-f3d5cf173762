<!-- BEGIN: main -->
<div class="bidding-detail-wrapper bidding-detail-wrapper-result">
    <h1 class="bidding-name">{DETAIL.title}</h1>

    <!-- BEGIN: management_unit_info -->
    <!-- BEGIN: dtnet_link_button -->
    <div class="pull-right">
        <a href="{DTNET_LINK_BUTTON}" class="btn btn-primary btn-xs" title="{LANG.profile_dtnet_title}"><i class="fa fa-link" aria-hidden="true"></i> {LANG.profile_dtnet}</a>
    </div>
    <!-- END: dtnet_link_button -->
    <h2 class="bidding-sub-title">{LANG.management_unit_info}: {CONTRACTOR.companyname}</h2>
    <div class="bidding-detail">
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.address}</div>
            <div class="c-val">{DETAIL.dia_chi}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.tax_code}</div>
            <div class="c-val">{DETAIL.ma_so_thue}</div>
        </div>
    </div>
    <!-- END: management_unit_info -->

    <h2 class="bidding-sub-title">{LANG.laboratory_info}</h2>
    <div class="bidding-detail">
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.lab_name}</div>
            <div class="c-val">{DETAIL.ten_phongtn}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.lab_address}</div>
            <div class="c-val">{DETAIL.dia_chi_phongtn}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.lab_code}</div>
            <div class="c-val">{DETAIL.ma_so_phongtn} {DETAIL.ma_so_phongtn_mr}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.allow_date}</div>
            <div class="c-val">{DETAIL.thoi_gian_cong_nhan}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.allow_content}</div>
            <div class="c-val">{DETAIL.noi_dung}</div>
        </div>
        <!-- BEGIN: postponed -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.postponed_date}</div>
            <div class="c-val red">{POSTPONED_DATE}</div>
        </div>
        <!-- END: postponed-->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.cap_nhat_lan_cuoi}</div>
            <div class="c-val">
                <div class="col-md-8">
                    {DETAIL.thoi_gian_boc} - {DETAIL.so_lan_boc_tin}
                </div>
                <!-- BEGIN: update -->
                <div class="col-md-4">
                    <a onclick="show_capcha()" href="">{LANG.cap_nhat_lai}</a>
                </div>
                <div class="col-md-16 capcha" style="display: none">
                    <!-- BEGIN: recaptcha -->
                    <div class="form-group">
                        <div class="middle text-center clearfix">
                            <div class="nv-recaptcha-default">
                                <div data-toggle="recaptcha" data-callback="verify_captcha" id="{RECAPTCHA_ELEMENT}"></div>
                            </div>
                            <script type="text/javascript">
                                function verify_captcha(e) {
                                    click_update_lab()
                                }
                            </script>
                        </div>
                    </div>
                    <!-- END: recaptcha -->
                </div>
                <!-- END: update -->
                <!-- BEGIN: login -->
                <div class="col-md-14">{LOGIN}</div>
                <!-- END: login -->
            </div>
        </div>
    </div>
    <h2 class="bidding-sub-title">{LANG.certificate_list_1}</h2>
    <div class="bidding-simple">
        <section class="block-bidding" style="margin-top: 10px">
            <table class="bidding-table">
                <thead>
                    <tr>
                        <th>{LANG.stt}</th>
                        <th>{LANG.decision_name}</th>
                        <th>{LANG.certification_period}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop -->
                    <tr>
                        <td data-column="{LANG.stt}" class="text-center"> {CHUNG_NHAN.stt}</td>
                        <td data-column="{LANG.decision_name}" class="text-center">
                            <a href="{CHUNG_NHAN.link}" data-toggle="tooltip" title="{CHUNG_NHAN.nguon}"> {CHUNG_NHAN.nguon} </a>
                        </td>
                        <td data-column="{LANG.certification_period}" class="text-center"> {CHUNG_NHAN.thoi_gian_cong_nhan}</td>
                    </tr>
                    <!-- END: loop -->
                </tbody>
            </table>
        </section>
    </div>
</div>
<!-- BEGIN: contractor -->
<div style="position: relative;">
    {IS_CONTRACTOR}
    {CONTRACTOR_INFO}
    {CONTRACTOR_LOCK}
</div>
<!-- END: contractor -->
<!-- BEGIN: solicitor -->
<div>
    {IS_SOCLICTOR}
</div>
<!-- END: solicitor -->
<!-- BEGIN: popup_login -->
{POPUP_LOGIN}
<!-- END: popup_login -->
<script type="text/javascript">
    var capcha = 0;
    function show_capcha() {
        if (capcha == 1) {
            $(".capcha").css("display", "none");
            capcha = 0;
        } else {
            $(".capcha").css("display", "block");
            capcha = 1;
        }
    }
    function click_update_lab() {
        $.ajax({
            type : "POST",
            url : "{URL_UPDATE}",
            data : {
                check : '{CHECKSESS_UPDATE}',
                update : '1'
            },
            success : function(res) {
                alert(res);
                window.location.href = window.location.href;
            }
        });
    }
</script>
<!-- END: main -->
