<!-- BEGIN: main -->
<link rel="stylesheet" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
{CHECK_LOGIN}
<form method="get" action="{ACTION}">
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="">{LANG.chontinhthanh}</label>
                <select name="tinhthanh" id="tinhthanh" class="form-control">
                    <option value="-1">---{LANG.bid_notify_status_4}---</option>
                    <!-- BEGIN: province -->
                        <option value="{ROW.id}" {ROW.selected}>{ROW.title}</option>
                    <!-- END: province -->
                </select>
            </div>
        </div>

        <div class="col-md-12">
            <div class="form-group">
                <label for="">{LANG.chonquyhoach}</label>
                <select name="id_phanloai" id="id_phanloai" class="form-control">
                    <option value="">---{LANG.bid_notify_status_4}---</option>
                    <!-- BEGIN: parentcat -->
                        <option value="{PARENTCAT.id}" {PARENTCAT.selected}>{PARENTCAT.title}</option>
                    <!-- END: parentcat -->
                </select>
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group">
                <label>{LANG.cancel_no}</label>
                <input type="text" name="soqd" value="{SEARCH.so_qd}" class="form-control">
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group">
                <button class="btn btn-info" name="timkiem" value="1" type="submit"><i class="fa fa-search" aria-hidden="true"></i> {LANG.search}</button>
            </div>
        </div>
    </div>
</form>
<!-- BEGIN: detail -->
<div id="myModal_QuyHoach" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <p class="modal-title"><a class="link__zom">{LANG.view_large_img}</a></p>
            </div>
            <div class="modal-body">
                <img src="" class="img__view_large">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{LANG.close}</button>
            </div>
        </div>
    </div>
</div>

<div class="title__tab_heading">
    <h1><span>{DETAIL.title}</span></h1>
</div>

<div class="mt-2 zoning-detail">
    <fieldset>
        <legend><h2>{LANG.motaquyhoach}</h2>:</legend>
        <div class="quyhoach__tt wrap__text">
            <!-- BEGIN: mota -->
            {DETAIL.mota}
            <!-- END: mota -->
        </div>
    </fieldset>

    <fieldset>
        <legend><h2>{LANG.ttquyhoach}</h2>:</legend>
        <div class="quyhoach__tt wrap__text">
            <!-- BEGIN: thongtin -->
            {DETAIL.thongtin}
            <!-- end: thongtin -->
        </div>
    </fieldset>

    <fieldset>
        <legend><h2>{LANG.thuyeminhqh}</h2>:</legend>
        <div class="quyhoach__tt wrap__text">
            <!-- BEGIN: thuyetminh -->
            {DETAIL.thuyetminh}
            <!-- END: thuyetminh -->
        </div>
    </fieldset>

    <fieldset>
        <legend><h2>{LANG.qdqlda}</h2>:</legend>
        <div class="quyhoach__tt wrap__text">
            <!-- BEGIN: quydinh -->
            <p class="thuyetminh">{DETAIL.quydinh}</p>
            <!-- END: quydinh -->
            <!-- <iframe src="https://quyhoach.xaydung.gov.vn/Images/editor/files/QHCHuongAn-Thuyetminh-08082016.pdf" frameborder="0" height="800px" width="100%"></iframe> -->
        </div>
    </fieldset>
</div>

<script type="text/javascript">
    $(document).ready(function() {
        document.querySelectorAll(".zoning-detail fieldset a").forEach(aTag => {
            let href = aTag.getAttribute("href");
            if (href && !/^https?:\/\//.test(href) && !href.startsWith("#")) {
                aTag.setAttribute("href", "https://" + href);
                aTag.setAttribute("rel", "noopener noreferrer nofollow");
                aTag.setAttribute("target", "_blank");
            }
        });
    });
</script>
<!-- END: detail -->

<!-- BEGIN: list -->
<div class="title__tab_heading">
    <h1><span>{TITLE_H1}</span></h1>
</div>

<!-- BEGIN: data -->
<div class="listQuyhoach">
    <div class="list">
        <!-- BEGIN: loop -->
            <div class="panel panel-default">
                <div class="panel-body featured">
                    <div class="featured__custom">
                        <div class="featured__custom_left">
                            <!-- BEGIN: image -->
                            <a href="{ROW.link_detail}"><img alt="{ROW.title}" src="{ROW.image}" class="img-thumbnail pull-left imghome featured__custom_left_img"></a>
                            <!-- END: image -->

                            <!-- BEGIN: no_image -->
                                <a href="{ROW.link_detail}"><img alt="{ROW.title}" src="{URL_NO_IMG}" class="img-thumbnail pull-left imghome featured__custom_left_img"></a>
                            <!-- END: no_image -->
                        </div>

                        <div class="featured__custom_right">
                            <h2>
                                <a href="{ROW.link_detail}">{ROW.title}</a>
                            </h2>
                            <div class="text-muted">
                                <ul class="list-unstyled">
                                    <li>
                                        <i class="fa fa-book icon__qh" aria-hidden="true"></i> {LANG.cancel_no}: <b>{ROW.so_qd}</b>
                                    </li>
                                    <li>
                                        <i class="fa fa-map-marker" aria-hidden="true"></i> {LANG.tinhthanh}: <a href="{ROW.link_tinhthanh}">{ROW.tinhthanh}</a>
                                    </li>
                                    <li>
                                        <i class="fa fa-th-large icon__qh" aria-hidden="true"></i> {LANG.phanloai}: <a href="{ROW.link_phanloai}">{ROW.phanloai}</a>
                                    </li>
                                    <li>
                                        <small><i class="fa fa-clock-o icon__qh" aria-hidden="true"></i> {LANG.tgupdatedata}: {ROW.addtime}</small>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <!-- END: loop -->
    </div>
    <!-- BEGIN: generate_page -->
    <div class="text-center">
        {NV_GENERATE_PAGE}
    </div>
    <!-- END: generate_page -->
</div>
<!-- END: data -->

<!-- BEGIN: nodata -->
    <p class="alert alert-warning listQuyhoach"><span class="list">{LANG.no_data}</span></p>
<!-- END: nodata -->
<!-- END: list -->
<script type="text/javascript">
    $(document).ready(function() {
        $("#tinhthanh").select2();
        $("#id_phanloai").select2();

        a = $(".content_anh").find('a.active');
        for (i=0; i < a.length; i++) {
            link = a.eq(i).find('img').attr('src');
            a.eq(i).attr("data-toggle", "modal");
            a.eq(i).addClass('zoom_img');
            a.eq(i).attr("data-target", "#myModal_QuyHoach");
        }

        $(".zoom_img").click(function() {
            tagimg = $(this).attr('tagimg');
            $("#myModal_QuyHoach").find('.img__view_large').attr('src', tagimg);
            $("#myModal_QuyHoach").find('.link__zom').attr('href', tagimg);
        });

        info_qh = $(".quyhoach__tt");
        for (i = 0; i < $(".quyhoach__tt").length; i++) {
            if (info_qh.eq(i).height() == 0) {
                info_qh.eq(i).html('<p class="alert alert-warning">{LANG.no_data}</p>')
            }
        }
    });
</script>
<!-- END: main -->
