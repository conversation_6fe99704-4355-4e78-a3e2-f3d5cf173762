<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css" rel="stylesheet" />

<section id="list-search-orgs" class="filter-{MODULE_NAME}">
    <form action="{FORM_ACTION}" method="get">
        <!-- BEGIN: no_rewrite -->
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
        <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
        <!-- END: no_rewrite -->

        <div class="row mt-1">
            <div class="form-row">
                <div class="form-group col-md-8">
                    <input type="text" class="form-control" name="query" value="{Q}" placeholder="{LANG.keyword}">
                </div>

                <div class="form-group col-md-8">
                    <input type="text" class="form-control" name="org_number" value="{ORG_NUMBER}" placeholder="{LANG.num}">
                </div>

                <div class="form-group col-md-8">
                    <input type="text" class="form-control" name="lab_name" value="{LAB_NAME}" placeholder="{LANG.org_name}">
                </div>

                <!-- BEGIN: search_chuquan -->
                <div class="form-group col-md-8 host">
                    <input type="text" class="form-control" name="host_name" value="{HOST_NAME}" placeholder="{LANG.host_name}">
                </div>
                <!-- END: search_chuquan -->
            </div>

            <div class="form-row">
                <!-- BEGIN: search_linhvuc -->
                <div class="form-group col-md-8 linhvuc">
                    <select title="{LANG.specialize}" name="linhvuc[]" class="form-control fselect2" multiple="multiple">
                        <!-- BEGIN: linhvuc -->
                        <option value="{LINHVUC.id}" {selected_linhvuc}>{LINHVUC.title}</option>
                        <!-- END: linhvuc -->
                    </select>
                </div>
                <!-- END: search_linhvuc -->

                <!-- BEGIN: search_province -->
                <div class="form-group col-md-8">
                    <select title="{LANG.province}" class="form-control fselect2" multiple="multiple" name="province[]">
                        <!-- BEGIN: province -->
                        <option value="{PROVINCE.id}" {selected_location}>{PROVINCE.title}</option>
                        <!-- END: province -->
                    </select>
                </div>
                <!-- END: search_province -->

                <div class="form-group col-md-8 status">
                    <select class="form-control fselect2" name="status">
                        <option value="100" selected>{LANG.status_select}</option>
                        <!-- BEGIN: status -->
                        <option value="{STATUS.key}" {selected_status}>{STATUS.value}</option>
                        <!-- END: status -->
                    </select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group col-md-24">
                <small><i>(*): {LANG.select_hint}</i></small>
            </div>
        </div>
        <div class="row">
            <div class="form-group col-md-4">
                <input class="btn btn-primary btn-block" type="submit" value="{LANG.search}">
            </div>
        </div>
    </form>

    <div class="border-bidding" id="bodycontent">
        <h2 class="title__tab_heading"><span>{PAGE_TITLE}</span></h2>
    </div>

    <!-- BEGIN: no_results -->
    <p class="alert alert-info text-center">{LANG.no_results}</p>
    <!-- END: no_results -->

    <table class="table-striped table-orgs">
        <thead>
            <!-- BEGIN: org_results -->
            <tr>
                <th class="stt">{LANG.stt}</th>
                <th class="vilas">{LANG.num} - {LANG.org_name}</th>
                <!-- BEGIN: show_host_unit -->
                <th class="host hide-on-mobile">{LANG.host_name}</th>
                <!-- END: show_host_unit -->
                <!-- BEGIN: show_specialize -->
                <th class="linhvuc hide-on-mobile">{LANG.specialize}</th>
                <!-- END: show_specialize -->
                <th class="province hide-on-mobile">{LANG.province}</th>
                <th class="status hide-on-mobile">{LANG.status}</th>
            </tr>
            <!-- END: org_results -->
        </thead>
        <tbody>
            <!-- BEGIN: org -->
            <tr>
                <td>{ORG.stt}</td>
                <td>
                    <a href="{ORG.detail_link}" data-toggle="tooltip" title="{ORG.organization_name}">
                        <h3><span class="lic-code organization_label">{ORG.organization_number}</span> {ORG.organization_name}</h3>
                    </a>
                    <br><i class="fa fa-map-marker" aria-hidden="true"></i>
                    {ORG.accreditation_location}
                    <br>
                    <!-- BEGIN: mobile_host_unit -->
                    <div class="host show-on-mobile">
                        <b>{LANG.host_name}:</b> {ORG.map_host_unit}
                    </div>
                    <!-- END: mobile_host_unit -->
                    <!-- BEGIN: mobile_specialize -->
                    <div class="linhvuc show-on-mobile">
                        <b>{LANG.specialize}:</b> {ORG.specialize}
                    </div>
                    <!-- END: mobile_specialize -->
                    <div class="province show-on-mobile">
                        <b>{LANG.province}:</b> {ORG.province}
                    </div>
                    <div class="status show-on-mobile">
                        <b>{LANG.status}:</b> {ORG.status}
                    </div>
                </td>
                <!-- BEGIN: host_unit -->
                <td class="host hide-on-mobile"> {ORG.map_host_unit}</td>
                <!-- END: host_unit -->
                <!-- BEGIN: specialize -->
                <td class="linhvuc hide-on-mobile">{ORG.specialize}</td>
                <!-- END: specialize -->
                <td class="hide-on-mobile">{ORG.province}</td>
                <td class="hide-on-mobile">{ORG.status}</td>
            </tr>
            <!-- END: org -->
        </tbody>
    </table>

    <!-- BEGIN: generate_page -->
    <div class="text-center">{NV_GENERATE_PAGE}</div>
    <!-- END: generate_page -->
</section>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        var selects = $('.fselect2');
        for (let i = 0; i < selects.length; i++) {
            $(selects[i]).select2({
                placeholder: $(selects[i]).attr('title') + '(*)',
                language: nv_lang_interface,
                width: 'resolve',
                minimumResultsForSearch: Infinity // Ẩn ô tìm kiếm
            });
        }
        $('.select2-search__field').addClass('form-control'); // Để style màu placeholder cho khớp
    });
</script>
<!-- END: main -->
