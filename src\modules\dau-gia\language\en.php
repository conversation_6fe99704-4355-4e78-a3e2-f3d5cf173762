<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 10 Nov 2020 06:56:08 GMT
 */
if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

$lang_translator['author'] = 'VINADES.,JSC (<EMAIL>)';
$lang_translator['createdate'] = '10/11/2020, 06:56';
$lang_translator['copyright'] = '@Copyright (C) 2020 VINADES.,JSC. All rights reserved';
$lang_translator['info'] = '';
$lang_translator['langtype'] = 'lang_module';

$lang_module['tukhoa'] = "Keyword";
$lang_module['diadiem'] = "Place";
$lang_module['sotuphap'] = "Department of Justice";
$lang_module['pleaseselect'] = "Please choose";
$lang_module['chondk'] = "You must choose at least 1 condition";
$lang_module['empty_data'] = "No Property Auction organizers were found for the request you searched for.";
$lang_module['select_all'] = 'All';
$lang_module['type_text_error'] = "Please enter at least 3 characters or leave it blank.";

$lang_module['tweet'] = 'Twitter';
$lang_module['main'] = 'Search page';
$lang_module['detail'] = 'See details';
$lang_module['search'] = 'Search';
$lang_module['organization'] = 'List of property auction organizers';
$lang_module['organization_at'] = 'List of property auction organizers at %s';
$lang_module['ctt'] = 'National web portal on property auctions';
$lang_module['note'] = 'Note';
$lang_module['note_status'] = 'Status';
$lang_module['post_time'] = 'Posted times';
$lang_module['date_public'] = 'Public posting date';
$lang_module['info_propertys'] = 'Information about property owners';
$lang_module['name_propertys'] = 'Property of';
$lang_module['address'] = 'Address';
$lang_module['info_tcdg'] = 'Information about the auction organization';
$lang_module['name_tcdg'] = 'Name of unit Auction organization';
$lang_module['phone'] = 'Phone number';
$lang_module['fax'] = 'Fax number';
$lang_module['info_sale'] = 'Auction info';
$lang_module['time_sale'] = 'Auction time';
$lang_module['location'] = 'Auction location';
$lang_module['stt'] = 'STT';
$lang_module['property_list'] = 'Property List';
$lang_module['name_property'] = 'Property name';
$lang_module['no'] = 'Amount';
$lang_module['location_pro'] = 'Location of property';
$lang_module['start_price'] = 'Starting price';
$lang_module['down_payment'] = 'Prepayment';
$lang_module['note'] = 'Note';
$lang_module['time_start'] = 'Auction registration start time';
$lang_module['time_end'] = 'Auction registration end time';
$lang_module['term'] = 'Conditions on how to register for the auction';
$lang_module['time_pay_er'] = 'Time to start depositing';
$lang_module['time_pay_end'] = 'Deposit payment end time';
$lang_module['time_data'] = 'Data update time';
$lang_module['turn'] = 'Updated';
$lang_module['tool'] = 'Tools';
$lang_module['utility'] = 'Utilities for you';
$lang_module['quality'] = 'Quality';
$lang_module['file'] = 'Attachment';
$lang_module['if_property'] = 'Auction property information';
$lang_module['auct_ozg'] = 'Information on receipt of application for registration of auction organization';
$lang_module['time_re'] = 'Reception time';
$lang_module['address_re'] = 'Receiving address';
$lang_module['contact'] = 'Contact information';
$lang_module['sql_rangetime_error'] = 'Note: Search data is limited to %d months (from date %s to date %s). If you need to search more, please split the search into multiple searches.';
$lang_module['thong_bao_cong_khai_dau_gia'] = 'Public announcement of the auction';
$lang_module['thong_bao_dau_gia'] = 'Auction Notice';
$lang_module['thong_bao_to_chuc_dau_gia'] = 'Notice of selection of auction organization';
$lang_module['date_bid'] = 'Public time';
$lang_module['open_date_bid'] = 'Public & Auction Date';
$lang_module['time_nop_hs'] = 'Time to apply';
$lang_module['to'] = 'to';
$lang_module['chance_for_you'] = 'Add-ons for you';
$lang_module['btn_filter'] = 'Create search filter';
$lang_module['btn_register'] = 'Register as a member';
$lang_module['currency'] = ' $';
$lang_module['from_date_to_date'] = '<b>From date %s to end date %s</b><br/>';
$lang_module['export_doc'] = 'Export DOC File';
$lang_module['maintenance'] = '<div class=\"alert alert-warning\">The website is currently undergoing an upgrade to transition from automatic data scraping to manual data posting. This upgrade may take several days. Please understand.</div>';
$lang_module['rss_description'] = 'Property name: %s; Address: %s';
$lang_module['copy_success'] = 'Copy link successful';

$lang_module['receive_email'] = 'Receive auction invitation by email';
$lang_module['receive_email_type_registration'] = 'How to register';
$lang_module['register'] = 'register for use';
$lang_module['renew'] = 'renew';
$lang_module['receive_email_content'] = 'To be one of the first <strong>users</strong> to receive by email invitations for bids of similar packages: "%s" as soon as they are posted load, please <a class="btn btn-default btn-xs" href="%s"><strong>%s package %s</strong></a> of DauThau.info.';
$lang_module['receive_email_content_plans'] = 'To be one of the <strong>first</strong> to receive email notifications of contractor selection plans similar to the "%s" plan as soon as they are approved. upload, please <a class="btn btn-default btn-xs" href="%s"><strong>register to use package %s</strong></a> of DauThau.info.';
$lang_module['vip6_form'] = 'Sign up for VIP6 package';
$lang_module['vip6_form_link'] = '<a href="%s">Sign up for VIP 6 package</a> <span>for details</span>';
$lang_module['vip6_renew'] = 'Renew VIP6';
$lang_module['vip6_renew_link'] = '<a href="%s">Renew VIP 6 package</a> <span>for details</span>';
$lang_module['link_detail'] = 'Attachment';
$lang_module['source'] = 'Source';
$lang_module['crawl'] = 'Update time';
$lang_module['content'] = 'Content';
$lang_module['tb_lien_quan'] = 'Related notice';
$lang_module['totalview'] = 'Viewed';
$lang_module['filter'] = 'Create a quick search filter for auction information';
$lang_module['filter_content'] = 'The function <strong>creates a search filter </strong>for members only. It helps you quickly and promptly search for auction notices similar to the package: "%s". Also you will get many other useful information when you sign up for our membership.';
$lang_module['number_dgv'] = 'Number of auctioneers';
$lang_module['so_quyet_dinh'] = 'Decision No';
$lang_module['day_quyet_dinh'] = 'Decision date';
$lang_module['so_giay_dkhd'] = 'Active Registration Number';
$lang_module['now_cap'] = 'Grant date';
$lang_module['so_cchn'] = 'Practising certificate ID';
$lang_module['auctioneerName'] = 'Agent';
$lang_module['email'] = 'Email';
$lang_module['branch_fast'] = 'Branch Info';
$lang_module['branch_fast_name'] = 'Branch Name';
$lang_module['chi_nhanh_dkhd'] = 'Active Registration Number';
$lang_module['chi_fast_timedkhd'] = 'Grant date';

$lang_module['van_phong'] = 'Representative office information';
$lang_module['van_phong_name'] = 'Office name';
$lang_module['van_phong_number'] = 'Text Number';
$lang_module['van_phong_time'] = 'Release date';

$lang_module['dgv'] = 'List of auctioneers belonging to the organization';
$lang_module['dgv_name'] = 'First name';
$lang_module['dgv_birthday'] = 'Birthday';
$lang_module['dgv_number_cchn'] = 'Certificate number';
$lang_module['dgv_time_cchn'] = 'Grant date';
$lang_module['dgv_number_card'] = 'Auctioner card number';
$lang_module['dgv_number_card_new'] = 'Auctioner card number';
$lang_module['dgv_time_card'] = 'Card issue date';
$lang_module['dgv_start_date'] = 'Work start date';
// $lang_module['empty_result'] = 'Không tìm thấy kết quả theo yêu cầu, vui lòng mở rộng thời gian tìm kiếm hoặc thay đổi thông số tìm kiếm.<br/>Xem chi tiết <a href="' . NV_MY_DOMAIN . '/news/tu-lieu-cho-nha-thau/huong-dan-cai-dat-bo-loc-de-tim-kiem-thong-tin-dau-thau-hieu-qua-nhat-158.html" >"Hướng dẫn sử dụng bộ lọc" </a> để tìm kiếm hiệu quả hơn.';
$lang_module['empty_result'] = 'The requested results were not found, please <a href="#" onclick="showAllTimeFilter()"> click here to expand the search time </a> or change the search parameters in the right column. </br>
See details <a href="%s" >"Filter User Guide" </a> for more efficient search.
<br/> If you need help searching, don&#39;t hesitate to contact our consultant Hotline: <a href="tel:0904634288">0904634288</a>, Email: <a href="javascript: send_mail_support_filter();"><EMAIL></a>, Message: <a target="_blank" href="https://m.me/dauthau.info">https://m.me/dauthau .info</a>
';

$lang_module['add_key_filter'] = 'Keyword <span class="red">"%s"</span> is not included in the filter. <br>You can create a filter with this keyword to find it faster next time <a href="%s" class="red">here</a> or see details <a class="red " href="%s">"How to use filters"</a> for more efficient search.';

$lang_module['department'] = 'List of Department of Justice';
$lang_module['department_title_seo'] = 'List of Department of Justice in Vietnam';
$lang_module['department_name'] = 'Department of Justice';
$lang_module['department_address'] = 'Address';
$lang_module['department_director'] = 'Director';
$lang_module['count_chinhanh'] = 'Number of organizations/branches';
$lang_module['count_tc'] = 'Number of organizations';
$lang_module['count_chinhanh'] = 'Number of branches';
$lang_module['count_dgv'] = 'Number of auctioneers';
$lang_module['website'] = 'Website';
$lang_module['roleinfo'] = 'Functions, tasks';

$lang_module['info'] = 'System Information';
$lang_module['info_redirect_click'] = 'Click here if the wait is long';
$lang_module['info_login'] = 'You need to login or register an account to operate in this area. The system will redirect you to the registration function, login in a moment.';

$lang_module['note_max_searchpage'] = 'The number of search results is exceeding the software&#39;s display limit of 100 pages. Please use the search engine to narrow your search or return to the previous page.';
$lang_module['note_wrong_page'] = 'Where did you get this link? I am the bearer, can&#39;t handle your request!';
$lang_module['notice'] = 'Notice';
$lang_module['back'] = 'Back';
$lang_module['notification_tb'] = 'Note: Search data is limited to %s month (from date %s to date %s). If you need to find more, please split into multiple searches. <a href="%s"><strong>Login</strong></a> to use if you want to have no time limit';

$lang_module['auctioneer'] = 'List of auctioneers';
$lang_module['auctioneer_title_seo'] = 'List of auctioneers in Vietnam';
$lang_module['auctioneer_name'] = 'First name';
$lang_module['auctioneer_info'] = 'Auctioneer info';
$lang_module['auctioneer_birthday'] = 'Birthday';
$lang_module['auctioneer_address'] = 'Registered place of permanent residence';
$lang_module['auctioneer_number_cchn'] = 'Practising certificate ID';
$lang_module['auctioneer_time_cchn'] = 'Grant date';
$lang_module['auctioneer_number_card'] = 'Auctioneer ID';
$lang_module['auctioneer_time_card'] = 'Card issue date';
$lang_module['auctioneer_bidder'] = 'Corporate Information';
$lang_module['auctioneer_empty'] = 'No auctioneer found for the request you searched for. ';
$lang_module['auctioneer_search_province'] = 'Province';
$lang_module['auctioneer_search_name'] = 'Full name';
$lang_module['auctioneer_search_org'] = 'Auction Organizer';
$lang_module['auctioneer_search_cchn'] = 'Practising certificate ID';
$lang_module['title_link_duplicate'] = '<a href="%s">here</a>';
$lang_module['dupicate_notification'] = 'Duplicate notification';
$lang_module['total'] = 'Total';
$lang_module['title_duplicate_dgv'] = 'Other auctioneer information is duplicate <span class="btn btn-warning">%s</span>';
$lang_module['title_duplicate_dgv_note'] = 'Note: Duplicate information is one or more information fields including: ';

$lang_module['panel_meeymap_title'] = 'Use Meeymap now to look up land parcel information';
$lang_module['panel_meeymap_content'] = 'Meey Map provides users with real estate planning information on a traffic map or satellite map, making it easy to access information and visualize the real estate landscape in a detailed and realistic manner. Meey Map is an essential tool for those seeking information about real estate and urban planning!';
$lang_module['panel_meeymap_content_vip6'] = 'Meey Map provides users with real estate planning information on a traffic map or satellite map, making it easy to access information and visualize the real estate landscape in a detailed and realistic manner. Meey Map is an essential tool for those seeking information about real estate and urban planning!<br>Meey Map offers a range of utilities such as: Easy real estate search with 4 methods, deep filter real estate search…';
$lang_module['register_now'] = 'Register now';

$lang_module['share'] = 'Share';
$lang_module['share_facebook'] = 'Share on Facebook';
$lang_module['share_twitter'] = 'Share on Twitter';
$lang_module['copy_link'] = 'Copy link';
$lang_module['updated_at'] = 'Updated at';
$lang_module['updated_histories'] = 'Update history';
$lang_module['field_phone'] = 'Phone number';
$lang_module['field_fax'] = 'Fax number';
$lang_module['field_email'] = 'Email';
$lang_module['field_name_bidder'] = 'Organization name';
$lang_module['field_address'] = 'Address';
$lang_module['field_so_cchn'] = 'CCHN number';
$lang_module['field_auctioneername'] = 'Representative';
$lang_module['field_quantityauctioneer'] = 'Number of auctioneers';
$lang_module['field_so_quyet_dinh'] = 'Decision number';
$lang_module['field_ngay_quyet_dinh'] = 'Decision date';
$lang_module['field_so_giay_dkhd'] = 'DKHD certificate number';
$lang_module['field_ngay_cap'] = 'Issue date';
$lang_module['not_found_organization'] = 'Auction organization not found';
$lang_module['not_found_data_log'] = 'No changes in data have been made';

$lang_module['modal_compare_title'] = 'Compare Auction Organization Change History';
$lang_module['select_type_compare'] = 'Select comparison mode';
$lang_module['compare_2_col'] = '2-column parallel mode';
$lang_module['compare_1_col'] = '1-column mode';
$lang_module['view_change_line'] = 'View line-by-line changes';
$lang_module['view_change'] = 'View changes';
$lang_module['error_check_notifi_ver'] = "Something wrong. The system will automatically reload the page";

$lang_module['first_publish'] = 'First publish';
$lang_module['change_org'] = 'Changes';
$lang_module['tooltip_org_version_check'] = 'Please check 2 versions to view changes';
$lang_module['error_view_download_doc'] = 'There was an error while exporting the doc file, please reload the browser or contact the technical department to check.';
$lang_module['title_view_download'] = '%s downloads';
$lang_module['meta_description_list_auctioneer'] = 'List of auctioneers in %s: detailed information about full name, practice certificate and permanent residence registration helps you easily look up and choose.';
$lang_module['land_use'] = 'Land use rights value at starting price';
$lang_module['price_asset'] = 'Starting price of the asset';
$lang_module['fee_level'] = 'Fee (including VAT)<br/>(VND/file)';
$lang_module['less_200'] = 'Under 1 billion VND';
$lang_module['from_200_to_500'] = 'From 1 billion VND to under 5 billion VND';
$lang_module['over_500'] = 'From 5 billion VND or more';
$lang_module['less_20'] = 'Under 100 million VND';
$lang_module['from_20_to_50'] = 'From 100 million VND to under 1 billion VND';
$lang_module['from_50_to_100'] = 'From 5 billion VND to under 10 billion VND';
$lang_module['over_10_bill'] = 'From 10 billion VND or more';
$lang_module['vip_register'] = 'Sign up for a VIP package';
$lang_module['view_auction_costs'] = 'To see the cost to participate in the auctions';
$lang_module['auction_costs'] = 'Auction participation costs';
$lang_module['auction_costs_desc'] = 'To better understand the regulations, please refer to the following document: <a ta href="https://daugia.net/vi/van-ban-dau-gia/detail/Thong-tu-so-03-2025-TT-BTC-cua-Bo-Tai-chinh-347/"><b>Circular No. 03/2025/TT-BTC of the Ministry of Finance</b></a>';
$lang_module['auctioncosts_desc'] = '<b>Amount of money collected from selling documents inviting participation in property auctions:</b>';
$lang_module['land_use_desc'] = 'In case of land use rights auction <a href="https://daugia.net/vi/van-ban-dau-gia/detail/Thong-tu-so-03-2025-TT-BTC-cua-Bo-Tai-chinh-347/"><i>(According to Clause 1, Article 6, Circular No. 03/2025/TT-BTC of the Ministry of Finance)</i></a>';
$lang_module['other_assets_desc'] = 'In case of property auction <a href="https://daugia.net/vi/van-ban-dau-gia/detail/Thong-tu-so-03-2025-TT-BTC-cua-Bo-Tai-chinh-347/"><i>(According to point b, clause 2, Article 6, Circular No. 03/2025/TT-BTC of the Ministry of Finance)</i></a>';
$lang_module['auction_costs_desc_1'] = 'a) For residential land for individuals:';
$lang_module['auction_costs_desc_2'] = 'b) In case of land use rights auction not covered by point a of this clause:';
$lang_module['auction_costs_desc_3'] = 'In case of auction of houses, land and constructions attached to land, radio frequency usage rights <a href="https://daugia.net/vi/van-ban-dau-gia/detail/Thong-tu-so-03-2025-TT-BTC-cua-Bo-Tai-chinh-347/"><i>(According to Point a, Clause 2, Article 6, Circular No. 03/2025/TT-BTC of the Ministry of Finance)</i></a>';
$lang_module['use_point_view'] = 'Use points';
$lang_module['point_view_auction_costs'] = 'Points to view auction costs';
$lang_module['error_point_auction_costs'] = 'You do not have enough points to purchase this feature (%s points), please <a href="%s" target="_blank">purchase more points</a> before viewing auction costs';
$lang_module['confirm_view_auction_costs'] = 'Do you want to use %s/%s points to view the auction costs of this auction notice?';
$lang_module['auction_costs_message'] = 'View auction costs ID: %s';
$lang_module['view_auction_cost_renew'] = 'You need to <a href="%s"><b>renew your VIP package</b></a> (except VIEWEB package) or <a href="#" class="btn_point_view_bidding_costs"><b>use points</b></a> to view the auction participation costs.';
$lang_module['view_auction_cost_regvip'] = 'You need to <a href="%s"><b>register for a VIP package</b></a> (except VIEWEB package) or <a href="#" class="btn_point_view_bidding_costs"><b>use points</b></a> to view the auction participation costs.';
$lang_module['renew_vip'] = 'Renew VIP software package';
$lang_module['log_in_up_auction_cost'] = 'You need to <strong><a href="javascript:void(0)" data-toggle="loginFormShow">Login</a></strong> or <strong><a href="%s">Register</a></strong> to see the cost of participating in the auction.';
$lang_module['or'] = 'Or';
$lang_module['ok'] = 'OK';
$lang_module['cancel'] = 'Cancel';
$lang_module['fee'] = 'Applicable rates';
$lang_module['login'] = 'Log in';
$lang_module['at'] = 'in';
$lang_module['update_info_last'] = 'Last updated at %s';
$lang_module['first_update_info'] = 'This is the first update';
$lang_module['reupdate'] = 'Re-update';
$lang_module['recapcha_title'] = 'Do you want to update again?';
$lang_module['recapcha_body'] = 'Please confirm you are not a robot!<br />';
$lang_module['confirm_crawl'] = 'In the process of updating the data, if there is any change in data, you will get +%s points (<a href=\'%spoints/#thuongdiem\'>details</a>) and no data changes %s points will be deducted (<a href=\'%spoints/#banggia1\'>details</a>). Are you sure you want to update again? <p class=\'text-center mt-1\'><button class=\'btn btn-default\' style=\'margin-right: 4px;\' type=\'button\' onclick=\'modalHide();\'>Cancel</button><button class=\'btn btn-primary\' id=\'accept_crawl\' data-text=\'In progress\'>Confirm</button> </p>';
$lang_module['update'] = 'Update';
$lang_module['update_ok'] = 'Your update request has been received. We will try to process this request as soon as possible';
$lang_module['update_err'] = 'The update failed.';
$lang_module['update_err_user_last'] = 'You submitted a news update request not long ago. We will only be able to process your update request after %s.';
$lang_module['update_err_last_one'] = 'You submitted a news update request not long ago. We will only be able to process your update request after %s.';
$lang_module['update_err_last'] = 'The news is waiting to be updated again.';
$lang_module['update_err_new'] = 'The news was updated not long ago. You can resubmit the request after %s.';
$lang_module['update_err_unknown'] = 'A technical error occurred while updating';
$lang_module['title_note_tbdg'] = 'The auction notice has the title: %s';
$lang_module['update_status_1'] = 'Your update request has been received. We will try to process this request as soon as possible.';
$lang_module['browser_refresh_5'] = 'The browser will refresh itself after 5 minutes';
$lang_module['fb_share'] = 'Share on Facebook';
$lang_module['tweet'] = 'Tweet';
$lang_module['vietnam'] = 'Vietnam';
$lang_module['title_num_organization'] = 'Total Auction Value of Organizations';
$lang_module['value_desire'] = 'Expected';
$lang_module['value_real'] = 'Maximum';
$lang_module['value_inde'] = 'Minimum';
$lang_module['time_static'] = 'Statistic Time';
$lang_module['view_chart_7_years'] = 'Display last 7 years';
$lang_module['view_chart_full'] = 'Display all';
$lang_module['title_kqlctc'] = 'Organization Selection Result';
$lang_module['title_value_chart'] = 'Total Value (million VND)';
$lang_module['title_minus_points'] = "The system has -%s points because you clicked to update the data of %s.";
$lang_module['bidorganization_schema_page_title'] = 'Notice of selection of auction organization';
$lang_module['bidorganization_schema_page_title_list'] = 'List of notices of selection of auction organizations';
$lang_module['organization_schema_page_title'] = 'Auction organization';
$lang_module['organization_schema_page_title_list'] = 'List of auction organizations';
$lang_module['department_schema_page_title'] = 'Department';
$lang_module['department_schema_page_title_list'] = 'List of departments';
$lang_module['auctioneer_schema_page_title'] = 'Auctioneer';
$lang_module['auctioneer_schema_page_title_list'] = 'List of auctioneers';
$lang_module['register_vip_to_view_price'] = 'Register for a VIP package to view';

$lang_module['result_select_organization'] = 'Results of auction organization selection';
$lang_module['result_join_select_organization'] = 'Results of participation in auction organization selection';
$lang_module['list_result_join_select_organization'] = 'List of results of participation in auction organization selection';
$lang_module['announcement_result_select_organization'] = 'Announcement of auction organization selection results';

$lang_module['resultorganization_title'] = 'Announcement title';
$lang_module['resultorganization_property'] = 'Property of';
$lang_module['resultorganization_time'] = 'Public time';
$lang_module['resultorganization_selected'] = 'Selected organization';
$lang_module['info_result_select_organization'] = 'Organization selection result information';

$lang_module['link_copy_successfully'] = 'Link copied successfully';
$lang_module['summary_bidder_bid'] = '<strong>Summary:</strong> Organization <strong>%s</strong> has posted <strong>%s</strong> auction notices. View full details <strong><a href="%s">here</a></strong>.';
$lang_module['summary_bidder_bid_novip'] = '<strong>Summary:</strong> Organization <strong>%s</strong> has posted <strong>%s</strong> auction notices. Some data has been hidden, to view full information please <strong><a href="%s">register for VIP 6 package</a></strong>.';
$lang_module['summary_bidder_select'] = '<strong>Summary:</strong> Organization <strong>%s</strong> has been selected as auction organization <strong>%s</strong> times. View full details <strong><a href="%s">here</a></strong>.';
$lang_module['summary_bidder_select_novip'] = '<strong>Summary:</strong> Organization <strong>%s</strong> has been selected as auction organization <strong>%s</strong> times. Some data has been hidden, to view full information please <strong><a href="%s">register for VIP 6 package</a></strong>.';
