<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES., JSC. All rights reserved
 * @Createdate 3/9/2010 23:25
 */
if (!defined('NV_MAINFILE'))
    die('Stop!!!');

if (!nv_function_exists('nv_search_blocks')) {

    function nv_search_blocks($block_config)
    {
        global $module_info, $module_name, $module_data, $nv_Request, $module_file, $nv_Cache, $global_array_config, $array_op, $db, $district_list, $ward_list, $province_list, $user_info, $global_array_vip, $nv_Lang, $db_config;
        global $global_config, $my_footer, $array_sort_options;

        $sort_type = $nv_Request->get_title('sort', 'get', 'default');
        $sort_type = in_array($sort_type, $array_sort_options) ? $sort_type : 'default';

        if (file_exists(NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file . "/block_search.tpl")) {
            $block_theme = $module_info['template'];
        } else {
            $block_theme = "default";
        }
        $is_vip_3 = false;
        $is_vip_x2 = false;
        $arr_customs_x2 = [];
        // Kiểm tra xem tk có là VIP hay không
        if (defined('NV_IS_USER')) {
            if (isset($global_array_vip[3])) {
                $arr_customs = $global_array_vip[3];
            } elseif (isset($global_array_vip[31])) {
                $arr_customs = $global_array_vip[31];
            }

            if (!empty($arr_customs)) {
                $is_vip_3 = true;
            }

            if (!empty($global_array_vip[89])) {
                $is_vip_x2 = true;
                $arr_customs_x2 = $global_array_vip[89];
            }
        }
        $xtpl = new XTemplate("block_search.tpl", NV_ROOTDIR . "/themes/" . $block_theme . "/modules/" . $module_file);
        $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
        $xtpl->assign('FORM_ACTION1', nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation", true));
        $xtpl->assign('FORM_ACTION2', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=search', true));
        $xtpl->assign('FORM_ACTION3', nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $module_info['alias']['listnation'], true));
        if ($global_config['rewrite_enable']) {
            $xtpl->assign('FORM_ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=search', true));
        } else {
            $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php');
            $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
            $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
            $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
            $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
            $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
            $xtpl->assign('MODULE_NAME', $module_name);
            $xtpl->parse('main.no_rewrite');
        }
        $isbussp = 0;
        if ($array_op[0] == 'listlocation') {
            $isbussp = 1;
        }
        $xtpl->assign('ISBUSSP', $isbussp);
        // Thông báo cấu hình số lượng từ khóa max
        $xtpl->assign('MAX_KEYWORD', $nv_Lang->getModule('tooltip_multi_keyword', $global_array_config['max_keyword']));

        // Data fix cứng của site dauthau.info.
        for ($i = 1; $i <= 5; $i++) {
            $array_lvkd[$i] = $nv_Lang->getModule('array_lvkd_' . $i);
        }

        for ($i = 0; $i <= 3; $i++) {
            $array_fee[$i] = $nv_Lang->getModule('array_fee_' . $i);
        }

        $keyword = $nv_Request->get_string('q', 'get', '');
        $industry = array_fill(0, 4, "");
        if (!empty($array_op) && preg_match('/^([A-Za-z0-9\-]+)\-([A-Za-z0-9]+)$/', $array_op[0], $m)) {
            if ($m[1] != 'page') {
                $str_l = strlen($m[2]);
                if ($str_l == 1) {
                    $industry[0] = $m[2];
                    $industry[1] = $industry[2] = $industry[3] = '';
                } elseif ($str_l == 3) {
                    $industry[0] = $m[2][0];
                    $industry[1] = $m[2];
                    $industry[2] = $industry[3] = '';
                } elseif ($str_l == 4) {
                    $industry[0] = $m[2][0];
                    $industry[1] = substr($m[2], 0, 3);
                    $industry[2] = $m[2];
                    $industry[3] = '';
                } elseif ($str_l >= 5) {
                    $industry[0] = $m[2][0];
                    $industry[1] = substr($m[2], 0, 3);
                    $industry[2] = substr($m[2], 0, 4);
                    $industry[3] = substr($m[2], 0, 5);
                }
            }
        } else {
            $industry[0] = $nv_Request->get_string('industry1', 'get', '');
            $industry[1] = $nv_Request->get_string('industry2', 'get', '');
            $industry[2] = $nv_Request->get_string('industry3', 'get', '');
            $industry[3] = $nv_Request->get_string('industry4', 'get', '');
        }
        $province = $nv_Request->get_int('province', 'get', -1);
        $district = $nv_Request->get_int('district', 'get', 0);
        $ward = $nv_Request->get_int('ward', 'get', 0);
        $businesstype = $nv_Request->get_int('businesstype', 'get', 0);
        $lvkd = $nv_Request->get_int('lvkd', 'get', 0);
        $is_advance = $nv_Request->get_int('is_advance', 'get', 0);

        // Ngày phê duyệt
        $sfrom_business = nv_substr($nv_Request->get_title('sfrom_business', 'get', ''), 0, 10);
        $sto_business = nv_substr($nv_Request->get_title('sto_business', 'get', ''), 0, 10);

        // Năm tham gia thầu
        if ($is_vip_x2) {
            if (!empty($arr_customs_x2['x2_adv_static_bibding']) || !empty($arr_customs_x2['x2_adv_static_result']) || !empty($arr_customs_x2['x2_adv_static_bidsecurity'])) {
                $bid_from = nv_substr($nv_Request->get_title('bid_from', 'get', '01/2010'), 0, 7);
                $bid_to = nv_substr($nv_Request->get_title('bid_to', 'get', nv_date('m/Y', NV_CURRENTTIME)), 0, 7);
            }
        }

        $fee = $nv_Request->get_int('fee', 'get', 0);
        $fee > 3 && $fee = 0;

        $a = $b = '';
        if (isset($array_op[1])) {
            $a = substr($array_op[1], 0, 1);
            $b = substr($array_op[1], 2);
            if (preg_match('/^([a-zA-Z0-9\-]+)\-([0-9]+)$/i', $b, $m)) {
                $b = $m[2];
            }
        }
        $module = "location";
        if ($array_op[0] == 'listlocation') {
            if ($a == "T") {
                $sql = "SELECT id, alias FROM " . NV_PREFIXLANG . "_" . $module . "_province WHERE status=1 AND id = " . $db->quote($b);
                $result = $db->query($sql);
                list ($_id, $alias) = $result->fetch(3);
                $province = $_id;
                $xtpl->assign('FORM_ACTION', nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation/T-" . $alias . '-' . $province, true));
            } elseif ($a == "H") {
                $sql = "SELECT id, idprovince, alias FROM " . NV_PREFIXLANG . "_" . $module . "_district WHERE status=1 AND id = " . $db->quote($b);
                $result = $db->query($sql);
                list ($_id, $idprovince, $alias) = $result->fetch(3);
                $district = $_id;
                $province = $idprovince;
                $xtpl->assign('FORM_ACTION', nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation/H-" . $alias . '-' . $district, true));
            } elseif ($a == "X") {
                $sql = "SELECT id, idprovince, iddistrict, alias FROM " . NV_PREFIXLANG . "_" . $module . "_ward WHERE status=1 AND id = " . $db->quote($b);
                $result = $db->query($sql);
                list ($_id, $idprovice, $iddistrict, $alias) = $result->fetch(3);
                $ward = $_id;
                $district = $iddistrict;
                $province = $idprovice;
                $xtpl->assign('FORM_ACTION', nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation/X-" . $alias . '-' . $ward, true));
            }
        }
        $code_level1 = "";
        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_industry WHERE level=1 AND status=1";
        $businesstype_list = $nv_Cache->db($sql, 'id', $global_array_config['module_industry']);
        $arr_industry = array();
        foreach ($businesstype_list as $businesstype_list_i) {
            $sl = "";
            if ($industry[0] == $businesstype_list_i['code']) {
                $code_level1 = $businesstype_list_i['code'];
                $sl = "selected='selected'";
            }
            $arr_industry[] = array(
                "id" => $businesstype_list_i['id'],
                "title" => $businesstype_list_i['title'],
                "code" => $businesstype_list_i['code'],
                "level" => $businesstype_list_i['level'],
                "sl" => $sl
            );
        }
        $count = 0;
        $html = "";
        foreach ($industry as $key => $val) {
            $count++;
            if ($count >= 1) {
                if ($val != "") {
                    $level_next = $count + 1;
                    $selectted = (isset($industry[$count])) ? $industry[$count] : 0;
                    if ($count <= 5) {
                        $onchange = "getValueIndustry_block('industry" . $count . "', " . $level_next . ", 'industry" . $level_next . "', '" . $val . "', '" . $selectted . "');";
                        $html .= "<script>" . $onchange . "</script>";
                    }
                }
            }
        }
        // businesstype
        $sql = "SELECT id, title FROM " . NV_PREFIXLANG . "_" . $module_data . "_businesstype ORDER BY weight";
        $businesstype_list = $nv_Cache->db($sql, 'id', $module_name);
        $arr_businesstype = array();
        foreach ($businesstype_list as $businesstype_list_i) {
            $sl = ($businesstype == $businesstype_list_i['id']) ? "selected='selected'" : "";
            $arr_businesstype[] = array(
                "id" => $businesstype_list_i['id'],
                "title" => $businesstype_list_i['title'],
                "sl" => $sl
            );
        }

        if ($ward > 0) {
            $sql = "SELECT id, idprovince, iddistrict FROM " . NV_PREFIXLANG . "_location_ward WHERE status=1 AND id = " . $ward;
            $result = $db->query($sql);
            list ($_id, $idprovice, $iddistrict) = $result->fetch(3);
            $ward = $_id;
            $district = $iddistrict;
            $province = $idprovice;
        } else if ($district > 0) {
            $sql = "SELECT id, idprovince FROM " . NV_PREFIXLANG . "_location_district WHERE status=1 AND id = " . $district;
            $result = $db->query($sql);
            list ($_id, $idprovince) = $result->fetch(3);
            $district = $_id;
            $province = $idprovince;
        }

        $xtpl->assign('TIMESTAMP', $global_config['timestamp']);
        $xtpl->assign('SEARCH_PROVINCE', $province);
        $xtpl->assign('SEARCH_DISTRICT', $district);
        $xtpl->assign('SEARCH_WARD', $ward);

        $xtpl->assign('CLASS_DISTRICT', $province >= 0 ? '' : ' hidden');
        $xtpl->assign('CLASS_WARD', $district > 0 ? '' : ' hidden');

        foreach ($arr_industry as $industry_i) {
            $xtpl->assign('sl_industry', $industry_i['sl']);
            $xtpl->assign('key_industry', $industry_i['code']);
            $xtpl->assign('val_industry', $industry_i['title']);
            $xtpl->parse('main.industry');
        }

        foreach ($arr_businesstype as $businesstype_i) {
            $xtpl->assign('sl_businesstype', $businesstype_i['sl']);
            $xtpl->assign('key_businesstype', $businesstype_i['id']);
            $xtpl->assign('val_businesstype', $businesstype_i['title']);
            $xtpl->parse('main.businesstype');
        }

        foreach ($array_lvkd as $__id => $__title) {
            $xtpl->assign('LVKD', [
                'key' => $__id,
                'title' => $__title,
                'selected' => $__id == $lvkd ? ' selected="selected"' : ''
            ]);
            $xtpl->parse('main.lvkd');
        }

        if (defined('NV_IS_MODADMIN') || $is_vip_3) {
            foreach ($array_fee as $__id => $__title) {
                $xtpl->assign('FEE', [
                    'key' => $__id,
                    'title' => $__title,
                    'selected' => $__id == $fee ? ' selected="selected"' : ''
                ]);
                $xtpl->parse('main.fee.fee_select');
            }
            $xtpl->parse('main.fee');
        }

        // Lấy và gán dữ liệu quốc gia
        $sql = "SELECT id, name, alias, code FROM " . $db_config['prefix'] . "_msc_nation ORDER BY name ASC";
        $array_nation = $nv_Cache->db($sql, 'alias', $module_name);

        $nation = $nv_Request->get_int('nation', 'get', 0);

        if ($province >= 0 || $district > 0 || $ward > 0) {
            $nation = 244;
        }

        if (($array_op[0] ?? '') == $module_info['alias']['listnation'] && isset($array_op[1])) {
            if (isset($array_nation[$array_op[1]])) {
                $nation = $array_nation[$array_op[1]]['id'];
            }
        }

        if (!empty($array_nation)) {
            foreach ($array_nation as $key => $value) {
                $xtpl->assign('NATION', array(
                    'key' => $value['id'],
                    'title' => $value['name'],
                    'alias' => $key,
                    'selected' => ($value['id'] == $nation) ? 'selected="selected"' : ''
                ));
                $xtpl->parse('main.nation');
            }
        }

        // Ngày phê duyệt
        $xtpl->assign('SFROM', $sfrom_business);
        $xtpl->assign('STO', $sto_business);

        if ($is_vip_x2) {
            if (!empty($arr_customs_x2['x2_adv_static_bibding']) || !empty($arr_customs_x2['x2_adv_static_result']) || !empty($arr_customs_x2['x2_adv_static_bidsecurity'])) {
                // Thời gian tham gia thầu
                $xtpl->assign('BID_FROM', $bid_from);
                $xtpl->assign('BID_TO', $bid_to);
                $xtpl->parse('main.advanced_search');
                $xtpl->parse('main.advanced_search_js');
            }
        }

        $advance_show = !empty($is_advance);
        if (!$advance_show) {
            $xtpl->parse('main.advance_bl_hide');
        }

        if ($advance_show) {
            $xtpl->assign('LANG_ADVANCE', $nv_Lang->getModule('search_simple'));
            $xtpl->assign('ADVANCE', 1);
            $xtpl->parse('main.advance_icon_1');
        } else {
            $xtpl->assign('LANG_ADVANCE', $nv_Lang->getModule('search_advance'));
            $xtpl->assign('ADVANCE', 0);
            $xtpl->parse('main.advance_icon_0');
        }

        $my_footer .= '<script type="text/javascript" src="' . NV_BASE_SITEURL . 'themes/dauthau/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js"></script>';
        $my_footer .= '<script type="text/javascript" src="' . NV_BASE_SITEURL . 'themes/dauthau/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.' . NV_LANG_INTERFACE . '.min.js"></script>';
        $my_footer .= '<link rel="stylesheet" type="text/css" href="' . NV_BASE_SITEURL . 'themes/dauthau/plugins/bootstrap-datepicker/css/bootstrap-datepicker.min.css">';
        $my_footer .= '<link rel="stylesheet" type="text/css" href="' . NV_BASE_SITEURL . 'themes/dauthau/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css">';

        foreach ($array_sort_options as $key) {
            $xtpl->assign('CURRENT_SORT_' . strtoupper(str_replace('-', '_', $key)), $sort_type === $key ? ' selected="selected"' : '');
        }
        $xtpl->assign('keyword', $keyword);
        $xtpl->parse('main');
        return $xtpl->text('main') . $html;
    }
}

if (defined('NV_SYSTEM')) {
    $content = nv_search_blocks($block_config);
}
