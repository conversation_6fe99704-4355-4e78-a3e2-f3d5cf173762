/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */

//------Lay gia tri nganh nghe
function getValueIndustry(nameselect, level, divid, val, selectted) {
    if (val == "") {
        var id = $("select[name=" + nameselect + "]").val();
    } else {
        var id = val;
    }
    for (var i = level; i <= 5; i++) {
        if (document.getElementById('industry' + i)) {
            document.getElementById('industry' + i).innerHTML = "";
        }

    }
    if (document.getElementById(divid)) {
        $.ajax({
            type: "POST",
            url: nv_base_siteurl + "index.php?" + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=post&num=' + nv_randomPassword(8),
            data: 'action=1&id=' + id + '&level=' + level + '&selectted=' + selectted,
            success: function(res) {
                $("#" + divid).html(res);
            }
        });
    }
}

function doaction() {
    var list_post = document.getElementById('list_post');
    var fa = list_post['idcheck[]'];
    var del_list = '';
    if (fa.length) {
        var k = 0;
        for (var i = 0; i < fa.length; i++) {
            if (fa[i].checked) {
                if (k == 0) {
                    del_list = fa[i].value;
                } else {
                    del_list = del_list + ',' + fa[i].value;
                }
                k++;
            }
        }
    }
    if (del_list == "" && fa.checked) {
        del_list = fa.value;
    }
    if (del_list == "") {
        alert('Bạn phải chọn ít nhât 1 dòng');
    } else {
        if (confirm(nv_is_change_act_confirm[0])) {
            $.ajax("post", script_name, nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=listpost&action=del&listid=' + del_list + '&num=' + nv_randomPassword(8), '', 'res');
        }
    }
}


function nv_del_post(id) {

    if (confirm(nv_is_change_act_confirm[0])) {
        $.ajax("post", script_name, nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=listpost&action=del&listid=' + id + '&num=' + nv_randomPassword(8), '', 'res');
    }

}

function nv_del_favoritecat(id) {

    if (confirm(nv_is_change_act_confirm[0])) {
        $.ajax("post", script_name, nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=listfavoritecat&action=del&listid=' + id + '&num=' + nv_randomPassword(8), '', 'res');
    }

}

function nv_del_favoritelist(id) {

    if (confirm(nv_is_change_act_confirm[0])) {
        $.ajax("post", script_name, nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=listfavorite&action=del&listid=' + id + '&num=' + nv_randomPassword(8), '', 'res');
    }

}

function doaction_favoritecat() {
    var list_post = document.getElementById('list_post');
    var fa = list_post['idcheck[]'];
    var del_list = '';
    if (fa.length) {
        var k = 0;
        for (var i = 0; i < fa.length; i++) {
            if (fa[i].checked) {
                if (k == 0) {
                    del_list = fa[i].value;
                } else {
                    del_list = del_list + ',' + fa[i].value;
                }
                k++;
            }
        }
    }
    if (del_list == "" && fa.checked) {
        del_list = fa.value;
    }
    if (del_list == "") {
        alert('Bạn phải chọn ít nhât 1 dòng');
    } else {
        if (confirm(nv_is_change_act_confirm[0])) {
            $.ajax("post", script_name, nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=listfavoritecat&action=del&listid=' + del_list + '&num=' + nv_randomPassword(8), '', 'res');
        }
    }
}


function doaction_favoritelist() {
    var list_post = document.getElementById('list_post');
    var fa = list_post['idcheck[]'];
    var del_list = '';
    if (fa.length) {
        var k = 0;
        for (var i = 0; i < fa.length; i++) {
            if (fa[i].checked) {
                if (k == 0) {
                    del_list = fa[i].value;
                } else {
                    del_list = del_list + ',' + fa[i].value;
                }
                k++;
            }
        }
    }
    if (del_list == "" && fa.checked) {
        del_list = fa.value;
    }
    if (del_list == "") {
        alert('Bạn phải chọn ít nhât 1 dòng');
    } else {
        if (confirm(nv_is_change_act_confirm[0])) {
            $.ajax("post", script_name, nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=listfavorite&action=del&listid=' + del_list + '&num=' + nv_randomPassword(8), '', 'res');
        }
    }
}

function res(res) {
    if (res == "OK") {
        window.location.href = window.location.href;
    }
}

//------Lay gia tri nganh nghe
function getValueIndustry_block(nameselect, level, divid, val, selectted) {
    if (val == "") {
        var id = $("select[name=" + nameselect + "]").val();
    } else {
        var id = val;
    }
    for (var i = level; i <= 5; i++) {
        if (document.getElementById('industry' + i)) {
            document.getElementById('industry' + i).innerHTML = "";
        }

    }
    if (document.getElementById(divid) && id != 0) {
        $.ajax({
            type: "POST",
            url: url_load_remote + '&' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=businesslistings&' + nv_fc_variable + '=loadindustry&nocache=' + new Date().getTime(),
            data: 'id=' + id + '&level=' + level + '&selectted=' + selectted,
            success: function(res) {
                $("#" + divid).html(res);
            }
        });
    }
}

function getValueICB_block(nameselect, level, divid, val, selectted) {
    if (val == "") {
        var id = $("select[name=" + nameselect + "]").val();
    } else {
        var id = val;
    }
    if (level < 4) {
        for (var i = level; i <= 3; i++) {
            if ($('#industry_' + i).length) {
                $('#industry_' + i).html("");
                $('#industry_' + i).parent().parent().addClass('hidden');
            }
        }
        if ($('#' + divid).length && id != 0) {
            $.ajax({
                type: "POST",
                url: url_load_remote + '&' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=businesslistings&' + nv_fc_variable + '=loadicb&nocache=' + new Date().getTime(),
                data: 'id=' + id + '&level=' + level + '&selectted=' + selectted,
                success: function(res) {
                    $("#" + divid).html(res);
                    $("#" + divid).parent().parent().removeClass('hidden');
                    $("[name='industry']").val(id);
                }
            });
        }
    } else {
        $("[name='industry']").val(id);
    }
    if (id == '') {
        $("[name='industry']").val(level == 1 ? '' : $('[name="industry_' + (level - 2) + '"]').find(":selected").val());
    }
}

function nv_sendtoemail_favoritelist(id) {

    if (confirm(nv_is_change_act_confirm[0])) {
        $.ajax("post", script_name, nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=listfavoritecat&action=sendtomail&catid=' + id + '&num=' + nv_randomPassword(8), '', 'nv_sendtoemail_favoritelist_res');
    }

}

function nv_sendtoemail_favoritelist_res(res) {
    alert(res);
    return;
}

function nv_exporthtml_favoritelist(id) {

    if (confirm(nv_is_change_act_confirm[0])) {
        location.href = script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=export&type=html&catid=' + id + '&num=' + nv_randomPassword(8);;
    }
}

function nv_exportexcecl_favoritelist(id) {

    if (confirm(nv_is_change_act_confirm[0])) {
        var sendemail = (confirm('Gửi về email của bạn ?')) ? 1 : 0;
        if (sendemail == 1) {
            $.ajax("get", script_name, nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=export&type=excecl&catid=' + id + '&sendemail=' + sendemail + '&num=' + nv_randomPassword(8), '', 'nv_sendtoemail_favoritelist_res');
        } else {
            location.href = script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=export&type=excecl&catid=' + id + '&sendemail=' + sendemail + '&num=' + nv_randomPassword(8);;
        }
    }
}

function nv_validForm(a) {
    $(".has-error", a).removeClass("has-error");
    var c = 0;
    $(a).find(".required").each(function() {
        $(this).val(trim(strip_tags($(this).val())));
        if (!nv_validCheck(this)) return c++, $(".tooltip-current", a).removeClass("tooltip-current"), $(this).addClass("tooltip-current").attr("data-current-mess", $(this).attr("data-mess")), nv_validErrorShow(this), !1
    });
    c || ($(a).find("[type='submit']").prop("disabled", !0), $.ajax({
        type: $(a).prop("method"),
        cache: !1,
        url: $(a).prop("action"),
        data: $(a).serialize(),
        dataType: "json",
        success: function(b) {
            change_captcha('.fcode');
            "error" == b.status && "" != b.input ? ($(".tooltip-current", a).removeClass("tooltip-current"), $(a).find("[name=" + b.input + "]").each(function() {
                $(this).addClass("tooltip-current").attr("data-current-mess", b.mess);
                nv_validErrorShow(this)
            }), setTimeout(function() {
                $(a).find("[type='submit']").prop("disabled", !1)
            }, 1E3), (nv_is_recaptcha && change_captcha())) : ($("input,select,button,textarea", a).prop("disabled", !0), "error" == b.status ? $(a).next().html(b.mess).removeClass("alert-info").addClass("alert-danger").show() : $(a).next().html(b.mess).removeClass("alert-danger").addClass("alert-info").show(), $("[data-mess]").tooltip("destroy"), setTimeout(function() {
                $(a).next().hide();
                $("input,select,button,textarea", a).not(".disabled").prop("disabled", !1);
                nv_validReset(a);
                (nv_is_recaptcha && change_captcha());
            }, 5E3))
        }
    }));
    return !1
};

var map;
var marker;

function googlemapload(c, a, e) {
    var zoom = e;
    var Clat = c;
    var Clng = a;
    map = new google.maps.Map(document.getElementById("googlemap"),{
        zoom: zoom,
        center: {
            lat: Clat,
            lng: Clng
        }
    });

    // Init default marker
    marker = new google.maps.Marker({
        map: map,
        position: new google.maps.LatLng(Clat,Clng),
        draggable: false,
        animation: google.maps.Animation.DROP
    });

    // Event on zoom map
    google.maps.event.addListener(map, 'zoom_changed', function() {
        $('#url_google_map').val("http://maps.google.com/maps?ll=" + map.getCenter().lat().toFixed(5) + "," + map.getCenter().lng().toFixed(5) + "&z=" + map.getZoom());
    });

    // Event on change center map
    google.maps.event.addListener(map, 'center_changed', function() {
        $('#url_google_map').val("http://maps.google.com/maps?ll=" + map.getCenter().lat().toFixed(5) + "," + map.getCenter().lng().toFixed(5) + "&z=" + map.getZoom());
        marker.setPosition(new google.maps.LatLng(map.getCenter().lat(), map.getCenter().lng()));
    });
}

function showAddress(c) {
    var geocoder = new google.maps.Geocoder();
    if (document.getElementById("sonha").value != "")
        c = document.getElementById("sonha").value + ', ' + c;
    geocoder.geocode({'address': c}, function(results, status) {
        if (status === 'OK') {
            map.setCenter(results[0].geometry.location);
            marker.setPosition(results[0].geometry.location);
            $('#url_google_map').val("http://maps.google.com/maps?ll=" + map.getCenter().lat().toFixed(5) + "," + map.getCenter().lng().toFixed(5) + "&z=" + map.getZoom());
        } else {
            $('#url_google_map').val("");
        }
    });
};

$(function() {
    $('[data-toggle="butip"]').tooltip({
        container: 'body',
        animation: false,
        html: true,
        template: '<div class="butip-wrap tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',
        placement: 'top',
        title: function() {
            return '<div class="butip-item"><img src="' + $(this).data('tipi') + '"/></div>';
        }
    });
});

function convertToSlug(text) {
    text = text.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    return text
        .toLowerCase()
        .replace(/đ/g, 'd')          // Đổi 'đ' thành 'd'
        .trim()
        .replace(/\s+/g, '-')        // Thay thế khoảng trắng bằng dấu gạch ngang
        .replace(/[^a-z0-9-]/g, '')  // Loại bỏ các ký tự không phải chữ cái và số
        .replace(/-+/g, '-');        // Loại bỏ dấu gạch ngang thừa
}

$(document).ready(function() {
    var selProvince = $('select[name="province"]');
    var selDistrict = $('select[name="district"]');
    var selWard = $('select[name="ward"]');
    // Load ra tỉnh
    function loadProvince() {
        selProvince.prop('disabled', true);
        selDistrict.prop('disabled', true);
        selWard.prop('disabled', true);
        $.ajax({
            url: domain_load_remote + 'data/config/location-province-' + nv_lang_interface + '.json?t=' + selProvince.data('timestamp'),
            cache: true,
            dataType: 'json',
            method: 'GET',
            success: function(json) {
                var html = '<option value="-1">' + selProvince.data('default') + '</option>';
                // Thêm điều kiện kiểm tra nv_func_name
                if (nv_func_name == 'stock') {
                    html += '<option data-alias="' + convertToSlug(selProvince.data('unclassified')) + '" value="0"' + (selProvince.data('selected') == 0 ? ' selected="selected"' : '') + '>' + selProvince.data('unclassified') + '</option>';
                }
                $.each(json, function(id, title) {
                    html += '<option data-alias="' + convertToSlug(title) + '" value="' + id + '"' + (id == selProvince.data('selected') ? ' selected="selected"' : '') + '>' + title + '</option>';
                });
                selProvince.prop('disabled', false);
                selProvince.html(html).select2({
                    'width' : '100%'
                });
                loadDistrict();
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(jqXHR, textStatus, errorThrown);
            }
        });
    }

    // Load ra huyện
    function loadDistrict() {
        selDistrict.prop('disabled', true);
        selWard.prop('disabled', true);
        if (selProvince.val() == 0 || selProvince.val() == -1) {
            selDistrict.prop('disabled', false);
            selDistrict.html('<option value="0">' + selDistrict.data('default') + '</option>').select2({
                'width' : '100%'
            });
            selDistrict.parent().addClass('hidden');
            loadWard();
            return;
        }
        $.ajax({
            url: domain_load_remote + 'data/config/location-district-' + nv_lang_interface + '.json?t=' + selDistrict.data('timestamp'),
            cache: true,
            dataType: 'json',
            method: 'GET',
            success: function(json) {
                var html = '<option value="0">' + selDistrict.data('default') + '</option>';
                if (typeof json[selProvince.val()] != 'undefined') {
                    json = json[selProvince.val()];
                    $.each(json, function(id, title) {
                        html += '<option data-alias="' + convertToSlug(title) + '" value="' + id + '"' + (id == selDistrict.data('selected') ? ' selected="selected"' : '') + '>' + title + '</option>';
                    });
                }
                selDistrict.prop('disabled', false);
                selDistrict.parent().removeClass('hidden');
                selDistrict.html(html).select2({
                    'width' : '100%'
                });
                loadWard();
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(jqXHR, textStatus, errorThrown);
            }
        });
    }

    // Load ra xã
    function loadWard() {
        selWard.prop('disabled', true);
        if (selDistrict.val() == 0) {
            selWard.prop('disabled', false);
            selWard.html('<option value="0">' + selWard.data('default') + '</option>').select2({
                'width' : '100%'
            });
            selWard.parent().addClass('hidden');
            return;
        }
        $.ajax({
            url: domain_load_remote + 'data/config/location-ward-' + nv_lang_interface + '.json?t=' + selWard.data('timestamp'),
            cache: true,
            dataType: 'json',
            method: 'GET',
            success: function(json) {
                var html = '<option value="0">' + selWard.data('default') + '</option>';
                if (typeof json[selDistrict.val()] != 'undefined') {
                    json = json[selDistrict.val()];
                    $.each(json, function(id, title) {
                        html += '<option data-alias="' + convertToSlug(title) + '" value="' + id + '"' + (id == selWard.data('selected') ? ' selected="selected"' : '') + '>' + title + '</option>';
                    });
                }
                selWard.prop('disabled', false);
                selWard.parent().removeClass('hidden');
                selWard.html(html).select2({
                    'width' : '100%'
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(jqXHR, textStatus, errorThrown);
            }
        });
    }
    if (selProvince.length) {
        loadProvince();
    }
    if (selProvince.length && selDistrict.length && selWard.length) {
        selProvince.on('change', function() {
            if ($(this).attr("data-flag") == 1) {
                loadDistrict();
            }
        });
        selDistrict.on('change', function() {
            if ($(this).attr("data-flag") == 1) {
                loadWard();
            }
        });
    }
});

function fastlink(a) {
    return fastlink_buy_download(a, $("#confirm"), "downfile")
}
function fastlink_buy_download(a, obj, op) {
    confirmDialog($(a).data("confirm"), function() {
        $.ajax({
            type: "POST",
            url: nv_base_siteurl + "index.php?" + nv_lang_variable + "=" + nv_lang_data + "&" + nv_name_variable + "=" + nv_module_name + "&" + nv_fc_variable + "=" + nv_func_name + "&nocache=" + (new Date).getTime(),
            data: op + "=1&type=" + $(a).data("type") + "&id=" + $(a).data("id") + "&checkess=" + checkess,
            dataType: "json",
            success: function(b) {
                var mess = b.mess;
                if ("error" == b.res) {
                    if (typeof b.button_callback !== "undefined" && "buy_points" == b.button_callback) {
                        mess += '<p class="text-center"><a class="btn btn-danger" href="' + b.url + '" target="_blank">' + b.button_mess + '</a></p>';
                    }
                }
                modalShow("", mess, () => {}, () => {
                    location.reload();
                });

                if ("error" != b.res) {
                    setTimeout(() => {
                        $("#sitemodal").modal("hide");
                    }, 5000);
                }
            }
        })
    });
    return !1
}

function confirmDialog(message, onConfirm) {
    var a = $("#confirm"),
        b = function() {
            a.modal("hide")
        };
    $("body").app
    a.modal("show");
    $(".modal-body", a).empty().html(message);
    $(".ok", a).unbind().one("click", onConfirm).one("click", b);
    $(".cancel", a).unbind().one("click", b)
}
