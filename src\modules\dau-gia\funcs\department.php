<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 10 Nov 2020 06:56:08 GMT
 */

if (!defined('NV_IS_MOD_DAU_GIA')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('department_title_seo');

$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('department'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op, true)
);

// Khởi tạo mảng chứa các tham số tìm kiếm
$array_search = [];
$array_search['province'] = $nv_Request->get_int('province', 'get', 0);
$array_search['department_name'] = $nv_Request->get_title('department_name', 'get', '');
$array_search['director'] = $nv_Request->get_title('director', 'get', '');
$array_search['min_chinhanh'] = $nv_Request->get_absint('min_chinhanh', 'get', 0);
$array_search['max_chinhanh'] = $nv_Request->get_absint('max_chinhanh', 'get', 0);
$array_search['min_dgv'] = $nv_Request->get_absint('min_dgv', 'get', 0);
$array_search['max_dgv'] = $nv_Request->get_absint('max_dgv', 'get', 0);

$location_only = false;
$base_url_location = '';

// Kiểm tra nếu bất kỳ tham số tìm kiếm nào được đặt
if (
    !empty($array_search['department_name']) || !empty($array_search['director']) ||
    !empty($array_search['min_chinhanh']) || !empty($array_search['max_chinhanh']) ||
    !empty($array_search['min_dgv']) || !empty($array_search['max_dgv'])
) {
    $nv_BotManager->setPrivate(); // Issue: https://vinades.org/dauthau/dauthau.info/-/issues/3341
} else {
    $province_id = 0;

    if (preg_match('/^T\-([a-zA-Z0-9\-]+)\-([0-9]+)$/i', ($array_op[1] ?? ''), $m)) {
        $province_id = intval($m[2]);
    } elseif ($array_search['province'] > 0) {
        $province_id = $array_search['province'];
    }

    if ($province_id > 0) {
        $sql = "SELECT id, alias, title FROM " . NV_PREFIXLANG . "_location_province WHERE status=1 AND id = " . $db->quote($province_id);
        $result = $db->query($sql);
        if ($row = $result->fetch()) {
            $array_search['province'] = $row['id'];
            $base_url_location = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/T-' . $row['alias'] . '-' . $array_search['province'];
            $location_only = true;
            $page_title = $nv_Lang->getModule('department') . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'];

            if (!empty($module_info['funcs'][$op]['description'])) {
                $description = $module_info['funcs'][$op]['description'] . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'];
                $meta_property['og:description'] = $description;
            }
        }
    }
}

$where = [];
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;

if (!empty($array_search['province'])) {
    $where[] = "id_province=" . $array_search['province'];
    $base_url .= "&amp;province=" . $array_search['province'];
}
if (!empty($array_search['department_name'])) {
    $where[] = "fullname LIKE '%" . $db->dblikeescape($array_search['department_name']) . "%'";
    $base_url .= "&amp;department_name=" . urlencode($array_search['department_name']);
}
if (!empty($array_search['director'])) {
    $where[] = "director LIKE '%" . $db->dblikeescape($array_search['director']) . "%'";
    $base_url .= "&amp;director=" . urlencode($array_search['director']);
}
if (!empty($array_search['min_chinhanh'])) {
    $where[] = "counttcdgts >= " . $array_search['min_chinhanh'];
    $base_url .= "&amp;min_chinhanh=" . $array_search['min_chinhanh'];
}
if (!empty($array_search['max_chinhanh'])) {
    $where[] = "counttcdgts <= " . $array_search['max_chinhanh'];
    $base_url .= "&amp;max_chinhanh=" . $array_search['max_chinhanh'];
}
if (!empty($array_search['min_dgv'])) {
    $where[] = "countdgv >= " . $array_search['min_dgv'];
    $base_url .= "&amp;min_dgv=" . $array_search['min_dgv'];
}
if (!empty($array_search['max_dgv'])) {
    $where[] = "countdgv <= " . $array_search['max_dgv'];
    $base_url .= "&amp;max_dgv=" . $array_search['max_dgv'];
}

$module_config_view = $module_config[$module_name];
$perpage = 10;
$page = $nv_Request->get_page('page', 'get', 1);

$db->sqlreset()
    ->select('COUNT(depofjusticeid)')
    ->from('nv4_dau_gia_deparment');

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}

$sth = $db->prepare($db->sql());
$sth->execute();
$total = $sth->fetchColumn();

if ($location_only && !empty($base_url_location)) {
    $base_url = $base_url_location;
}

$urlappend = '&amp;page=';
betweenURLs($page, ceil($total / $perpage), $base_url, $urlappend, $prevPage, $nextPage);

$db->select('*')
    ->order('depofjusticeid DESC')
    ->limit($perpage)
    ->offset(($page - 1) * $perpage);
$sth = $db->prepare($db->sql());
$sth->execute();
$i = ($page - 1) * $perpage;
$array_data = [];
while ($view = $sth->fetch()) {
    $view['stt'] = $i + 1;
    $array_data[$view['depofjusticeid']] = $view;
    $i++;
}

$generate_page = nv_generate_page($base_url, $total, $perpage, $page);

// lấy page_url
$page_url = $base_url;
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}
$canonicalUrl = getCanonicalUrl($page_url);

// Xử lý title, description phân trang
if ($page > 1) {
    $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
    if (!empty($module_info['funcs'][$op]['description'])) {
        $description = $module_info['funcs'][$op]['description'] .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
        $meta_property['og:description'] = $description;
    } elseif (!empty($description)) {
        $description .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
        $meta_property['og:description'] = $description;
    }
}

$contents = nv_theme_dau_gia_department($array_data, $generate_page, $module_config_view, $total);

// Schema: CollectionPage
$my_schema_items = [];
$pos = 1;
foreach ($array_data as $row) {
    $my_schema_items[] = [
        '@type' => 'ListItem',
        'position' => $pos,
        'item' => [
            '@context' => 'https://schema.org',
            '@type' => 'GovernmentOffice',
            'identifier' => DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/' . $op . '/' . change_alias($row['fullname']) . '-' . $row['depofjusticeid'] . '.html',
            'name' => $row['fullname'],
            'alternateName' => $row['licenseno'],
            'foundingDate' => $row['licensedate'],
            'telephone' => $row['phonenumber'],
            'faxNumber' => $row['faxnumber'],
            'email' => $row['email'],
            'address' => [
                '@type' => 'PostalAddress',
                'streetAddress' => $row['address'],
                'addressLocality' => $db->query("SELECT title FROM " . $db_config['prefix'] . "_vi_location_district WHERE id=" . $row['id_district'])->fetchColumn(),
                'addressRegion' => $db->query("SELECT title FROM " . $db_config['prefix'] . "_vi_location_province WHERE id=" . $row['id_province'])->fetchColumn(),
                'addressCountry' => 'VN'
            ],
            'url' => $row['website'],
            'employee' => [
                '@type' => 'Person',
                'name' => $row['director'],
                'jobTitle' => $row['roleinfo']
            ]
        ]
    ];
    $pos++;
}
$nv_schemas[] = [
    '@context' => 'https://schema.org',
    '@type' => 'CollectionPage',
    'name' => ($page > 1) ? $nv_Lang->getModule('department_schema_page_title') . NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page : $nv_Lang->getModule('department_schema_page_title'),
    'url' => $canonicalUrl,
    'mainEntity' => [
        '@type' => 'ItemList',
        'name' => ($page > 1) ? $nv_Lang->getModule('department_schema_page_title_list') . NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page : $nv_Lang->getModule('department_schema_page_title_list'),
        'itemListElement' => $my_schema_items
    ]
];

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
