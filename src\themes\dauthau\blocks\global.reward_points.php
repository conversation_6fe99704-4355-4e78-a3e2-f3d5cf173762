<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jan 17, 2011 11:34:27 AM
 */
if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

use NukeViet\Point\Point;

if (!nv_function_exists('nv_reward_points')) {

    function nv_reward_points()
    {
        global $global_config, $db, $user_info, $db_config, $nv_Request, $page_url, $nv_Lang;

        if (!defined('NV_IS_USER')) {
            return '';
        }

        if (file_exists(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/blocks/global.reward_points.tpl')) {
            $block_theme = $global_config['module_theme'];
        } elseif (file_exists(NV_ROOTDIR . '/themes/' . $global_config['site_theme'] . '/blocks/global.reward_points.tpl')) {
            $block_theme = $global_config['site_theme'];
        } else {
            $block_theme = 'default';
        }
        $xtpl = new XTemplate('global.reward_points.tpl', NV_ROOTDIR . '/themes/' . $block_theme . '/blocks');
        $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_global);
        $xtpl->assign('AJAX_URL', nv_url_rewrite($page_url ?? (NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA), true));
        $xtpl->assign('URL_CRM_SITE', URL_CRM_SITE);
        $xtpl->assign('NV_LANG_INTERFACE', NV_LANG_INTERFACE);

        /**
         *
         * @since 14/10/2022 by dungpt: Block này cho load qua ajax.
         *        Để đảm bảo load trang xong thì mới hiển thị. Vì message chỉ hiển thị 1 lần
         *        nếu người dùng không đợi load trang xong mà chuyển sang trang khác thì sẽ bị miss thông báo.
         */
        $my_points = Point::getMyPoint();
        // $my_points['have_msg'] = 1;
        if (empty($my_points['have_msg'])) {
            return '';
        }

        if (isset($_POST['load_reward_point_message']) and $nv_Request->get_title('load_reward_point_message', 'post', '') === NV_CHECK_SESSION) {
            $messages = Point::getRewardMessages(0, 10);
            if (empty($messages)) {
                nv_htmlOutput('');
            }

            // Lấy thông tin tùy biến của thành viên
            $row_field_old = $db->query("SELECT * FROM " . NV_USERS_GLOBALTABLE . "_info WHERE userid =" . $user_info['userid'])->fetch();

            // Lặp message nhóm theo expired_time
            $expired_time = 0;
            $array = [];

            /*
             * Thông báo online 10 phút để nhận thêm điểm nếu
             * - Trong thông báo có type=3
             * - Trong thông báo không có type = 1 trong các loại 5,6,7
             */
            $online10 = '';

            foreach ($messages as $message) {
                if (empty($expired_time) or $expired_time != $message['expired_time']) {
                    $expired_time = $message['expired_time'];
                }
                $_aray_mess = json_decode($message['message_reward'], true);
                if (is_array($_aray_mess)) {
                    if (isset($_aray_mess[NV_LANG_DATA])) {
                        $message['message'] = $_aray_mess[NV_LANG_DATA];
                    } else {
                        $arr_value = array_values($_aray_mess);
                        $message['message'] = array_shift($arr_value);
                    }
                } else {
                    $message['message'] = $message['message_reward'];
                }

                $array[$expired_time][] = $message;
                if ($online10 !== false and !empty($message['online10'])) {

                    $_arr_online10 = json_decode($message['online10'], true);
                    if (is_array($_arr_online10)) {
                        if (isset($_arr_online10[NV_LANG_DATA])) {
                            $message['online10_mess'] = $_arr_online10[NV_LANG_DATA];
                        } else {
                            $arr_value = array_values($_arr_online10);
                            $message['online10_mess'] = array_shift($arr_value);
                        }
                    } else {
                        $message['online10_mess'] = $message['online10'];
                    }

                    $online10 = $message['online10_mess'];
                }
                if (in_array($message['give_type'], [
                    5,
                    6,
                    7
                ])) {
                    $online10 = false;
                }
            }

            foreach ($array as $expired_time => $rows) {
                foreach ($rows as $row) {
                    $xtpl->assign('ROW', $row);
                    $xtpl->parse('ajax_' . NV_LANG_DATA . '.group.loop');
                }

                $xtpl->assign('EXPIRED_TIME', nv_date('H:i:s d/m/Y', $expired_time));
                $xtpl->parse('ajax_' . NV_LANG_DATA . '.group');
            }

            if ($online10) {
                $xtpl->assign('ONLINE10', $online10);
                $xtpl->parse('ajax_' . NV_LANG_DATA . '.online10');
            }

            $xtpl->parse('ajax_' . NV_LANG_DATA);
            nv_htmlOutput(nv_url_rewrite($xtpl->text('ajax_' . NV_LANG_DATA)));
        }

        $xtpl->parse('main');
        return $xtpl->text('main');
    }
}

if (defined('NV_SYSTEM')) {
    $content = nv_reward_points();
}
