/* *
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
// NukeViet Default Custom JS
var tip_active = false,
    ftip_active = false,
    screen_sm = !1,
    screen_sm_checked = !1,
    screen_dekstop_checked = !1,
    winX = 0,
    winY = 0,
    oldWinX = 0,
    oldWinY = 0,
    cRangeX = 0,
    cRangeY = 0,
    docX = 0,
    docY = 0,
    brcb = $('.breadcrumbs-wrap'),
    menuSite = $('#menusite'),
    menuSiteButton = $("#menuSiteButton"),
    news_other = null,
    ps = null;

function ScaleSize() {
    oldWinX = winX;
    oldWinY = winY;
    winX = $(window).width();
    winY = $(window).height();
    docX = $(document).width();
    docY = $(document).height();
    cRangeX = Math.abs(winX - oldWinX);
    cRangeY = Math.abs(winY - oldWinY);
}

function winResize() {
    ScaleSize();
    if (!winX || !winY || !docX || !docY) {
        setTimeout(ScaleSize, 30);
    }
    checkMenu();
}

function fix_banner_center() {
    var a = Math.round((winX - 1330) / 2);
    0 <= a ? ($("div.fix_banner_left").css("left", a + "px"), $("div.fix_banner_right").css("right", a + "px"), a = Math.round((winY - $("div.fix_banner_left").height()) / 2), 0 >= a && (a = 0), $("div.fix_banner_left").css("top", a + "px"), a = Math.round((winY - $("div.fix_banner_right").height()) / 2), 0 >= a && (a = 0), $("div.fix_banner_right").css("top", a + "px"), $("div.fix_banner_left").show(), $("div.fix_banner_right").show()) : ($("div.fix_banner_left").hide(), $("div.fix_banner_right").hide())
}

function delete_confirm(ob) {
    bootbox.confirm(nv_is_del_confirm[0], function(result){
      if (result) {
        window.location.href = $(ob).attr("href")
      }
  });
  return!1;
}

function click_show_sitemenu(event) {
    event.preventDefault();
    event.stopPropagation();
    $(this).parent().siblings().removeClass('open');
    $(this).parent().toggleClass('open');
}

function set_dropdown_event() {
    if ($("body").is('.touch')) {
        $(".dropdown", menuSite).unbind('mouseenter mouseleave');
        $(".dropdown-toggle", menuSite).attr('data-toggle', 'dropdown');
        $(".parent-menu", menuSite).show();
        $('[data-toggle=dropdown]', menuSite).bind('click', click_show_sitemenu);
        $(".dropdown", menuSite).hover(function() {
            $(this).addClass("open")
        }, function() {
            $(this).removeClass("open")
        });
    } else {
        $('[data-toggle=dropdown]', menuSite).unbind('click', click_show_sitemenu);
        $("[data-toggle=dropdown]", menuSite).attr('data-toggle', '');
        $(".parent-menu", menuSite).hide();
        $(".dropdown", menuSite).hover(function() {
            $(this).addClass("open")
        }, function() {
            $(this).removeClass("open")
        });
    }
}

function checkMenu() {
    if (!theme_responsive) {
        $(".dropdown-submenu .caret", menuSite).addClass("caret-right");
    } else {
        if (menuSiteButton.is(":visible")) {
            screen_sm = !0;
            screen_dekstop_checked = !1;
            if (!screen_sm_checked) {
                $(".dropdown-submenu .caret", menuSite).removeClass("caret-right");
                screen_sm_checked = !0;
            }
            $(".navbar-nav", menuSite).on('swipeleft', function(e) {
                $('.navbar-collapse', menuSite).collapse('hide')
            });
            $(".touch-content").on('swiperight', function(e) {
                if ($(".navbar-collapse", menuSite).hasClass('in')) {} else {
                    $('.navbar-collapse', menuSite).collapse('show')
                }
            });
        } else {
            $('.navbar-collapse', menuSite).collapse('hide')
            screen_sm = !1;
            screen_sm_checked = !1;
            if (!screen_dekstop_checked) {
                $(".dropdown-submenu .caret", menuSite).addClass("caret-right");
                screen_dekstop_checked = !0;
            }
            if ($(".navbar-nav").width() > $("body").width() + 10) {
                $(".navbar-collapse", menuSite).css({
                    width: $("body").width()
                }).addClass("scroll-menu")
            }
        }
    }

    checkSubMenu(false);
}

function checkSubMenu(isCheckBtn) {
    // Xử lý menu desktop
    var btnToggle = $('#menuSiteButton');
    var areaExpand = $('#menusite-desktop-expand');
    var dtMenu = $('#dt_menu');
    var maxW = $('.navbar-nav', $('#menusite')).width() - (isCheckBtn ? 100 : 70);

    areaExpand.addClass('hidden').html('<a href="#"><span class="icon-menu"></span></a>');
    $('.item-menu-lev1', dtMenu).removeClass('hidden dropleft-item-menu');

    if (!btnToggle.is(':visible')) {
        var currW = 0;
        var arrHidden = [];
        var arrShow = [];
        $('.item-menu-lev1', dtMenu).each(function() {
            var menu = $(this);
            currW += menu.width();
            if (currW > maxW) {
                arrHidden.push(menu);
            } else {
                arrShow.push(menu);
            }
        });
        if (arrHidden.length > 0) {
            areaExpand.html('<a href="#"><span class="icon-menu"></span></a><ul class="dropdown-menu dropdown-menu-expand"></ul>');
            $.each(arrHidden, function() {
                var menu = $(this);
                var cl = [];
                menu.addClass('hidden');
                if (menu.is('.dropdown')) {
                    cl.push('dropdown dropdown-submenu');
                }
                if (menu.is('.active')) {
                    cl.push('active');
                }
                $('.dropdown-menu-expand', areaExpand).append('<li' + (cl.length > 0 ? (' class="' + cl.join(' ') + '"') : '') + '>' + menu.html() + '</li>');
            });
            //$('li', areaExpand).removeClass('active');
            areaExpand.removeClass('hidden');
            if (!isCheckBtn) {
                checkSubMenu(true);
            } else {
                set_dropdown_event();
            }
        }
        var arrShowSize = arrShow.length;
        if (arrShowSize > 0 && arrHidden.length > 0) {
            var stt = 0;
            $.each(arrShow, function() {
                var menu = $(this);
                stt++;
                if (stt >= (arrShowSize - 1)) {
                    menu.addClass('dropleft-item-menu');
                }
            });
        }
    }
}

function checkWidthMenu() {
    $('ul.dropdown-menu [data-toggle=dropdown]').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        $(this).parent().siblings().removeClass('open');
        $(this).parent().toggleClass('open');
    });
}

function tipHide() {
    $("[data-toggle=tip]").attr("data-click", "y").removeClass("active");
    $("#tip").hide();
    tip_active = false;
}

function ftipHide() {
    $("[data-toggle=ftip]").attr("data-click", "y").removeClass("active");
    $("#ftip").hide();
    ftip_active = false;
}

function tipShow(a, b, callback) {
    tip_active && tipHide();
    ftip_active && ftipHide();
    $("[data-toggle=tip]").removeClass("active");
    $(a).attr("data-click", "n").addClass("active");
    $("#tip").attr("data-content", b);
    if (typeof callback != "undefined") {
        $("#tip").show("fast", function() {
            if (callback == "recaptchareset") {
                loadCaptcha(this)
            } else if (typeof window[callback] === "function") {
                window[callback]()
            }
        });
    } else {
        $("#tip").show("fast");
    }
    tip_active = true;
}

function ftipShow(a, b, callback) {
    if ($(a).is(".qrcode") && "no" == $(a).attr("data-load")) {
        return qrcodeLoad(a), !1;
    }
    tip_active && tipHide();
    ftip_active && ftipHide();
    $("[data-toggle=ftip]").removeClass("active");
    $(a).attr("data-click", "n").addClass("active");
    $("#ftip").attr("data-content", b);
    if (typeof callback != "undefined") {
        $("#ftip").show("fast", function() {
            if (callback == "recaptchareset") {
                loadCaptcha(this)
            } else if (typeof window[callback] === "function") {
                window[callback]()
            }
        });
    } else {
        $("#ftip").show("fast");
    }
    ftip_active = true;
};

function openID_load(a) {
    nv_open_browse($(a).attr("href"), "NVOPID", 550, 500, "resizable=no,scrollbars=1,toolbar=no,location=no,titlebar=no,menubar=0,location=no,status=no");
    return !1
}

function openID_result() {
    var resElement = $("#openidResult");
    resElement.fadeIn();
    setTimeout(function() {
        if (resElement.data('redirect') != '') {
            window.location.href = resElement.data('redirect');
        } else if (resElement.data('result') == 'success') {
            location.reload();
        } else {
            resElement.hide(0).html('').data('result', '').data('redirect', '');
        }
    }, 5000);
}

// QR-code
function qrcodeLoad(a) {
    var b = new Image,
        c = $(a).data("img");
    $(b).on('load', function() {
        $(c).attr("src", b.src);
        $(a).attr("data-load", "yes").click()
    });
    b.src = nv_base_siteurl + "index.php?second=qr&u=" + encodeURIComponent($(a).data("url"))
};

// Switch tab
function switchTab(a) {
    if ($(a).is(".current")) {
        return !1;
    }
    var b = $(a).data("switch").split(/\s*,\s*/),
        c = $(a).data("obj");
    $(c + " [data-switch]").removeClass("current");
    $(a).addClass("current");
    $(c + " " + b[0]).removeClass("hidden");
    for (i = 1; i < b.length; i++) {
        $(c + " " + b[i]).addClass("hidden")
    }
};

// Breadcrumbs
function nvbreadcrumbs() {
    if (brcb.length) {
        var g = $(".display", brcb).innerWidth() - 40,
            b = $(".breadcrumbs", brcb),
            h = $(".temp-breadcrumbs", brcb),
            e = $(".subs-breadcrumbs", brcb),
            ic_d = brcb.data("icon-down"),
            ic_u = brcb.data("icon-up"),
            a = [],
            c = !1;
        h.find("a").each(function() {
            a.push([$(this).attr("title"), $(this).attr("href")])
        });
        b.html("");
        e.html("");

        // Thiết bị điện thoại
        let checkMaxText = 0;
        let totalWidth = 0;

        for (let i = 0; i < a.length; i++) {
            let liId = 'brcr_' + i;
            b.append('<li id="' + liId + '"><a href="' + a[i][1] + '"><span>' + a[i][0] + "</span></a></li>");
            let liWidth = $("#" + liId).outerWidth(true);

            if (i < a.length - 1) {
                totalWidth += liWidth;

                if (totalWidth > g) {
                    $("#" + liId).remove();
                    c = true;
                }
            } else {
                totalWidth += liWidth;
                if (totalWidth > g) {
                    let availableSpace = g - (totalWidth - liWidth);
                    let spanText = a[i][0];
                    while ($("#" + liId + " span").outerWidth(true) > availableSpace && spanText.length > 20) {
                        spanText = spanText.slice(0, -1);
                        $("#" + liId + " span").text(spanText + "...");
                    }

                    if ($("#" + liId + " span").outerWidth(true) > availableSpace) {
                        $("#" + liId).remove();
                        checkMaxText = 1;
                    }
                }
            }
        }

        // Khi ký tự đã vượt quá 20 ký tự và vượt quá kích thước màn hình cho phép
        if (checkMaxText) {
            // Xoá bỏ những item bị thừa khi vượt quá kích thước màn hình
            for (let i = 0; i < a.length - 1; i++) {
                $("#brcr_" + i).remove();
            }

            for (i = a.length - 1; 0 <= i; i--) {
                if (i == a.length - 1) {
                    b.prepend('<li id="brcr_' + i + '"><a href="' + a[i][1] + '"><span>' + a[i][0] + "</span></a></li>");
                    var d = $("li", b).outerWidth(!0);
                    d > g && $("li a span", b).addClass("dotsign")
                } else {
                    if (!c) {
                        var d = 0;
                        b.prepend('<li id="brcr_' + i + '"><a href="' + a[i][1] + '"><span>' + a[i][0] + "</span></a></li>");
                        b.find("li").each(function() {
                            d += $(this).outerWidth(!0)
                        });
                        d > g && (c = !0, $("#brcr_" + i, b).remove())
                    }
                    c && e.append('<li><a href="' + a[i][1] + '"><span><em class="' + ic_u + '"></em> ' + a[i][0] + "</span></a></li>")
                }
            }
        }

        if (c) {
            b.prepend('<li><a class="show-subs-breadcrumbs" href="#" onclick="showSubBreadcrumbs(this, event);"><span><em class="' + ic_d + '"></em></span></a></li>');
        }
    }
}

function showSubBreadcrumbs(a, b) {
    b.preventDefault();
    b.stopPropagation();
    var c = $(".subs-breadcrumbs", brcb),
        ic_r = brcb.data("icon-right"),
        ic_d = brcb.data("icon-down");
    $("em", a).is("." + ic_d) ? $("em", a).removeClass(ic_d).addClass(ic_r) : $("em", a).removeClass(ic_r).addClass(ic_d);
    c.toggleClass("open");
    $(document).on("click", function() {
        $("em", a).is("." + ic_r) && ($("em", a).removeClass(ic_r).addClass(ic_d), c.removeClass("open"))
    })
}

function footerSubMenu(t) {
    var a = $(".footerSubMenu-caret", t),
        d = $(t).next(),
        b = $(t).attr("data-icon-hide"),
        c = $(t).attr("data-icon-show");
    if (a.css('display') !== 'none') {
        if (d.hasClass('hd-xs')) {
            d.removeClass('hd-xs');
            a.removeClass(b).addClass(c)
        } else {
            d.addClass('hd-xs');
            a.removeClass(c).addClass(b)
        }
    }
}

function offsetAnchor() {
    if (location.hash.length !== 0) {
      window.scrollTo(window.scrollX, window.scrollY - 50);
    }
}

function toggleFullScreen(elem) {
    if ((document.fullScreenElement !== undefined && document.fullScreenElement === null) || (document.msFullscreenElement !== undefined && document.msFullscreenElement === null) || (document.mozFullScreen !== undefined && !document.mozFullScreen) || (document.webkitIsFullScreen !== undefined && !document.webkitIsFullScreen)) {
        if (elem.requestFullScreen) {
            elem.requestFullScreen();
        } else if (elem.mozRequestFullScreen) {
            elem.mozRequestFullScreen();
        } else if (elem.webkitRequestFullScreen) {
            elem.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT);
        } else if (elem.msRequestFullscreen) {
            elem.msRequestFullscreen();
        }
        $('#fullscreen').hide();
        $('#exitfullscreen').show();
    } else {
        if (document.cancelFullScreen) {
            document.cancelFullScreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.webkitCancelFullScreen) {
            document.webkitCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
        $('#fullscreen').show();
        $('#exitfullscreen').hide();
    }
}

$(function() {
    try {
        document.createEvent("TouchEvent");
        $("body").addClass('touch');
    } catch (e) {
        $("body").removeClass('touch');
    }
    set_dropdown_event();
    $(".get_language_icons_block").html($(".language_icons_block").html());
    $(".get_social_icons_block").html($(".social_icons_block").html());
    $(".get_head_right_block").html($(".head_right_block").html());

    // Smooth scroll to top
    $("#totop,#bttop,.bttop").click(function(e) {
        e.preventDefault();
        $("html,body").animate({
            scrollTop: 0
        }, 800);
        return !1
    });

    news_other = document.querySelector('.news_other');
    ps = news_other !== null ? new PerfectScrollbar(news_other) : null;

    if (typeof bootbox != 'undefined') {
        bootbox.setDefaults({
            locale: nv_lang_interface,
            show: true,
            backdrop: true,
            closeButton: false,
            animate: false,
            className: "my-bootbox"
        });
    }

    // Search form
    $(".headerSearch button").on("click", function() {
        if ("n" == $(this).attr("data-click")) {
            return !1;
        }
        $(this).attr("data-click", "n");
        var a = $("input", $(this).parents(".headerSearch")),
            c = a.attr("maxlength"),
            b = strip_tags(a.val()),
            d = $(this).attr("data-minlength");
        a.parent().removeClass("has-error");
        "" == b || b.length < d || b.length > c ? (a.parent().addClass("has-error"), a.val(b).focus(), $(this).attr("data-click", "y")) : window.location.href = $(this).attr("data-url") + rawurlencode(b);
        return !1
    });
    $(".headerSearch input").on("keypress", function(a) {
        13 != a.which || a.shiftKey || (a.preventDefault(), $("button", $(this).parents(".headerSearch")).trigger("click"))
    });

    // Show confirm message on leave, reload page
    $("form.confirm-reload").change(function() {
        $(window).bind("beforeunload", function() {
            return nv_msgbeforeunload
        })
    });

    // Tooltip
    $(".form-tooltip").tooltip({
        selector: "[data-toggle=tooltip]",
        container: "body"
    });
    $("[data-rel='tooltip'][data-content!='']").removeAttr("title").tooltip({
        container: "body",
        html: !0,
        title: function() {
            return ("" == $(this).data("img") || !$(this).data("img") ? "" : '<img class="img-thumbnail pull-left" src="' + $(this).data("img") + '" width="90" />') + $(this).data("content")
        }
    });

    // Change site lang
    $(".nv_change_site_lang").change(function() {
        document.location = $(this).val()
    });

    // Menu bootstrap
    $("a", menuSite).hover(function() {
        $(this).attr("rel", $(this).attr("title"));
        $(this).removeAttr("title")
    }, function() {
        $(this).attr("title", $(this).attr("rel"));
        $(this).removeAttr("rel")
    });

    $(document).on('click', function(event) {
        if (tip_active && !(
            $(event.target).closest("[data-toggle=tip]", this).length ||
            $(event.target).closest("#tip", this).length ||
            $(event.target).closest(".modal").length ||
            $(event.target).closest(".cr-md").length ||
            $(event.target).closest(".cr-cap").length
        )) {
            tipHide();
        } else if (ftip_active && !(
            $(event.target).closest("[data-toggle=ftip]", this).length ||
            $(event.target).closest("#ftip", this).length ||
            $(event.target).closest(".modal").length ||
            $(event.target).closest(".cr-md").length ||
            $(event.target).closest(".cr-cap").length
        )) {
            ftipHide();
        }
    });

    $("[data-toggle=tip], [data-toggle=ftip]").click(function(e) {
        e.preventDefault();
        var a = $(this).attr("data-target"),
            b = $(this).attr("data-toggle"),
            c = $("#" + b).attr("data-content");
        var callback = $(this).data("callback");
        if (a != c) {
            "" != c && $('[data-target="' + c + '"]').attr("data-click", "y");
            $("#" + b + " .bg").html($(a).html());
            "tip" == b ? tipShow(this, a, callback) : ftipShow(this, a, callback)
        } else {
            if ("n" == $(this).attr("data-click")) {
                "tip" == b ? tipHide() : ftipHide()
            } else {
                "tip" == b ? tipShow(this, a) : ftipShow(this, a)
            }
        }
    });


    // Google map
    if ($('.company-address').length) {
        $('.company-map-modal').on('shown.bs.modal', function() {
            if (!$('iframe', this).length) {
                $('.modal-body', this).html('<iframe class="company-map" frameborder="0" src="' + $(this).data('src') +'" allowfullscreen></iframe>')
            }
        })
    };

    // OpenID
    $("#openidBt").on("click", function() {
        openID_result();
        return !1
    });

    //Đăng nhập bằng OpenID
    $('body').on('click', '[data-toggle=openID_load]', function(e) {
        e.preventDefault();
        openID_load(this)
    });


    // Logo desktop
    $('.navbar-brand-other').hover(function() {
        $(this).addClass('active');
        $('.navbar-brand-self').addClass('gray-filter');
    }, function() {
        $(this).removeClass('active');
        $('.navbar-brand-self').removeClass('gray-filter');
    });
    // Click link trong phần giới thiệu cách lề đầu 1 đoạn tránh bị nav che mất
    $(document).on('click', 'a[href^="#"]', function(event) {
        window.setTimeout(function() {
            offsetAnchor();
        }, 0);
    });
    // Popup phụ, có z-index nằm dưới navbar
    if ($('.ext-popup').length) {
        $(".ext-popup-dialog").css("margin-top", $('.navbar').outerHeight(true) + 10);
        window.scrollTo({top: 0, behavior: 'smooth'});
        $('body').addClass('ext-popup-open');
    }

    /* Chat GPT cũ đã bỏ
    var chatGPT = $('#modalChatGPT');
    if (chatGPT.length) {
        $('.new-chat', chatGPT).on('click', function() {
            $('iframe', chatGPT).attr('src', 'https://vinades.mauwebhay.com/?' + Date.now());
        });
        chatGPT.on('shown.bs.modal', function() {
            if (!chatGPT.data('rendered')) {
                chatGPT.data('rendered', 1);
                $('iframe', chatGPT).attr('src', 'https://vinades.mauwebhay.com/?' + Date.now());
            }
        });
    }
    */

    // Gọi modal đăng nhập thành viên
    $(document).delegate('[data-toggle="loginFormShow"]', 'click', function(e) {
        e.preventDefault();
        var redirect = '';
        if ($(this).data('redirect')) {
            redirect = $(this).data('redirect');
        } else if (typeof window.page_url != 'undefined' && window.page_url != '') {
            redirect = window.page_url;
        }
        loginForm(redirect);
    });

    // Xử lý chat GPT
    var gptIframe = $('#chat_gpt_dauthauv2_chatFrame');
    window.addEventListener("message", (event) => {
        if (event.origin != gptIframe.data('origin') || !event.data) {
            return false;
        }
        // Gửi cho đối tác UID để họ ghi nhận trong các đoạn chat
        if (event.data == 'ClientIDRequest') {
            document.getElementById("chat_gpt_dauthauv2_chatFrame").contentWindow.postMessage({userIdentifier: gptIframe.data('uid')}, gptIframe.data('origin'));
        }
        // Đánh dấu client bắt đầu chat nếu chưa ghi nhận thông tin chat trước đó
        if (event.data == 'ClientStartChat' && !gptIframe.data('attached') && !gptIframe.data('busy')) {
            gptIframe.data('busy', 1);
            $.ajax({
                type: 'POST',
                url: nv_base_siteurl,
                data: {
                    chat_gpt_attach: gptIframe.data('tokend'),
                },
                dataType: 'json',
                cache: false,
                success: function(respon) {
                    gptIframe.data('busy', 0);
                    if (respon.attached == 1) {
                        gptIframe.data('attached', 1);
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    gptIframe.data('busy', 0);
                    console.log(jqXHR, textStatus, errorThrown);
                }
            });
        }
    }, false);
});

$(window).bind("load resize orientationchange", function() {
    winResize();
    fix_banner_center();
    nvbreadcrumbs();
    ps !== null && ps.update();
    // if (150 < cRangeX || 150 < cRangeY) tipHide(), ftipHide()

    $('#dt_menu').on('hide.bs.collapse', function() {
        $('body').removeClass('open-mobile-menu');
    });
    $('#dt_menu').on('show.bs.collapse', function() {
        $('body').addClass('open-mobile-menu');
    });
});

// Popup phụ, có z-index nằm dưới navbar
$(window).bind("resize orientationchange", function() {
    if ($('.ext-popup').length) {
        $(".ext-popup-dialog").css("margin-top", $('.navbar').outerHeight(true) + 10);
    }
});

// Load Social script - lasest
$(window).on('load', function() {
    nvbreadcrumbs();

    // Webapp
    var a = document.createElement("script");
    a.type = "text/javascript";
    a.src = "/themes/dauthau/js/webapp.js?v=30006";
    $("head").append(a);

    $('body').addClass('dom-loaded');

    $('.btn-share-group>.btn-copy-link').on('click', function() {
        navigator.clipboard.writeText(window.location.href);
        $('.btn-share-group>.btn-copy-link>.tip').show();
        setTimeout(function() {$('.btn-share-group>.btn-copy-link>.tip').hide();}, 2000);
    });

    // Xử lý slide ảnh news và page
    var pswpElementImages = $('.pswp-container-images');
    if (pswpElementImages.length) {
        var pswpElement = document.querySelectorAll('.pswp')[0];
        var images = [], imageIndex = 0;
        $('img.slide', pswpElementImages).each(function() {
            var figure = $(this).parent();
            var iTitle = '';
            if (figure.length && figure.prop('tagName').toLowerCase() == 'figure' && $('figcaption', figure).length == 1) {
                iTitle = $('figcaption', figure).text();
            }
            images.push({
                src: $(this).prop('src'),
                w: this.naturalWidth,
                h: this.naturalHeight,
                title: iTitle
            });
            $(this).data('index', imageIndex);
            $(this).css({
                cursor: 'pointer'
            });
            imageIndex++;
        });
        if (images.length > 0) {
            $('img.slide', pswpElementImages).on('click', function() {
                (new PhotoSwipe(pswpElement, PhotoSwipeUI_Default, images, {
                    index: $(this).data('index'),
                    mouseUsed: true
                })).init();
            });
        }
    }
});

$(window).scroll(function() {
    if ($(window).scrollTop() > 300 && ($(window).scrollTop() + $(window).height() < $(document).height() - $("#footer").height())) {
        $('#totop').addClass('fixed');
    } else {
        $('#totop').removeClass('fixed');
    }
});

function nv_object2query(e) {
    return Object.keys(e)
        .filter(function (t) {
            return void 0 !== e[t] && "" !== e[t];
        })
        .map(function (t) {
            return ""
                .concat(encodeURIComponent(t), "=")
                .concat(encodeURIComponent(e[t]));
        })
        .join("&");
}

function nv_fb_share(url = '', quote = '') {
    var G = "scrollbars=0, resizable=1, menubar=0, left=100, top=100, width=550, height=440, toolbar=0, status=0";
    if (!url) {
        url = window.location.href;
    }
    var i;
    if (0 !== quote) {
        i = nv_object2query({
            kid_directed_site: "0",
            sdk: "joey",
            u: url,
            display: "popup",
            ref: "plugin",
            src: "share_button",
            quote: quote,
        });
    } else {
        i = nv_object2query({
            kid_directed_site: "0",
            sdk: "joey",
            u: url,
            display: "popup",
            ref: "plugin",
            src: "share_button",
        });
    }
    return window.open("https://www.facebook.com/sharer/sharer.php?".concat(i),
        "_blank", G
    );
}

function nv_tw_share(url = '', title = '', hashtags = undefined) {
    var G = "scrollbars=0, resizable=1, menubar=0, left=100, top=100, width=550, height=440, toolbar=0, status=0";
    if (!url) {
        url = window.location.href;
    }
    r = nv_object2query({
        text: title,
        url: url,
        hashtags: (void 0 === hashtags ? [] : hashtags).join(","),
    });
    return window.open("https://twitter.com/intent/tweet?".concat(r),
        "_blank", G
    );
}

$(".fixed_table").hide();
function calTheadWidth() {
    fixed_table = $(".fixed_table").find(".th_custom")
    thead = $(".box__table").find("th");
    for (i = 0; i < thead.length; i++) {
        fixed_table.eq(i).css({
            'width': thead.eq(i).outerWidth() + 0.5
        });
    }

    $(".fixed_table").css({
        height: $(".box__table").find("thead").height(),
    });
}

// Có class fixed_table này mới xử lý
$(window).resize(function() {
    var width = $(window).width();
    if (width >= 1200) {
        responsive_thead_table();
    }
});

if (window.matchMedia("(min-width: 1200px)").matches) {
    responsive_thead_table();
}

function responsive_thead_table() {
    if ($(".fixed_table").length > 0) {
        $(window).on('load', function() {
            calTheadWidth();
        });

        $(window).on('resize', function() {
            if (window.matchMedia("(min-width: 768px)").matches) {
                calTheadWidth();
            }
        });

        fixScroll();

        function fixScroll() {
            var scroll = $(this).scrollTop(); // khi kéo chuột
            calTheadWidth();

            if (scroll >= ($(".box__table").height() + $(".box__table").offset().top - 300)) {
                $(".fixed_table").hide();
                $(".fixed_table").removeClass('fixed-content');
            } else if (scroll >= ($(".box__table").offset().top + 250)) {
                setTimeout(function() {
                    $(".fixed_table").show();
                    fixScroll();
                }, 200);
                $(".fixed_table").addClass('fixed-content');
            } else if (scroll <= $(".box__table").offset().top + 10){
                $(".fixed_table").hide();
                $(".fixed_table").removeClass('fixed-content');
            }

            if (scroll <= $(".box__table").offset().top + 10){
                $(".fixed_table").hide();
                $(".fixed_table").removeClass('fixed-content');
            }
        }

        $(window).scroll(function () {
            fixScroll();
        });
    }
}

$('#crawl_request_history').on('click', function () {
    $('#crawl_request_history_list').slideToggle(150);
});

addCttCompare = (id, check) => {
    let url_cp = document.querySelector('.mload_compare').getAttribute('data-mload');
    fetch(url_cp, {
        method: 'POST',
        body: new URLSearchParams({
            id: id,
            checkaddcp: check,
            l: document.getElementById('cmp-loaded').getAttribute('data-loaded')
        })
    })
    .then(response => response.json())
    .then(res => {
        if (res.res === 'error') {
            alert(res.mess);
        } else {
            if (document.getElementById('is_reload_cmp') != null && document.getElementById('is_reload_cmp').getAttribute('data-reload') == '1') {
                document.getElementById('modalCompare').style.display = 'none';
                location.reload();
            }
            document.querySelectorAll('[data-id="' + id + '"].btn-compare-actt').forEach(e => {e.classList.add('hidden')});
            document.querySelectorAll('[data-id="' + id + '"].btn-compare-rctt').forEach(e => {e.classList.remove('hidden')});
            document.getElementById('size-cp-ctt').innerHTML = res.size;
        }
        if (typeof(res.html) != 'undefined') {
            document.querySelector('.listcompare').innerHTML = res.html;
            document.getElementById('cmp-loaded').setAttribute('data-loaded', 1);
        }
        if (!document.querySelector(".expand-compare").classList.contains('hidden')) {
            document.querySelector(".expand-compare").click();
        }
    })
}

removeCttCompare = (id, check) => {
    let url_cp = document.querySelector('.mload_compare').getAttribute('data-mload');
    fetch(url_cp, {
        method: 'POST',
        body: new URLSearchParams({
            id: id,
            checkremvcp: check
        })
    })
    .then(response => response.json())
    .then(res => {
        if (res.res === 'error') {
            alert(res.mess);
        } else {
            if (document.getElementById('is_reload_cmp') != null && document.getElementById('is_reload_cmp').getAttribute('data-reload') == '1') {
                document.getElementById('modalCompare').style.display = 'none';
                location.reload();
            }
            document.querySelector('.listcompare').innerHTML = res.html;
            document.querySelectorAll('[data-id="' + id + '"].btn-compare-actt').forEach(e => {e.classList.remove('hidden')});
            document.querySelectorAll('[data-id="' + id + '"].btn-compare-rctt').forEach(e => {e.classList.add('hidden')});
            document.getElementById('size-cp-ctt').innerHTML = res.size;
            if (!document.querySelector(".expand-compare").classList.contains('hidden')) {
                collapseCmpPosition(false);
            }
        }
    })
}

RemoveAllIdCompare = (check) => {
    let url_cp = document.querySelector('.mload_compare').getAttribute('data-mload');
    fetch(url_cp, {
        method: 'POST',
        body: new URLSearchParams({
            checkclrctt: check
        })
    })
    .then(response => response.json())
    .then(res => {
        if (res.res === 'error') {
            alert(res.mess);
        } else {
            if (document.getElementById('is_reload_cmp') != null && document.getElementById('is_reload_cmp').getAttribute('data-reload') == '1') {
                document.getElementById('modalCompare').style.display = 'none';
                location.reload();
            }
            document.querySelector('.listcompare').innerHTML = res.html;
            document.querySelectorAll('.btn-compare-actt').forEach(e => {e.classList.remove('hidden')});
            document.querySelectorAll('.btn-compare-rctt').forEach(e => {e.classList.add('hidden')});
            document.getElementById('size-cp-ctt').innerHTML = 0;
            if (!document.querySelector(".expand-compare").classList.contains('hidden')) {
                collapseCmpPosition(false);
            }
        }
    })
}

collapseCmpPosition = (roll = true) => {
    const stickcompare = document.querySelector(".stickcompare");
    const stickcompareHeight = stickcompare.offsetHeight;
    if (!roll) {
        stickcompare.classList.remove('rolling-bottom');
        stickcompare.style.bottom = '-' + stickcompareHeight + 'px';
        stickcompare.offsetHeight
        stickcompare.classList.add('rolling-bottom');
    } else {
        stickcompare.style.bottom = '-' + stickcompareHeight + 'px';
    }
}

// Fix bootstrap multiple modal
$(document).on({
    'show.bs.modal': function() {
        var zIndex = 1040 + (10 * $('.modal:visible').length);
        $(this).css('z-index', zIndex);
        setTimeout(function() {
            $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
        }, 0);
    },
    'hidden.bs.modal': function() {
        if ($('.modal:visible').length > 0) {
            setTimeout(function() {
                $(document.body).addClass('modal-open');
            }, 0);
        }
    }
}, '.modal');
