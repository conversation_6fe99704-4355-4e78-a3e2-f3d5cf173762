<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_BIDDING')) {
    die('Stop!!!');
}

use NukeViet\Dauthau\Share;
use NukeViet\Point\Point;

$base_url = $page_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['investors'];

// Gọi hàm confirm update crawl
Share::submitConfirmCrawl();

$db->sqlreset()
   ->select('*')
   ->from(BID_PREFIX_GLOBAL . '_investor_approved_detail')
   ->where('id = ' . $id);
$sth = $db->prepare($db->sql());
$sth->execute();
$detail = $sth->fetch();

$array_data = array_merge($array_data, $detail);

if (empty($array_data)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['investors']);
}

// Cập nhật lại nhà đầu tư
if (defined('NV_IS_USER') and $nv_Request->isset_request('update', 'post')) {
    $id_update = $nv_Request->get_int('id_update', 'post, get', 0);
    $check = $nv_Request->get_title('check', 'post, get', '');
    $info = $nv_Lang->getModule('update_err');
    $customs_points = Point::getMyPoint();
    $last = [];

    if ($id_update > 0 and $check == md5($user_info['userid'] . $id_update . NV_CACHE_PREFIX . $client_info['session_id'])) {
        // Kiểm tra xem khách hàng còn điểm không?
        $sodiem = Point::getAllConfig()['point_crawl_mintus'];
        $sodiem = (isset($sodiem) ? $sodiem : 0);
        if ($customs_points['point_total'] < $sodiem) {
            nv_jsonOutput([
                'res' => 'error',
                'mess' => Share::langErrorUserNoPoint()
            ]);
        }

        // Kiểm tra xem có dữ liệu người dùng vừa bấm cập nhật mới nhất không?
        if (!empty($module_config['bidding']['up_request_user_inteval'])) {
            $last = $db->query("SELECT * FROM " . BID_PREFIX_GLOBAL . "_update_user WHERE userid = " . $user_info['userid'] . " AND investor_id = " . $id_update . " ORDER BY last_reload DESC LIMIT 1")->fetch();
            if (!empty($last)) {
                $pas = NV_CURRENTTIME - $last['last_reload'] - $module_config['bidding']['up_request_user_inteval'] * 60;
                if ($pas < 0) {
                    nv_jsonOutput([
                        'res' => 'error',
                        'mess' => sprintf($nv_Lang->getModule('update_err_user_last'), nv_convertfromSec(abs($pas))),
                    ]);
                }
            }
        }

        $url_detail = nv_url_rewrite($base_url . '/' . $array_data['alias'] . '-' . $array_data['id'], true);
        $data = [
            'title' => $array_data['org_fullname'],
            'link' => urlRewriteWithDomain($url_detail, NV_MY_DOMAIN)
        ];

        $data = json_encode($data);

        // Chỉ cho phép thực hiện update lại sau 30p
        if (NV_CURRENTTIME - $array_data['get_time'] >= 1800) {
            $dbcr = connect_dbcr();
            $investor_approved = $dbcr->query("SELECT * FROM nv24_crawls_investor_approved WHERE orgcode = " . $dbcr->quote($array_data['org_code']))->fetch();

            if (!empty($investor_approved)) {
                // Kiểm tra xem bên crawl xem cái này đã chạy chưa và thời gian update bên site chính có vượt quá 10p không
                if ($investor_approved['run_url'] != 0 and (NV_CURRENTTIME - $array_data['updatetime'] >= 600)) {
                    $exec = $dbcr->exec("UPDATE nv24_crawls_investor_approved SET update_again = 1 WHERE id = " . $investor_approved['id']);
                    if ($exec) {
                        $info = $nv_Lang->getModule('update_ok');
                    } else {
                        $info = $nv_Lang->getModule('update_err_wait');
                    }
                } else {
                    // Tin đang chờ được cập nhật lại
                    $info = $nv_Lang->getModule('update_err_last');
                }
            } else {
                // TH insert mới vào bên kho crawl
                $data_investor_approved = [
                    'orgcode' => $array_data['org_code'],
                    'fullname' => $array_data['org_fullname'],
                    'taxcode' => $array_data['tax'],
                    'eff_role_date' => $array_data['aproval_time'],
                    'office_pro' => $array_data['id_province'],
                    'office_dis' => $array_data['id_district'],
                    'office_war' => $array_data['id_ward'],
                    'office_add' => $array_data['address'],
                    'addtime' => NV_CURRENTTIME,
                    'update_again' => 1
                ];

                foreach ($data_investor_approved as $key => $value) {
                    $arrayKey[] = $key;
                    $arrayInsert[] = $db->quote($value);
                }
                $sqlInsert = "INSERT INTO nv24_crawls_investor_approved (" . implode(",", $arrayKey) . ") VALUES ( " . implode(",", $arrayInsert) . " )";

                // Thực hiện insert dữ liệu nếu bên crawl chưa có dữ liệu
                $dbcr->query($sqlInsert);
                $_id = $dbcr->lastInsertId();

                if ($_id) {
                    $info = $nv_Lang->getModule('update_ok');
                }
            }

            if (!empty($last)) {
                $exec = $db->exec("UPDATE " . BID_PREFIX_GLOBAL . "_update_user SET last_reload = " . NV_CURRENTTIME . " WHERE userid = " . $user_info['userid'] . " AND investor_id = " . $id_update);
            } else {
                $message = [
                    'vi' => sprintf(get_lang('vi', 'title_minus_points'), $sodiem, get_lang('vi', 'title_reupdate_ndt') . $array_data['org_code']),
                    'en' => sprintf(get_lang('en', 'title_minus_points'), $sodiem, get_lang('en', 'title_reupdate_ndt') . $array_data['org_code']),
                ];

                $res = Share::insertBiddingUpdateUser($id, 'investor_id', $data, $sodiem, json_encode($message));
                if ($res == -1) {
                    nv_jsonOutput([
                        'res' => 'error',
                        'mess' => Share::langErrorTransactionPoint()
                    ]);
                }
            }
        } else {
            $info = $nv_Lang->getModule('investor_update_last_time');
        }
    }

    nv_jsonOutput(
        [
            'mess' => $info
        ]
    );
}

$page_title = $array_data['org_fullname'];
$description = $nv_Lang->getModule('detail_investor_description', $array_data['org_fullname']);

$array_data['detail_link'] = nv_url_rewrite($base_url . '/' . $array_data['alias'] . '-' . $array_data['id'], true);
$canonicalUrl = getCanonicalUrl($array_data['detail_link']);

$array_data['aproval_time'] = nv_date_format(1, $array_data['aproval_time']);
if ($array_data['status']) {
    $array_data['status'] = $nv_Lang->getModule('status_title_1');
    $array_data['class_status'] = 'btn-primary';
} else {
    $array_data['status'] = $nv_Lang->getModule('status_title_2');
    $array_data['class_status'] = 'btn-warning';
}

$arr[] = $array_data['address'];

if (!empty($array_data['id_ward']) and isset($ward_list[$array_data['id_ward']])) {
    $arr[] = $array_data['title_ward'] = $ward_list[$array_data['id_ward']]['title'];
}

if (!empty($array_data['id_district']) and isset($district_list[$array_data['id_district']])) {
    $arr[] = $array_data['title_district'] = $district_list[$array_data['id_district']]['title'];
}

if (!empty($array_data['id_province']) and isset($province_list[$array_data['id_province']])) {
    $arr[] = $array_data['title_province'] = $province_list[$array_data['id_province']]['title'];
}

$array_data['title_type_business'] = !empty($array_data['businesstype']) ? getBusinesType($array_data['businesstype']) : '-';
$array_data['tax_date'] = nv_date_format(1, $array_data['tax_date']);
$array_data['decision_date'] = $array_data['decision_date'] > 0 ? nv_date_format(1, $array_data['decision_date']) : '-';
$array_data['business_date'] = $array_data['business_date'] > 0 ? nv_date_format(1, $array_data['business_date']) : '-';
$array_data['fget_time'] = $array_data['fget_time'] > 0 ? nv_date('d/m/Y H:i:s', $array_data['fget_time']) : '-';
$array_data['get_time'] = $array_data['get_time'] > 0 ? nv_date('d/m/Y H:i:s', $array_data['get_time']) : '-';
$array_data['full_address'] = implode(', ', $arr);
$array_data['website'] = !empty($array_data['website']) ? $array_data['website'] : '-';
// Các file đính kèm
$array_data['dinh_kem'] = [];

// Quyết định
$decision_file = json_decode($array_data['comp_charter_file'], true);
if (!empty($decision_file)) {
    // Xử lý file
    $array_data['dinh_kem']['decision']['title'] = $nv_Lang->getModule('title_qdtl_ndt');
    $array_data['dinh_kem']['decision']['name_file'] = $decision_file['file'];
    $array_data['dinh_kem']['decision']['link_download'] = $decision_file['link_download']  ?? 'javascript:void(0)';
}

// Giấy đăng kí kinh doanh
$business_file = json_decode($array_data['business_file'], true);
if (!empty($business_file)) {
    // Xử lý file
    $array_data['dinh_kem']['business_file']['title'] = $nv_Lang->getModule('business_file_name');
    $array_data['dinh_kem']['business_file']['name_file'] = $business_file['file'];
    $array_data['dinh_kem']['business_file']['link_download'] = $business_file['link_download']  ?? 'javascript:void(0)';
}

// Điều lệ hoạt động của DN
$comp_charter_file = json_decode($array_data['comp_charter_file'], true);
if (!empty($comp_charter_file)) {
    // Xử lý file
    $array_data['dinh_kem']['comp_charter']['title'] = $nv_Lang->getModule('comp_charter_file_name');
    $array_data['dinh_kem']['comp_charter']['name_file'] = $comp_charter_file['file'];
    $array_data['dinh_kem']['comp_charter']['link_download'] = $comp_charter_file['link_download']  ?? 'javascript:void(0)';
}

// Sơ đồ tổ chức
$chart_fileid_file = json_decode($array_data['chart_fileid_file'], true);
if (!empty($chart_fileid_file)) {
    // Xử lý file
    $array_data['dinh_kem']['chart_fileid']['title'] = $nv_Lang->getModule('org_chart_file_name');
    $array_data['dinh_kem']['chart_fileid']['name_file'] = $chart_fileid_file['file'];
    $array_data['dinh_kem']['chart_fileid']['link_download'] = $chart_fileid_file['link_download']  ?? 'javascript:void(0)';
}

// Dữ liệu quốc gia trên trang MSC mới
$array_data['rep_position'] = $db->query('SELECT title FROM ' . NV_PREFIXLANG . '_rep_position WHERE id = ' . intval($array_data['rep_position']))->fetchColumn();

// Quốc gia cấp MST
$array_data['tax_nation'] = $arr_nation[$array_data['tax_nation']]['name'] ?? '-';

// Quốc gia cấp doanh nghiệp
$array_data['business_nation'] = $arr_nation[$array_data['business_nation']]['name'] ?? '-';

// Quốc gia ban hành
$array_data['decision_nation'] = $arr_nation[$array_data['decision_nation']]['name'] ?? '-';

// Quy mô doanh nghiệp
$array_data['org_scale'] = getQMDN($array_data['org_scale']) ?? '-';

// Chuyển đổi lĩnh vực tham gia
if (!empty($array_data['lvtg_lcnt'])) {
    $arr_lvtg = explode(',', $array_data['lvtg_lcnt']);
    $arr_title_lvtg = [];
    foreach ($arr_lvtg as $v) {
        $arr_title_lvtg[] = getLVHD($v);
    }

    $array_data['lvtg_lcnt'] = implode(', ', $arr_title_lvtg);
}

// Xử lý dữ liệu ngành nghề kinh doanh
if (!empty($array_data['industry'])) {
    $nameColumn = (NV_LANG_DATA === 'vi') ? 'name_vi' : 'name_en';
    $array_data['nganhnghe_kd_fomart'] = $db->query("SELECT id, code, " . $nameColumn . " as name FROM " . BID_PREFIX_GLOBAL . "_investor_approved_industry WHERE id IN (" . $array_data['industry'] . ") ORDER BY code")->fetchAll();
    if (!empty($array_data['main_industry'])) {
        $arr_main = explode(',', $array_data['main_industry']);
    } else {
        $arr_main = [];
    }

    foreach ($array_data['nganhnghe_kd_fomart'] as $k => $v) {
        if (in_array($v['id'], $arr_main)) {
            $array_data['nganhnghe_kd_fomart'][$k]['main'] = 1;
        } else {
            $array_data['nganhnghe_kd_fomart'][$k]['main'] = 0;
        }
    }
}

// Xoá cache
if (defined('NV_IS_ADMIN') and $nv_Request->isset_request('delete_cache', 'get')) {
    if (file_exists($filedir . $cache_file)) {
        unlink($filedir . $cache_file);
    }
    nv_redirect_location($array_data['detail_link']);
}

// Lấy danh sách dự án mà các nhà đầu đã đăng ký tham gia dự án
$arrBmt = $list_result_project = $listTotalCost = $arrListResultStatus = [];

// Lấy thông tin hoạt động của nhà đầu tư
$array_data['timeline'] = getTimelineInvestor($array_data, $_data_static);
$arrSolictor = [];

// Lấy 3 bên mời thầu hiển thị
$listTBMT = $_data_static['arr_row']['list_tbmt'] ?? [];

if (!empty($listTBMT)) {
    foreach ($listTBMT as $k => $v) {
        if (!isset($arrSolictor[$v['solicitor_id']]['num_kq'])) {
            $arrSolictor[$v['solicitor_id']]['num_kq'] = 0;
        } else {
            $arrSolictor[$v['solicitor_id']]['num_kq']++;
        }

        $arrSolictor[$v['solicitor_id']]['title'] = $v['ben_moi_thau'];
        $arrSolictor[$v['solicitor_id']]['tbmt'][] = $v;
    }

    // Thống kê
    $array_data['link_summary_investor_solictor'] = $nv_Lang->getModule('summary_investor_solictor', $array_data['org_fullname'], count($arrSolictor), count($listTBMT), nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['investors'] . '/' . $array_data['alias'] . '-' . $array_data['id'] . '/bidders'));

    // Lấy 3 kết quả để hiển thị
    if (count($arrSolictor) > 3) {
        $arrSolictor = array_slice($arrSolictor, 0, 3);
    }
}

$array_data['list_solictor'] = $arrSolictor;

// TH1: Lấy danh sách nhà đầu tư dựa vào bảng kết quả
$list_result_business = $_data_static['arr_row']['list_result_business'] ?? [];

if (!empty($list_result_business)) {
    // Mảng gói thầu theo tỷ lệ trúng/trượt, liên danh hay không liên danh
    foreach ($list_result_business as $k => $v) {
        $arrListResultStatus[$v['resultid']] = $v;
    }

    // Thực hiện lấy danh sách mã dự án dựa vào các nhà đầu tư ở bảng kết quả
    $list_result_project = $_data_static['arr_row']['list_result_project'];
    foreach ($list_result_project as $k => $v) {
        // Tổng số chi phí dự án, trúng trượt, liên danh
        $listTotalCost[$v['code']]['total_cost'] = $v['total_cost'];
        $listTotalCost[$v['code']]['partnership'] = $arrListResultStatus[$v['id']]['partnership'];
        $listTotalCost[$v['code']]['result_status'] = $arrListResultStatus[$v['id']]['result_status'];
    }
}

// Danh sách nhà đầu tư mà %s từng đấu
$listBusiness = [];
if (!empty($list_result_business)) {
    // Thực hiện lấy các nhà đầu tư từng đấu
    $listBusiness = $db->query("SELECT orgcode, id, resultid, bidder_name, joint_venture, result_status, partnership FROM " . NV_PREFIXLANG . "_" . $module_data . "_result_project_business WHERE resultid IN (" . implode(",", array_column($list_result_business, 'resultid')) . ") AND orgcode != " . $db->quote($array_data['org_code']))->fetchAll();
}

$arrInvestor = [];
if (!empty($listBusiness)) {
    // Lấy danh sách thông tin nhà đầu tư
    $db->sqlreset()
       ->select('id, org_fullname, alias, org_code')
       ->from(BID_PREFIX_GLOBAL . '_investor_approved')
       ->where('org_code IN (' . implode(',', array_map([$db,'quote'], array_column($listBusiness, 'orgcode'))) . ')');

    $sth = $db->prepare($db->sql());
    $sth->execute();
    $listInvestor = $sth->fetchAll();
    foreach ($listInvestor as $k => $v) {
        $arrInvestor[$v['org_code']] = $v;
    }
}

$arrBusiness = [];
$num_tbmt_competitors = 0;
foreach ($listBusiness as $k => $v) {
    if (isset($arrInvestor[$v['orgcode']])) {
        $arrBusiness[$v['orgcode']]['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['investors'] . '/' . $arrInvestor[$v['orgcode']]['alias'] . '-' . $arrInvestor[$v['orgcode']]['id'];
    } else {
        $arrBusiness[$v['orgcode']]['link'] = "javascript:void(0)";
    }
    $arrBusiness[$v['orgcode']]['orgcode'] = $v['orgcode'];
    $arrBusiness[$v['orgcode']]['bidder_name'] = $v['bidder_name'];
    $arrBusiness[$v['orgcode']]['num_tbmt'][$v['resultid']] = $v;
    $num_tbmt_competitors++;
}

$array_data['arr_business'] = array_slice($arrBusiness, 0, 3);

$array_data['link_summary_project_competitors'] = $nv_Lang->getModule('title_static_competitors', $array_data['org_fullname'], count($arrBusiness), $num_tbmt_competitors, nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['investors'] . '/' . $array_data['alias'] . '-' . $array_data['id'] . '/competitors', true));

// TH2: Danh sách dự án sử dụng đất hoặc Dự án đầu tư kinh doanh (Dự án chuyên ngành/XHH)
$num_duan = 0;
if (!empty($_data_static['arr_row']['list_project_proposal'])) {
    // Danh sách id dự án
    $num_duan = $_data_static['arr_static']['num_project'];
    $array_data['list_project_proposal'] = $_data_static['arr_row']['list_project_proposal'];

    $solicitor_ids = array_filter(
        array_unique(array_column($array_data['list_project_proposal'], 'solicitor_id')),
        function ($id) {
            return $id !== 0;
        }
    );

    if (!empty($solicitor_ids)) {
        $db->sqlreset()
            ->select('id, alias')
            ->from(BID_PREFIX_GLOBAL . '_solicitor')
            ->where('id IN (' . implode(',', $solicitor_ids) . ')');
        $sth = $db->prepare($db->sql());
        $sth->execute();
        while ($rowSolicitor = $sth->fetch()) {
            $arrBmt[$rowSolicitor['id']] = $rowSolicitor['alias'];
        }
    }
} else {
    $array_data['list_project_proposal'] = [];
}

$array_data['link_summary_project_proposal'] = $nv_Lang->getModule('title_summary_project_ndt', $array_data['org_fullname'], $num_duan, nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['investors'] . '/' . $array_data['alias'] . '-' . $array_data['id'] . '/project', true));

// Lấy 3 nhà đầu tư khác cùng liên danh tại bảng kết quả
$array_data['list_orther_investor'] = $_data_static['arr_row']['list_orther_investor'] ?? [];
$num_orther_investor = $_data_static['arr_row']['num_orther_investor'] ?? 0;
// Lấy thống kê các gói thầu của các nhà đầu tư liên danh này
if (!empty($array_data['list_orther_investor'])) {
    // Đưa dữ liệu vào một mảng
    $resultMap =  $_data_static['arr_row']['result_map'] ?? [];

    // Cập nhật dữ liệu cho `list_orther_investor`
    foreach ($array_data['list_orther_investor'] as $k => $v) {
        $orgcode = $v['orgcode'];
        $array_data['list_orther_investor'][$k]['num_kqlcndt_win'] = $resultMap[$orgcode]['num_kqlcndt_win'] ?? 0;
        $array_data['list_orther_investor'][$k]['num_kqlcndt_lost'] = $resultMap[$orgcode]['num_kqlcndt_lost'] ?? 0;
        // Tổng số gói thầu
        $array_data['list_orther_investor'][$k]['num_kqlcndt'] = $array_data['list_orther_investor'][$k]['num_kqlcndt_win'] + $array_data['list_orther_investor'][$k]['num_kqlcndt_lost'];
        $array_data['list_orther_investor'][$k]['info_investor'] = $db->query("SELECT id, org_fullname, alias FROM " . BID_PREFIX_GLOBAL . "_investor_approved WHERE org_code = " . $db->quote($v['orgcode']))->fetch();
    }
}

$array_data['link_summary_orther_investor'] = $nv_Lang->getModule('title_orther_investor', $array_data['org_fullname'], intval($num_orther_investor), nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['investors'] . '/' . $array_data['alias'] . '-' . $array_data['id'] . '/partners', true));

// Thống kê danh sách tỉnh thành tham của chủ đầu tư
$provinceInvestor = [];
$countProvince = 0;

if (!empty($_data_static['arr_row']['num_provice'])) {
    $countProvince = $_data_static['arr_row']['num_provice'];
    if (!empty($_data_static['arr_row']['list_provice_project_row'])) {
        $listProviceProjectRow = $_data_static['arr_row']['list_provice_project_row'] ?? [];

        foreach ($listProviceProjectRow as $k => $v) {
            $v['info_tbmt'] = $listTotalCost[$v['so_tbmt']];

            // Tính tổng chi phí theo province_id
            if (!isset($provinceInvestor[$v['province_id']]['total_sum'])) {
                $provinceInvestor[$v['province_id']]['total_sum'] = 0;
            }

            // Tính tổng số lượng gói thầu trúng/trượt
            if (!isset($provinceInvestor[$v['province_id']]['result_status_win'])) {
                $provinceInvestor[$v['province_id']]['result_status_win'] = 0;
            }

            if (!isset($provinceInvestor[$v['province_id']]['result_status_lost'])) {
                $provinceInvestor[$v['province_id']]['result_status_lost'] = 0;
            }

            // Tính tổng số lượng gói liên danh
            if (!isset($provinceInvestor[$v['province_id']]['partnership_id'])) {
                $provinceInvestor[$v['province_id']]['partnership'] = 0;
            }

            // Tổng chi phí dự án
            $provinceInvestor[$v['province_id']]['total_sum'] += $v['info_tbmt']['total_cost'];

            // Trúng thầu
            $provinceInvestor[$v['province_id']]['result_status_win'] += ($v['info_tbmt']['result_status'] == 1) ? 1: 0;

            // Trượt thầu
            $provinceInvestor[$v['province_id']]['result_status_lost'] += ($v['info_tbmt']['result_status'] == 0) ? 1: 0;

            // Liên danh
            $provinceInvestor[$v['province_id']]['partnership'] += ($v['info_tbmt']['partnership'] == 1) ? 1: 0;
            $provinceInvestor[$v['province_id']]['info'] = $province_list[$v['province_id']];

            unset($v['info_tbmt']);
            // Thêm vào danh sách tỉnh
            $provinceInvestor[$v['province_id']]['data'][] = $v;
        }
    }
}

$array_data['list_province'] = $provinceInvestor;
$array_data['link_summary_province'] = $nv_Lang->getModule('title_summary_province', $array_data['org_fullname'], $countProvince, ($_data_static['arr_static']['num_tbmt'] ?? 0), nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['investors'] . '/' . $array_data['alias'] . '-' . $array_data['id'] . '/locations', true));

// Không có gói VIP kiểm tra xem gói VIP hết hạn hay chưa có gói VIP
$vip3_renew = 0;
$link = '';

// Hiển thị link đăng nhập, đăng ký người dùng và đăng ký VIP 3 khi chưa đăng nhập
if (!defined('NV_IS_USER')) {
    $link_login = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    $link = sprintf($nv_Lang->getModule('view_result_one_1'), $link_register, nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3'));
} else {
    // Không Tồn tại gói VIP thì mới kiểm tra tiếp
    if (!(isset($global_array_vip[3]) and !isset($global_array_vip[31]))) {
        $vip3_renew = $db->query('SELECT id FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id=' . $user_info['userid'] . ' AND status != 1 AND (vip = 3 OR vip = 31) AND prefix_lang = ' . BID_LANG_DATA)->fetchColumn();
        if (!empty($vip3_renew)) {
            $link = $nv_Lang->getModule('view_result_user_view1_renew', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3', true));
        } else {
            // Không có GÓI VIP nào thì hiện link đăng ký
            $link = $nv_Lang->getModule('view_result_user_view1', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=vip&form=1&vip=3', true));
        }
    }
}

$contents = nv_theme_bidding_investors_detail($array_data, $arrBmt, $_data_static);
// Kiểm tra nếu gói VIP 3 hết hạn
if (!empty($link) and !empty($vip3_renew)) {
    // Nếu hết hạn
    $contents = str_replace("##title__vip##", $nv_Lang->getModule('title_renew_vip'), $contents);
    $contents = str_replace("##link__vip##", NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3', $contents);
} else if (!empty($link)){
    $contents = str_replace("##title__vip##", $nv_Lang->getModule('title_resg_vip'), $contents);
    $contents = str_replace("##link__vip##", NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3', $contents);
}

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';

function getTimelineInvestor($array_data, $_data_static) {
    global $module_file, $global_config, $nv_Lang, $module_info, $db, $module_name;
    $xtpl = new XTemplate('investor-timeline.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('DATA', $array_data);

    $arrSolicitorIds = [];
    // Danh sách dự án
    $listProject = $_data_static['arr_row']['list_project_proposal_full'] ?? [];
    if (!empty($listProject)) {
        foreach ($listProject as $k => $v) {
            if ($v['solicitor_id'] > 0) {
                $arrSolicitorIds[$v['solicitor_id']] = $v['solicitor_id'];
            }
        }
    }

    //Lấy thông tin list solicitors
    if (!empty($arrSolicitorIds)) {
        $db->sqlreset()
            ->select('id, alias')
            ->from(BID_PREFIX_GLOBAL . '_solicitor')
            ->where('id IN (' . implode(',', $arrSolicitorIds) . ')');
        $sth = $db->prepare($db->sql());
        $sth->execute();
        $arrBmt = [];
        while ($rowSolicitor = $sth->fetch()) {
            $arrBmt[$rowSolicitor['id']] = $rowSolicitor['alias'];
        }
    }

    $arr_duan = [
        $nv_Lang->getModule('project_ppp'),
        $nv_Lang->getModule('project_dtsdd'),
        $nv_Lang->getModule('project_xhh'),
        $nv_Lang->getModule('project_dtsdd')
    ];

    // Danh sách dự án
    if (!empty($listProject)) {
        foreach ($listProject as $k => $v) {
            // Lấy 6 dự án hiển thị ra timeline
            if ($k < 6) {
                // Chẵn
                if ($k%2 != 0) {
                    $xtpl->parse('main.timeline.loop.le');
                }

                $v['link_project'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['projectdetail'] . '/' . $v['alias'] . '-' . $v['code'];
                if ($v['solicitor_id'] > 0 && isset($arrBmt[$v['solicitor_id']])) {
                    $v['link_don_vi'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['solicitor'] . '/' . $arrBmt[$v['solicitor_id']] . '-' . $v['solicitor_id'];
                } else {
                    $v['link_don_vi'] = "javascript:void(0)";
                }

                $v['type'] = $arr_duan[$v['type_project']] ?? [];
                $v['time_post'] = nv_date('d/m/Y H:i:s', $v['time_post']);
                $v['detail'] = $nv_Lang->getModule('title_investor_detail_timeline', $array_data['detail_link'], $array_data['org_fullname'], $v['link_project'], $v['ten_du_an'], $v['type'], $v['link_don_vi'], $v['don_vi'], $v['link_project'], $v['time_post']);
                $xtpl->assign('PROJECT', $v);
                $xtpl->parse('main.timeline.loop');
            }
        }
        $xtpl->parse('main.timeline');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}
