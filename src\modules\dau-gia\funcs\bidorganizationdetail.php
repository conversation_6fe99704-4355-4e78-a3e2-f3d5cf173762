<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 10 Nov 2020 06:56:08 GMT
 */
if (!defined('NV_IS_MOD_DAU_GIA')) {
    die('Stop!!!');
}
/*
 * // Chỉ thành viên hoặc Bot mới vào đây được
 * if (!(defined('NV_IS_USER') or $client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
 * $url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=users&amp;' . NV_OP_VARIABLE . '=login&amp;nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']), true);
 * $contents = nv_theme_alert($nv_Lang->getModule('info'), $nv_Lang->getModule('info_login'), 'info', $url, $nv_Lang->getModule('info_redirect_click'), 5);
 * include NV_ROOTDIR . '/includes/header.php';
 * echo nv_site_theme($contents);
 * include NV_ROOTDIR . '/includes/footer.php';
 * }
 *
 * // thành viên thì hiển thị thông báo đóng cửa, chỉ cho view, vip xem chi tiết
 * if ($type_user == 1 and !$client_info['is_bot']) {
 * $contents = '<div class="alert alert-warning"><div style="text-align: justify;"><strong>1.&nbsp;Từ ngày 27/04/2021</strong>, DauThau.info sẽ <a href="/news/tin-tuc/thong-bao-ngung-cung-cap-dich-vu-tra-cuu-thong-tin-thau-mien-phi-cho-cac-tai-khoan-dang-ky-mien-phi-367.html"><strong>ngừng cung cấp dịch vụ tra cứu thông tin thầu miễn phí</strong></a> cho các tài khoản đăng ký miễn phí trên dauthau.asia <strong>để tránh bị quy kết là cung cấp thông tin không đầy đủ</strong>. Nhà thầu&nbsp;muốn xem thông tin gói thầu cần <strong><a href="/vip/?plan=1">đăng ký mua gói phần mềm VIEWEB hoặc gói phần mềm VIP</a></strong>.<br><strong>2. Từ ngày 06/05/2021</strong>, DauThau.info sẽ áp dụng bảng giá mới do&nbsp;thay đổi về chính sách của cơ quan quản lý nhà nước không cho quét dữ liệu tự động dẫn tới các thay đổi về chi phí&nbsp;bảo trì và phát triển Phần mềm “săn” thông tin thầu DauThau.info. Chi tiết&nbsp;xem <a href="/news/blog/nang-cap-chuyen-sang-nhap-lieu-thu-cong-bang-gia-moi-369.html"><strong>tại đây</strong></a>.</div></div>';
 * include NV_ROOTDIR . '/includes/header.php';
 * echo nv_site_theme($contents);
 * include NV_ROOTDIR . '/includes/footer.php';
 * }
 */

use NukeViet\Point\Point;
use NukeViet\Dauthau\Share;

$arr_view = [];
$sql = "SELECT config_name, config_value FROM " . $db_config['prefix'] . "_config WHERE module = 'dau-gia'";
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_view[$row['config_name']] = $row;
}
$key_words = $module_info['keywords'];

$array_data = [];
$array_data_asset = [];
$array_data_craws = [];
$space = ', ';

$db->sqlreset()
    ->select('*')
    ->from($db_config['prefix'] . '_' . 'dau_gia_bid_select')
    ->where('id_bid = ' . $id_bid);
$sql = $db->sql();
$result = $db->query($sql);
$array_data = $db->query($sql)->fetch();

if (!empty($array_data)) {
    // Gọi hàm confirm update crawl
    Share::submitConfirmCrawl();

    // Update
    if (defined('NV_IS_USER') && $nv_Request->isset_request('update', 'post,get')) {
        $id = $nv_Request->get_int('id_update', 'post, get', 0);
        $check = $nv_Request->get_title('check', 'post, get', '');
        if (!($id > 0 and $check == md5(intval($user_info['userid']) . $id . NV_CACHE_PREFIX . $client_info['session_id']))) {
            nv_jsonOutput([
                'res' => 'error',
                'mess' => $nv_Lang->getModule('update_err'),
                'time' => 5 * 60,
                'line' => 38
            ]);
        }

        // Đoạn này kiểm tra xem tin này đã được cập nhật chưa, nếu tin này đã được người dùng trước bấm cập nhật rồi, thì người dùng sau phải đợi 10p mới được bấm tiếp
        $last = [];
        if (!empty($module_config['bidding']['up_request_user_inteval'])) {
            $last = $db->query('SELECT last_reload FROM ' . BID_PREFIX_GLOBAL . '_update_user WHERE userid=' . $user_info['userid'] . ' AND auction_selection_id =' . $id . ' ORDER BY last_reload DESC LIMIT 1')->fetch();
            if (!empty($last)) {
                // Nếu cập nhật rồi thực hiện kiểm tra xem: Thời gian hiện tại - Thời gian bấm cập nhật - thời gian cấu hình
                // 1743561774 - 1743561094 - 600
                $pas = NV_CURRENTTIME - $last['last_reload'] - $module_config['bidding']['up_request_user_inteval'] * 60;
                if ($pas < 0) {
                    nv_jsonOutput([
                        'res' => 'error',
                        'mess' => sprintf($nv_Lang->getModule('update_err_user_last'), nv_convertfromSec(abs($pas))),
                        'time' => abs($pas),
                        'line' => 50
                    ]);
                }
            }
        }

        // Kiểm tra xem có dữ liệu chi tiết của thông báo đấu giá kh
        if (empty($array_data) && $array_data['id_bid'] != $id) {
            nv_jsonOutput([
                'res' => 'error',
                'mess' => $nv_Lang->getModule('data_not_exited')
            ]);
        }

        $url_detail = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=bidorganization/' . $array_data['alias'] . '-' . $array_data['id_bid'] . $global_config['rewrite_exturl'], true);

        // Đoạn này kiểm tra chỉ phép cập nhật lại tin sau 30p
        if (!empty($module_config['bidding']['up_request_inteval'])) {
            $pas = NV_CURRENTTIME - $array_data['update_at'] - $module_config['bidding']['up_request_inteval'] * 60;
            if ($pas < 0) {
                nv_jsonOutput([
                    'res' => 'error',
                    'mess' => sprintf($nv_Lang->getModule('update_err_new'), nv_convertfromSec(abs($pas))),
                    'time' => abs($pas)
                ]);
            }
        }

        $info = [
            'res' => 'error',
            'mess' => $nv_Lang->getModule('update_err_unknown'),
            'time' => 60,
            'line' => 75
        ];

        // Kiểm tra xem khách hàng còn điểm không?
        $customs_points = Point::getMyPoint();
        $sodiem = Point::getAllConfig()['point_crawl_mintus'];
        $sodiem = (isset($sodiem) ? $sodiem : 0);

        // Thông báo nếu khách hàng không còn điểm
        if (intval($customs_points['point_total']) < intval($sodiem)) {
            nv_jsonOutput([
                'res' => 'error',
                'mess' => Share::langErrorUserNoPoint()
            ]);
        }

        // Thực hiện kiểm tra bảng bóc tin
        $check_crawl = $db->query("SELECT * FROM " . $db_config['prefix'] . "_dau_gia_craws_select WHERE id_source = " . $array_data['id_source'])->fetch();

        if (empty($check_crawl)) {
            // Thêm mới
            $db->query("INSERT INTO " . $db_config['prefix'] . "_dau_gia_craws_select (id_source, title, publish_time, time_start, time_end) VALUES (" . $array_data['id_source'] . ", " . $db->quote($array_data['title']) . ", " . $array_data['date_bid'] . ", " . $array_data['opening_reg'] . ", " . $array_data['closing_reg'] . ")");
        } else {
            // Update
            $db->query("UPDATE " . $db_config['prefix'] . "_dau_gia_craws_select SET time_crawl = 0 WHERE id_source = " . $array_data['id_source']);
        }

        $info = [
            'res' => 'ok',
            'mess' => $nv_Lang->getModule('update_status_1') . ". " . $nv_Lang->getModule('browser_refresh_5'),
            'time' => 300
        ];

        $data = [
            'title' => $array_data['title'],
            'link' => urlRewriteWithDomain($url_detail, NV_MY_DOMAIN)
        ];

        $data = json_encode($data);

        if (!empty($last)) {
            $exec = $db->exec("UPDATE " . BID_PREFIX_GLOBAL . "_update_user SET last_reload = " . NV_CURRENTTIME . " WHERE userid = " . $user_info['userid'] . " AND auction_selection_id = " . $id);
        } else {
            $message = [
                'vi' => sprintf(get_lang('vi', 'title_minus_points'), $sodiem, sprintf(get_lang('vi', 'title_note_tbdg'), $array_data['title'])),
                'en' => sprintf(get_lang('en', 'title_minus_points'), $sodiem, sprintf(get_lang('en', 'title_note_tbdg'), $array_data['title']))
            ];

            $res = Share::insertBiddingUpdateUser($id, 'auction_selection_id', $data, $sodiem, json_encode($message));
            if ($res == -1) {
                nv_jsonOutput([
                    'res' => 'error',
                    'mess' => Share::langErrorTransactionPoint()
                ]);
            }
        }

        nv_jsonOutput($info);
    }

    if (NV_CURRENTTIME >= $close_time_dauthau and NV_CURRENTTIME <= $open_maunal_time_dauthau and $type_user == 2) {
        // view k dc xem các tin lớn hơn ngày close, chỉ xem dc tin cũ
        // vip dc xem do gửi qua mail
        if ($array_data['opening_bid'] > $close_time_dauthau) {
            nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
        }
    }
    // kiểm tra vip
    $is_vip6_renew = false;
    if (defined('NV_IS_USER')) {
        // kiểm tra vip
        $arr_vip = [];
        if (!empty($global_array_vip)) {
            $arr_vip = $global_array_vip;
        }
        if (!empty($arr_vip)) {
            define('NV_IS_VIP', true);
            if (isset($arr_vip[6])) {
                define('NV_IS_VIP6', true);
            }
        }
        if (!defined('NV_IS_VIP6')) {
            // Kiểm tra nếu gói VIP 6 hết hạn
            $vip6_renew = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id=' . $user_info['userid'] . ' AND status != 1 AND vip =6 AND prefix_lang = ' . BID_LANG_DATA)->fetch();
            if (!empty($vip6_renew)) {
                $is_vip6_renew = true;
            }
        }
    }

    if ($alias != $array_data['alias']) {
        // Kiểm tra lại url có đúng với CSDL, nếu không đúng điều hướng lại
        $url_Permanently = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=bidorganization/' . $array_data['alias'] . '-' . $array_data['id_bid'] . '.html');
        nv_redirect_location($url_Permanently);
    }
    if (!empty($array_data['file_detail'])) {
        $array_data['file_detail'] = json_decode($array_data['file_detail'], true);
    } else {
        $array_data['file_detail'] = [];
    }

    $page_title = $array_data['title'];

    $array_mod_title[] = array(
        'title' => $nv_Lang->getModule('thong_bao_to_chuc_dau_gia'),
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=bidorganization', true)
    );
    $array_mod_title[] = array(
        'title' => $array_data['title'],
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=bidorganization/' . $array_data['alias'] . '-' . $array_data['id_bid'] . '.html', true)
    );

    if (!empty($array_data['id_bid_parent'])) {
        $db->sqlreset()
            ->select('alias')
            ->from($db_config['prefix'] . '_dau_gia_bid_select')
            ->where('id_bid = ' . $array_data['id_bid_parent']);
        $sql = $db->sql();

        $array_data['alias_parent'] = $db->query($sql)->fetch();
    }

    /*
    * Query lấy thông tin của tổ chức đấu giá
    */
    if (!empty($array_data['chosen_org_id'])) {
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_dau_gia_bidder WHERE id_bidder =" . $array_data['chosen_org_id'];
        $result = $db->query($sql);
        if ($row = $result->fetch()) {
            $array_data['chosen_org_title'] .= '<a href="' . nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization/' . $row['alias']) . '-' . $array_data['chosen_org_id'] . '.html' . '">' . $row['name_bidder'] . '</a>';
        }
    }

    /*
     * Parsing các file thông báo kết quả
     */

    $tmp_result_files = json_decode($array_data['result_files'], true);
    if (!empty($tmp_result_files)) {
        $array_data['result_files'] = implode('<br>', array_map(function ($file) {
            return '<a rel="nofollow" href="https://dgts.moj.gov.vn/common/download?name=' . str_replace(' ', '+', $file['fileName'] . '&path=' . $file['linkFile']) . '">' . $file['fileName'] . '</a>';
        }, $tmp_result_files));
    } else {
        $array_data['result_files'] = '';
    }

    /*
     * Xử lý địa chỉ người sở hữu tài sản
     */

    $sql = "SELECT title, alias FROM " . NV_PREFIXLANG . "_location_district WHERE id =" . $array_data['id_district_owner'];
    $result = $db->query($sql);
    if ($row = $result->fetch()) {
        $array_data['address_owner'] .= $space;
        $array_data['address_owner'] .= '<a href="' . nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=bidorganization/H-' . $row['alias'] . '-' . $array_data['id_district_owner']) . '">' . $row['title'] . '</a>';
    }

    $sql = "SELECT title, alias FROM " . NV_PREFIXLANG . "_location_province WHERE id =" . $array_data['id_province_owner'];
    $result = $db->query($sql);
    if ($row = $result->fetch()) {
        $array_data['address_owner'] .= $space;
        $array_data['address_owner'] .= '<a href="' . nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=bidorganization/T-' . $row['alias'] . '-' . $array_data['id_province_owner']) . '">' . $row['title'] . '</a>';
    }

    $db->sqlreset()
        ->select('*')
        ->from($db_config['prefix'] . '_' . 'dau_gia_asset_select')
        ->where($db_config['prefix'] . '_' . 'dau_gia_asset_select.id_bid = ' . $array_data['id_source']);
    $sql = $db->sql();

    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $array_data_asset[$row['id_asset']] = $row;
        if (!empty($array_data_asset[$row['id_asset']]['file_detail'])) {
            $array_data_asset[$row['id_asset']]['file_detail'] = json_decode($array_data_asset[$row['id_asset']]['file_detail'], true);
        } else {
            $array_data_asset[$row['id_asset']]['file_detail'] = [];
        }
    }
    // lấy dữ liệu từ bảng nv4_dau_gia_craws
    $db->sqlreset()
        ->select('*')
        ->from($db_config['prefix'] . '_' . 'dau_gia_craws_select')
        ->where($db_config['prefix'] . '_' . 'dau_gia_craws_select.id_source = ' . $array_data['id_source']);
    $sql = $db->sql();

    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $array_data_craws[$row['id_source']] = $row;
    }
    /*
     * Đếm số lượt xem
     */
    if (!$nv_Request->isset_request($module_data . '_' . $op . '_' . $id_bid, 'session')) {
        $nv_Request->set_Session($module_data . '_' . $op . '_' . $id_bid, NV_CURRENTTIME);
        $array_data['viewcount'] += 1;
        $db->query('UPDATE ' . $db_config['prefix'] . '_' . 'dau_gia_bid_select SET viewcount = viewcount + 1 WHERE id_bid=' . $id_bid);
    }

    /*
     * Kết thúc đếm số lượt xem
     */
    /*
     * Hiển thị nguồn tin
     */

    if ($arr_view['view_type']['config_value'] != '1') {
        $array_data['view_type']['name'] = $nv_Lang->getModule('ctt');
        if ($arr_view['view_type']['config_value'] == '3') {
            $array_data['view_type']['link'] = 'https://dgts.moj.gov.vn/thong-bao-lua-chon-to-chuc-dau-gia/' . change_alias($array_data_craws[$id_bid]['title']) . '-' . $array_data_craws[$id_bid]['id_source'] . '.html';
        }
    }

    if ($arr_view['view_obj']['config_value'] == '3') {
        if (!defined('NV_IS_ADMIN') || !defined('NV_IS_SPADMIN')) {
            $array_data['view_type'] = '';
        }
    } elseif ($arr_view['view_obj']['config_value'] == '2') {
        if (!defined('NV_IS_VIP')) {
            $array_data['view_type'] = '';
        }
    }
    $array_data['vip'] = "true";
    if ($arr_view['view_vip']['config_value'] == '2') {
        if (!defined('NV_IS_VIP6')) {
            if ($is_vip6_renew) {
                $array_data['vip'] = "renew";
            } else {
                $array_data['vip'] = "false";
            }
        }
    }

    // lấy các thông báo liên quan
    if ($array_data['id_bid_parent'] > 0) {
        $sql = "SELECT alias, id_bid, re_post FROM " . $db_config['prefix'] . "_dau_gia_bid_select WHERE id_bid_parent = " . $array_data['id_bid_parent'] . ' AND id_bid != ' . $array_data['id_bid'];
        $result = $db->query($sql);
        while ($row = $result->fetch()) {
            $array_data['other'][$row['id_bid']] = $row;
        }
    }

    // Kiểm tra xem thời gian góc lần đầu hay k
    if ($array_data['create_at'] != $array_data['update_at']) {
        $array_data['update_info'] = sprintf($nv_Lang->getModule('update_info_last'), nv_datetime_format($array_data['update_at'], 1));
    } else {
        $array_data['update_info'] = $nv_Lang->getModule('first_update_info');
    }
    $array_data['update_at_original'] = $array_data['update_at'];
    $array_data['update_at'] = nv_datetime_format($array_data['update_at'], 1);

    // Lấy thông tin kết quả lựa chọn tổ chức đấu giá
    $result_data = $db->query("SELECT * FROM " . $db_config['prefix'] . '_' . $module_data . '_bid_select_results WHERE id_select = ' . $array_data['id_bid'])->fetch();
    if(!empty($result_data)) {
        $result_data['url'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=resultorganization/' . $result_data['alias'] . '-' . $result_data['id'] . '.html', true);
    }

    $page_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=bidorganization/' . $array_data['alias'] . '-' . $array_data['id_bid'] . '.html';
    $canonicalUrl = getCanonicalUrl($page_url);
    $contents = nv_theme_dau_gia_bidorganizationdetail($array_data, $array_data_asset, $array_data_craws, $result_data);

    // Schema: NewsArticle
    $my_schema = [
        "@context" => "https://schema.org",
        "@type" => "CreativeWork",
        "@id" => $canonicalUrl,
        "url" => $canonicalUrl,
        "name" => $array_data['title'],
        "headline" => $array_data['title'],
        "description" => $array_data['title'],
        "inLanguage" => NV_LANG_DATA,
        "datePublished" => nv_date("Y-m-d\TH:i:sP", $array_data['opening_bid']),
        "dateModified" => nv_date("Y-m-d\TH:i:sP", $array_data['update_at_original']),
        "creator" => [
            "@type" => "Organization",
            "name" => $array_data['name_owner']
        ],
        "temporalCoverage" => nv_date("Y-m-d", $array_data['opening_reg']) . '/' . nv_date("Y-m-d", $array_data['closing_reg']),
        "locationCreated" => [
            "@type" => "Place",
            "address" => [
                "@type" => "PostalAddress",
                "streetAddress" => strip_tags($array_data['address_owner']),
                "addressLocality" => $db->query("SELECT title FROM " . $db_config['prefix'] . "_vi_location_district WHERE id=" . $array_data['id_district_owner'])->fetchColumn(),
                "addressRegion" => $db->query("SELECT title FROM " . $db_config['prefix'] . "_vi_location_province WHERE id=" . $array_data['id_province_owner'])->fetchColumn(),
                "addressCountry" => "VN"
            ]
        ],
        "publisher" => [
            "@type" => "Organization",
            "name" => "DauGia.net",
            "url" => "https://daugia.net"
        ]
    ];
    $nv_schemas[] = $my_schema;
} else {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=bidorganization');
    die();
}
include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
