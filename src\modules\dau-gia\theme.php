<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 10 Nov 2020 06:56:08 GMT
 */
if (!defined('NV_IS_MOD_DAU_GIA')) {
    die('Stop!!!');
}

/**
 * nv_theme_dau_gia_main()
 *
 * @param mixed $array_data
 * @return
 */
function nv_theme_dau_gia_main($array_data, $generate_page, $module_config_view, $arr_unexist_key, $filter_url, $error = [], $total = 0)
{
    global $module_info, $op, $module_name, $module_config, $nv_Lang, $global_config;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_MY_DOMAIN', NV_MY_DOMAIN);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);
    $xtpl->assign('TOTAL_RESULTS', number_format($total, 0, ",", "."));

    $nv_Lang->setModule('empty_result', sprintf($nv_Lang->getModule('empty_result'), $module_config['dau-gia']['view_guide']));
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    if (!empty($error)) {
        $xtpl->assign('ERROR', implode('<br />', $error));
        $xtpl->parse('main.error');
    }

    // Hiển thị danh sách đơn đấu giá
    if (!empty($array_data)) {
        if (!empty($arr_unexist_key)) {
            $key_filter = implode(',', $arr_unexist_key);
            $xtpl->assign('LINK_ADD', sprintf($nv_Lang->getModule('add_key_filter'), $key_filter, $filter_url, $module_config['dau-gia']['view_guide']));
            $xtpl->parse('main.add_filter');
        }
        foreach ($array_data as $row) {
            $row['opening_reg_format'] = nv_date("H:i d/m/Y", $row['opening_reg']);
            $row['closing_reg_format'] = nv_date("H:i d/m/Y", $row['closing_reg']);
            $row['opening_bid_format'] = nv_date("H:i d/m/Y", $row['opening_bid']);
            $row['date_bid_format'] = nv_date("H:i d/m/Y", $row['date_bid']);
            if ($module_config_view['view_detail']) {
                $row['url_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $row['alias'] . '-' . $row['id_bid'] . '.html';
                if ($global_config['iddomain'] != 2) {
                    $row['url_detail'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/' . $row['alias'] . '-' . $row['id_bid'] . '.html';
                }
            } else {
                $row['url_detail'] = 'https://dgts.moj.gov.vn/thong-bao-cong-khai-viec-dau-gia/' . change_alias($array_data[$row['id_bid']]['alias']) . '-' . $array_data[$row['id_bid']]['id_bid'] . '.html';
            }
            $xtpl->assign('ROW', $row);
            $xtpl->parse('main.view.loop');
        }
        if ($generate_page) {
            $xtpl->assign('GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.view.page');
        }
        $xtpl->parse('main.view');
    } else {
        $xtpl->parse('main.empty');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_theme_dau_gia_detail()
 *
 * @param mixed $array_data
 * @return
 */
function nv_theme_dau_gia_detail($row, $array_data_asset, $array_data_craws, $_dt_view_auction_costs)
{
    global $module_info, $op, $module_name, $client_info, $global_config, $module_config, $nv_Lang, $nv_Request, $user_info, $module_captcha;
    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);
    $xtpl->assign('SELFURL', $client_info['selfurl']);
    $xtpl->assign('NUM_DOWNLOAD', $nv_Lang->getModule('title_view_download', nv_number_format($row['view_download'] ?? 0)));
    $html = '';
    $file_detail_html = '';

    $b = 0;
    $reg_link = DAUTHAU_INFO_DOMAIN . '/vip/?form=1&amp;vip=6';
    $renew_link = DAUTHAU_INFO_DOMAIN . '/vip/?renewal=1&amp;vip=6';
    if ($row['vip'] === 'renew') {
        $redirect = sprintf($nv_Lang->getModule('vip6_renew_link'), $renew_link);
    } else {
        $redirect = sprintf($nv_Lang->getModule('vip6_form_link'), $reg_link);
    }
    if (!empty($array_data_asset)) {
        $i = 1;
        foreach ($array_data_asset as $asset) {
            $asset['stt'] = $i;
            $asset['min_bid_prices'] = str_replace(',', '.', number_format($asset['min_bid_prices']));
            $asset['min_bid_prices'] .= $nv_Lang->getModule('currency');
            $html2 = '';
            $abc = [];
            $b = 0;
            if (($asset['deposit']) != 0) {
                if ($asset['deposit'] < 30) {
                    $html2 = $asset['deposit'] . '%';
                } else {
                    $html2 = str_replace(',', '.', number_format($asset['deposit'])) . $nv_Lang->getModule('currency');
                }
                $asset['deposit'] = $html2;
            }
            if (empty($asset['qty'])) {
                $asset['qty'] = '';
            }
            if ($row['vip'] == 'false') {
            }
            $xtpl->assign('ASSET', $asset);
            $xtpl->parse('main.asset');
            $i++;
        }
    }
    if (!empty($array_data_craws)) {
        foreach ($array_data_craws as $crawl) {
            $crawl['time_crawl_format'] = nv_date("H:i d/m/Y", $crawl['time_crawl']);
            $xtpl->assign('CRAWL', $crawl);
            $xtpl->parse('main.crawl');
        }
    }

    $row['date_bid_c'] = $row['date_bid'];
    $row['closing_deposit_c'] = $row['closing_deposit'];
    $row['opening_reg_c'] = $row['opening_reg'];
    $row['closing_reg_c'] = $row['closing_reg'];
    $row['date_bid_format'] = $row['date_bid'] > 0 ? nv_date("H:i d/m/Y", $row['date_bid']) : '';
    $row['opening_bid_format'] = $row['opening_bid'] > 0 ? nv_date("H:i d/m/Y", $row['opening_bid']) : '';
    $row['opening_reg_format'] = $row['opening_reg'] > 0 ? nv_date("H:i d/m/Y", $row['opening_reg']) : '';
    $row['closing_reg_format'] = $row['closing_reg'] > 0 ? nv_date("H:i d/m/Y", $row['closing_reg']) : '';
    $row['closing_deposit_format'] = $row['closing_deposit'] > 0 ? nv_date("H:i d/m/Y", $row['closing_deposit']) : '';
    $row['opening_deposit_format'] = $row['opening_deposit'] > 0 ? nv_date("H:i d/m/Y", $row['opening_deposit']) : '';
    if (!empty($row['file_detail'])) {
        $file_detail_html = '';
        $file_detail_html = "";
        foreach ($row['file_detail'] as $file) {
            $file_detail_html .= '<div><p><b><a rel="nofollow" href="' . str_replace(' ', '+', 'https://dgts.moj.gov.vn/common/download?name=' . $file['fileName'] . '&path=' . $file['linkFile']) . '">' . $file['fileName'] . '</a></b></p></div>';
        }
        $row['link_detail'] = $file_detail_html;
    }
    if (!empty($row['view_type'])) {
        $_view_type = '';
        if ($row['view_type']['name']) {
            $_view_type = "";
            if (!empty($row['view_type']['link'])) {
                $_view_type .= '<div><p><b><a rel="nofollow" href="' . $row['view_type']['link'] . '">' . $row['view_type']['name'] . '</a></b></p></div>';
            } else {
                $_view_type .= '<div><p><b>' . $row['view_type']['name'] . '</b></p></div>';
            }
        }
        $row['view_type'] = $_view_type;
    }
    // <a href="{ROW.doc}"><i class="fa fa-file-word-o" aria-hidden="true" style="margin-left: 86%"> Xuất File DOC</i></a><br>
    $row['doc_link'] = 'https://dgts.moj.gov.vn/portal/exportWord?aucInfoId=' . $row['id_source'];
    if ($row['vip'] != 'true') {
        $row['detail'] = $redirect;
        $row['address'] = $redirect;
        $row['phone'] = $redirect;
        $row['fax'] = $redirect;
        $row['link_detail'] = '';
        $row['doc'] = '';
    }

    if (!defined('NV_IS_VIP6')) {
        $receive_email = [];
        $receive_email['content'] = sprintf($nv_Lang->getModule('receive_email_content'), $row['title'], ($row['vip'] === 'renew' ? $renew_link : $reg_link), ($row['vip'] === 'renew' ? $nv_Lang->getModule('renew') : $nv_Lang->getModule('register')), 'VIP6');
        $receive_email['reg_link'] = $row['vip'] === 'renew' ? $renew_link : $reg_link;
        $receive_email['vip_paket'] = $row['vip'] === 'renew' ? $nv_Lang->getModule('vip6_renew') : $nv_Lang->getModule('vip6_form');
        $xtpl->assign('RECEIVE_EMAIL', $receive_email);
        $xtpl->parse('main.receive_email_content');
    }

    if (stripos($row['title'], "Bất động sản") !== false || stripos($row['title'], "Quyền sử dụng đất") !== false) {
        $meeymap_data = [
            'title' => $nv_Lang->getModule('panel_meeymap_title'),
            'content' => $nv_Lang->getModule('panel_meeymap_content' . (defined('NV_IS_VIP6') ? '_vip6' : '')),
            'link' => DAUTHAU_INFO_DOMAIN . '/page/tinh-nang-bang-gia-meey-map.html',
            'register_now' => $nv_Lang->getModule('register_now'),
        ];
        $xtpl->assign('MEEYMAP_DATA', $meeymap_data);
        $xtpl->parse('main.show_panel_meeymap');
    }


    $filter = [];
    $filter['content'] = sprintf($nv_Lang->getModule('filter_content'), $row['title']);
    if (!defined('NV_IS_USER')) {
        $filter['reg_link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=register&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
        $filter['btn_title'] = $nv_Lang->getModule('btn_register');
    } else {
        $filter['reg_link'] = DAUTHAU_INFO_DOMAIN . '/filters/';
        $filter['btn_title'] = $nv_Lang->getModule('btn_filter');
    }

    $xtpl->assign('FILTER', $filter);
    $xtpl->assign('ROW', $row);

    if (defined('NV_IS_USER') and $module_captcha == 'recaptcha' and $global_config['recaptcha_ver'] == 2) {
        $xtpl->assign('CHECKSESS_UPDATE', md5($user_info['userid'] . $row['id_bid'] . NV_CACHE_PREFIX . $client_info['session_id']));
        $xtpl->assign('RECAPTCHA_ELEMENT', 'recaptcha' . nv_genpass(8));
        $xtpl->assign('N_CAPTCHA', $nv_Lang->getGlobal('securitycode1'));
        $xtpl->parse('main.recaptcha');
        $xtpl->parse('main.update');
    }

    $arr_ld = array(
        'title' => str_replace(['\\', '"'], ['\\\\\\\\', '\"'], $row['title']),
        'datePublished' => date('c', $row['date_bid_c']),
        'dateModified' => date('c', $row['closing_deposit_c']),
        'brand_name' => str_replace(['\\', '"'], ['\\\\\\\\', '\"'], trim($row['name_owner'])),
        'site_name' => $global_config['site_name'],
        'url' => $row['link__detail'],
        'url_logo' => $row['link_site'],
        'validFrom' => date('c', $row['opening_reg_c']),
        'validThrough' => date('c', $row['closing_reg_c'])
    );
    $xtpl->assign('SCHEMA_IMAGE', NV_STATIC_URL . 'themes/' . $module_info['template'] . '/images/no_image.gif');
    $xtpl->assign('ARR_LD', $arr_ld);
    $xtpl->parse('main.json_ld');

    if (!(defined('NV_IS_USER') or $client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
        global $page_url;
        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $link__popup = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
        // Gọi hàm Popuplogin và truyền tham số link vào
        $content_popup = PopupLogin($link__popup, nv_url_rewrite($page_url, true));
        $xtpl->assign('POPUP_LOGIN', $content_popup);
        $xtpl->parse('main.popup_login');
    }

    if (defined('NV_IS_USER')) {
        $xtpl->parse('main.show_download');
    }
    $xtpl->assign('COST', $_dt_view_auction_costs);
    $xtpl->assign('LINK_REG', DAUTHAU_INFO_DOMAIN . '/vip/?plan=1');
    $xtpl->assign('CHECK_VIEW_AUCTION_COST', md5('view_auction_costs' . $user_info['userid'] . $row['id_bid'] . NV_CACHE_PREFIX . $client_info['session_id']));
    $xtpl->assign('CHECK_POINT_AUCTION_COST', md5('point_auction_costs' . $user_info['userid'] . $row['id_bid'] . NV_CACHE_PREFIX . $client_info['session_id']));
    if (!empty($_dt_view_auction_costs)) {
        $view_land_use = ($_dt_view_auction_costs['is_land_use'] == 1) ? '.view_land_use' : ($_dt_view_auction_costs['is_land_use'] == 2 ? '.view_land_use_b' : '.view_other_assets');
        if (defined('NV_IS_USER')) {
            $check_view_bid_cost = $nv_Request->get_title('view_auction_costs_point_' . $row['id_bid'], 'cookie');
            $xtpl->parse('main.btn_use_point_view_auction_costs_modal');
            if (defined('NV_IS_VIP_NO_VIEWEB') || defined('NV_IS_VIP_IS_VIEWEB') || $check_view_bid_cost == md5($user_info['userid'] . '_' . $row['id_bid'])) {
                $xtpl->assign('LOCK_AUCTION_COST', '');
                $xtpl->parse('main.view_auctioncosts' . $view_land_use . '.view_qsdd_is_vip');
                $xtpl->parse('main.view_auctioncosts' . $view_land_use . '.view_is_vip');
            } else if (defined('NV_IS_VIP_RENEW_NO_VIEWEB')) {
                $xtpl->assign('LOCK_AUCTION_COST', sprintf($nv_Lang->getModule('view_auction_cost_renew'), DAUTHAU_INFO_DOMAIN . '/vip/?renewal=1'));
                $xtpl->assign('LINK_RENEW', DAUTHAU_INFO_DOMAIN . '/vip/?renewal=1');
                $xtpl->parse('main.view_auctioncosts' . $view_land_use . '.view_qsdd_no_vip');
                $xtpl->parse('main.view_auctioncosts' . $view_land_use . '.view_no_vip');
                $xtpl->parse('main.view_auctioncosts.view_lock_vip.btn_use_point_auction_costs');
                $xtpl->parse('main.view_auctioncosts.view_lock_vip.is_user_renew');
                $xtpl->parse('main.view_auctioncosts.view_lock_vip');
            } else {
                $xtpl->assign('LOCK_AUCTION_COST', sprintf($nv_Lang->getModule('view_auction_cost_regvip'), DAUTHAU_INFO_DOMAIN . '/vip/?plan=1'));
                $xtpl->parse('main.view_auctioncosts' . $view_land_use . '.view_qsdd_no_vip');
                $xtpl->parse('main.view_auctioncosts' . $view_land_use . '.view_no_vip');
                $xtpl->parse('main.view_auctioncosts.view_lock_vip.btn_use_point_auction_costs');
                $xtpl->parse('main.view_auctioncosts.view_lock_vip.is_user');
                $xtpl->parse('main.view_auctioncosts.view_lock_vip');
            }
            $xtpl->parse('main.view_auctioncosts' . $view_land_use . '');
            $xtpl->parse('main.view_auctioncosts');
        } else {
            $xtpl->assign('LOCK_AUCTION_COST', sprintf($nv_Lang->getModule('log_in_up_auction_cost'), $link_register));
            $xtpl->parse('main.view_auctioncosts' . $view_land_use . '.view_qsdd_no_vip');
            $xtpl->parse('main.view_auctioncosts' . $view_land_use . '.view_no_vip');
            $xtpl->parse('main.view_auctioncosts.view_lock_vip.no_user');
            $xtpl->parse('main.view_auctioncosts.view_lock_vip');
            $xtpl->parse('main.view_auctioncosts' . $view_land_use . '');
            $xtpl->parse('main.view_auctioncosts');
        }
    }

    if (!empty($row['other'])) {
        foreach ($row['other'] as $k1 => $v) {
            foreach ($v as $k => $other) {
                $other['url_parent'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $other['alias'] . '-' . $other['id_bid'] . $global_config['rewrite_exturl'];

                // Hiển thị id xem thay đổi ở lần đăng thứ 2 trở đi
                if ($k1 > 1 and $k < 1) {
                    $other['id_view'] = $row['other'][$k1][$k]['id_bid'] . '-' . $row['other'][$k1 - 1][$k]['id_bid'];
                }

                $xtpl->assign('VALUE', $other);

                // Mặc định lấy bản ghi đầu tiên chính là bản ghi không trùng
                if ($k <= 0) {
                    if ($k1 > 1 && sizeof($row['other']) > 1) {
                        $xtpl->parse('main.thongbao_lq.loop.list.view_change');
                    }
                    $xtpl->parse('main.thongbao_lq.loop.list');
                } else if ($v[$k]['re_post'] == $v[$k]['re_post']) {
                    // Ở đây chúng ta kiểm tra nếu phiên sau mà bằng phiên bản trước thì hiểu rằng đó là phiên bản trùng
                    $xtpl->parse('main.thongbao_lq.loop.list_duplicate');
                }
            }
            $xtpl->parse('main.thongbao_lq.loop');
        }

        if (sizeof($row['other']) > 1) {
            $xtpl->parse('main.thongbao_lq.show_btn');
        }
        $xtpl->parse('main.thongbao_lq');
    }

    if (!empty($row['fax'])) {
        $xtpl->parse('main.fax');
    }
    if (!empty($row['link_detail'])) {
        $xtpl->parse('main.link_detail');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_theme_dau_gia_search()
 *
 * @param mixed $array_data
 * @return
 */
function nv_theme_dau_gia_search($array_data)
{
    global $module_info, $op, $nv_Lang;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    // ------------------
    // Viết code vào đây
    // ------------------

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_dau_gia_bidorganization($array_data, $generate_page, $module_config_view, $arr_unexist_key, $filter_url, $error = "", $total = 0)
{
    global $module_info, $op, $module_name, $module_config, $nv_Lang, $global_config, $client_info;

    $xtpl = new XTemplate('bidorganization.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);
    $xtpl->assign('TOTAL_RESULT', number_format($total, 0, ",", "."));

    $nv_Lang->setModule('empty_result', sprintf($nv_Lang->getModule('empty_result'), $module_config['dau-gia']['view_guide']));
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    if (!empty($error)) {
        $xtpl->assign('ERROR', implode('<br />', (array)$error));
        $xtpl->parse('main.error');
    }

    // Hiển thị danh sách đơn đấu giá
    if (!empty($array_data)) {
        if (!empty($arr_unexist_key)) {
            $key_filter = implode(',', $arr_unexist_key);
            $xtpl->assign('LINK_ADD', sprintf($nv_Lang->getModule('add_key_filter'), $key_filter, $filter_url, $module_config['dau-gia']['view_guide']));
            $xtpl->parse('main.add_filter');
        }
        foreach ($array_data as $row) {
            $row['opening_reg_format'] = nv_date("d/m/Y", $row['opening_reg']);
            $row['closing_reg_format'] = nv_date("d/m/Y", $row['closing_reg']);
            $row['opening_bid_format'] = nv_date("H:i d/m/Y", $row['opening_bid']);
            $row['date_bid_format'] = nv_date("H:i d/m/Y", $row['date_bid']);
            $row['result'] = !empty($row['chosen_org_id']) ? $nv_Lang->getModule('has_result') : $nv_Lang->getModule('no_result');

            if ($module_config_view['view_detail']) {
                $row['url_organizationdetail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $row['alias'] . '-' . $row['id_bid'] . '.html';
                if ($global_config['iddomain'] != 2) {
                    $row['url_organizationdetail'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/' . $op . '/' . $row['alias'] . '-' . $row['id_bid'] . '.html';
                }
            } else {
                $row['url_organizationdetail'] = 'https://dgts.moj.gov.vn/thong-bao-lua-chon-to-chuc-dau-gia/' . change_alias($array_data[$row['id_bid']]['alias']) . '-' . $array_data[$row['id_bid']]['id_bid'] . '.html';
            }

            $xtpl->assign('ROW', $row);
            $xtpl->parse('main.view.loop');
        }
        if ($generate_page) {
            $xtpl->assign('GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.view.page');
        }
        $xtpl->parse('main.view');
    } else {
        $xtpl->parse('main.empty');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_dau_gia_bidorganizationdetail($row, $array_data_asset, $array_data_craws, $result_data = null)
{
    global $module_info, $op, $module_name, $client_info, $module_config, $nv_Lang, $module_captcha, $user_info, $global_config;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);
    $xtpl->assign('SELFURL', $client_info['selfurl']);

    if (!empty($result_data)) {
        $xtpl->assign('RESULT_DATA', $result_data);
        $xtpl->parse('main.has_result_data');
    } else {
        $xtpl->parse('main.no_result_data');
    }

    $reg_link = DAUTHAU_INFO_DOMAIN . '/vip/?form=1&amp;vip=6';
    $renew_link = DAUTHAU_INFO_DOMAIN . '/vip/?renewal=1&amp;vip=6';
    if ($row['vip'] == 'renew') {
        $redirect = sprintf($nv_Lang->getModule('vip6_renew_link'), $renew_link);
    } else if ($row['vip'] == 'false') {
        $redirect = sprintf($nv_Lang->getModule('vip6_form_link'), $reg_link);
    }

    if (!empty($row)) {
        if (!empty($array_data_asset)) {
            $i = 1;
            foreach ($array_data_asset as $asset) {
                $file_detail_html = '';
                $asset['stt'] = $i;
                $asset['min_bid_prices'] = str_replace(',', '.', number_format($asset['min_bid_prices']));
                $asset['min_bid_prices'] .= $nv_Lang->getModule('currency');
                if (!empty($asset['file_detail'])) {
                    foreach ($asset['file_detail'] as $file) {
                        $file_detail_html .= '<div><p><b><a rel="nofollow" href="' . str_replace(' ', '+', 'https://dgts.moj.gov.vn/common/download?name=' . $file['fileName'] . '&path=' . $file['linkFile']) . '">' . $file['fileName'] . '</a></b></p></div>';
                    }
                    $asset['link_detail'] = $file_detail_html;
                }

                if ($row['vip'] == 'false' || $row['vip'] == 'renew') {
                    $asset['qty'] = $redirect;
                    $asset['min_bid_prices'] = $redirect;
                    if (!empty($asset['link_detail'])) {
                        $asset['link_detail'] = $redirect;
                    }
                }

                $xtpl->assign('ASSET', $asset);
                $xtpl->parse('main.asset');
                $i++;
            }
        }
        if (!empty($row['view_type'])) {
            $_view_type = '';
            if ($row['view_type']['name']) {
                $_view_type = "";
                if (!empty($row['view_type']['link'])) {
                    $_view_type .= '<div><p><b><a rel="nofollow" href="' . $row['view_type']['link'] . '">' . $row['view_type']['name'] . '</a></b></p></div>';
                } else {
                    $_view_type .= '<div><p><b>' . $row['view_type']['name'] . '</b></p></div>';
                }
            }
            $row['view_type'] = $_view_type;
        }
        if (!empty($array_data_craws)) {
            foreach ($array_data_craws as $crawl) {
                $crawl['time_crawl_format'] = nv_date("H:i d/m/Y", $crawl['time_crawl']);
                $xtpl->assign('CRAWL', $crawl);
                $xtpl->parse('main.crawl');
            }
        }

        $html = '';
        if (!empty($row['other'])) {
            foreach ($row['other'] as $other) {
                $other['url_parent'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $other['alias'] . '-' . $other['id_bid'];
                $html .= '<a href="' . $other['url_parent'] . '" style="color: #0685d6"> [' . ($other['re_post']) . ']</a> ';
            }
            $row['link'] = $html;
        }
        if (!empty($row['file_detail'])) {
            $file_detail_html = '';
            foreach ($row['file_detail'] as $file) {
                $file_detail_html .= '<a rel="nofollow" href="' . str_replace(' ', '+', 'https://dgts.moj.gov.vn/common/download?name=' . $file['fileName'] . '&path=' . $file['linkFile']) . '">' . $file['fileName'] . '</a>';
            }
            $row['link_detail'] = $file_detail_html;
        }
        $row['doc_link'] = 'https://dgts.moj.gov.vn/portal/exportWord?aucInfoId=' . $row['id_bid'];

        $row['date_bid_format'] = nv_date("H:i d/m/Y", $row['date_bid']);
        $row['opening_bid_format'] = nv_date("H:i d/m/Y", $row['opening_bid']);
        $row['opening_reg_format'] = nv_date("H:i d/m/Y", $row['opening_reg']);
        $row['closing_reg_format'] = nv_date("H:i d/m/Y", $row['closing_reg']);
        $row['date'] = sprintf($nv_Lang->getModule('from_date_to_date'), $row['opening_reg_format'], $row['closing_reg_format']);

        if ($row['vip'] == 'false' || $row['vip'] == 'renew') {
            $row['address_owner'] = $redirect;
            $row['detail'] = $redirect;
            $row['contact'] = $redirect;
            $row['link_detail'] = '';
            $row['doc'] = '';
        }

        if (!defined('NV_IS_VIP6')) {
            $receive_email = [];
            $receive_email['content'] = sprintf($nv_Lang->getModule('receive_email_content'), $row['title'], ($row['vip'] === 'renew' ? $renew_link : $reg_link), ($row['vip'] === 'renew' ? $nv_Lang->getModule('renew') : $nv_Lang->getModule('register')), 'VIP6');
            $receive_email['reg_link'] = $row['vip'] === 'renew' ? $renew_link : $reg_link;
            $receive_email['vip_paket'] = $row['vip'] === 'renew' ? $nv_Lang->getModule('vip6_renew') : $nv_Lang->getModule('vip6_form');
            $xtpl->assign('RECEIVE_EMAIL', $receive_email);
            $xtpl->parse('main.receive_email_content');
        }

        $filter = [];
        $filter['content'] = sprintf($nv_Lang->getModule('filter_content'), $row['title']);
        if (!defined('NV_IS_USER')) {
            $filter['reg_link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=register&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
            $filter['btn_title'] = $nv_Lang->getModule('btn_register');
        } else {
            $filter['reg_link'] = DAUTHAU_INFO_DOMAIN . '/filters/';
            $filter['btn_title'] = $nv_Lang->getModule('btn_filter');
        }
        $xtpl->assign('FILTER', $filter);

        $xtpl->assign('ROW', $row);
    }

    if (!(defined('NV_IS_USER') or $client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
        global $page_url;
        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $link__popup = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
        // Gọi hàm Popuplogin và truyền tham số link vào
        $content_popup = PopupLogin($link__popup, nv_url_rewrite($page_url, true));
        $xtpl->assign('POPUP_LOGIN', $content_popup);
        $xtpl->parse('main.popup_login');
    }

    if (!empty($row['other'])) {
        $xtpl->parse('main.tb_lien_quan');
    }

    if (!empty($row['chosen_org_title'])) {
        $xtpl->parse('main.chosen_org_title');
    }

    if (defined('NV_IS_USER') and $module_captcha == 'recaptcha' and $global_config['recaptcha_ver'] == 2) {
        $xtpl->assign('CHECKSESS_UPDATE', md5($user_info['userid'] . $row['id_bid'] . NV_CACHE_PREFIX . $client_info['session_id']));
        $xtpl->assign('RECAPTCHA_ELEMENT', 'recaptcha' . nv_genpass(8));
        $xtpl->assign('N_CAPTCHA', $nv_Lang->getGlobal('securitycode1'));
        $xtpl->parse('main.recaptcha');
        $xtpl->parse('main.update');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_dau_gia_resultorganization($array_data, $generate_page, $module_config_view, $arr_unexist_key, $filter_url, $error = [], $total = 0)
{
    global $module_info, $op, $module_name, $module_config, $nv_Lang, $global_config;

    $xtpl = new XTemplate('resultorganization.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);
    $xtpl->assign('TOTAL_RESULT', number_format($total, 0, ",", "."));

    $nv_Lang->setModule('empty_result', sprintf($nv_Lang->getModule('empty_result'), $module_config['dau-gia']['view_guide']));
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    if (!empty($error)) {
        $xtpl->assign('ERROR', implode('<br>', $error));
        $xtpl->parse('main.error');
    }

    // Hiển thị danh sách đơn đấu giá
    if (!empty($array_data)) {
        if (!empty($arr_unexist_key)) {
            $key_filter = implode(',', $arr_unexist_key);
            $xtpl->assign('LINK_ADD', sprintf($nv_Lang->getModule('add_key_filter'), $key_filter, $filter_url, $module_config['dau-gia']['view_guide']));
            $xtpl->parse('main.add_filter');
        }
        foreach ($array_data as $row) {
            if ($module_config_view['view_detail']) {
                $row['url_resultorganizationdetail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $row['alias'] . '-' . $row['id'] . '.html';
                if ($global_config['iddomain'] != 2) {
                    $row['url_resultorganizationdetail'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/' . $op . '/' . $row['alias'] . '-' . $row['id_bid'] . '.html';
                }
            } else {
                $row['url_resultorganizationdetail'] = 'https://dgts.moj.gov.vn/thong-bao-ket-qua-lua-chon-to-chuc-dau-gia/' . change_alias($array_data[$row['id_bid']]['alias']) . '-' . $array_data[$row['id_bid']]['id_bid'] . '.html';
            }
            $row['date_published_format'] = nv_date("H:i d/m/Y", $row['date_published']);
            $xtpl->assign('ROW', $row);
            $xtpl->parse('main.view.loop');
        }
        if ($generate_page) {
            $xtpl->assign('GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.view.page');
        }
        $xtpl->parse('main.view');
    } else {
        $xtpl->parse('main.empty');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_dau_gia_resultorganizationdetail($row, $array_data_asset, $array_data_craws, $select_data)
{
    global $module_info, $op, $module_name, $client_info, $module_config, $nv_Lang;

    $xtpl = new XTemplate('resultorganizationdetail.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);
    $xtpl->assign('SELFURL', $client_info['selfurl']);
    $xtpl->assign('SELECT_DATA', $select_data);

    $reg_link = DAUTHAU_INFO_DOMAIN . '/vip/?form=1&amp;vip=6';
    $renew_link = DAUTHAU_INFO_DOMAIN . '/vip/?renewal=1&amp;vip=6';
    if ($row['vip'] == 'renew') {
        $redirect = sprintf($nv_Lang->getModule('vip6_renew_link'), $renew_link);
    } else if ($row['vip'] == 'false') {
        $redirect = sprintf($nv_Lang->getModule('vip6_form_link'), $reg_link);
    }

    if (!empty($row)) {
        if (!empty($array_data_asset)) {
            $i = 1;
            foreach ($array_data_asset as $asset) {
                $file_detail_html = '';
                $asset['stt'] = $i;
                $asset['min_bid_prices'] = str_replace(',', '.', number_format($asset['min_bid_prices']));
                $asset['min_bid_prices'] .= $nv_Lang->getModule('currency');
                if (!empty($asset['file_detail'])) {
                    foreach ($asset['file_detail'] as $file) {
                        $file_detail_html .= '<div><p><b><a rel="nofollow" href="' . str_replace(' ', '+', 'https://dgts.moj.gov.vn/common/download?name=' . $file['fileName'] . '&path=' . $file['linkFile']) . '">' . $file['fileName'] . '</a></b></p></div>';
                    }
                    $asset['link_detail'] = $file_detail_html;
                }

                if ($row['vip'] == 'false' || $row['vip'] == 'renew') {
                    $asset['qty'] = $redirect;
                    $asset['min_bid_prices'] = $redirect;
                    if (!empty($asset['link_detail'])) {
                        $asset['link_detail'] = $redirect;
                    }
                }

                $xtpl->assign('ASSET', $asset);
                $xtpl->parse('main.asset');
                $i++;
            }
        }
        $row['view_type'] = $nv_Lang->getModule('ctt');
        if (!empty($array_data_craws)) {
            foreach ($array_data_craws as $crawl) {
                $crawl['time_crawl_format'] = nv_date("H:i d/m/Y", $crawl['time_crawl']);
                $xtpl->assign('CRAWL', $crawl);
                $xtpl->parse('main.crawl');
            }
        }
        $row['date_published'] = nv_date_format(1, $row['date_published']);

        if ($row['vip'] == 'false' || $row['vip'] == 'renew') {
            $row['address_owner'] = $redirect;
            $row['detail'] = $redirect;
            $row['contact_bidder'] = $redirect;
            $row['files'] = $redirect;
            $row['doc'] = '';
        }

        if (!defined('NV_IS_VIP6')) {
            $receive_email = [];
            $receive_email['content'] = sprintf($nv_Lang->getModule('receive_email_content'), $row['title'], ($row['vip'] === 'renew' ? $renew_link : $reg_link), ($row['vip'] === 'renew' ? $nv_Lang->getModule('renew') : $nv_Lang->getModule('register')), 'VIP6');
            $receive_email['reg_link'] = $row['vip'] === 'renew' ? $renew_link : $reg_link;
            $receive_email['vip_paket'] = $row['vip'] === 'renew' ? $nv_Lang->getModule('vip6_renew') : $nv_Lang->getModule('vip6_form');
            $xtpl->assign('RECEIVE_EMAIL', $receive_email);
            $xtpl->parse('main.receive_email_content');
        }

        $filter = [];
        $filter['content'] = sprintf($nv_Lang->getModule('filter_content'), $row['title']);
        if (!defined('NV_IS_USER')) {
            $filter['reg_link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=register&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
            $filter['btn_title'] = $nv_Lang->getModule('btn_register');
        } else {
            $filter['reg_link'] = DAUTHAU_INFO_DOMAIN . '/filters/';
            $filter['btn_title'] = $nv_Lang->getModule('btn_filter');
        }
        $xtpl->assign('FILTER', $filter);

        $xtpl->assign('ROW', $row);
    }
    if (!(defined('NV_IS_USER') or $client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
        global $page_url;
        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $link__popup = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
        // Gọi hàm Popuplogin và truyền tham số link vào
        $content_popup = PopupLogin($link__popup, nv_url_rewrite($page_url, true));
        $xtpl->assign('POPUP_LOGIN', $content_popup);
        $xtpl->parse('main.popup_login');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_dau_gia_organization($array_data, $generate_page, $module_config_view)
{
    global $module_info, $op, $module_name, $nv_Cache, $nv_Lang, $global_config;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);

    // province
    $sql_province = "SELECT id, title FROM " . NV_PREFIXLANG . "_location_province";
    $province_list = $nv_Cache->db($sql_province, 'id', 'location');

    // district
    $sql_district = "SELECT id, title FROM " . NV_PREFIXLANG . "_location_district";
    $district_list = $nv_Cache->db($sql_district, 'id', 'location');

    // ward
    $sql_ward = "SELECT id, title FROM " . NV_PREFIXLANG . "_location_ward";
    $ward_list = $nv_Cache->db($sql_ward, 'id', 'location');

    if (!empty($array_data)) {
        foreach ($array_data as $row) {
            $row['url_organizationdetail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($row['name_bidder']) . '-' . $row['id_bidder'] . '.html';
            if ($global_config['iddomain'] != 2) {
                $row['url_organizationdetail'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/' . $op . '/' . change_alias($row['name_bidder']) . '-' . $row['id_bidder'] . '.html';
            }
            $space = ', ';
            if ($row['id_ward_bidder'] > 0) {
                $row['address_bidder'] .= $space;
                $row['address_bidder'] .= $ward_list[$row['id_ward_bidder']]['title'];
            }
            if ($row['id_district_bidder'] > 0) {
                $row['address_bidder'] .= $space;
                $row['address_bidder'] .= $district_list[$row['id_district_bidder']]['title'];
            }
            if ($row['id_province_bidder'] > 0) {
                $row['address_bidder'] .= $space;
                $row['address_bidder'] .= $province_list[$row['id_province_bidder']]['title'];
            }
            $row['address_bidder'] = trim($row['address_bidder'], " ,");
            $xtpl->assign('ROW', $row);
            $xtpl->parse('main.data.loop');
        }

        if ($generate_page) {
            $xtpl->assign('GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.data.page');
        }
        $xtpl->parse('main.data');
    } else {
        $xtpl->parse('main.empty');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_dau_gia_auctioneer($array_data, $generate_page)
{
    global $module_info, $op, $module_name, $nv_Cache, $nv_Lang, $global_config;

    $xtpl = new XTemplate('auctioneer.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);

    if (!empty($array_data)) {
        foreach ($array_data as $row) {
            $row['url_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['auctioneer'] . '/' . $row['alias'] . '-' . $row['id'] . '.html';
            if ($global_config['iddomain'] != 2) {
                $row['url_detail'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/' . $module_info['alias']['auctioneer'] . '/' . $row['alias'] . '-' . $row['id'] . '.html';
            }
            $xtpl->assign('ROW', $row);
            $xtpl->parse('main.data.loop');
        }

        if ($generate_page) {
            $xtpl->assign('GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.data.page');
        }
        $xtpl->parse('main.data');
    } else {
        $xtpl->parse('main.empty');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_dau_gia_auctioneerdetail($array_data, $arr_bidder, $arrCheckDGV)
{
    global $module_info, $op, $module_name, $nv_Cache, $nv_Lang, $page_url, $client_info, $module_config;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);

    $array_data['start_date'] = $array_data['start_date'] > 0 ? date('d/m/Y', $array_data['start_date']) : '';
    $xtpl->assign('ROW', $array_data);

    if ($array_data['time_cchn'] != '') {
        $xtpl->parse('main.show_time_cchn');
    }

    if ($array_data['num_dgv'] != '') {
        $xtpl->parse('main.show_num_dgv');
    }

    if ((!defined('NV_IS_USER') and !$client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $link__popup = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
        // Gọi hàm Popuplogin và truyền tham số link vào
        $content_popup = PopupLogin($link__popup, nv_url_rewrite($page_url, true));
        $xtpl->assign('POPUP_LOGIN', $content_popup);
        $xtpl->parse('main.popup_login');
    }

    // Kiểm tra xem có dữ liệu trùng đấu giá viên hay không?
    if (!empty($arrCheckDGV)) {
        foreach ($arrCheckDGV as $k => $v) {
            $v['stt'] = $k + 1;
            $v['url_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['auctioneer'] . '/' . $v['alias'] . '-' . $v['id'] . '.html';
            $xtpl->assign('ROW_DUP', $v);
            $xtpl->parse('main.duplicate_dgv.loop');
        }

        $xtpl->assign('ITEM', $nv_Lang->getModule('title_duplicate_dgv', sizeof($arrCheckDGV)));
        $xtpl->parse('main.duplicate_dgv');
    }

    // province
    $sql_province = "SELECT id, title FROM " . NV_PREFIXLANG . "_location_province";
    $province_list = $nv_Cache->db($sql_province, 'id', 'location');

    // district
    $sql_district = "SELECT id, title FROM " . NV_PREFIXLANG . "_location_district";
    $district_list = $nv_Cache->db($sql_district, 'id', 'location');

    // ward
    $sql_ward = "SELECT id, title FROM " . NV_PREFIXLANG . "_location_ward";
    $ward_list = $nv_Cache->db($sql_ward, 'id', 'location');

    if (!empty($arr_bidder)) {
        $i = 1;
        foreach ($arr_bidder as $bidder) {
            $bidder['stt'] = $i;
            $bidder['url_organizationdetail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization/' . change_alias($bidder['name_bidder']) . '-' . $bidder['id_bidder'] . '.html';
            $bidder['address_bidder'] = ucwords($bidder['address_bidder']);
            $space = ', ';
            if ($bidder['id_ward_bidder'] > 0) {
                $bidder['address_bidder'] .= $space;
                $bidder['address_bidder'] .= $ward_list[$bidder['id_ward_bidder']]['title'];
            }
            if ($bidder['id_district_bidder'] > 0) {
                $bidder['address_bidder'] .= $space;
                $bidder['address_bidder'] .= $district_list[$bidder['id_district_bidder']]['title'];
            }
            if ($bidder['id_province_bidder'] > 0) {
                $bidder['address_bidder'] .= $space;
                $bidder['address_bidder'] .= $province_list[$bidder['id_province_bidder']]['title'];
            }

            $xtpl->assign('BIDDER', $bidder);
            $xtpl->parse('main.bidder.loop');
            $i++;
        }

        $xtpl->parse('main.bidder');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_dau_gia_organizationdetail($array_data, $arr_bid, $arr_chi_nhanh, $arr_van_phong, $arr_dgv, $arr_select_result, $count_total)
{
    global $module_info, $op, $module_name, $nv_Lang, $page_url, $client_info, $module_config;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);
    $xtpl->assign('LINK_JS', NV_STATIC_URL . 'themes/' . $module_info['template']);

    if ((!defined('NV_IS_USER') and !$client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $link__popup = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
        // Gọi hàm Popuplogin và truyền tham số link vào
        $content_popup = PopupLogin($link__popup, nv_url_rewrite($page_url, true));
        $xtpl->assign('POPUP_LOGIN', $content_popup);
        $xtpl->parse('main.popup_login');
    }

    $array_data['update_at'] = nv_datetime_format($array_data['update_at'], 1);

    $xtpl->assign('ROW', $array_data);
    if (!empty($array_data['dep'])) {
        $xtpl->parse('main.department');
    }

    if (!empty($array_data['so_quyet_dinh'])) {
        $xtpl->parse('main.so_quyet_dinh');
    }

    if (!empty($array_data['so_giay_dkhd'])) {
        $xtpl->parse('main.so_giay_dkhd');
    }

    if (!empty($array_data['logs']) && count($array_data['logs']) >= 2) {
        $_first_log = true;
        foreach ($array_data['logs'] as $row_log) {
            $row_log['checked'] = $_first_log ? 'checked="checked"' : '';
            $_first_log = false;
            $row_log['text_fields'] = $row_log['type'] == 'first_publish' ? '<strong>' . $nv_Lang->getModule('first_publish') . '</strong>' : '<strong>' . $nv_Lang->getModule('change_org') . ':</strong>';
            if ($row_log['type'] != 'first_publish' && !empty($row_log['fields'])) {
                $_arr_text_fields = [];
                foreach ($row_log['fields'] as $_field) {
                    $_arr_text_fields[] = in_array($_field, ['id_province_bidder', 'id_district_bidder', 'id_ward_bidder', 'address_bidder']) ? $nv_Lang->getModule('field_address') : $nv_Lang->getModule('field_' . $_field);
                }
                $_arr_text_fields = array_unique($_arr_text_fields);
                $row_log['text_fields'] .= ' ' . implode(', ', $_arr_text_fields);
            }
            $row_log['created_at'] = nv_datetime_format($row_log['created_at'], 1);
            $xtpl->assign('ROW_LOG', $row_log);
            if ($row_log['prev'] > 0) {
                $xtpl->parse('main.logs.loop.action_inline');
            }
            $xtpl->parse('main.logs.loop');
        }
        $xtpl->parse('main.logs');
    }

    if (!empty($arr_chi_nhanh)) {
        $i = 1;
        foreach ($arr_chi_nhanh as $_chi_nhanh) {
            $_chi_nhanh['stt'] = $i;
            $xtpl->assign('CHI_NHANH', $_chi_nhanh);
            $xtpl->parse('main.chi_nhanh.loop');
            $i++;
        }
        $xtpl->parse('main.chi_nhanh');
    }
    if (!empty($arr_van_phong)) {
        $i = 1;
        foreach ($arr_van_phong as $_van_phong) {
            $_van_phong['stt'] = $i;
            $xtpl->assign('VAN_PHONG', $_van_phong);
            $xtpl->parse('main.van_phong.loop');
            $i++;
        }
        $xtpl->parse('main.van_phong');
    }
    if (!empty($arr_dgv)) {
        $i = 1;
        foreach ($arr_dgv as $_dgv) {
            $_dgv['stt'] = $i;
            $_dgv['url_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['auctioneer'] . '/' . $_dgv['alias'] . '-' . $_dgv['id'] . '.html';
            $xtpl->assign('DAU_GIA_VIEN', $_dgv);
            $xtpl->parse('main.dau_gia_vien.loop');
            $i++;
        }
        $xtpl->parse('main.dau_gia_vien');
    }

    if (!empty($arr_bid)) {
        foreach ($arr_bid as $_bid) {
            $xtpl->assign('BID', $_bid);
            $xtpl->parse('main.view.loop');
        }
        if ($count_total['bid'] > 2) {
            if (defined('NV_IS_VIP6')) {
                $xtpl->assign('SUMMARY_BIDDER_BID', sprintf($nv_Lang->getModule('summary_bidder_bid'), $array_data['name_bidder'], $count_total['bid'], DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/?keyword_id_bidder=' . $array_data['id_bidder'] . '&is_advance=1&type_search=3&type_info3=1'));
            } else {
                $xtpl->assign('SUMMARY_BIDDER_BID', sprintf($nv_Lang->getModule('summary_bidder_bid_novip'), $array_data['name_bidder'], $count_total['bid'], DAUTHAU_INFO_DOMAIN . '/vip/?form=1&amp;vip=6'));
            }
            $xtpl->parse('main.view.show_more');
        }
        $xtpl->parse('main.view');
    }
    if (!empty($arr_select_result)) {
        foreach ($arr_select_result as $_select_result) {
            $xtpl->assign('SELECT_RESULT', $_select_result);
            $xtpl->parse('main.select_result.loop');
        }
        if ($count_total['selected'] > 2) {
            if (defined('NV_IS_VIP6')) {
                $xtpl->assign('SUMMARY_BIDDER_SELECT', sprintf($nv_Lang->getModule('summary_bidder_select'), $array_data['name_bidder'], $count_total['selected'], DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/resultorganization/?keyword_id_bidder=' . $array_data['id_bidder'] . '&is_advance=1&type_search=3&type_info3=3&type_info=7&type_org=1&searching=1'));
            } else {
                $xtpl->assign('SUMMARY_BIDDER_SELECT', sprintf($nv_Lang->getModule('summary_bidder_select_novip'), $array_data['name_bidder'], $count_total['selected'], DAUTHAU_INFO_DOMAIN . '/vip/?form=1&amp;vip=6'));
            }
            $xtpl->parse('main.select_result.show_more');
        }
        $xtpl->parse('main.select_result');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_dau_gia_department($array_data, $generate_page, $module_config_view, $total = 0)
{
    global $module_info, $op, $module_name, $nv_Lang, $global_config;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);

    $xtpl->assign('NUM_ITEMS', nv_number_format($total));

    // Hiển thị danh sách đơn đấu giá
    if (!empty($array_data)) {
        foreach ($array_data as $row) {
            $row['url'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($row['fullname']) . '-' . $row['depofjusticeid'] . '.html';
            if ($global_config['iddomain'] != 2) {
                $row['url'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/' . $op . '/' . change_alias($row['fullname']) . '-' . $row['depofjusticeid'] . '.html';
            }
            $xtpl->assign('ROW', $row);
            $xtpl->parse('main.loop');
        }
    }
    if ($generate_page) {
        $xtpl->assign('GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.page');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_dau_gia_departmentdetail($array_data, $arr_bid)
{
    global $module_info, $op, $module_name, $nv_Lang, $page_url, $client_info, $module_config;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('OP', $op);
    $xtpl->assign('TITLE_ORGANIZATION', !empty($array_data['name_province']) ? sprintf($nv_Lang->getModule('organization_at'), $array_data['name_province']) : $nv_Lang->getModule('organization'));

    if ((!defined('NV_IS_USER') and !$client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $link__popup = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
        // Gọi hàm Popuplogin và truyền tham số link vào
        $content_popup = PopupLogin($link__popup, nv_url_rewrite($page_url, true));
        $xtpl->assign('POPUP_LOGIN', $content_popup);
        $xtpl->parse('main.popup_login');
    }

    $xtpl->assign('ROW', $array_data);
    if (!empty($arr_bid)) {
        $i = 1;
        foreach ($arr_bid as $row) {
            $row['stt'] = $i;
            $xtpl->assign('BIDDER', $row);
            $xtpl->parse('main.view.loop');
            $i++;
        }
        $xtpl->parse('main.view');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}
