<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 02:25:16 GMT
 */

 if (!defined('NV_IS_MOD_VPCNCL')) {
    die('Stop!!!');
}

$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;

if (empty($org_id)) {
    nv_redirect_location($base_url);
}

$db->sqlreset()
    ->select('*')
    ->from(NV_PREFIXLANG . '_vpcncl_to_chuc')
    ->where('organization_id = ' . $db->quote($org_id));
$sth = $db->prepare($db->sql());
$sth->execute();

$org = [];

while ($row = $sth->fetch()) {
    $row['organization_number'] = strtoupper($module_name) . ' - ' . $row['organization_number'];
    $row['status'] = ORG_STATUSES[$row['status']];
    $row['cancellation_date'] =  !empty($row['cancellation_date']) ? nv_date('m-Y', $row['cancellation_date']) : '';
    $row['effective_date'] =  !empty($row['effective_date']) ? nv_date('d-m-Y', $row['effective_date']) : '';
    $row['province'] = process_province($row['province_id'], $location_id_map);
    $row['accreditation_location'] = str_replace('|<>|', '<br>- ', $row['accreditation_location']);
    $row['last_update'] = $row['crawls_last'] == 0 ? nv_date('H:i d-m-Y', $row['crawls_first']) : nv_date('H:i d-m-Y', $row['crawls_last']);
    $row['crawls_number'] = sprintf($nv_Lang->getModule('crawls_numbers'), $row['crawls_number']);
    if ($type_id != 2) {
        $row['linhvuc'] = process_linhvuc($row['specialize_id'], $linhvuc_id_map);
    }
    $row['map_host_unit'] = process_donvichuquan($row['host_unit'], $row['solicitor_id'], $row['contractor_id']);
    // xử lý file download
    $row['file_download'] = process_file_download($row['file_download']);
    $org = $row;
}

if (empty($org)) {
    nv_redirect_location($base_url);
}

// Xử lý cập nhật dữ liệu
$check_ss = $nv_Request->get_title('check', 'post, get', '');
$update = $nv_Request->isset_request('update', 'post');
$_userid = defined('NV_IS_USER') ? intval($user_info['userid']) : 0;
$session_update = md5($_userid . $org['organization_id'] . NV_CACHE_PREFIX . $client_info['session_id']);
if ($update && $check_ss == $session_update && $org['organization_id'] > 0 && $_userid > 0) {
    $info = $nv_Lang->getModule('update_err');
    $last = $db->query("SELECT * FROM " . BID_PREFIX_GLOBAL . "_update_user WHERE userid = " . $_userid . " AND vpcncl_id= " . $org['organization_id'])->fetch();
    if (!empty($last) && NV_CURRENTTIME - $last['last_reload'] <= 600) {
        $info = sprintf($nv_Lang->getModule('update_err_user_last'), nv_convertfromSec(600 - (NV_CURRENTTIME - $last['last_reload'])));
    } else {
        if (NV_CURRENTTIME - $org['crawls_last'] >= 1800) {
            $dbcr = connect_dbcr();
            try {
                $_result = $dbcr->query("SELECT * FROM " . $db_config['prefix'] . "_vpcncl_url WHERE organization_id = " . $dbcr->quote($org['organization_id']))->fetch();
                if (!empty($_result)) {
                    if ($_result['status_crawls'] == 1 and (NV_CURRENTTIME - $_result['crawls_time'] >= 600)) {
                        $exec = $dbcr->exec("UPDATE " . $db_config['prefix'] . "_vpcncl_url SET status_crawls = 0 WHERE organization_id = " . $_result['organization_id']);
                        $info = $exec ? $nv_Lang->getModule('update_ok') : $nv_Lang->getModule('update_err_new');
                    } else {
                        $info = $nv_Lang->getModule('update_err_new');
                    }
                } else {
                    $url_node = NV_LANG_INTERFACE == 'vi' ? 'http://www.boa.gov.vn/vi/node/' . $org['organization_id'] : 'http://www.boa.gov.vn/en/node/' . $org['organization_id'];
                    $query = "INSERT INTO " . $db_config['prefix'] . "_vpcncl_url (organization_id, type, url_crawls, status, time_get_url) VALUES
                        (" . $dbcr->quote($org['organization_id']) . ", " . $org['type'] . ", " . $dbcr->quote($url_node) . ", " . $org['status'] . ", " . NV_CURRENTTIME . ")";
                    $_id = $dbcr->query($query);
                    if ($_id) {
                        $info = $nv_Lang->getModule('update_ok');
                    }
                }
                if (!empty($last)) {
                    $exec = $db->exec("UPDATE " . BID_PREFIX_GLOBAL . "_update_user SET last_reload='" . NV_CURRENTTIME . "' WHERE userid=" . $user_info['userid'] . " AND vpcncl_id= " . $org['organization_id']);
                } else {
                    $exec = $db->exec("INSERT INTO " . BID_PREFIX_GLOBAL . "_update_user (userid, vpcncl_id, last_reload) VALUES (" . $user_info['userid'] . ", " . $org['organization_id'] . ", " . NV_CURRENTTIME . ")");
                }
            } catch (PDOException $e) {
                trigger_error($e);
                die();
            }
        } else {
            $info = $nv_Lang->getModule('update_err_new');
        }
    }
    nv_jsonOutput($info);
}

$base_url = $base_url . '&amp;' . NV_OP_VARIABLE . '=' . $array_op[0] . $global_config['rewrite_exturl'];
$page_url = $base_url;
$canonicalUrl = getCanonicalUrl($page_url);

$page_title = $org['organization_name'];
$description = $nv_Lang->getModule($module_name . '_detail_desc');

$xtpl = new XTemplate('detail.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);
$xtpl->assign('TEMPLATE', $module_info['template']);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_NAME_UPPER', strtoupper($module_name));
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('CHECKSESS_UPDATE', $session_update);
$xtpl->assign('ORG', $org);

if (!empty($org['cancellation_date'])) {
    $xtpl->parse('main.cancellation_date');
}

if (in_array($type_id, $show_specializes)) {
    $xtpl->parse('main.show_specialize');
}

if (!in_array($type_id, $hiden_host_unit)) {
    $xtpl->parse('main.show_host_unit');
}

// Cập nhật lại thông tin
if (defined('NV_IS_USER')) {
    if ($module_captcha == 'recaptcha' and  $global_config['recaptcha_ver'] == 2) {
        $xtpl->assign('RECAPTCHA_ELEMENT', 'recaptcha' . nv_genpass(8));
        $xtpl->assign('N_CAPTCHA', $nv_Lang->getGlobal('securitycode1'));
        $xtpl->parse('main.update.recaptcha');
    }
    $xtpl->parse('main.update');
} else {
    $link_register = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=register&nv_redirect=' . nv_redirect_encrypt(urlRewriteWithDomain($page_url, NV_MY_DOMAIN));
    $pop_up = sprintf($nv_Lang->getModule('login_popup'), $link_register);
    $xtpl->assign('LOGIN', $pop_up);
    $xtpl->parse('main.login');
}

if (!empty($org['effective_date'])) {
    $xtpl->parse('main.effective_date');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$array_mod_title[] = [
    'title' => $page_title
];

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
