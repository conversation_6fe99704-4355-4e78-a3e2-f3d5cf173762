<?php
// danh sách bên mời thầu mà nhà đầu tư có quan hệ

if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

function download_logs() {

    global $db, $db_config, $nv_Cache, $site_mods;

    $download_logs = [];
    //tính số lượng download trung bình (#2320)
    $cache_download_logs = $nv_Cache->getItem($site_mods['bidding']['module_data'], NV_LANG_DATA . '_download_logs.cache');

    if ($cache_download_logs !== 'false' && $cache_download_logs != '') {
        //Nếu đã lưu cache thì đọc luôn
        $download_logs = json_decode($cache_download_logs, 'true');
    } else {
        //số lượng chưa download
        $download_logs = $db->query("SELECT num_total, downloaded_count FROM " . $db_config['prefix'] . "_download_logs WHERE time_static >= " . (NV_CURRENTTIME - 3600) . " LIMIT 1")->fetch();
        if (!empty($download_logs) && $download_logs['downloaded_count'] > 0) {
            //Tốc độ trung bình mỗi phút
            $download_logs['speed'] = ceil($download_logs['downloaded_count']/60);

            //Thời gian chờ
            $download_logs['avg'] = ceil($download_logs['num_total']/$download_logs['speed']);

            if ($download_logs['avg'] > 10) {
                $download_logs['num_total'] = number_format($download_logs['num_total'], 0, ',', '.');

                $download_logs['speed'] = number_format($download_logs['speed'], 0, ',', '.');

            } else {
                //nếu chờ ít hơn 10p thì dùng dòng thông báo cũ
                unset($download_logs);
            }
        }
        $nv_Cache->setItem($site_mods['bidding']['module_data'], NV_LANG_DATA . '_download_logs.cache', json_encode($download_logs), 3600);
    }

    return $download_logs;
}


