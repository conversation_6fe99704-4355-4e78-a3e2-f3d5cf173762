<!-- BEGIN: main -->
<!-- BEGIN: province -->
<div class="container {class}">
    <div class="title_view">
        <h2>{LANG.list_tinh_thanh}</h2>
    </div>
    <div class="row">
        <div class="col-xs-24 col-sm-12 col-md-6 col-lg-4">
            <!-- BEGIN: part1 -->
            <a href="{PROVINCE.link}">{PROVINCE.title} ({PROVINCE.count})</a> <br>
            <!-- END: part1 -->
        </div>
        <div class="col-xs-24 col-sm-12 col-md-6 col-lg-4">
            <!-- BEGIN: part2 -->
            <a href="{PROVINCE.link}">{PROVINCE.title} ({PROVINCE.count})</a> <br>
            <!-- END: part2 -->
        </div>
        <div class="col-xs-24 col-sm-12 col-md-6 col-lg-4">
            <!-- BEGIN: part3 -->
            <a href="{PROVINCE.link}">{PROVINCE.title} ({PROVINCE.count})</a> <br>
            <!-- END: part3 -->
        </div>
        <div class="col-xs-24 col-sm-12 col-md-6 col-lg-4">
            <!-- BEGIN: part4 -->
            <a href="{PROVINCE.link}">{PROVINCE.title} ({PROVINCE.count})</a> <br>
            <!-- END: part4 -->
        </div>
        <div class="gradient col-sm-18 col-md-18"></div>
    </div>
    <div class="col-sm-18 col-md-18">
        <div class="view_province"><span class="hien">{LANG.view_province}</span><span class="an">{LANG.view_province_tg}</span></div>
    </div>
</div>
<script type="text/javascript">
    $( ".province .view_province" ).click(function() {
        $( ".province .row" ).toggleClass( "active" );
        $(this).toggleClass( "active" );
    });
</script>
<!-- END: province -->
<!-- BEGIN: linhvuc -->
<div class="container">
    <h2>{LANG.list_phan_muc}</h2>
    <div class="row list-phan-muc">
        <div class="col-sm-5">
            <!-- BEGIN: linhvuc1 -->
            <a href="{LINHVUC.link}">{LINHVUC.title} ({LINHVUC.count})</a> <br>
            <!-- END: linhvuc1 -->
        </div>
        <div class="col-sm-6">
            <!-- BEGIN: linhvuc2 -->
            <a href="{LINHVUC.link}">{LINHVUC.title} ({LINHVUC.count})</a> <br>
            <!-- END: linhvuc2 -->
        </div>
        <div class="col-sm-5">
            <!-- BEGIN: linhvuc0 -->
            <a href="{LINHVUC.link}">{LINHVUC.title} ({LINHVUC.count})</a> <br>
            <!-- END: linhvuc0 -->
        </div>
    </div>
</div>
<script type="text/javascript">
    function checkScreenWidth() {
        if ($(window).width() < 1366) {
            $('.list-phan-muc > div').removeClass('col-sm-5 col-sm-6').addClass('col-sm-8');
        } else {
            $('.list-phan-muc > div').removeClass('col-sm-5 col-sm-6 col-sm-8').addClass(function (index) {
                return index % 2 === 0 ? 'col-sm-5' : 'col-sm-6';
            });
        }
    }
    checkScreenWidth();
    $(window).resize(function () {
        checkScreenWidth();
    });
</script>
<!-- END: linhvuc -->
<!-- BEGIN: industry -->
<div>
    <h2>{LANG.list_phan_muc}</h2>
    <div class="row industry_tree__mobile">
        <!-- BEGIN: industry_col -->
        <div class="col-sm-8">
            <ul class="tree1">
                <!-- BEGIN: show_parent -->
                <li>
                    <a href="{PAR.link}" class="fs16 industry-parent">{PAR.title} ({PAR.count})</a>
                    <!-- BEGIN: subindustry -->
                    <ul>
                        <!-- BEGIN: loop -->
                        <li>
                            <a href="{CHILD.link}" data-alias="{CHILD.alias}" class="industry-child">{CHILD.title} ({CHILD.count})</a>
                        </li>
                        <!-- END: loop -->
                    </ul>
                    <!-- END: subindustry -->
                </li>
                <!-- END: show_parent -->
            </ul>
        </div>
        <!-- END: industry_col -->
    </div>
</div>
<script type="text/javascript">
    cur_alias = location.pathname.split('/').filter(element => element != '').at(-1);
    if (cur_alias != undefined) {
        document.querySelectorAll(".industry-child").forEach((element, index) => {
            if (element.getAttribute('data-alias') === cur_alias) {
                element.style.color = "#a94442";
                element.parentElement.parentElement.parentElement.querySelector('a.industry-parent').click();
            }
        });
    }
</script>
<!-- END: industry -->
<!-- BEGIN: vsic -->
<div class="m-bottom">
    <h2>{LANG.list_vsic}</h2>
    <div class="row">
        <!-- BEGIN: col -->
        <div class="col-sm-12">
            {LIST_VSIC}
        </div>
        <!-- END: col -->
    </div>
</div>
<script type="text/javascript">
    cur_code = location.pathname.split('/').filter(element => element != '').at(-1);
    cur_code = cur_code.split('-').filter(element => element != '').at(-1);
    if (cur_code != undefined && cur_code.match(/^\w\d*$/)) {
        document.querySelectorAll(".vsic-child").forEach((element, index) => {
            if (element.getAttribute('data-code') === cur_code) {
                element.style.color = "#a94442";
                e = element;
                i = parseInt(e.getAttribute('data-level'))
                while (i >= 2) {
                    e.parentElement.parentElement.parentElement.querySelector('i').click();
                    e = e.parentElement.parentElement.parentElement.querySelector('a');
                    i--;
                }
            }
        });
    }
</script>
<!-- END: vsic -->
<br>

<!-- BEGIN: chart_tbmt -->
<div class="border-bidding">
    <h2 class="title__tab_heading"><span>{LANG.statistic_title}</span></h2>
</div>
<div class="alert alert-warning">Mục này đang phát triển - Chỉ hiển thị với tài khoản quản trị</div>
<!-- BEGIN: unlock -->
    <script type="text/javascript" src="{ASSETS_STATIC_URL}/js/chart/chart.js"></script>
    <div class="{EXPAND_ACTIVE} expand-wrap mb-4">
        <div id="chart_tbmt_wrap" class="expand-content">
            <div class="center-search-bl">
                <div class="ltablesearch-cont">
                    <form class="tender-notice-chart ltablesearch" id="tender-notice-chart" action="{ACTION}" method="get" data-maxspan="{MAXSPAN}" data-mindate="{MINDATE}" data-opens="left">
                        <div class="row margin-bottom-lg">
                            <div class="col-sm-24 col-md-24 ">
                                <div class="row">
                                    <div class="col-xs-24 col-sm-24">
                                        <div class="form-group col-md-12 pdl0">
                                            <label class="control-label">{LANG.type_chart}</label>
                                            <select class="form-control" name="typeChart">
                                                <!-- BEGIN: type_chart -->
                                                <option value="{TYPE.key}"{TYPE.selected}>{TYPE.value}</option>
                                                <!-- END: type_chart -->
                                            </select>
                                        </div>

                                        <div class="form-group col-md-12 pdl0">
                                            <label class="control-label">{LANG.range_chart}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <input type="hidden" name="sfrom" value="{FROM}" data-default="{FROM_DEFAULT}" />
                                                    <input type="hidden" name="sto" value="{TO}" data-default="{TO_DEFAULT}" />
                                                    <input class="form-control search_range_tbmt" type="text" value="{FROM} - {TO}" data-default="{FROM_DEFAULT} - {TO_DEFAULT}" data-cancel-lang="{GLANG.cancel}" data-apply-lang="{GLANG.apply}" data-last3months-lang="{LANG.last_3_months}" data-last6months-lang="{LANG.last_6_months}" data-last12months-lang="{LAST_12MONTHS_LANG}" data-last-maxspan-lang="{LAST_MAXSPAN_LANG}" data-customrange-lang="{GLANG.custom_range}" data-lastall="{LAST_ALL_DAYS}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="row mb-2">
                <div class="main_chart_history">
                    <div id="canvas-notice" class="chart col-md-24">
                        <canvas id="tender-notice" width="800" height="500"></canvas>
                    </div>
                </div>
            </div>
            <div class="center-search-bl">
                <div class="ltablesearch-cont">
                    <div class="row">
                        <div class="col-xs-24">
                            <p class="stat-date">{STAT_DATE}</p>
                            <p class="mt-2 package_total">-&nbsp;{STAT_PACKAGE}</p>
                            <ul class="statistics-chart">
                                <!-- BEGIN: thongke -->
                                <li class="{CLASS}"><span>+&nbsp;{STAT}</span></li>
                                <!-- END: thongke -->
                            </ul>
                            <p class="mt-2">-&nbsp;{STAT_GENERAL}</p>
                            <p>{LANG.notice_list_desc}</p>
                        </div>
                        <div class="col-md-24 pdl0 text-right">
                            <form id="download-chart" action="{ACTION}" method="GET">
                                <input type="hidden" name="dfrom" value="{FROM}" />
                                <input type="hidden" name="dto" value="{TO}" />
                                <input type="hidden" name="checkss" value="{NV_CHECK_SESSION}" />
                                <input type="hidden" name="typeChart" value="1" />
                                <input type="hidden" name="download" value="0" />
                                <a class="statistics-export" type="submit" href="javascript:;"><i class="fa fa-download" aria-hidden="true"></i> {LANG.chart_export}</a>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="gradient"></div>
        </div>
        <a href="javascript:;" class="btn-expand text-center">{LANG.expand}</a>
    </div>
    <script type="text/javascript">
        var tbmtObject = $("[id=tender-notice-chart]");
        var downloadObject = $("[id=download-chart]");
        function tbmt_setDaterangepicker(_options) {
            var maxspan = parseInt(tbmtObject.attr("data-maxspan")), mindate = tbmtObject.attr("data-mindate"), opens = tbmtObject.attr("data-opens"), _maxspanDate = moment().subtract(maxspan, 'months');

            // Menu khoảng tìm kiếm
            var ranges = {};
            ranges[$(".search_range_tbmt", tbmtObject).attr("data-last3months-lang")] = [ moment().subtract(3, 'months'), moment() ];
            ranges[$(".search_range_tbmt", tbmtObject).attr("data-last6months-lang")] = [ moment().subtract(6, 'months'), moment() ];
            ranges[$(".search_range_tbmt", tbmtObject).attr("data-last12months-lang")] = [ moment().subtract(12, 'months'), moment() ];
            ranges[$(".search_range_tbmt", tbmtObject).attr("data-last-maxspan-lang")] = [ moment().subtract(24, 'months'), moment() ];

            var allday = $(".search_range_tbmt", tbmtObject).data('lastall');
            if (allday != '') {
                ranges[allday] = [ moment(mindate, "DD/MM/YYYY"), moment() ];
            }

            var calendar_options = {
                showDropdowns: true,
                locale: {
                    cancelLabel: $(".search_range_tbmt", tbmtObject).attr("data-cancel-lang"),
                    applyLabel: $(".search_range_tbmt", tbmtObject).attr("data-apply-lang"),
                    customRangeLabel: $(".search_range_tbmt", tbmtObject).attr("data-customrange-lang"),
                    format: "DD/MM/YYYY",
                },
                ranges: ranges,
                startDate: moment().subtract(6, 'days'),
                endDate: moment(),
                minDate: moment(mindate, "DD/MM/YYYY"),
                maxDate: moment(),
                opens: opens,
                drops: "auto",
                linkedCalendars: false
            };

            $.extend(calendar_options, _options);

            $(".search_range_tbmt", tbmtObject).daterangepicker(calendar_options, function(start, end, label) {
                $("[name=sfrom]", tbmtObject).val(start.format('DD/MM/YYYY'));
                $("[name=sto]", tbmtObject).val(end.format('DD/MM/YYYY'))
            });
        }
        tbmt_setDaterangepicker({ startDate : $("[name=sfrom]", tbmtObject).val(), endDate : $("[name=sto]", tbmtObject).val() });
        
        var lineChart;
        window.chartColors = {
            red : 'rgb(255, 99, 132)',
            orange : 'rgb(255, 159, 64)',
            yellow : 'rgb(255, 205, 86)',
            green : 'rgb(75, 192, 192)',
            blue : 'rgb(54, 162, 235)',
            purple : 'rgb(153, 102, 255)',
            grey : 'rgb(201, 203, 207)'
        };
        var lineChartData = {
            labels : [ {LABEL} ],
            datasets : [
                {
                    label : '{LANG.package_total}',
                    borderColor : window.chartColors.green,
                    backgroundColor : window.chartColors.green,
                    fill : false,
                    data : [ {PACKAGE_TOTAL} ]
                },
                {
                    label : '{LANG.notice_total}',
                    borderColor : window.chartColors.yellow,
                    backgroundColor : window.chartColors.yellow,
                    fill : false,
                    data : [ {NOTICE_TOTAL} ]
                },
                <!-- BEGIN: package_type -->
                    {
                        label : '{PACKAGE_LABEL}',
                        borderColor : '{PACKAGE_COLOR}',
                        backgroundColor : '{PACKAGE_COLOR}',
                        stack: 'Bar',
                        type: 'bar',
                        data : [ {PACKAGE_DATA} ]
                    },
                <!-- END: package_type -->
            ]
        };

        window.onload = function() {
            var ctx = document.getElementById('tender-notice').getContext('2d');
            lineChart = new Chart(ctx,{
                type: 'line',
                data: lineChartData,
                responsive : true,
                hoverMode : 'index',
                stacked : false,
                options: {
                    barThickness: 'flex',
                    maxBarThickness: 40,
                    plugins: {
                        legend: {
                            labels: {
                                filter: function (legendItem, data) {
                                    var lastValue = data.datasets[legendItem.datasetIndex].data[data.datasets[legendItem.datasetIndex].data.length - 1];
                                    var label = data.datasets[legendItem.datasetIndex].label || '';
                                    if (lastValue == 0) {
                                        return false;
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            })
        };
        $('[name="typeChart"], .search_range_tbmt').on('change', function(e){
            $.ajax({
                url: location.href,
                method: 'POST',
                dataType: 'json',
                data: {
                    'ajaxchart': 1,
                    'typeChart': $('[name="typeChart"]', tbmtObject).val(),
                    'sfrom': $('[name="sfrom"]', tbmtObject).val(),
                    'sto': $('[name="sto"]', tbmtObject).val()
                },
                success: function(data) {
                    if (data.res == 'success') {
                        lineChart.data.labels = data.label;
                        lineChart.data.datasets[0].data = data.chart.package_total;
                        lineChart.data.datasets[1].data = data.chart.notice_total;
                        $(".stat-date strong:first-child").text($('[name="sfrom"]', tbmtObject).val().toLocaleString());
                        $(".stat-date strong:last-child").text($('[name="sto"]', tbmtObject).val().toLocaleString());
                        $('.package_total strong').text(data.general.package_total.toLocaleString());
                        $('strong#package_online').text(data.general.package_online.toLocaleString());
                        $('strong#notice_online').text(data.general.notice_online.toLocaleString());
                        $('strong#open_total').text(data.general.open_total.toLocaleString());
                        $('strong#open_expired').text(data.general.open_expired.toLocaleString());
                        $('strong#result_total').text(data.general.result_total.toLocaleString());
                        $('strong#result_expired').text(data.general.result_expired.toLocaleString());
                        var packageIndex = 2;
                        for (var key in data.package) {
                            if (Object.prototype.hasOwnProperty.call(data.package, key)) {
                                var subObject = data.package[key];
                                var lastValue = null;
                                var packageValue = [];
                                for (var subKey in subObject) {
                                    if (Object.prototype.hasOwnProperty.call(subObject, subKey)) {
                                        packageValue.push(subObject[subKey]);
                                        lastValue = subObject[subKey];
                                    }
                                }
                                if (lastValue > 0) {
                                    $('.' + key + ' strong').text(lastValue.toLocaleString());
                                    $('.' + key).addClass('show').removeClass('hide');
                                } else {
                                    $('.' + key).addClass('hide').removeClass('show');
                                }
                                lineChart.data.datasets[packageIndex].data = packageValue;
                                ++packageIndex;
                            }
                        }
                        lineChart.update();
                    }
                }
            });
        });
        $('.statistics-export').on('click', function(e){
            e.preventDefault();
            var form = document.getElementById('dowload-chart');
            $('[name="download"]').val(1);
            $('[name="typeChart"]', downloadObject).val($('[name="typeChart"]', tbmtObject).val());
            $('[name="dfrom"]', downloadObject).val($('[name="sfrom"]', tbmtObject).val());
            $('[name="dto"]', downloadObject).val($('[name="sto"]', tbmtObject).val());
            form.submit();
        });
    </script>
<!-- END: unlock -->
<!-- BEGIN: lock -->
    <div class="static lock-chart-tbmt" style="position: relative; min-height: 200px">
        <lock_detail>
            <div class="box_lockvip__detail">
                <div class="main_lockvip">
                    <div class="tw_lockvip">
                        <div class="tw_lockvip_head">
                            <img class="tw_lockvip_head__image">
                        </div>

                        <div class="tw_lockvip__button">
                            <a href="##link_lock_detail##" class="btn__lock_login">##title_lock_detail##</a>
                        </div>

                        <div class="tw_lockvip__content">
                            <p class="tw_lockvip__content__des">{LANG.title_view_info}</p>
                        </div>
                    </div>
                </div>
            </div>
        </lock_detail>
        ##lock##
        <div class="text-right">
            <a class="statistics-export" href="javascript:;"><i class="fa fa-download" aria-hidden="true"></i> {LANG.chart_export}</a>
        </div>
    </div>
    <script type="text/javascript">
        $(document).ready(function() {
            $('.statistics-export').on('click', function(){
                $('#chart-lock-download').modal({
                    backdrop: 'static'
                }).modal('show');
            });
        });
    </script>
<!-- END: lock -->
<script>
    $(".btn-expand").click(function(){
        $( "#chart_tbmt_wrap" ).toggleClass('active');
        $(this).text(function(i, text){
            return text === "{LANG.expand}" ? "{LANG.collapse}" : "{LANG.expand}";
        });
    });
</script>
<!-- END: chart_tbmt -->

<!-- BEGIN: title_type1 -->
<div class="border-bidding" id="bodycontent">
    <h2 class="title__tab_heading"><span>{PAGE_TITLE_1}</span></h2>
</div>
<!-- END: title_type1 -->
<!-- BEGIN: title_type2 -->
<div class="border-bidding" id="bodycontent">
    <h2 class="title__tab_heading"><span>{LANG.pagetitle_tbmt_type2}</span></h2>
</div>
<!-- END: title_type2 -->

<!-- BEGIN: error_money -->
<div class="alert alert-danger">{ERROR_MONEY}</div>
<!-- END: error_money -->

<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->

<!-- BEGIN: empty -->
<div class="alert alert-warning">{LANG.empty_result}</div>
<!-- END: empty -->
<!-- BEGIN: add_filter -->
<div class="alert alert-success">{LINK_ADD}</div>
<!-- END: add_filter -->
<!-- BEGIN: note_small_org -->
<div class="alert alert-success">{LANG.note_small_org_detail}</div>
<!-- END: note_small_org -->

<!-- BEGIN: view -->
<form action="{NV_BASE_SITEURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <table class="bidding-table">
        <thead>
            <tr>
                <th class="h-ten-goithau">{LANG.goi_thau}</th>
                <th>{LANG.ben_moi_thau}</th>
                <th>{LANG.ngay_dang_tai}</th>
                <th>{LANG.den_ngay}</th>
            </tr>
        </thead>
        <tbody>
            <!-- BEGIN: loop -->
            <tr>
                <td class="order-header" data-column="{LANG.goi_thau}">
                    <div class="wrap__text">
                        <a title="{VIEW.goi_thau}" href="{VIEW.link}"><span class="bidding-code">{VIEW.so_tbmt}</span> {VIEW.goi_thau}</a>

                        <!-- BEGIN: admin_link -->
                        ({LANG.crawl_time}: {VIEW.get_time})
                        <!-- END: admin_link -->
                        
                        <!-- BEGIN: violate_warning -->
                        <span class="warning-area">
                            <i class="fa fa-exclamation-triangle online-rule-violate" aria-hidden="true"></i>
                            <blockquote class="custom_border">{VIEW.violate_msg}</blockquote>
                        </span>
                        <!-- END: violate_warning -->

                        <!-- BEGIN: clarify -->
                        <span class="warning-area">
                            <i class="fa fa-exclamation-triangle online-rule-violate" aria-hidden="true"></i>
                            <blockquote class="custom_border">{VIEW.clarify_msg}</blockquote>
                        </span>
                        <!-- END: clarify -->

                    </div>
                </td>
                <td data-column="{LANG.ben_moi_thau}">
                    <div>
                        <!-- BEGIN: link_solicitor -->
                        <a title="{VIEW.ben_moi_thau}" href="{VIEW.link_solicitor}"> <!-- BEGIN: solicitor_code --> <span class="solicitor-code">{VIEW.solicitor_code}</span> <!-- END: solicitor_code --> {VIEW.ben_moi_thau}
                        </a>
                        <!-- END: link_solicitor -->
                        <!-- BEGIN: no_link_solicitor -->
                        {VIEW.ben_moi_thau}
                        <!-- END: no_link_solicitor -->
                    </div>
                </td>
                <td class="txt-center" data-column="{LANG.ngay_dang_tai}"><div>{VIEW.ngay_dang_tai}</div></td>
                <td class="txt-center" data-column="{LANG.den_ngay}"><div>{VIEW.den_ngay}</div></td>
            </tr>
            <!-- END: loop -->
        </tbody>
    </table>

    <!-- BEGIN: generate_page -->
    <div class="text-center">{NV_GENERATE_PAGE}</div>
    <!-- END: generate_page -->
</form>
<div class="modal fade" id="idmodals" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <p class="modal-title">{LANG.bidding_info}</p>
            </div>
            <div class="modal-body">
                <em class="fa fa-spinner fa-spin">&nbsp;</em>
            </div>
        </div>
    </div>
</div>
<!-- END: view -->
<div id="chart-lock-download" class="modal fade auto-height" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <div class="h1" style="display: inline-block;"><strong>{LANG.popup_title}</strong></div>
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                ##lock_download##
            </div>
        </div>
    </div>
</div>
<!-- BEGIN: bids_row_dtnet -->
<div class="border-bidding" id="bodycontent">
    <h2 class="title__tab_heading"><span>{LANG.result_for_search_dtnet}</span></h2>
</div>
<div class="alert alert-success">{LANG.goi_y_goi_thau_dtnet}</div>
<table class="bidding-table">
    <thead>
    <tr>
        <th class="h-ten-goithau">{LANG.ten_goi_thau}</th>
        <th>{LANG.chu_dau_tu}</th>
        <th>{LANG.ngay_dang_tai}</th>
        <th>{LANG.den_ngay}</th>
    </tr>
    </thead>
    <tbody>
    <!-- BEGIN: loop -->
    <tr>
        <td class="order-header" data-column="{LANG.ten_goi_thau}">
            <div class="wrap__text">
                <a title="{ROW_DTNET.title}" href="{ROW_DTNET.link_bids_rows}"><span class="bidding-code">{ROW_DTNET.code}</span> {ROW_DTNET.title}</a>
            </div>
        </td>
        <td data-column="{LANG.chu_dau_tu}">
            <div>
                <!-- BEGIN: link_invector -->
                <a title="{ROW_DTNET.prof_name}" href="{ROW_DTNET.link_invector}"> <!-- BEGIN: invector_code --> <span class="solicitor-code">{ROW_DTNET.prof_code}</span> <!-- END: invector_code --> {ROW_DTNET.prof_name}
                </a>
                <!-- END: link_invector -->
                <!-- BEGIN: no_link_invector -->
                {ROW_DTNET.prof_name}
                <!-- END: no_link_invector -->
            </div>
        </td>
        <td class="txt-center" data-column="{LANG.ngay_dang_tai}"><div>{ROW_DTNET.bid_time_open}</div></td>
        <td class="txt-center" data-column="{LANG.den_ngay}"><div>{ROW_DTNET.bid_time_closed}</div></td>
    </tr>
    <!-- END: loop -->
    </tbody>
</table>
<div class="text-center"><a class="btn btn-primary" href="{ROW_DTNET.link_see_more}">{LANG.see_more}</a></div>
<!-- END: bids_row_dtnet -->
<!-- END: main -->
<!-- BEGIN: subindustry -->
<ul>
	<!-- BEGIN: loop -->
	<li>
		<a href="{VALUE.link}" data-level="{VALUE.level}" data-code="{VALUE.code}" class="vsic-child">{VALUE.code} - {VALUE.title} ({VALUE.count})</a>
		<!-- BEGIN: item --> {SUB} <!-- END: item -->
	</li>
	<!-- END: loop -->
</ul>
<!-- END: subindustry -->
<!-- BEGIN: vsic -->
<div class="row industry_tree__mobile">
	<ul class="tree1">
		<!-- BEGIN: show_parent -->
		<li>
			<a href="{VALUE.link}" data-level="{VALUE.level}" data-code="{VALUE.code}" class="vsic-child"><!-- BEGIN: is_code -->{VALUE.code} - <!-- END: is_code -->{VALUE.title} ({VALUE.count})</a>
			<!-- BEGIN: show_item --> {SUB} <!-- END: show_item -->
		</li>
		<!-- END: show_parent -->
	</ul>
</div>
<!-- END: vsic -->
