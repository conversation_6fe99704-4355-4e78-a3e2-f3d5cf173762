<!-- BEGIN: main -->
<!-- BEGIN: popup_login -->
{POPUP_LOGIN}
<!-- END: popup_login -->
<!-- BEGIN: recaptcha -->
<div id="captchaModal" class="modal fade auto-width auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <p class="modal-title">{LANG.recapcha_title}</p>
            </div>
            <div class="modal-body">
                <div>{LANG.recapcha_body}</div>
                <div data-toggle="recaptcha" data-callback="click_update" id="{RECAPTCHA_ELEMENT}"></div>
            </div>
        </div>
    </div>
</div>
<!-- END: recaptcha -->

<div class="bidding-detail-wrapper bidding-detail-wrapper-result">
    <h1 class="bidding-name">{ROW.title}</h1>

    <div class="prb_container">
        <div class="prb clearfix">
            <span class="prb-progressbar">{LANG.viewing}</span>
            <span class="prb-progressbar">&nbsp;</span>
        </div>
        <div class="prb clearfix">
            <span class="item"> <span class="icn tbmt active current" title="{LANG.thong_bao_to_chuc_dau_gia}"></span> <span class="tl">{LANG.thong_bao_to_chuc_dau_gia}</span>
            </span>

            <!-- BEGIN: has_result_data -->
            <a class="item" href="{RESULT_DATA.url}"> <span class="icn active kqlcnt" title="{LANG.result_select_organization}"></span> <span class="tl">{LANG.result_select_organization}</span>
            </a>
            <!-- END: has_result_data -->
            <!-- BEGIN: no_result_data -->
            <span class="item"> <span class="icn kqlcnt" title="{LANG.result_select_organization}"></span> <span class="tl">{LANG.result_select_organization}</span>
            </span>
            <!-- END: no_result_data -->
        </div>
    </div>

    <div class="bidding-page-btn flex_end">
        <div class="bidding-page-btn-right">
            <div class="btn-share-group">
                <span>{LANG.share} </span>
                <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_fb_share();" title="{LANG.fb_share}">
                    <span class="icon-facebook"></span>
                </a>
                <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_tw_share('', '{DATA.title}');" title="{LANG.tweet}">
                    <span class="icon-twitter"></span>
                </a>
                <a href="javascript:void(0)" class="btn-share btn-copy-link" title="{LANG.copy_link}">
                    <em class="fa fa-link"></em>
                    <span class="tip" style="display: none;">{LANG.link_copy_successfully}</span>
                </a>
            </div>
            <!-- BEGIN: update -->
            <div class="text-right m-bottom">
                <div class="small">
                    {LANG.updated_at}: <strong>{ROW.update_at}</strong>
                </div>
                <div class="margin-top-sm m-bottom">
                    <span class="small">{ROW.update_info}</span> <a id="reupdate" class="btn btn-default btn-xs active" onclick="show_captcha()" href="javascript:void(0)" data-id="{ROW.id_bid}" data-check="{CHECKSESS_UPDATE}">{LANG.reupdate}</a><span id="show_error" class="text-danger margin-left" style="display: none"></span>
                </div>
            </div>
            <!-- END: update -->
        </div>
    </div>
    <div class="bidding-detail">
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.note_status}</div>
                <div class="c-val">{ROW.note}</div>
            </div>
            <div>
                <div class="c-tit">{LANG.post_time}</div>
                <div class="c-val">{ROW.re_post}</div>
            </div>
        </div>
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.date_public}</div>
                <div class="c-val">{ROW.date_bid_format}</div>
            </div>
            <!-- BEGIN: tb_lien_quan -->
            <div>
                <div class="c-tit">{LANG.tb_lien_quan}</div>
                <div class="c-val">{ROW.link}</div>
            </div>
            <!-- END: tb_lien_quan -->
        </div>
        <p class="bidding-sub-title">{LANG.info_propertys}:</p>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.name_propertys}</div>
            <div class="c-val title-strong">{ROW.name_owner}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.address}</div>
            <div class="c-val">{ROW.address_owner}</div>
        </div>
        <p class="bidding-sub-title">{LANG.info_sale}:</p>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.time_re}</div>
            <div class="c-val">{ROW.date}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.address_re}</div>
            <div class="c-val">{ROW.address}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.contact}</div>
            <div class="c-val">{ROW.contact}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.link_detail}</div>
            <div class="c-val">{ROW.link_detail}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.source}</div>
            <div class="c-val">{ROW.view_type}</div>
        </div>

        <!-- BEGIN: chosen_org_title -->
        <p class="bidding-sub-title">{LANG.result}:</p>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.chosen_org}</div>
            <div class="c-val">{ROW.chosen_org_title}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.result_files}</div>
            <div class="c-val">{ROW.result_files}</div>
        </div>
        <!-- END: chosen_org_title -->

        <h2 class="bidding-sub-title">{LANG.property_list}:</h2>
        <table class="bidding-table">
            <thead>
                <tr>
                    <th>{LANG.stt}</th>
                    <th>{LANG.name_property}</th>
                    <th>{LANG.no}</th>
                    <th>{LANG.quality}</th>
                    <th>{LANG.start_price}</th>
                    <th>{LANG.file}</th>
                </tr>
            </thead>
            <tbody row="rowgroup">
                <!-- BEGIN: asset -->

                <tr role="row">
                    <td data-column="STT"><div>{ASSET.stt}</div></td>
                    <td data-column="Tên tài sản"><div>{ASSET.name_asset}</div></td>
                    <td data-column="Số lượng"><div>{ASSET.qty}</div></td>
                    <td data-column="Chất lượng"><div>{ASSET.quality}</div></td>
                    <td data-column="Giá khởi điểm"><div>{ASSET.min_bid_prices}</div></td>
                    <td data-column="File đính kèm"><div>{ASSET.link_detail}</div></td>
                </tr>
                <!-- END: asset -->
            </tbody>
        </table>
    </div>

    <h2 class="bidding-sub-title">{LANG.chance_for_you}:</h2>
    <div class="chance-cont" id="box_for_you">
        <div class="chance">
            <div class="panel panel-default">
                <div class="cont panel-heading">
                    <div class="tl">{LANG.filter}</div>
                    <div class="ct">{FILTER.content}</div>
                    <div class="bt">
                        <a class="btn btn-primary" href="{FILTER.reg_link}">{FILTER.btn_title}</a>
                    </div>
                </div>
            </div>
            <!-- BEGIN: receive_email_content -->
            <div class="panel panel-default">
                <div class="cont panel-heading">
                    <div class="tl">{LANG.receive_email}</div>
                    <div class="ct">{RECEIVE_EMAIL.content}</div>
                    <div class="bt">
                        <a class="btn btn-danger" href="{RECEIVE_EMAIL.reg_link}">{RECEIVE_EMAIL.vip_paket}</a>
                    </div>
                </div>
            </div>
            <!-- END: receive_email_content -->
        </div>
    </div>
    <div class="news_column row">
        <div class="panel-body ">
            <div class="col-md-18">
                <span class="label label-primary">{LANG.totalview}: <span class="badge">{ROW.viewcount}</span></span>
            </div>
            <div class="col-md-6 pull-right">
                <div class="socialicon clearfix col-md-16">
                    <div class="fb-like" data-href="{SELFURL}" data-layout="button_count" data-action="like" data-show-faces="false" data-share="true">&nbsp;</div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- <div class="news_column panel panel-default">
    <div class="panel-body">[FACEBOOK_COMMENT]</div>
</div> -->
<!-- END: main -->
