<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_IS_MOD_BIDDING'))
    die('Stop!!!');

$province = $nv_Request->get_int('province', 'get', -1);
$district = $nv_Request->get_int('district', 'get', 0);
$ward = $nv_Request->get_int('ward', 'get', 0);
$where = [];
$page = $nv_Request->get_page('page', 'post,get', 1);
$per_page = 20;
$arr_beackcolumn = [];
$config_bidding = $module_config[$module_name];

if (isset($array_op[2])) {
    $a = substr($array_op[2], 0, 1);
    $b = substr($array_op[2], 2);
    $_alias = "";
    if (preg_match('/^([a-zA-Z0-9\-]+)\-([0-9]+)$/i', $b, $m)) {
        $b = $m[2];
        $_alias = $m[1];
    } else {
        nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
    }

    if ($a == "T") {
        if ($b == 0) {
            $arr_beackcolumn['T'] = array(
                "id" => 0,
                "key" => "T",
                "title" => $nv_Lang->getModule('undefined'),
                "alias" => strtolower(change_alias($nv_Lang->getModule('undefined')))
            );
            $province = $id = 0;
            $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $other_lang_module_info['alias']['solicitor'] . "/listlocation/T-" . $other_lang_alias . '-' . $id;
        } else {
            $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province WHERE status=1 AND id = " . $db->quote($b);
            $result = $db->query($sql);
            list ($id, $title, $alias) = $result->fetch(3);
            if (empty($id)) {
                $sql = "SELECT id, alias FROM " . NV_PREFIXLANG . "_location_province WHERE status=1 AND alias = " . $db->quote($_alias);
                $result = $db->query($sql);
                list ($id, $alias) = $result->fetch(3);
                if (empty($id)) {
                    nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('admin_no_allow_func'), 404);
                } else {
                    nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "/listlocation/T-" . $alias . '-' . $id);
                }
            }
            $arr_beackcolumn['T'] = array(
                "id" => $id,
                "key" => "T",
                "title" => $title,
                "alias" => $alias
            );
            $province = $id;
            $other_lang_alias = $db->query("SELECT alias FROM " . $other_lang_prefix . "_location_province WHERE id = " . $db->quote($id))
                ->fetchColumn();
            $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $other_lang_module_info['alias']['solicitor'] . "/listlocation/T-" . $other_lang_alias . '-' . $id;
        }
    } elseif ($a == "H") {
        $sql = "SELECT id, idprovince, title, alias FROM " . NV_PREFIXLANG . "_location_district WHERE status=1 AND id= " . $db->quote($b);
        $result = $db->query($sql);
        list ($iddistrict, $idprovince, $title, $alias) = $result->fetch(3);
        if (empty($iddistrict)) {
            $sql = "SELECT id, idprovince, title, alias FROM " . NV_PREFIXLANG . "_location_district WHERE status=1 AND alias= " . $db->quote(substr($array_op[2], 2));
            $result = $db->query($sql);
            list ($iddistrict, $idprovince, $title, $alias) = $result->fetch(3);
            if (!empty($iddistrict)) {
                $url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "/H-" . $alias . '-' . $iddistrict;
                if ($page > 1) {
                    $url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "/H-" . $alias . '-' . $iddistrict . '/' . 'page-' . $page;
                }
                nv_redirect_location($url);
            }
        }

        if (empty($iddistrict)) {
            nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name);
        }

        $arr_beackcolumn['H'] = array(
            "id" => $iddistrict,
            "key" => "H",
            "title" => $title,
            "alias" => $alias
        );
        // lay tinh cua huyen
        $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province WHERE status=1 AND id = " . intval($idprovince);
        $result = $db->query($sql);
        list ($id, $title, $alias) = $result->fetch(3);
        $arr_beackcolumn['T'] = array(
            "id" => $id,
            "key" => "T",
            "title" => $title,
            "alias" => $alias
        );
        $district = $iddistrict;
        $other_lang_alias = $db->query("SELECT alias FROM " . $other_lang_prefix . "_location_district WHERE id = " . $db->quote($iddistrict))
            ->fetchColumn();
        $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $other_lang_module_info['alias']['solicitor'] . "/listlocation/H-" . $other_lang_alias . '-' . $iddistrict;
    } elseif ($a == "X") {
        $sql = "SELECT id, idprovince, iddistrict, title, alias FROM " . NV_PREFIXLANG . "_location_ward WHERE status=1 AND id = " . $db->quote($b);
        $result = $db->query($sql);
        list ($id, $idprovice, $iddistrict, $title, $alias) = $result->fetch(3);
        if (empty($id)) {
            $sql = "SELECT id, idprovince, iddistrict, title, alias FROM " . NV_PREFIXLANG . "_location_ward WHERE status=1 AND alias= " . $db->quote(substr($array_op[2], 2));
            $result = $db->query($sql);
            list ($id, $idprovice, $iddistrict, $title, $alias) = $result->fetch(3);
            if (!empty($id)) {
                $url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "/listlocation/X-" . $alias . '-' . $id;
                if ($page > 1) {
                    $url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "/listlocation/X-" . $alias . '-' . $id . '/' . 'page-' . $page;
                }
                nv_redirect_location($url);
            }
        }

        if (empty($id)) {
            nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name);
        }

        // breakcolumn
        $arr_beackcolumn['X'] = array(
            "id" => $id,
            "key" => "X",
            "title" => $title,
            "alias" => $alias
        );
        // lay huyen cua xa
        $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_district WHERE status=1 AND id = " . intval($iddistrict) . "";
        $result = $db->query($sql);
        list ($iddistrict, $title, $alias) = $result->fetch(3);
        $arr_beackcolumn['H'] = array(
            "id" => $iddistrict,
            "key" => "H",
            "title" => $title,
            "alias" => $alias
        );
        // lay tinh cua huyen
        $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province WHERE status=1 AND id = " . intval($idprovice) . "";
        $result = $db->query($sql);
        list ($idprovince, $title, $alias) = $result->fetch(3);
        $arr_beackcolumn['T'] = array(
            "id" => $idprovince,
            "key" => "T",
            "title" => $title,
            "alias" => $alias
        );

        $ward = $id;
        $other_lang_alias = $db->query("SELECT alias FROM " . $other_lang_prefix . "_location_ward WHERE id = " . $db->quote($id))
            ->fetchColumn();
        $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $other_lang_module_info['alias']['solicitor'] . "/listlocation/X-" . $other_lang_alias . '-' . $id;
    } else {
        nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
    }
} else {
    nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
}

$list_compare = $nv_Request->get_title('lstcpctt', 'cookie', true);

$list_compare = json_decode($list_compare, true);
!is_array($list_compare) && $list_compare = [];
$is_project_owner = false;
if (isset($array_op[0]) && $array_op[0] == $module_info['alias']['project-owner']) {
    $is_project_owner = true;
}

if ($config_bidding['elas_use']) {
    // kết nối tới ElasticSearh
    $nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($config_bidding['elas_host'], $config_bidding['elas_port'], 'dauthau_solicitor', $config_bidding['elas_user'], $config_bidding['elas_pass']);
    $search_elastic = [];
    if ($is_project_owner) {
        $search_elastic['must'][] = [
            "range" => [
                "is_project_owner" => [
                    'gt' => 0
                ]
            ]
        ];
    } else {
        $search_elastic['must'][] = [
            'match' => [
                'is_solicitor' => [
                    'query' => 1
                ]
            ]
        ];
    }
    if ($ward > 0) {
        $search_elastic['must'][] = [
            'match' => [
                'id_ward' => [
                    'query' => $ward
                ]
            ]
        ];
    }
    if ($district > 0) {
        $search_elastic['must'][] = [
            'match' => [
                'id_district' => [
                    'query' => $district
                ]
            ]
        ];
    }
    if ($province > -1) {
        $search_elastic['must'][] = [
            'match' => [
                'id_province' => [
                    'query' => $province
                ]
            ]
        ];
    }
    $array_query_elastic = array();
    if (!empty($search_elastic)) {
        $array_query_elastic['query']['bool'] = $search_elastic;
    }
    $array_query_elastic['track_total_hits'] = 'true';
    $array_query_elastic['size'] = $per_page;
    $array_query_elastic['from'] = ($page - 1) * $per_page;
    $array_query_elastic['sort'] = [
        [
            "aproval_time" => [
                "order" => "desc"
            ]
        ]
    ];
    $response = $nukeVietElasticSearh->search_data(BID_PREFIX_GLOBAL . '_solicitor', $array_query_elastic);
    $num_solicitor = $response['hits']['total']['value'];
    foreach ($response['hits']['hits'] as $value) {
        if (!empty($value['_source'])) {
            $view = $value['_source'];
            if (in_array($view['id'], $list_compare)) {
                $view['is_added_cmp'] = 1;
            } else {
                $view['is_added_cmp'] = 0;
            }
            $view['checkremvcp'] = md5(NV_CHECK_SESSION . '_checkremvcp_' . $view['id']);
            $view['checkaddcp'] = md5(NV_CHECK_SESSION . '_checkaddcp_' . $view['id']);
            $arr_data[] = $view;
        }
    }
} else {
    if ($province > -1) {
        $where[] = 'id_province = ' . $province;
    }
    if (!empty($district)) {
        $where[] = 'id_district = ' . $district;
    }
    if (!empty($ward)) {
        $where[] = 'id_ward = ' . $ward;
    }

    if (!empty($where)) {
        $_where = implode(' AND ', $where);
    }

    $db->sqlreset()
        ->select('SQL_CALC_FOUND_ROWS *')
        ->from(BID_PREFIX_GLOBAL . '_solicitor')
        ->order('aproval_time DESC')
        ->limit($per_page)
        ->offset(($page - 1) * $per_page)
        ->where($_where);
    $sth = $db->prepare($db->sql());
    $sth->execute();
    $arr_data = $sth->fetchAll();
    foreach ($arr_data as $k => $view) {
        if (in_array($view['id'], $list_compare)) {
            $view['is_added_cmp'] = 1;
        } else {
            $view['is_added_cmp'] = 0;
        }
        $view['checkremvcp'] = md5(NV_CHECK_SESSION . '_checkremvcp_' . $view['id']);
        $view['checkaddcp'] = md5(NV_CHECK_SESSION . '_checkaddcp_' . $view['id']);
        $arr_data[$k] = $view;
    }

    list ($num_solicitor) = $db->query("SELECT FOUND_ROWS()")->fetch(3);
}
$link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "/listlocation";
$beackcolumn = "";
foreach ($arr_beackcolumn as $item) {
    $beackcolumn .= "<a href=\"" . $link . "/" . $item['key'] . "-" . $item['alias'] . "-" . $item['id'] . "\" alt=\"" . $nv_Lang->getModule('solicitor_list_at') . " " . $item['title'] . " \">" . $item['title'] . "</a>, ";
}
$beackcolumn = rtrim(trim($beackcolumn), ',');

// Trả về kết quả id, tên xã
// Lấy Tiêu đề các xã
$_arr_ward = [];
if (!empty($arr_data)) {
    foreach ($arr_data as $row) {
        if ($row['id_ward'] > 0) {
            $_arr_ward[] = $row['id_ward'];
        }
    }
}

$base_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "/listlocation/" . $array_op[2];
$page_url = $base_url;

if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
    $global_lang_url .= '&amp;page=' . $page;
}

$canonicalUrl = getCanonicalUrl($page_url);
$urlappend = '&amp;page=';
betweenURLs($page, ceil($num_solicitor / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

$list_solicitor = array();
if (!empty($arr_data)) {
    foreach ($arr_data as $row) {
        $row['addressfull'] = [];
        $_link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op;
        if ($row['address'] != "") {
            $row['addressfull'][] = handle_address($row['address'], $row['id_ward'], $row['id_district'], $row['id_province']);
        }
        if ($row['id_ward'] > 0 and isset($ward_list[$row['id_ward']])) {
            $ward = $ward_list[$row['id_ward']];
            $row['addressfull'][] = '<a href="' . $_link . '/listlocation/X-' . $ward['alias'] . '-' . $ward['id'] . '"\>' . $ward['title'] . '</a>';
        }
        $row['ward_seo'] = $ward_list['title'] ?? '';
        if (isset($district_list[$row['id_district']]) and !empty($row['id_district'])) {
            $row['addressfull'][] = '<a href="' . $_link . '/listlocation/H-' . $district_list[$row['id_district']]['alias'] . '-' . $district_list[$row['id_district']]['id'] . '"\>' . $district_list[$row['id_district']]['title'] . '</a>';
        }
        $row['district_seo'] = $district_list[$row['id_district']]['title'] ?? '';
        if (isset($province_list[$row['id_province']]) and !empty($row['id_province'])) {
            $row['addressfull'][] = '<a href="' . $_link . '/listlocation/T-' . $province_list[$row['id_province']]['alias'] . '-' . $province_list[$row['id_province']]['id'] . '"\">' . $province_list[$row['id_province']]['title'] . '</a>';
        }
        $row['addressfull'] = implode(', ', array_filter($row['addressfull']));
        $row['addressfull'] = str_replace(',,', ',', $row['addressfull']);
        if (empty($row['addressfull'])) {
            $row['addressfull'] = 'N/A';
        }
        $row['address'] = $row['addressfull'];
        if (isset($province_list[$row['id_province']])) {
            $row['location'] = $province_list[$row['id_province']]['title'];
            $row['link_location'] = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "/listlocation/T-" . $province_list[$row['id_province']]['alias'] . '-' . $province_list[$row['id_province']]['id'];
        } else {
            $row['location'] = 'N/A';
            $row['link_location'] = '';
        }
        $row['aproval_time'] = $row['aproval_time'] > 0 ? nv_date('d/m/Y', $row['aproval_time']) : '';
        if ($is_project_owner) {
            $row['link_detail'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['project-owner'] . '/' . $row['alias'] . '-' . $row['id'], true);
        } else {
            $row['link_detail'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['solicitor'] . '/' . $row['alias'] . '-' . $row['id'], true);
        }
        
        $list_solicitor[] = $row;
    }
}
$generate_page = nv_generate_page($base_url, $num_solicitor, $per_page, $page);
$_title_h = sprintf(($is_project_owner ? $nv_Lang->getModule('project_owner_list_at') : $nv_Lang->getModule('solicitor_list_at')), $beackcolumn);
$contents = nv_theme_bidding_solicitor_location($list_solicitor, $generate_page, $num_solicitor, $_title_h, $is_project_owner);

$description = sprintf(($is_project_owner ? $nv_Lang->getModule('project_owner_list_info_at') : $nv_Lang->getModule('solicitor_list_info_at')), strip_tags($beackcolumn));
$page_title = str_replace('&raquo;', '', strip_tags($beackcolumn));

include (NV_ROOTDIR . "/includes/header.php");
echo nv_site_theme($contents);
include (NV_ROOTDIR . "/includes/footer.php");
