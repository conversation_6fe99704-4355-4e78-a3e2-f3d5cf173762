<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_BIDDING'))
    die('Stop!!!');

use NukeViet\Dauthau\Url;
use NukeViet\Dauthau\Share;
$max_vaule_price = Share::MAX_VALUE_PRICE;
$where = ' addtime>=' . $sfrom1 . ' AND addtime<=' . $sto1;
if ($type_search == 2 and $is_advance) {
    // tìm theo tổng mức đầu tư
    if ($invest_from > 0 and $invest_from <= $max_vaule_price) {
        $where .= " AND total_cost_num >= " . $invest_from;
    }
    if ($invest_to > 0 and $invest_to <= $max_vaule_price) {
        $where .= " AND total_cost_num <= " . $invest_to;
    }
} else {
    // tìm theo tổng mức đầu tư
    if ($invest_from > 0 and $invest_from <= $max_vaule_price and $is_advance) {
        $where .= " AND total_invest_number >= " . $invest_from;
    }
    if ($invest_to > 0 and $invest_to <= $max_vaule_price and $is_advance) {
        $where .= " AND total_invest_number <= " . $invest_to;
    }
}

if ($price_from > 0 and $is_advance) {
    // tìm theo giá của các gói thầu
    $where .= " AND price_max>=" . $price_from;
}
if ($price_to > 0 and $is_advance) {
    // tìm theo giá của các gói thầu
    $where .= " AND price_min<=" . $price_to;
}

if (!empty($_idprovince) and $type_search == 1) {
    $find_province = [];
    foreach ($_idprovince as $v) {
        if ($v != 0) {
            $find_province[] = "FIND_IN_SET($v, list_id_province)";
        }
    }

    if (!empty($find_province)) {
        $where .= " AND (" . implode(' OR ', $find_province) . ")";
    }
}

if ($id_hinhthucluachon != 0 && $is_advance) {
    $where .= " AND (is_new_msc=0 and id_hinhthucluachon LIKE " . $db->quote('%[' . $id_hinhthucluachon . ']%') . ") OR (is_new_msc=1 and is_domestic=" . $id_hinhthucluachon == 1 ? 1 : 0 . ")";
}

if (!empty($without_key) and $is_advance) {
    // Tìm theo từ khóa loại trừ
    $arr_key = explode(',', $without_key);
    foreach ($arr_key as $key) {
        if ($search_type_content == 1 and $type_search == 1) {
            $key = trim($key);
            if (!$par_search) {
                $where .= " AND content_full COLLATE utf8mb4_unicode_ci NOT LIKE " . $db->quote('%' . $key . '%') . '';
            } else {
                if (!preg_match('/[0-9]/', $key)) {
                    $where .= " AND title COLLATE utf8mb4_unicode_ci NOT LIKE " . $db->quote('%' . $key . '%') . "";
                } else {
                    $where .= " AND code COLLATE utf8mb4_unicode_ci NOT LIKE " . $db->quote('%' . $key . '%') . " AND title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . '';
                }
            }
        } else {
            $key = trim($key);
            if (!$par_search) {
                $where .= " AND content NOT LIKE " . $db->quote('%' . $key . '%');
            } else {
                if (!preg_match('/[0-9]/', $key)) {
                    $where .= " AND title NOT LIKE " . $db->quote('%' . $key . '%');
                } else {
                    $where .= " AND code NOT LIKE " . $db->quote('%' . $key . '%') . " AND title NOT LIKE " . $db->quote('%' . $key . '%');
                }
            }
        }
    }
}

if (!empty($key_search2) and $is_advance) {
    // Tìm theo Từ khóa bổ sung
    $arr_key = explode(',', $key_search2);
    $_where_1 = [];
    foreach ($arr_key as $key) {
        if (!empty($search_one_key)) {
            if ($search_type_content == 1 and $type_search == 1) {
                $key = trim($key);
                if (!$par_search) {
                    $_where_1[] = "content_full COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . '';
                } else {
                    if (!preg_match('/[0-9]/', $key)) {
                        $_where_1[] = "title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . "";
                    } else {
                        $_where_1[] = "code COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . " OR title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%');
                    }
                }
            } else {
                $key = trim($key);
                if (!$par_search) {
                    $_where_1[] = "content LIKE " . $db->quote('%' . $key . '%');
                } else {
                    if (!preg_match('/[0-9]/', $key)) {
                        $_where_1[] = "title LIKE " . $db->quote('%' . $key . '%');
                    } else {
                        $_where_1[] = "code LIKE " . $db->quote('%' . $key . '%') . " OR title LIKE " . $db->quote('%' . $key . '%');
                    }
                }
            }
        } else {
            if ($search_type_content == 1 and $type_search == 1) {
                $key = trim($key);
                if (!$par_search) {
                    $where .= " AND content_full COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . '';
                } else {
                    if (!preg_match('/[0-9]/', $key)) {
                        $where .= " AND title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . "";
                    } else {
                        $where .= " AND (code COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . " OR title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . ')';
                    }
                }
            } else {
                $key = trim($key);
                if (!$par_search) {
                    $where .= " AND content LIKE " . $db->quote('%' . $key . '%');
                } else {
                    if (!preg_match('/[0-9]/', $key)) {
                        $where .= " AND title LIKE " . $db->quote('%' . $key . '%');
                    } else {
                        $where .= " AND (code LIKE " . $db->quote('%' . $key . '%') . " OR title LIKE " . $db->quote('%' . $key . '%') . ')';
                    }
                }
            }
        }
    }
    if (!empty($_where_1)) {
        $where .= ' AND (' . implode(' OR ', $_where_1) . ')';
    }
}

if ($solicitor_id > 0) {
    $where .= " AND solicitor_id=" . $solicitor_id;
}
if (!empty($q)) {
    $where .= ' AND (';
    $num = 0;
    $arr_key = explode(',', $q);
    foreach ($arr_key as $key) {
        if ($search_type_content == 1 and $type_search == 1) {
            $key = trim($key);
            $num > 0 && $where .= ' OR ';
            if (!$par_search) {
                $where .= "content_full COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . '';
            } else {
                if (!preg_match('/[0-9]/', $key)) {
                    $where .= "title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . "";
                } else {
                    $where .= "code COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . " OR title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . '';
                }
            }
        } else {
            $key = trim($key);
            $num > 0 && $where .= ' OR ';
            if (!$par_search) {
                $where .= "content LIKE " . $db->quote('%' . $key . '%');
            } else {
                if (!preg_match('/[0-9]/', $key)) {
                    $where .= "title LIKE " . $db->quote('%' . $key . '%');
                } else {
                    $where .= "code LIKE " . $db->quote('%' . $key . '%') . " OR title LIKE " . $db->quote('%' . $key . '%');
                }
            }
        }
        $num++;
    }
    $where .= ')';
}

// gói vip 4
if ($type_search == 2) {
    $db->sqlreset()
        ->select('COUNT(id)')
        ->from(NV_PREFIXLANG . '_' . $module_data . '_plans_project')
        ->where($where);
    $sth = $db->prepare($db->sql());
} else {
    $db->sqlreset()
        ->select('COUNT(id)')
        ->from(NV_PREFIXLANG . '_' . $module_data . '_plans')
        ->where($where);
    $sth = $db->prepare($db->sql());
}

$sth->execute();
$num_items = $sth->fetchColumn();

$db->select('*')
    ->order('addtime DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());

$sth->execute();

while ($view = $sth->fetch()) {

    $view['addtime'] = $view['addtime'] > 0 ? $TBMT::time_display($view['addtime']) : '';
    if ($type_search == 2) {
        $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['plans'] . '/' . Url::getKHLCNDT() . '/' . $view['alias']. '-' . $view['id'] . $global_config['rewrite_exturl'];
        $view['link_search'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=listplan&type=1&solicitor_id=' . $view['solicitor_id'];
    } else {
        $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['plans'] . '/' . Url::getKHLCNT() . '/' . $view['alias'] . '-' . $view['id'] . $global_config['rewrite_exturl'];
        $view['link_search'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=listplan&solicitor_id=' . $view['solicitor_id'];
    }

    $array_data[$view['id']] = $view;
    $arr_solicitor_id[$view['solicitor_id']] = $view['solicitor_id'];
}
