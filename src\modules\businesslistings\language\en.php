<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_MAINFILE'))
    die('Stop!!!');

$lang_translator['author'] = "VINADES.,JSC (<EMAIL>)";
$lang_translator['createdate'] = "15/09/2011, 03:06";
$lang_translator['copyright'] = "@Copyright (C) 2011 VINADES.,JSC. All rights reserved";
$lang_translator['info'] = "";
$lang_translator['langtype'] = "lang_module";

$lang_module['main'] = "Main page";
$lang_module['detail'] = "View details";
$lang_module['pleaseselect'] = "Please select";
$lang_module['deselect'] = "Deselect";
$lang_module['businesstype'] = "Form of Business";
$lang_module['save'] = "Save";
$lang_module['industry'] = "Industry Classification";
$lang_module['industry_icb'] = "Industry Classification";
$lang_module['lvkd'] = "Field of Business";
$lang_module['fee_status'] = "Payment Status";
$lang_module['quantri'] = "Enterprise Governance Framework";
$lang_module['businessnghiepyeuthich'] = "Favorite businesses";
$lang_module['kiemtradoanhnghiep'] = "Check the business you are monitoring.";
$lang_module['error'] = "Error";
$lang_module['error1'] = "Company name cannot be empty, and must contain at least 3 characters.";
$lang_module['error2'] = "You must select a business.";
$lang_module['error3'] = "Abbreviated name is not empty.";
$lang_module['error4'] = "Company phone is not empty.";
$lang_module['error5'] = "Company email is not blank.";
$lang_module['error6'] = "Business license is not blank.";
$lang_module['error7'] = "Company representative is not empty.";
$lang_module['error8'] = "The agent address is not empty.";
$lang_module['error9'] = "Charter capital is not empty";
$lang_module['error10'] = "You must choose an address";
$lang_module['error_sonha'] = "Error: House number, lane number cannot be left blank";
$lang_module['error_gmaps'] = "Error: The address on gmaps cannot be empty";
$lang_module['error_officialname'] = "Error: The international name cannot be empty";
$lang_module['error_short'] = "Error: Short name cannot be empty";
$lang_module['error_fax'] = "Error: Corporate fax cannot be blank";
$lang_module['error_website'] = "Error: Website cannot be empty";
$lang_module['error_dateestablished'] = "Error: Company establishment date cannot be empty";
$lang_module['error_registrationtime'] = "Error: The nth registration cannot be empty";
$lang_module['error_about'] = "Error: About company cannot be empty";
$lang_module['error_taxcode'] = "Error: Business Tax ID cannot be empty";
$lang_module['error_taxdate'] = "Error: Grant date cannot be empty";
$lang_module['notice'] = "Notice";
$lang_module['notice_tb'] = "Notice";
$lang_module['listpost'] = "Businesses posted";
$lang_module['delete'] = "Delete";
$lang_module['orupload'] = "Or select";
$lang_module['or'] = 'or';
$lang_module['search'] = "Search";

// company info
$lang_module['code'] = "Code";
$lang_module['totalview'] = "Views";
$lang_module['industry1'] = "Business";
$lang_module['province'] = "Province / city";
$lang_module['district'] = "District/town";
$lang_module['ward'] = "Ward/commune";
$lang_module['address'] = "Address";
$lang_module['sonha'] = "House number, alley number";
$lang_module['gmaps'] = "Address on gmaps";
$lang_module['businesstype'] = "Form of Business";
$lang_module['logo'] = "Company logo";
$lang_module['companyname'] = "Company name";
$lang_module['officialname'] = "International name";
$lang_module['short'] = "Abbreviated name";
$lang_module['phone'] = "Company phone";
$lang_module['phone1'] = "Contact phone";
$lang_module['fax'] = "Company Fax";
$lang_module['email'] = "Company email";
$lang_module['website'] = "Company Website";
$lang_module['dateestablished'] = "Company Establishment Date";
$lang_module['registrationtime'] = "Second registration time";
$lang_module['representative'] = "Representative";
$lang_module['addressrepresentative'] = "Address of representative";
$lang_module['chartercapital'] = "Charter Capital";
$lang_module['about'] = "About, general information";
$lang_module['currentstatus'] = "Company Status";
$lang_module['browse_image'] = "Browse";
$lang_module['danghoatdong'] = "Active";
$lang_module['giaithe'] = "Dissolve";
$lang_module['tamngungkinhdoanh'] = "Suspended business";
$lang_module['nhapvondieule'] = "Enter charter Capital";
$lang_module['taxcode'] = "Business Tax ID";
$lang_module['taxdate'] = "Grant date";
$lang_module['ty'] = "billion";
$lang_module['tieu'] = "million";
$lang_module['ngin'] = "thousand";
$lang_module['viewlogo'] = "View logo";
$lang_module['notinser'] = "For some reason the operation cannot be performed. Please inform the Board of Directors! Thank you.";
$lang_module['notinserimg'] = "The uploaded image is wider than allowed. The maximum allowed width is %s pixels.";
$lang_module['vnd'] = "dong";
$lang_module['level1'] = "Level 1";
$lang_module['level2'] = "Level 2";
$lang_module['level3'] = "Level 3";
$lang_module['level4'] = "Level 4";
$lang_module['level5'] = "Level 5";
$lang_module['row'] = "Posting";
$lang_module['editrow'] = "Edit message";
$lang_module['captcha'] = "Security Code";
$lang_module['action'] = "Action";
$lang_module['edit'] = "Edit";
$lang_module['del'] = "Delete";
$lang_module['active'] = "Status";
$lang_module['unactive'] = "Unapproved";
$lang_module['actived'] = "Approved";
$lang_module['dangdoanhnghiep'] = "Post a business";
$lang_module['anhnghiepdadang'] = "Business posted";
$lang_module['chuacodoanhnghiep'] = "You don't have any businesses posted to the system yet";
$lang_module['represent_phone'] = "Contact phone number (new MSC)";
$lang_module['represent_email'] = "Contact email (new MSC)";
$lang_module['giai_doan'] = 'Statistical stage:';

$lang_module['timkiemdoanhnghiep'] = "Search for businesses";
$lang_module['chondk'] = "You must select at least 1 condition";
$lang_module['nganhnghe'] = "Industry Classification";
$lang_module['nation'] = 'Country';
$lang_module['diadiem'] = "Location";
$lang_module['tukhoa'] = "Keyword";
$lang_module['thongtintimiem'] = "Search Information";
$lang_module['doanhnghiep'] = "Form of Business";
$lang_module['luotview'] = "Views";
$lang_module['themdoanhnghiepyeuthich'] = "Add a favorite business";
$lang_module['themtatcadoanhnghiepyeuthich'] = "Add all favorite businesses";
$lang_module['danhbafavcomcuaban'] = "Your Favcom Directory";
$lang_module['chondanhmuc'] = "Select Category";
$lang_module['hoac'] = "Or create new";
$lang_module['themvao'] = "Add";
$lang_module['huybo'] = "Cancel";
$lang_module['error_danhmuc'] = "Non-blank directory name";
$lang_module['error_choiceall'] = "You can only save to 1 region";
$lang_module['error_savedn'] = "Error : This business is already saved in a directory.";
$lang_module['taomoidanhmuc'] = "Create new directory";
$lang_module['title'] = "Title";
$lang_module['doanhnghiep'] = "Form of Business";
$lang_module['themthanhcong'] = "Add successful business";
$lang_module['chuacodanhmuc'] = "You don't have any categories yet, Please add a category at \"Add favorite\"";
$lang_module['chuacodoanhnghiep'] = "You don't have any businesses yet, Please add a category at \"Add favorite\"";
$lang_module['allowmail'] = "Receive email notifications when businesses follow";
$lang_module['khongtontaidoanhnghiepnao'] = "No business information exists";
$lang_module['guiveemail'] = "Send email contacts";
$lang_module['xuatrahtml'] = "Export contacts to html";
$lang_module['xuatraexecl'] = "Export contacts to execl";
$lang_module['dienthoai'] = "Phone";
$lang_module['websitea'] = "Website";
$lang_module['search'] = "Search";
$lang_module['dakichhoatguiemail'] = "You have enabled email notifications. The system is processing your request.";
$lang_module['thaotackothuchienduoc'] = "Error: The operation could not be executed.";
$lang_module['thaotacdachapnhan'] = "The system will process your request, The sending of information may be fast or slow depending on the number of contacts or the email server.";
$lang_module['thongtindoanhnghiepcuaban'] = "Your business information";
$lang_module['businessnghiepduocquantam'] = "Your business is interested in";
$lang_module['thongtinguiemail'] = "Hello! <br/>Your business was posted on the <b>%s</b> information channel marked as a follower by another member. You can view the information. their business here %s This is an email sent automatically to you because you used the service \"Receive email notifications when a business follows\". <br/><br/>If you don't, understand anything about this email simply delete it.";
$lang_module['guithanhcong'] = "Your email address has been sent!";
$lang_module['guithatbai'] = "The system could not send email. Please try again later!";
$lang_module['dbdncb'] = "Your business directory";
$lang_module['thongtingui'] = "Dear Customer !<br/>%s would like to send you business information. Please see the attachment to this message. This is an automated email sent from %s. If you don't understand something, Simply delete it.<br/><br/><br/>---------------------------- -------------<br/><br/>Website Administrator";

$lang_module['listresult'] = "List of bid packages participated";
$lang_module['listresult_old'] = "List of bidding packages participated by contractor %s";
$lang_module['listresult1'] = "Contractor activity %s";
$lang_module['STT'] = "No.";
$lang_module['so_tbmt'] = "TBMT number";
$lang_module['goi_thau'] = "Bid Package";
$lang_module['goi_thau1'] = "tender package";
$lang_module['investor'] = "Bid Solicitor";
$lang_module['finish_time'] = "Time";
$lang_module['win_price'] = "Bid Value";
$lang_module['vai_tro'] = "Contractor role";
$lang_module['result'] = "Result";
$lang_module['result1'] = "Won";
$lang_module['result1_partner'] = "Winning the bid (joint venture)";
$lang_module['result2'] = "Lost";
$lang_module['result3'] = "No results yet";
$lang_module['vaitro1'] = "Standalone";
$lang_module['vaitro2'] = "Main consortium";
$lang_module['vaitro3'] = "Secondary Consortium";
$lang_module['number_result'] = "<b >Summarization:</b> Bidder <b>%s</b> participated in %s package, of which %s package was won, failed % s packages, %s packages with no results, %s packages canceled.";
$lang_module['number_result_prov'] = "<b >Summary:</b> Contractor <b>%s</b> participated in %s bid package %s<b>%s</b>, in that has won the bid %s package, failed bid %s package, %s package has no results, %s bid package has been canceled.";

$lang_module['view_result_client'] = 'Data table has been partially hidden, to view full information please <strong><a href="%s">Login</a></strong> or <strong><a href="%s">Register</a></strong>';
$lang_module['view_result_client1'] = 'The data table has been partially hidden, to see the full information please <strong><a href="javascript:void(0)" data-toggle="loginFormShow"> Login</a></strong> or <strong><a href="%s">Register</a></strong>';
$lang_module['view_result_user'] = 'The data table has been partially hidden, to see the full analysis please <strong><a href="%s">subscribe to the VIP3 package</a></strong >';
$lang_module['view_result_user_renew1'] = 'The data table has been partially hidden, to see the full analysis please <strong><a href="%s">renew the VIP3 package</a></strong>';
$lang_module['view_result_vip'] = 'See full list <strong><a href="%s">here</a></strong>';
$lang_module['title_resg_vip'] = 'Sign up for the VIP3 package';
$lang_module['title_renew_vip'] = 'Renew VIP3 package';
$lang_module['title_view_info'] = "To view full information";
$lang_module['title_view_ability'] = "To view contractor ability score";
$lang_module['sub_for_any_vip'] = 'You need to <strong><a href="/en/vip/?plan=1">subscribe for any software package</a></strong> to view all informations.';
$lang_module['log_in_up'] = 'You need to <strong><a href="javascript:void(0)" data-toggle="loginFormShow">Login</a></strong> or <strong><a href="%s">Sign up</a></strong> to view all informations.';
$lang_module['sub_for_any_vip_short'] = 'Subscribe software package';

$lang_module['view_result_client_table'] = '<strong><a href="%s" title="To view full information please Login or Register">Login</a></strong> or < strong><a href="%s" title="To see full information please Login or Register">Register</a></strong>';
$lang_module['view_result_user_table'] = '<strong><a href="%s" title="To see the full analysis please Subscribe to the VIP3 package">Subscribe to the VIP3 package</a></strong>' ;

$lang_module['view_result_client_view'] = 'You do not have access, to view full information please <strong><a href="%s" title="To view full information please Login or Login Register">Login</a></strong> or <strong><a href="%s" title="To see full information please Login or Register">Register</a></strong>';
$lang_module['view_result_client_view1'] = 'You do not have access, to view full information please <strong><a href="javascript:void(0)" data-toggle="loginFormShow" title="To view full information please Login or Register">Login</a></strong> or <strong><a href="%s" title="To view full information please Login or Register">Register</a></strong> to check the right to see full information. Note: To view full information you must <a href="%s"><b>Register for VIP3 package</b></a>';
$lang_module['view_result_user_view'] = 'You do not have access, to view full information please <strong><a href="%s" title="Sign up for VIP3 package">Sign up for VIP3 package></a></strong>';
$lang_module['view_result_user_renew'] = 'You do not have access, to view full information please <strong><a href="%s" title="Renew VIP3 package">Renew VIP3 package</a></strong>';
$lang_module['view_result_user_view1'] = 'The data table has been partially hidden, to view full information please <strong><a href="%s" title="Register for VIP3 package">Register VIP3 package</a></strong>';
$lang_module['view_result_user_view2'] = '<strong><a title="Login">Login</a></strong> or <strong><a title="Register">Register</a> </strong>';
$lang_module['view_result_one'] = 'Data table has been partially hidden, please <strong><a href="javascript:void(0)" data-toggle="loginFormShow" title="Data table partially hidden, please Login">Login</a></strong> or <strong><a href="%s" title="Datasheet has been partially hidden, please Please Register">Register</a> </strong> to check the right to see full information. Note: To view full information you must <a href="%s"><b>Register for VIP3 package</b></a>';
$lang_module['listinvestor'] = "List of bid solicitors in which contractors have participated in bidding";
$lang_module['listinvestor1'] = "List of Procuring entities that %s participated in bidding";
$lang_module['goithau_total'] = 'Total number of bids';
$lang_module['goithau_join'] = 'Total number of bid packages participated';
$lang_module['goithau_trung'] = 'Number of winning bids';
$lang_module['goithau_truot'] = 'Number of losing bids';
$lang_module['goithau_chuaketqua'] = 'Number of non result bids';
$lang_module['goithau_huy'] = 'Number of cancelled bids';
$lang_module['goithau_tonggiatritrung'] = "Total winning bid value";
$lang_module['goithau_tonggiatritrung_inde'] = 'Total independent winning bid value';
$lang_module['goithau_tonggiatrijoin'] = 'Total value participated';
$lang_module['goithau_winonest'] = "Ratio of winning bid price / bid value";
$lang_module['goithau_luuy'] = "Based only on packages with published estimate or bid price";
$lang_module['number_soclocitor'] = "<b >Summary:</b> Contractor %s is related to %s Bid solicitor.";
$lang_module['select_0'] = "---- Sort ----";
$lang_module['select_1'] = "A-Z Procuring Entity";
$lang_module['select_2'] = "Tenderer Z-A";
$lang_module['select_3'] = "High - low winning bid";
$lang_module['select_4'] = "Winning bid low - high";
$lang_module['select_5'] = "Successful bid result - failed";
$lang_module['select_6'] = "Failed - won bid";
$lang_module['select_7'] = "Maximum number of winning bids";
$lang_module['select_8'] = "Least number of winning bids";
$lang_module['select_9'] = "Most failed bids";
$lang_module['select_10'] = "Least number of bids missed";
$lang_module['select_11'] = "New to the top";
$lang_module['select_12'] = "Old to top";
$lang_module['select_13'] = "Maximum total number of bids";
$lang_module['select_14'] = "Least total number of bids";
$lang_module['sort'] = "Sort";
$lang_module['main_description'] = "This is a list of approved online bidding bidders on the national procurement network with full details and bidding history";
$lang_module['detail_description'] = 'Full details and bidding experience of the contractor %s';
$lang_module['page_num'] = 'Page %s';
$lang_module['detailinvestor'] = "List of bidding packages that the contractor has participated in by the Procuring Entity";
$lang_module['number_detailinvestor'] = "<b >Summary:</b> Contractor %s participated in %s of the bid package of the procuring entity: %s, in which the winning bid %s package, failed % s packages, %s packages with no results, %s packages canceled.";
$lang_module['number_detailinvestor_noname'] = "<b >Summarization:</b> There are %s of bidding packages where the bidder could not check the business registration number with the Procuring Entity: %s, in which the winning bid %s package, failed bid %s package, %s package has no results, %s bid package has been canceled.";
$lang_module['title_detail_viewinvestor'] = "Relationship between bidder <a href='%s'> %s </a> and procuring entity <a href='%s'>%s</a>";
$lang_module['title_detail_viewinvestor_noname'] = "Bidding packages in which the contractor cannot check the business registration number with the Procuring Entity <a href='%s'>%s</a>";
$lang_module['title_detail_viewrelative'] = "Relationship between contractor <a href='%s'> %s </a> and contractor <a href='%s'>%s</a>";
$lang_module['title_tab'] = "Relationship between contractor %s and procuring entity %s";
$lang_module['no_name'] = "Contractor failed to check the Business Registration Number";
$lang_module['business'] = "Contractor";
$lang_module['listbusiness'] = "List of previous competitors";
$lang_module['listbusiness1'] = "Previous competitors of %s";
$lang_module['view_relative'] = "View relations";
$lang_module['viewrelative'] = "View relations";
$lang_module['number_business'] = "<b >Summary:</b> Contractor %s competed with %s contractors in %s package, won %s package, lost %s package, %s bidding package has no results, %s bidding package has been canceled.";
$lang_module['number_business1'] = "Contractor %s has fought with %s bidders in %s package, won bid %s package, lost %s package, %s bid package has no result, %s package bid has been cancelled.";

$lang_module['number_viewrelative'] = "<b>Summary</b>: <b>%s</b> (1st bidder) fought with <b>%s</b> (contractor 2) ): %s bidding package, in which contractor 1 wins: %s times, bidder 2 wins: %s times, %s bid package has no results, %s bid package has been cancelled.";
$lang_module['note'] = "<b>Note:</b><br> Contractor 1: %s </br> Contractor 2: %s.";

$lang_module['listpartnership'] = "List of joint venture contractors";
$lang_module['listpartnership1'] = "List of contractors with which %s is joint venture";
$lang_module['number_partnership'] = "<b >Summarization:</b> Contractor %s once entered into a partnership with %s contractors in %s package, won bid %s package, lost %s package, % s bidding package has no results, %s bidding package has been canceled.";
$lang_module['number_province'] = '<b >Summary:</b> Contractor %s participated in bidding in <span>%s</span> provinces/cities, %s bidding packages were carried out nationwide, %s bidding packages were carried out outside of Vietnam.';
$lang_module['number_viewpartnership'] = "<b>Summarization:</b> Bidder %s entered into a joint venture with contractor %s: %s package, in which winning bid %s package, missed %s package, %s package has no results.";

$lang_module['total'] = "Total:";
$lang_module['business'] = "Contractor";
$lang_module['period_doan'] = 'Statistical period:';
$lang_module['select_total'] = 'Whole';
$lang_module['static_title_all'] = 'Contractor Statistics';
$lang_module['static_num_total'] = 'Top 10 most bidders';
$lang_module['static_num_result'] = 'Top 10 most successful bidders';
$lang_module['static_num_false'] = 'Top 10 most failed bidders';
$lang_module['static_num_revenue'] = 'Top 10 bidders with the most winning revenue';
$lang_module['static_num_capability'] = 'Top 10 bidders with the most ability points';
$lang_module['static_num_total_block'] = 'Top contractor who participated the most in the bid';
$lang_module['static_num_result_block'] = 'Contractor who won the most bids';
$lang_module['static_num_false_block'] = 'Contractor who lost the most bids';
$lang_module['static_type_0'] = 'bidding';
$lang_module['static_type_1'] = 'winning';
$lang_module['static_type_2'] = 'failed';
$lang_module['static_type_3'] = 'ability';
$lang_module['static_type_4'] = 'revenue';
$lang_module['trillion_billion'] = 'quadrillion';
$lang_module['thousand_billion'] = 'trillion';
$lang_module['billion'] = 'billion';
$lang_module['million'] = 'million';
$lang_module['view_more'] = 'View more';
$lang_module['hide_info'] = 'For more details please contact our staff.';
//$lang_module['hide_info'] = 'To view full information please <strong><a href="%s">register for VIP3 package</a></strong>. For detailed information about the VIP 3 package, please see <strong><a href="%s">here.</a></strong>';
$lang_module['hide_vip3'] = 'The data sheet has been hidden by this contractor, any questions please contact our staff.';
$lang_module['ngay_phe_duyet'] = 'Date of registration on the Public procurement system';
$lang_module['nop_phi'] = 'Payment Status';
$lang_module['nop_phi_type_title_1'] = 'Never paid fees.';
$lang_module['nop_phi_type_title_2'] = 'Paid but currently expired';
$lang_module['nop_phi_type_title_3'] = 'Paid';
$lang_module['thoi_han'] = 'Validation information';
$lang_module['trungthau'] = 'Winning bid';
$lang_module['boc_tin'] = 'Last Updated';
$lang_module['update_time'] = 'Last updated';
$lang_module['reupdate'] = 'Re-update';
$lang_module['update_ok'] = 'Update successful. Please wait about 10 minutes for the information to be updated.';
$lang_module['update_err'] = 'Update failed.';
$lang_module['update_err_user_last'] = 'Contractor is awaiting re-demolition. Please come back in 10 minutes.';
$lang_module['update_err_new'] = 'New contractor has been posted to the system. You can update again after 30 minutes.';
$lang_module['first_update_info'] = 'This is the first update';
$lang_module['update_info'] = '%s update';
$lang_module['login'] = 'Please <strong><a href="%s" title="To view full information please Login or Register">Login</a></strong> or <strong><a href="%s" title="To see full information please Login or Register">Register</a></strong> member to request the system to update again latest data';
$lang_module['login_popup'] = 'Please <strong><a href="javascript:void(0)" data-toggle="loginFormShow" title="To view full information please Login or Register" >Login</a></strong> or <strong><a href="%s" title="To view full information please Login or Register">Register</a></strong > members to request the system to update the latest data';

$lang_module['news_api'] = 'Related news';
$lang_module['info'] = 'System Information';
$lang_module['info_redirect_click'] = 'Click here if the wait is long';
$lang_module['info_login'] = 'You need to login or register an account to operate in this area. The system will redirect you to the registration function, login in a moment.';
$lang_module['delete_cache'] = 'Clear cache';
$lang_module['delete_cache_api'] = 'Clear API cache';
$lang_module['undefined'] = 'Undefined';

$lang_module['static'] = '<p>Data analysis results of DauThau.info software for contractors <strong>%s</strong> are as follows:</p>';
$lang_module['name_ratio_win'] = 'Contractor fail rate';
$lang_module['name_ratio_order'] = 'Failure to win against other bidders';
$lang_module['name_ratio_joint_venture'] = 'Bidding rate in joint venture with other contractors';
$lang_module['label_hit_rate'] = 'Winning bid';
$lang_module['label_slip_rate'] = 'Loss Bid';
$lang_module['label_no_result'] = 'No results yet';
$lang_module['label_cancel'] = 'Cancel bid';
$lang_module['value_desire'] = 'Expect';
$lang_module['value_real'] = 'Maximum';
$lang_module['value_inde'] = 'Minimum';
$lang_module['value_chart_title'] = 'Total winning value and total value of bid packages';
$lang_module['sum_package'] = 'Total number of bid packages participated <b>%s</b> bid packages';

$lang_module['is_soclictor'] = 'This contractor is also a Procuring Entity. The results of data analysis for the procuring entity <a href="%s"> <strong>%s</strong> </a> are as follows:
<blockquote class="list">
    <ul>
        <li>Published contractor selection plan of %s project with a total of %s of bidding packages.</li>
        <li>Invitation to bid %s package (with %s Tender Notices), conducting Prequalificatin Notice %s packages.</li>
        <li>Results of %s packages announced, canceled bids %s packages (among the packages above).</li>
        <li>There are %s packages with results without Tender Notice and Prequalification Notice.</li>
    </ul>
    <footer>DauThau.info software aggregates and analyzes information from the national bidding database</footer>
</blockquote>';
$lang_module['is_investor'] = '
<p>
    This contractor is also the investor. The results of data analysis for investors <a href="%s"> <strong>%s</strong> </a> are as follows:
</p>
<blockquote class="list">
    <ul>
        <li>As the investor of <strong>%s</strong> investment and development projects.</li>
        <li>As the investor of <strong>%s</strong> Contractor Selection Plan, including <strong>%s</strong> self-published Contractor Selection Plan, <strong>%s</strong> Contractor Selection Plan is self-published by the entity. other posted.</li>
        <li>As the investor of <strong>%s</strong> bidding package in which the package is self-inviting <strong>%s</strong>, with <strong>%s</strong> the package is owned by the unit. other bidding.</li>
    </ul>
    <footer>DauThau.info software reads from national bidding database</footer>
</blockquote>
';
$lang_module['is_construct_org'] = 'This contractor is also a construction organization with the following information:<blockquote class=list>';
$lang_module['is_construct_org_html'] = '<ul>
                                             <li>Building organization name: <a href="%s"> <strong>%s</strong> </a></li>
                                             <li>Construction capacity certificate code: %s</li>
                                             <li>Issuing Authority: %s</li>
                                             <li>Field: <br/>
                                                 %s
                                             </li>
                                         </ul>';
$lang_module['is_construct_org_footer'] = '<footer>DauThau.info software collected from National Construction Capacity Database</footer>
</blockquote>';
$lang_module['is_dauthaunet'] = 'This contractor is also listed on <a href="https://dauthau.net">DauThau.Net Private Procurement Network</a> with the business profile name of </a> <a href="%s"><b>%s</b></a>.';
$lang_module['is_dauthaunet_detail'] = '<blockquote class=list><ul><li>Project %s posted.</li><li>Posted %s contractor selection plan.</li><li>Posted bid package %s.</li></ul></blockquote>';
$lang_module['trading_address'] = 'Trading address';
$lang_module['addresstruso'] = 'Company Address';
$lang_module['name_link_detail_thau'] = 'Details of the winning project';
$lang_module['name_count_business'] = 'Business number';
$lang_module['count_goithau'] = 'Number of bidding packages';
$lang_module['count_nhathau'] = 'Number of participating contractors';
$lang_module['tongquan'] = 'Overview';
$lang_module['nhathaukhac'] = 'Bidding directly competitors';
$lang_module['nhathauliendanh'] = 'Bidding as a Joint Venture';
$lang_module['sogoithau'] = 'Number of packages';
$lang_module['thongkedauthau'] = 'Bid Statistics';
$lang_module['goithau'] = ' tender package';
$lang_module['lichsudauthau'] = 'Statistics of bidding packages that contractors have participated in';
$lang_module['title_bieudobidding'] = 'Number of participating contractors by number of bidding packages';
$lang_module['title_loaihinh'] = 'Business by type';
$lang_module['tyle'] = 'Proportion %';
$lang_module['view_log'] = 'View changelog';
$lang_module['close'] = 'Close';
$lang_module['modal_log_title'] = 'Changed information';
$lang_module['industry2'] = "Level 2 industry";
$lang_module['industry3'] = "Level 3 industry";
$lang_module['industry4'] = "Industry level 4";
$lang_module['industry5'] = "Industry level 5";
$lang_module['name_search'] = "Search Name";
$lang_module['update_data'] = "Update status";
$lang_module['info_old'] = 'Old info';
$lang_module['info_new'] = 'New Info';
$lang_module['type_info'] = 'Type info';
$lang_module['time_crawler'] = 'Time taken';
$lang_module['update_leads'] = 'Convert to leads';
$lang_module['timeupdate'] = 'Updated time';
$lang_module['time_hide'] = 'Hidden start time';
$lang_module['time_hide_end'] = 'Hidden end time';
$lang_module['block_not_join'] = 'There are %s contractor who have not participated in any bidding packages';
$lang_module['block_bidfrom'] = 'Number of packages from';
$lang_module['block_bidto'] = 'To';
$lang_module['block_bidrange'] = 'Group per column';
$lang_module['block_error_num'] = 'Please enter an integer';
$lang_module['block_error_min'] = 'Please enter minimum';
$lang_module['block_error_max'] = 'Please enter maximum';
$lang_module['block_error_ft'] = 'Please enter less than to';
$lang_module['block_accept'] = 'Apply';
$lang_module['block_default'] = 'Default';
$lang_module['block_custom'] = 'Custom';
$lang_module['block_static'] = '<ul>
<li>Total number of approved bidders on the national bidding network collected: %s.</li>
<li>- Number of contractors who have not paid fees: %s.</li>
<li>- Number of contractors who have paid fees but are currently expired: %s.</li>
<li>- Number of active contractors: %s.</li>
<li>- Number of bidders who participated in at least 1 bidding package (bidding regardless of winning or failing, being appointed...): %s.</li>
<li id="static_login"> <<LINK_LOGIN>> </li>
<li id="static_vip3">%s</li>
<li id="static_vip3_renew">%s</li>
</ul>';
$lang_module['block_static_stocks'] = '<ul>
<li>Total number of contractors who are listed companies on all exchanges: <strong>%s</strong>. Including:</li>
<li>- Number of contractors who are listed companies on HNX: <strong>%s</strong>.</li>
<li>- Number of contractors who are listed companies on HOSE: <strong>%s</strong>.</li>
<li>- Number of contractors who are listed companies on UPCOM: <strong>%s</strong>.</li>
</ul>';
$lang_module['block_static_stocks'] = '<ul>
<li>Statistical results:</li>
<li>- Total number of bid packages participated: <strong>%s</strong></li>
<li>- Total value of bid packages participated: <strong>%s</strong> VND</li>
<li>- Total winning bid value: <strong>%s</strong> VND</li>
<li>- Total independent winning bid value: <strong>%s</strong> VND</li>
</ul>';
$lang_module['block_static_login'] = 'Please <a href="javascript:void(0)" data-toggle="loginFormShow"><strong>login</strong></a> or <a href=" %s"><strong>register</strong></a> (if you don\'t have an account) to be able to look up this list.';
$lang_module['block_static_vip3'] = 'You need <a href="%s"><strong>register to use the VIP3 package</strong></a> to view this list.';
$lang_module['block_static_vip3_renew'] = 'You need <a href="%s"><strong>renew your VIP3 package</strong></a> to view this list.';
$lang_module['desc_timeline'] = '<b><a href="%s">%s</a> %s</b> for the <b><a href="%s">%s</a></b> package, which was invited for bidding by the <b><a href="%s">%s</a></b> through an <b>%s</b> as an independent entity.';
$lang_module['desc_timeline_partnership'] = '<b><a href="%s">%s</a> %s</b> for the <b><a href="%s">%s</a></b> package, which was invited for bidding by the <b><a href="%s">%s</a></b> through an <b>%s</b> as %s of <b> %s</b>.';
$lang_module['desc_timeline_count_ld'] = '<b>%s</b> other contractors also participated.';
$lang_module['ldc'] = 'master consortium';
$lang_module['ldp'] = 'sub-joint';
$lang_module['tg'] = 'join ';
$lang_module['st'] = 'pre-qualification';
$lang_module['tt'] = 'Won the bidding';
$lang_module['trt'] = ' lost the bidding ';
$lang_module['tv'] = 'member';
$lang_module['tht'] = ' in the form ';

$lang_module['kq'] = '- Result: <b><a href="%s">%s</a></b>';
$lang_module['title_timeline'] = 'Contractor Activity';
$lang_module['alert_timeline'] = 'All contractor activity data %s displayed';

$lang_module['timeline_result_client'] = 'Contractor activities have been partially hidden, to view full information please <strong><a href="%s">Login</a></strong> or <strong><a href="%s">Register</a></strong>';
$lang_module['timeline_result_client1'] = 'Contractor activities have been partially hidden, for full information please <strong><a href="javascript:void(0)" data-toggle=" loginFormShow" title="To see full information please Login or Register">Login</a></strong> or <strong><a href="%s">Register</a></strong>';

$lang_module['confirm_not_user1'] = 'To view information please <strong><a href="javascript:void(0)" data-toggle="loginFormShow">Login</a></strong> or <strong><a href="%s">Register</a></strong>&nbsp;if you don\'t have an account.
Signing up is simple and completely free.';

$lang_module['timeline_view_result_user'] = 'Contractor activities have been partially hidden, to view full information please <strong><a href="%s">subscribe to VIP3 package</a> </strong>';
$lang_module['result_cancel'] = "Bid canceled";
$lang_module['result4'] = "Tender opening completed, no contractor selected";
$lang_module['result0_pq'] = "Prequalification results available";
$lang_module['result1_pq'] = "Failed prequalification";
$lang_module['result2_pq'] = "Pass the preliminaries";
$lang_module['number_result_i'] = "<b >Sum:</b> Contractor <b>%s</b> has %s active, where: <br> - Has %s related activity to <b>participating in the bidding package</b>: %s of successful bids, %s of failed bids, %s of unresponsive activities, %s of bids canceled.<br> - Yes %s activities related to participating in <b>pre-qualification of bidding packages</b>: %s activities that have passed pre-qualification, %s activities that have failed pre-qualification, %s activities that do not have pre-qualification results.";

$lang_module['notif_view_dropdown_not_user'] = 'You need <strong><a href="javascript:void(0)" data-toggle="loginFormShow">Login</a></strong> or <strong><a href="%s">Register</a></strong> to be able to see the chart of the value of contractor appointment packages and packages with IMP but no TBMT';
$lang_module['notif_view_dropdown_not_vip'] = 'You need <strong><a href="%s">subscribe to the VIP3 package</a></strong> to be able to view the value chart of direct contracting packages and other packages of contractor selection plan with no tender notices';
$lang_module['notif_view_dropdown_vip_expired'] = 'You need <a href="%s"><strong>to renew your VIP3 package</strong></a> to be able to see the value chart of the bid packages and packages with IMP but no TBMT';

$lang_module['refresh'] = "Refresh";
$lang_module['nganhnghe_2'] = "Contractors with occupation %s";
$lang_module['is_fail'] = "(This contractor data has been labeled as incorrect by DauThau.info!)";
$lang_module['mark_not_used'] = "Mark profile unused";
$lang_module['tranfer_contract'] = "Transfer data to another contractor";
$lang_module['listduplicate'] = "List of contractors with the same information";
$lang_module['duplicate_data'] = "Duplicate data";
$lang_module['duplicate_name'] = "Same name";
$lang_module['duplicate_phone'] = "Dual phone number";
$lang_module['duplicate_code'] = "Double DKKD";
$lang_module['marked'] = "Wrong Profile Marked";
$lang_module['mark_status'] = "Label";
$lang_module['not_exist_dauthaunet'] = "DauThau.info software could not find this business information on <a href=\"https://dangkytinhdoanh.gov.vn/\"> National portal for registration business registration </a>";
$lang_module['marked_not_used'] = "This contractor data has been labeled as incorrect by DauThau.info!";
$lang_module['no_data'] = "No data found related to '<strong>%s</strong>' in the database %s named bidders on the national bidding network on request the request you've been looking for.";

$lang_module['industry_registration'] = 'Industry by business registration';
$lang_module['industry_msc'] = 'Industry Classification';
$lang_module['main_industry'] = 'Main industry';
$lang_module['industry_icb'] = 'Industry by ICB code';
$lang_module['undo'] = 'Restore Profile';
$lang_module['note_wrong_page'] = 'Where did you get this link? I am the bearer, can\'t handle your request!';
$lang_module['note_max_searchpage'] = 'The number of search results is exceeding the software\'s display limit of 100 pages. Please use the search engine to narrow your search or return to the previous page.';
$lang_module['note_max_searchpage_not_user'] = 'The number of search results exceeds the software display limit of 100 pages. Please use the search tool to narrow down the search scope or <a href="javascript:void(0)" data-toggle="loginFormShow"><strong>log in</strong></a> or <a href="%s"><strong>register</strong></a> (if you do not have an account) to use the export to excel function to view the full list of search results.';
$lang_module['note_max_searchpage_x2'] = 'The number of search results is exceeding the software display limit of 100 pages. Please see the results on pages 1-100. You can also use the search tool to narrow your search or use the export data to excel function above to see the full list of search results.';
$lang_module['redirect_to_search_form'] = 'Redirect to contractor search page';
$lang_module['back'] = 'Back';
$lang_module['verify_profile_to_receive_notifications'] = 'Please <a href="%s" title="verify your account on DauThau.Net">verify your account on DauThau.Net</a> so that the system automatically sends mail remind you when you forget to renew.';
$lang_module['taxdate_new'] = 'Tax ID issue date';
$lang_module['tax_nation'] = 'Tax ID issue country';
$lang_module['orgcode'] = 'Business Registration ID';
$lang_module['rep_name'] = 'Legal representative';
$lang_module['rep_position'] = 'Position';
$lang_module['fee_payment_information'] = 'According to <a href="https://dauthau.asia/van-ban-dau-thau/detail/Thong-tu-06-2021-TT-BKHDT-123/" title= "Circular 06/2021">Circular 06/2021</a>, from March 3, 2022, investors and contractors will be stopped from trading if they do not pay fees for posting information, selecting contractors, investors on the national bidding network system.';

$lang_module['stocks_list'] = 'List of bidders are listed companies';
$lang_module['stocks_code'] = 'CK Token';
$lang_module['stocks_exchange'] = 'Exchange';
$lang_module['at_exchange'] = 'at the floor';
$lang_module['at'] = 'at %s';
$lang_module['on_all_nation'] = 'Nationwide';
$lang_module['contractor_name'] = 'Contractor name';
$lang_module['list_date'] = 'List date';
$lang_module['khong_co_ket_qua'] = 'The requested results were not found, please change the search parameters.';
$lang_module['is_stock'] = 'This contractor is a listed company with the following information:';

$lang_module['all_packages'] = 'All packages';
$lang_module['pg_appointment_contractors'] = 'Bidders only';
$lang_module['pg_with_kqlcnt_without_tbmt'] = 'Only packages with Contractor Selection Result and without Tender Notice or Contractor Selection Plan';
$lang_module['pg_with_kqlcnt_without_tbmt_1'] = 'with Contractor Selection Result and without Tender Notice or Contractor Selection Plan';

$lang_module['decisionno'] = 'Number of sanctioning decisions';
$lang_module['vp_name'] = 'Name of the offending organization or individual';
$lang_module['vp_name_violate'] = 'Name of the agency/unit that decided to handle the violation';
$lang_module['vp_issued_date'] = 'Issuance date';
$lang_module['listvipham'] = 'List of violations of the contractor\'s regulations on bidding';
$lang_module['number_listvipham'] = "<strong>Summary: </strong>DauThau.info found %s sanctioning decision related to contractor <strong>%s</strong>";
$lang_module['view_listvipham'] = 'Full view <strong><a href="%s">here</a></strong>';
$lang_module['vp_pentype'] = 'Sanction';
$lang_module['viewviolators'] = "Sanction decision regarding contractor %s";

$lang_module['static_detail'] = '<blockquote class=list>
                                         <ul>
                                             <li><a href="#number_result">Participated in %s package, in which %s packages won, %s packages lost, %s pending pesult, %s package was cancelled.</a> </li>
<li><a href="#number_result">Total winning value: %s VND (In which %s VND are packages of direct contracting; %s VND are packages of contract selection plan without tender notice)</a></li>
                                             <li><a href="#number_result">Lowest offer price rate when joining bid: %s </a>%s<a href="#number_result"> (Based on packages with published estimating price or bid package value)</a></li>
<li><a href="#number_result">Average ratio of winning bid to estimated price: %s (Based only on packages with the published estimate price or bid package value)</a></li>
                                             <li><a href="#number_result">Average ratio of winning bid to estimated price: %s (Based only on packages with published estimate or bid price)</a></li>
                                             <li>Cities participated in the bid: %s</li>
                                             <li><a href="#number_solocitor">Relation to %s soliciting party</a></li>
                                             <li><a href="#number_business">Have fought with %s bidders in %s package, won %s package, lost %s package, %s no result, %s package has been canceled. </a></li>
                                             <li><a href="#number_partnership">Already entered into a joint venture with %s contractors in %s package, won bid %s package, lost %s package, %s no result, %s package was rejected cancel.</a></li>
                                             <li><a href="#number_qdxp">Received %s sanction decision for violation of bidding regulations.</a></li>
                                         </ul>
                                         <footer>DauThau.info software synthesizes and analyzes information from the national bidding database</footer>
                                     </blockquote>';
$lang_module['static_no_link'] = '<blockquote class=list>
                         <ul>
                             <li>Participated in %s package, in which %s package won, %s failed, %s failed, %s package was canceled.</li>
                             <3
                             <li>Lowest bid rate when participating: %s %s(Only based on packages with published estimate or bid price)</li>
                             <li>Average ratio of winning bid to estimated price: %s (Only based on packages with announced estimate or bid price)</li>
                             <li>Cities participated in the bid: %s</li>
                             <li>Relationship with %s soliciting party</li>
                             <li>Have fought with %s bidders in %s packages, won %s packages, lost %s packages, %s still have no results, %s packages have been canceled.</li>
                             <li>Already entered into a joint venture with %s contractors in %s packages, won %s packages, lost %s packages, %s still had no results, %s packages were canceled.</li>
                             <li>Received %s of sanctioning decision for violation of bidding regulations.</li>
                         </ul>
                         <footer>DauThau.info software synthesizes and analyzes information from the national bidding database</footer>
                     </blockquote>';
$lang_module['static_no_bid_detail'] = '<blockquote class=list>
                             <ul>
                                 <li><a href="#number_result">Participated in 0 bidding packages.</a></li>
                             </ul>
                             <footer>DauThau.info software synthesizes and analyzes information from the national bidding database</footer>
                         </blockquote>';
$lang_module['static_no_bid'] = '<blockquote class=list>
                         <ul>
                             <li>Participated in 0 bidding packages.</li>
                         </ul>
                         <footer>DauThau.info software synthesizes and analyzes information from the national bidding database</footer>
                     </blockquote>';
$lang_module['min_duthau'] = '<a href="%s">in the bidding package <strong> here</strong></a>';

$lang_module['array_lvkd_1'] = 'Goods';
$lang_module['array_lvkd_2'] = 'Civil Works';
$lang_module['array_lvkd_3'] = 'Consulting';
$lang_module['array_lvkd_4'] = 'Non-consulting';
$lang_module['array_lvkd_5'] = 'Uncategoried';

$lang_module['array_fee_0'] = 'All';
$lang_module['array_fee_1'] = 'Contractor Never paid fees';
$lang_module['array_fee_2'] = 'Bill paid but currently expired';
$lang_module['array_fee_3'] = 'Active Contractor';

$lang_module['array_type_lc_11'] = 'shortened competitive offer';
$lang_module['array_type_lc_12'] = 'competitive offer';
$lang_module['array_type_lc_13'] = 'assign shorthand';
$lang_module['array_type_lc_14'] = 'assign bid';
$lang_module['array_type_lc_16'] = 'restricted bidding';
$lang_module['array_type_lc_17'] = 'widely bid';
$lang_module['array_type_lc_19'] = 'direct shopping';
$lang_module['array_type_lc_21'] = 'invitation to express interest';
$lang_module['array_type_lc_22'] = 'in special cases';
$lang_module['array_type_lc_23'] = 'do it yourself';
$lang_module['array_type_lc_24'] = 'selection based on ability';
$lang_module['array_type_lc_25'] = 'selection on the basis of quality and cost';
$lang_module['array_type_lc_28'] = 'select individual consultants';
$lang_module['year'] = 'Year';
$lang_module['share'] = 'Share';
$lang_module['fb_share'] = 'Share to facebook';
$lang_module['link_copy_successfully'] = 'Copy link successful';
$lang_module['tweet'] = 'Tweet';
$lang_module['copy_link'] = 'Copy link';
$lang_module['array_type_violate_pt'] = 'Administrative processing';
$lang_module['array_type_violate_cd'] = 'Terminate';
$lang_module['array_type_violate_cc'] = 'Warning';
$lang_module['array_type_violate_ct'] = 'Bidding prohibited';
$lang_module['array_type_violate_other'] = 'Other';
$lang_module['array_type_violate_cppp'] = 'Participation in PPP investment is prohibited';
$lang_module['array_type_violate_cdmsg'] = '182714000 Termination';
$lang_module['laboratory_info'] = 'Laboratory Information';
$lang_module['is_laboratory'] = 'The contractor has a specialized construction laboratory with the following information:<blockquote class=list>';
$lang_module['is_laboratory_html'] = '<ul>
                                             <li>Lab name: <a href="%s"> <strong>%s</strong> </a></li>
                                             <li>Laboratory code: %s</li>
                                             <li>Certifications: <br/>
                                                 %s
                                             </li>
                                         </ul>';
$lang_module['is_laboratory_footer'] = '<footer>DauThau.info software collected from the Ministry of Construction Portal\'s database</footer>
                                         </blockquote>';
$lang_module['crawl_request_history'] = 'Update request history';
$lang_module['request_time'] = 'Request time';
$lang_module['username'] = 'Account';
$lang_module['title_note_business'] = "Contractor with DKKD number or identifier: ";
$lang_module['list_company_at'] = 'List of companies, businesses, contractors at';
$lang_module['list_info_company_at'] = 'Find out the information of organizations for better bidding and below is a list of companies, businesses, contractors at %s updated by DauThau.info';

$lang_module['point'] = 'Point';
$lang_module['alert_reg_x2_to_decrese_point'] = 'Please register and pay for the package <a href="%s" target="_blank">X2</a> to get 50%% off the number of points when downloading contractor information';
$lang_module['unknown_error'] = 'An unknown error. Please try again';
$lang_module['error_point_out'] = 'You do not have enough points to download data (%d/%d points), please <a href="%s" target="_blank">buy more points</a> before downloading the file';
$lang_module['confirm_download_business_data'] = 'Are you sure you want to use %s/%s points to download %s contractor data by search results?';
$lang_module['ok'] = 'Confirm';
$lang_module['cancel'] = 'Cancel';
$lang_module['request_add_ok'] = 'You have successfully submitted your export request. The system will process it momentarily and send you an email notification.';
$lang_module['download_excel'] = 'Download Excel';
$lang_module['title_btn_download_excel'] = 'Download the Excel file of contractor data according to the search results';
$lang_module['wrong_checksess'] = 'Error: Wrong security code';
$lang_module['decrese_point_mess'] = 'Download business data %b enterprise/ %d points';
$lang_module['processing'] = 'Processing';
$lang_module['this_month'] = 'This month';
$lang_module['last_month'] = 'Last month';
$lang_module['last_3_months'] = 'The last 3 months';
$lang_module['last_6_months'] = 'The last 6 months';
$lang_module['custom_range'] = 'Other';
$lang_module['this_year'] = 'Current year';
$lang_module['last_all_days'] = 'The whole history';
$lang_module['last_7_days'] = 'The last 7 days';
$lang_module['last_14_days'] = 'The last 14 days';
$lang_module['last_30_days'] = 'The last 30 days';
$lang_module['none'] = 'Cancel';
$lang_module['addtime_export'] = 'Request submission date';
$lang_module['content'] = 'Content';
$lang_module['status'] = 'Status';
$lang_module['status0'] = 'Pending';
$lang_module['status1'] = 'Processing';
$lang_module['status2'] = 'Processed';
$lang_module['status3'] = 'Downloaded the file ';
$lang_module['status4'] = 'Deleted file';
$lang_module['num_row'] = 'Number of results';
$lang_module['link_file'] = 'File download';
$lang_module['redirect_to_manager_request'] = 'Go to the file download request management page';
$lang_module['continue_browsing'] = 'Continue browsing';
$lang_module['array_request'] = 'List of requests to export Contractor data';
$lang_module['title_filter'] = 'Data filter type: ';
$lang_module['title_theo_tukhoa'] = '<br/>- By keyword: %s';
$lang_module['title_theo_industry4'] = '<br/>- By Industry Level 4: %s';
$lang_module['title_theo_industry3'] = '<br/>- By Industry Level 3: %s';
$lang_module['title_theo_industry2'] = '<br/>- By Industry Level 2: %s';
$lang_module['title_theo_industry1'] = '<br/>- By Industry Level 1: %s';
$lang_module['title_theo_province'] = '<br/>- By Province/City: %s';
$lang_module['title_theo_district'] = '<br/>- By County/District: %s';
$lang_module['title_theo_ward'] = '<br/>- By Ward/Commune: %s';
$lang_module['title_theo_businesstype'] = '<br/>- By Type of Business: %s';
$lang_module['title_theo_lvkd'] = '<br/>- By Business Line: %s';
$lang_module['title_theo_fee'] = '<br/>- By Payment Status: %s';
$lang_module['title_theo_ngay_phe_duyet'] = '<br/>- By date of approval: %s';
$lang_module['title_theo_bidding_start_time'] = '<br/>- By Time of Bidding: %s';
$lang_module['title_den'] = ' to ';
$lang_module['bidding_start_time'] = 'Time of Bidding';
$lang_module['bidding_start_time_search'] = 'Statistics by a specific time period';
$lang_module['filter_ngay_phe_duyet'] = 'Approval date';
$lang_module['bid_time_tooltip'] = 'Note: Criteria of time to bid does not affect the search results, but only the information of the contractor in excel.';
$lang_module['error_advance_export'] = 'Error: Filter condition "Time to Bid" is less than "Approval Date". Please change the filter condition (the "Time to Bid" filter condition must be between the "Approval Date" and the current time)';
$lang_module['export'] = "Export the list of contractors";
$lang_module['login_require'] = 'You need to login to use this function.';
$lang_module['customs_info'] = 'You are using package X2, expiry date to %s.';
$lang_module['x2_adv_static_bibding'] = 'Total number of bidding packages participating in each year of each contractor (Number of winning packages, number of failed bids, number of unresponsive bidding packages, number of canceled packages)';
$lang_module['x2_adv_static_result'] = 'Total winning value in each year of each contractor';
$lang_module['x2_adv_static_bidsecurity'] = 'Total value arising from the bid guarantee of each contractor in each year';
$lang_module['x2_adv'] = 'Advanced features in use.';
$lang_module['note_search_x2'] = '(Applicable only to X2 package\'s advanced features)';
$lang_module['num_worker'] = 'The number of workers';
$lang_module['no_change'] = '-';
$lang_module['type_lc_1'] = 'Domestic';
$lang_module['type_lc_2'] = 'International';
$lang_module['type_lc_3'] = 'No prequalification';
$lang_module['type_lc_4'] = 'Pre-qualification';
$lang_module['type_lc_5'] = 'Over the air';
$lang_module['type_lc_6'] = 'Over the air';
$lang_module['type_lc_7'] = 'Single Stage Single Envelope';
$lang_module['type_lc_8'] = 'Single Stage Two Envelopes';
$lang_module['type_lc_9'] = 'Two Stages Single Envelope';
$lang_module['type_lc_10'] = 'Two Stages Two Envelopes';
$lang_module['type_lc_11'] = 'Shortened competitive offer';
$lang_module['type_lc_12'] = 'Competitive Offer';
$lang_module['type_lc_13'] = 'Shortened Direct Contracting';
$lang_module['type_lc_14'] = 'Specify reference';
$lang_module['type_lc_15'] = 'Price negotiation';
$lang_module['type_lc_16'] = 'Restricted Match';
$lang_module['type_lc_17'] = 'Open bidding';
$lang_module['type_lc_18'] = 'Fixed budget selection';
$lang_module['type_lc_19'] = 'Direct shopping';
$lang_module['type_lc_20'] = 'Join the community';
$lang_module['type_lc_21'] = 'Invitation to Express Interest';
$lang_module['type_lc_22'] = 'In special cases';
$lang_module['type_lc_23'] = 'Do it yourself';
$lang_module['type_lc_24'] = 'Quality-based selection';
$lang_module['type_lc_25'] = 'Selection on the basis of quality and cost';
$lang_module['type_lc_26'] = 'Quality-based selection';
$lang_module['type_lc_27'] = 'Single Source Selection';
$lang_module['type_lc_28'] = 'Selection of individual consultants';
$lang_module['type_lc_29'] = 'Low cost consultant selection';
$lang_module['type_lc_30'] = 'Special case selection of contractors';
$lang_module['type_lc_31'] = 'Personal consultation';
$lang_module['type_lc_32'] = 'Competitive negotiation';
$lang_module['type_lc_33'] = 'Quality and Cost Based Selection (QCBS)';
$lang_module['type_lc_34'] = 'Quality-Based Consultant Selection (QBS)';
$lang_module['type_lc_35'] = 'Fixed budget selection (FBS)';
$lang_module['type_lc_36'] = 'Last cost consultant selection (LCS)';
$lang_module['type_lc_37'] = 'Capability-Based Selection (CQS)';
$lang_module['type_lc_38'] = 'Single Source Selection (SSS)';
$lang_module['type_lc_39'] = 'Selection of consultants in loans to institutions or financial intermediaries';
$lang_module['type_lc_40'] = 'Selection of consultants in Bank-guaranteed loans';
$lang_module['type_lc_41'] = 'Selection of certain types of consultants';
$lang_module['danopphi'] = 'Paid';
$lang_module['chuanopphi'] = 'Not yet paid';
$lang_module['hethan'] = 'Expires';
$lang_module['nhathau'] = 'contractor';
$lang_module['tinhtrang'] = 'Status';
$lang_module['tongso'] = 'Total number of approved contractors';
$lang_module['title__static'] = 'Statistics of contractors on the National Procurement Network';
$lang_module['title__numnt_one'] = 'Participated in at least 1 bid';
$lang_module['title__ntctg'] = 'No bidding package';
$lang_module['view_result_user_view1_renew'] = 'The data sheet has been partially hidden, to see the full information please <strong><a href="%s" title="Renew VIP3 package">renew the VIP3 package</a></strong>';
$lang_module['title__overlay_lock'] = 'The data table has been partially hidden, please Login';
$lang_module['title_login'] = 'Login';
$lang_module['tinhtrangnp'] = 'Payment Status';
$lang_module['thamgiathau'] = 'Join bid';
$lang_module['nodata'] = 'No Data';
$lang_module['placeholder_search_tnt'] = 'Please enter the name of the contractor to search';
$lang_module['ttnganhhang'] = 'Category';
$lang_module['chontinhthanh'] = 'Select city';
$lang_module['tooltip_multi_keyword'] = 'Keywords can be contractor name (English/Vietnamese), tax code, identification code, industry information, address, phone number, fax, website. You can use up to %s keywords, separated by commas (,).';
$lang_module['error_api_warning'] = 'There was an error while aggregating contractor data. Please <a href="#" onClick="window.location.reload();">reload</a> the page. If the problem persists, please <a href="https://support.dauthau.net/en/supportticket/add/">contact us</a>.';
$lang_module['title_btn_download_pdf'] = 'Download contractor data PDF file';
$lang_module['download_PDF'] = 'Download PDF file';
$lang_module['exportpdf_request_add_ok'] = 'You have successfully sent the request to export the PDF file. The system will process it momentarily and send you a notification via email.';
$lang_module['array_request_exportpdf'] = 'History of requesting PDF reports on business performance in the Public Procurement market';
$lang_module['array_exist_request_exportpdf'] = 'These are the PDF reports "Enterprise performance results in the Public Procurement market" that you asked DauThau.info to prepare. This report is intended to assist users in making investment decisions and financial strategies or evaluate the feasibility of investing in those projects/companies… see introduction <a href="https://dauthau.asia/news/tin-tuc/gioi-thieu-chi-tiet-bao-cao-phan-tich-lich-su-dau-thau-cua-nha-thau-ben-moi-thau-cua-goi-t100-kem-file-pdf-mau-1160.html">here</a>.';
$lang_module['array_empty_request_exportpdf'] = 'You have not exported the PDF report "Enterprise performance in the Public Procurement market" any time. This report is intended to assist users in making investment decisions and financial strategies or evaluating the feasibility of investing in specific projects/companies… see details <a href="https://dauthau.asia/news/tin-tuc/gioi-thieu-chi-tiet-bao-cao-phan-tich-lich-su-dau-thau-cua-nha-thau-ben-moi-thau-cua-goi-t100-kem-file-pdf-mau-1160.html">here</a>.';
$lang_module['alert_reg_t100'] = 'Buy now <a href="%s" target="_blank">T100 package</a> to download 100 reports analyzing the contractor\'s bidding history from 2010 to present !';
$lang_module['alert_reg_t100_vip3'] = 'Buy now <a href="%s" target="_blank">T100 package</a> (at half the price of regular customers) to download 100 analytical reports contractor\'s bidding history from 2010 to present!';
$lang_module['confirm_download_pdf_point'] = 'Are you sure you want to use %s/%s points to download the contractor\'s report PDF file?';
$lang_module['error_point_out_pdf'] = 'You do not have enough points to purchase this feature (%s points), please <a href="%s" target="_blank">buy more points</a> before downloading file';
$lang_module['confirm_download_pdf'] = 'Are you sure you want to download the contractor\'s report PDF?';
$lang_module['confirm_download_pdf_point_1'] = 'Are you sure you want to use %s/%s points to download the tenderer\'s report PDF file?';
$lang_module['confirm_download_pdf_1'] = 'Are you sure you want to download the PDF of the tenderer\'s report?';
$lang_module['decrese_point_mess_pdf'] = 'Download PDF file of contractor\'s report losing %d points';
$lang_module['total_download_pdf'] = 'Number of downloads of package T100: <strong>%s</strong>';
$lang_module['total_no_download_pdf'] = 'Number of downloads of package T100: <strong>%s</strong>';
$lang_module['expired_download'] = 'T100 package expiry date: <strong>%s</strong>';
$lang_module['log_in_up_pdf'] = 'You need <strong><a href="javascript:void(0)" data-toggle="loginFormShow">Login</a></strong> or <strong><a href="%s">Register</a></strong> to determine file download permissions of <a href="%s">package T100</a>.';
$lang_module['business_contractor'] = 'Contractor/Procurator';
$lang_module['status_1'] = 'File export error';
$lang_module['decrese_point_mess_pdf_solicitor'] = 'Download PDF file of the tenderer\'s report losing %d points';
$lang_module['information_pdf'] = '<p><strong>PDF REPORT "RESULTS OF BUSINESS PERFORMANCE IN THE PUBLIC PROCUREMENT MARKET" <span class="icon_new"></span></strong></p>
<p>DauThau.info has just launched the PDF report export feature "Enterprise performance results in the Public Procurement market" to support users in making investment decisions and financial strategies or assessments. evaluate the feasibility of investing in specific projects/companies... see details <a href="https://dauthau.asia/news/tin-tuc/gioi-thieu-chi-tiet-bao-cao-phan-tich-lich-su-dau-thau-cua-nha-thau-ben-moi-thau-cua-goi-t100-kem-file-pdf-mau-1160.html">here</a>.</p>';
$lang_module['comp_charter_file_name'] = 'Adjust business operations';
$lang_module['org_chart_file_name'] = 'Organizational chart';
$lang_module['business_file_name'] = 'Business registration certificate';
$lang_module['org_scale'] = 'Enterprise scale';
$lang_module['difference_price_title'] = 'Bid Package Price Difference and Winning Bid Price';
$lang_module['grouping'] = 'Grouping of packages by percentage difference between winning bid price and package price';
$lang_module['quantity'] = 'Number of packages in each group';
$lang_module['percentage'] = 'Percentage ratio of total packages';
$lang_module['difference_price_1'] = 'Lower by 20% or more';
$lang_module['difference_price_2'] = 'Lower by 10% to 20%';
$lang_module['difference_price_3'] = 'Lower by 5% to 10%';
$lang_module['difference_price_4'] = 'Lower by 1% to 5%';
$lang_module['difference_price_5'] = 'Lower by 0% to 1%';
$lang_module['difference_price_6'] = '0%';
$lang_module['difference_price_7'] = 'Higher by 0% to 1%';
$lang_module['difference_price_8'] = 'Higher by 1% to 5%';
$lang_module['difference_price_9'] = 'Higher by 5% to 10%';
$lang_module['difference_price_10'] = 'Higher by 10% or more';
$lang_module['unknown'] = 'Unknown';
$lang_module['difference_price_total'] = 'Total';
$lang_module['gia_goi_thau'] = 'Tender package price';
$lang_module['gia_trung_thau'] = 'Winning bid price';
$lang_module['cong_thuc_tinh_chenh_lech'] = 'Formula for calculating % difference:';
$lang_module['search_advance'] = 'Click for advanced search';
$lang_module['search_simple'] = 'Back to basic search';
$lang_module['reset'] = 'Reset';
$lang_module['explanation'] = 'Explanation:';
$lang_module['difference_note_1'] = 'Negative ratios indicate winning prices higher than bidding prices.';
$lang_module['difference_note_2'] = 'Positive ratios indicate winning prices lower than bidding prices.';
$lang_module['difference_note_3'] = '(0,1]% means From 0 to 1%, (1,5]% means From over 1 to 5%, excluding the case of 1. Apply similar meanings to other ratio groups.';
$lang_module['view_chart_full'] = 'Show all';
$lang_module['view_chart_7_years'] = 'Show the most recent 7 years';
$lang_module['unclassified'] = 'Unclassified';
$lang_module['search_key_title'] = 'Enter search keywords';

$lang_module['link_file_fast'] = 'Quick download';
$lang_module['buy_TO'] = 'Buy package T0';
$lang_module['show_info_down_point_2227'] = 'To activate this feature, click on the <strong>Quick Download</strong> button above. You will have <strong>%d</strong> points deducted from your account for <strong>%d Mb</strong> which is the total size of the files.';
$lang_module['show_info_down_t0_2227'] = 'In addition, you can <b>purchase package T0</b> which is a service package that allows unlimited fast download of bidding documents on any browser.';
$lang_module['down_info_not_point_2227'] = 'Downloading files directly on the new Public Procurement System requires a computer using the Windows operating system and needs to install Client Agent software (Linux and MacOS cannot download Client software yet). ). Therefore, to be able to download files on smartphones, tablets or computers using operating systems other than Windows, you need to use our DauThau.info.<br>Our system will help you download files faster, on any device without installing Client Agent.';

$lang_module['origlink'] = 'Original link';
$lang_module['viplink'] = 'Download';
$lang_module['att_file'] = 'Attached files';
$lang_module['message_point_download'] = 'Quick download %s of Contractor: %s';
$lang_module['message_point_down_err'] = 'Error: The system cannot save profile purchases.';
$lang_module['message_point_down_suss'] = 'Successfully enabled the fast file download feature. After the browser reloads this page, you can click on the button <p class="btn btn-primary btn-xs"><em class="fa fa-snowflake-o"> </em> Download</p> to download the file.';
$lang_module['error_by_quick_plan'] = 'An error occurred while downloading the profile';
$lang_module['cf_buy_download'] = 'To get the download link, %s points will be deducted from your account. Please confirm this operation';
$lang_module['wait_download'] = 'Downloading';
$lang_module['show_info_down_not_point'] = 'Your account does not have enough points for the system to support Fast Download. You need to have at least %d points in your account. Please click on the <strong>Buy Points</strong> button below to buy more points and then return to the page again.';
$lang_module['buy_points'] = 'Buy points';
$lang_module['expand'] = 'Expand';
$lang_module['collapse'] = 'Collapse';
$lang_module['nationwide'] = 'Nationwide';
$lang_module['vn_out_territory'] = 'Outside the territory of Vietnam';
$lang_module['list_contractor_province'] = 'List of provinces/cities participating in bidding';
$lang_module['title_chart_contractor_province'] = 'Statistics of bidding data of %s according to major cities of Vietnam';
$lang_module['list_contractor_province_1'] = 'List of provinces/cities in which %s participates in bidding';

$lang_module['ability_point'] = 'Contractor ability score';
$lang_module['ability_point_short'] = 'Ability Point';
$lang_module['ability_rank'] = 'Contractor rating';
$lang_module['ability_point_description'] = 'The above scores and rankings were analyzed by <b>DauThau.info</b> Software based on bidding history and business size. However, this scoring is only relative and cannot accurately determine which bidder\'s capacity is stronger or weaker or determine the likelihood of winning the bid. When comparing, you should carefully consider many other factors such as the specific content of the bidding package and the field and industry of each business.';
$lang_module['use_point'] = 'Use point';
$lang_module['some_error'] = 'An error occurred, please try again later.';
$lang_module['confirm_view_ability'] = 'Do you want to use %s/%s points to view this contractor\'s ability score?';
$lang_module['view_ability_message'] = 'View ability score of contractor %s';
$lang_module['evaluation_criteria'] = 'Evaluation criteria';
$lang_module['point_evaluation'] = 'Evaluation score';
$lang_module['experience_ability'] = 'Experience capacity';
$lang_module['finace_ability'] = 'Financial capacity';
$lang_module['compete_ability'] = 'Competitiveness';
$lang_module['enterprise_scale'] = 'Enterprise scale';
$lang_module['violation_history'] = 'Violation history';
$lang_module['see_more_ability'] = 'To better understand the scoring criteria, see the following article: <a href="https://dauthau.asia/en/news/general-information/the-bidder-capability-assessment-indicators-according-to-dauthau-info-285.html"><b>Capability Assessment indicator set</b></a>';
$lang_module['view_ability_renew'] = 'You need to <a href="%s"><b>renew VIP3 package</b></a> or <a href="#" class="btn_point_view_ability">< b>use score</b></a> to see detailed contractor capacity score.';
$lang_module['view_ability_regvip'] = 'You need to <a href="%s"><b>subscribe to the VIP3 package</b></a> or <a href="#" class="btn_point_view_ability">< b>use score</b></a> to see detailed contractor capacity score.';
$lang_module['log_in_up_ability'] = 'You need <strong><a href="javascript:void(0)" data-toggle="loginFormShow">Login</a></strong> or <strong><a href="%s">Register</a></strong> to see contractor capacity scores.';
$lang_module['chart_bidsfield'] = 'The chart shows the difference in criteria of contractors in different fields';
$lang_module['total_num_bids_dl'] = 'Total independent bidding package';
$lang_module['total_num_win_dt'] = 'Total independent winning bid package';
$lang_module['total_num_bids_partnership'] = 'Total bidding package when joint venture';
$lang_module['total_num_win_partnership'] = 'Total winning bid package in joint venture';
$lang_module['total_price_win_dt'] = 'Total independent bid winning amount';
$lang_module['total_num_slip_dl'] = 'Total slip packages participating independently';
$lang_module['total_num_slip_ld'] = 'Total slip packages participating in joint venture';
$lang_module['total_price_win_ld'] = 'Total winning price in joint venture';
$lang_module['total_province'] = 'Total provinces and cities have participated in the bidding';
$lang_module['num_total_bids'] = 'Total number of bids';
$lang_module['num_total_trung_liendanh'] = 'Winning a bid in a joint venture';
$lang_module['num_price_trung_liendanh'] = 'Total winning bid value in joint venture';
$lang_module['total_price'] = 'Total value';
$lang_module['total_num'] = 'Total';
$lang_module['error_point_out_ability'] = 'You do not have enough points to purchase this feature (%s points), please <a href="%s" target="_blank">buy more points</a> before viewing Contractor capacity score';
$lang_module['exportpdf'] = 'Contractor exported to PDF';
$lang_module['points_exportpdf'] = 'Number of points needed to export PDF';
$lang_module['company_stock'] = 'Listed company';
$lang_module['no_business_exportpdf'] = 'Currently no contractors are allowed to export PDF files';
$lang_module['message_static'] = 'There are %s contractors who have exported PDF files with the total points used to export the file being %s points. Of which, %s contractors are listed companies that have exported PDF files with the total points needed to export the PDF file being %s points.';
$lang_module['search_title_1'] = 'Search by name or tax code';
$lang_module['re_download'] = 'Redownload file';
$lang_module['request_download_file'] = 'Your request to redownload the file has been received. We will try to fulfill this request as quickly as possible.';
$lang_module['ability_point'] = 'Ability score of the soliciting entity';
$lang_module['ability_point_short'] = 'Ability Point';
$lang_module['ability_rank'] = 'Rank of tenderer';
$lang_module['ability_point_description'] = 'The above scores and rankings were analyzed by <b>DauThau.info</b> Software based on bidding history and business size. However, this scoring is only relative and cannot accurately determine which bidding party\'s capacity is stronger or weaker or determine the likelihood of winning a bid. When comparing, you should carefully consider many other factors such as the specific content of the bidding package and the field and industry of each business.';
$lang_module['some_error'] = 'An error occurred, please try again later.';
$lang_module['confirm_view_ability'] = 'Do you want to use %s/%s points to view the capacity score of this tenderer?';
$lang_module['view_ability_message'] = 'View ability score of contractor %s';
$lang_module['evaluation_criteria'] = 'Evaluation criteria';
$lang_module['point_evaluation'] = 'Evaluation score';
$lang_module['experience_ability'] = 'Experience';
$lang_module['finace_ability'] = 'Financial capacity';
$lang_module['transparency'] = 'Transparency of the Investor/Procuring Entity';
$lang_module['bidding_history'] = 'Bidding history';
$lang_module['see_more_ability'] = 'To better understand the scoring criteria, see more in the following article: <a href="https://dauthau.asia/news/tin-tuc/bo-chi-so-danh-gia-nang-luc-nha-thau-cua-dauthau-info-1316.html"><b>Set of indicators to evaluate the capacity of the bidding party</b></a>';
$lang_module['view_ability_renew'] = 'You need to <a href="%s"><b>renew VIP3 package</b></a> or <a href="#" class="btn_point_view_ability"><b>use score</b></a> to see the capacity score of the bidding party for detailed bidding.';
$lang_module['view_ability_regvip'] = 'You need to <a href="%s"><b>subscribe to the VIP3 plan</b></a> or <a href="#" class="btn_point_view_ability"><b>use score</b></a> to see detailed capacity scores of the bidding party.';
$lang_module['log_in_up_ability'] = 'You need <strong><a href="javascript:void(0)" data-toggle="loginFormShow">Login</a></strong> or <strong><a href="%s">Register</a></strong> to see the bidder\'s capacity score.';
$lang_module['error_point_out_ability'] = 'You do not have enough points to purchase this feature (%s points), please <a href="%s" target="_blank">buy more points</a> before viewing Contractor capacity score';
$lang_module['no_data_stocks'] = 'There are currently no listed company bidders';
$lang_module['search_title_2'] = 'Search by name or code';
$lang_module['message_static_stock'] = 'There are %s contractors who are listed companies with a total score of %s points needed to export a PDF file.';
$lang_module['stock_pdf_points'] = 'Listed Company PDF Export Points';
$lang_module['total_win_price_title_y'] = 'Total winning bid amount (million VND)';
$lang_module['interest_bids'] = 'Field of the bid package';
$lang_module['system_error'] = 'System failure, please contact us about this issue';
$lang_module['idfile_not_found'] = 'Error: File not found';
$lang_module['idbid_not_found'] = 'Error: Bidding documents could not be identified when downloading the file';
$lang_module['total_download_filepdf'] = 'Total PDF export times';
$lang_module['total_point_filepdf'] = 'Total points used to export PDF';
$lang_module['exportpdftest'] = 'The contractor has been exported PDF (Test data)';
$lang_module['mess_re_export_pdf'] = 'You have requested to export the file before at %s, do you want to download the file again? See <a href="%s" target="_blank">here</a>.';
$lang_module['download_old_pdf'] = 'Download old file again';
$lang_module['download_new_pdf'] = 'Download new file';
$lang_module['title_value_chart'] = 'Total value (million VND)';
$lang_module['statistics_time'] = 'Statistics time';
$lang_module['des_value_desire'] = '<span>Explanation:</span><ul>
<li>The expected value is the total value of the bid packages that the bidder bids for (whether it wins or not is unknown).</li>
<li>The maximum value is the total winning bid value of the bid packages that the bidder participates in (including joint bid packages).</li>
<li>The minimum value is the total winning bid value of the bid packages that the bidder participates in independently!</li>
</ul>';
$lang_module['compare_limit_reach'] = 'You can only compare up to 3 contractors, please remove the comparison list to add this contractor to the list';
$lang_module['add_business'] = 'Add contractor';
$lang_module['add_compare'] = 'Add comparison';
$lang_module['remove_compare'] = 'Remove comparison';
$lang_module['remove_all_business'] = 'Remove all';
$lang_module['business_compare'] = 'Compare contractors';
$lang_module['recent_viewed_ctt'] = 'Recently viewed contractors';
$lang_module['bidding_join_cmp'] = '<b>%s</b> packages, winning <b>%s</b> packages, failing <b>%s</b> packages, <b>%s</b> packages without results, <b>%s</b> packages cancelled';
$lang_module['total_win_doclap'] = 'Total winning bid value with independent role';
$lang_module['total_win_liendanh'] = 'Total winning bid value with joint venture role';
$lang_module['bidding_win_cmp'] = '<b>%s</b> VND (In which <b>%s</b> VND are packages with designated bids; <b>%s</b> VND are packages with KQLCNT but no TBMT)';
$lang_module['lowest_bid_rate'] = 'Lowest bid rate when participating';
$lang_module['win_on_est'] = 'Average ratio of winning bid price to estimated price';
$lang_module['num_solicitor_relate'] = 'Number of solicitors with relationship';
$lang_module['num_contractor_fight'] = 'Number of bidders who have competed';
$lang_module['num_contractor_venture'] = 'Number of bidders who have joint ventures';
$lang_module['contractor_with'] = '<b>%s</b> bidders in ';
$lang_module['list_province'] = 'Provinces that have participated in bidding';
$lang_module['ability_point_detail'] = 'Detailed ability score';
$lang_module['x2_download_with'] = 'with X2 package';
$lang_module['x2_download_without'] = 'do not use the X2 package';
$lang_module['array_exist_request_export'] = 'These are the results of "Export contractor information" that you asked DauThau.info to process. This result can serve your research, statistics, calculations, etc. work. This feature gives users many different benefits such as: Saving time and effort; provide complete, accurate and transparent information; Diverse types of downloaded information, optimize workflow... See introduction <a href="https://dauthau.asia/news/tin-tuc/ra-mat-goi-phan-mem-x2-xuat-du-lieu-nha-thau-ra-file-excel- 1024.html">here</a>.';
$lang_module['array_empty_request_export'] = 'You have not used the "Export contractor information" feature any time. This feature is intended to export contractor search results <a href="https://dauthau.asia/businesslistings/">here</a> to an excel file, serving research, statistics, and calculations. ,.... of organizations and individuals. This feature gives users many different benefits such as: Saving time and effort; provide complete, accurate and transparent information; Diverse types of downloaded information, optimize workflow... See introduction <a href="https://dauthau.asia/news/tin-tuc/ra-mat-goi-phan-mem-x2-xuat-du-lieu-nha-thau-ra-file-excel- 1024.html">here</a>.';
$lang_module['x2_id_request_err'] = 'Error: Could not find download request details';
$lang_module['x2_download_point_mess'] = 'Download list contractors %s, %s contractors - %s point';
$lang_module['crawl_time'] = 'Find';
$lang_module['info_contractor'] = 'General contractor information';
$lang_module['result_contractor'] = 'Contractor data analysis results';
$lang_module['ability_core'] = 'Contractor\'s ability score';
$lang_module['list_charts'] = 'Data statistics charts';
$lang_module['utilities'] = 'Utilities for you';
$lang_module['contractor_activities'] = 'Contractor activities';
$lang_module['detail_data'] = 'Detailed data statistics tables';
$lang_module['title_search_contractor'] = 'Search for business information';
$lang_module['title_search_stocks'] = 'Search for information on listed company contractors';
$lang_module['recapcha_title'] = 'Want to update again?';
$lang_module['empty_data'] = 'No data found';
$lang_module['no_answer'] = 'No Answer';
$lang_module['points_deducted_on_viewing_contractor_details'] = 'The number of points deducted when viewing the contractor\'s detailed information.';
$lang_module['message_point_view'] = 'View %s';
$lang_module['title_view_point_business'] = 'Contractor: ';
$lang_module['message_point_view_suss'] = 'You have been deducted %s points for viewing bid information.<br /> See details <strong><a href="/en/news/blog/hot-all-accounts-can-view-bid-information-by-points-10.html" target="_blank">here</a></strong>';
$lang_module['info_point_not_enough'] = 'According to <strong><a href="/en/news/blog/hot-all-accounts-can-view-bid-information-by-points-10.html" target="_blank">to the new rules,</a></strong> you will be deducted 1 point for each view of bid information!<br /> Your account does not have enough points to perform the information viewing operation. Please click the <strong>Buy Points</strong> button below to purchase more points and then return to this page.';
$lang_module['info_point_not_enough_short'] = 'Your account does not have enough points to view information. Please click the <strong>Buy Points</strong> button below to purchase more points and then return to this page.';
$lang_module['chart_select_label'] = 'Statistics options';
$lang_module['chart_by_role'] = 'By role';
$lang_module['chart_by_type'] = 'By type';
$lang_module['package_status_total'] = 'Total number of packages participated';
$lang_module['package_status_won'] = 'Total number of packages won';
$lang_module['package_status_lost'] = 'Total number of packages lost';
$lang_module['num_revenue'] = 'Revenue';
$lang_module['total_revenue'] = 'Total revenue';
$lang_module['independent_contractor'] = 'Independent contractor';
$lang_module['joint_contractor'] = 'Joint contractor';
$lang_module['direct_contractor'] = 'Direct appointment method';
$lang_module['other_contractor'] = 'Other methods';
$lang_module['note_static'] = 'Notes';
$lang_module['user_view_detail'] = 'Requires login to view detailed content';

$lang_module['plpreport_not_vip'] = 'You need to <strong><a href="%s">subscribe to the PLP Report package</a></strong> to be able to Download the PDF file “Business performance results in the public procurement market of listed companies on the stock market”';
$lang_module['plpreport_renew_vip'] = 'You need to <strong><a href="%s">renew to the PLP Report package</a></strong> to be able to Download the PDF file “Business performance results in the public procurement market of listed companies on the stock market”';
$lang_module['plpreport_not_user'] = 'You need to <strong><a href="javascript:void(0)" data-toggle="loginFormShow">Login</a></strong> or <strong><a href="%s">Register</a></strong> to be able to Download the PDF file “Business performance results on the public procurement market of listed companies on the stock market”';
$lang_module['plp_accessdeny'] = 'The link is incorrect or you do not have access to this page';
$lang_module['plpreport_pagetitle'] = 'Download the file "Business performance results in the public procurement market of listed companies on the stock market"';
$lang_module['month'] = 'Month';
$lang_module['download_left'] = 'Remaining downloads';
$lang_module['download'] = 'Download file';
$lang_module['plp_download_limit'] = 'You have used up your download limit for this file';
$lang_module['plp_download_confirm'] = 'You will lose one download for this file, are you sure you want to download this file?';
$lang_module['plp_download_note'] = 'Note: You can only download each file 10 times.';
$lang_module['confirm'] = 'Confirm';
$lang_module['sort_orderby'] = 'Sort by';
$lang_module['sort_type_default'] = 'Newest to Oldest';
$lang_module['sort_type_num_total_desc'] = 'Most Bids';
$lang_module['sort_type_num_result_desc'] = 'Most Winning Bids';
$lang_module['sort_type_num_false_desc'] = 'Most Failed Bids';
$lang_module['sort_type_ability_point_desc'] = 'Ability Points';
$lang_module['sort_type_total_revenue'] = 'Total revenue';
$lang_module['sort_type_total_by_role'] = 'By Role';
$lang_module['sort_type_total_by_type'] = 'By Type';
$lang_module['sort_type_independent_contractor'] = 'Independent contractor';
$lang_module['sort_type_joint_contractor'] = 'Joint contractor';
$lang_module['sort_type_direct_contractor'] = 'Direct appointment method';
$lang_module['sort_type_other_contractor'] = 'Other methods';
$lang_module['plp_report_notvip_feature'] = 'You need to subscribe to the PLP Report package to use this feature';
$lang_module['plp_report_renewvip_feature'] = 'You need to renew the PLP Report package to use this feature';
$lang_module['click_to_action'] = 'Click here to take action';
$lang_module['stocks_multi_keyword'] = 'Keywords can be contractor name, tax code, address. You can use up to 5 keywords, separated by commas (,).';
$lang_module['plp_show_only'] = 'Data is only displayed to customers who have purchased the <b><a href="/page/tinh-nang-bang-gia-goi-plp-report.html">PLP Report!</a></b>';
$lang_module['mss_time_download'] = '<u>The system has recorded <b>%s</b> re-download requests, with the most recent successful attempt at <b>%s</b></u>';
$lang_module['get_ho_so_mss'] = 'The system has previously received a request to redownload the file, please wait until the download is completed!';
$lang_module['contractor_noname'] = 'Contractor could not find business registration number';
$lang_module['view_full_list'] = 'View full list';

$lang_module['vnr_static'] = 'This contractor belongs to %s with the following information:<br/>
<blockquote class=list>
    <ul>
        <li>Year started in TOP 500: <b>%s</b></li>
        <li>Industry: <b>%s</b></li>
    </ul>
    <footer>DauThau.info software aggregates information from the Vietnam Report database</footer>
</blockquote>';
$lang_module['vnr_type1_lg'] = 'TOP 500 largest enterprises in Vietnam';
$lang_module['vnr_type2_lg'] = 'TOP 500 largest private enterprises in Vietnam';
$lang_module['vnr_chart_title'] = 'TOP 500 HISTORY Chart';
$lang_module['vnr_type1'] = 'VNR500';
$lang_module['vnr_type2'] = 'VNR500 private';
$lang_module['rank'] = 'Rank';
$lang_module['and'] = 'and';
$lang_module['vnr_info'] = 'VNR500 information';
$lang_module['statictis_range'] = 'Statistics range';
$lang_module['sort_type'] = 'Sort type';
$lang_module['summarize'] = 'Summarize';
$lang_module['order_by'] = 'Sort order';
$lang_module['all_roles'] = 'All roles';
$lang_module['all_types'] = 'All types';
$lang_module['role_label'] = 'Role';
$lang_module['type_label'] = 'Type';
$lang_module['vnr_top10_block_title'] = 'Historical chart of contractors who have made it to the top 10 of VNR500';
$lang_module['vnr_top10_filter_options'] = 'Statistical options:';
$lang_module['vnr_top10_no_data'] = 'No data';
$lang_module['vnr_top10_error_load'] = 'ApexCharts has not been loaded';
$lang_module['vnr_top10_error_element'] = 'Chart element not found';
$lang_module['vnr_top10_chart_rank_prefix'] = 'Rank ';
$lang_module['vnr_top10_chart_no_rank'] = 'Not in top 10';
$lang_module['error_loading_data'] = 'Error_loading_data :';
$lang_module['title_business_industry'] = 'Business industry';
$lang_module['title_business_industry_dn'] = 'Business industry of %s';
$lang_module['title_funs_main'] = 'List of approved contractors';
$lang_module['title_funs_listlocation'] = 'List of contractors in %s';
$lang_module['des_funs_listlocation'] = 'Look up the latest list of contractors and businesses in %s on DauThau.info, information is continuously updated, full of details to support effective bidding';
$lang_module['des_funs_detail'] = 'Detailed information %s, tax code %s, address %s';
$lang_module['vnr_top10_detail_title'] = 'Historical chart of top 10 contractors who have been in the top VNR500';
$lang_module['vnr_top10_total_top10'] = 'Total number of times in the top 10';
$lang_module['redownload_logs'] = 'The system is processing the file re-download request from admin <b>%s</b> at <b>%s</b>, please wait!<br>';
$lang_module['title_with_x2'] = ' with X2 account';
$lang_module['title_minus_points'] = "The system has -%s points because you clicked to update the data of %s.";
$lang_module['linkprovince'] = 'tenderlistbylocation';
$lang_module['alias_nationwide'] = 'Nationwide';
$lang_module['nationwide'] = 'Implemented Nationwide';
$lang_module['alias_vn_out_territory'] = 'Outside the Territory of Vietnam';
$lang_module['vn_out_territory'] = 'Implemented Outside the Territory of Vietnam';
$lang_module['idprovince'] = 'Province/City';
$lang_module['idprovince_i'] = 'City';
$lang_module['idprovince_ii'] = 'Province/City';
