<!-- BEGIN: main -->
<div class="border-bidding" id="bodycontent">
    <span>{LANG.hanghoa}</span>
</div>
<!-- BEGIN: search -->
<form action="" method="get" class="form-horizontal" id="ltablesearch">
    <div class="form-group">
        <label class="control-label col-md-7">{LANG.key}:</label>
        <div class="col-md-17">
            <input class="form-control" type="text" value="{ARRAY_KEY.key}" name="key">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-md-7">{LANG.search_type2}:</label>
        <div class="col-md-17">
            <select class="form-control" name="type">
                <option value="all">----</option>
                <!-- BEGIN: type_hh -->
                <option value="{hh.key}" {hh.selected}>{hh.title}</option>
                <!-- END: type_hh -->
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-md-7">{LANG.finish_time_search}:</label>
        <div class="col-md-17">
            <input type="hidden" name="sfrom" value="{ARRAY_KEY.sfrom}" data-default="{ARRAY_KEY.from}">
            <input type="hidden" name="sto" value="{ARRAY_KEY.sto}" data-default="{ARRAY_KEY.to}">
            <input class="form-control search_range" type="text" value="{ARRAY_KEY.sfrom} - {ARRAY_KEY.sto}">
        </div>
    </div>
    <div class="row">
        <div class="col-md-16 col-md-offset-8">
            <input id="fsearch" type="submit" value="{LANG.search_submit}" class="btn btn-primary">
        </div>
    </div>
</form>
<br>
<!-- END: search -->
<!-- BEGIN: title -->
<h2 id="dshs" class="bidding-sub-title">{TITLE}</h2>
<!-- END: title -->
<div class="margin-bottom-sm">
    <form method="post">
        <!-- BEGIN: show_download -->
        <div class="flex">
            <div>
                <button type="button" class="btn xuatexcel" onclick="confirm_export()"><i class="fa fa-file-excel-o" aria-hidden="true"></i> {LANG.exporthh}</button>
                <span><i>({NUM_ITEMS} {LANG.field_1} = {TOTAL_POINT_DOWNLOAD} {LANG.points})</i></span>
            </div>
            <div class="info__view_rq">
                <p>{VIEW_RQ}</p>
            </div>
        </div>
        <div id="alertDownloadExcel"></div>
        <!-- END: show_download -->

        <!-- BEGIN: x4_alert -->
            <div id="x4_alert" class="alert alert-info">{X4_ALERT}</div>
        <!-- END: x4_alert -->

        <!-- Modal -->
        <div id="myModal" class="modal fade" role="dialog">
            <div class="modal-dialog text-left">
                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header text-left">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">{LANG.notice}</h4>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning"></div>
                        <p class="confirm-export-excel hidden">{LANG.confirm_download_hh}</p>
                        <div id="loading_data"></div>
                        <div class="alert alert-success notification_success"></div>
                        <div class="alert alert-danger notification_danger"></div>
                    </div>
                    <div class="modal-footer">
                        <div>
                            <div class="text-left no_point"></div>
                            <input type="hidden" id="is_x4" value=""/>
                            <p class="alert alert-danger no-data-goods hidden">{LANG.no_data_goods}</p>
                            <button type="button" class="btn btn-primary" data-fcode="" id="save_excel" name="xuatexcel" value="xuatexcel">{LANG.link_file_normal}</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<!-- BEGIN: view_table -->
<table class="bidding-table">
    <thead>
    <tr>
        <th>{LANG.number_title}</th>
        <th>{LANG.good_name}</th>
        <th>{LANG.code_goods}</th>
        <th>{LANG.desc}</th>
        <th class="{HIDDEN2}">{LANG.origin}</th>
        <th class="{HIDDEN2}">{LANG.capacity}</th>
        <th class="{HIDDEN2}">{LANG.classify}</th>
        <th class="{HIDDEN2}">{LANG.note}</th>
        <th class="{HIDDEN}">{LANG.bidder_name}</th>
        <th>{LANG.win_price}</th>
        <th>{LANG.so_tbmt}</th>
    </tr>
    </thead>
    <tbody>
    <!-- BEGIN: loop -->
    <tr>
        <td data-column="{LANG.number}" class="text-center">{DATA.stt}</td>
        <td data-column="{LANG.good_name}" class="text-left pos-relative">
            <p class="text-ellip wrap__text"  data-toggle="tooltip" data-placement="right" title="{DATA.goods_name}">
                {DATA.goods_name}
            </p>

        </td>
        <td data-column="{LANG.code_goods}" class="text-left pos-relative">
            <p class="text-ellip wrap__text" data-toggle="tooltip" data-placement="right" title="{DATA.sign_product}">{DATA.sign_product}</p>
        </td>
        <td data-column="{LANG.desc}" class="text-left pos-relative">
            <p class="text-ellip wrap__text" data-popover="popover" data-toggle="tooltip" data-placement="right" title="{DATA.description}" popover-content="{DATA.description}">{DATA.description}</p>
        </td>
        <td data-column="{LANG.origin}" class="text-left wrap__text {HIDDEN2}">{DATA.origin}</td>
        <td data-column="{LANG.capacity}" class="text-left wrap__text {HIDDEN2}">{DATA.capacity}</td>
        <td data-column="{LANG.classify}" class="text-left wrap__text {HIDDEN2}">{DATA.classify}</td>
        <td data-column="{LANG.note}" class="text-left wrap__text {HIDDEN2}">{DATA.note}</td>
        <!-- BEGIN: view2 -->
            <td data-column="{LANG.notice}" class="text-center" colspan="3">
                <a href="javascript:void(0)" class="view__price" data-id="{DATA.id}">{TB_POINT}</a>
                <p>
                    {LINKVIP}
                    <!-- BEGIN: popup_login -->
                    {POPUP_LOGIN}
                    <!-- END: popup_login -->
                </p>
            </td>
        <!-- END: view2 -->
        <!-- BEGIN: view4 -->
            <td data-column="{LANG.notice}" class="text-center" colspan="3">
                <a href="javascript:void(0)" class="view__price" data-id="{DATA.id}">{TB_POINT}</a>
                <p>
                    {LINKVIP_RENEW}
                </p>
            </td>
        <!-- END: view4 -->
        <!-- BEGIN: view3 -->
        <td data-column="{LANG.bidder_name}" class="text-center {HIDDEN}">
            <div>
                <div class="margin-bottom">
                    <!-- BEGIN: bidder -->
                    <ul class="list-bidder">
                        <!-- BEGIN: yes_link -->
                        <li><a href="{BIDDER.link}"> <!-- BEGIN: caret --> <em class="fa fa-caret-right">&nbsp;</em>
                                <!-- END: caret --> <!-- BEGIN: licence --> <span
                                        class="lic-code">{BIDDER.no_business_licence} </span>
                                <!-- END: licence -->{BIDDER.name}
                            </a></li>
                        <!-- END: yes_link -->
                        <!-- BEGIN: no_link -->
                        <li>
                            <!-- BEGIN: caret --> <em class="fa fa-caret-right">&nbsp;</em> <!-- END: caret -->
                            <!-- BEGIN: licence --> <span class="lic-code">{BIDDER.no_business_licence} </span>
                            <!-- END: licence -->{BIDDER.name}
                        </li>
                        <!-- END: no_link -->
                    </ul>
                    <!-- END: bidder -->
                </div>
            </div>
        </td>
        <td data-column="{LANG.win_price}" class="text-center">{DATA.bid_price}</td>
        <td data-column="{LANG.so_tbmt}" class="text-center"><a href="{DATA.linkgoithau1}">{DATA.tbmt1}</a></td>
        <!-- END: view3 -->
        <!-- BEGIN: view -->
        <td data-column="{LANG.bidder_name}" class="text-center {HIDDEN}">
            <div>
                <div class="margin-bottom">
                    <!-- BEGIN: bidder -->
                    <ul class="list-bidder">
                        <!-- BEGIN: yes_link -->
                        <li><a href="{BIDDER.link}"> <!-- BEGIN: caret --> <em class="fa fa-caret-right">&nbsp;</em>
                                <!-- END: caret --> <!-- BEGIN: licence --> <span
                                        class="lic-code">{BIDDER.no_business_licence} </span>
                                <!-- END: licence -->{BIDDER.name}
                            </a></li>
                        <!-- END: yes_link -->
                        <!-- BEGIN: no_link -->
                        <li>
                            <!-- BEGIN: caret --> <em class="fa fa-caret-right">&nbsp;</em> <!-- END: caret -->
                            <!-- BEGIN: licence --> <span class="lic-code">{BIDDER.no_business_licence} </span>
                            <!-- END: licence -->{BIDDER.name}
                        </li>
                        <!-- END: no_link -->
                    </ul>
                    <!-- END: bidder -->
                </div>
            </div>
        </td>
        <td data-column="{LANG.win_price}" class="text-center">{DATA.bid_price}</td>
        <td data-column="{LANG.so_tbmt}" class="text-center"><a href="{DATA.linkgoithau}">{DATA.tbmt}</a></td>
        <!-- END: view -->
    </tr>
    <!-- END: loop -->
    <!-- BEGIN: no_keyword -->
    <td data-column="{LANG.notice}" class="text-center" colspan="10"><p class="alert alert-warning">{NO_DATA}</p></td>
    <!-- END: no_keyword -->
    </tbody>
</table>
<!-- END: view_table -->

<!-- BEGIN: limit_page -->
    {LIMIT_PAGE}
<!-- END: limit_page -->

{CHANGE_SEARCH}
<!-- BEGIN: generate_page -->
<div class="text-center">{NV_GENERATE_PAGE}</div>
<!-- END: generate_page -->
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/plugins/daterangepicker/moment.min.js"></script>
<script type="text/javascript"
        src="{NV_BASE_SITEURL}themes/dauthau/plugins/daterangepicker/daterangepicker.js"></script>
<link type="text/css" rel="stylesheet"
      href="{NV_BASE_SITEURL}themes/dauthau/plugins/daterangepicker/daterangepicker.css"/>
<script type="text/javascript">
    var formObject = $("[id=ltablesearch]");
    function bl_setDaterangepicker(_options) {
        // Menu khoảng tìm kiếm
        var ranges = {};
        ranges['{LANG.this_month}'] = [moment().startOf('month'), moment().endOf('month')];
        ranges['{LANG.last_3_months}'] = [moment().startOf('quarter'), moment().endOf('quarter')];
        ranges['{LANG.this_year}'] = [moment().startOf('year'), moment().endOf('year')];
        ranges['{LANG.last_all_days}'] = [moment('{ARRAY_KEY.min_date}', "DD/MM/YYYY"), moment()];

        var calendar_options = {
            showDropdowns: true,
            locale: {customRangeLabel: '{LANG.custom_range}', format: 'DD/MM/YYYY', help: ''},
            ranges: ranges,
            startDate: moment().subtract(14, 'days'),
            endDate: moment(),
            opens: 'right',
            drops: "auto",
            alwaysShowCalendars: true,
        };

        $.extend(calendar_options, _options);

        $(".search_range", formObject).daterangepicker(calendar_options, function (start, end, label) {
            $("[name=sfrom]", formObject).val(start.format('DD/MM/YYYY'));
            $("[name=sto]", formObject).val(end.format('DD/MM/YYYY'))
        });
    }

    $(function () {
        bl_setDaterangepicker({
            startDate: $("[name=sfrom]", formObject).val(),
            endDate: $("[name=sto]", formObject).val()
        });
    });
</script>

<script>
    window.page_url = '{PAGE_URL}';
</script>

<div id="trudiem"></div>
<style>
    #trudiem {
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background: #d15c18d1;
        padding: 10px;
        color: #ffffff;
        margin: 0;
        box-shadow: 0px 5px 10px rgb(141 136 136 / 60%);
        border-radius: 2px;
        display: none;
    }

    #trudiem p {
        margin: 0;
    }
    .tooltip {
        width: 400px;
    }
</style>

<script type="text/javascript">
    $(document).ready(function($) {
        $('[data-toggle="tooltip"]').tooltip()
        $(".notification_success").hide();
        $(".notification_danger").hide();

        $("#save_excel").click(function() {
            if ($("#is_x4").val() == 1) {
                $("#loading_data").html('{LANG.processing_data} <i class="fa fa-spinner fa-spin"></i>');
                $.ajax({
                    url: location.href,
                    type: 'POST',
                    data: {
                        xuatexcel: 1
                    },
                })
                .done(function(res) {
                    if (res['status'] == 'success') {
                        $(".notification_success").hide();
                        $(".notification_danger").hide();
                        $(".notification_success").show(500);
                        $(".notification_success").html(res['messages']);
                    } else {
                        $(".notification_danger").hide();
                        $(".notification_danger").show(500);
                        $(".notification_danger").html(res['messages']);
                    }
                    $("#loading_data").html('');
                });
            } else {
                if (confirm("{LANG.confirm_download_hh}")) {
                    $("#loading_data").html('{LANG.processing_data} <i class="fa fa-spinner fa-spin"></i>');
                    $.ajax({
                        url: location.href,
                        type: 'POST',
                        data: {
                            xuatexcel: 1
                        },
                    })
                    .done(function(res) {
                        if (res['status'] == 'success') {
                            $(".notification_success").hide();
                            $(".notification_danger").hide();
                            $(".notification_success").show(500);
                            $(".notification_success").html(res['messages']);
                            setTimeout(function() {
                                location.href = location.href;
                            }, 15000);
                        } else {
                            $(".notification_danger").hide();
                            $(".notification_danger").show(500);
                            $(".notification_danger").html(res['messages']);
                        }
                        $("#loading_data").html('');
                    });
                }
            }
        });


        $(".view__price").click(function() {
            id = $(this).attr('data-id');
            let type_search = '{TYPE_SEARCH}';
            $html = '';
            if (type_search == 1) {
                $html +=    '<td data-column="{LANG.bidder_name}">\n' +
                                '<div>\n' +
                                    '<div class="margin-bottom">\n' +
                                        '<ul class="list-bidder_' + id + '">\n' +

                                        '</ul>\n' +
                                    '</div>\n' +
                                '</div>\n' +
                            '</td>';
            }

            $html += '<td data-column="{LANG.win_price}"><span class="win_price_' + id +'"></span></td>';
            $html += '<td data-column="{LANG.so_tbmt}"> <span class="so_tbmt_' + id +'"></span></td>';

            $(this).closest('tr').append($html);
            $(this).parent().remove();

            $.ajax({
                url: location.href,
                type: 'POST',
                data: {
                    'view_price': 1,
                    'id': id
                },
            })
            .done(function(res) {
                result = JSON.parse(res);
                if (result['khongdudiem'] == 1) {
                    alert("{LANG.point_miss_goods}");
                } else if (result['khongdudiem'] == 2) {
                    alert("{LANG.point_goods_err}");
                } else {
                    if (result['data']['bidder_list'] != null && type_search == 1) {
                        let size_bidder = result['data']['bidder_list'].length;
                        let html_bidder = '';
                        result['data']['bidder_list'].forEach(bidder => {
                            if (bidder['link'] != null) {
                                html_bidder += '<li><a href="' + bidder['link'] + '">';
                                if (size_bidder > 1) {
                                    html_bidder += ' <em class="fa fa-caret-right">&nbsp;</em>';
                                }
                                if (bidder['no_business_licence'] != null) {
                                    html_bidder += ' <span class="lic-code">' + bidder['no_business_licence'] + '</span>';
                                }
                                html_bidder += bidder['name'] + '</a></li>';
                            } else {
                                html_bidder += '<li>';
                                if (size_bidder > 1) {
                                    html_bidder += ' <em class="fa fa-caret-right">&nbsp;</em>';
                                }
                                if (bidder['no_business_licence'] != null) {
                                    html_bidder += ' <span class="lic-code">' + bidder['no_business_licence'] + '</span>';
                                }
                                html_bidder += bidder['name'] + '</li>';
                            }

                        });
                        // thêm tên nhà thầu vào html
                        if (html_bidder != '') {
                            $(".list-bidder_" + result['data']['id']).html(html_bidder);
                        }
                    }
                    $(".so_tbmt_" + result['data']['id']).html("<a href=" + result['linkgoithau'] + ">" + result['tbmt'] + "</a>");
                    $(".win_price_" + result['data']['id']).html(result['data']['bid_price']);
                    $("#trudiem").html(result['notifi']);
                    $("#trudiem").slideDown(500);
                    setTimeout(function() {
                        $("#trudiem").slideUp(500);
                    }, 2000);
                }
            })
        });
    });

    function confirm_export () {
        $("#myModal .alert-warning").html('');
        $(".notification_success").html('');
        $(".notification_danger").html('');
        $(this).attr('disabled', true);
        $("#save_excel").attr('disabled', false);
        $(".confirm-export-excel").addClass("hidden");
        $.ajax({
            url: location.href,
            type: 'POST',
            data: {
                confirm_export_excel: 1
            },
        })
        .done(function(res) {
            if (res['status'] == 'success') {
                $(this).attr('disabled', false);
                $(".notification_success").hide();
                $(".notification_danger").hide();
                $("#is_x4").val(res.is_x4);
                if (res.is_x4 == 1) {
                    if (res.total == 0) {
                        $(".confirm-export-excel").html("{LANG.no_data_goods}");
                        $("#save_excel").attr('disabled', true);
                    }
                    $(".confirm-export-excel").removeClass("hidden");
                    $("#myModal .alert-warning").addClass("hidden");
                } else {
                    if (res.total == 0) {
                        $(".no-data-goods").removeClass("hidden");
                        $("#save_excel").hide();;
                    }
                    $("#save_excel").html(res.icon_download + " {LANG.link_file_normal}");
                    $("#myModal .alert-warning").removeClass("hidden");
                    $("#myModal .alert-warning").html(res.value + res.note__download_novip);
                    if (res.no_point != '') {
                        $('#alertDownloadExcel').html('<div class="alert alert-warning">' + res.no_point + '</div>');
                        $(".no_point").html('<div class="alert alert-danger">' + res.no_point + '</div>');
                    }
                    if (res.disable == 1) {
                        $("#save_excel").attr('disabled', true);
                    }
                }
                $('#myModal').modal('show');
            } else {
                $(".notification_danger").hide();
                $(".notification_danger").show(500);
                $(".notification_danger").html(res['messages']);
            }
            $("#loading_data").html('');
        });
    }

    $(".text-ellip").each(function() {
        if ($(this)[0].clientHeight < ($(this)[0].scrollHeight - 1)) {
            $(this).parent().append('<a href="javascript:void(0)" class="see-more">[<i class="fa fa-caret-down"></i>{LANG.expand}]</a>');
        }
    });

    $(".see-more").click(function() {
        $(this).parent().find('p').toggleClass("active");
        if ($(this).parent().find('p').hasClass("active")) {
            $(this).html("[<i class=\"fa fa-caret-up\"></i>{LANG.collapse}]");
        } else {
            $(this).html("[<i class=\"fa fa-caret-down\"></i>{LANG.expand}]");
        }
    });

</script>
<style type="text/css">
    #sitemodal .modal-dialog {
        z-index: 9999;
    }
</style>
<!-- END: main -->
