<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES., JSC. All rights reserved
 * @Createdate 3/9/2010 23:25
 */

if (!defined('NV_MAINFILE'))
    die('Stop!!!');

if (!nv_function_exists('nv_search_blocks')) {
    function nv_search_blocks($block_config)
    {
        global $module_info, $module_name, $module_data, $nv_Request, $module_file, $nv_Cache, $global_array_config, $array_op, $db, $district_list, $ward_list, $province_list, $user_info, $nv_Lang;
        global $global_config;

        if (file_exists(NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file . "/block_search_organization.tpl")) {
            $block_theme = $module_info['template'];
        } else {
            $block_theme = "default";
        }

        $xtpl = new XTemplate("block_search_organization.tpl", NV_ROOTDIR . "/themes/" . $block_theme . "/modules/" . $module_file);
        $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

        if ($global_config['rewrite_enable']) {
            $xtpl->assign('FORM_ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization', true));
        } else {
            $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php');
            $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
            $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
            $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
            $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
            $xtpl->assign('NV_OP_VALUE', 'organization');
            $xtpl->assign('MODULE_NAME', $module_name);
            $xtpl->parse('main.no_rewrite');
        }

        $keyword = $nv_Request->get_string('q', 'get', '');
        $province = $nv_Request->get_int('province', 'get', 0);
        $district = $nv_Request->get_int('district', 'get', 0);
        $ward = $nv_Request->get_int('ward', 'get', 0);
        $department = $nv_Request->get_int('department', 'get', 0);

        $a = $b = '';
        if (isset($array_op[1])) {
            $a = substr($array_op[1], 0, 1);
            $b = substr($array_op[1], 2);
            if(preg_match('/^([a-zA-Z0-9\-]+)\-([0-9]+)$/i', $b, $m)) {
                $b = $m[2];
            }
        }
        $module = "location";
        if ($a == "T") {
            $sql = "SELECT id FROM " . NV_PREFIXLANG . "_" . $module . "_province WHERE status=1 AND id = " . $db->quote($b);
            $result = $db->query($sql);
            list($_id) = $result->fetch(3);
            $province = $_id;
        } elseif ($a == "H") {
            $sql = "SELECT id, idprovince FROM " . NV_PREFIXLANG . "_" . $module . "_district WHERE status=1 AND id = " . $db->quote($b);
            $result = $db->query($sql);
            list($_id, $idprovince) = $result->fetch(3);
            $district = $_id;
            $province = $idprovince;
        } elseif ($a == "X") {
            $sql = "SELECT id, idprovince, iddistrict FROM " . NV_PREFIXLANG . "_" . $module . "_ward WHERE status=1 AND id = " . $db->quote($b);
            $result = $db->query($sql);
            list($_id, $idprovice, $iddistrict) = $result->fetch(3);
            $ward = $_id;
            $district = $iddistrict;
            $province = $idprovice;
        }

        //department
        $sql = "SELECT depofjusticeid, fullname FROM nv4_dau_gia_deparment ORDER BY depofjusticeid";
        $department_list = $nv_Cache->db($sql, 'id', $module_name);
        $arr_department = array();
        foreach ($department_list as $department_list_i) {
            $sl = ($department == $department_list_i['depofjusticeid']) ? "selected='selected'" : "";
            $arr_department[] = array(
                "id" => $department_list_i['depofjusticeid'],
                "title" => $department_list_i['fullname'],
                "sl" => $sl);
        }

        if ($ward > 0) {
            $sql = "SELECT id, idprovince, iddistrict FROM " . NV_PREFIXLANG . "_location_ward WHERE status=1 AND id = " . $ward;
            $result = $db->query($sql);
            list ($_id, $idprovice, $iddistrict) = $result->fetch(3);
            $ward = $_id;
            $district = $iddistrict;
            $province = $idprovice;
        } else if ($district > 0) {
            $sql = "SELECT id, idprovince FROM " . NV_PREFIXLANG . "_location_district WHERE status=1 AND id = " . $district;
            $result = $db->query($sql);
            list ($_id, $idprovince) = $result->fetch(3);
            $district = $_id;
            $province = $idprovince;
        }

        $xtpl->assign('TIMESTAMP', $global_config['timestamp']);
        $xtpl->assign('SEARCH_PROVINCE', $province);
        $xtpl->assign('SEARCH_DISTRICT', $district);
        $xtpl->assign('SEARCH_WARD', $ward);

        $xtpl->assign('CLASS_DISTRICT', $province > 0 ? '' : ' hidden');
        $xtpl->assign('CLASS_WARD', $district > 0 ? '' : ' hidden');

        foreach ($arr_department as $department_i) {
            $xtpl->assign('sl_department', $department_i['sl']);
            $xtpl->assign('key_department', $department_i['id']);
            $xtpl->assign('val_department', $department_i['title']);
            $xtpl->parse('main.department');
        }

        $xtpl->assign('keyword', $keyword);
        $xtpl->parse('main');
        return $xtpl->text('main');
    }
}

if (defined('NV_SYSTEM')) {
    $content = nv_search_blocks($block_config);
}
