<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<link type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css" rel="stylesheet" />
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js"></script>
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.{NV_LANG_INTERFACE}.min.js"></script>
<link rel="stylesheet" type="text/css" href="{NV_BASE_SITEURL}themes/dauthau/plugins/bootstrap-datepicker/css/bootstrap-datepicker.min.css">
<link rel="stylesheet" type="text/css" href="{NV_BASE_SITEURL}themes/dauthau/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css">

<div class="border-bidding">
    <span>{LANG.title_search_stocks}</span>
</div>
<form action="{ACTION}" method="get" class="form-horizontal form-search form-bussiness-search" id="stocksSearchBlock"  data-action="{FORM_ACTION}" data-action16="{FORM_ACTION16}" >
    <!-- BEGIN: no_rewrite -->
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
    <!-- END: no_rewrite -->
    <div class="form-group margin-bottom-sm">
        <div class="row">
            <label class="control-label col-md-5">{LANG.tukhoa}:</label>
            <div class="col-md-19">
                <input class="form-control" placeholder="{LANG.search_key_title}" type="text" value="{Q}" name="q" data-default=""/>
                <em class="help-block margin-bottom-sm">{LANG.stocks_multi_keyword}</em>
            </div>
        </div>  
    </div>
    <div class="row">
        <div class="col-md-19 col-md-offset-5">
            <input type="hidden" name="is_advance" value="{ADVANCE}" />
            <input type="hidden" name="is_provinceBuss" value="{provinceBuss}"/>
            <input id="fsearch" type="submit" value="{LANG.search}" class="btn btn-primary bussubmit_search"/>
            <a class="btn-search-advance btn btn-default" href="javascript:void(0);" data-search-simple="{LANG.search_simple}" data-search-advance="{LANG.search_advance}" data-icon-search-simple="icon-chevron-down" data-icon-search-advance="icon-chevron-up">
                <em class="<!-- BEGIN: advance_icon_0 -->icon-chevron-down <!-- END: advance_icon_0 -->
                <!-- BEGIN: advance_icon_1 -->icon-chevron-up <!-- END: advance_icon_1 -->margin-right-sm"></em>
                <strong class="txt">{LANG_ADVANCE}</strong>
            </a>
        </div>
    </div>
    <div class="advance-search-content" <!-- BEGIN: advance_bl_hide -->style="display: none"<!-- END: advance_bl_hide -->>
        <div class="mt-1">
            <div class="form-row">
                <div class="form-group">
                    <div class="row">
                        <label class="control-label col-md-5">{LANG.diadiem}:</label>
                        <div class="col-md-19" id="block-search-location">
                            <select disabled="disabled" data-selected="{SEARCH_PROVINCE}" name="province" id="province" class="form-control" data-flag="1" data-default="{LANG.pleaseselect}" data-unclassified="{LANG.unclassified}" data-timestamp="{TIMESTAMP}">
                                <option value="-1">{LANG.pleaseselect}</option>
                                <option value="0">{LANG.unclassified}</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>         
        </div>
        <div class="form-group">
            <div class="row">
                <label class="control-label col-md-5">{LANG.stocks_exchange}:</label>
                <div class="col-md-19">
                    <select name="exchange" class="form-control">
                        <option value="-1">{LANG.pleaseselect}</option>
                        <!-- BEGIN: exchange -->
                        <option value="{EXCHANGE.id}" {selected_exchange}>{EXCHANGE.title}</option>
                        <!-- END: exchange -->
                    </select>
                </div>
            </div>
        </div>
       
        <div class="form-group">
            <div class="row">
                <label class="control-label col-md-5">{LANG.ttnganhhang}:</label>
                <div class="col-md-19" id="industry_0">
                    <select name="industry_0" data-default="{LANG.pleaseselect}" onchange="getValueICB_block('industry_0', 1, 'industry_1', '', '');" class="form-control">
                        <option value="">{LANG.pleaseselect}</option>
                        <!-- BEGIN: industry -->
                        <option value="{VALUE.code}" {sl_industry}>{VALUE.code} - {VALUE.title}</option>
                        <!-- END: industry -->
                    </select>
                </div>
            </div>
        </div>
        <!-- BEGIN: child_industry -->
        <div class="form-group<!-- BEGIN: hidden --> hidden<!-- END: hidden -->">
            <div class="row">
                <div class="col-md-19 col-md-offset-5" id="industry_{LVL}">
                    <!-- BEGIN: child_sel -->
                    <select name="industry_{LVL}" data-default="{LANG.pleaseselect}" onchange="getValueICB_block('industry_{LVL}', {N_LVL}, 'industry_{N_LVL}', '', '');" class="form-control">
                        <option value="">{LANG.pleaseselect}</option>
                        <!-- BEGIN: industry -->
                        <option value="{VALUE.code}" {sl_industry}>{VALUE.code} - {VALUE.title}</option>
                        <!-- END: industry -->
                    </select>
                    <!-- END: child_sel -->
                </div>
            </div>
        </div>
        <!-- END: child_industry -->
    </div>
    <div class="mt-2">
        {STATIC}
    </div>
    <div class="h2 border-bidding mt-2">
        {LANG.statictis_range} <!-- BEGIN: bid_year_tooltip -->&nbsp;<a class="text-info" role="button" data-toggle-stock="tooltip" data-trigger="hover" data-placement="top" title="{PLP_TOOLTIP}" href="{LINK_VIP_32}"<!-- BEGIN: login_form_show --> data-toggle="loginFormShow"<!-- END: login_form_show -->>(<i class="fa fa-info-circle" aria-hidden="true"></i>)</a><!-- END: bid_year_tooltip -->
    </div>
    <div class="row">
        <div class="col-md-19 col-md-offset-5">
            <label class="custom-checkbox toggle">
                <input class="form-control" type="checkbox" name="bid_year_search" value="1" data-default="false"{BID_YEAR_CHECKED}{BID_YEAR_USABLE}><span class="txt">{LANG.bidding_start_time_search}</span>
            </label>
        </div>
    </div>
    <div class="form-group row">
        <!-- <label class="control-label col-md-5">{LANG.bidding_start_time}:</label> -->
        <div class="col-md-7 col-md-offset-5">
            <div class="input-group input-daterange" id="bid-year-range">
                <div class="input-group-addon">{LANG.year}</div>
                <input type="text" class="form-control" name="sfrom" id="sfrom" value="{SFROM}" data-default="{SFROM_DEFAULT}"{BID_YEAR_DISABLED}>
                <div class="input-group-addon"><i class="fa fa-arrow-right fa-fw"></i></div>
                <input type="text" class="form-control" name="sto" id="sto" value="{STO}" data-default="{STO_DEFAULT}"{BID_YEAR_DISABLED}>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-19 col-md-offset-5">
            <input id="fsearch-submit" type="submit" value="{LANG.summarize}" class="btn btn-primary bussubmit_search"/>
            <input class="btn btn-default form-by-reset" type="button" value="{LANG.reset}" />
        </div>
    </div>
    <div id="stocks_summarized" class="mt-2"></div>
    <div class="h2 border-bidding mt-2">
        {LANG.sort_type} <!-- BEGIN: sort_tooltip -->&nbsp;<a class="text-info" role="button" data-toggle-stock="tooltip" data-trigger="hover" data-placement="top" title="{PLP_TOOLTIP}" href="{LINK_VIP_32}"<!-- BEGIN: login_form_show --> data-toggle="loginFormShow"<!-- END: login_form_show -->>(<i class="fa fa-info-circle" aria-hidden="true"></i>)</a><!-- END: sort_tooltip -->
    </div>
    <div class="row">
        <div class="col-md-19 col-md-offset-5">
            <select class="form-control m-bottom" id="order_sel"{SORT_USABLE} name="order">
                <!-- BEGIN: order -->
                <option value="{ORDER.value}" {ORDER.selected}>{ORDER.title}</option>
                <!-- END: order -->
            </select>
        </div>
    </div>
    <input type="hidden" name="industry" value="{INDUSTRY}" />
</form>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript">
    $(function () {
        $('[data-toggle-stock="tooltip"]').tooltip()
    })
    var formObject_business = $("#stocksSearchBlock");
    $(".btn-search-advance", formObject_business).click(function(a) {
        a.preventDefault();
        is_advance = $('input[name="is_advance"]', formObject_business).val();
        if(parseInt(is_advance) == 1) {
            $(".advance-search-content", formObject_business).not(":hidden") && $(".advance-search-content", formObject_business).slideUp();
            $('input[name="is_advance"]', formObject_business).val(0);
            $(".btn-search-advance em", formObject_business).removeClass($(".btn-search-advance", formObject_business).data("icon-search-advance")).addClass($(".btn-search-advance", formObject_business).data("icon-search-simple"));
            $(".btn-search-advance .txt", formObject_business).text($(".btn-search-advance", formObject_business).data("search-advance"))
        } else {
            $(".advance-search-content", formObject_business).is(":hidden") && $(".advance-search-content", formObject_business).slideDown();
            $('input[name="is_advance"]', formObject_business).val(1);
            $(".btn-search-advance em", formObject_business).removeClass($(".btn-search-advance", formObject_business).data("icon-search-simple")).addClass($(".btn-search-advance", formObject_business).data("icon-search-advance"));
            $(".btn-search-advance .txt", formObject_business).text($(".btn-search-advance", formObject_business).data("search-simple"))
        }
    });
    $(".form-reset", formObject_business).click(function() {
        formObject_business[0].reset();
        $("#province", formObject_business).val(-1).trigger("change");
        $("#exchange", formObject_business).val(-1).trigger("change");
        $("#industry", formObject_business).val(-1).trigger("change");
        $("[name='q']", formObject_business).val("");
    });
    $(".form-by-reset", formObject_business).click(function() {
        $("[name='bid_year_search']", formObject_business).prop('checked', false).trigger("change");
        $("[name='sfrom']", formObject_business).val($("[name='sfrom']", formObject_business).data('default'));
        $("[name='sto']", formObject_business).val($("[name='sto']", formObject_business).data('default'));
    });
    // Thời gian tham dự thầu
    $('#bid-year-range input').each(function () {
        $(this).datepicker({
            autoclose: true,
            format: "mm/yyyy",
            viewMode: "months",
            minViewMode: "months",
            startDate : '01/2000',
            endDate : new Date(),
            maxViewMode: 2
        });

        if ($(this).val() == '0') {
            $(this).val('');
        }
    });

    if ($('#sfrom').val() != '0') {
        let startDate = $('#sfrom').val();
        $('#sto').data('datepicker').setStartDate(startDate);
    }
    if ($('#sto').val() != '0') {
        let endDate = $('#sto').val();
        $('#sfrom').data('datepicker').setEndDate(endDate);
    }

    $('#sto').on('change', function () {
        let endDate = $('#sto').val();
        if (endDate != '') {
            $('#sfrom').data('datepicker').setEndDate(endDate);
        } else {
            $('#sfrom').data('datepicker').setEndDate(new Date());
        }
    });
    $('#sfrom').on('change', function () {
        let startDate = $('#sfrom').val();
        if (startDate != '') {
            $('#sto').data('datepicker').setStartDate(startDate);
        } else {
            $('#sto').data('datepicker').setStartDate('01/2010');
        }
    });
    $('[name="bid_year_search"]').change(() => {
        if ($('[name="bid_year_search"]').is(':checked')) {
            $('#sfrom').prop('disabled', false);
            $('#sto').prop('disabled', false);
        } else {
            $('#sfrom').prop('disabled', true);
            $('#sto').prop('disabled', true);
        }
    })
    $('#order_sel').change(function() {
        formObject_business.submit();
    });
    $('#stocksSearchBlock').on('submit', function (e) {
        e.preventDefault();
        // Get the form value then remove all industry_* and submit
        var is_provinceBuss = parseInt($('[name=is_provinceBuss]', formObject_business).val());
        const selectedAliasesBuss = getSelectedAliasesBuss();
        if (is_provinceBuss === 1 && selectedAliasesBuss.length === 1) {
            window.location.href = formObject_business.attr("action");
            return false;
        } else {
            let formData = $(this).serializeArray();
            formData = formData.filter(item => !item.name.startsWith('industry_'));
            let formAction = $(this).attr('action');
            let queryString = $.param(formData);
            window.location.href = formAction + '?' + queryString;
        }
    });
    function getFormValueBuss(name) {
        return formObject_business.find('[name="' + name + '"]').val();
    }
    function getSelectedAliasesBuss() {
        return $('#province option:selected', formObject_business).map(function() {
            return $(this).data('alias');
        }).get();
    }
    function setFormActionBuss(actionUrlBuss, isProvinceBuss) {
        formObject_business.attr("action", actionUrlBuss);
        
        $('input[name="is_provinceBuss"]', formObject_business).val(isProvinceBuss);
    }
    function updateFormActionBuss() {
        const selectedAliasesBuss = getSelectedAliasesBuss();
        const qbuss = getFormValueBuss('q').trim();
        const exchange = getFormValueBuss('exchange');
        const industry_0 = getFormValueBuss('industry_0');
        const onlyProvinceSelected =
            selectedAliasesBuss.length === 1 &&
            qbuss === '' &&
            (exchange === '' || exchange === '-1') &&
            (industry_0 === '' || industry_0 === '-1');
        if (onlyProvinceSelected) {
            const provinceAliasBuss = selectedAliasesBuss[0];
            const actionUrlBuss = formObject_business.data("action16") + encodeURIComponent(provinceAliasBuss) + '/';
            if (formObject_business.attr("action") !== actionUrlBuss) {
                setFormActionBuss(actionUrlBuss, 1);
            }
        } else {
            setFormActionBuss(formObject_business.data("action"), 0);
        }
    }
    $('#province', formObject_business).on('change', updateFormActionBuss);
    $('[name="q"]', formObject_business).on('input', updateFormActionBuss);
    $('[name="exchange"]', formObject_business).on('change', updateFormActionBuss);
    $('[name="industry_0"]', formObject_business).on('change', updateFormActionBuss);
</script>
<style>
    .select2-container--default .select2-selection--single, .select2-container--bootstrap .select2-selection--single {
        height: 38px !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 32px;
    }
</style>
<!-- END: main -->
