<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_MOD_CERTIFICATE')) {
    exit('Stop!!!');
}

/**
 * nv_theme_bidding_training()
 *
 * @param mixed $array_data
 * @return
 */
function nv_theme_bidding_training($array_data, $generate_page, $page_title_h2)
{
    global $array_op, $module_name, $module_file, $module_info, $op, $nv_Lang, $module_config;
    $tb_point = sprintf($nv_Lang->getModule('notifi_click_minutue_price'), $module_config['bidding']['points_items_price']);
    $xtpl = new XTemplate('training.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);

    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('TEMPLATE', $module_info['template']);
    $xtpl->assign('OP', $op);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    foreach ($array_data as $row) {
        $xtpl->assign('ROW', $row);
        
        $xtpl->parse('main.view.loop');
    }
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.view.generate_page');
    }
    $xtpl->assign('TITLE', $page_title_h2);
    $xtpl->parse('main.view');
    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_theme_bidding_training_detail()
 *
 * @param mixed $array_data
 * @return
 */
function nv_theme_bidding_training_detail($array_data, $array_data_courses, $generate_page)
{
    global $array_op, $module_name, $module_file, $module_info, $op, $nv_Lang, $module_config, $page_name;
    $tb_point = sprintf($nv_Lang->getModule('notifi_click_minutue_price'), $module_config['bidding']['points_items_price']);
    $xtpl = new XTemplate('training_detail.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);

    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('TEMPLATE', $module_info['template']);
    $xtpl->assign('OP', $op);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    
    foreach ($array_data_courses as $row) {
        $row['certificate_date'] = date('d/m/Y', $row['certificate_date']);
        $row['start_date'] = date('d/m/Y', $row['start_date']);
        $row['end_date'] = date('d/m/Y', $row['end_date']);
        $row['type_course'] = sprintf($nv_Lang->getModule('type_course_s'), $row['type_course']);
        $xtpl->assign('ROW', $row);
        $xtpl->parse('main.view.loop');
        
    }
    $page_title = sprintf($nv_Lang->getModule('name_training_detail'), $page_name);
    $xtpl->assign('TITLE', $page_title);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.view.generate_page');
    }
    $xtpl->parse('main.view');
    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_theme_bidding_courses()
 *
 * @param mixed $array_data
 * @return
 */
function nv_theme_bidding_courses($array_data, $generate_page, $error)
{
    global $array_op, $module_name, $module_file, $module_info, $op, $nv_Lang, $module_config, $page_title;
    $tb_point = sprintf($nv_Lang->getModule('notifi_click_minutue_price'), $module_config['bidding']['points_items_price']);
    $xtpl = new XTemplate('courses.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);

    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('TEMPLATE', $module_info['template']);
    $xtpl->assign('OP', $op);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    foreach ($array_data as $row) {
        $row['certificate_date'] = date('d/m/Y', $row['certificate_date']);
        $row['start_date'] = date('d/m/Y', $row['start_date']);
        $row['end_date'] = date('d/m/Y', $row['end_date']);
        $row['type_course'] = sprintf($nv_Lang->getModule('type_course_s'), $row['type_course']);
        $xtpl->assign('ROW', $row);
        $xtpl->parse('main.view.loop');
    }
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.view.generate_page');
    }
    if (!empty($error)) {
        $xtpl->assign('ERROR', implode('<br />', $error));
        $xtpl->parse('main.error');
    }
    $xtpl->assign('TITLE', $page_title);
    $xtpl->parse('main.view');
    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_theme_bidding_courses_detail()
 *
 * @param mixed $array_data
 * @return
 */
function nv_theme_bidding_courses_detail($array_data, $array_data_students, $generate_page, $permision, $checkvip, $array_points_certificate)
{
    global $array_op, $module_name, $module_file, $module_info, $op, $nv_Lang, $module_config, $client_info, $page_name;
    $tb_point = sprintf($nv_Lang->getModule('notifi_click_minutue_price'), $module_config['bidding']['points_items_price']);
    $xtpl = new XTemplate('courses_detail.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);

    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('TEMPLATE', $module_info['template']);
    $xtpl->assign('OP', $op);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $tb = sprintf($nv_Lang->getModule('VIP1_reg2_new'), nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=1', true));
    $tb_renew = sprintf($nv_Lang->getModule('register_vip_rand'), nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&plan=1', true));
    if ($checkvip == -1) {
        global $page_url;
        $page_url = nv_url_rewrite($page_url, true);
        $xtpl->assign('PAGE_URL', nv_redirect_encrypt(urlRewriteWithDomain($page_url, NV_MY_DOMAIN)));
        $link_register = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=register&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
        $tb = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
    }
    $xtpl->assign('LINKVIP', $tb);
    $xtpl->assign('LINKVIP_RENEW', $tb_renew);
    if (defined('NV_IS_USER') && $checkvip == 0) {
        $xtpl->assign('TB_POINT', $tb_point);
    }
    if (defined('NV_IS_USER') && $checkvip == 2) {
        $xtpl->assign('TB_POINT', $tb_point);
    }
    $result_map = [
        $nv_Lang->getModule('average') => 1,
        $nv_Lang->getModule('good') => 2,
        $nv_Lang->getModule('excellent') => 3,
        $nv_Lang->getModule('outstanding') => 4,
    ];
    foreach ($array_data_students as $row) {
        $row['result_id'] = $result_map[$row['result']] ?? null;
        $row['link_result'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'] . '&amp;result=' . $row['result_id']);
        if (is_numeric($row['birthday'])) {
            $row['birthday'] = !empty($row['birthday']) ? date('d/m/Y', $row['birthday']) : '';
        }
        if (is_numeric($row['issuedate'])) {
            $row['issuedate'] = !empty($row['issuedate']) ? date('d/m/Y', $row['issuedate']) : '';
        }
        $xtpl->assign('ROW', $row);
        if (!empty($checkvip) && $checkvip == 1) {
            $xtpl->parse('main.view.loop.view');
        } elseif (in_array($row['id'], $array_points_certificate)) {
            $xtpl->parse('main.view.loop.view3');
        } elseif (!in_array($row['id'], $array_points_certificate) && !empty($checkvip) && $checkvip == 2) {
            $xtpl->parse('main.view.loop.view4');
        } else {
            $xtpl->parse('main.view.loop.view2');
        }
        $xtpl->parse('main.view.loop');
    }

    $array_data['certificate_date'] = date('d/m/Y', $array_data['certificate_date']);
    $array_data['start_date'] = date('d/m/Y', $array_data['start_date']);
    $array_data['end_date'] = date('d/m/Y', $array_data['end_date']);
    $array_data['type_course'] = sprintf($nv_Lang->getModule('type_course_s'), $array_data['type_course']);
    $array_data['name_courses_s'] = sprintf($nv_Lang->getModule('title_name_courses'), $array_data['name_courses']);
    $array_data['name_information'] = sprintf($nv_Lang->getModule('title_information'), $page_name);
    $alias_courses = change_alias($array_data['name']);
    $array_data['link_courses'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $alias_courses . '-' . $array_data['id_training']);
    $xtpl->assign('COURSES', $array_data);
    $xtpl->parse('main.view.courses');
   
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.view.generate_page');
    }
    
    $xtpl->parse('main.view');
    $xtpl->parse('main');
    return $xtpl->text('main');
}
/**
 * nv_theme_bidding_students()
 *
 * @param mixed $array_data
 * @return
 */
function nv_theme_bidding_students($array_data, $array_courses, $generate_page, $permision, $checkvip, $array_points_certificate, $title_student, $error)
{
    global $array_op, $module_name, $module_file, $module_info, $op, $nv_Lang, $module_config, $client_info;
    $tb_point = sprintf($nv_Lang->getModule('notifi_click_minutue_price'), $module_config['bidding']['points_items_price']);
    $xtpl = new XTemplate('student-list.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);

    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('TEMPLATE', $module_info['template']);
    $xtpl->assign('OP', $op);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $tb = sprintf($nv_Lang->getModule('VIP1_reg2_new'), nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=1', true));
    $tb_renew = sprintf($nv_Lang->getModule('register_vip_rand'), nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&plan=1', true));
    if ($checkvip == -1) {
        global $page_url;
        $page_url = nv_url_rewrite($page_url, true);
        $xtpl->assign('PAGE_URL', nv_redirect_encrypt(urlRewriteWithDomain($page_url, NV_MY_DOMAIN)));
        $link_register = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=register&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
        $tb = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
    }
    $xtpl->assign('LINKVIP', $tb);
    $xtpl->assign('LINKVIP_RENEW', $tb_renew);
    if (defined('NV_IS_USER') && $checkvip == 0) {
        $xtpl->assign('TB_POINT', $tb_point);
    }
    if (defined('NV_IS_USER') && $checkvip == 2) {
        $xtpl->assign('TB_POINT', $tb_point);
    }
    $result_map = [
        $nv_Lang->getModule('average') => 1,
        $nv_Lang->getModule('good') => 2,
        $nv_Lang->getModule('excellent') => 3,
        $nv_Lang->getModule('outstanding') => 4,
    ];
    foreach ($array_data as $_i => $row) {
        $row['result_id'] = $result_map[$row['result']] ?? null;
        $row['link_result'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'] . '&amp;result=' . $row['result_id']);
        if (is_numeric($row['birthday'])) {
            $row['birthday'] = !empty($row['birthday']) ? date('d/m/Y', $row['birthday']) : '';
        }
        if (is_numeric($row['issuedate'])) {
            $row['issuedate'] = !empty($row['issuedate']) ? date('d/m/Y', $row['issuedate']) : '';
        }
        $xtpl->assign('ROW', $row);
        if (!empty($checkvip) && $checkvip == 1) {
            $xtpl->parse('main.view.loop.view');
        } elseif (in_array($row['id'], $array_points_certificate)) {
            $xtpl->parse('main.view.loop.view3');
        } elseif (!in_array($row['id'], $array_points_certificate) && !empty($checkvip) && $checkvip == 2) {
            $xtpl->parse('main.view.loop.view4');
        } else {
            $xtpl->parse('main.view.loop.view2');
        }
        $xtpl->parse('main.view.loop');
    }
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.view.generate_page');
    }
    if (!empty($error)) {
        $xtpl->assign('ERROR', implode('<br />', $error));
        $xtpl->parse('main.error');
    }
    $xtpl->assign('TITLE', $title_student);
    $xtpl->parse('main.view');
    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_theme_bidding_students_detail()
 *
 * @param mixed $array_data
 * @return
 */
function nv_theme_bidding_students_detail($array_data, $data_courses, $permision, $checkvip, $array_points_certificate)
{
    global $array_op, $module_name, $module_file, $module_info, $op, $nv_Lang, $module_config, $client_info;
    $tb_point = sprintf($nv_Lang->getModule('notifi_click_minutue_price'), $module_config['bidding']['points_items_price']);
    $xtpl = new XTemplate('student_detail.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);

    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('TEMPLATE', $module_info['template']);
    $xtpl->assign('OP', $op);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $array_data['student_name'] = sprintf($nv_Lang->getModule('title_student_name'), $array_data['student_name']);
    $tb = sprintf($nv_Lang->getModule('VIP1_reg2_new'), nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=1', true));
    $tb_renew = sprintf($nv_Lang->getModule('register_vip_rand'), nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&plan=1', true));
    if ($checkvip == -1) {
        global $page_url;
        $page_url = nv_url_rewrite($page_url, true);
        $xtpl->assign('PAGE_URL', nv_redirect_encrypt(urlRewriteWithDomain($page_url, NV_MY_DOMAIN)));
        $link_register = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=register&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
        $tb = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
    }
    $xtpl->assign('LINKVIP', $tb);
    $xtpl->assign('LINKVIP_RENEW', $tb_renew);
    if (defined('NV_IS_USER') && $checkvip == 0) {
        $xtpl->assign('TB_POINT', $tb_point);
    }
    if (defined('NV_IS_USER') && $checkvip == 2) {
        $xtpl->assign('TB_POINT', $tb_point);
    }
    
    $result_map = [
        $nv_Lang->getModule('average') => 1,
        $nv_Lang->getModule('good') => 2,
        $nv_Lang->getModule('excellent') => 3,
        $nv_Lang->getModule('outstanding') => 4,
    ];
    
    if (is_numeric($array_data['birthday'])) {
        $array_data['birthday'] = !empty($array_data['birthday']) ? date('d/m/Y', $array_data['birthday']) : '';
    }
    if (is_numeric($array_data['issuedate'])) {
        $array_data['issuedate'] = !empty($array_data['issuedate']) ? date('d/m/Y', $array_data['issuedate']) : '';
    }
    $array_data['result_id'] = $result_map[$array_data['result']] ?? null;
    $array_data['link_result'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'] . '&amp;result=' . $array_data['result_id']);
    $xtpl->assign('COURSES', $array_data);
    if (!empty($checkvip) && $checkvip == 1) {
        $xtpl->parse('main.view');
    } elseif (in_array($array_data['id'], $array_points_certificate)) {
        $xtpl->parse('main.view3');
    } elseif (!in_array($array_data['id'], $array_points_certificate) && !empty($checkvip) && $checkvip == 2) {
        $xtpl->parse('main.view4');
    } else {
        $xtpl->parse('main.view2');
    }
    if (!empty($data_courses)) {
        $data_courses['start_date'] = date('d/m/Y', $data_courses['start_date']);
        $data_courses['end_date'] = date('d/m/Y', $data_courses['end_date']);
        $data_courses['certificate_date'] = date('d/m/Y', $data_courses['certificate_date']);
        $xtpl->assign('ROW', $data_courses);
        $xtpl->parse('main.data_courses');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}
