<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> JSC <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Dec 3, 2010  11:11:28 AM
 */

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE') or !defined('NV_IS_MODADMIN'))
    die('Stop!!!');

$allow_func = array(
    'main',
    'district',
    'ward');

/**
 * nv_Province()
 *
 * @return
 */
function nv_Province()
{
    global $db, $module_data;

    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_province ORDER BY weight ASC";
    $result = $db->query($sql);
    $list = array();
    while ($row = $result->fetch()) {
        $list[$row['id']] = array( //
            'title' => $row['title'], //
            'alias' => $row['alias'], //
            'images' => $row['images'], //
            'description' => $row['description'], //
            'weight' => (int)$row['weight'] //
                );
    }

    return $list;
}

/**
 * fix_catWeight()
 *
 * @return void
 */
function fix_catWeight()
{
    global $db, $module_data;

    $sql = "SELECT id FROM " . NV_PREFIXLANG . "_" . $module_data . "_province ORDER BY weight ASC";
    $result = $db->query($sql);
    $weight = 0;
    while ($row = $result->fetch()) {
        $weight++;
        $query = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_province SET weight=" . $weight . " WHERE id=" . $row['id'];
        $db->query($query);
    }
}

/**
 * nv_District()
 *
 * @return
 */
function nv_District()
{
    global $db, $module_data;

    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_district  ORDER BY weight ASC";
    $result = $db->query($sql);
    $list = array();
    while ($row = $result->fetch()) {
        $list[$row['id']] = array( //
            'idprovince' => $row['idprovince'], //
            'title' => $row['title'], //
            'alias' => $row['alias'], //
            'weight' => (int)$row['weight'] //
        );
    }

    return $list;
}

/**
 * fix_DisWeight()
 *
 * @param mixed $pro
 * @return void
 */
function fix_DisWeight($pro)
{
    global $db, $module_data;

    $sql = "SELECT id FROM " . NV_PREFIXLANG . "_" . $module_data . "_district WHERE idprovince=" . $pro . " ORDER BY weight ASC";
    $result = $db->query($sql);
    $weight = 0;
    while ($row = $result->fetch()) {
        $weight++;
        $query = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_district SET weight=" . $weight . " WHERE id=" . $row['id'] . " AND idprovince=" . $pro;
        $db->query($query);
    }
}

/**
 * fix_wardWeight()
 *
 * @param mixed $dis
 * @return void
 */
function fix_wardWeight($dis)
{
    global $db, $module_data;

    $sql = "SELECT id FROM " . NV_PREFIXLANG . "_" . $module_data . "_ward WHERE iddistrict=" . $dis . " ORDER BY weight ASC";
    $result = $db->query($sql);
    $weight = 0;
    while ($row = $result->fetch()) {
        $weight++;
        $query = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_ward SET weight=" . $weight . " WHERE id=" . $row['id'] . " AND iddistrict=" . $dis;
        $db->query($query);
    }
}


define('NV_IS_FILE_ADMIN', true);
