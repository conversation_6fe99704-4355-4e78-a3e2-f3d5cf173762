<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 02:25:16 GMT
 */

if (!defined('NV_MAINFILE'))
    die('Stop!!!');

$lang_translator['author'] = "VINADES.,JSC (<EMAIL>)";
$lang_translator['createdate'] = "15/09/2011, 02:25";
$lang_translator['copyright'] = "@Copyright (C) 2011 VINADES.,JSC. All rights reserved";
$lang_translator['info'] = "";
$lang_translator['langtype'] = "lang_module";

// Page title
$lang_module['vilas'] = 'Danh sách phòng thí nghiệm được công nhận VILAS';
$lang_module['vicas'] = 'Danh sách các tổ chức chứng nhận bởi VICAS';
$lang_module['vias'] = 'Danh sách các tổ chức giám định bởi VIAS';
$lang_module['vilas-med'] = 'Danh sách phòng thí nghiệm y tế được công nhận VILAS-MED';
$lang_module['vipas'] = 'Nhà cung cấp thử nghiệm thành thạo VIPAS';
$lang_module['viras'] = 'Tổ chức sản xuất mẫu chuẩn bởi VIRAS';

// Page description
$lang_module['vilas_desc'] = 'Danh sách phòng thí nghiệm được công nhận theo hệ thống VILAS (Vietnam Laboratory Accreditation Scheme) với chi tiết các thông tin số hiệu, tên tổ chức, lĩnh vực, tình trạng,...';
$lang_module['vicas_desc'] = 'Danh sách các tổ chức chứng nhận được công nhận bởi tổ chức Công nhận Chất lượng VICAS (VIETNAM CERTIFICATION ACCREDITATION SCHEME) chi tiết và đầy đủ nhất.';
$lang_module['vias_desc'] = 'DauThau.info cập nhật đầy đủ danh sách các tổ chức giám định theo VIAS, một trong các chương trình công nhận của Văn phòng Công nhận Chất lượng (BoA)';
$lang_module['vilas-med_desc'] = 'DauThau.info cập nhật đầy đủ danh sách các phòng thí nghiệm y tế được công nhận bởi VILAS/ VILAS-MED với đầy đủ thông tin theo tỉnh thành, lĩnh vực, trạng thái';
$lang_module['vipas_desc'] = 'DauThau.info cập nhật đầy đủ danh sách các nhà cung cấp thử nghiệm thành thạo theo VIPAS với đầy đủ thông tin theo tỉnh thành, lĩnh vực, trạng thái';
$lang_module['viras_desc'] = 'DauThau.info cập nhật đầy đủ danh sách các tổ chức sản xuất mẫu chuẩn theo VIRAS với đầy đủ thông tin theo tỉnh thành, lĩnh vực, trạng thái, số hiệu, tên tổ chức';

// Page detail description
$lang_module['vilas_detail_desc'] = 'Thông tin chi tiết của các phòng thí nghiệm VILAS được DauThau.info cập nhật theo thời gian mới nhất và đầy đủ thông tin cùng các file tài liệu hệ thống rõ ràng';
$lang_module['vicas_detail_desc'] = 'Thông tin chi tiết của các tổ chức chứng nhận được công nhận bởi VICAS có đầy đủ thông tin theo lĩnh vực, vị trí trong và ngoài nước theo thời gian mới nhất';
$lang_module['vias_detail_desc'] = 'Thông tin chi tiết các tổ chức giám định được công nhận bởi VIAS (một trong những chương trình của BoA) có đầy đủ thông tin theo lĩnh vực, vị trí, thời gian mới nhất';
$lang_module['vilas-med_detail_desc'] = 'Thông tin chi tiết của các phòng thí nghiệm y tế được công nhận bởi VILAS/VILAS-MED có đầy đủ thông tin và file tài liệu được cập nhật theo thời gian mới nhất';
$lang_module['vipas_detail_desc'] = 'Thông tin chi tiết của các nhà cung cấp thử nghiệm thành thạo được công nhận bởi VIPAS có đầy đủ thông tin & file tài liệu được cập nhật theo thời gian mới nhất';
$lang_module['viras_detail_desc'] = 'Thông tin chi tiết của các tổ chức sản xuất mẫu chuẩn được công nhận bởi VIRAS có đầy đủ thông tin & file tài liệu được cập nhật theo thời gian mới nhất';


$lang_module['no_results'] = 'Không có kết quả';
$lang_module['wrong_filter'] = 'Bộ lọc sai';
$lang_module['keyword'] = 'Từ khóa';
$lang_module['num'] = 'Số hiệu';
$lang_module['org_name'] = 'Tên tổ chức';
$lang_module['host_name'] = 'Đơn vị chủ quản';
$lang_module['specialize'] = 'Lĩnh vực';
$lang_module['province'] = 'Tỉnh thành';
$lang_module['search'] = 'Tìm kiếm';
$lang_module['accreditation_location'] = 'Địa điểm công nhận';
$lang_module['content'] = 'Chi tiết';
$lang_module['last_update'] = 'Thời gian cập nhật';
$lang_module['status'] = 'Tình trạng';
$lang_module['dinh_chi'] = 'Đình chỉ';
$lang_module['hoat_dong'] = 'Hoạt động';
$lang_module['huy_bo'] = 'Hủy bỏ';
$lang_module['file_download'] = 'File Download';
$lang_module['date'] = 'ngày';
$lang_module['status_select'] = 'Chọn trạng thái';
$lang_module['stt'] = 'STT';
$lang_module['tra_cuu'] = 'Tra cứu';
$lang_module['plz_select'] = 'Vui lòng chọn';
$lang_module['select_hint'] = 'Giữ Ctrl để chọn liên tiếp';
$lang_module['cap_nhat_lai'] = '(Cập nhật lại)';
$lang_module['update_ok'] = 'Cập nhật thành công. Bạn vui lòng chờ khoảng 10p để thông tin được cập nhật thay đổi.';
$lang_module['update_err_new'] = 'Tổ chức mới được đăng lên hệ thống. Sau 30 phút mới có thể cập nhật lại.';
$lang_module['login_popup'] = 'Vui lòng <strong><a href="javascript:void(0)" data-toggle="loginFormShow" title="Để xem đầy đủ thông tin mời bạn Đăng nhập hoặc Đăng ký">Đăng nhập</a></strong> hoặc <strong><a href="%s" title="Để xem đầy đủ thông tin mời bạn Đăng nhập hoặc Đăng ký">Đăng kí</a></strong> thành viên để yêu cầu hệ thống cập nhật lại dữ liệu mới nhất';
$lang_module['update_err_user_last'] = 'Hãy quay lại sau %s';
$lang_module['effective_date'] = 'Ngày hiệu lực';
$lang_module['cancellation_date'] = 'Thời gian hủy';
$lang_module['time_query'] = '<br/>Tìm kiếm trong: %s - Số kết quả:  %s';
$lang_module['crawls_numbers'] = 'Cập nhật lần thứ %s.';
$lang_module['view_by_tt'] = 'Danh sách %s theo %s';
$lang_module['no_title'] = 'Chưa phân loại';
$lang_module['back'] = 'Trở lại';
$lang_module['note_wrong_page'] = 'Anh/Chị kiếm đâu ra cái link này? Em là em chịu, không thể xử lý được theo yêu cầu của anh/chị!';
$lang_module['notice'] = 'Thông báo';
$lang_module['note_max_searchpage'] = 'Số lượng kết quả tìm kiếm đang vượt quá giới hạn hiển thị của phần mềm là 100 trang. Vui lòng sử dụng công cụ tìm kiếm để thu hẹp phạm vi tìm kiếm hoặc quay lại trang trước.';
