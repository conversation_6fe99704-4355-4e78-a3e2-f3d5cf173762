<!-- BEGIN: main -->
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/js/apexcharts.js"></script>
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/js/loadMorekqlcnt.js"></script>
<div class="province-conent">
    <div class="row">
        <!-- BEGIN: province -->
        <div class="province-top">
            <div class="{CLASS_MOBILE} col-lg-12 col-md-14 col-sm-24 col-xs-24">
                <div class="tooltip"></div>
                <div class="tooltip_hoangsa"></div>
                <div class="tooltip_truongsa"></div>
                <!-- BEGIN: plan_pie -->
                <div
                    class="plan_chart plan_chart-{STATISTICS_PROVINCE.id} {STATISTICS_PROVINCE.class} {STATISTICS_PROVINCE.class_curent}">
                    <div id="plan_pie-{STATISTICS_PROVINCE.id}"></div>
                </div>
                <!-- END: plan_pie -->
                <div id="map_province" class="relative_province map_province">
                    <!-- BEGIN: province_list -->
                    <svg class="vietnammap custom-svg" width="381.07504" height="800.61719">
                        <path
                            d="m 62.338471,33.343229 1.76,0.42 1.04,1 1.11,0.33 0.23,0.67 1.03,0.14 0.38,1.17 -0.86,0.35 -0.11,1.07 1.83,2.05 -0.25,0.96 0.44,0.4 0.03,0.33 0.87,0.68 0.36,0.93 1,0.22 -0.03,0.55 0.93,0.63 0.31,-0.04 0,0 0.27,0.54 1.07,-0.08 0.9,0.44 -0.27,0.59 0.74,1.97 0.61,0.27 -0.7,1.96 -0.7,0.44 -0.62,0.89 0.88,1.2 0.82,0.68 0.12,0.45 0.73,0.22 0.08,0.9 1.23,0.69 0.87,-0.42 2.57,-0.25 0.33,0.14 1.37,-0.53 1.02,0.43 0.93,0.92 0.07,2 0.67,0.34 -0.1,0.39 0.22,0.2 -0.65,0.36 0.12,0.44 -0.52,0.7 0.58,0.18 0.07,0.39 0.89,0.21 1.84,1.73 0.06,0.43 0.61,0.72 0.09,1.22 0.48,0.32 0.31,0.75 0.76,0.39 0,0.54 -0.33,0.34 0.15,0.31 1.18,0.37 1.18,1.44 1.31,0.34 -0.12,1 0.43,0.79 -0.34,1.39 0.75,1.04 0.21,0.75 -0.18,1.31 0.63,0.75 -0.43,0.7 1.4,1.8 0,0 -0.75,0.42 -0.47,0.75 -0.07,0.57 -0.91,0.99 -0.73,-0.31 -0.49,-0.5 -0.68,0.6 -0.66,0.03 -0.4,0.77 0.16,2.32 1.57,1.51 -0.07,0.53 1.38,1.03 0.43,0.7 -0.05,0.51 0.86,1.05 -0.24,0.75 -0.61,0.11 0.12,0.72 -0.27,-0.15 -0.56,0.26 -0.31,-0.2 -0.87,0.15 0.15,0.67 -0.37,0.68 0.53,0.39 0.11,0.53 0,0 -0.89,0.33 -0.81,-0.39 -0.44,-0.42 -0.15,1.27 -1.32,-0.28 -0.27,-0.51 -0.52,-0.29 -0.24,-2.08 -0.37,0.46 -0.71,-0.3 -0.31,0.17 -0.91,-1.43 -0.36,0 -0.22,-0.87 -1.23,0.01 -0.08,-0.47 0.31,-1.17 -1.47,-0.13 -0.34,0.24 -0.68,-0.82 -0.79,-0.25 -0.01,-0.7 0.47,-0.21 0.89,-2.04 -0.34,-0.08 -0.05,-0.27 -0.49,0 0.04,-1.03 -1.17,-0.99 0.28,-1.03 -1.22,-0.59 -0.28,-1.47 -0.44,-0.69 -1.14,-0.04 -0.91,-0.68 -0.29,-0.56 -2.7,-0.59 -0.53,-0.38 -1,0.28 -0.67,0.76 0.09,1.01 -0.23,0.44 -0.47,0.26 -0.2,0.54 0,0 -1.21,-0.6 -0.4,-0.85 -0.66,-0.72 0.05,-0.35 0,0 0.73,-0.61 0,0 -0.72,-1.54 -0.49,-0.23 -0.43,0.17 -0.25,-0.58 -0.46,-0.38 -0.85,-3.15 -0.73,-0.77 0.01,-0.72 -0.19,-0.12 -2.92,0.17 -0.67,0.56 -0.14,0.57 -0.53,0.44 0.07,0.64 -1.08,0.32 -0.15,1.24 -0.55,0.65 0.1,0.46 0.83,0.98 -0.03,0.38 -0.39,0.24 -2.07,-0.59 -1.6,-1.13 -0.79,0.09 -1.38,-0.64 -0.99,0.21 -0.08,0.32 -0.28,0.07 -0.92,-0.82 -0.25,-0.69 -0.99,-0.4 -0.71,1.72 0.34,0.26 -0.05,0.48 -1.64,-0.32 -0.94,0.77 -0.49,-0.02 -0.68,-0.39 -0.57,0.32 -0.53,-0.1 -0.23,0.85 -0.51,0.59 -0.96,-0.26 -0.3,-0.36 0.14,-0.23 -0.21,-0.47 -0.86,-0.88 -0.72,-0.19 -0.08,-0.42 0.38,-0.52 -0.11,-0.53 -1.12,0.27 -1.26,0.88 -0.54,-0.32 -0.55,0.14 -0.38,1 -0.47,-0.35 -1.61,0.04 -0.53,-0.47 -1.72,-0.72 -0.1,-0.19 0.28,-0.56 -0.64,-0.88 -0.24,0.04 -0.93,-0.79 -0.31,-0.81 0.17,-0.42 -0.16,-1 0.48,-0.4 0.34,-1.73 -1.43,-1.69 -0.92,-0.04 -0.87,-0.42 -0.8,-0.87 -0.7,0.49 1.27,0.93 0.53,1.58 -0.09,0.56 -0.34,0.29 -0.61,1.42 -1.21,-0.46 0,0 -0.55,0.05 0,0 -0.39,-0.67 -0.42,-0.13 -0.49,-0.53 0.01,-2 -0.8,-0.29 -1.36,-1.16 -0.8,0.05 -0.51,0.91 -0.53,0.05 -0.96,-0.56 -0.52,-0.43 -0.29,-1.06 -0.94,-0.99 -0.91,-1.7 -0.89,-0.83 0.05,-0.41 -0.42,-0.61 -1.17,-0.56 -0.21,-0.82 -0.64,-0.15 -1.22,-1.24 0.27,-0.75 0.33,0.27 -0.08,-0.79 -0.28,-0.46 0.12,-1.06 -0.35,-0.45 -1.22,-0.4 -0.23,-0.82 -0.55,-0.04 0.01,-0.83 -0.47,-0.82 0.19,-0.48 -0.65,-0.36 -0.4,-0.55 -0.04,-0.77 0,0 1.44,-0.47 -0.44,-1.24 1.36,-0.31 1.51,-2.09 0.88,-0.26 -0.15,-0.35 -0.63,-0.11 -0.85,-1.49 -0.07,-0.74 0.57,-0.31 0.75,0.29 1.36,-0.94 0.44,-1.29 0.65,-0.44 -0.29,-0.56 0.05,-0.58 0.32,-0.52 0.48,-0.44 0.53,0 0.2,-0.52 0.93,-0.04 0.59,0.16 1.42,1.2 0.69,1.18 0.62,0.5 0.28,1.18 0.81,-0.14 0.18,-0.92 0.63,-0.53 0.32,-0.01 2.55,2.39 1.62,-0.66 1.03,0.45 0.2,0.74 0.73,0.1 0.61,0.69 0.79,-0.05 0.71,1.13 0.92,0.66 0.3,0.52 1.47,0.22 0.45,-0.26 1.03,0.41 0.92,0.47 0.13,0.51 0.73,0.63 -0.32,0.8 0.54,0.45 0.69,1.3 0.66,1.15 0.13,0.8 0.53,0.16 0.94,1.24 0.52,0.19 0.78,0.23 1.19,-0.07 0.47,0.99 1.75,0.98 1.2,-0.15 0.99,-0.03 0.41,-0.28 -0.07,-0.96 -0.41,-0.4 0.05,-0.78 0.4,-0.91 0.95,-0.51 1.06,-0.15 1.07,-1.23 0.49,-0.14 0.41,0.26 1.31,-0.99 -1.23,-2.02 0.6,-1.52 0.34,-0.56 0.37,-0.56 0.55,-0.54 0.73,0.23 1,-0.09 0.58,-1.01 0.69,-0.41 0.54,0.14 0.92,-0.48 0.1,-1.19 0.35,-0.58 -0.17,-1.4 1.22,-1.51 0.47,-1.07 0.13,-2.04 0.92,0.61 z"
                            id="tinh-lai-chau" data-id="tinh-lai-chau" data-link="{PROVINCE_LIST_302.link}"
                            data-tinh="{PROVINCE_LIST_302.title}" data-province="{PROVINCE_LIST_302.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 109.74847,31.493229 0.39,-0.28 0.65,0.37 -0.2,1 -0.6,2.41 0.14,0.64 0.56,0.75 0.06,0.66 0.46,0.01 1.2,0.67 1,-0.11 0.84,0.52 0.26,0.66 0,0 -0.3,1.15 1.31,0.87 1.89,-0.35 0.61,0.85 0.02,0.64 0.65,1.25 0.72,0.61 1.37,0.39 0.13,2.19 -0.47,0.19 -0.68,-0.3 -0.87,0.1 -0.21,1.71 -0.39,0.77 0.99,0.42 0.52,1.04 1.56,1.03 -0.46,0.84 -0.84,0.61 0.27,0.82 0.77,1.14 1.15,0.24 0.4,0.87 0.51,-0.03 2.1,1.26 0.34,0.52 -0.34,0.7 0.35,0.36 0.8,-0.07 0.59,0.36 -0.83,0.95 0.71,0.87 0.01,0.72 -1.03,1.06 -0.7,0.25 -0.13,0.34 0.45,1.55 0.56,0.2 0.61,-0.14 0.24,-0.39 0.38,0.12 0,0 -0.17,1.01 0.39,0.67 0.31,0.09 0.86,-0.43 0.76,0.35 -0.64,0.41 -0.06,0.45 0.58,1.77 -0.39,-0.34 -0.8,0.17 -0.21,-0.43 -0.47,0.02 -0.35,0.31 0,0.55 0.29,0.4 -0.43,0.28 0.23,0.62 -0.24,0.26 -0.1,0.98 0.22,0.49 -0.42,0.15 -0.15,0.66 -0.5,0.67 -0.67,-0.32 -0.21,-0.45 -0.92,-0.57 -0.61,-0.88 0,0 -0.58,-0.33 0,0 -1.03,-0.71 -0.67,-0.96 -0.69,-0.32 -0.29,-1.01 -0.69,0.01 -0.8,-0.76 -0.43,-0.06 -0.61,0.51 0.09,0.23 -0.34,0.15 -0.17,0.41 -0.64,0.07 0.4,0.89 0.39,0.16 0.13,0.53 -0.2,0.66 0.42,1.05 -0.26,-0.12 -0.54,0.84 -0.05,0.48 -0.54,0.22 0,1.11 -0.2,0.15 0.49,0.22 0.39,0.91 0.26,0.03 0.89,1.11 0.38,2.04 0.41,0.97 -0.16,1.04 0.27,0.55 -0.27,0.58 -2.07,1.3 -0.09,0.7 -1.64,1.35 -0.76,-0.58 -0.08,-0.4 -0.39,-0.34 -0.24,-1.09 -1.68,-0.51 -0.49,-0.7 -0.51,0.34 -0.94,0.12 -1.72,-0.91 -0.57,-0.05 -0.12,0.48 1.11,0.96 -0.18,0.74 0.18,0.42 -0.91,0.61 -0.95,0.22 -2.86,-0.49 -0.68,-0.58 -1.74,-0.53 -0.87,0.66 -1.819999,-0.69 -0.3,0.16 -1.35,-0.24 -0.68,-0.49 -0.35,-0.6 0.31,-0.42 -0.03,-0.51 -0.54,-0.69 0,0 -1.4,-1.8 0.44,-0.7 -0.63,-0.75 0.18,-1.31 -0.2,-0.75 -0.76,-1.05 0.34,-1.39 -0.43,-0.79 0.12,-1 -1.31,-0.34 -1.18,-1.44 -1.18,-0.37 -0.14,-0.31 0.33,-0.34 0,-0.53 -0.76,-0.39 -0.3,-0.76 -0.49,-0.32 -0.09,-1.22 -0.61,-0.72 -0.07,-0.43 -1.84,-1.73 -0.89,-0.21 -0.07,-0.39 -0.58,-0.19 0.53,-0.7 -0.12,-0.44 0.65,-0.36 -0.22,-0.2 0.1,-0.39 -0.67,-0.34 -0.07,-2 -0.93,-0.92 -1.02,-0.43 -1.37,0.53 -0.34,-0.13 -2.57,0.24 -0.86,0.42 -1.23,-0.69 -0.08,-0.9 -0.73,-0.22 -0.12,-0.45 -0.82,-0.68 -0.88,-1.2 0.62,-0.88 0.7,-0.45 0.7,-1.95 -0.61,-0.27 -0.73,-1.97 0.26,-0.59 -0.9,-0.44 -1.07,0.08 -0.27,-0.54 0,0 0.81,-1.03 0.3,-1.23 0.98,-0.39 0.43,-1.14 -0.78,-1.33 0.07,-1.04 1.66,-2.08 0.31,-0.71 0.32,-0.73 0.5,-0.32 -0.1,-0.62 0.25,-0.23 0.57,-0.08 0.35,-0.35 0.99,0.48 0.46,1.15 0.64,0.28 0.32,0.68 0.76,0.31 1.92,1.84 -0.11,0.55 0.58,0.38 0.1,0.34 0.93,0 2.34,2.8 0.59,1.27 0.78,0.6 0.52,0.75 0.71,0.42 0.73,1.11 0.35,0.13 0.41,-0.25 1.22,1.34 0.63,0.3 0.32,0.58 0.8,0.29 0.33,0.62 0.4,0.25 0.87,0.01 0.64,-0.69 0.66,0.11 0.39,-0.22 -0.31,-0.97 0.21,-0.31 0.04,-0.85 0.19,-1.14 0.89,-3.54 0.02,-0.62 -0.55,-0.32 -0.01,-0.86 0.51,-0.51 0.42,-2.18 0.39,-0.66 0.969999,-1.22 1.22,-0.81 0.31,-1.01 1.04,-1.27 0.67,-0.07 0.32,0.39 0.31,-0.01 0.74,-0.26 0.55,-0.58 0.54,0.06 0.94,-0.51 1.23,-0.12 1.31,-0.77 z"
                            id="tinh-lao-cai" data-id="tinh-lao-cai" data-link="{PROVINCE_LIST_205.link}"
                            data-tinh="{PROVINCE_LIST_205.title}" data-province="{PROVINCE_LIST_205.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 166.03847,0.9232294 0.91,0.8 0.14,1.33 0.97,1.47 0.76,0.15 1.28,1.48 0.78,-0.26 0.25,-0.44 0.45,0.02 0.43,0.52 0.29,0.91 0.79,0.89 0.75,0.1 -0.09,0.72 0.64,0.74 -0.05,0.6899996 0.51,0.78 0.01,0.37 1.09,0.51 1.08,-0.12 0.64,0.45 0.13,0.63 0.63,0.67 0.67,0.22 0.12,0.46 0.35,0.26 -1.43,1.53 0,0 -0.7,0.51 -1.19,0.1 -0.57,1.37 0.12,0.47 -0.62,0.87 -0.24,1.02 -0.45,0.5 -0.33,1.33 -1.06,0.12 -0.95,0.47 -1.65,-0.12 -0.27,0.35 -0.69,0.22 -1.27,0.07 -0.71,0.69 -0.04,0.34 0.3,0.41 -0.3,0.88 0.21,0.46 -0.3,0.24 -1.16,0.21 -0.65,0.95 -0.64,0.12 -0.3,0.79 0.04,1.39 -1.82,-0.12 0.07,0.86 0.57,0.31 1.27,0.21 0.54,0.73 1.25,0.64 0.17,0.61 0.79,-0.05 1.33,0.37 0.69,0.95 1.11,0.51 0.21,0.41 0.9,0.28 0.46,0.79 1.12,0.23 0.73,2.63 0.63,0.69 -0.54,0.39 -0.14,0.66 0.48,1.02 0.14,1.31 0,0 -0.61,0.88 -0.54,-0.12 -1.33,0.73 -0.22,-0.29 -0.22,0.44 0,-0.29 -0.29,0.05 0.02,0.26 -0.41,-0.27 -1.01,-0.16 -0.78,-1.02 -0.52,0.16 -1.21,-0.67 -1.63,-0.05 -0.28,0.18 -0.09,0.68 -0.27,0.13 -0.06,1.03 -0.29,-0.22 0.01,-0.28 -1.12,-0.02 -0.18,-0.19 0.09,-0.48 -0.32,-0.52 0.1,-0.22 -0.59,-0.51 -1.13,-0.33 -0.36,0.26 -0.12,-0.41 -0.58,-0.44 -0.58,0.16 -0.42,-0.26 -0.31,-0.66 -1.64,-0.32 -0.2,-0.31 -1.14,-0.4 -0.47,0.46 -0.2,0.89 -0.26,0.14 0.48,0.31 0.02,0.44 0.4,0.32 0.62,1.59 -0.38,0.19 -0.48,-0.39 -0.2,0.69 -0.65,0.47 0.33,0.89 -1,0.78 0.33,0.43 0.38,1.74 -1.05,2.53 0.26,0.41 -0.05,1.04 0.32,0.51 -1.59,0.94 0.48,0.55 0.25,0.83 -0.5,1.02 -0.74,0.04 -0.18,0.46 -0.04,0.28 0.72,1.17 -0.18,0.42 -1.87,-0.19 -0.32,0.26 0.55,0.94 0.48,0.31 -0.05,0.98 -0.77,0.08 0.17,1.24 -0.94,0.35 -0.77,0.84 -0.26,0.03 -0.3,-0.97 0.11,-0.79 -0.56,-0.18 -0.14,-0.7 -0.53,-0.53 -0.72,0.26 -0.97,-0.08 -0.63,-0.69 -1.05,-0.03 -0.57,-0.95 -0.39,0.08 -0.14,1.07 -0.29,0.38 0.29,0.43 0.72,0.18 0.36,0.49 -0.13,0.4 0.33,0.32 -0.49,0.11 -0.35,0.76 -0.51,-0.06 -0.21,0.22 0.77,0.77 0.09,0.41 0.45,0.29 0.05,0.33 -0.35,0.47 0.13,0.88 -0.17,0.27 -0.28,-0.15 -0.11,0.15 -0.5,1.58 -0.75,-0.17 -0.2,-0.85 -0.57,-0.07 -0.19,0.31 -0.19,-0.09 -0.42,0.75 0,0 0,0 0,0 0,0 0,0 -0.98,0.43 -0.67,-0.47 0.09,0.33 -0.31,0.4 -0.69,0.15 -0.85,-0.14 -0.2,0.09 0.01,0.29 -0.88,-0.1 -0.35,-0.35 -0.05,-0.65 -0.7,0 -0.39,-0.69 -0.51,-0.11 -0.2,-0.84 0.48,-0.6 0,-0.53 -0.39,-1.25 -0.93,-0.37 -0.97,0.14 -0.47,-0.19 -0.34,-0.66 -0.51,0.18 -0.51,-0.38 -0.44,0.19 -0.65,-0.56 -0.63,-0.01 -0.18,-0.24 -0.39,0.04 -1.08,1.5 0.08,0.8 0,0 -0.38,-0.12 -0.24,0.39 -0.61,0.14 -0.56,-0.2 -0.45,-1.55 0.13,-0.34 0.7,-0.25 1.03,-1.06 -0.01,-0.72 -0.71,-0.87 0.83,-0.95 -0.59,-0.36 -0.8,0.07 -0.35,-0.36 0.34,-0.7 -0.34,-0.52 -2.1,-1.26 -0.51,0.03 -0.4,-0.87 -1.15,-0.24 -0.77,-1.14 -0.27,-0.82 0.84,-0.61 0.46,-0.84 -1.56,-1.03 -0.52,-1.04 -0.99,-0.42 0.39,-0.77 0.21,-1.71 0.87,-0.1 0.68,0.3 0.47,-0.19 -0.13,-2.19 -1.37,-0.39 -0.72,-0.61 -0.65,-1.25 -0.02,-0.64 -0.61,-0.85 -1.89,0.35 -1.31,-0.87 0.3,-1.15 0,0 0.79,0.97 1.13,-0.1 0.48,-0.6 0.57,0.15 0.59,-1.39 0.76,-0.66 2.43,-1.08 1.45,-1.6 0.95,-0.57 0.59,-1.26 0.44,-0.44 0.49,-0.71 1.61,-0.77 0.64,0.02 0.35,0.33 0.38,1.02 -0.33,0.28 0.06,0.39 0.9,-0.14 0.38,-0.63 -0.3,-0.33 0.31,-0.59 0.38,0.59 1.31,0.15 0.86,0.88 1.48,-0.48 1.17,0.34 0.44,-0.16 0.19,-0.48 -0.55,-1.06 0.67,-0.48 0.96,0.02 1.1,-2.58 0.72,-0.06 1.55,-1 1,0.09 1.12,-1.43 -0.58,-0.94 -0.65,-0.51 -0.46,-0.52 0.02,-0.93 -0.06,-1.5 -0.43,-0.63 -0.33,-1.22 -0.09,-0.74 -0.31,-0.63 -0.24,-2.16 -0.32,-0.41 1.74,-0.35 0.56,0.28 0.73,-0.15 0.35,0.17 0.65,0.12 0.47,-0.98 -0.06,-1.15 0.53,-0.59 1.33,-0.53 1.72,0.71 0.37,-0.57 0.14,-1.26 1.34,-0.33 0.14,0.39 0.5,-0.01 0.39,-0.8 1.09,-0.17 0.96,-0.8599996 0.47,-0.77 0.75,-0.41 0.91,-0.71 0.72,0.27 0.28,0.52 -0.16,0.37 0.63,0.15 0.69,-0.43 0.4,-0.21 1.14,-0.5 0.59,-0.88 0.26,-0.32 0.47,0.08 0.88,0.4 0.12,0.4 0.59,0.32 0.68,-0.2 0.22,-0.28 0.28,-0.58 0.81,-0.69 -0.02,-0.6 0.4,-0.28 -0.39,-1.02 0.16,-0.32 0.27,-0.3 0.75,0.19 0.54,-0.16 0.21,-0.86 0.56,-0.59 -0.04,-0.56 -0.46,0.06 -0.15,-0.31 0.7,-0.2 0.57,0.24 0.7,-0.61 z"
                            id="tinh-ha-giang" data-id="tinh-ha-giang" data-link="{PROVINCE_LIST_201.link}"
                            data-tinh="{PROVINCE_LIST_201.title}" data-province="{PROVINCE_LIST_201.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 178.29847,16.363229 0.18,0.87 -0.08,0.61 -0.01,0.38 0.42,0.37 1.24,-0.47 0.88,0.49 0.67,-0.63 0.61,-0.13 0.52,0.41 0.79,0.19 0.46,0.43 2.04,-0.05 0.9,0.09 0.09,0.3 -0.29,0.56 0.45,0.82 1.21,0.06 0.11,0.29 0.86,0.25 0.52,0.97 0.62,0.57 1.23,-0.08 0.6,0.52 0.6,0.53 0.35,0.49 0.46,0.91 -0.12,0.69 0.54,0.44 0.32,0.18 0.55,-0.44 1.59,-1.04 0.46,0.28 0.56,0.68 0.85,-0.03 0.02,-0.46 0.42,-0.14 0.85,0.19 0.61,0.42 0.32,-0.12 0.19,-0.3 -0.1,-0.75 0.02,-1.04 0.13,-0.76 1.89,-0.1 0.49,-0.08 1.85,-0.29 0.41,0.1 0.65,0.7 0.79,-0.05 0.61,-0.15 0.17,-0.33 0.52,-0.05 1.11,0.65 0.71,0.11 0.94,-0.37 0.68,1.03 0.7,0.41 0.6,0.6 0.51,0.7 0.34,1.08 0.37,0.12 0.55,0.48 0.33,0.77 0.09,0.63 -0.32,0.52 0.78,0.5 0.36,-0.39 0.9,0.6 0.69,-0.74 1.07,1.14 0.34,0.05 0.62,-0.61 0.05,-0.71 0.31,-0.29 1.73,0.23 0.27,-0.34 0.55,-0.32 0.47,0.33 0.69,-0.22 0.31,-0.74 0.36,-0.02 0.53,0.15 0.35,0 0.53,-0.14 0.65,-0.24 0.5,-0.36 0.15,-0.95 0.3,-0.85 0.62,-0.09 0.95,0.32 0.1,0.76 1.09,0.56 1.13,-0.73 0.54,-0.07 0.4,0.38 0.43,1.23 0.61,0.37 0.32,0.4 0.35,0.25 -0.06,0.53 0.98,0.47 0.54,-0.4 -0.05,-0.66 0.24,-0.3 0.32,0.02 0.9,-0.05 0.24,0.24 0.71,0.11 0.39,0.77 -0.48,0.23 -0.03,0.24 1.69,1.18 0.62,0.72 0.71,0.28 0.3,0.54 0.59,0.28 1.13,-0.48 0.82,0.17 0.84,0.82 -0.08,0.51 -0.65,0.42 -0.01,0.41 -0.32,0.38 -0.7,0.24 -0.47,-0.08 -0.38,0.9 -1.2,0.65 -0.02,0.56 0.3,0.5 0.55,0.9 -0.77,0.28 -0.33,0.36 0.07,0.52 -0.17,0.6 -0.38,0.29 -0.43,-0.04 -0.26,0.42 -0.17,0.52 -0.51,1.92 0.37,0.47 -0.12,0.78 0.24,0.36 -1.27,0.7 -0.63,-0.29 -0.81,0.66 -0.75,-0.35 -0.95,0.02 -0.76,-0.75 -0.23,-0.8 -1.11,0.21 -0.22,0.65 -0.13,0.78 0.12,0.65 -0.18,0.68 -0.47,0.13 -0.04,0.84 -0.43,0.84 0.1,2.42 -0.55,0.26 -0.39,0.16 -0.18,0.34 -0.19,0.7 0.52,0.98 -0.42,0.6 0,0 -1.36,0.15 -0.38,0.35 0.21,0.28 -0.66,0.54 -0.78,-0.39 -0.72,0.2 0.14,0.37 -0.51,0.26 0.35,0.34 -0.58,0.28 0.5,0.19 -0.07,0.39 -1.08,0.54 -0.8,0.13 -0.79,-0.38 -0.57,0.25 -0.57,-0.6 -0.71,-0.01 -0.23,-0.2 -0.94,0.8 -0.84,0.14 -1.08,-1.03 0.17,-0.92 -0.57,-0.56 0,-0.46 -0.26,-0.15 -0.42,0.27 -0.39,-0.11 -0.88,-0.89 -0.43,-0.06 -0.57,-1.17 -0.65,-0.31 -0.28,0.16 -1.01,-0.61 -0.43,0.35 -0.76,0.18 0,0 -0.31,0.62 0,0 -0.26,0.14 -0.66,1.27 -0.36,1.49 0,0 -0.38,-0.12 0.04,-0.5 -0.51,-0.06 -0.03,-0.3 -0.48,-0.13 -0.97,0.86 -0.92,-0.43 -0.61,0.18 -0.23,-0.14 -0.29,-0.7 0.07,-1.16 -0.38,-0.52 1.32,-1.57 -0.21,-0.61 0.23,-1.48 -0.97,-0.36 0.07,-0.34 -0.37,-0.41 0.31,-0.31 -0.42,-0.13 0.12,-0.55 -0.44,-0.31 -0.66,-0.03 0.01,-0.43 0.42,-0.38 -0.05,-0.32 -0.79,0.2 -0.95,-0.46 -0.45,0.84 -1.71,0.46 -0.21,0.36 -1.7,0.69 -0.25,0.46 -0.52,0.27 -0.47,-0.29 -0.73,0.04 -0.4,0.36 -0.74,1.43 -0.37,0.01 -0.33,-0.65 -1.18,-0.41 -0.3,-0.38 -1.52,0.31 -0.38,-0.03 -0.07,-0.59 -0.54,0.37 -0.89,-0.25 -0.9,-1.5 -0.75,0.07 0.1,-0.22 -0.3,-0.09 0.28,-0.22 -0.14,-0.33 0.43,-0.4 -0.08,-0.28 -0.59,-0.59 -0.76,0.09 -0.31,-0.44 0.43,-0.77 -0.63,-0.69 -0.05,-0.39 -0.61,-0.22 -0.01,-0.38 0.58,-0.25 0.58,-1.26 1.2,-1.08 0.2,-0.43 -0.96,-0.36 -0.37,-0.79 0.37,-0.45 -0.05,-0.34 -0.4,-0.02 -0.56,-0.77 -0.65,0.14 -0.29,0.44 -0.54,-0.1 -0.5,0.67 -0.29,-0.5 -0.25,0.63 -0.98,-0.41 -0.48,0.09 -0.82,1.41 -1.46,-0.08 -0.08,0.23 -0.45,0.08 -0.4,0.33 0.16,0.97 -0.45,0.01 -0.53,-0.35 -0.31,0.66 -0.6,-0.09 -1.21,0.49 -0.3,0.34 0.01,0.8 -0.49,0.19 0,0 -0.91,-0.17 -0.8,0.13 0,0 -0.14,-1.31 -0.48,-1.02 0.14,-0.66 0.54,-0.39 -0.63,-0.69 -0.73,-2.63 -1.12,-0.23 -0.46,-0.79 -0.9,-0.28 -0.21,-0.41 -1.11,-0.51 -0.69,-0.95 -1.33,-0.37 -0.79,0.05 -0.17,-0.61 -1.25,-0.64 -0.54,-0.73 -1.27,-0.21 -0.57,-0.31 -0.07,-0.86 1.82,0.12 -0.04,-1.39 0.3,-0.79 0.64,-0.12 0.65,-0.95 1.16,-0.21 0.3,-0.24 -0.21,-0.46 0.3,-0.88 -0.3,-0.41 0.04,-0.34 0.71,-0.69 1.27,-0.07 0.69,-0.22 0.27,-0.35 1.65,0.12 0.95,-0.47 1.06,-0.12 0.33,-1.33 0.45,-0.5 0.24,-1.02 0.62,-0.87 -0.12,-0.47 0.57,-1.37 1.19,-0.1 0.7,-0.51 0,0 z"
                            id="tinh-cao-bang" data-id="tinh-cao-bang" data-link="{PROVINCE_LIST_203.link}"
                            data-tinh="{PROVINCE_LIST_203.title}" data-province="{PROVINCE_LIST_203.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 70.638471,80.133229 0.2,-0.54 0.47,-0.26 0.23,-0.43 -0.09,-1.01 0.67,-0.75 1,-0.28 0.53,0.38 2.7,0.59 0.29,0.56 0.91,0.68 1.14,0.05 0.44,0.69 0.28,1.47 1.22,0.59 -0.28,1.03 1.17,1 -0.04,1.03 0.49,0 0.05,0.27 0.34,0.08 -0.89,2.04 -0.47,0.22 0.01,0.71 0.79,0.26 0.68,0.82 0.34,-0.24 1.47,0.14 -0.31,1.17 0.08,0.47 1.23,-0.01 0.22,0.87 0.36,0 0.92,1.43 0.31,-0.17 0.71,0.29 0.37,-0.46 0.24,2.08 0.52,0.29 0.27,0.51 1.32,0.28 0.15,-1.27 0.37,0.44 0.82,0.39 0.89,-0.33 0,0 0.77,0.47 -0.03,1.18 1.26,-0.01 0.37,0.33 0.81,-0.79 0.16,0.23 0.43,-0.1 0.52,0.19 0.12,0.35 0.39,-0.25 0.59,0.07 0.05,-0.18 1.1,0.83 1.479999,0.38 0.65,0.45 0.64,-0.27 0.65,0.05 0.58,-0.31 1.68,-0.19 0.84,0.39 0.98,0.06 0.55,-0.45 0.47,-0.81 0.35,-0.14 1.8,0.12 0.33,0.41 1.43,0.1 0.45,0.48 0.96,-0.19 1.23,0.18 0.06,0.65 -0.49,0.22 -1.3,1.630001 -0.95,0.48 0.54,1.57 -0.47,0.15 -0.62,0.96 0.86,1.43 0.08,0.82 -0.53,1.05 -0.1,1.11 0.25,0.47 0.76,0.48 -0.16,0.88 1.07,0.58 0.62,1.15 0.64,-0.11 -0.08,-0.31 0.3,-0.15 1.01,1.04 0.78,-0.16 0.47,0.14 0.3,0.43 1.08,0.65 0.1,0.99 0.62,0.51 0.67,-0.22 1.28,0.36 0.38,-0.08 1.44,-1.32 1.3,-2.03 1.61,0.23 0.5,-0.22 0.49,0.25 0.17,-0.53 0.34,-0.25 0.92,0.41 0.47,0.74 1.53,1.02 0.39,-0.09 1.29,0.56 0.54,-0.35 0.13,-1.03 0.36,0.01 0.39,-0.31 0.29,0.32 0.54,0.01 0.66,-0.38 0.06,1.38 0.69,0.8 0.09,0.88 0.38,-0.07 0.01,0.27 0.6,0.11 0.02,0.87 0.51,0.24 0.78,-0.48 0.5,0.22 0.68,-0.23 0,0 0.15,0.71 1.27,0.75 -0.1,2.6 0.46,0.37 -0.04,0.55 0.33,0.74 -0.29,0 -0.27,0.43 0.6,0.33 0.41,0.65 -0.65,1.53 0.39,0.35 -0.33,1.21 0.2,0.27 0.64,0.13 0.6,0.6 0.77,0.24 0.42,0.54 0.44,0.11 0.09,0.3 -0.15,0.01 0,0 -1,0.21 -0.82,0.49 -0.46,1.26 -0.54,-0.21 -0.23,0.16 0.28,1.29 0.49,1.27 0.71,0.43 0.45,0.73 0.28,1.22 1.38,1.24 0.93,1.8 2.02,1.46 0.07,0.77 1.13,2 -0.22,0.53 0.48,0.09 -0.1,1.02 -0.94,0.68 0.38,0.15 0.43,-0.15 1.22,0.65 -0.66,0.94 -0.37,-0.22 -0.36,1.33 -0.35,0.36 0.11,0.44 1.02,0.53 -0.01,0.43 -0.5,0.23 -0.91,0.1 -0.33,-0.18 -0.35,0.21 -0.99,-0.72 -0.4,0.2 -0.37,-0.44 -0.8,0.02 -0.35,-0.52 -0.7,-0.03 -1.12,-0.76 -0.4,0 -0.78,0.77 -1.06,0.22 -0.12,0.83 0.42,0.2 -0.32,0.86 0.55,0.68 -0.67,0.34 0.27,0.62 -0.24,1.41 0.49,0.44 0,0 0.06,0.14 -1.4,0.77 -0.2,0.31 -0.2,-0.13 -0.71,0.79 0.12,2.01 0,0 -0.93,0.02 0,0 -0.37,0.47 -0.69,-0.51 -0.39,0.4 -0.67,0.11 -0.69,-0.75 -0.32,-0.88 -0.38,-0.11 -0.92,0.14 -0.15,0.31 -0.45,0.11 -0.08,-0.92 -0.62,-0.56 -0.48,-0.11 -0.31,-0.74 -0.37,-0.14 -0.57,-0.75 0,0 -0.12,-1.03 -1.77,0.05 -0.9,-0.49 -0.08,-0.98 -1.04,-1.03 -0.69,-0.19 -1.07,0.55 -0.37,0.58 -0.83,-0.53 -0.47,-0.22 -0.52,-0.88 -0.29,-2.11 -0.62,-0.37 -0.96,-0.4 -0.23,-0.93 -0.82,0.09 -0.29,0.31 -1.75,-1.75 -0.68,0.14 -0.34,-0.12 -1.72,-1.64 -0.63,-0.29 0.33,-0.82 -0.11,-0.55 -1.09,-1.28 -0.85,-0.32 -0.21,-0.72 -0.57,-0.12 -0.76,0.42 -0.47,1.06 -0.9,-0.71 0.12,-1.22 -2.38,-0.66 -0.3,-0.62 -1.86,0.11 -0.87,0.38 -0.37,-0.05 -0.31,-0.99 -0.95,-0.59 -0.76,0.72 -1.02,0.2 -1.039999,0.58 0.3,0.57 -0.47,0.65 0.04,0.71 -0.97,0.7 -1.77,-0.07 -0.39,-0.33 -0.53,0.07 -0.97,0.58 -0.71,0.22 -0.5,-0.42 -1.99,-0.12 -1.31,1.05 -1.15,1.39 -0.96,-0.59 -1.06,0.32 0.09,0.58 -0.72,0.27 0.45,1.09 -0.05,0.35 -0.6,0.27 -0.9,0.9 0.04,0.68 0.48,0.55 -0.35,0.81 0.38,1 -1,0.71 -0.78,0.18 -1.17,-0.09 -0.22,0.66 0.34,0.51 -0.05,1.01 0.38,0.32 0.11,0.62 -0.52,0.46 -0.94,0 -1.31,0.38 -0.46,0.01 -0.37,-0.36 -0.05,-0.86 -0.36,-0.89 -0.58,0.23 -0.97,-1.32 -1.45,-0.64 -0.43,-0.72 -1.96,-0.31 -0.65,0.27 -0.73,-0.49 -0.78,0.23 -0.67,-0.28 -0.41,-1.14 -0.96,-1.15 0.09,-0.88 -0.92,-0.87 -0.79,0.28 -0.41,0.68 -0.18,0.85 -1.39,-0.14 -0.71,0.73 -0.81,-0.17 -1,-1.12 -0.77,0.21 -1.45,-0.87 -1.15,-0.17 -0.07,-0.4 -0.45,-0.5 -1.14,0.82 -1.64,-0.6 -0.46,-0.53 -0.09,-0.46 0.44,-1.21 -1.08,-0.96 0.38,-0.28 0,0 1.39,0.09 1.3,-0.47 0.46,0.27 0.46,-0.55 -0.49,-0.74 -0.17,-0.76 0.45,-0.59 0.78,-0.11 0.02,-1 -0.63,-1.51 -0.32,-0.24 0.08,-0.14 -0.28,0 0.42,-0.6 -0.08,-0.33 0.41,-0.23 -0.48,-1.04 0,0 -0.3,-0.36 0,0 -0.06,-0.92 0.27,0.05 0.02,-0.2 1,0.2 -0.62,-0.93 0.13,-0.32 -0.2,-0.54 0.3,-0.17 2.15,0.37 1.93,1.51 1.65,0.15 1.2,-0.46 -0.89,-1.04 -0.39,-1.01 -0.35,-2.3 0.63,-0.4 0.33,-0.62 0.75,0.13 0.24,-0.27 0.21,-1.33 -0.25,-0.95 0.14,-1.75 -0.22,-0.6 0.39,-0.75 0.08,-1.14 -0.59,-1.48 -1.81,-0.32 -1.4,-0.82 1.09,-1.72 -0.2,-0.24 -1.21,-0.21 -0.17,-0.84 -0.86,-0.71 -0.49,-0.8 0.66,-0.82 0.54,0.26 1.17,-0.26 0.08,-0.34 0.75,-0.04 1.25,0.53 0.28,-0.2 1,-0.03 0.37,-0.31 -0.12,-0.38 0.48,-0.98 -0.4,-1.45 0.19,-0.78 0.46,-0.6 -0.04,-0.34 1.25,-0.19 0.87,-0.62 0.88,-0.01 0.2,-0.7 -0.08,-0.95 0.67,-0.28 -0.15,-1.03 0.39,-0.32 0.1,-0.47 0.46,-0.17 0.56,-1.270001 -0.92,-1.15 0.73,-0.47 0.34,-0.78 0.75,-0.79 0.33,-0.08 0.55,0.46 0.2,-0.25 -1.31,-3.29 -0.56,-0.57 -0.17,-0.66 -0.87,-0.85 0.27,-1.1 -0.5,-0.99 0.19,-0.53 -0.59,-0.57 -0.38,-1.43 -0.83,-0.63 -0.75,-0.14 -0.14,-0.84 -0.27,-0.31 0.16,-0.46 -0.4,-0.33 0.16,-1.01 0.35,-0.3 -0.24,-0.23 0.63,-0.38 0.04,-0.35 0.36,-0.3 -0.44,-0.93 z"
                            id="tinh-son-la" data-id="tinh-son-la" data-link="{PROVINCE_LIST_303.link}"
                            data-tinh="{PROVINCE_LIST_303.title}" data-province="{PROVINCE_LIST_303.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 127.26847,64.503229 -0.08,-0.8 1.08,-1.5 0.39,-0.04 0.18,0.24 0.63,0.01 0.65,0.56 0.44,-0.19 0.51,0.38 0.51,-0.18 0.34,0.66 0.47,0.19 0.97,-0.14 0.93,0.37 0.39,1.25 0,0.53 -0.48,0.6 0.2,0.84 0.51,0.11 0.39,0.69 0.7,0 0.05,0.65 0.35,0.35 0.88,0.1 -0.01,-0.29 0.2,-0.09 0.85,0.14 0.69,-0.15 0.31,-0.4 -0.09,-0.33 0.67,0.47 0.98,-0.43 0,0 0.34,0.83 -0.08,0.72 0.5,0.83 -0.21,0.78 0.27,1.75 0.89,1.38 0.76,0.07 0.34,0.41 0.5,-0.09 0.29,0.37 0.49,-0.17 0.37,0.18 0.36,0.35 -0.05,0.45 0.31,0.48 0.49,0.04 0.3,0.31 0.3,-0.11 0.31,-0.53 0.86,-0.24 0.4,0.13 -0.28,1.25 0.21,0.31 -0.35,0.68 0.06,0.5 0.47,0.32 -0.38,0.89 0.05,1.1 0,0 -0.21,0.39 0,0 -0.15,0.49 0.57,0.44 0.64,-0.48 0.49,0.08 0.15,1.03 -0.41,0.56 0.01,0.51 0.59,0.35 0.58,0.03 0.28,0.47 0.1,1.01 0.73,0.35 0.93,1.51 0.65,0.17 0.57,0.78 -0.6,0.43 -0.4,1.1 -0.47,0.35 0.5,0.51 -0.36,0.77 -0.23,0.32 -0.68,0.23 1.02,0.67 0.02,0.72 0.38,-0.06 0.13,0.31 0,0 0.29,0.33 -0.27,0.56 0.5,0.11 0.19,0.59 -0.28,0.64 -0.37,-0.27 -0.78,0.05 -0.58,-0.42 -0.17,0.23 -0.44,-0.14 -0.99,-0.74 -0.25,0.33 -0.26,-0.04 -0.08,0.46 -1.02,0.02 0.06,0.44 -1.08,0.4 0.33,0.24 -0.04,0.34 -0.65,0.29 -0.07,0.59 -0.79,-0.82 -0.96,0.5 -0.18,0.54 -0.77,0.09 -0.5,0.35 -0.31,0.57 0.1,0.860001 -0.97,-0.2 0.32,0.43 0.26,2.22 -0.62,0.41 -0.72,0.06 -0.26,0.44 0.03,0.34 0.51,0.38 0.36,0.7 -0.02,1.25 0.64,1.75 0.77,1.26 0,0.68 -0.32,0.34 0.11,0.74 -0.67,1.19 -0.61,0.11 -0.07,0.46 0.13,0.6 0.47,0.38 0.37,0.79 -0.23,0.37 -0.69,0.12 -0.74,-0.64 -0.26,-0.62 -0.26,-0.02 -0.27,0.31 0.03,0.43 -0.49,0.01 0.25,0.7 -1.44,0.02 -0.28,0.21 0,0 -0.68,0.22 -0.5,-0.22 -0.78,0.49 -0.52,-0.24 -0.02,-0.87 -0.59,-0.11 -0.01,-0.28 -0.38,0.08 -0.09,-0.88 -0.69,-0.8 -0.06,-1.38 -0.66,0.38 -0.54,0 -0.29,-0.32 -0.39,0.31 -0.36,-0.02 -0.14,1.04 -0.54,0.35 -1.29,-0.56 -0.39,0.09 -1.53,-1.02 -0.47,-0.74 -0.92,-0.41 -0.34,0.25 -0.17,0.54 -0.49,-0.25 -0.5,0.22 -1.61,-0.23 -1.31,2.04 -1.43,1.32 -0.39,0.08 -1.28,-0.36 -0.67,0.22 -0.61,-0.5 -0.1,-0.99 -1.08,-0.65 -0.3,-0.43 -0.47,-0.14 -0.79,0.17 -1.01,-1.04 -0.29,0.15 0.08,0.31 -0.64,0.11 -0.61,-1.14 -1.07,-0.59 0.16,-0.87 -0.75,-0.48 -0.25,-0.47 0.1,-1.11 0.53,-1.05 -0.08,-0.82 -0.86,-1.43 0.62,-0.96 0.47,-0.16 -0.54,-1.57 0.95,-0.48 1.3,-1.630001 0.49,-0.22 -0.06,-0.64 -1.23,-0.19 -0.96,0.19 -0.45,-0.48 -1.44,-0.1 -0.32,-0.4 -1.8,-0.12 -0.35,0.14 -0.47,0.81 -0.56,0.46 -0.98,-0.07 -0.84,-0.38 -1.69,0.18 -0.58,0.32 -0.65,-0.05 -0.64,0.26 -0.65,-0.44 -1.479999,-0.38 -1.1,-0.83 -0.05,0.19 -0.58,-0.07 -0.39,0.24 -0.13,-0.35 -0.51,-0.19 -0.43,0.1 -0.15,-0.23 -0.82,0.79 -0.36,-0.32 -1.27,0 0.03,-1.18 -0.77,-0.47 0,0 -0.11,-0.53 -0.53,-0.39 0.37,-0.68 -0.15,-0.67 0.87,-0.16 0.31,0.21 0.56,-0.27 0.27,0.15 -0.11,-0.72 0.61,-0.1 0.25,-0.75 -0.86,-1.05 0.05,-0.51 -0.43,-0.7 -1.37,-1.04 0.07,-0.53 -1.57,-1.51 -0.16,-2.32 0.4,-0.77 0.66,-0.04 0.68,-0.6 0.49,0.5 0.73,0.31 0.91,-0.99 0.08,-0.57 0.47,-0.75 0.75,-0.43 0,0 0.54,0.69 0.03,0.51 -0.31,0.42 0.35,0.6 0.68,0.49 1.35,0.24 0.3,-0.16 1.819999,0.69 0.87,-0.66 1.74,0.53 0.68,0.58 2.86,0.49 0.95,-0.22 0.91,-0.61 -0.18,-0.42 0.18,-0.74 -1.11,-0.96 0.12,-0.48 0.57,0.05 1.72,0.91 0.94,-0.12 0.51,-0.34 0.49,0.7 1.68,0.51 0.24,1.09 0.39,0.34 0.08,0.4 0.76,0.58 1.64,-1.35 0.09,-0.7 2.07,-1.3 0.27,-0.58 -0.27,-0.55 0.16,-1.04 -0.41,-0.97 -0.38,-2.04 -0.89,-1.11 -0.26,-0.03 -0.39,-0.91 -0.49,-0.22 0.2,-0.15 0,-1.11 0.54,-0.22 0.05,-0.48 0.54,-0.84 0.26,0.12 -0.42,-1.05 0.2,-0.66 -0.13,-0.53 -0.39,-0.16 -0.4,-0.89 0.64,-0.07 0.17,-0.41 0.34,-0.15 -0.09,-0.23 0.61,-0.51 0.43,0.06 0.8,0.76 0.69,-0.01 0.29,1.01 0.69,0.32 0.67,0.96 1.03,0.71 0,0 0.58,0.33 0,0 0.61,0.88 0.92,0.57 0.21,0.45 0.67,0.32 0.5,-0.67 0.15,-0.66 0.42,-0.15 -0.22,-0.49 0.1,-0.98 0.24,-0.26 -0.23,-0.62 0.43,-0.28 -0.29,-0.4 0,-0.55 0.35,-0.31 0.47,-0.02 0.21,0.43 0.8,-0.17 0.39,0.34 -0.58,-1.77 0.06,-0.45 0.64,-0.41 -0.76,-0.35 -0.86,0.43 -0.31,-0.09 -0.39,-0.67 z"
                            id="tinh-yen-bai" data-id="tinh-yen-bai" data-link="{PROVINCE_LIST_213.link}"
                            data-tinh="{PROVINCE_LIST_213.title}" data-province="{PROVINCE_LIST_213.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 140.87847,68.103229 0.42,-0.75 0.19,0.09 0.19,-0.31 0.57,0.07 0.2,0.85 0.75,0.17 0.5,-1.58 0.11,-0.15 0.28,0.15 0.17,-0.27 -0.13,-0.88 0.35,-0.47 -0.05,-0.33 -0.45,-0.29 -0.09,-0.41 -0.77,-0.77 0.21,-0.22 0.51,0.06 0.35,-0.76 0.49,-0.11 -0.33,-0.32 0.13,-0.4 -0.36,-0.49 -0.72,-0.18 -0.29,-0.43 0.29,-0.38 0.14,-1.07 0.39,-0.08 0.57,0.95 1.05,0.03 0.63,0.69 0.97,0.08 0.72,-0.26 0.53,0.53 0.14,0.7 0.56,0.18 -0.11,0.79 0.3,0.97 0.26,-0.03 0.77,-0.84 0.94,-0.35 -0.17,-1.24 0.77,-0.08 0.05,-0.98 -0.48,-0.31 -0.55,-0.94 0.32,-0.26 1.87,0.19 0.18,-0.42 -0.72,-1.17 0.04,-0.28 0.18,-0.46 0.74,-0.04 0.5,-1.02 -0.25,-0.83 -0.48,-0.55 1.59,-0.94 -0.32,-0.51 0.05,-1.04 -0.26,-0.41 1.05,-2.53 -0.38,-1.74 -0.33,-0.43 1,-0.78 -0.33,-0.89 0.65,-0.47 0.2,-0.69 0.48,0.39 0.38,-0.19 -0.62,-1.59 -0.4,-0.32 -0.02,-0.44 -0.48,-0.31 0.26,-0.14 0.2,-0.89 0.47,-0.46 1.14,0.4 0.2,0.31 1.64,0.32 0.31,0.66 0.42,0.26 0.58,-0.16 0.58,0.44 0.12,0.41 0.36,-0.26 1.13,0.33 0.59,0.51 -0.1,0.22 0.32,0.52 -0.09,0.48 0.18,0.19 1.12,0.02 -0.01,0.28 0.29,0.22 0.06,-1.03 0.27,-0.13 0.09,-0.68 0.28,-0.18 1.63,0.05 1.21,0.67 0.52,-0.16 0.78,1.02 1.01,0.16 0.41,0.27 -0.02,-0.26 0.29,-0.05 0,0.29 0.22,-0.44 0.22,0.29 1.33,-0.73 0.54,0.12 0.61,-0.88 0,0 0.8,-0.13 0.91,0.17 0,0 -0.14,0.78 1.04,1.12 -0.13,0.61 0.28,0.69 0.36,0.18 0.23,0.62 0.77,0.15 0.68,0.89 0.36,1.15 -0.33,0.81 0.35,1.07 -2,1.49 -0.19,1.36 -0.39,0.03 0.29,0.53 -0.21,0.42 0.14,0.49 0.34,0.1 0.61,-0.51 -0.4,0.86 -1.45,-0.21 -0.9,0.4 0.08,0.6 -0.33,0.76 0.18,0.54 -0.15,1 -0.93,0.41 0.07,1.18 -0.78,0.18 -0.19,0.48 0.68,0.83 -1.66,0.54 0.66,0.58 -0.5,0.48 -0.49,-0.19 -0.94,0.52 0.16,0.24 -0.14,0.85 0.27,0.51 -0.29,0.14 -0.05,0.59 -0.19,0.05 1,1.43 0.31,0.22 0.2,-0.15 0.21,0.46 -0.09,0.67 0.35,0.46 0.37,-0.15 0.89,0.34 0.01,0.39 -0.6,0.25 -0.38,0.51 0.03,0.59 -0.3,0.09 -0.34,0.54 0.1,0.63 0.52,0.37 -0.35,0.44 0.07,0.47 -0.34,0.37 0.56,0.36 0.07,0.54 0.65,0.6 0.35,2.02 0.6,0.2 -0.04,0.86 0.32,0.14 0.05,0.3 0.22,-0.15 0.44,0.31 0.4,0.77 0.98,0.28 0.22,0.29 0,0 -0.34,0.55 0.19,2.19 -0.4,0.24 -0.07,0.71 -1,0.2 -0.32,0.62 0.53,1.35 -0.02,0.95 -0.59,0.48 -0.54,-0.34 -0.5,0.05 -0.15,0.23 -0.04,1.3 0.25,0.63 -0.18,1.02 0.3,0.24 0.82,-0.21 0.51,0.36 -0.42,0.69 -0.45,0.28 0.36,0.76 -0.62,0.39 -0.74,1.47 -0.63,0.32 0.53,0.49 -0.06,0.45 -0.39,0.37 0.57,1.69 0.51,0.64 -0.18,0.76 0.94,0.910001 0.63,1.08 0.48,0.07 0.34,0.42 0,0 -0.63,0.87 -0.47,1.29 -0.49,-0.08 -0.7,0.62 -0.25,-0.16 -0.34,0.59 -0.69,-0.35 -0.27,0.35 -0.53,-0.05 -0.16,0.42 -1.3,0.27 -0.36,0.47 -0.6,-0.84 -1.12,0.07 -0.2,-0.46 -0.8,-0.27 -0.61,0.32 -0.8,0.02 -0.38,0.55 0.19,0.27 -0.81,-0.18 0,0 -0.13,-0.23 -0.71,0.22 -0.38,-0.12 -0.26,-0.67 0.12,-0.55 -1.58,-0.83 0.29,-0.82 -0.63,-0.37 -0.59,-0.94 -0.25,-0.75 0.41,-0.25 -0.13,-0.48 -0.87,-0.350001 -0.42,-0.68 0.68,-0.82 -0.2,-0.8 -1.58,-0.21 -0.54,-0.39 -0.13,-0.41 -0.67,-0.3 -0.09,-0.27 -0.63,-0.07 -0.52,0.39 0.05,-0.34 -0.44,-0.68 -0.78,0.16 -0.49,-0.43 -0.75,-0.08 -0.39,-0.65 -0.8,-0.03 0,0 -0.13,-0.31 -0.38,0.06 -0.02,-0.72 -1.02,-0.67 0.68,-0.23 0.23,-0.32 0.36,-0.77 -0.5,-0.51 0.47,-0.35 0.4,-1.1 0.6,-0.43 -0.57,-0.78 -0.65,-0.17 -0.93,-1.51 -0.73,-0.35 -0.1,-1.01 -0.28,-0.47 -0.58,-0.03 -0.59,-0.35 -0.01,-0.51 0.41,-0.56 -0.15,-1.03 -0.49,-0.08 -0.64,0.48 -0.57,-0.44 0.15,-0.49 0,0 0.21,-0.39 0,0 -0.05,-1.1 0.38,-0.89 -0.47,-0.32 -0.06,-0.5 0.35,-0.68 -0.21,-0.31 0.28,-1.25 -0.4,-0.13 -0.86,0.24 -0.31,0.53 -0.3,0.11 -0.3,-0.31 -0.49,-0.04 -0.31,-0.48 0.05,-0.45 -0.36,-0.35 -0.37,-0.18 -0.49,0.17 -0.29,-0.37 -0.5,0.09 -0.34,-0.41 -0.76,-0.07 -0.89,-1.38 -0.27,-1.75 0.21,-0.78 -0.5,-0.83 0.08,-0.72 z"
                            id="tinh-tuyen-quang" data-id="tinh-tuyen-quang" data-link="{PROVINCE_LIST_211.link}"
                            data-tinh="{PROVINCE_LIST_211.title}" data-province="{PROVINCE_LIST_211.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 210.42847,56.703229 0.36,-1.49 0.66,-1.27 0.26,-0.14 0,0 0.31,-0.62 0,0 0.76,-0.18 0.43,-0.35 1.01,0.61 0.28,-0.16 0.65,0.31 0.57,1.17 0.43,0.06 0.88,0.89 0.39,0.11 0.42,-0.27 0.26,0.15 0,0.46 0.57,0.56 -0.17,0.92 1.08,1.03 0.84,-0.14 0.94,-0.8 0.23,0.2 0.71,0.01 0.57,0.6 0.57,-0.25 0.79,0.38 0.8,-0.13 1.08,-0.54 0.07,-0.39 -0.5,-0.19 0.58,-0.28 -0.35,-0.34 0.51,-0.26 -0.14,-0.37 0.72,-0.2 0.78,0.39 0.66,-0.54 -0.21,-0.28 0.38,-0.35 1.36,-0.15 0,0 0.54,1.02 0.71,0.52 0.18,0.74 -0.89,0.69 0.17,0.53 -0.78,0.55 0.58,0.44 0.91,-0.15 0.76,0.38 0.53,-0.14 1.18,-0.02 0.88,0.02 0.38,0.31 -0.15,0.37 0.57,0.93 -0.08,0.58 0.74,0.61 0.58,0.75 -0.37,0.63 0.31,0.38 0.08,0.87 0.55,0.9 0,0.37 -0.32,0.78 -0.25,0.21 -0.55,-0.06 -0.17,0.63 -0.43,0.37 0.85,1.16 0.43,0.18 0.43,-0.11 0.16,0.19 0,0.3 -0.44,0.07 -0.33,0.81 0.37,1.78 0.56,0.21 0.22,0.42 -0.3,1.26 0.21,0.46 -0.72,0.69 -0.02,0.48 0.43,0.34 0.12,0.78 -1.18,1.09 -0.08,0.47 0.08,0.88 0.35,0.41 0.57,0.36 0.88,-0.41 0.94,-1.88 1.48,-0.3 0.99,0.37 0.16,0.73 0.77,0.3 0.1,0.64 0.34,0.17 1.09,-0.04 0.74,-0.29 0.85,-0.07 1.16,0.05 0.99,0.48 0.66,-0.2 0.32,0.59 -0.07,0.6 0.38,0.68 -0.01,0.54 1.2,-0.11 0.05,0.48 0.42,0.31 0.95,-0.42 0.74,-1.47 0.36,0.53 0.91,-0.09 1.12,0.84 1.04,-0.08 0.29,1.16 -0.34,0.49 0.21,0.5 -0.58,0.66 -0.63,0.18 -0.84,0.78 0.2,0.29 0.68,0.23 -1,1.56 0.2,0.38 1.93,0.59 1.21,-0.01 0.28,0.53 1.17,0.4 0.47,0.72 0.59,0.24 0.36,0.72 1.03,0.34 0.64,0.24 0.64,0.33 0.44,0.64 0.87,0.74 1.18,0.83 0.77,0.17 0.15,-0.71 0.72,-0.69 0.82,-0.28 0.42,-0.15 0.75,-0.27 0.33,0.63 0.74,0.29 -0.15,0.55 0.21,0.54 0.53,0.36 0.57,1.24 0.66,0.39 0.31,1.68 -0.46,0.75 0.29,0.49 -0.19,0.600001 0.26,0.38 0,0 -1.26,0.63 -0.42,0.03 -0.42,-0.36 -0.44,0.22 -0.21,-0.36 -0.64,0.14 -0.25,0.11 -0.17,0.58 -0.31,0.2 0.01,0.47 -0.34,0.33 -0.98,0.02 -0.62,0.78 -0.68,-0.34 -0.38,0.18 -0.24,-0.12 -0.88,0.65 0.37,0.6 0.27,-0.04 0.26,0.3 0.07,0.39 0.42,-0.13 0.14,0.15 0.24,0.72 -0.1,0.57 0.52,0.77 -0.35,0.52 0.12,0.4 -0.19,0.36 0.11,0.69 0.26,0.19 -0.19,0.5 0.74,0.88 0.1,0.83 0.36,0.19 -0.63,1.87 -0.63,0.13 -0.27,-0.52 -0.99,-0.86 -0.66,0.56 -0.92,0.18 -0.93,0.7 -0.57,-0.29 -0.53,0.22 -0.27,-0.1 0.07,-0.18 -0.85,0.01 -1.29,-0.42 -0.27,0.18 -0.11,0.8 -0.52,0.23 -0.31,0.48 -0.81,-0.11 -0.59,0.72 0,0.32 -1.02,0.24 0,0 -0.52,-0.35 -0.22,-0.73 -1.59,-0.82 0.29,-0.98 -0.4,-1.33 0.31,-0.37 -0.03,-0.33 0.68,-0.43 -0.63,-0.47 -0.28,-0.54 -0.91,0.2 -0.81,0.72 -0.46,-0.64 -0.83,0.51 0.07,-0.61 -0.19,-0.23 -0.5,0.58 -0.28,-0.01 -1.04,-1.18 0.05,-0.45 0.5,-0.6 -0.52,-0.36 -0.14,-0.74 -1.08,-0.39 -0.78,0.1 -0.07,-0.24 -0.34,0.28 -0.29,-0.97 0.54,-0.25 0.12,-0.48 0.36,0.17 0.3,-0.6 -0.76,-0.47 -0.37,-1.01 -1.13,-0.16 -0.81,0.18 -0.68,-0.39 -0.34,-0.59 -1.39,0.37 -0.21,0.48 0.3,0.42 -0.16,0.06 0,0.48 -0.15,0.2 -0.67,-0.06 -0.48,-0.18 -0.37,-0.57 -0.84,-0.3 -0.54,-0.59 0.15,-1.21 -1.69,-0.88 -0.75,0.31 -0.57,-0.17 -0.48,0.32 -1.05,-0.4 -1.29,0.52 -1.02,-0.22 -0.33,0.41 -0.4,0.09 -0.77,1.29 -0.75,0.65 -0.3,1.26 -0.74,0.63 0.71,1.37 -0.61,0.73 -0.79,0.24 -0.62,1.87 -0.49,0.06 -0.48,0.6 -0.57,-0.13 -0.45,0.17 -0.31,-0.41 -0.31,0.32 -0.75,0.03 -0.68,0.6 0.53,0.59 -0.04,0.47 -0.74,0.12 0,0 -0.25,-0.01 -0.42,0.45 0,0 -0.52,0.2 -0.55,-0.3 -0.25,0.18 0,0 -0.35,-0.08 0,0 -0.2,0.09 -0.27,-0.3 -0.29,0.21 -0.29,-0.39 0,0 -0.3,-0.2 0,0 -0.27,-0.31 0.44,-1.23 -0.22,-0.63 -0.64,0.14 -0.29,-0.41 -0.33,0.21 0.09,0.31 -0.39,-0.22 -0.16,0.18 -0.35,-0.34 0.07,-0.36 -0.32,0.19 0.14,-0.87 -0.21,-0.27 0.29,-1.02 -0.72,-0.13 -0.68,0.17 0,0 -0.23,0.2 0,0 -0.37,-0.03 -0.26,0.31 -0.38,-0.72 -1.4,-0.12 -2.19,-0.87 0.05,-4.56 0.82,-0.52 0,0 0.66,-0.900001 -0.13,-0.36 0.16,-0.34 -0.3,-0.24 -0.05,-0.49 1.78,-1.88 -0.07,-0.65 0.43,-0.41 0.05,-0.52 0,0 0.22,-0.43 -0.15,-0.65 -0.51,-0.31 -0.36,0.34 -0.36,-0.1 0,0 -0.75,-0.38 -0.12,-0.33 -0.67,-0.1 0,0 -0.68,-0.36 0,0 -0.04,-0.26 -0.97,-0.37 -0.63,-1.32 -0.92,0.2 0,0 -0.38,-0.01 -0.04,-0.31 0,0 -0.25,-0.19 0.06,-0.7 -0.63,-0.99 0.3,-0.54 0.35,-0.09 -0.03,-0.48 0.55,-0.14 -0.32,-0.35 -0.06,-0.61 0.25,-0.57 -0.12,-1.1 0.74,-0.51 0.22,-1.16 -1.72,-0.96 0,0 0.69,-0.69 -0.68,-0.07 0,-0.44 0,0 0.29,-0.1 0,0 0.41,-0.09 0.17,-0.86 -0.06,-0.87 -0.35,-0.23 0.18,-0.93 -0.81,-1.22 0.38,-0.49 -0.13,-1.48 0.41,-0.26 0.32,0.19 0.47,-0.69 1.76,0.2 0.29,-1.17 0.34,-0.5 0.38,0.06 0.45,-0.4 -0.22,-0.64 0.54,-0.83 0.27,0.05 1.04,-1.3 0.48,-0.18 0.41,-0.51 -0.65,-1.04 0,-0.56 0.55,-0.73 0.3,-0.08 0.49,-1.94 -0.1,-0.59 -0.53,-0.66 -0.53,-0.21 -0.2,-0.43 -0.21,0.14 -0.25,-0.15 -0.37,-0.63 -0.27,-0.65 0.32,-1.04 0,0 0.16,-0.31 0,0 0.54,-0.55 -0.07,-0.51 -0.48,-0.65 -1.11,-0.64 z"
                            id="tinh-lang-son" data-id="tinh-lang-son" data-link="{PROVINCE_LIST_209.link}"
                            data-tinh="{PROVINCE_LIST_209.title}" data-province="{PROVINCE_LIST_209.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 259.28847,144.19323 -0.02,0.4 0.61,0.62 -0.09,0.55 0.45,0.22 -0.35,0.25 0.04,0.34 -0.39,0.23 -0.35,-0.09 -0.33,-0.75 0.09,-0.72 -0.27,-0.65 0.04,-0.41 0.57,0.01 z m 11.38,-2.21 0.22,0.28 -0.19,0.31 0.22,0.75 0.38,-0.37 0.14,-0.58 0.33,1.26 0.36,0.06 0.64,-0.23 -0.14,0.46 0.19,1.48 -0.23,0.08 -0.69,-0.75 -1.21,0.15 -0.06,-1.51 -0.48,-1.03 0.06,-0.28 0.33,0.15 0.13,-0.23 z m -0.1,-2.43 0.32,0.02 0.64,0.63 0.69,-0.21 0.72,0.08 0,0.28 0.35,0.05 0.1,0.32 -0.03,0.37 -0.2,-0.23 -0.22,0.17 0.26,0.54 -0.55,-0.11 -0.45,0.43 -0.69,-0.08 -0.29,0.17 -0.69,-1.37 -0.19,0.55 -0.48,-0.45 0.04,-0.83 0.67,-0.33 z m 9.18,-0.83 -0.74,1.26 -0.75,-0.25 -0.68,0.09 1.32,-0.86 0.85,-0.24 z m -10.98,-0.3 0.65,1 0.26,1 -0.22,0.12 0.23,0.32 -0.07,0.28 -1.26,-1.15 -0.45,-0.82 0.91,0.46 -0.05,-1.21 z m 0.7,-0.43 1.14,0.34 0.36,0.43 -0.46,0.05 -1.55,-0.51 0.51,-0.31 z m -25.51,4.75 -1.8,-0.35 -0.62,0.48 -0.77,-0.58 -0.59,-1.74 0.91,-1.37 0.56,0.11 0.06,-0.38 -0.51,-1.09 1,-0.04 0.43,-0.02 0.54,0.45 0.57,0.11 0.3,0.61 0.77,0.61 -0.19,0.58 0.12,0.42 0.45,1.01 0.55,0.38 0.28,1.26 -1.37,0.86 -0.68,-0.64 -0.01,-0.67 z m 3.91,-6.09 1.26,0.91 0.2,0.71 0.33,0.19 0.52,0.75 -2.22,-0.09 -0.53,0.26 -0.01,-0.6 -0.43,-0.48 -0.13,-0.75 0.35,-0.92 0.66,0.02 z m 25.1,-0.79 0.19,0.31 -0.33,0.54 -0.94,1.06 -0.77,0.38 -1.36,-0.18 -0.85,-0.45 0.22,-0.35 0.28,0.17 0.79,-0.26 0.51,0.28 0.45,-0.11 1.81,-1.39 z m 8.81,-0.14 0.14,0.26 -0.29,0.31 -0.16,0.77 0.13,0.22 0.28,-0.09 0.16,0.51 -0.92,0.54 -0.58,0.86 -0.56,0.37 -0.16,1.01 -1.29,1.06 -0.07,0.91 -0.55,1.48 -0.66,0.42 -0.1,-0.26 0.52,-0.94 0.3,-1.32 0.28,-0.28 -0.2,-0.52 -0.45,-0.17 -0.07,-0.58 0.43,-0.11 0.41,0.23 1.01,-0.2 0.46,-1.58 0.46,-0.43 -0.09,0.45 0.2,0.02 0.45,-0.74 -0.14,-0.46 1.06,-1.74 z m -5.5,-0.05 0.2,0.2 -0.32,1.11 -1.47,1.48 -2.57,0.94 0.25,0.35 -0.74,-0.2 -0.12,0.28 -0.79,-0.55 3.24,-0.85 0.49,-0.58 0.19,-0.91 0.32,-0.01 0.48,-0.65 0.84,-0.61 z m 1.3,-0.43 0.29,0.57 -0.43,0.68 -1.2,1.21 -0.39,0.08 0.69,-0.8 0.07,-0.52 0.65,-1.05 0.32,-0.17 z m 14.16,-2.57 0.13,0.54 0.39,0.08 0.45,0.54 1.06,0.81 -0.48,0.55 0.13,0.55 -0.81,0.45 0,0.25 -0.75,0.09 -0.13,0.43 -0.56,0.22 0.55,-0.86 0.78,-0.57 -0.45,-0.81 -0.98,-0.38 0.12,-0.51 -0.36,-0.25 0.39,-0.74 0.52,-0.39 z m -9.94,-1.15 0.17,0.46 -0.68,0.83 -0.2,0.97 0.13,0.4 0.3,0.08 -0.33,0.57 -0.36,0.42 -1.13,3.11 -0.53,0.39 -2.98,0.54 0.14,0.32 -0.17,0.39 -0.95,0.49 -0.81,-0.54 1.21,-0.8 -1.11,0.26 -0.41,0.22 -0.1,0.32 -0.71,0.17 0.02,-0.63 1.02,-0.35 0.38,-0.46 0.67,-0.08 0.29,-0.41 1.84,-1.09 0.81,-1.77 -0.46,-0.01 -0.22,-0.49 0.45,0.17 0.25,-0.34 0.03,-0.32 1.04,-1.26 0.32,-0.17 0.98,-1.12 0.77,-0.2 0.33,-0.07 z m 15.47,-0.06 0.29,0.2 0.16,1.05 -0.33,-0.17 -0.32,0.26 -0.32,0.15 -0.17,0.43 -0.48,0.35 -0.03,0.29 -0.11,0.25 -0.43,0.05 0.04,0.65 -0.55,0.03 -0.36,0.31 -0.07,-0.29 0.29,-0.37 -0.29,-0.34 0.14,-0.49 0.36,-0.66 0.26,-0.06 0.23,-0.69 0.45,-0.45 0.32,-0.4 0.49,0.34 0.43,-0.44 z m -14.26,-3.95 0,0.88 -0.51,1 -0.42,-0.05 -0.16,0.51 -1.17,0.66 -0.26,0.65 -0.49,0.42 -0.53,0 -0.4,0.45 -0.43,0.12 -0.16,-0.45 0.2,-0.29 1.14,-0.69 0.33,-0.52 -0.16,-0.26 0.12,-0.32 0.04,-0.6 0.46,0 0.04,-0.26 0.46,-0.2 0.2,-0.86 0.42,-0.09 0.38,0.23 0.38,-0.35 0.52,0.02 z m 2.63,-0.46 -1.21,3.63 -0.42,0.6 -0.1,1.03 -0.48,0.37 -0.61,2.48 -0.72,0.52 0.03,-0.75 0.54,-0.26 -0.38,-0.26 -0.07,-0.37 0.52,-1.43 0.55,-0.41 -0.14,-0.46 0.23,-1.72 2.26,-2.97 z m -11.67,-3.11 0.2,2.43 -0.37,0.46 -0.39,0.09 -0.32,-1.12 0.12,-1.15 0.35,-0.35 0.01,-0.34 0.4,-0.02 z m 14.21,-1.91 0.12,0.17 -0.92,2.8 0.06,1.03 -0.5,0.39 -0.13,-0.74 0.43,-2.25 0.42,-0.97 0.52,-0.43 z m -2.13,-2.53 0.22,0.09 -0.07,0.35 -0.91,0.46 -0.46,0.65 -0.35,0.05 -0.22,-0.26 -0.68,0.06 1.49,-1.03 0.98,-0.37 z m -9.26,-0.69 0.72,0.15 2.27,2.19 0.35,0.34 0.42,0.69 0.06,0.52 0.46,0.35 2.1,-0.48 0.69,-0.72 0.71,-0.01 0.04,0.19 -0.94,1.54 -0.46,0.56 -0.56,0.2 -0.49,0.86 -1.14,0.85 -0.46,0.45 -2.12,1.36 -1.01,0.86 -0.32,0.66 -0.48,-0.02 -0.26,-0.4 -0.3,0.6 -1.47,1.05 -0.22,0.48 -1.07,0.29 -0.53,0.4 -0.06,-0.82 -0.23,-0.28 -0.17,-0.14 -0.37,-0.71 0.74,-0.2 0.22,-0.48 -0.25,-0.25 0.77,-0.62 0.26,-0.75 0.88,-1 -0.17,-2.73 1.1,-0.72 0.3,-0.65 0.14,-0.29 0.35,-0.57 0.27,-2.74 0.23,-0.01 z m 5.59,-0.18 1.04,0.88 -0.74,0.28 -0.45,-0.09 -0.3,0.29 -0.46,-0.09 -0.36,-0.11 -0.45,-0.18 0.16,-0.43 1.56,-0.55 z m -1.88,-0.44 0.8,0.25 0.29,-0.18 0.3,0.25 -1.53,0.89 -0.4,-0.77 0.54,-0.44 z m 8.77,-0.88 -0.11,0.46 -1.46,0.85 -0.16,-0.42 0.27,-0.32 1.46,-0.57 z m 5.98,-3.17 0.36,0.4 -0.66,0.43 -0.22,0.17 -0.48,0.46 -0.95,0.52 -1.04,0.31 -1.39,0.83 0.09,-0.37 -0.2,-0.15 -0.72,0.48 -0.64,0.08 4.71,-2.75 1.14,-0.41 z m 9.78,-2.24 -0.27,0.56 -0.97,0.17 -0.25,0.31 -0.58,0.17 -0.59,0.59 -0.01,0.42 -0.42,0.22 -0.69,-0.12 -0.25,-0.29 -0.82,0.36 -0.39,-0.18 -1.1,0.06 -1.94,1.11 -0.17,-0.28 0.26,-0.68 0.17,-0.17 0.22,-0.12 0.33,-0.34 -0.74,-0.14 -0.42,0.29 -0.55,-0.01 1.01,-0.71 0.82,-0.22 0.25,0.15 0.91,-0.29 1.03,0.14 2.8,-0.83 2.36,-0.17 z m -27.3,-14.500001 0.54,1.05 0.44,0.34 -0.25,0.6 0.23,0.35 -0.08,0.640001 -0.42,0.34 0.52,0.29 0.79,-0.12 0.66,0.44 0.92,0.01 0.78,-1.32 0.5,0.05 0.37,-0.270001 1.3,0.560001 2.74,-0.11 0.65,-0.280001 0.64,0.250001 1.96,-0.690001 1.76,-1.13 1.14,-0.12 0.44,-0.27 0.1,-0.36 0.87,0.09 0.89,-0.23 1.34,0.96 0.24,-0.46 0.59,-0.26 0.66,0.35 0.44,0.45 0.03,0.52 0.48,0.23 0.13,0.59 0.14,0.570001 -0.14,0.35 0.46,0.54 1.35,0.3 0.81,0.67 0.66,1.86 0.72,0.13 0.67,-0.48 2.27,-0.37 0.26,-0.28 1.11,0.05 0.41,0.82 0.62,0.16 0.05,0.23 -0.55,0 -0.49,-0.54 -0.91,-0.22 0.14,0.4 0.56,0.36 0.06,0.48 -0.87,-0.22 0.45,0.67 -0.74,0.22 -0.78,-0.32 0.64,0.56 0.64,-0.03 0.17,-0.22 0.36,0.29 -0.56,0.51 -1.18,0.49 0.82,0.22 1.04,-0.51 0.78,0 0.56,0.26 0.04,0.34 -1.21,-0.02 -2.11,0.73 -2.18,2.16 -0.1,-0.85 1.01,-0.93 -0.13,-0.37 -0.58,0.31 -0.49,0.19 -0.61,1 -0.35,0.32 -0.65,0.08 -0.87,-0.49 -0.35,-1.13 0.51,-0.66 0.54,-0.12 -0.01,-0.68 -0.33,0.37 -0.39,0 -0.28,-0.17 -0.3,-0.31 0.42,-0.59 -0.45,-0.22 -0.38,-0.02 -0.23,-0.03 -0.78,1.07 -0.74,-0.23 -0.43,-0.93 -0.33,1.1 -0.46,0.93 -0.35,-0.06 -0.12,-2.67 -0.35,0.77 -0.19,1.45 -0.2,0.15 -0.36,-0.48 -0.42,-0.79 -0.61,-0.46 -0.16,0.14 0.13,0.34 0.51,0.49 0.25,1.28 -0.65,0 -0.19,0.88 -0.98,0.82 -0.38,0.71 -0.07,0.46 -0.45,1.08 -1.4,0.43 -0.88,0.39 -0.98,0.28 -0.62,0.06 -0.64,0.77 -1.01,-0.43 0.25,0.83 -0.22,0.85 -0.63,0.57 -0.29,0.66 -0.22,0.39 -0.25,0.54 -0.13,0.23 -0.32,0.28 -0.16,0.22 -0.74,0.14 -0.35,0.43 -0.66,-0.22 -0.78,-0.38 -0.77,-1.79 -0.01,0.26 0.09,0.6 -0.49,-0.05 -0.48,-0.01 1.07,0.49 0.19,0.39 -0.19,0.28 -0.87,-0.08 -0.56,-0.02 -0.25,-0.83 -0.27,0.14 -0.01,0.56 -0.32,0.12 -0.52,0.02 -0.26,0.2 -1.26,-1.29 0.17,0.8 -1.37,-0.38 -0.01,0.22 0.64,0.56 0.03,0.35 -1.27,0.28 -0.42,0.57 -0.4,-0.25 -0.65,-1.8 -0.62,0.03 0.43,0.88 -0.04,0.23 -0.3,0.23 -0.81,0.4 -0.46,-0.49 -0.19,0.08 0.56,0.96 0.19,0.28 0.12,0.26 0.28,-0.31 0.13,0.11 0.36,0.12 1.04,-0.12 -0.16,0.39 1.26,-0.52 0.25,0.94 -0.1,0.34 -1.47,0.99 -0.3,0.42 0.04,0.26 -0.56,1.02 -0.35,0.23 -0.33,0.12 -0.2,0.35 1.08,-0.25 -0.4,0.83 -0.13,1.17 0.23,0.43 -0.37,0.8 0.06,0.39 -0.81,0.89 0.36,0.66 -1.07,0.48 0.52,0.49 0.12,0.75 -0.27,0.75 0.49,0.95 -0.71,0.97 -0.95,0.57 -0.13,-0.34 -0.49,0.32 -1.31,-0.37 -0.64,0.39 -0.98,-0.12 -1.65,0.34 -0.29,0.48 -1.04,0.82 0.25,0.42 -0.26,0.28 0.06,0.32 -0.59,0.02 0.2,-0.81 -0.23,0.09 -0.52,0.78 0.29,0.77 -1.3,0.05 -1.2,0.57 -0.4,-0.66 0.25,-0.17 -0.09,-0.28 -0.71,0.34 -0.92,-0.12 -1.26,-0.6 -0.4,0.05 -0.2,0.29 -0.53,-0.25 -0.17,-0.49 0.26,-0.2 0.42,-1.22 1.2,-0.91 0.3,-0.66 -0.25,-0.15 -1.16,1.23 -0.24,0.15 -1.1,0.25 -0.33,-0.43 -0.23,-1.17 -0.1,-0.12 -0.22,0.26 -0.22,0.09 -0.42,-0.26 -0.35,0.25 0.2,0.28 -0.42,0.32 1.2,-0.02 0.16,0.69 0.07,0.34 0.32,0.02 0.36,0.77 0.39,0.17 -0.1,0.29 -1.57,0.75 -1.77,-0.89 -1.41,0.55 -1.59,0.05 -0.68,-0.34 -0.42,-0.65 -1.04,-0.09 -0.49,-0.35 0.05,-0.75 -0.82,0.18 -0.23,0.46 0.32,0.63 0.79,0.11 -0.5,2.16 0.54,0.68 -0.15,0.54 -0.62,0.62 -0.06,0.28 -0.37,0.37 0.06,0.45 -0.66,0.17 -0.49,-1.01 0.09,-0.8 -0.97,-1.15 -1.44,-0.65 -1.26,-0.05 -0.25,-0.33 -0.07,-1.54 0.25,-0.26 0.69,-0.22 -0.23,-0.27 -0.72,0.35 -0.79,-1.14 -0.35,-0.18 -1.53,0.12 -0.53,-0.26 -0.24,0.19 0,0 -2.84,-1.1 -1.31,0.43 -0.81,-0.53 0,0 -0.39,-0.84 -1.46,-1.16 -0.38,0.23 -0.39,-0.17 -0.37,0.19 -0.48,-0.09 -0.63,0.38 -0.4,-0.16 -0.08,0.25 -0.4,-0.01 0.19,-0.42 -0.14,-0.36 -0.68,0.19 -0.86,-0.62 -1.04,0.08 0,0 -0.57,0.08 0,0 -0.78,-0.71 0.51,-0.92 -0.6,-0.67 0.11,-0.59 0.85,-0.12 0.19,0.25 1.15,-0.73 0.01,-0.87 -0.22,-0.08 0.16,-0.73 0.39,-0.45 -0.18,-0.36 0.15,-0.41 0.34,-0.22 -0.09,-0.79 0.33,-0.14 -0.12,-0.53 0.19,-0.74 0,0 0.31,0.16 0.61,-0.22 0.45,0.15 0.78,1.71 0.72,-0.14 0.52,0.32 0.79,-0.18 0.43,0.43 -0.09,0.32 0.88,-0.07 0.23,0.49 1.13,-0.06 0.33,0.2 0.16,-0.68 0.57,0.35 1.39,0.2 0.16,0.2 0.83,-0.17 1.25,0.6 0.59,-0.1 0.52,0.21 1.04,-0.01 0.27,0.22 1.23,0.18 1.35,-0.4 0.75,0.2 -0.38,-0.64 0.02,-1.34 1.5,-0.01 0.31,0.68 0.62,0.14 0.5,-0.54 0.51,0.24 1.23,-0.28 1.02,0.85 0.37,-0.29 0.25,-0.8 0.41,-0.14 0.04,-0.82 0.66,-1.05 0.49,-0.11 -0.06,-0.69 0.42,-1.07 -0.09,-0.4 -0.38,-0.26 -0.19,-0.77 0.78,-0.51 1.19,-0.06 0.49,-0.25 0.72,-0.88 -0.08,-0.56 0,0 1.02,-0.24 0,-0.32 0.59,-0.71 0.81,0.11 0.31,-0.48 0.52,-0.23 0.12,-0.8 0.26,-0.18 1.29,0.42 0.85,-0.01 -0.07,0.18 0.27,0.1 0.54,-0.22 0.56,0.29 0.93,-0.69 0.92,-0.18 0.67,-0.56 0.99,0.85 0.27,0.52 0.63,-0.12 0.63,-1.88 -0.36,-0.19 -0.1,-0.83 -0.74,-0.88 0.19,-0.5 -0.26,-0.19 -0.11,-0.69 0.2,-0.36 -0.12,-0.4 0.35,-0.52 -0.51,-0.76 0.1,-0.57 -0.24,-0.72 -0.14,-0.15 -0.42,0.13 -0.07,-0.39 -0.26,-0.29 -0.27,0.04 -0.37,-0.6 0.88,-0.64 0.24,0.12 0.38,-0.18 0.69,0.33 0.62,-0.78 0.98,-0.02 0.34,-0.33 -0.01,-0.48 0.31,-0.19 0.17,-0.58 0.25,-0.11 0.65,-0.15 0.21,0.36 0.44,-0.22 0.42,0.36 0.41,-0.03 1.27,-0.63 0,0 1.09,0.19 0.76,-0.79 -0.09,-0.450001 0.63,-0.28 0.49,-0.55 -0.01,-0.37 0.75,-0.58 1.82,-0.81 0.59,0.24 z"
                            id="tinh-quang-ninh" data-id="tinh-quang-ninh" data-link="{PROVINCE_LIST_225.link}"
                            data-tinh="{PROVINCE_LIST_225.title}" data-province="{PROVINCE_LIST_225.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 183.55847,151.94323 0,0 0,0 0.5,0.37 -0.02,1.25 0.41,0.48 -0.42,0.53 -0.38,-0.21 -0.28,0.11 -0.02,0.49 0,0 0.29,0.29 0,0 0.81,0 0.22,-0.29 0.55,0 0.14,1.03 0.22,0.22 0.62,0.12 0.37,-0.49 0.29,0.32 0.53,0.04 1.13,1.27 0.2,0.58 0,0 0.26,0.87 0.53,0.6 0.59,1.49 0.31,-0.06 0.21,0.67 0.21,0.11 0.55,-0.39 0.02,0.62 0.25,0.31 0.69,0.48 0.73,0.13 0.1,2.45 0.19,0.41 0,0 0.23,0.36 -0.22,0.09 -0.48,-0.24 -0.57,0.11 -0.25,0.31 -0.49,-0.18 -1.17,-1.02 -0.46,-1.01 -0.22,0.06 -0.31,0.94 -0.4,0.05 -0.08,-0.38 -0.92,0.04 -0.2,0.16 0.41,1.26 -0.34,0.16 -0.03,0.35 0.41,1.55 -0.16,0.64 -0.55,0.04 -0.04,-0.88 -0.78,-0.2 0.08,-0.7 -0.68,-0.51 -0.66,-0.23 -1.09,0.09 0.12,0.4 0.35,0.02 0.21,0.3 -0.33,0.27 1.05,1.44 -0.13,0.34 1.03,1.17 -0.26,0.52 -1.23,0.48 -0.73,0.85 -0.85,-0.22 -1.45,-1.6 -0.89,-0.57 -0.38,-0.02 -1,-0.82 -0.93,-0.32 -0.56,0.58 -1.44,-0.16 0,0 -0.13,-0.34 -0.48,-0.19 -0.29,0.16 -0.17,0.48 -0.84,0.01 -1.53,-0.85 -0.84,0.02 -1.34,-0.56 -1.48,0.69 -1.89,-0.67 -0.2,-0.26 -1.07,-0.07 -0.16,-0.32 -0.5,-0.24 0,0 -0.19,-0.05 0,0 -1.36,-1.06 -0.13,0.26 -0.28,-0.03 -0.95,-0.8 -0.29,0.14 -0.68,-0.28 0.47,-1.17 -0.27,-0.06 -0.04,-0.53 -1.08,-0.55 0.2,-0.69 -0.72,-0.5 -0.03,-0.38 -0.42,-0.07 -0.99,-0.7 -0.64,0.04 -0.36,-0.22 -0.19,0.32 -0.51,0.04 -0.46,-0.42 0.01,-0.65 -0.47,-0.48 -0.7,-0.13 -0.42,-0.62 -0.95,-0.34 -0.39,-0.57 -0.57,0.28 -0.4,-0.5 -0.39,-0.08 -1.33,0.45 -0.03,0.52 -0.39,0.21 -0.2,-0.17 -0.95,0.06 -0.5,-0.55 -0.92,-0.49 -0.8,0.15 0.21,-0.75 -1.12,-1.04 0,-0.8 -0.74,-1.2 -0.84,-0.26 -1.54,-1.1 -0.71,-0.1 -1.16,0.49 -0.74,-0.14 -0.34,0.65 0,0 -0.49,-0.44 0.24,-1.41 -0.27,-0.62 0.67,-0.35 -0.55,-0.67 0.32,-0.86 -0.42,-0.2 0.11,-0.84 1.06,-0.22 0.78,-0.77 0.4,0.01 1.12,0.76 0.7,0.03 0.35,0.52 0.81,-0.02 0.37,0.45 0.4,-0.21 0.98,0.72 0.35,-0.21 0.33,0.18 0.92,-0.1 0.49,-0.23 0.02,-0.43 -1.02,-0.53 -0.11,-0.44 0.34,-0.36 0.37,-1.33 0.37,0.22 0.67,-0.94 -1.22,-0.65 -0.44,0.14 -0.38,-0.15 0.94,-0.68 0.11,-1.02 -0.48,-0.09 0.22,-0.53 -1.12,-2 -0.08,-0.77 -2.01,-1.46 -0.93,-1.8 -1.38,-1.24 -0.28,-1.22 -0.44,-0.73 -0.71,-0.42 -0.49,-1.27 -0.28,-1.3 0.23,-0.15 0.54,0.2 0.46,-1.26 0.82,-0.49 1,-0.21 0,0 0.18,0.72 0.58,0.76 -0.21,0.5 -0.33,0.06 -0.27,0.39 1.05,0.13 0.4,0.5 1.29,-0.25 0.3,0.4 1.06,0.23 1.05,0.6 0.56,0.7 -0.09,0.58 0.61,-0.39 0.65,0.68 0.67,-0.01 0.77,0.63 1.12,2.11 0.91,0.28 0.65,0.75 0.47,0.21 0.85,-0.18 0.69,0.12 0.06,-0.26 0.78,-0.06 0.65,-0.38 0.14,-0.37 0.74,0.43 -0.65,1.18 0.23,0.26 -0.1,0.22 0.37,0.2 1.1,-0.51 -0.29,-0.34 0.18,-0.21 0.39,0.04 0.88,-0.56 0.68,0.56 0.6,-0.29 0.2,0.89 0,0 0.73,-0.37 0,0 0.38,0.4 0.83,0 0.24,-0.6 1.43,-0.33 -0.75,-2.66 -0.67,-1.29 0,0 0.38,-0.51 0.21,0.21 -0.14,0.48 0.41,0.06 0.27,-0.31 0.46,-0.02 0.36,0.35 1.01,-0.33 0.68,-0.59 -0.03,0.36 0.32,0.19 0.91,-0.2 0.35,0.35 0.91,-0.31 1.25,0.21 1.57,1.2 0.34,-0.07 0.25,0.64 0.62,0.06 -0.04,0.16 0.92,0.73 -0.09,0.61 0,0 -0.23,0.03 0,0 -0.36,0.24 -0.05,0.54 -0.68,0.45 0.57,0.46 0.65,0.22 0.69,-0.5 0.39,0.29 0.32,-0.28 0.43,0.14 1.09,-0.2 0.16,0.33 -0.3,0.08 0.08,0.24 -1.32,0.63 0.26,0.16 -0.23,0.16 0.23,0.5 -0.86,0.48 0.08,0.24 0.4,-0.31 0.35,0.11 0.77,0.74 0,0 0.06,0.17 0,0 0.25,0.35 0.25,-0.02 0.16,0.75 -0.4,0.48 -0.04,0.46 0.85,-0.19 0.02,-0.6 0.67,0.21 0.39,0.38 0.75,0.12 0.05,-0.49 0.21,-0.04 -0.06,0.31 0.27,0.23 0,0 0.08,0.17 0,0 -0.26,0.25 0,0 -0.3,-0.11 0.11,0.12 0,0 -0.22,0.27 0.22,1.14 0,0 0.03,0.17 0,0 0.77,0.02 0.12,2.09 -0.29,0.1 -0.15,-0.32 -0.01,0.57 2.06,2.75 -0.1,0.6 0,0 -0.15,0.23 0,0 -0.26,0.18 0,0 -0.15,-0.02 0.05,0.2 0,0 z"
                            id="tinh-hoa-binh" data-id="tinh-hoa-binh" data-link="{PROVINCE_LIST_305.link}"
                            data-tinh="{PROVINCE_LIST_305.title}" data-province="{PROVINCE_LIST_305.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 176.96847,169.26323 1.43,0.16 0.56,-0.58 0.93,0.31 0.99,0.83 0.38,0.01 0.89,0.58 1.45,1.6 0.85,0.22 0.74,-0.85 1.23,-0.48 0.26,-0.53 -1.04,-1.17 0.13,-0.33 -1.04,-1.44 0.33,-0.28 -0.22,-0.3 -0.35,-0.02 -0.12,-0.39 1.08,-0.09 0.67,0.22 0.68,0.51 -0.08,0.7 0.78,0.2 0.03,0.88 0.55,-0.04 0.16,-0.64 -0.4,-1.55 0.03,-0.36 0.34,-0.16 -0.4,-1.26 0.2,-0.16 0.92,-0.04 0.08,0.38 0.4,-0.05 0.31,-0.94 0.23,-0.06 0.46,1.01 1.17,1.03 0.5,0.18 0.25,-0.31 0.57,-0.11 0.48,0.23 0.22,-0.08 -0.22,-0.36 0,0 2.31,2.92 0.36,0.05 0.13,-0.25 0.57,0.76 0.64,-0.27 0,0 0.17,0.65 0.85,0.15 -0.28,0.81 -0.67,0.56 0.36,0.26 0,0 1.16,0.56 0,0 1.02,1.28 -0.16,1.53 1.26,0.65 1.04,0.06 0.81,-0.29 0.3,0.21 0.02,0.55 -0.94,0.2 0.14,0.46 0.62,0.56 0.61,0.11 0.67,-0.36 0.59,-0.61 0.54,-1.08 0.33,0.09 0.52,2 2,-0.58 0.39,0.2 0.48,1.88 0.31,3.26 -0.99,1.52 -0.45,2.13 -1.34,0.96 -0.14,1.48 -0.85,2.69 0.37,2.04 -0.38,1.19 0,0 -0.7,-0.24 -0.39,0.18 -0.68,-0.36 -0.97,-1.05 0.14,-0.63 -1.02,-0.5 0,0 0,-1.72 0.45,-0.53 0.88,-0.19 0.39,-0.6 0.81,-2.3 0.06,-0.37 -0.21,-0.2 -2.53,0.43 -1.37,-0.5 -1.86,0.13 -1.61,-1.35 -0.31,-0.65 -0.86,-0.23 -0.81,-0.72 -1.19,-0.28 -0.87,-0.78 -0.88,-0.25 -0.18,-0.42 -1.2,-0.45 -0.81,-0.62 -0.85,-0.11 -0.19,-0.26 -1.69,-0.47 -0.16,-0.49 -2.32,-1.51 0.05,-0.51 -0.56,-0.23 -0.23,-0.65 -0.46,-0.1 0.03,-0.27 -1.07,-1.12 -1.55,-0.47 -1.1,-1.65 -1.01,0.02 -0.41,-0.3 -0.42,0.04 0.03,-0.49 -0.54,-0.14 -0.27,-0.35 0.06,-0.69 -0.4,-0.85 z"
                            id="tinh-ninh-binh" data-id="tinh-ninh-binh" data-link="{PROVINCE_LIST_117.link}"
                            data-tinh="{PROVINCE_LIST_117.title}" data-province="{PROVINCE_LIST_117.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 232.02847,171.96323 0.04,0.7 -0.17,0.03 -0.09,0.58 -0.29,0.11 0.17,0.23 0.15,0.12 0.2,0.05 0.14,-0.08 -0.12,0.98 -0.49,0.69 -0.74,-1.21 0.77,-1.23 0.38,-1.31 0.05,0.34 z m -17.25,-20.5 -0.79,-0.31 -0.27,-0.34 0.85,-0.43 0.52,0.63 0.62,-0.05 0.08,-0.24 -0.31,-0.45 0.2,-0.29 0.41,0.26 -0.03,0.54 0.48,0.41 1.57,-1.17 0.41,0 0.48,0.55 0.4,-0.01 1.27,-0.59 1.01,-0.94 0.32,0.02 0,0 0.09,0.36 0.4,0.27 -0.25,0.26 0.12,0.3 -0.73,1.02 1.96,1.44 0.87,1.29 -0.25,1.04 0.83,0.36 0.3,0.85 0.53,-0.07 0.59,-0.95 1.17,0.29 0.2,0.49 0,0 0.64,-0.06 0,0 0.43,-0.26 0.09,-0.8 0.28,-0.24 0.64,0.03 0,0 0.33,0.33 0,0 1.09,-0.44 0.79,-0.65 0.54,0.72 1.18,0.03 0.59,0.39 0,0 0.15,0.66 -1.07,0.68 -0.3,0.48 -0.45,0.15 -0.17,0.31 -1.05,1.38 0.04,0.72 -0.19,0.4 -0.33,0.57 0.12,0.6 1.04,0.92 0.2,0.95 -0.13,0.61 -0.2,-0.26 -0.48,0.31 -0.14,0.35 0.5,0.76 0.1,0.9 -0.4,0.72 -0.79,2.74 0.2,1.04 0.95,1.64 -0.53,1.44 -0.75,0.81 -0.77,-0.36 0,0 -1.71,-1.78 -0.87,0.43 -0.65,0.01 -0.7,0.74 -0.53,0.23 -0.66,0.1 -0.99,-0.25 -0.68,-0.48 -1.57,-1.94 -2.24,-0.87 -0.9,-1.35 0,0 -0.88,0.86 0,0 -2.27,1.79 -0.92,0.05 -0.43,-0.78 0.09,-0.71 1.53,-1.35 0.09,-0.77 -0.58,-0.5 -2.84,-0.43 -0.52,-0.99 0.65,-1.13 0.14,-1.01 -0.71,-0.57 -1.11,-0.1 -0.34,-0.27 0,0 -0.25,-0.57 0.05,-1.32 -0.4,-1.51 -0.71,-1.04 -1.36,-0.52 -0.07,-1.77 -0.51,-0.58 0,0 0.41,-0.35 -0.01,-0.27 -0.51,-0.44 -0.41,-0.02 -0.02,-0.33 0.84,-0.3 0.54,-0.59 0.48,0.01 0.9,0.76 0.32,-0.09 0.28,-0.64 1.43,0.79 0.87,0.14 0,0 0.29,-0.17 -0.41,-0.58 0,0 0.82,-0.24 0.55,-0.63 1.2,-0.49 0.08,-0.29 z"
                            id="tinh-thai-binh" data-id="tinh-thai-binh" data-link="{PROVINCE_LIST_115.link}"
                            data-tinh="{PROVINCE_LIST_115.title}" data-province="{PROVINCE_LIST_115.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 143.93847,189.64323 0,0 -0.01,-0.03 0.01,0.03 z m -3.19,-36.39 0.34,-0.65 0.74,0.14 1.16,-0.49 0.71,0.1 1.54,1.1 0.84,0.26 0.73,1.19 0.01,0.8 1.12,1.04 -0.21,0.75 0.8,-0.15 0.92,0.49 0.51,0.55 0.95,-0.07 0.21,0.18 0.38,-0.21 0.04,-0.52 1.32,-0.46 0.4,0.08 0.4,0.51 0.58,-0.28 0.39,0.56 0.96,0.34 0.42,0.62 0.7,0.13 0.47,0.48 -0.01,0.65 0.47,0.41 0.51,-0.04 0.19,-0.32 0.36,0.22 0.64,-0.04 0.99,0.7 0.42,0.07 0.03,0.39 0.73,0.5 -0.2,0.68 1.08,0.55 0.04,0.53 0.27,0.07 -0.47,1.17 0.69,0.28 0.29,-0.14 0.95,0.8 0.28,0.03 0.13,-0.26 1.37,1.07 0,0 0.19,0.05 0,0 0.5,0.24 0.16,0.33 1.07,0.07 0.2,0.26 1.89,0.67 1.48,-0.68 1.34,0.55 0.84,-0.01 1.53,0.84 0.84,0 0.17,-0.48 0.29,-0.15 0.48,0.19 0.13,0.35 0,0 0.38,0.84 -0.06,0.69 0.27,0.36 0.54,0.14 -0.03,0.49 0.42,-0.04 0.41,0.3 1.01,-0.02 1.1,1.65 1.55,0.47 1.07,1.12 -0.03,0.27 0.46,0.1 0.23,0.65 0.56,0.23 -0.05,0.51 2.32,1.51 0.16,0.49 1.69,0.47 0.19,0.26 0.85,0.11 0.81,0.62 1.2,0.45 0.18,0.42 0.88,0.25 0.87,0.78 1.19,0.28 0.81,0.72 0.86,0.24 0.31,0.66 1.61,1.35 1.86,-0.13 1.37,0.51 2.53,-0.42 0.21,0.2 -0.06,0.38 -0.81,2.3 -0.39,0.6 -0.88,0.19 -0.45,0.53 0,1.72 0,0 -1.05,0.5 -0.07,0.29 -0.37,0.03 -0.59,0.08 -1.63,1.28 -0.21,1.74 0.71,-0.03 -0.84,1.16 -0.73,2.03 -0.12,0.99 0.1,0.64 0.32,0.4 -0.18,0.53 -1.36,1.94 -0.62,1.5 -0.19,0.09 -0.5,-0.5 -0.33,0 -0.84,0.78 -1.23,2.61 -1.36,5.89 -0.26,2.07 0.03,3.7 0.36,1.54 -0.78,-0.06 -0.5,1.42 -0.04,2.11 0.61,1.28 0.84,0.96 0.06,0.49 -0.09,0.23 -0.51,0.61 -0.06,0.63 0,0 -0.27,-0.78 -1.74,0.53 -0.6,-0.87 -0.24,0.28 -0.23,-0.08 -0.17,-1.05 -1.53,1.06 -1.06,-0.03 -0.12,0.18 -0.75,-0.73 -0.11,-0.46 -0.49,0.47 -0.01,-1.45 -0.44,-0.04 -0.19,-0.64 -0.47,-0.03 -0.24,-0.74 -0.42,-0.05 -0.16,-0.67 -0.36,-0.2 -0.54,0.73 -1.12,-0.11 -0.31,-0.44 -0.03,-0.58 -1.01,-1.05 -1.59,-2.61 -0.4,-0.19 -1.29,-0.05 -0.94,0.75 -0.66,-0.46 -1.26,0.91 -0.58,-0.78 -0.82,-0.24 -0.78,0.56 -0.62,-0.3 0.04,-0.64 -0.46,-1.02 -0.8,-0.74 0.2,-0.21 -0.13,-0.97 -0.82,-1.01 -0.48,-0.02 -0.18,0.4 -0.33,-0.04 -0.55,1.21 -0.73,0.57 -0.07,0.54 -0.5,0.25 -0.23,0.49 -0.24,-0.81 -0.59,-0.1 0.09,-0.64 -0.39,-0.22 0.08,-0.75 -0.22,-0.43 0.23,-0.96 -0.14,-0.55 0.61,-0.55 -0.3,-1.2 0.24,-0.28 0.04,-0.66 -0.64,-0.04 0.11,-0.29 -0.52,-1.46 -0.7,-1.01 -0.48,0.18 -0.16,0.47 -0.61,-0.18 -0.87,-0.96 -1.31,-0.89 -0.41,0.19 -0.37,-0.28 -0.13,-0.79 -0.56,-0.61 0.44,-0.5 -0.01,-0.34 -0.44,-0.13 -0.94,-0.9 0.11,-1.4 -0.51,-1.75 0.22,-1.43 0.56,-0.31 0.46,-1.26 1.54,0.15 0.38,-0.66 -0.5,-0.97 -0.08,-0.66 -0.38,-0.36 -0.15,-0.77 -0.7,-0.42 -1.32,0.04 -0.68,-0.65 -1.14,-0.27 -2.38,0.44 0.02,-0.47 -0.21,-0.19 -0.28,0.21 -1.28,-0.76 -0.99,0.01 0.07,-0.41 -1.22,-0.71 -0.5,-0.01 -0.36,-0.92 -1.77,0.12 -0.35,-0.45 -0.51,-0.14 0,0 0.5,-0.32 0.19,-0.33 -0.11,-0.32 0.79,-1.01 2,-1.5 0.21,-0.99 0.92,-0.84 -1.5,-0.28 -0.24,-0.29 0.04,-0.38 -1.4,-0.78 -0.65,-0.88 0.42,-0.44 -0.14,-0.47 -1.54,0.65 -1.85,0.3 -1.03,-0.4 -0.5,-0.36 -0.44,-0.25 -0.19,-0.59 -2.34,-1.52 -2.89,-0.15 -1.33,-0.78 -1.34,-0.17 -0.37,-0.82 -0.87,-0.09 -0.73,0.31 -0.88,-0.78 -0.24,-0.7 0.07,-1.05 0.35,-0.92 -0.08,-0.73 0.32,-2.03 -0.32,-0.28 0.09,-0.77 0.7,-0.5 0.83,-0.51 1.2,-0.44 0.59,-0.46 0.12,-0.43 1.59,0.32 0.3,-0.48 -0.53,-0.2 -0.68,-1.09 -0.56,-0.67 -1.09,-0.53 -0.06,-0.44 0.37,-0.23 -0.13,-0.28 -0.45,-0.33 -1.79,0.75 -0.18,0.54 -0.46,0.28 0.31,0.29 -0.2,0.74 -0.83,0.63 -1.8,0.02 -1.04,0.45 -0.37,-0.39 -0.49,0.11 -0.11,1.06 -0.74,0.49 -0.78,0.11 -1.14,0.79 -0.97,-0.21 -0.93,-0.16 -0.88,-0.03 -0.35,-0.26 -0.33,-0.4 -0.17,-0.78 0.46,-1.19 -1.12,-0.9 -0.78,-0.12 0.22,-0.47 -0.25,-0.92 0.84,-0.67 -0.25,-0.54 1.39,0.03 1.19,-1.06 1.93,-0.42 1.24,-1.51 0.51,-0.01 0.96,-0.48 -0.13,-0.54 0.55,-0.79 0.36,-1.21 1.13,-0.53 0.77,0.07 0.36,-0.19 0.87,0.26 0.54,-0.82 0.73,-0.29 0.52,-1.43 0.22,-0.05 0,0 0.57,0.75 0.37,0.14 0.31,0.74 0.48,0.11 0.62,0.56 0.08,0.92 0.45,-0.11 0.15,-0.31 0.92,-0.14 0.38,0.11 0.32,0.88 0.69,0.75 0.68,-0.11 0.39,-0.4 0.69,0.51 0.37,-0.47 0,0 0.93,-0.02 0,0 -0.12,-2.01 0.71,-0.79 0.2,0.14 0.2,-0.31 1.4,-0.77 -0.2,-0.37 z"
                            id="tinh-thanh-hoa" data-id="tinh-thanh-hoa" data-link="{PROVINCE_LIST_401.link}"
                            data-tinh="{PROVINCE_LIST_401.title}" data-province="{PROVINCE_LIST_401.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 143.90847,189.31323 0.51,0.14 0.35,0.45 1.77,-0.12 0.36,0.92 0.5,0.01 1.22,0.71 -0.07,0.42 0.99,-0.01 1.28,0.76 0.28,-0.21 0.21,0.19 -0.02,0.47 2.38,-0.44 1.14,0.27 0.68,0.65 1.32,-0.04 0.7,0.42 0.15,0.77 0.38,0.36 0.08,0.66 0.5,0.97 -0.38,0.66 -1.54,-0.15 -0.46,1.26 -0.56,0.32 -0.22,1.43 0.51,1.75 -0.11,1.4 0.94,0.9 0.44,0.13 0.01,0.34 -0.44,0.5 0.56,0.61 0.13,0.79 0.37,0.28 0.41,-0.18 1.31,0.89 0.87,0.96 0.61,0.18 0.16,-0.47 0.48,-0.17 0.7,1.01 0.52,1.46 -0.11,0.29 0.64,0.04 -0.04,0.67 -0.24,0.28 0.3,1.2 -0.61,0.55 0.14,0.55 -0.23,0.97 0.23,0.43 -0.08,0.76 0.39,0.22 -0.09,0.64 0.59,0.1 0.24,0.81 0.23,-0.49 0.5,-0.25 0.07,-0.54 0.73,-0.57 0.55,-1.21 0.33,0.04 0.18,-0.4 0.49,0.03 0.82,1.01 0.13,0.97 -0.2,0.21 0.8,0.74 0.46,1.02 -0.04,0.64 0.62,0.3 0.79,-0.56 0.82,0.24 0.58,0.78 1.26,-0.91 0.66,0.46 0.94,-0.75 1.29,0.05 0.4,0.19 1.59,2.61 1.01,1.05 0.03,0.58 0.32,0.44 1.12,0.12 0.54,-0.72 0.36,0.2 0.16,0.67 0.42,0.05 0.25,0.74 0.47,0.03 0.19,0.64 0.45,0.04 0.01,1.45 0.49,-0.47 0.11,0.46 0.75,0.73 0.12,-0.18 1.06,0.03 1.53,-1.06 0.17,1.05 0.23,0.08 0.24,-0.28 0.6,0.87 1.74,-0.53 0.27,0.78 0,0 -0.4,0.83 -0.65,-0.23 -0.3,0.29 -0.47,1.75 0.12,0.21 -0.35,0.42 -0.46,0.13 -0.27,-0.41 -0.74,0.72 -0.35,1.72 0.17,1.16 -0.17,0.14 -0.07,2.19 0.49,1.06 -1.09,0.4 -1.33,0.84 -0.26,-0.08 0,-0.29 -0.42,-0.38 -0.46,-0.03 -0.56,0.35 -0.91,1.11 -0.84,1.76 -0.4,2.67 0.01,0.53 0.2,1.75 0.72,2.64 0.77,0.32 0.55,0.12 0.61,0.44 0.61,0.24 0.32,0.18 0.9,1.37 0.36,0.29 0.25,0.61 -0.29,0.14 0.79,1.72 1.76,1.94 0,0 -0.97,0.8 -0.13,0.43 1.26,2.92 0.12,0.55 -0.3,0.4 0,0 -1.1,0.13 0,0 -0.82,-0.23 -0.57,0.34 -0.67,1.83 -0.62,0.26 -0.04,1.74 -0.64,1.02 -0.69,-0.39 -2,0.9 -0.48,-0.51 -0.88,0.01 -0.5,0.24 -0.32,0.76 -1.19,-0.04 -0.76,0.4 -0.79,-0.48 -0.23,-0.82 -0.43,-0.44 -0.16,-0.82 -0.56,-0.61 -0.64,-0.01 0,0 -0.52,-0.14 0,0 -0.29,-0.05 0,0.35 -0.89,0.36 -0.96,0.14 -0.31,0.6 -0.7,0.11 -0.18,-0.29 -1.06,0.6 -1.06,0.08 -2,-0.65 -0.44,-0.62 -1.32,-0.3 -0.54,-0.82 -0.32,0.14 -0.35,-0.22 -1.21,0.44 -0.9,0.61 -0.63,0.91 -0.57,-0.53 -1.49,-0.16 0,0 -0.1,-0.21 0.26,-0.34 -0.16,-0.83 0.41,-0.99 -0.81,-0.89 -0.69,0.18 -0.84,-1.19 -0.79,-0.42 -0.06,-0.19 0.51,-0.72 -0.61,-0.73 -0.95,0.09 -0.57,0.52 -1.04,-0.22 -0.56,0.2 -0.35,-0.19 -0.8,0.04 0.46,-0.87 -0.46,-0.31 -0.68,0.23 -0.11,-0.39 -0.69,0.09 -0.26,-0.44 -1.08,0.12 -0.82,-0.03 -0.94,-0.15 -1.12,0.14 -0.11,-0.64 -0.3,-0.36 0.3,-0.37 -0.09,-0.37 -1.26,-0.89 -2.08,0.12 -0.78,0.62 -0.88,-0.58 -2.17,0.23 -0.54,-0.43 -0.34,-0.71 -1.14,0.47 -1.12,-0.46 -0.15,-0.73 -0.78,-0.16 -0.43,-1.01 -0.77,-0.15 -0.33,0.26 -0.71,-0.15 -0.43,-0.35 -0.07,-0.57 -2.08,-1.31 -0.01,-0.53 -1.86,-0.82 -0.63,0.04 -0.99,-2.03 -0.71,-0.75 -0.31,-1.18 -0.63,-0.49 -0.86,0.13 -0.94,-1.32 -0.73,-0.04 -0.53,1.03 -1.36,0.11 -1.36,-1.2 -0.01,-0.6 -1.12,-1.28 0.06,-0.7 -0.31,-0.3 -0.77,-0.2 -0.63,-0.65 -1.01,0.08 -1.97,-0.98 -0.27,-0.24 -0.23,-1.01 -0.56,-0.26 -0.85,-0.18 -1.3,0.52 -1.5,-0.37 -0.3,-0.58 0.6,-1.17 -0.06,-0.32 -1.29,-0.33 -0.4,-1.09 -0.59,-0.82 -2.72,-0.4 -0.83,-1.02 -0.52,0.46 -0.47,-0.76 -1.18,-1.03 -3.619999,0.85 -0.56,-0.52 -1.51,-0.61 -1.87,-1.76 -0.45,-0.98 -1.05,-0.34 -0.51,0.39 -0.64,-0.42 0.09,-0.57 0.6,-0.49 1.14,-0.14 0.56,-0.55 0.6,-0.15 1.09,-0.73 0.38,-0.6 -0.16,-0.77 1.04,-0.85 0.61,-0.08 0.39,-0.35 0.83,0.16 0.52,-0.75 0.43,0.06 0.98,-0.57 0.23,0.4 0.919999,0.16 0.19,-1.82 -0.54,-0.22 0.99,-0.85 0.48,-1.61 0.39,0.38 0.46,0.09 0.62,-0.63 -0.95,-1.13 0.26,-0.61 -0.07,-2 -0.26,-0.34 -0.83,-0.37 -1.329999,-2.05 0.389999,-0.9 -0.519999,-0.88 0.519999,-0.61 0.14,-1.04 1.92,1.14 1.33,0.06 1.16,-0.51 0,-0.95 0.24,-0.53 1.1,0.19 0.68,-0.08 0.78,-0.42 1.31,-0.23 1.45,0.18 0.45,0.35 0.81,0.18 0.96,-0.15 0.91,0.7 -0.17,0.76 0.21,0.11 0.87,-0.53 0.61,-0.76 1.64,-0.17 1.12,-0.48 0.53,-0.08 0.55,0.22 -0.14,1.09 0.31,0.42 0.96,0.01 0.22,0.82 0.24,0.14 1.88,0.01 0.46,0.39 0.39,1.25 1.09,0.57 0.56,0.04 1.2,-0.07 0.98,-0.76 1.46,-0.32 1.26,0.7 0.55,0 0.45,-0.77 -0.28,-0.92 0.84,-0.45 -0.16,-0.57 0.61,-0.73 0.06,-0.84 0.18,-0.53 0.8,-0.91 0.69,-0.37 0.49,-1.14 0.55,-0.22 0.51,0.35 1.03,0.11 0.65,-0.81 -0.25,-0.5 0.66,-0.13 1.15,-0.77 1.1,0.34 0.72,-0.39 0.22,-0.72 -0.19,-0.54 0.12,-0.7 0.55,-0.81 -0.58,-0.34 -0.05,-0.28 0.76,-0.92 -0.34,-1.07 0.03,-1.31 0.15,-0.74 0.79,-0.77 1.44,-0.7 0.84,-0.79 0.25,-1.35 0,0 -0.01,-0.03 0,0 -0.16,-0.55 z"
                            id="tinh-nghe-an" data-id="tinh-nghe-an" data-link="{PROVINCE_LIST_403.link}"
                            data-tinh="{PROVINCE_LIST_403.title}" data-province="{PROVINCE_LIST_403.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 188.76847,257.23323 0.42,0.11 0.1,0.26 1.01,4.59 1.36,3.65 1.69,3.14 0.51,0.77 2.86,4.15 -0.5,0.48 0.02,1.23 -0.42,0.94 0.27,0.01 0.55,-0.48 0.42,-0.88 -0.14,-0.89 0.71,-0.97 0.3,0.7 2.49,3.75 4.16,4.62 1.58,1.29 0.62,0.26 0.4,0.14 1.92,-0.04 1.42,0.8 2.53,2 0.89,0.06 2.75,3.19 2.66,2.39 -0.12,0.2 -0.72,-0.38 -0.2,0.16 0.36,0.49 0.16,0.44 0.88,-0.69 0.91,0.11 0.51,0.4 0.74,-0.01 0.2,-0.43 -0.12,-0.32 0.43,-0.58 0.4,-0.11 0.07,0.27 0.63,-0.07 -0.77,1.09 0.79,2.45 1.68,3.19 0.28,0.08 1.15,1.77 0.81,0.27 0,0 -0.89,-0.04 -0.72,0.38 -0.59,-0.14 -0.98,0.37 -0.31,-0.13 -0.02,-0.92 -0.44,-0.2 -0.66,0.17 -0.29,-0.25 -0.38,0.08 -0.51,-0.47 -3.13,-0.56 -0.53,0.65 -0.31,0.91 -0.93,0 -0.5,0.95 -0.69,0.3 -0.31,1.13 -0.89,0.19 -0.99,-0.79 -0.2,0.16 -0.68,-0.15 -0.86,0.27 -0.39,0.46 -0.46,-0.37 -1.16,0.47 -1.97,-0.33 -0.61,-0.37 -2.18,-0.3 -1.65,-0.71 0.13,-1.03 1.09,-1.09 0.47,-0.13 -0.61,-0.83 0,0 -0.4,-0.27 0,0 0.09,-0.31 -0.53,-0.22 -0.22,-0.6 -0.63,0.41 -0.65,-0.19 -1.84,-2.15 -0.53,0.08 -0.42,0.38 -0.36,-0.28 -0.56,0.07 -0.05,-0.43 -0.82,-1.18 -0.8,0.38 -0.35,-0.34 -0.88,-0.15 -1.21,1.24 -1.14,0.02 -0.08,0.46 0.84,0.86 -0.15,0.7 -0.6,0.35 -0.61,-0.06 -1.11,-0.75 -0.47,0.34 0.1,0.31 -1.1,0.04 -0.6,0.45 -0.38,-0.36 -0.65,-0.14 -0.37,-0.43 -0.6,-0.16 -0.41,0.14 -0.53,0.7 0.07,0.73 -0.83,0.83 -3.25,0.01 0,0 -0.77,-0.12 -0.79,-0.93 -0.46,0.11 -0.34,0.41 -0.25,-0.36 -0.63,-0.28 -0.09,-0.68 0.23,-1 -0.18,-0.92 -0.21,-0.43 -0.28,-0.77 -0.68,0.19 -0.93,-0.08 -0.25,-1.84 -0.68,-0.54 -0.85,0.08 -0.1,-0.79 0.45,-0.57 -0.53,-1.98 -0.16,-0.16 -0.29,0.67 -0.28,0 -0.08,-0.49 0.35,-0.8 -0.42,-0.01 -0.27,0.25 -1.06,-0.53 -0.67,0.43 -0.39,0.85 -0.43,0.16 -0.57,-0.24 -0.58,1.6 -0.89,-0.3 -0.45,-0.56 -0.37,0.53 -0.44,-0.03 -0.55,-0.92 -0.42,-0.28 -0.68,0.02 -0.38,-0.52 -0.46,-0.46 0.31,-0.51 -0.14,-0.99 0.42,-1.35 -0.14,-0.65 -0.95,-0.08 -0.56,1.63 -0.2,-0.48 0.19,-0.53 -0.91,-0.32 -0.57,0.05 -0.8,0.26 0.13,-0.79 -0.77,-1.47 -0.63,-0.35 -0.69,0 -0.47,-0.62 -1.26,-0.57 0.24,-1.56 -0.54,-0.97 0.32,-0.82 -0.62,-0.32 -0.29,0.01 -0.1,0.27 -0.77,-0.08 -0.17,-0.81 -0.52,-0.33 -0.37,-0.3 -0.5,-0.21 -0.11,-0.72 -0.45,-0.13 -0.33,-0.71 0.23,-0.48 0.04,-0.47 -0.07,-0.62 0.33,-0.81 -0.19,-0.61 0.28,-0.93 0,-0.44 -0.02,-0.61 0.71,-0.51 -0.01,-0.43 0.28,-0.77 0.05,-1.18 1.11,-0.81 0.18,-0.38 1.53,0.72 0,0 1.49,0.16 0.57,0.53 0.63,-0.91 0.9,-0.61 1.21,-0.44 0.35,0.22 0.32,-0.14 0.54,0.82 1.32,0.3 0.44,0.62 2,0.66 1.06,-0.07 1.06,-0.6 0.19,0.29 0.7,-0.11 0.31,-0.6 0.96,-0.14 0.89,-0.36 0,-0.35 0.29,0.05 0,0 0.52,0.14 0,0 0.64,0.01 0.56,0.61 0.16,0.82 0.44,0.44 0.23,0.82 0.8,0.48 0.76,-0.4 1.19,0.04 0.32,-0.76 0.5,-0.24 0.88,-0.01 0.48,0.51 2,-0.89 0.69,0.39 0.64,-1.02 0.05,-1.74 0.62,-0.26 0.67,-1.83 0.57,-0.34 0.82,0.24 0,0 1.1,-0.13 0,0 0.3,-0.39 -0.12,-0.55 -1.26,-2.92 0.13,-0.43 0.97,-0.79 0,0 0.27,0.19 z"
                            id="tinh-ha-tinh" data-id="tinh-ha-tinh" data-link="{PROVINCE_LIST_405.link}"
                            data-tinh="{PROVINCE_LIST_405.title}" data-province="{PROVINCE_LIST_405.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 182.21847,299.00323 3.25,-0.01 0.83,-0.83 -0.07,-0.73 0.53,-0.7 0.41,-0.14 0.6,0.16 0.38,0.43 0.65,0.14 0.38,0.36 0.6,-0.45 1.1,-0.04 -0.1,-0.31 0.47,-0.34 1.11,0.75 0.62,0.07 0.6,-0.35 0.16,-0.7 -0.84,-0.86 0.08,-0.46 1.14,-0.02 1.21,-1.24 0.88,0.15 0.35,0.34 0.8,-0.38 0.82,1.18 0.05,0.44 0.56,-0.07 0.3,0.2 0.42,-0.38 0.53,-0.08 1.84,2.15 0.65,0.19 0.63,-0.41 0.22,0.6 0.53,0.22 -0.09,0.31 0,0 0.4,0.27 0,0 0.61,0.83 -0.47,0.13 -1.09,1.1 -0.13,1.03 1.65,0.72 2.18,0.3 0.61,0.37 1.97,0.34 1.16,-0.47 0.46,0.37 0.39,-0.46 0.86,-0.27 0.68,0.15 0.2,-0.16 0.99,0.79 0.89,-0.19 0.31,-1.13 0.69,-0.3 0.5,-0.94 0.93,0 0.31,-0.91 0.54,-0.65 3.13,0.56 0.51,0.48 0.38,-0.08 0.29,0.25 0.66,-0.17 0.44,0.2 0.02,0.92 0.31,0.13 0.98,-0.36 0.59,0.14 0.72,-0.38 0.9,0.04 0,0 -0.32,0.13 0,0.26 0.27,0.53 -0.07,0.31 -0.36,0.76 -0.99,0.17 -1.62,2.64 -0.35,1.16 0.06,1.58 0.95,3.44 1.34,2.78 0.32,0.19 0.38,1.3 1.46,2.43 0.87,0.81 1.91,3.6 3.73,5.59 2.24,2.58 3.66,3.66 3.48,3.15 0.93,0.5 3.6,3.01 3.71,2.62 0,0 -2.5,2.58 -1.77,0.73 -1.68,1.26 -0.81,-0.01 -0.35,0.21 0.3,1.27 -0.63,0.67 -0.92,0.28 -0.78,-0.37 -0.49,0.37 0.03,0.33 -0.39,0.4 0.3,1 -0.23,0.51 -0.65,0.36 -0.4,-0.03 -0.27,0.51 0.12,0.42 -0.37,0.84 0.2,1.27 -0.3,0.3 -0.74,-0.21 -0.75,0.39 -1.06,-0.47 -0.38,-0.62 -0.79,-0.04 -0.42,-0.75 -0.37,-0.07 -1,0.47 -0.37,0.59 -1.58,-0.26 -1.19,0.11 -0.38,-0.4 0,-0.52 -0.34,-0.14 -0.35,-0.68 0.02,-0.32 0.36,-0.23 -0.06,-0.31 -0.42,-0.14 -1.15,0.14 -0.65,-0.55 0,0 -0.17,-0.44 -0.83,1.44 -0.81,0.4 -0.97,-0.51 -1.3,-0.13 -0.33,-0.22 -0.23,-0.71 -1.1,0.31 -0.65,-0.69 -0.2,-1.33 -1.11,-1.13 0.73,-1.38 -1.33,-1.34 -0.76,-0.28 -0.31,-0.87 -0.72,-0.5 -0.07,-0.54 0.02,-0.81 -0.91,-0.5 -0.51,-0.61 -0.32,-0.65 0.22,-0.41 -1.14,-0.46 0.18,-0.55 0.92,0.14 0.46,-0.62 0,-0.37 -1.39,-0.28 0.54,-0.5 -0.69,-0.52 0.17,-0.69 -0.21,-1.33 -1.48,0.04 0.29,1.1 -0.18,0.52 -1.33,0.72 -0.32,0.1 -0.51,-0.04 -1.81,-1.25 -1.99,-1.67 -0.87,-0.27 -0.59,-1.39 -2.24,-1.46 -11.95,-14.4 -0.91,0.01 -0.4,-0.72 -0.37,-0.52 -0.81,-0.54 -0.38,0.22 -0.65,-0.43 -0.13,-0.35 -0.59,-0.25 -1.52,0.35 -0.37,-2.81 -0.71,-0.07 0,-1.24 -0.57,-0.65 -0.94,-0.28 -0.67,-1.25 -0.65,-0.58 0.45,-1.31 -0.33,-0.83 -0.23,-0.63 -0.67,-0.54 -1.25,-0.32 -0.87,-0.63 -0.61,-0.16 0.04,-1.08 0.66,-1.1 0.12,-1.33 0.62,-1.02 0.36,-0.11 0.02,-1.68 z"
                            id="tinh-quang-binh" data-id="tinh-quang-binh" data-link="{PROVINCE_LIST_407.link}"
                            data-tinh="{PROVINCE_LIST_407.title}" data-province="{PROVINCE_LIST_407.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 252.54847,344.00323 2.1,1.62 4.29,2.57 0,0.51 -0.3,0.29 0.27,1.48 -0.4,0.26 0.21,0.65 -0.11,0.64 1.8,3.12 2.34,2.79 0.38,0.12 2.69,2.85 3.22,2.82 3.89,3.01 0,0 -0.91,0.44 -0.72,1.29 -0.27,0.11 0.37,0.51 -0.46,0.12 -0.83,1.15 -0.49,0.3 0.33,0.2 -0.62,0.24 0.54,0.65 0,0.38 -0.35,-0.56 -0.8,0.69 0.2,0.28 0.29,-0.21 -0.29,0.77 0.49,0.21 0.45,0.7 -0.69,0.72 -2.29,1.55 -1,-0.59 -0.3,-0.55 -1.49,0.51 -0.85,-0.31 -2.45,0.45 0,0 -0.68,0.16 0,0 -0.73,0.39 -0.26,0.68 -1.05,-0.03 -0.75,0.36 -0.55,-0.02 -0.12,0.21 -0.74,-0.23 0.09,0.72 -0.37,0.66 0.69,0.58 0.63,0.14 0.17,0.3 0.63,-0.13 0.21,0.79 0.27,-0.28 0.99,0.65 0.63,1.5 -0.64,0.71 -0.9,0.11 -0.93,-0.48 -1.49,0.03 -0.52,1.24 0.47,0.67 0.06,1.37 -0.31,0.33 -0.44,0.06 -0.31,0.58 -0.48,-0.03 -0.73,0.49 0.39,0.32 -0.07,1.88 0.28,0.53 0,0 -0.4,0.41 -0.81,-0.56 -1.76,0.62 -0.52,-0.11 0.37,-1.71 -0.15,-0.98 -2.1,-0.78 -1.37,-1.18 -0.33,-1.04 0.36,-0.87 -0.37,-0.05 -0.25,0.28 -0.69,-0.27 -0.1,-0.29 0.87,-1.04 -0.07,-0.58 -0.49,-0.4 -0.04,-0.33 0.1,-0.47 0.42,-0.36 0.03,-0.99 0.17,-0.12 -0.2,-0.56 -1.24,-1.03 -1.66,-0.42 -0.35,1.04 0.23,0.4 -1.37,3.15 -0.29,-0.63 -1.33,0.41 0.23,0.47 -0.18,1.46 -0.47,0.38 -0.89,0.07 0.01,0.37 -0.32,0.33 -0.3,-0.73 -0.41,-0.03 -0.56,-0.44 -1.42,-0.32 -0.07,-0.25 0.32,-0.33 -0.49,-0.45 -0.39,0.31 -0.1,-1.26 -0.68,0.13 0.16,-0.72 -0.22,-0.34 0.29,-0.58 -0.57,-0.34 -0.35,-0.64 0,-0.92 0.65,-0.83 0.65,-0.33 -0.53,-0.71 -0.32,-0.44 -0.5,-0.67 -0.74,0.16 -0.42,-0.15 0.11,-0.42 -0.84,-0.29 -1.26,0.32 -0.19,-0.45 0.44,-0.38 -0.01,-0.37 -1.26,-0.87 0.03,-1 -0.8,-1.95 0,-9.22 -0.66,-0.33 -0.4,-0.82 -0.61,-0.18 -0.26,-0.33 0.1,-0.77 0.18,-0.64 0.24,-0.2 1.21,-0.01 0.14,-3.87 0,0 0.65,0.55 1.15,-0.14 0.42,0.14 0.06,0.31 -0.36,0.23 -0.07,0.25 0.35,0.68 0.34,0.14 0,0.52 0.38,0.4 1.19,-0.11 1.58,0.26 0.37,-0.59 1,-0.47 0.38,0.08 0.42,0.75 0.73,-0.04 0.39,0.62 1.06,0.47 0.76,-0.39 0.74,0.21 0.31,-0.3 -0.2,-1.27 0.37,-0.84 -0.12,-0.42 0.27,-0.51 0.4,0.03 0.65,-0.36 0.23,-0.51 -0.3,-1 0.39,-0.4 -0.03,-0.33 0.49,-0.37 0.78,0.37 0.93,-0.28 0.63,-0.67 -0.29,-1.27 0.35,-0.21 0.81,0.01 1.68,-1.26 1.77,-0.73 2.5,-2.58 0,0 -0.04,0.03 z"
                            id="tinh-quang-tri" data-id="tinh-quang-tri" data-link="{PROVINCE_LIST_409.link}"
                            data-tinh="{PROVINCE_LIST_409.title}" data-province="{PROVINCE_LIST_409.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 288.31847,377.43323 5.19,4.01 5.1,4.69 1.89,1.24 -0.32,0.52 -1.2,0.31 -0.9,-0.28 -0.66,-0.51 -0.39,0.66 -0.03,-0.39 -1.14,-1.65 -1.43,-1.29 -0.85,-1.18 -0.43,-0.01 -0.21,-0.24 -0.8,-1.59 -0.65,-0.39 -0.17,-0.96 -0.49,-0.43 -0.51,0.05 0.03,-0.55 -0.37,-0.39 -0.59,-0.51 -0.36,0.06 -0.52,-0.73 -1,-0.44 -0.32,-0.62 1.13,0.62 z m -2.71,-1.43 0.92,0.37 0.49,0.52 -0.2,0.09 -1.36,-0.75 -0.07,-0.25 0.22,0.02 z m -12.55,-9.17 6.89,5.53 4.36,2.97 0.64,0.66 -0.52,0.21 -0.72,-0.81 -0.85,-0.54 -3.78,-1.96 -0.66,-0.55 -0.84,-0.25 -1,-0.78 -0.56,-0.04 -0.36,0.32 -0.46,0 -0.3,-0.66 0.01,0.73 0.48,0.36 0,0.25 1.16,0.14 0.74,1 0.42,-0.01 0.9,1.06 1.46,-0.15 0.03,0.27 0.79,0.61 0.59,0.51 0.51,0.33 0.82,-0.12 0.22,-0.34 0.4,-0.05 0.14,0.78 0.78,0.39 0.41,0.24 0.49,0.12 0.15,0.3 0.96,-0.4 0.12,0.52 -1,0.54 -0.06,0.78 0.25,0.63 0.35,-0.39 0.78,-0.16 -0.22,0.49 -0.23,0.05 0.17,1.03 1.89,-1.62 0.74,1.32 0.58,0.21 1.08,1.35 1.27,0.69 0.5,1.11 0.81,0.6 1.5,1.86 0.8,0.51 0.48,0.9 -0.1,0.67 -1.69,-1.66 -0.53,-0.24 -0.17,-0.42 -0.37,0.12 0.17,0.24 -0.43,1.05 0.32,0.63 -0.62,0.42 1.01,-0.15 0.19,-0.3 0.3,1 -0.34,0.27 0.21,0.82 1.23,1.32 1.5,0.19 0.82,-0.58 -0.14,0.61 0.39,0.51 0.74,-0.15 0.32,0.19 0.72,-0.47 0.32,-0.22 0.38,-0.19 0.03,-0.57 0.44,-0.81 0.05,-0.51 -0.38,-1.18 0.14,-0.24 0.88,0.69 0.35,0.03 0.14,-0.42 0.45,0.17 0.69,1.14 0.62,0.39 1.06,0.23 0.82,-0.19 0.54,-0.53 -0.54,-0.64 0.26,-0.37 0.74,0.51 0.39,1.05 -0.26,0.22 0.09,0.3 2.56,4.05 -0.09,0.24 -0.92,-1.41 -0.51,-0.43 -0.55,-0.2 -0.56,0.21 -0.17,0.28 0.33,1.41 0.48,0.33 0.43,0.31 0.67,-0.46 1.06,0.18 0.09,-0.37 1.36,0.88 0.77,-0.27 0.41,0.26 0.33,-0.46 1.16,0.67 0.63,-0.35 0.62,0.27 0,0 -1.15,0.39 -0.81,-0.13 -1.93,1.31 -1,-0.29 -0.42,0.35 -1.48,-0.21 -0.28,0.27 -1.16,-0.66 -1.12,-0.13 -0.48,-0.61 -0.89,-0.48 -0.98,1.07 -0.71,0.27 -1.37,-0.46 -0.96,-0.96 -0.82,0.56 -0.28,-0.07 -0.16,0.37 -0.58,0.14 0.09,0.41 -0.53,0.34 0.27,0.27 0,0 0.14,0.91 0,0 -0.81,0.42 -0.17,2.1 -1.22,0.95 -0.57,1 0,0 0.17,0.38 -0.26,0.31 0,0 -0.07,0.36 0,0 -0.16,0.27 -0.33,0.02 -0.98,-0.66 -0.47,0.18 -0.4,-0.25 0.07,0.41 -0.32,0.41 -0.38,0.11 -0.19,0.33 -0.46,0.04 0,0 0,0 0,0 -1.17,0.7 0.07,0.61 -0.21,0.55 -0.85,0.67 -0.58,0.09 -0.99,-0.24 -0.31,-0.69 -0.42,-0.35 -0.66,-0.06 -0.91,-0.6 -0.69,-0.12 0.27,-0.9 -0.31,-0.76 -1.27,0.4 -1.22,0.06 -0.95,0.57 0.36,0.76 -0.02,0.47 -0.63,0.58 -1.88,-0.3 -0.43,-0.82 -0.67,-0.5 -0.77,0.34 -0.46,0.52 0,0 -1.06,-0.77 0.13,-1.21 0.62,-0.59 -0.63,-1 -0.3,0.46 -0.43,-0.03 -0.4,0.3 -0.81,-0.35 -0.43,0.34 -0.23,0.84 -0.52,0.01 -0.59,-0.63 -0.62,0.69 -1.01,-0.06 -0.4,0.37 -1.62,-1.38 -1.54,-1.86 -0.57,-0.17 -0.23,-0.97 -0.43,0.07 -0.62,-0.65 -1.48,0.35 -0.84,-0.15 -0.79,-0.45 -0.66,-0.76 -0.54,-0.08 -0.57,-1.21 -0.24,-1.26 0.24,-1.28 0.41,-1.01 -0.6,-0.78 -3.07,-2.23 -0.74,0.45 -1.05,-0.03 -0.31,0.54 -0.92,-0.87 0,0 -0.28,-0.53 0.07,-1.88 -0.39,-0.32 0.73,-0.49 0.48,0.03 0.31,-0.58 0.44,-0.06 0.31,-0.33 -0.06,-1.37 -0.46,-0.67 0.52,-1.24 1.49,-0.03 0.93,0.48 0.9,-0.11 0.64,-0.71 -0.63,-1.5 -0.99,-0.65 -0.27,0.28 -0.2,-0.79 -0.63,0.14 -0.17,-0.3 -0.63,-0.14 -0.69,-0.58 0.37,-0.66 -0.09,-0.72 0.74,0.23 0.12,-0.2 0.55,0.02 0.75,-0.36 1.05,0.03 0.26,-0.68 0.73,-0.39 0,0 0.69,-0.16 0,0 2.45,-0.45 0.85,0.31 1.49,-0.51 0.3,0.55 1,0.59 2.29,-1.55 0.69,-0.72 -0.45,-0.7 -0.48,-0.21 0.29,-0.77 -0.29,0.21 -0.2,-0.28 0.8,-0.69 0.35,0.56 0,-0.38 -0.54,-0.65 0.62,-0.24 -0.33,-0.19 0.49,-0.3 0.83,-1.15 0.46,-0.12 -0.37,-0.51 0.27,-0.1 0.72,-1.29 0.91,-0.43 0,0 0.02,-0.01 z"
                            id="tinh-thua-thien-hue" data-id="tinh-thua-thien-hue" data-link="{PROVINCE_LIST_411.link}"
                            data-tinh="{PROVINCE_LIST_411.title}" data-province="{PROVINCE_LIST_411.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 331.28847,408.12323 0.12,0.5 1.36,0.47 -0.07,1.4 0.16,0.37 -0.33,0.02 -0.24,-0.44 -0.82,-0.42 -0.35,-1 -0.4,-0.15 -0.3,0.21 -0.18,-0.39 -0.3,-0.1 0.99,-0.73 0.36,0.26 z m -54.23,-2.76 0.46,-0.52 0.77,-0.34 0.67,0.5 0.43,0.82 1.88,0.3 0.63,-0.58 0.03,-0.47 -0.36,-0.76 0.95,-0.57 1.22,-0.06 1.27,-0.4 0.31,0.76 -0.27,0.9 0.69,0.12 0.91,0.6 0.66,0.06 0.42,0.35 0.31,0.69 0.99,0.24 0.58,-0.09 0.85,-0.67 0.21,-0.55 -0.07,-0.61 2.21,-1.18 0.32,-0.41 -0.07,-0.41 0.4,0.26 0.47,-0.17 0.98,0.67 0.33,-0.02 0.16,-0.27 0,0 0.69,0.22 0.42,-0.31 1.17,-0.09 0.61,0.51 0.94,0.26 0.07,0.38 0.44,0.36 1.93,-1.27 1.13,-0.37 -0.15,1.5 0.55,0.46 0.05,0.43 0.73,0.44 -0.15,1.27 -1.25,1.09 -0.04,0.52 -0.34,0.5 1.91,1.62 1.01,-0.14 0.76,-0.47 1.09,-0.26 1.01,0.06 0.4,0.38 0.67,0.07 1.49,-1.22 1.48,0.94 0.46,-0.16 0.36,-1.06 0.6,0.09 0.18,0.52 0.52,0.11 0.28,-1.37 0.34,0.21 0.33,-0.18 0.47,0.17 -0.18,0.29 0.42,0.73 0.31,-0.29 0.32,0.06 0.11,-0.51 0.3,-0.05 0.01,-0.25 0.85,-0.67 0.52,0 0.24,0.48 0.37,-0.07 0.08,0.38 1,-0.39 0,0 0.63,0.99 1.4,1.42 3.23,1.99 -0.04,0.52 -0.25,-0.12 -0.62,0.45 0.22,0.42 1.28,-0.53 0.45,2.5 0.48,1.34 3.96,6.83 4.16,6.06 2.15,2.38 1.7,1.05 -0.5,0.04 -2.25,-1.58 -0.35,-0.02 -0.54,-0.35 -0.12,0.14 0.66,0.56 -0.72,0.58 0.58,1.51 0.59,0.47 0.59,-0.64 0.35,0.34 0.62,0.09 0.03,0.69 -0.31,0.06 0.02,0.4 0.96,0.77 0.44,-0.1 0.62,0.39 0.41,-0.49 0.35,-0.11 -0.04,-0.91 -0.42,0.03 0.86,-0.64 -0.03,-0.53 0.28,-0.12 0.18,0.21 -0.13,0.3 0.37,0.33 0.22,0.66 -0.26,0.91 2.15,2.28 0,0 -0.6,1.13 -0.48,0.25 -2.13,0.22 -0.27,0.22 -0.5,-0.29 -0.16,0.22 -0.89,-0.12 -0.63,0.56 -1.42,0.28 -0.06,0.37 -0.74,0.19 -0.3,-0.15 -1.12,0.57 -0.79,-0.06 -0.97,0.8 -1.1,-0.33 -1.03,1 -2.38,-0.25 -0.41,-0.71 -0.72,1.02 -0.69,-0.14 0,0.46 -0.55,0.14 -0.63,-0.29 -0.85,0.48 -2.35,-0.35 -0.98,0.92 0.3,0.83 -0.17,0.54 -1.12,0.79 0.45,0.56 -0.17,0.32 0.48,-0.09 0,0 0.56,0.14 0,0 -0.13,0.9 -0.79,0.4 0.28,0.38 0.44,0.12 -0.14,0.55 0.31,0.43 -0.58,0.33 -0.6,0.78 -0.1,0.48 -0.26,0.07 0.14,0.16 -0.22,0.17 0.04,0.3 -0.91,0.81 -0.05,1.38 -1.12,0.65 0.34,0.73 -0.24,0.93 0.3,0.34 -0.22,0.52 0,0 -1.37,-0.05 -0.69,-0.65 -0.26,0.2 -0.13,-0.44 -0.78,-0.62 -0.76,-0.05 -0.83,0.83 0.34,0.96 -0.19,0.3 0.05,1.09 0.28,0.23 0.39,1.23 -0.7,0.64 -1.57,0.16 -0.64,0.44 -0.99,0 -0.05,-0.6 -0.55,-0.31 -0.69,-0.01 -0.85,-1.27 -1.54,-0.51 -0.45,0.25 -0.28,-0.25 -0.69,0.18 -0.37,-0.58 -0.57,-0.14 -1.06,-2.22 0.67,-0.64 0.6,-0.19 0.14,-0.56 -0.16,-0.31 0,0 0.42,-0.09 0,0 0.5,-1.25 0.38,-0.41 -0.12,-0.51 0.28,-0.67 -0.21,-0.86 -0.6,-0.1 -0.34,-0.45 -1.62,-0.45 -0.08,-0.6 -0.61,-0.24 -0.32,-0.86 -0.86,-0.28 -0.07,0.15 0,0 -0.62,0.18 0,0 -0.51,0.69 -0.46,-0.19 -0.6,-0.75 -2.01,0.14 -0.7,-0.44 -0.51,0.23 -1.1,-0.36 -0.47,0.05 -0.41,-0.27 0.01,-0.37 -0.82,-0.37 -0.64,0.32 0,0 -0.39,0.02 0,0 -0.12,-0.25 0.31,-0.11 -0.35,-1.31 -0.31,-0.36 0.19,-0.13 -0.14,-1.74 0.22,-0.16 -0.86,-0.09 -0.39,0.23 -1.49,-1.67 -0.1,-0.48 -0.41,-0.1 -0.32,-0.48 0.08,-0.39 0,0 -0.02,-0.19 0,0 -0.23,-0.42 0.11,-0.6 -0.35,-0.68 -0.66,-0.5 -0.24,-0.5 -0.68,0.06 0,0 -1.11,-0.33 -0.77,0.46 -0.9,0.79 -0.57,0.18 -0.78,-0.59 -1.25,-0.39 -0.18,-0.3 0.48,-1.29 -0.6,-0.97 -0.07,-0.65 -1.09,-0.35 -0.53,-0.46 -0.6,-0.48 -0.87,-0.36 -1,-0.02 -0.26,0.22 0.02,0.52 -0.69,-0.01 -1.81,-0.18 -0.35,-1.96 -0.53,-0.92 -0.01,-1.21 -0.63,0.04 -0.51,-0.36 0.29,-0.63 -0.31,-0.86 0.05,-0.56 -0.43,-0.43 -0.96,-0.23 -1.35,0.13 -0.69,-0.49 -0.16,-0.6 -0.47,-0.53 -0.08,-0.24 0.62,-1.26 0.06,-0.74 -0.08,-0.44 -0.42,-0.34 -0.64,-0.59 -0.31,-0.3 0.28,-0.59 -0.26,-0.36 -1.89,0.31 -0.09,-0.99 0.4,-1.07 0.53,-0.37 -0.06,-1.04 -0.9,-1.22 0.56,-0.53 0.63,-0.43 0.74,0.34 0.45,-0.57 0.67,-0.25 0.61,-0.26 0.89,-0.57 0.78,0.03 0.23,-0.68 0.6,-0.35 0.94,-0.36 1.21,0.15 0.62,0.34 0.77,-0.28 0.61,-1.02 0.52,-0.92 0.46,-0.26 0.32,-1.59 0.67,-1.12 0.25,-0.46 0.17,-0.8 0.44,-1.19 z"
                            id="tinh-quang-nam" data-id="tinh-quang-nam" data-link="{PROVINCE_LIST_503.link}"
                            data-tinh="{PROVINCE_LIST_503.title}" data-province="{PROVINCE_LIST_503.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 285.26847,438.14323 0.68,-0.05 0.24,0.5 0.67,0.5 0.34,0.68 -0.11,0.6 0.24,0.43 0,0 0.02,0.19 0,0 -0.08,0.39 0.32,0.47 0.41,0.11 0.1,0.48 1.49,1.67 0.4,-0.23 0.85,0.09 -0.22,0.16 0.15,1.74 -0.19,0.12 0.32,0.37 0.34,1.3 -0.31,0.11 0.12,0.25 0,0 0.4,-0.02 0,0 0.63,-0.32 0.82,0.37 -0.01,0.37 0.41,0.27 0.48,-0.05 1.09,0.36 0.51,-0.23 0.7,0.44 2.01,-0.13 0.6,0.75 0.46,0.19 0.5,-0.69 0,0 0.63,-0.17 0,0 0.07,-0.16 0.86,0.28 0.32,0.86 0.61,0.24 0.08,0.6 1.63,0.45 0.34,0.46 0.6,0.1 0.21,0.86 -0.28,0.67 0.12,0.51 -0.39,0.41 -0.5,1.25 0,0 -0.42,0.09 0,0 0.16,0.31 -0.14,0.56 -0.6,0.2 -0.67,0.64 1.06,2.22 0.58,0.14 0.36,0.58 0.69,-0.18 0.28,0.25 0.45,-0.25 1.55,0.51 0.84,1.28 0.7,0.01 0.54,0.31 0.05,0.59 0.99,0.01 0.64,-0.45 1.57,-0.15 0.7,-0.64 -0.39,-1.23 -0.29,-0.23 -0.04,-1.09 0.19,-0.3 -0.34,-0.96 0.83,-0.83 0.76,0.05 0.78,0.62 0.13,0.44 0.26,-0.2 0.69,0.65 1.38,0.05 0,0 0.57,0.26 0.21,1.09 0.47,0.42 0.29,0.84 0.21,-0.04 0.3,0.35 0,0 0.28,0.15 0,0 -0.02,0.47 1.5,1 0.21,0.42 -0.11,0.2 0.22,0.37 0.18,-0.04 -0.16,0.18 0.12,0.64 0.9,0.27 0.56,0.56 0.13,0.95 0.54,-0.13 0.2,0.17 0.32,1.2 0,0 0.24,0.6 0.91,1.01 0,0 0.51,0.32 0,0 1.2,0.16 0,0 0.43,0.64 0.86,0.2 0.27,0.62 1.91,-0.55 0.41,0.64 -0.1,0.18 0,0 0.16,0.32 0.7,0.29 0.34,0.87 0,0 0.23,0.17 0.07,0.48 -0.39,0.33 -0.14,0.96 -1.02,0.99 0,0 -0.12,0.27 0.29,0.09 0,0 0.55,0.64 -0.32,0.92 -0.58,0.5 -0.21,0.65 -0.36,0.06 -0.19,0.84 0.61,0.38 0.54,-0.21 0.2,0.25 1.04,-0.16 0.37,1.01 0.68,0.81 0,0 -0.79,0.58 -0.19,1.22 -1.8,0.64 -2.74,-0.61 -1.25,-0.83 -0.72,-1.46 -0.44,-0.27 -0.1,0.17 -0.9,-0.04 -0.42,0.86 -0.51,0.24 -0.03,0.48 0.11,0.39 0.77,0.8 -0.2,0.91 0.71,0.3 -0.1,0.78 -0.34,0.08 0,0 -0.29,0.37 0,0 -2.24,2.39 -0.19,0.85 -0.8,1.04 -0.48,0.19 -0.35,0.68 0.26,0.81 -1.11,0.27 -1.19,-0.24 -0.34,-0.3 -0.68,0.37 -0.61,-0.09 -0.6,0.25 -0.31,0.63 -0.25,-0.11 0.01,0.21 -0.32,-0.05 -0.15,0.28 -1.01,0.03 -0.51,0.29 -0.22,0.49 -0.25,0.05 0.16,0.18 -0.36,0.09 -0.28,-0.32 0.38,-0.36 -0.49,0.01 -0.26,0.25 -1.05,-0.43 -0.34,0.54 0.21,0.4 -0.41,0.13 -0.02,0.3 -1.32,0.26 -0.02,0.19 0.5,0.29 0.01,0.46 -0.78,0.49 -0.68,2.15 -1.06,-0.62 -0.82,1.1 0.04,0.25 -0.4,0.07 -0.13,0.56 -0.38,0.02 -0.3,-0.29 -1.01,0.02 -1.14,0.88 -0.61,-0.85 -0.29,0.25 -0.55,-0.06 -0.69,-1.05 -1.02,-0.88 -1.19,-0.31 -0.2,0.38 -1.18,-0.01 -0.68,0.4 0.05,1.8 -0.5,0.09 -0.57,1.02 -0.55,0.06 -1.21,-0.69 -0.3,0.52 -0.24,-0.16 -0.52,0.33 -0.15,0.56 -1.79,-0.68 -1.36,-0.03 -0.62,-0.33 0.29,-0.3 0.06,-0.96 -0.29,-0.44 0.04,-1.14 -0.56,-0.6 0.25,-1.69 0.82,-0.52 1.04,0.08 -0.05,-0.58 0.51,-0.18 0.6,-1.38 -0.35,-0.66 -0.56,0 -0.36,-1.68 -1,-0.63 0.53,-0.63 -0.45,-0.44 0.27,-0.64 -0.38,-0.62 -0.77,-0.54 -0.57,0.47 -0.87,0.14 -0.43,0.4 -0.88,-0.1 -0.38,0.73 -0.64,0.26 -0.41,-0.29 -0.42,0.24 -0.5,-0.1 -0.7,-0.52 -0.41,0.09 -1.04,-0.49 -0.28,0.16 -0.47,-0.2 -1.12,0.14 0,0 -0.16,-0.29 0.26,-1.04 0.41,-0.68 -0.35,-0.57 0.73,-0.49 0.28,-1.32 0.28,-0.19 -0.02,-0.5 -0.28,-0.25 -0.49,-1.32 0.42,-0.71 0.45,-0.01 0.54,-0.45 0,-0.5 0.81,-0.36 -0.58,-1.93 0.06,-0.97 0.37,-0.46 -0.76,-0.38 0.42,-0.86 -0.41,-0.72 -0.43,-0.18 -0.6,0.12 -0.11,-0.32 1.03,-1.1 -0.8,-1.46 -0.61,-0.46 -0.47,-0.1 0.22,-0.53 0.73,-0.5 -0.07,-0.46 1.01,-0.75 1.03,-0.61 0.41,-0.7 1.04,-0.63 -0.01,-0.35 -0.25,-1.16 -0.59,0.03 -0.95,-0.52 -0.82,-0.34 -0.49,-0.77 -1.24,-0.36 -0.33,-0.74 -0.48,-0.05 -0.36,-0.39 -0.24,-0.41 0.19,-0.69 -0.81,-0.72 0.49,-0.13 0.1,-0.24 -0.38,-0.27 -0.16,-0.54 0.37,-0.76 1.36,-0.02 0.76,-0.47 0.15,-0.48 1.13,-0.54 2.28,1.13 0.22,-0.06 0.3,-1.17 1.07,0.3 0.36,-0.28 -0.34,-0.82 -0.22,-0.05 -0.67,-1.11 1.29,-0.98 -0.22,-0.72 -0.56,-0.71 0.76,-0.12 0.23,-0.78 -0.52,-0.26 -0.73,-1.55 -0.67,-0.6 0.25,-0.87 0.42,0.38 0.9,-0.44 0.01,-0.75 0.2,-0.44 -0.45,-2.83 0.35,-0.44 0.11,-0.83 0.49,-0.12 0.07,-0.34 -0.47,-0.71 -0.45,0.12 -0.36,-0.23 -0.08,-0.95 0.02,-0.8 -0.67,-0.83 0.77,-0.61 z"
                            id="tinh-kon-tum" data-id="tinh-kon-tum" data-link="{PROVINCE_LIST_601.link}"
                            data-tinh="{PROVINCE_LIST_601.title}" data-province="{PROVINCE_LIST_601.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 346.88847,438.25323 0.31,0.04 0.51,-0.42 0.19,0.15 -0.28,0.38 0.29,0.67 0,1.1 0.98,1.36 0.47,0.27 0.5,-0.38 0.34,0.16 -0.45,0.39 0,0.28 0.19,0.33 0.74,0.15 0.09,0.24 -0.75,1.32 1.09,1.78 1.02,1.03 0.54,0.2 0.32,-0.25 0.5,0.03 0.28,0.26 0,0.88 -0.6,0.85 -0.77,-0.09 0.41,0.48 -0.3,0.21 -0.13,-0.28 -0.36,-0.03 -0.63,0.58 -0.06,1.6 0.19,0.87 -0.32,-0.04 -0.06,0.53 0.2,0.06 -0.29,0.73 -0.02,0.22 0.22,0.59 -0.14,0.61 0.42,0.16 0.28,0.25 -0.04,0.62 0.63,2.73 2.02,5.75 2.65,5.56 1.94,3.39 1.81,2.61 0.04,0.42 0.25,1.07 0.02,0.52 -0.33,0.16 -0.6,0.78 0.5,1.69 -0.21,0.09 0.13,0.86 0.42,1.2 0,0 -1.11,-0.73 -0.29,-0.63 -0.32,-0.04 -0.31,-1.13 0.15,-0.3 -0.56,-0.94 -0.39,0.12 -0.52,-1.04 -0.45,-0.26 -0.41,0.1 -0.34,-0.28 -0.85,0.16 -1.26,-1.09 -0.33,0.19 -0.39,-0.38 -0.52,-0.12 -0.08,-0.31 -1.22,0.85 -0.65,0.06 -0.15,0.21 -1.07,-0.52 -0.49,-0.02 -0.3,0.04 -0.08,0.44 -0.27,0.03 -0.4,-0.61 -0.62,0.93 -0.72,0.35 -1.63,0.01 -0.67,-0.41 -0.51,0.63 0.37,2.02 -0.26,0.51 0.11,1.02 -0.28,0.31 -0.53,0.11 -0.36,-0.17 -0.23,0.35 -1.4,-0.84 -0.22,-0.23 0.11,-0.26 -0.25,0 -0.27,0.93 -0.5,0.31 -0.36,-0.35 -0.42,0.9 -1.69,0.8 0.18,0.41 -0.1,0.54 -0.55,-0.19 -0.17,0.36 0,0 -0.51,0.07 -0.24,0.56 -0.34,-0.09 -0.56,-0.7 0.02,-0.82 -1.19,-0.53 0.03,-0.18 -0.73,-0.54 -0.61,0.03 -0.07,-0.21 0,0 -0.68,-0.81 -0.37,-1.01 -1.04,0.16 -0.2,-0.24 -0.54,0.21 -0.61,-0.38 0.19,-0.83 0.37,-0.07 0.21,-0.65 0.58,-0.5 0.32,-0.93 -0.55,-0.63 0,0 -0.26,-0.07 0.09,-0.29 0,0 1.02,-0.99 0.14,-0.96 0.4,-0.33 -0.07,-0.48 -0.24,-0.16 0,0 -0.34,-0.87 -0.6,-0.21 -0.25,-0.41 0,0 0.1,-0.18 -0.41,-0.64 -1.91,0.55 -0.27,-0.63 -0.86,-0.19 -0.43,-0.64 0,0 -1.19,-0.16 0,0 -0.52,-0.32 0,0 -0.9,-1 -0.24,-0.61 0,0 -0.32,-1.2 -0.19,-0.16 -0.55,0.13 -0.13,-0.95 -0.56,-0.56 -0.9,-0.27 -0.12,-0.64 0.16,-0.18 -0.18,0.04 -0.22,-0.37 0.11,-0.2 -0.21,-0.42 -1.5,-1.01 0.01,-0.47 0,0 -0.28,-0.15 0,0 -0.29,-0.35 -0.21,0.04 -0.29,-0.83 -0.47,-0.42 -0.21,-1.09 -0.57,-0.26 0,0 0.22,-0.52 -0.3,-0.34 0.24,-0.93 -0.34,-0.73 1.12,-0.65 0.05,-1.38 0.91,-0.81 -0.04,-0.29 0.22,-0.17 -0.14,-0.16 0.27,-0.07 0.1,-0.48 0.6,-0.78 0.58,-0.32 -0.31,-0.43 0.14,-0.55 -0.44,-0.12 -0.28,-0.38 0.79,-0.4 0.13,-0.9 0,0 -0.56,-0.14 0,0 -0.48,0.09 0.18,-0.32 -0.44,-0.56 1.12,-0.79 0.17,-0.54 -0.3,-0.83 0.98,-0.92 2.35,0.35 0.85,-0.48 0.63,0.29 0.55,-0.14 0.01,-0.46 0.69,0.14 0.72,-1.02 0.41,0.72 2.38,0.25 1.03,-1 1.1,0.33 0.97,-0.8 0.79,0.06 1.12,-0.57 0.31,0.15 0.74,-0.19 0.06,-0.37 1.42,-0.28 0.63,-0.56 0.89,0.13 0.16,-0.22 0.51,0.29 0.27,-0.22 2.13,-0.22 0.48,-0.25 0.6,-1.13 0,0 1.89,0.78 0.13,-0.31 0.48,0.06 0.75,-0.34 0.38,-0.7 -0.3,-0.04 -0.35,-0.47 0.07,-0.37 0.57,0.45 z"
                            id="tinh-quang-ngai" data-id="tinh-quang-ngai" data-link="{PROVINCE_LIST_505.link}"
                            data-tinh="{PROVINCE_LIST_505.title}" data-province="{PROVINCE_LIST_505.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 279.07847,489.31323 1.12,-0.14 0.47,0.2 0.28,-0.16 1.04,0.49 0.41,-0.09 0.7,0.52 0.5,0.1 0.42,-0.24 0.41,0.29 0.64,-0.26 0.38,-0.73 0.88,0.1 0.43,-0.4 0.87,-0.14 0.57,-0.47 0.77,0.54 0.38,0.62 -0.27,0.64 0.45,0.44 -0.53,0.63 1,0.63 0.36,1.68 0.56,0 0.35,0.66 -0.6,1.38 -0.51,0.18 0.05,0.58 -1.04,-0.08 -0.82,0.52 -0.25,1.69 0.56,0.6 -0.04,1.14 0.29,0.44 -0.06,0.96 -0.29,0.3 0.62,0.33 1.36,0.03 1.79,0.68 0.15,-0.56 0.52,-0.33 0.24,0.16 0.3,-0.52 1.21,0.69 0.55,-0.06 0.57,-1.02 0.5,-0.09 -0.05,-1.8 0.68,-0.4 1.18,0.01 0.2,-0.38 1.19,0.31 1.02,0.88 0.69,1.05 0.55,0.06 0.29,-0.25 0.61,0.85 1.14,-0.88 1.01,-0.02 0.3,0.29 0.38,-0.02 0.13,-0.56 0.4,-0.07 -0.04,-0.25 0.82,-1.1 1.06,0.62 0.68,-2.15 0.78,-0.49 -0.01,-0.46 -0.5,-0.29 0.02,-0.19 1.32,-0.26 0.02,-0.3 0.41,-0.13 -0.21,-0.4 0.34,-0.54 1.05,0.43 0.26,-0.25 0.49,-0.01 -0.38,0.36 0.28,0.32 0.36,-0.09 -0.16,-0.18 0.25,-0.05 0.22,-0.49 0.51,-0.29 1.01,-0.03 0.15,-0.28 0.32,0.05 -0.01,-0.21 0.25,0.11 0.31,-0.63 0.6,-0.25 0.61,0.09 0.68,-0.37 0.34,0.3 1.19,0.24 1.11,-0.27 -0.26,-0.81 0.35,-0.68 0.48,-0.19 0.8,-1.04 0.19,-0.85 2.24,-2.39 0,0 0.29,-0.37 0,0 0.34,-0.08 0.1,-0.78 -0.71,-0.3 0.2,-0.91 -0.77,-0.8 -0.11,-0.39 0.03,-0.48 0.51,-0.24 0.42,-0.86 0.9,0.04 0.1,-0.17 0.44,0.27 0.72,1.46 1.25,0.83 2.74,0.61 1.8,-0.64 0.19,-1.22 0.79,-0.58 0,0 0.07,0.21 0.62,-0.03 0.72,0.53 -0.03,0.18 1.2,0.53 -0.03,0.82 0.56,0.7 0.35,0.09 0.24,-0.56 0.52,-0.07 0,0 0.1,0.74 0.28,0.31 -0.3,0.39 0.38,0.61 -0.28,0.48 0.42,0.56 -0.11,0.35 0.32,0.23 -0.17,1.24 -0.45,0.3 -0.57,-0.18 -0.22,-0.35 -0.35,0.12 -0.44,-0.24 0.61,1.06 -0.18,0.23 0.08,0.55 0.35,0.43 -0.16,0.23 1.01,2.14 -0.54,0.88 0.51,0.38 0.36,1.12 0.36,0.34 -0.24,0.49 0.21,0.78 0.71,0.89 -0.25,0.35 -0.01,0.78 0.66,0.81 -0.16,0.58 0.57,0.54 -0.11,0.84 0.32,0.24 0.06,0.51 -0.37,0.46 0.15,0.36 -0.56,0.69 0.64,0.23 -0.09,0.62 0.85,1.51 -0.07,0.28 0.36,0.06 0.09,0.43 0.65,0.56 0.49,1.64 0.63,0.68 1.04,0.38 0.15,0.45 0.01,0.52 -0.28,0.5 0.62,1.29 -0.64,0.67 0.28,0.61 -0.25,0.65 0.17,0.48 -0.31,-0.07 -0.16,0.27 -0.14,1.88 1.06,0.93 0.38,1.34 1.32,1.16 -0.22,0.16 0.21,0.4 -0.14,0 0.03,0.76 0.33,0.7 1.08,0.35 0.32,0.48 -0.18,0.87 0.2,0.76 0.27,0.07 0.1,1.14 -0.46,0.85 0.31,0.82 -0.98,0.6 0.4,1.13 0.03,1.24 1.02,2.17 -0.14,0.46 0.62,0.93 -0.03,0.8 0,0 -0.36,0.25 -0.84,-0.05 -0.67,-0.65 -1.13,-0.06 -0.35,0.03 -0.25,0.47 -0.31,-0.08 -0.36,0.46 -1,-0.26 -0.06,0.59 -0.35,-0.06 -0.24,0.39 -0.45,1.57 -0.7,0.08 -0.04,0.87 -0.54,0.82 0.62,0.64 -0.07,0.53 0.7,0.04 0.41,0.29 0.53,0.02 0.35,-0.32 0.26,0.24 0.2,-0.3 1.01,-0.07 0.75,-0.35 0.05,0.43 -0.31,0.58 0.34,0.07 0.52,0.86 0.08,1.82 0,0 0,0.44 0,0 -0.18,0.15 0.24,1.79 1.6,1.75 0.36,0.91 0,0 0.4,0.7 0,0 0.31,0.39 0.42,-0.09 0.26,0.46 -0.26,0.95 0.11,0.82 -0.36,1.08 -0.72,0.48 0.11,0.37 -0.19,0.43 0.37,0.43 -0.33,0.21 0.03,0.54 -0.2,-0.13 -0.35,0.61 0.18,0.32 -0.26,0.16 -0.53,-0.13 -0.19,0.56 -0.52,-0.28 -0.47,0.7 -0.06,-0.32 -0.32,-0.03 -0.09,0.58 -0.24,-0.32 -0.25,0.32 -0.48,-0.3 0.02,0.5 -0.8,0.13 -0.13,0.51 0.42,0.09 0.09,0.22 -0.47,0.2 -0.51,1.17 0.32,3.25 -2.17,0.81 -1.09,0.04 -0.62,1 0,0 -0.28,0.1 -0.48,-0.45 -0.57,0.27 -0.58,-0.6 -1.62,0.17 -1.16,-1.18 -0.74,0.07 -0.49,0.45 -1.58,-0.66 -1.28,-0.12 -0.1,-0.61 0.53,-0.92 -0.11,-0.43 -1,-0.69 -0.17,-0.52 -0.42,-0.22 0.22,-0.38 -0.29,-0.31 -1.36,-0.46 -0.21,-0.4 0.67,-0.71 -0.28,-0.01 0.07,-0.37 -0.24,-0.2 0.13,-0.54 -1.4,-0.96 -0.7,-0.95 -0.37,-1.25 0.31,-0.28 -0.25,-0.36 0.2,-0.35 -0.55,-0.42 -0.15,-0.53 -1,0.06 0,0 -0.61,-0.53 0,0 -0.24,-0.02 0.05,-1.11 -0.24,-0.37 0.2,-0.39 -0.54,-0.65 -0.33,-0.03 -0.13,-0.74 -0.39,-0.15 0.06,-0.38 -0.51,0.07 -1.12,-0.49 -1.41,-0.17 -0.37,-0.58 -0.91,-0.6 -0.31,0.21 -0.79,0.02 -0.23,-0.24 -0.95,0 -0.36,0.24 -0.21,-0.44 -0.3,0.17 -0.29,-0.28 -1.06,-0.27 -0.18,-0.41 -0.49,-0.16 -0.19,-0.36 -0.52,0.08 -0.31,-0.42 -1.04,-0.11 -0.69,-0.37 -0.27,0.28 -0.87,0.13 -0.48,0.49 -0.08,-0.25 -0.31,0.26 -0.21,-0.53 -0.62,0.11 -0.19,-0.28 -0.42,0.62 -0.5,-0.09 -0.13,0.36 -0.33,0.03 -0.25,0.42 -0.65,0.34 -0.63,-0.52 -0.7,-0.11 -0.19,0.14 0.07,0.33 -0.4,0.14 -0.77,0.05 -0.48,-0.27 -0.37,0.66 -1.14,-0.34 -0.72,-0.5 -1.05,0.23 -0.54,-0.48 -1.29,0.23 -0.65,-0.59 -1.16,0.4 -0.8,-0.04 -0.2,0.29 0.12,0.37 -0.41,-0.02 -1.41,0.9 -0.11,-0.38 -0.56,0.13 -0.17,0.34 -1.02,-0.15 -0.63,-0.43 -0.36,0.23 -0.31,-0.27 -0.57,0.23 -0.19,0.45 -0.22,-0.09 -0.55,0.34 -0.17,0.4 -0.44,-0.02 -0.68,0.41 -0.06,0.81 -0.42,-0.17 -0.09,0.35 -0.44,0.09 -0.05,0.31 -0.67,0.15 -0.47,0.4 0,0 1.18,-2.92 -0.54,-7.44 -0.15,-1.32 -2.34,-5.12 -0.12,-2.2 -0.95,-1.45 -0.94,-2.77 -2.3,-1.64 -1,-0.25 -0.94,-0.8 0.54,-1.15 -0.32,-0.64 0.56,-0.61 0.37,-1.02 -1.01,-0.68 0.85,-1.01 0.14,-1.44 -0.21,-0.26 -0.48,0.01 -0.41,-0.45 0.12,-2.65 -0.51,-0.44 -0.09,-0.46 -2.93,0.43 -1.04,-1.22 -0.43,-0.21 -0.09,-0.65 0.45,-0.36 -0.02,-1.59 0.8,-0.1 0.15,-0.63 -2.19,-0.97 -0.44,-0.49 0.42,-1.47 0.66,-1.23 0.29,-0.78 0.55,-0.56 0.38,0.09 0.62,-1.58 0.44,-0.7 0.59,-0.65 -0.4,-0.73 0.49,-0.82 -0.15,-1.8 -0.83,-1 0,-0.49 0.91,-1.08 0.21,-0.81 1.45,-1.09 0.03,-0.73 0.47,-1.21 0.43,0.3 0.13,0.37 1.43,0 0.08,-1.22 0.43,-0.39 z"
                            id="tinh-gia-lai" data-id="tinh-gia-lai" data-link="{PROVINCE_LIST_603.link}"
                            data-tinh="{PROVINCE_LIST_603.title}" data-province="{PROVINCE_LIST_603.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 337.46847,484.70323 0.17,-0.36 0.55,0.19 0.1,-0.54 -0.18,-0.41 1.69,-0.79 0.42,-0.9 0.36,0.35 0.5,-0.31 0.27,-0.92 0.25,0 -0.11,0.26 0.22,0.23 1.4,0.84 0.23,-0.35 0.36,0.17 0.53,-0.11 0.28,-0.31 -0.11,-1.02 0.26,-0.51 -0.36,-2.02 0.51,-0.63 0.67,0.41 1.63,-0.01 0.72,-0.35 0.62,-0.93 0.4,0.61 0.27,-0.03 0.08,-0.44 0.3,-0.04 0.49,0.02 1.07,0.52 0.16,-0.21 0.65,-0.06 1.22,-0.85 0.08,0.31 0.52,0.12 0.39,0.38 0.33,-0.19 1.26,1.09 0.85,-0.15 0.34,0.28 0.41,-0.09 0.45,0.26 0.52,1.04 0.39,-0.12 0.56,0.95 -0.15,0.3 0.31,1.13 0.32,0.04 0.29,0.63 1.11,0.73 0,0 -0.52,-0.01 -0.22,0.51 1.36,4.24 -0.13,0.52 0.49,0.06 0.09,-0.23 1.2,2.02 -0.47,1.16 0.68,1.51 -0.27,0.39 -0.06,0.73 1.81,3.18 1.12,1.51 0.42,0.26 -0.4,0.25 0.03,0.46 0.75,1 0.23,0.78 -0.21,0.43 -0.38,-0.15 -0.3,0.36 0.24,1.22 -0.17,0.19 0.39,0.93 0.46,0.46 0.38,1.01 -0.07,0.44 0.16,0.19 0.49,-0.18 0.56,0.27 -0.38,0.6 -0.88,-0.12 -1.58,-2.53 -0.82,0.3 0.23,0.27 0.03,0.55 0.33,0.42 0.06,0.92 0.23,0.3 1.34,0.2 0.26,0.62 0.39,1.13 1.12,2.36 1,1.35 -0.37,0.69 0.57,0.87 -0.16,0.25 -0.26,-0.03 -0.04,0.35 0.28,0.66 0.29,0.15 -0.85,0.43 0.02,0.76 1.35,2.3 0.83,0.61 0.24,-0.07 0.42,0.43 -0.28,0.22 -0.03,1.27 0.35,0.44 -0.15,0.44 0.18,0.33 -0.72,1.3 0.44,1.31 -0.24,1.29 -0.14,0.09 -0.33,-0.46 -1.11,0.31 -0.48,-0.24 0.55,-0.55 -0.36,-0.22 0.01,-0.39 0.23,-0.3 0.34,-0.19 0.77,-0.05 0.06,-1.05 -0.19,-0.13 -0.41,0.87 -0.51,-0.61 -0.36,-0.03 -0.04,-0.22 0.34,-0.32 -0.19,-1.59 -0.3,-0.86 -0.61,-0.86 -0.26,-0.07 -0.23,1.26 0.03,0.3 -0.01,0.3 -0.36,0.15 0.23,0.49 -0.1,0.59 0.1,0.53 0.22,0.87 -0.3,0.74 0.52,0.48 0.3,0.08 0.64,0.3 -1.07,0.15 -0.86,1.08 0.24,0.7 -0.1,0.97 0.26,0.18 0.3,1.26 0.24,0.08 -0.03,0.93 0,0 -0.35,-0.47 -0.96,-0.12 -0.44,-0.77 -0.5,0.12 -0.41,0.53 -1.33,0.27 -0.28,1.21 0.08,1.12 -0.41,0.24 -0.64,-0.61 -0.48,-0.18 -0.16,0.17 -0.07,0.33 0.43,0.6 -0.6,0.55 0.12,0.54 -0.23,0.57 0.25,0.35 -0.54,-0.11 -0.7,0.82 -0.56,0.26 -1.57,-0.06 -1.26,2.01 -1.57,0.46 -0.26,0.34 -1.8,0.07 -0.52,-0.59 -0.84,-0.27 -0.61,-0.51 -0.74,-0.06 -0.18,0.2 -0.44,-0.16 -1.4,0.32 0.1,-0.6 -0.69,-0.38 -0.32,-0.79 0,0 0.03,-0.8 -0.62,-0.93 0.14,-0.46 -1.03,-2.17 -0.02,-1.24 -0.4,-1.13 0.98,-0.6 -0.31,-0.81 0.46,-0.85 -0.09,-1.13 -0.28,-0.07 -0.2,-0.76 0.18,-0.87 -0.32,-0.48 -1.07,-0.35 -0.34,-0.7 -0.03,-0.76 0.15,0 -0.21,-0.4 0.22,-0.16 -1.31,-1.16 -0.39,-1.34 -1.06,-0.94 0.14,-1.87 0.16,-0.27 0.31,0.07 -0.17,-0.48 0.25,-0.65 -0.28,-0.61 0.64,-0.67 -0.61,-1.29 0.28,-0.5 -0.01,-0.52 -0.15,-0.45 -1.04,-0.38 -0.62,-0.68 -0.49,-1.63 -0.65,-0.57 -0.1,-0.42 -0.36,-0.06 0.07,-0.29 -0.84,-1.51 0.09,-0.61 -0.65,-0.23 0.57,-0.69 -0.15,-0.36 0.37,-0.46 -0.06,-0.51 -0.32,-0.23 0.12,-0.84 -0.57,-0.54 0.16,-0.58 -0.65,-0.81 0,-0.77 0.25,-0.35 -0.7,-0.89 -0.22,-0.78 0.24,-0.48 -0.36,-0.34 -0.36,-1.12 -0.51,-0.38 0.55,-0.88 -1.02,-2.14 0.16,-0.24 -0.35,-0.43 -0.08,-0.54 0.19,-0.24 -0.62,-1.06 0.45,0.25 0.34,-0.13 0.22,0.36 0.57,0.18 0.45,-0.3 0.18,-1.23 -0.32,-0.23 0.11,-0.34 -0.42,-0.56 0.28,-0.49 -0.38,-0.61 0.3,-0.39 -0.28,-0.31 -0.2,-0.87 z"
                            id="tinh-binh-dinh" data-id="tinh-binh-dinh" data-link="{PROVINCE_LIST_507.link}"
                            data-tinh="{PROVINCE_LIST_507.title}" data-province="{PROVINCE_LIST_507.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 349.02847,536.70323 0.32,0.79 0.69,0.38 -0.09,0.6 1.4,-0.32 0.44,0.16 0.18,-0.2 0.74,0.06 0.61,0.51 0.84,0.27 0.52,0.59 1.8,-0.07 0.26,-0.34 1.57,-0.46 1.26,-2.01 1.57,0.06 0.56,-0.25 0.71,-0.82 0.54,0.12 -0.25,-0.35 0.23,-0.57 -0.12,-0.54 0.6,-0.55 -0.43,-0.6 0.07,-0.33 0.16,-0.17 0.48,0.18 0.64,0.61 0.41,-0.24 -0.08,-1.12 0.28,-1.21 1.34,-0.27 0.41,-0.52 0.5,-0.11 0.44,0.77 0.96,0.12 0.35,0.47 0,0 0.04,0.82 -0.41,0.63 0.83,1.72 1.31,1.53 0.65,0.45 0.36,0.05 0.16,-0.2 0.51,0.44 -0.37,-0.08 -0.11,0.46 -0.51,0.23 -0.18,0.51 0.17,0.71 0.4,0.26 -0.28,0.14 -0.66,-0.35 0.05,-0.65 0.12,-0.6 -1.36,-0.95 -0.19,-0.79 -1.49,-2.87 -0.35,-0.35 -0.35,1.3 0.85,0.47 1.27,2.3 0.3,0.3 -0.1,0.61 0.51,0.39 -0.38,1.2 2.05,0.87 0.43,-0.26 -0.19,-0.71 0.53,0.26 0.05,0.86 -0.33,-0.29 -0.44,0.59 0.26,0.96 0.81,0.96 0.56,-0.08 0.19,-0.49 0.39,0.32 0.06,0.35 -0.2,0.1 -0.19,-0.21 -0.34,0.3 0.16,0.85 -0.48,0.3 0.05,1.31 -0.46,-0.1 -0.25,0.58 -0.23,-0.16 -0.08,-0.5 -1.02,0.09 -0.19,0.19 -0.51,-0.86 0.33,-0.38 0.6,0 0.4,-0.7 0.34,0.07 0.11,-0.5 -0.3,-0.34 -0.4,0.04 -0.65,-0.9 -0.77,-0.2 -0.48,-0.22 -0.71,0.35 0.24,0.47 -0.74,1.25 0.44,0.85 0.55,0.19 -0.32,0.26 -0.29,-0.11 -0.39,0.44 0.61,0.23 -0.17,0.84 -0.35,0.04 -0.35,-0.35 -0.07,0.3 0.53,0.45 0.61,-0.18 0.48,0.21 0.25,1.15 0.48,0.46 0.33,0.09 0.29,-0.17 -0.02,-0.25 0.31,-0.07 0.98,0.11 0.12,0.41 -0.32,0.24 0.46,0.87 -0.29,0.11 -0.49,-0.23 -0.28,0.65 0.52,1.82 -0.27,0.12 -0.4,-0.88 -0.37,-0.18 -0.13,-0.38 -0.38,0.37 0.42,0.8 -0.59,0.45 0.25,0.55 0.17,0.31 -0.01,1 0.39,0.22 0.2,0.32 0.36,0.38 0.24,-0.86 -0.47,-1.1 0.05,-0.64 0.53,0.06 0.34,-0.44 0.74,0.69 0.43,1.12 -0.74,0.27 -0.06,0.6 0.27,0.52 -0.75,0.69 0.11,1.07 0.32,0.5 -0.41,0.91 0.13,1.69 2,2.95 2.22,4.03 1.21,1.47 1.65,1.71 -0.1,0.5 0.65,0.97 0.72,0.3 0.13,0.84 -0.21,0.44 -0.15,0.59 0.03,1 -0.72,0.14 -0.52,0.5 -0.43,-0.06 0.94,-1.21 -0.04,-0.51 -0.41,-0.04 -0.55,0.22 -0.91,0.56 0.12,0.42 -0.56,0.39 -0.14,0.42 0,0 -1.19,-1.33 -0.22,0.47 -0.66,0.16 -0.41,0.5 -0.58,-0.06 -0.54,0.66 -0.24,-0.39 -0.38,0.23 -0.39,-0.11 -0.75,0.48 0,0 0,0 0,0 -0.85,0 -0.73,-0.3 -1.42,1.35 -1.03,-0.92 -2.15,0.17 -1.37,2.38 -0.82,-0.75 -0.42,-0.12 -0.46,1.32 -0.9,0.71 0.09,0.48 -0.3,0.39 -0.34,0.02 -0.64,-0.52 -0.39,0.45 -1.06,-0.05 -0.08,0.47 -0.68,0.04 -0.64,0.37 0.35,0.34 -1.38,0.82 0,0 -0.05,-0.77 -0.33,-0.32 -0.16,-0.84 0.36,-0.66 -0.27,-0.82 0.08,-0.55 -0.76,-0.37 0.22,-0.56 -0.22,-0.17 -0.1,-0.74 -0.35,-0.08 -0.4,0.32 -0.46,0.04 -0.17,0.28 -0.48,-0.27 -1.33,0.05 -0.7,-0.29 -1.4,-0.06 -0.34,-0.52 -0.54,-0.31 -0.18,-1.23 -1.07,-0.67 -0.41,0.02 -0.29,-0.41 -0.55,0.06 -0.47,0.34 -0.21,-0.16 -0.4,-1.19 -0.48,-0.33 -0.17,-0.91 -0.43,-0.36 -0.6,-1.17 -1.61,-0.48 -0.26,0.46 -0.6,0.13 -0.14,-0.26 0.25,-0.86 -0.65,-0.37 -0.12,-0.45 -0.57,-0.08 -0.3,-0.42 -0.03,-0.23 0.44,-0.22 0,0 0.62,-1 1.08,-0.04 2.17,-0.81 -0.31,-3.25 0.5,-1.17 0.47,-0.2 -0.09,-0.22 -0.42,-0.09 0.13,-0.5 0.8,-0.13 -0.02,-0.49 0.48,0.29 0.25,-0.32 0.24,0.32 0.09,-0.59 0.33,0.03 0.06,0.32 0.47,-0.7 0.52,0.29 0.19,-0.56 0.53,0.12 0.26,-0.15 -0.18,-0.32 0.35,-0.62 0.2,0.13 -0.02,-0.54 0.33,-0.2 -0.37,-0.43 0.19,-0.43 -0.11,-0.37 0.72,-0.48 0.36,-1.08 -0.11,-0.83 0.27,-0.94 -0.25,-0.47 -0.42,0.1 -0.31,-0.39 0,0 -0.4,-0.7 0,0 -0.36,-0.91 -1.6,-1.75 -0.24,-1.78 0.18,-0.15 0,0 0,-0.44 0,0 -0.08,-1.82 -0.52,-0.86 -0.33,-0.07 0.31,-0.58 -0.06,-0.43 -0.75,0.35 -1.01,0.07 -0.19,0.31 -0.26,-0.25 -0.35,0.32 -0.53,-0.02 -0.41,-0.29 -0.7,-0.04 0.07,-0.54 -0.62,-0.63 0.54,-0.82 0.04,-0.88 0.7,-0.07 0.45,-1.58 0.24,-0.39 0.35,0.06 0.06,-0.58 1,0.26 0.35,-0.46 0.31,0.07 0.25,-0.47 0.36,-0.03 1.13,0.06 0.67,0.65 0.84,0.05 0.17,-0.41 z"
                            id="tinh-phu-yen" data-id="tinh-phu-yen" data-link="{PROVINCE_LIST_509.link}"
                            data-tinh="{PROVINCE_LIST_509.title}" data-province="{PROVINCE_LIST_509.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 284.39847,550.67323 0.47,-0.4 0.67,-0.15 0.05,-0.31 0.44,-0.09 0.09,-0.35 0.42,0.17 0.06,-0.81 0.68,-0.41 0.44,0.02 0.17,-0.4 0.55,-0.34 0.22,0.09 0.19,-0.45 0.57,-0.23 0.31,0.27 0.36,-0.23 0.63,0.43 1.02,0.15 0.17,-0.34 0.56,-0.13 0.11,0.38 1.41,-0.9 0.41,0.02 -0.12,-0.37 0.2,-0.29 0.8,0.04 1.16,-0.4 0.65,0.59 1.29,-0.23 0.54,0.48 1.05,-0.23 0.72,0.5 1.14,0.34 0.37,-0.66 0.48,0.27 0.77,-0.05 0.4,-0.14 -0.07,-0.33 0.19,-0.14 0.7,0.11 0.63,0.52 0.65,-0.34 0.25,-0.42 0.33,-0.03 0.13,-0.36 0.5,0.09 0.42,-0.62 0.19,0.28 0.62,-0.11 0.21,0.53 0.31,-0.26 0.08,0.25 0.48,-0.49 0.87,-0.13 0.27,-0.28 0.69,0.37 1.04,0.11 0.31,0.42 0.52,-0.08 0.19,0.36 0.49,0.16 0.18,0.41 1.06,0.27 0.29,0.28 0.3,-0.17 0.21,0.44 0.36,-0.24 0.95,0 0.23,0.24 0.79,-0.02 0.31,-0.21 0.91,0.6 0.37,0.58 1.41,0.17 1.12,0.49 0.51,-0.07 -0.06,0.38 0.39,0.15 0.13,0.74 0.33,0.03 0.54,0.65 -0.2,0.39 0.24,0.37 -0.05,1.11 0.24,0.02 0,0 0.61,0.53 0,0 1,-0.06 0.15,0.53 0.55,0.42 -0.2,0.35 0.25,0.36 -0.31,0.28 0.37,1.25 0.7,0.95 1.4,0.96 -0.13,0.54 0.24,0.2 -0.07,0.37 0.28,0.01 -0.67,0.71 0.21,0.4 1.36,0.46 0.29,0.31 -0.22,0.38 0.42,0.22 0.17,0.52 1,0.69 0.11,0.43 -0.53,0.92 0.1,0.61 1.28,0.12 1.58,0.66 0.49,-0.45 0.74,-0.07 1.16,1.18 1.62,-0.17 0.58,0.6 0.57,-0.27 0.48,0.45 0.28,-0.1 0,0 -0.43,0.22 0.03,0.23 0.3,0.42 0.57,0.08 0.12,0.45 0.65,0.37 -0.25,0.86 0.14,0.26 0.6,-0.12 0.26,-0.46 1.61,0.47 0.6,1.17 0.43,0.37 0.17,0.91 0.48,0.32 0.4,1.2 0.21,0.16 0.47,-0.34 0.56,-0.06 0.28,0.42 0.41,-0.02 1.07,0.67 0.18,1.23 0.54,0.31 0.34,0.52 1.41,0.06 0.69,0.29 1.34,-0.05 0.48,0.27 0.17,-0.29 0.46,-0.03 0.41,-0.32 0.34,0.08 0.1,0.74 0.22,0.17 -0.22,0.56 0.76,0.36 -0.07,0.55 0.26,0.82 -0.36,0.66 0.17,0.84 0.33,0.32 0.05,0.77 0,0 -0.36,0.25 0.17,0.62 -0.55,0.46 0.19,0.45 -0.08,1.32 -0.39,0.62 0.43,0.59 -0.84,1.34 -0.81,0 -0.74,-0.44 -2.27,1.51 0.19,0.8 -0.15,0.78 -0.61,0.47 -0.4,0.82 0.21,0.56 -0.21,0.46 -0.72,0.64 0.97,0.8 -0.1,0.49 -0.32,0.21 -0.01,0.72 -1.61,-0.55 -1.33,-1.09 -0.72,0.16 -0.28,-0.88 -0.82,-0.43 -0.82,0.39 -0.6,0.03 -0.24,0.92 -0.63,0.13 -0.98,-0.34 -0.78,0.51 -0.12,0.7 -1.51,1.89 0.57,0.51 -0.32,0.73 0.21,0.46 0.42,0.2 -0.1,0.49 0.29,0.36 -0.63,1.3 0.34,1.34 -0.22,0.39 0,0 -0.8,0.88 -2.14,0.48 -0.31,0.45 -0.58,-0.08 -0.62,0.36 -0.53,-0.3 0.18,-0.33 -0.5,0.13 -0.54,-0.51 -0.24,0.17 -0.65,-0.19 -0.12,0.19 -0.4,-0.28 -0.34,0.39 0.12,0.38 -1.48,0.3 -0.56,-0.41 0.05,-0.36 -0.41,-0.72 -0.34,0.03 -0.11,-0.25 -0.15,0.27 0.16,0.25 -0.46,-0.03 -0.14,0.64 -0.35,-0.47 0.06,0.73 -0.33,0.39 -0.17,-0.01 0.07,-0.37 -0.41,0.52 -0.41,-0.23 0.06,0.75 -0.37,0.32 0.15,0.37 -0.52,-0.47 -0.11,0.4 -0.76,0.43 -1.3,0.14 -0.94,-0.71 -0.72,-0.02 -0.7,0.5 -0.39,-0.08 -0.08,0.6 0.31,0.06 0.05,0.32 -0.48,-0.12 -0.49,0.35 0.05,0.22 -0.56,0.08 -0.8,0.82 -0.08,-0.32 -0.86,-0.3 -0.32,0.52 -0.65,0.22 0.03,0.47 -0.39,0.14 -0.19,0.43 0.02,0.22 0.3,-0.11 0.01,0.22 -0.74,0.72 -0.15,-0.79 -0.33,-0.05 -0.18,-0.3 -0.24,0.55 -0.35,-0.63 -0.39,0.31 -0.01,0.29 -0.48,0.08 -0.16,-0.16 0,0 0,-0.18 0,0 -0.64,0.16 -0.1,-0.47 -0.24,-0.12 -0.3,0.31 0.02,-0.36 -0.35,-0.53 -2.21,1.13 -0.57,-0.48 0,0 -0.06,-0.63 -1.24,-1.29 -0.27,-0.73 0.09,-0.39 -0.6,-0.47 -0.38,-0.81 -0.34,0.08 -0.25,-0.27 -0.23,0.19 -0.55,-1.19 -1.09,-0.13 -0.24,-0.64 -0.58,0.36 -0.31,-0.45 -1.22,0.39 -0.13,-0.69 -0.97,-0.43 -0.45,-0.46 0.04,-0.38 -0.55,-0.39 -0.09,-1.71 -0.24,-0.36 -0.38,0.01 0.03,-0.3 0.27,-0.12 -0.47,-0.14 -0.24,-0.46 0.16,-0.34 0.61,-0.3 -0.55,-0.18 0.39,-0.15 -0.11,-0.47 0.44,0.19 -0.04,-0.42 0.35,0.04 0.45,-0.51 0.38,-0.03 0,0 0.67,0.26 0,0 1,-0.52 0.4,-0.98 0.2,-0.01 -0.09,-0.24 0.37,-0.37 -0.2,-0.51 -0.36,0.04 0.03,-0.39 -0.57,-0.15 -0.28,-0.62 -1.05,-0.71 -0.31,0.03 -0.27,-0.35 -0.51,0.12 -0.4,-0.23 -0.71,0.2 -1.2,-0.74 -0.01,-0.38 0.46,-0.37 -0.14,-0.57 0.93,-0.63 1.13,-0.35 0.11,-0.67 -0.29,-0.21 -0.13,-0.97 -0.43,-0.15 0.01,-1.4 -0.68,-0.01 -0.16,-0.24 0.53,-0.39 -0.52,-0.87 0.44,-0.95 -0.9,-1.03 0.23,-0.67 0.5,-0.44 0.02,-0.48 -2.18,-0.45 -0.43,-0.34 -0.33,-1.17 0.49,-0.89 -0.4,-0.6 -0.43,-0.28 -0.73,0.55 -0.47,0.02 -0.3,0.64 -0.37,-0.01 0.02,0.24 -0.24,-0.05 0.1,0.13 -0.29,0.2 -1.13,0.1 -2,-0.29 -0.54,0.12 -2.5,-0.43 -1.31,-0.47 -0.89,0.19 -0.45,-0.43 -1.3,0.35 -0.39,-0.25 -0.92,0.32 -1.21,-0.09 0,0 -0.11,-0.4 -0.15,-0.67 -0.17,-0.32 -0.61,-0.66 -0.62,-0.22 -0.82,-0.93 0.01,-0.58 -0.69,-0.5 -0.09,-0.78 -0.11,-0.89 -0.14,-0.82 -0.41,-1.01 0.31,-1.09 0.74,-0.57 -0.41,-0.62 -0.29,-0.79 -0.07,-1.71 z"
                            id="tinh-dak-lak" data-id="tinh-dak-lak" data-link="{PROVINCE_LIST_605.link}"
                            data-tinh="{PROVINCE_LIST_605.title}" data-province="{PROVINCE_LIST_605.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 370.91847,607.58323 0.26,0.22 1.04,-0.13 0.54,0.89 0.28,-0.05 0.34,-0.71 0.24,0.37 0.39,-0.01 -0.08,1.04 0.39,0.44 -0.48,0.13 -0.02,0.23 -0.5,0.03 -0.3,-0.2 -0.64,-0.13 -0.48,-0.12 0.33,0.67 -0.59,0.07 -0.56,-0.34 -0.17,-0.46 -1.4,-0.74 -0.19,-0.74 -0.49,-0.15 0.19,-0.34 0.3,0.01 0.66,0.44 0.37,0.59 0.24,-1.18 0.33,0.17 z m 2.8,-21.43 0.45,0.01 0.76,0.54 0.37,0.03 0.47,-0.35 1.08,0.43 0.05,1.03 0.61,0.66 0.03,1.11 0.8,0.76 -0.23,0.37 -0.49,0.08 -0.81,-0.68 0.01,-0.24 -0.56,0 -0.49,-0.63 -1.28,-0.63 -0.01,-0.65 -0.39,-0.34 -0.46,0.05 -0.24,0.28 -0.51,-0.44 -0.31,-0.47 0.05,-0.48 0.77,-0.55 0.33,0.11 z m -17.17,-3.69 1.38,-0.82 -0.35,-0.34 0.64,-0.37 0.68,-0.04 0.08,-0.47 1.06,0.05 0.39,-0.45 0.64,0.52 0.34,-0.02 0.3,-0.39 -0.09,-0.48 0.9,-0.71 0.46,-1.32 0.42,0.12 0.82,0.75 1.38,-2.38 2.15,-0.17 1.03,0.92 1.42,-1.35 0.73,0.3 0.84,0.01 0.76,-0.48 0.4,0.11 0.38,-0.23 0.24,0.4 0.54,-0.66 0.58,0.06 0.41,-0.5 0.67,-0.16 0.22,-0.46 1.19,1.33 0,0 -1.53,0.58 -0.18,0.7 0.13,0.43 0.02,0.89 1.06,2.91 0.84,1.48 0.51,0.63 0.19,0.31 0.49,0.91 0.34,0.43 0.74,-0.18 0.12,0.41 0.53,-0.22 0.37,0.49 -0.14,0.33 -0.12,0.11 -0.9,0.18 -0.15,0.31 0.11,0.55 -0.18,0.33 0.23,0.67 -0.11,0.33 -0.32,0.38 0.15,0.19 0.16,0.51 0.25,0.35 -0.02,0.63 -0.4,0.26 -0.25,-0.7 -0.63,-0.07 -0.19,-0.48 0.14,-0.91 -0.44,0.06 -0.14,-0.29 -0.25,0.08 -0.48,-1.19 0.19,-0.21 0.83,-0.06 0.02,-0.25 0.19,-0.63 -0.34,-1.09 -0.5,-0.7 -0.3,0.26 0.1,0.55 -1.27,-0.2 -0.12,0.33 0.23,0.48 -0.13,0.56 -0.38,0.08 -0.35,-0.4 -0.17,-0.51 -0.71,0.57 -0.47,-0.6 0.79,-0.24 0.57,-1.43 0.25,0.36 0.46,-0.11 0.59,-1.18 -1.23,-2.49 -0.03,-2.04 -0.33,-0.18 -1.05,0.38 -0.11,0.53 0.2,0.3 -0.52,0.65 -0.7,0.28 -0.25,-0.1 -0.49,0.46 -0.25,0.63 -0.98,0.77 -0.23,0.58 -0.26,0.27 -0.19,0.44 -0.64,0.01 -0.75,0.44 -0.93,1.39 -0.06,1.03 -0.29,-0.03 -0.46,0.38 -0.24,0.64 0.37,1.15 -0.09,1.28 -0.23,0.44 0.52,0.72 -0.18,0.35 -0.16,0.34 -0.08,0.35 0.5,0.4 0.55,-0.19 -0.09,-1.64 0.31,-0.36 0.14,-0.15 0.55,-0.71 0.19,0.36 0.41,-0.01 0.07,0.62 -0.42,0.1 -0.33,1.7 0.47,1.6 0.8,0.27 1.06,0.94 0.71,0.15 0.4,0.37 -0.15,0.87 -0.31,0.23 0.03,1.27 0.34,0.53 0.72,0.33 0.7,-0.04 0.42,0.55 0.39,-0.07 0.24,0.65 -0.8,0.56 0.23,0.4 -0.09,0.27 -0.99,-0.82 -0.39,0.43 -0.11,1.48 -0.31,0.35 -0.5,-0.09 -0.36,-0.59 -0.48,0.08 -0.37,-0.26 -0.02,-0.49 0.22,-0.16 -0.95,-1.06 -0.15,-0.55 -0.9,-0.61 -0.94,-1.13 -1.64,-1.09 -0.19,0.6 -0.3,0.49 -0.66,0.35 -0.28,0.3 0.43,0.12 0.06,0.59 0.32,0.39 0.32,0.09 0.11,-0.24 0.99,0.5 0.34,-0.05 0.16,0.54 -0.29,1.5 0.1,0.62 0.19,0.66 0.5,0.6 1.64,1.03 -0.42,0.55 -1.03,-0.05 -0.6,0.66 -0.06,0.45 0.24,0.23 -0.43,0.66 -0.04,1.08 0.3,0.84 0.71,0.57 -0.12,0.45 -0.56,0.18 -0.36,-0.38 -0.78,-0.17 -0.09,0.22 -0.01,0.1 1.44,0.86 -0.02,0.65 -0.36,0.14 -0.11,0.33 1.1,0.57 0.19,0.32 0.02,0.45 -0.48,0.22 -0.14,0.75 -0.85,0.44 -0.01,1.15 0.4,1.6 1.99,3.86 0.62,0.7 0.32,-0.18 0.56,0.2 0.04,1.04 0.35,0.26 -0.04,0.33 -0.11,0.49 -0.52,0.03 -0.34,0.55 0.18,1.1 0.23,0.25 0.1,0.32 0.4,0.25 0.3,0.42 -0.51,1.09 -0.73,-0.35 -0.44,0.72 -0.12,-0.68 -0.76,-0.82 -1.49,0.06 0.12,-1.47 0.4,-1.08 0.12,-0.62 -0.57,-0.6 0.45,-0.27 0.05,-1.11 -0.37,-0.91 -0.28,-2.41 -0.88,-0.89 -0.62,-2.32 0.47,-0.48 -0.5,-0.44 -0.45,-0.04 -0.49,0.73 0.89,1.37 -0.04,0.74 -0.12,0.51 0.1,0.46 0.35,0.4 0.7,0.27 0.49,0.56 0.17,0.53 -0.39,0.96 0.33,0.66 -0.34,0.48 0.13,0.44 -0.74,0.98 -0.09,0.48 -1.66,1.9 -0.24,0 -0.3,-0.46 -0.52,0.12 -0.34,1.09 -0.43,-0.14 -0.44,1.46 0.47,1.49 0.74,0.75 0.75,0.14 0.84,-1.19 0.1,-0.77 0.46,-0.44 0.32,-0.03 0.64,-0.68 0.18,0.26 -0.59,0.74 0.18,0.35 -0.34,0.44 0.39,0.66 -0.23,0.34 0.06,0.34 0.72,0.31 0,0 -3.17,0.37 -1.33,-0.13 -0.77,-0.48 -0.2,-0.52 -0.71,-0.48 -0.87,-1.35 -0.58,-1.98 -0.58,-0.56 -0.87,-0.08 -0.78,-0.69 -0.8,0.46 -0.43,0 -1.28,-0.53 -0.5,-0.67 -0.62,0.06 -0.42,-0.22 -0.58,-0.62 -1.23,0.28 -0.57,-0.67 -0.39,0.07 -0.38,-0.48 -0.74,0.17 -0.28,-0.82 0.65,-0.82 -0.15,-1.16 -1.02,-0.24 -0.65,-0.47 -0.19,-0.44 -1,-0.26 0.43,-0.75 -0.37,-0.63 -0.08,-0.77 -0.27,-0.17 0.17,-0.43 -0.24,-0.77 -0.45,-0.69 0.37,-1.87 -0.82,-0.28 -0.39,0.62 -0.29,-0.26 -0.88,0.11 -0.81,-0.32 0,0 -1.03,-1.54 0.61,-1.42 -0.19,-0.39 -0.7,-0.47 -0.27,-0.56 0.33,-0.55 -0.28,-1.68 0.31,-0.64 -0.33,-0.49 -0.61,-0.12 -0.18,-0.33 -0.27,0.06 0,0 0.23,-0.38 -0.34,-1.35 0.63,-1.29 -0.29,-0.36 0.1,-0.49 -0.43,-0.19 -0.21,-0.46 0.32,-0.74 -0.57,-0.51 1.51,-1.88 0.13,-0.71 0.78,-0.51 0.98,0.34 0.63,-0.13 0.24,-0.92 0.6,-0.03 0.82,-0.39 0.82,0.43 0.28,0.88 0.72,-0.16 1.33,1.1 1.62,0.54 0,-0.71 0.32,-0.22 0.1,-0.48 -0.98,-0.8 0.72,-0.64 0.21,-0.47 -0.21,-0.56 0.4,-0.82 0.61,-0.47 0.14,-0.78 -0.19,-0.8 2.28,-1.51 0.74,0.44 0.81,0 0.85,-1.34 -0.43,-0.59 0.38,-0.62 0.08,-1.32 -0.19,-0.44 0.55,-0.46 -0.18,-0.63 0.17,-0.39 z"
                            id="tinh-khanh-hoa" data-id="tinh-khanh-hoa" data-link="{PROVINCE_LIST_511.link}"
                            data-tinh="{PROVINCE_LIST_511.title}" data-province="{PROVINCE_LIST_511.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 340.26847,603.15323 0.27,-0.07 0.18,0.34 0.61,0.11 0.33,0.5 -0.31,0.64 0.28,1.68 -0.33,0.55 0.27,0.56 0.7,0.47 0.19,0.4 -0.61,1.42 1.03,1.54 0,0 -0.56,0.45 -0.8,-0.05 -0.1,0.25 -0.54,0.19 0.16,0.32 -0.2,0.57 -0.35,-0.03 -0.42,0.44 -0.24,-0.03 1.6,1.55 -0.09,0.36 0.31,0.34 0.17,1.19 -0.29,0.63 -0.6,0.21 -0.49,0.86 -0.24,0.63 0.18,0.63 -0.17,0.83 -1,1.24 -0.09,0.48 0.28,0.26 0.03,0.51 -0.29,0.57 0.22,0.62 -0.53,1.09 -0.18,1.63 0.14,1.1 -0.63,0.61 -0.58,0.1 -0.19,0.81 -0.36,0.34 0.07,0.27 0,0 0.9,0.48 1.63,0.27 0.21,0.38 -0.45,0.84 0,0 0.12,0.91 1.25,0.85 0,0 0.32,1.38 0,0 0.23,0.35 -0.46,0.1 -0.03,0.19 0,0 -0.47,0.99 0,0 -0.45,0.42 0,0 -0.35,0.17 0,0 -0.19,-0.19 0,0 -0.48,0.16 0,0 -1.5,0.54 -0.14,0.24 -1.44,0.06 -0.85,0.39 0,0 -0.63,-0.04 0,0 -0.23,-0.04 -0.49,0.55 -0.09,1.62 1.14,1.32 0.44,1.09 0,0 -0.5,0.24 0.08,0.29 -0.43,0.47 -0.49,1.16 -0.81,0.46 -0.73,-0.14 -0.11,-0.25 -1.59,-0.87 -1.13,0.3 -0.18,0.35 -1.23,-0.08 -0.72,0.25 -0.69,-0.67 -1.16,0.46 -0.67,-0.37 -0.49,0.85 -1.04,0.22 -0.12,-0.86 -0.23,-0.02 -0.14,-0.35 -0.71,0.3 -0.31,-0.5 -1.51,0.87 -0.88,-0.01 -0.66,0.94 0.27,0.49 -0.68,0.76 0.87,1.89 0.51,-0.47 0.5,0.36 0.27,0.68 0.36,0.26 0.01,0.51 -1.43,1 -0.06,0.51 -1.16,1.25 -1.43,0.78 -0.53,0.78 -1.12,0.47 -0.58,0.86 -1.76,0.33 -1.13,0.49 -0.34,0.48 -0.38,-0.62 -0.47,1.28 -0.3,0.17 -0.66,-0.13 -1.58,1.33 -0.03,1.24 -0.58,0.2 0.01,-1.05 0.33,-0.25 -0.01,-0.29 -0.55,-0.75 0.13,-0.87 -0.72,-1.19 -0.11,-1.45 -1.28,-0.32 -0.47,-0.43 -0.97,-0.16 -0.79,-0.39 -1.06,0.18 -0.37,-0.53 -1.33,-0.29 -5.78,-0.45 -3.05,0.29 -0.42,0.47 -0.26,-0.18 0.03,-0.34 -0.25,1.11 -0.34,0.49 -1.75,0.74 -0.72,-0.11 -1.05,-0.66 -0.76,0.01 -1.18,-0.36 -1.73,-1.96 -0.46,-0.01 -1.25,1.02 0,0 -0.34,0.36 -0.62,-0.35 -0.01,-0.39 -0.35,-0.06 -0.24,-0.71 0,0 -0.5,0.06 0,0 -0.31,-0.64 -0.53,-0.06 0.05,-1.59 -0.69,-0.51 -0.79,0.27 -0.29,-0.14 -0.02,0.7 -0.52,0.05 0.15,-0.42 -0.21,-0.24 -0.64,0.33 -0.01,-0.76 0.71,-0.54 0.15,0.1 0.23,-0.55 0.58,-0.22 -0.01,-0.57 0.51,-0.16 -0.89,-0.82 -0.1,-0.51 -0.61,-0.43 0.02,-0.34 -0.43,0.23 -0.2,-0.36 -0.49,0.41 -0.4,-0.36 -0.14,-0.54 -0.41,-0.08 -0.8,0.2 -0.05,0.27 -0.6,0.31 -0.33,-0.14 0.45,-0.32 0.13,-1.01 -0.36,-0.79 -0.19,0.01 0.14,0.47 -0.54,-0.33 0.33,-0.32 -0.3,-0.37 0.27,-0.26 -0.25,-0.47 -0.2,0.08 -0.03,1.07 -0.47,0.35 -0.06,0.5 -0.52,-0.1 0,0 -0.21,-0.42 -1.16,-0.05 -0.42,-0.84 0.4,0.13 0.1,-0.16 -0.6,-0.34 -0.17,-0.38 0.48,-0.74 -0.3,-0.4 0.19,-0.59 -0.88,-0.71 -0.83,-0.28 -0.15,-0.33 0.24,-0.36 -0.38,-0.61 1,-1.34 -0.34,-0.3 0.28,-0.33 0.09,-1.39 0.19,-0.25 0.36,0 0.26,-0.53 0.72,-0.06 0.58,-0.46 1.37,-0.17 0.19,-0.48 0.33,0.05 0.48,-0.47 0.78,-0.16 0.19,0.31 0,0 1.37,0.95 0.84,-0.89 0.83,0.41 0.76,-0.16 0.72,-0.87 0.16,-0.73 1.1,-0.36 0.24,-0.29 0.15,0.3 1.21,0.04 0,0 0.55,0.26 0,0 0.49,0.04 1.32,-1.57 0.73,-0.26 0.72,-0.64 0.66,-0.03 0.57,-1.39 0.49,-0.02 0.48,-0.47 0.55,-0.06 0.26,-0.62 0.57,0.06 0.16,-0.2 -0.32,-0.26 0.21,-0.42 -0.16,-0.44 0.88,-0.46 0.65,0.21 0.37,-0.34 1.01,0.38 0.29,0.63 -0.58,0.24 0.51,0.94 -0.36,0.26 0.06,0.3 0.41,-0.24 0.86,0.42 1.06,0.93 0.89,-0.56 1,-0.06 0.28,-0.36 0.5,0.25 0.41,-0.75 0.28,-0.01 0.39,0.45 0.53,-0.3 2.07,0.19 0.21,0.15 0.19,0.87 -0.54,0.4 -0.01,0.76 0.43,0.23 -0.43,-0.04 -0.13,0.28 0.55,0.65 1.34,0.6 0.29,-0.85 0.15,0.19 0.28,-0.12 0.05,-0.31 0.43,-0.31 0.06,0.34 0.46,-0.09 0.03,0.4 0.42,0.24 0.4,0.86 0.27,-0.04 0.33,-0.46 0.62,-0.06 0.36,-0.31 0.69,0.02 0.32,-0.63 1.76,-1 0.54,0.14 1.69,-2.25 0.51,-1.04 0,0 -0.16,-0.43 -0.39,-0.06 0,0 -0.85,-0.27 -0.08,-0.93 -0.74,-1 -0.39,0.29 -0.75,-0.38 0.18,-0.34 -0.22,-1.08 -0.94,-0.73 0.01,-0.42 -0.25,-0.25 0.16,-0.54 -0.45,-0.49 -0.52,-0.08 0.25,-0.4 -0.16,-0.14 0.15,-0.07 -0.14,-0.27 0.19,0.02 -0.07,-0.61 -0.58,0.16 -0.27,-0.23 -0.2,0.11 -0.03,-0.19 -0.6,0.1 -0.54,-0.54 -0.22,0.06 -0.09,-0.28 0.37,-0.41 0.26,0 -0.14,-0.26 0.56,0 -0.02,-1.28 0.13,-0.32 0.27,0.02 -0.05,-0.62 0.52,-0.08 -0.06,-0.27 0.48,0.65 0.21,-0.43 0.43,-0.2 -0.04,-0.71 0.27,0.27 0.55,-0.46 0.44,0.32 0.41,0 0.19,-0.27 0.08,0.37 0.55,-0.06 0.23,0.3 0.42,-0.04 -0.05,-0.28 0.65,0.23 0.25,-0.52 0.33,0.39 0.04,-0.65 0.2,0.21 0.13,-0.28 0.3,0.12 -0.12,-0.25 0.22,-0.46 -0.4,-0.19 0.24,-0.16 -0.03,-0.86 0,0 0.57,0.48 2.21,-1.13 0.35,0.53 -0.02,0.36 0.3,-0.31 0.24,0.12 0.1,0.47 0.64,-0.16 0,0 0,0.18 0,0 0.16,0.16 0.48,-0.08 0.01,-0.29 0.39,-0.31 0.35,0.63 0.24,-0.55 0.18,0.3 0.33,0.05 0.15,0.79 0.74,-0.72 -0.01,-0.22 -0.3,0.11 -0.02,-0.22 0.19,-0.43 0.39,-0.14 -0.03,-0.47 0.65,-0.22 0.32,-0.52 0.86,0.3 0.08,0.32 0.8,-0.82 0.56,-0.08 -0.05,-0.22 0.49,-0.35 0.48,0.12 -0.05,-0.32 -0.31,-0.06 0.08,-0.6 0.39,0.08 0.7,-0.5 0.72,0.02 0.94,0.71 1.3,-0.14 0.76,-0.43 0.11,-0.4 0.52,0.47 -0.15,-0.37 0.37,-0.32 -0.06,-0.75 0.41,0.23 0.41,-0.52 -0.07,0.37 0.17,0.01 0.33,-0.39 -0.06,-0.73 0.35,0.47 0.14,-0.64 0.46,0.03 -0.16,-0.25 0.15,-0.27 0.11,0.25 0.34,-0.03 0.41,0.72 -0.05,0.36 0.56,0.41 1.48,-0.3 -0.12,-0.38 0.34,-0.39 0.4,0.28 0.12,-0.19 0.65,0.19 0.24,-0.17 0.54,0.51 0.5,-0.13 -0.18,0.33 0.53,0.3 0.62,-0.36 0.58,0.08 0.31,-0.45 2.14,-0.48 z"
                            id="tinh-lam-dong" data-id="tinh-lam-dong" data-link="{PROVINCE_LIST_703.link}"
                            data-tinh="{PROVINCE_LIST_703.title}" data-province="{PROVINCE_LIST_703.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 342.87847,611.29323 0.81,0.33 0.88,-0.11 0.29,0.26 0.39,-0.62 0.83,0.28 -0.37,1.87 0.45,0.69 0.24,0.77 -0.17,0.43 0.27,0.17 0.08,0.77 0.37,0.63 -0.43,0.76 1,0.26 0.19,0.44 0.65,0.47 1.02,0.25 0.15,1.17 -0.65,0.82 0.28,0.82 0.74,-0.17 0.38,0.48 0.39,-0.07 0.57,0.67 1.23,-0.28 0.58,0.62 0.42,0.23 0.62,-0.06 0.5,0.67 1.28,0.53 0.44,0 0.8,-0.46 0.79,0.69 0.87,0.08 0.59,0.56 0.59,1.98 0.87,1.35 0.72,0.48 0.2,0.52 0.78,0.48 1.34,0.13 3.17,-0.36 0,0 -0.51,0.95 1.33,1.15 0.16,0.39 -0.18,0.25 0.41,0.27 0.41,-0.09 0.23,0.46 0.11,1.1 -0.59,0.64 -0.39,-0.28 -0.4,0.31 -0.16,0.38 -0.8,0.85 -0.57,0.75 -0.58,2.91 -0.41,-0.13 -0.42,0.41 -0.55,1.67 -0.55,0.4 0.07,0.65 -0.14,0.08 -0.21,-0.51 -1.69,-0.8 -1.34,0.38 -1.16,-0.47 -0.45,0.15 -0.42,0.56 -0.08,0.7 0.48,0.76 -0.49,0.45 -0.38,0.6 -0.22,0.65 0.09,2.46 -0.35,1.36 0.29,1.04 -0.38,0.63 0.07,1.55 0.25,0.49 0.48,0.23 -0.1,0.51 -0.68,0.23 -0.62,0.32 -0.36,0.79 -1.28,0.98 -1.29,0.23 -0.74,0.06 -0.27,-0.19 -1.56,-1.14 -0.82,0.17 -0.33,-0.04 0,0 0.42,-0.3 0.07,-0.77 -0.49,0.09 -0.49,-0.25 -0.54,-0.51 -0.13,-0.58 -3.03,-0.59 -0.13,-0.31 -1.35,-0.92 0.18,-3.08 -1.83,0.03 -0.64,0.38 -1.8,-0.59 -1.01,-1.62 -0.81,-2.4 -3.2,-0.31 0,0 -0.44,-1.09 -1.14,-1.33 0.1,-1.61 0.49,-0.55 0.23,0.04 0,0 0.63,0.04 0,0 0.85,-0.39 1.44,-0.06 0.14,-0.24 1.51,-0.54 0,0 0.48,-0.16 0,0 0.19,0.19 0,0 0.35,-0.16 0,0 0.45,-0.42 0,0 0.47,-0.99 0,0 0.03,-0.19 0.45,-0.1 -0.23,-0.35 0,0 -0.31,-1.38 0,0 -1.25,-0.85 -0.12,-0.91 0,0 0.45,-0.84 -0.18,-0.36 -1.7,-0.31 -0.86,-0.47 0,0 -0.07,-0.27 0.36,-0.34 0.19,-0.81 0.58,-0.09 0.63,-0.61 -0.14,-1.1 0.18,-1.64 0.53,-1.08 -0.22,-0.62 0.3,-0.58 -0.03,-0.5 -0.28,-0.27 0.09,-0.47 1,-1.24 0.17,-0.83 -0.18,-0.64 0.24,-0.62 0.49,-0.86 0.6,-0.21 0.29,-0.64 -0.17,-1.19 -0.31,-0.34 0.09,-0.36 -1.6,-1.54 0.23,0.02 0.43,-0.44 0.35,0.03 0.19,-0.57 -0.15,-0.32 0.54,-0.19 0.1,-0.24 0.8,0.05 0.46,-0.53 z"
                            id="tinh-ninh-thuan" data-id="tinh-ninh-thuan" data-link="{PROVINCE_LIST_705.link}"
                            data-tinh="{PROVINCE_LIST_705.title}" data-province="{PROVINCE_LIST_705.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 204.49847,631.73323 0.95,0.47 0.54,0.51 0.49,0.65 1.29,-0.04 1.06,0.44 0.71,-0.64 0.47,0.13 0.59,-0.37 0.38,0.69 0.64,0.5 2.16,0.15 0.87,0.41 0.19,0.78 1.49,1.29 0.92,0.25 1.18,-0.26 1.33,-0.74 1.01,-0.07 0.8,0.28 0.83,0.72 0.56,0.01 0.4,0.39 0.82,-0.02 0,0 0.06,0.51 0.23,0.14 -0.11,0.21 0.31,0.15 -0.04,0.31 0.32,0.3 -0.05,0.84 0.29,0.5 0.4,0.01 -0.05,0.16 0.55,0.36 -0.2,0.45 0.4,0.83 -0.25,0.09 0.34,0.47 -0.99,1.23 -0.99,0.27 -0.55,-0.3 -0.58,0.35 -0.17,1.57 -0.37,0.16 0.13,0.66 0,0 -0.53,0.84 0.17,0.09 -0.34,0.1 0.05,0.27 -0.5,0.15 0.12,0.22 -0.74,0.31 -0.13,-0.12 -0.3,0.33 -0.43,0.07 -0.1,-0.19 -0.17,0.24 0.1,0.15 -0.35,0.31 0.18,0.38 -0.47,0.73 0.01,1.17 -0.54,0.55 0.08,0.6 -0.16,-0.15 -0.1,0.19 0.04,0.66 -0.54,-0.02 -0.45,1.11 0.96,1.02 -0.5,1.5 0.23,0.19 -0.05,-0.3 0.26,0.11 -0.18,0.46 0.15,0.2 0.17,-0.36 0.29,0.22 0.13,-0.15 -0.24,1.33 0.72,0.13 -0.67,0.64 0.52,0.54 0.51,-0.34 0.41,0.85 0.24,0.02 -0.19,0.53 0.49,0.31 -0.28,0.97 0.22,0.44 0.64,0.22 0.24,-0.35 0.29,0.25 -0.01,0.59 1.13,-0.03 0.25,0.76 -0.39,0.5 0.93,0.74 0,0 -0.28,1.08 -0.57,0.83 -0.02,0.35 -0.21,0 -0.03,1.36 -0.23,0.21 0.17,0.73 -0.19,0.67 -0.41,0.15 -0.24,0.55 0.28,0.72 -1.33,0.52 -1.34,0.11 -0.29,0.25 0,0 -0.29,-0.36 -0.28,0.21 -0.11,-0.43 -0.95,-0.67 -1.3,-0.56 -0.46,0.02 -0.68,0.53 -0.22,-0.26 -0.15,0.13 -0.06,0.3 0.6,0.41 0.27,0.61 -0.2,0.4 -0.53,0.26 -1.74,-0.32 -3.22,1.3 -0.5,-0.03 -0.21,-0.24 0,-0.56 0,0 1.52,0.23 0.63,-0.21 -0.64,-1.26 -0.31,-2.59 -1.72,-2.62 -0.5,0.01 -1.08,0.62 -0.8,0.93 -0.28,0 -0.25,-0.35 0.07,-0.45 -0.54,-0.66 -0.66,0.11 -1.15,-1.32 -1.63,-0.95 -0.28,-0.54 -0.36,-2.1 -1.15,-0.08 -0.67,-0.31 -0.65,0.09 -1.13,-0.48 -0.79,-0.65 -0.81,-1.21 -0.15,-2.1 -1.54,-0.35 -0.46,0.48 -0.26,-0.09 -0.01,-0.6 0.36,-0.85 -0.2,-0.55 0.36,-0.49 0.47,-1.66 -0.01,-0.48 -0.63,-0.29 0.43,-0.91 -0.41,-0.72 0.59,-0.24 0.44,-0.86 0.54,-0.25 0.13,-0.9 -0.19,-0.28 -0.61,0.06 -0.68,-0.56 0.13,-0.81 0.37,-0.35 -0.31,-0.96 0.31,-1.2 -0.37,-1.28 -0.82,-0.91 -0.58,0.3 -1.23,-0.22 -0.44,-0.17 -0.16,-0.32 -0.22,-0.73 -0.06,-1.62 0.94,-0.06 0.25,-0.33 0.22,-1.15 0.98,-0.87 1.35,-0.65 0.47,0.03 1.77,1.41 1.48,0.54 1.44,-1.59 -0.2,-0.71 0.28,-0.47 0.69,-0.61 0.34,-0.03 0.67,-1.2 -0.28,-1.2 0.46,-1.16 1.17,-0.41 1.04,-0.18 0.32,0.03 0.19,0.26 z"
                            id="tinh-tay-ninh" data-id="tinh-tay-ninh" data-link="{PROVINCE_LIST_709.link}"
                            data-tinh="{PROVINCE_LIST_709.title}" data-province="{PROVINCE_LIST_709.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 248.74847,693.04323 -0.53,0.06 -0.35,-0.79 -1.16,-1.26 -0.52,-0.09 -0.7,0.47 -1.02,0.14 -0.15,0.48 -0.58,-0.03 -0.95,-1.58 -1.26,-0.82 -0.17,0.13 0,0 -1.34,-1.25 -0.08,-1.28 1.03,-1.3 1.49,-0.72 0.28,-0.57 0.66,-0.51 0,0 0.31,0.19 0,0 -0.02,0.93 1.05,-0.82 0.42,0.03 0.64,0.52 0.28,-0.04 0.16,-0.36 -0.49,-0.89 0.89,-0.91 -0.23,-0.7 -1.19,-1.31 -0.38,-0.93 0.22,-0.72 -0.38,-0.44 -0.18,-0.75 0,0 -0.43,-0.43 -0.79,0.19 -0.36,-0.86 -1.11,-0.12 0.19,-0.4 -1.09,-0.69 0.01,-0.47 -0.42,-0.07 0.03,-0.62 0.4,-0.03 0.22,0.32 1.08,-0.66 -0.72,-0.79 -0.39,-0.99 0.54,-0.65 1.21,-0.42 0.24,-0.29 -0.21,-0.7 -0.51,-0.62 1.46,-0.43 0.66,1.33 0.96,0.39 0.53,0.57 0.26,0.09 1.09,-0.41 0.72,0.27 0.22,-0.96 0.57,-0.39 -0.04,-0.42 0.49,-0.45 0.48,0.39 0.35,-0.13 0.23,-0.65 -0.39,-0.33 0.1,-0.56 0.65,-0.62 -0.08,-0.33 0,0 0.38,-0.06 0,0 -0.59,-1.11 0.19,-0.28 -0.15,-0.35 0.6,-0.1 0.02,-0.26 -0.52,-0.26 -0.15,-0.59 -0.63,-0.2 0.23,-0.47 -0.33,-0.14 -0.31,-0.55 -0.52,0.11 -0.19,-0.32 -0.67,0.04 0,-0.58 -0.29,-0.28 0.72,-0.29 0.15,-0.29 -0.24,-0.62 0.27,-0.2 -0.35,-0.02 -0.05,-0.28 1.09,-0.17 -0.26,-0.13 0.04,-0.71 -0.17,-0.18 0.21,-0.03 -0.17,-0.33 0.31,-0.09 0.13,-0.75 0.44,-0.32 -0.23,-0.17 0.14,-0.01 -0.1,-0.56 0,0 0.56,0.04 0.49,0.33 0.01,-0.46 0.52,-0.22 -0.05,-0.17 0.85,0.25 0.1,-0.53 -0.37,-0.47 0.4,0.01 -0.01,0.19 0.39,-0.19 0.09,0.3 0.14,-0.2 0.18,0.09 -0.08,-0.24 0.21,0.03 0.02,-0.33 0.55,-0.11 -0.16,-0.42 0.33,0.05 0.43,-0.38 0.3,0.11 -0.02,-0.2 0.21,0.03 0.21,-0.26 -0.13,-0.27 0.3,-0.04 -0.31,-0.2 0.36,-0.08 -0.05,-0.2 0.61,0.17 0.41,-0.22 -0.2,-0.18 0.21,-0.44 -0.27,-0.32 0.67,-0.47 -0.02,-0.44 0.41,-0.29 -0.28,-0.51 -0.24,-0.03 0,-0.66 0.41,-0.75 1.04,-0.7 0.06,-0.62 1.24,-0.15 0.85,-1.01 9.65,-2.07 0.75,-0.34 0.52,0.11 0.06,-0.5 0.47,-0.35 0.03,-1.07 0.2,-0.08 0.25,0.47 -0.27,0.26 0.29,0.37 -0.32,0.32 0.54,0.33 -0.13,-0.47 0.19,-0.01 0.37,0.8 -0.13,1 -0.46,0.32 0.34,0.14 0.59,-0.3 0.06,-0.27 0.79,-0.2 0.42,0.08 0.14,0.54 0.4,0.36 0.49,-0.41 0.21,0.36 0.42,-0.23 -0.01,0.35 0.61,0.43 0.1,0.51 0.89,0.82 -0.51,0.16 0.01,0.58 -0.58,0.22 -0.23,0.55 -0.15,-0.1 -0.7,0.54 0.01,0.76 0.64,-0.34 0.21,0.24 -0.15,0.42 0.52,-0.04 0.02,-0.71 0.28,0.14 0.8,-0.27 0.69,0.51 -0.04,1.6 0.53,0.06 0.31,0.64 0,0 0.5,-0.06 0,0 0.24,0.72 0.36,0.05 0.01,0.39 0.62,0.35 0.34,-0.36 0,0 -0.4,1.95 -0.91,0.79 0.32,0.54 -0.28,0.79 0.32,1.72 -0.27,0.21 -0.13,-0.19 -0.47,0.77 -0.31,0.08 -0.23,-0.36 0.14,0.7 -0.32,0.22 0.27,0.23 -0.43,-0.24 -0.42,0.21 0.31,0.24 -0.52,0.37 0.27,0.37 -0.18,0.22 -0.1,-0.22 -0.25,0.12 0.35,0.52 -0.42,-0.15 0,0.4 -0.23,0.11 0.17,0.27 -0.24,-0.1 -0.29,0.2 0.08,0.5 -0.38,0.42 0.16,0.44 -0.43,0.23 -0.23,-0.11 -0.14,0.51 -0.41,0.11 0.05,0.38 0,0 0.47,0.28 0,0 0.48,0.78 -0.56,1.37 -0.25,-0.29 -0.4,0.14 0.08,0.23 -0.49,0.36 0.14,0.66 -0.47,0.67 -0.84,-0.16 -0.3,0.21 0.44,0.06 0.16,0.28 0.33,-0.16 0.02,0.2 0.51,0.03 -0.05,0.26 1.17,0.32 0.31,0.52 0.79,0.25 0.48,0.48 1.17,-0.39 0.52,0.06 0.56,0.77 0.92,0.61 0.11,0.5 0.28,-0.07 0.92,2.31 -0.01,3.5 0.72,1.1 0.17,0.65 -0.18,1.09 -2.07,1.78 -0.62,0.37 -0.78,0.08 0,0 -0.47,0.3 -0.61,-0.09 -0.67,-1.29 -0.71,0.13 -0.22,-0.6 -0.26,-0.1 -0.55,0.22 -0.39,0.63 0,0 -0.21,0.08 0,0 -0.32,0.56 0,0 -0.07,0.11 0,0 -0.07,0.09 0,0 -0.21,-0.04 0,0 -0.67,0.21 -0.47,0.7 -0.45,0.23 -0.62,1.04 -0.56,0.2 -0.15,0.69 -0.52,0.46 0.29,0.48 -0.22,0.44 0.25,0.14 -0.27,0.45 0,0 -0.24,0.14 0,0 -0.65,-0.14 -0.73,0.47 -0.93,-0.81 0.41,-0.51 -0.34,-0.41 0.24,-0.67 -0.26,-0.22 0.04,-0.6 0.17,0.06 -0.22,-1.36 -2.91,-0.09 0.01,-0.16 -0.7,-0.21 -0.52,0.36 -0.77,-0.09 0.01,-0.85 -0.16,0.01 -1.19,0.38 -0.59,0.72 -0.77,0.49 -0.92,-0.01 0.12,0.64 0.35,0.12 -0.12,0.31 -0.23,-0.21 0.09,0.49 -0.37,1.74 -0.45,0.16 -0.21,0.48 -1.06,0.55 -0.25,0.58 -2.57,-0.29 -1.68,0.71 0,0 -0.71,0.02 -0.02,0.27 -0.32,0.13 0.04,0.76 0.58,0.53 0,0 -0.87,0.29 -0.18,0.42 0,0 -0.09,0.19 -0.12,0.58 -0.27,-0.16 -0.87,-0.56 -0.97,-0.17 -0.44,-0.18 -0.85,-0.59 z"
                            id="tinh-dong-nai" data-id="tinh-dong-nai" data-link="{PROVINCE_LIST_713.link}"
                            data-tinh="{PROVINCE_LIST_713.title}" data-province="{PROVINCE_LIST_713.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 353.95847,696.06323 0.49,0.4 0.3,0.69 -0.03,0.61 0.46,0.29 0.03,0.32 -0.3,0.23 -0.61,0.15 -1.05,-0.6 -0.46,-1.7 0.35,-0.44 0.82,0.05 z m -18.87,-52.77 3.2,0.31 0.81,2.4 1.01,1.62 1.8,0.6 0.64,-0.38 1.83,-0.03 -0.18,3.08 1.36,0.92 0.13,0.31 3.03,0.59 0.13,0.58 0.54,0.51 0.49,0.25 0.49,-0.09 -0.07,0.77 -0.42,0.3 0,0 -0.78,-0.05 -1.22,0.79 -0.62,-0.1 -1.44,0.44 -0.32,0.34 -0.25,1 -1.01,0.21 -0.38,0.89 -0.26,1.85 -0.66,1.22 -0.16,1.2 -0.94,0.57 -0.14,0.51 -0.29,0.03 -0.05,-0.71 -0.35,-0.26 -1.37,-0.48 -1.03,-0.06 -1.01,0.26 -0.72,0.7 -0.25,-0.43 -1.25,0.01 -1.18,0.98 -2.06,0.57 -0.88,0.89 -0.88,1.71 -0.9,2.93 -0.77,0.49 -0.66,-0.42 -0.73,0.06 -0.95,1.07 -0.26,-0.37 -1.91,0.04 -1.13,1.33 -0.58,2.8 -0.41,0.16 -0.23,-0.37 -0.41,-0.13 -0.5,0.17 -0.62,1.04 -0.16,1.22 -0.74,-0.17 0.12,-0.79 -0.35,-0.52 -2.56,-0.64 -1.03,0.33 -0.94,0.64 -1.5,0.34 -1.25,-0.21 -1.47,0.87 -0.37,-0.19 -0.7,0.31 -1.07,1.31 -1.38,2.75 -0.75,2.38 -0.54,3 -0.41,0.72 -0.71,0.86 -0.82,-0.07 -0.79,0.37 -0.25,-0.37 -1.3,-0.48 -1.59,-0.29 -1.56,0.27 -1.42,0.69 -0.69,-0.1 -1.11,0.7 -1.14,1.4 -1.33,1.05 -1.86,0.75 -1.49,-0.02 -1.23,0.57 -0.6,0.6 -4.33,1.73 0,0 -0.09,-0.46 0.19,-0.4 -0.55,-0.56 -1.67,-4.08 0.34,-0.7 -0.12,-0.63 -0.52,-0.86 -1.22,-1.13 -0.44,-0.1 0.24,-1.39 0,0 0.78,-0.08 0.63,-0.37 2.07,-1.78 0.18,-1.08 -0.17,-0.65 -0.72,-1.1 0.01,-3.5 -0.92,-2.31 -0.28,0.07 -0.11,-0.5 -0.92,-0.61 -0.56,-0.77 -0.52,-0.06 -1.17,0.39 -0.48,-0.48 -0.79,-0.25 -0.31,-0.51 -1.17,-0.32 0.05,-0.25 -0.51,-0.03 -0.02,-0.2 -0.33,0.16 -0.16,-0.28 -0.44,-0.06 0.3,-0.21 0.84,0.16 0.47,-0.67 -0.14,-0.66 0.49,-0.35 -0.08,-0.23 0.4,-0.14 0.25,0.29 0.56,-1.37 -0.48,-0.78 0,0 -0.46,-0.28 0,0 -0.05,-0.37 0.41,-0.11 0.14,-0.51 0.23,0.11 0.43,-0.23 -0.16,-0.44 0.38,-0.42 -0.08,-0.5 0.29,-0.2 0.24,0.11 -0.17,-0.27 0.23,-0.11 0.01,-0.4 0.42,0.15 -0.35,-0.52 0.25,-0.12 0.1,0.22 0.19,-0.22 -0.27,-0.37 0.52,-0.37 -0.31,-0.24 0.42,-0.21 0.43,0.24 -0.27,-0.23 0.33,-0.22 -0.14,-0.7 0.23,0.36 0.31,-0.08 0.47,-0.77 0.14,0.19 0.27,-0.21 -0.32,-1.72 0.28,-0.79 -0.31,-0.54 0.91,-0.79 0.41,-1.95 0,0 1.25,-1.02 0.46,0.01 1.73,1.96 1.18,0.36 0.76,-0.01 1.05,0.66 0.72,0.11 1.75,-0.74 0.35,-0.49 0.25,-1.11 -0.03,0.34 0.26,0.18 0.42,-0.47 3.05,-0.28 5.78,0.45 1.34,0.29 0.37,0.53 1.06,-0.18 0.79,0.4 0.97,0.15 0.47,0.44 1.28,0.31 0.11,1.46 0.72,1.19 -0.12,0.87 0.55,0.75 0.01,0.29 -0.33,0.26 -0.01,1.05 0.59,-0.2 0.02,-1.24 1.58,-1.33 0.66,0.13 0.29,-0.16 0.47,-1.28 0.38,0.62 0.34,-0.47 1.14,-0.49 1.76,-0.33 0.58,-0.86 1.12,-0.47 0.53,-0.78 1.42,-0.78 1.16,-1.25 0.07,-0.51 1.43,-1 -0.01,-0.52 -0.36,-0.26 -0.26,-0.68 -0.5,-0.35 -0.51,0.47 -0.87,-1.89 0.68,-0.76 -0.27,-0.5 0.67,-0.93 0.88,0.01 1.5,-0.87 0.31,0.5 0.71,-0.29 0.14,0.34 0.23,0.03 0.12,0.86 1.04,-0.22 0.49,-0.84 0.67,0.37 1.16,-0.46 0.69,0.67 0.71,-0.25 1.24,0.08 0.18,-0.36 1.12,-0.3 1.59,0.88 0.11,0.25 0.73,0.14 0.81,-0.46 0.49,-1.16 0.43,-0.47 -0.08,-0.29 0.46,-0.38 z"
                            id="tinh-binh-thuan" data-id="tinh-binh-thuan" data-link="{PROVINCE_LIST_715.link}"
                            data-tinh="{PROVINCE_LIST_715.title}" data-province="{PROVINCE_LIST_715.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 189.27847,670.95323 1.45,3.03 2.97,3.9 -0.85,1.74 0.08,0.17 2.54,0.98 1.96,0.27 -0.31,-1.26 1.36,-1.08 -0.27,-0.28 -0.31,-1.49 3.44,3.24 3.18,2.3 1.29,0.23 3.5,-0.22 1.37,1.21 -0.18,-2.7 -1.25,-2.03 -0.04,-0.44 -1.3,-1.5 0.69,-1.35 -0.3,-0.44 -0.08,-1.44 0.54,-0.31 0.56,0.24 0,0 -0.01,0.56 0.21,0.24 0.5,0.03 3.22,-1.3 1.73,0.32 0.53,-0.26 0.2,-0.39 -0.27,-0.62 -0.6,-0.4 0.06,-0.3 0.15,-0.13 0.22,0.26 0.68,-0.53 0.46,-0.01 1.3,0.56 0.95,0.67 0.12,0.43 0.27,-0.2 0.29,0.36 0,0 0.85,1.16 2.03,-0.21 2.16,1.68 4.14,2.13 -1.52,5.91 -2.1,1.71 1.23,1.4 0.74,0.17 0.4,0.5 0.57,-0.34 0.49,0.32 -0.13,0.47 0.35,1.08 0.88,0.14 0.15,0.93 -0.23,0.53 0.36,0.03 0.07,0.4 0.62,-0.36 0.4,0.39 0.15,-0.35 1.06,0.27 0.29,-0.45 0.97,0.61 -0.17,0.34 0.42,-0.05 0.03,0.81 0.21,0.1 0.41,-0.1 0.23,-0.44 0.37,0.1 0.16,-0.56 0.48,0.18 0,0 0.09,0.03 0,0 0.66,-0.34 0.28,-0.55 0.28,0.41 0.51,-0.28 1.11,0.42 0.46,0.56 0.13,0.34 -0.22,0.03 0.14,0.28 -0.22,0.36 0.27,0.33 -0.47,-0.1 -0.25,0.31 0.07,0.5 0.44,0.86 0.42,-0.33 0.14,0.2 -0.2,0.27 0.53,0.06 -0.2,0.76 0.34,1.9 -0.7,1.49 0,0 -1.84,1.75 0,0 -0.9,1.25 0,0 -0.27,0.08 0,0 -0.66,-0.13 -0.24,-0.45 0,0 0.02,-0.82 0,0 -0.14,-0.66 -0.31,-0.3 -0.6,0.16 -0.22,0.41 0,0 -0.16,0.75 0,0 -0.94,0.59 0,0 -0.87,0.54 0,0 -0.59,-0.02 0.02,0.59 0,0 0.03,0.2 0,0 0.04,0.22 -0.5,0.18 0,0 -1.04,0.09 -0.06,-0.62 0,0 -0.48,0 0,0 -0.14,-0.25 -0.44,0.46 -0.43,-0.67 -0.54,0.47 -0.44,-0.2 -0.57,0.68 -0.58,0.26 -0.09,0.35 -0.25,-0.04 -0.08,0.39 -0.78,-0.12 -0.46,0.21 -0.11,-0.25 -0.31,0.05 0.05,-0.25 0.29,0.15 0.3,-0.21 -0.42,-0.43 -1.1,-0.07 -0.13,0.23 -0.17,-0.06 -0.22,-0.84 -0.32,-0.29 0.63,-0.06 1.33,-1.02 -0.57,-0.27 -0.02,-0.43 -1.27,-0.81 0.03,-0.32 -0.75,-0.19 -0.57,0.76 -0.66,-0.14 -0.46,-0.42 0.17,-0.33 -0.27,-0.19 0.16,-0.43 -0.28,-0.26 0.15,-0.33 -1.11,-0.6 0.01,-0.84 -0.41,-0.07 -1.06,-1.27 -0.49,0.24 -1.25,-1.12 -4.24,0.42 -5.48,0.18 0.16,2.77 -8.36,-0.71 0,0 -1.63,-0.11 -3.98,-5.27 -1.52,-0.82 -1.26,-0.24 -9.59,-9.02 0.79,-0.06 -0.03,-0.16 -1.46,-3.01 -3.56,-1.64 -0.83,-1.62 0,0 0.51,0.25 1.47,-0.14 0.81,-0.37 5.51,-1.35 1.95,-0.93 1.34,-1.06 z"
                            id="tinh-long-an" data-id="tinh-long-an" data-link="{PROVINCE_LIST_801.link}"
                            data-tinh="{PROVINCE_LIST_801.title}" data-province="{PROVINCE_LIST_801.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 235.33847,790.06323 0.28,0.53 -0.38,0.2 0.12,0.32 -0.08,0.37 -0.11,0.16 -0.89,-0.1 -0.33,0.77 1.15,1.28 -0.41,0.11 -0.4,-0.4 -0.56,-0.07 -0.33,0.65 -0.31,-0.16 -1.2,0.97 0.82,1.65 -1.92,-0.26 -0.65,0.36 -0.39,0.57 -0.4,-0.83 -0.35,-0.16 -0.08,-0.75 0.41,0.11 0.19,-0.31 0.39,0.68 0.85,-0.1 -0.81,-1.14 0.05,-0.36 0.4,-0.53 0.28,0.06 0.32,-0.78 0.51,0.02 -0.06,-0.58 0.25,-0.4 0.68,-0.2 0.28,-0.31 0.37,0.2 0.29,-0.35 -0.09,-0.32 1.49,-0.6 0.22,-0.28 0.4,-0.02 z m 26.21,-88.45 -0.12,-0.79 0.51,0.25 0.2,0.29 0.64,0.29 0.33,0.34 0.35,0.53 -0.55,0.19 -0.36,-0.13 -0.58,0.92 -0.39,0.06 -0.49,1.18 -1.53,0.69 -1.18,0.95 -1.24,1.96 -0.58,-0.67 0.07,-0.67 -0.69,-0.55 0.12,-0.52 -0.36,-0.42 0.1,-0.58 0.35,-0.04 0.46,0.41 0.53,-0.69 0.97,-0.09 0.39,-0.8 0.44,-0.16 0.23,0.23 0.29,-0.38 0.79,-0.07 0.13,-0.29 -1.27,0.09 -0.13,0.18 -0.26,-1.04 0.54,-0.32 -0.22,-0.61 0.79,-0.1 0.27,0.25 0.3,-0.74 0.3,0.04 0.13,0.45 -0.25,0.18 0.97,0.18 z m -4.81,-1.46 0.58,-0.12 0.39,0.13 -0.09,0.37 0.32,0.42 0.67,-0.79 0.29,0.69 -0.13,0.72 -0.46,0.23 -0.2,0.5 -0.74,-0.03 -0.29,0.26 -1.39,-0.82 -0.71,-1.02 1.76,-0.54 z m -1.78,0.11 -0.78,-0.09 -0.04,0.53 -0.36,-0.44 0.13,-0.42 -0.22,-0.66 -0.46,0.07 -0.35,-0.47 0.52,-0.36 0.26,-0.56 0.67,-0.9 -0.13,-0.6 -0.69,-0.26 0.77,-1.45 0.02,-0.54 -0.52,-1.12 -0.52,-0.19 -0.21,-0.42 -0.24,-0.2 0.33,-0.45 0.64,0.28 -0.14,-0.67 1.68,-0.71 2.57,0.29 0.25,-0.58 1.06,-0.55 0.21,-0.48 0.45,-0.16 0.37,-1.74 -0.09,-0.49 0.23,0.21 0.12,-0.31 -0.35,-0.12 -0.12,-0.64 0.92,0.01 0.77,-0.49 0.59,-0.72 1.19,-0.38 0.16,-0.01 -0.01,0.85 0.77,0.09 0.52,-0.36 0.7,0.21 -0.01,0.16 2.92,0.09 0.22,1.36 -0.17,-0.06 -0.04,0.6 0.26,0.22 -0.24,0.67 0.34,0.41 -0.41,0.51 0.93,0.82 0.73,-0.47 0.65,0.14 0,0 0.24,-0.13 0,0 0.27,-0.45 -0.25,-0.13 0.22,-0.44 -0.29,-0.48 0.52,-0.46 0.15,-0.69 0.56,-0.19 0.62,-1.04 0.45,-0.23 0.47,-0.7 0.67,-0.21 0,0 0.21,0.04 0,0 0.07,-0.09 0,0 0.07,-0.11 0,0 0.32,-0.56 0,0 0.21,-0.08 0,0 0.39,-0.63 0.55,-0.22 0.26,0.1 0.22,0.6 0.71,-0.13 0.67,1.29 0.61,0.09 0.47,-0.3 0,0 -0.24,1.39 0.44,0.1 1.22,1.13 0.52,0.86 0.12,0.63 -0.34,0.7 1.67,4.08 0.55,0.56 -0.19,0.4 0.09,0.46 0,0 -1.9,1.64 -1.55,2.29 -2.39,0.24 -1.29,1.51 -4.66,0.09 -2.51,1.69 -2.34,2.44 -0.16,0.42 -0.82,0.13 -1.25,-1.44 -2.1,0.22 0.64,-0.48 0.2,-0.51 0.58,0.22 0.35,-0.15 0.13,-0.29 -0.14,-1.01 -0.19,-0.28 -0.26,0.32 -1.2,-1.05 -0.53,-0.12 -0.51,-0.5 -1.03,0.18 -0.29,0.1 -0.5,0.03 -0.46,-0.44 -0.81,-0.54 -0.38,0.32 -0.3,-0.01 -0.56,0.1 -0.42,-0.34 -0.36,0.38 -0.68,0.01 -0.39,0.22 z"
                            id="tinh-ba-ria-vung-tau" data-id="tinh-ba-ria-vung-tau"
                            data-link="{PROVINCE_LIST_717.link}" data-tinh="{PROVINCE_LIST_717.title}"
                            data-province="{PROVINCE_LIST_717.id}" fill="#0685d6" stroke="rgb(247, 247, 247)"
                            strokewidth="0.621318px">
                        </path>
                        <path
                            d="m 154.85847,674.85323 -0.02,0.69 -0.75,1.08 0.18,0.22 2.56,-0.2 1.67,0.6 0,0 0.02,2.02 1.06,1.12 1.1,2.44 1.58,0.18 -0.08,1.05 0.18,0.6 0.62,0.35 1.31,0.09 0.27,0.27 -0.12,0.47 -1.27,0.86 0.08,0.48 0.96,0.35 2.01,-0.34 0.67,0.31 -0.34,2.27 0.05,1.91 1.33,3.22 1.21,0.39 1.25,0.98 1.51,-0.13 1.83,0.91 0.35,-0.33 1.4,-0.28 1.33,0.76 0.93,1.16 0.74,1.73 0.17,2.53 -1,-0.25 -0.33,0.25 -0.1,0.44 0.31,0.23 -0.68,0.19 -0.39,0.28 -0.11,0.39 -0.29,0.02 0.26,1.27 -0.43,0.33 0,0.35 -1.05,0.44 -0.46,0.45 -0.2,0.6 0.4,0.49 0,0 -0.43,0.08 -0.51,0.68 -2.64,0.41 0.77,1.75 -5.47,1.86 -1.21,-2.01 -0.33,0.19 -0.27,-0.43 -1.78,2.02 -0.32,-0.38 -0.34,0.33 -1.22,1.61 0,0 -0.18,-0.22 -1.13,1.56 -4.19,-3.67 -5.53,-3.38 -8.03,-1.99 -2.01,-2.64 0.12,-0.12 -1.61,-3.1 -1,-1.04 0.24,-0.86 -0.23,-0.78 0,0 4.67,-0.18 1.51,-1.35 1.92,-3.32 1.19,-1.42 7.36,-4.37 -0.32,-1.84 -0.86,-1.08 -0.64,-0.36 -0.07,-0.6 -0.32,-0.39 0.11,-0.86 -1.12,-2.35 -0.4,-1.44 0.79,-0.54 -0.13,-0.55 0.58,-0.28 -0.15,-0.5 0.23,-0.27 0.69,-0.38 0.59,0.09 0.25,-1.05 1.46,-0.28 0.24,0.21 z"
                            id="tinh-an-giang" data-id="tinh-an-giang" data-link="{PROVINCE_LIST_805.link}"
                            data-tinh="{PROVINCE_LIST_805.title}" data-province="{PROVINCE_LIST_805.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 171.28847,673.97323 0.98,0.95 0.67,-0.47 0.72,0.13 0.54,0.62 0.48,-0.18 0,0 0.83,1.62 3.56,1.64 1.47,3.01 0.03,0.15 -0.79,0.06 9.6,9.02 1.26,0.24 1.51,0.82 3.98,5.27 1.64,0.12 0,0 0,2.2 -4.28,4.56 -0.79,2.21 -1.48,-0.23 0.18,1.47 -0.13,1.05 0.67,0.23 -0.42,0.46 0.75,0.22 0.12,0.37 1.86,0.33 1.04,0.84 0,0 -0.85,0.66 0.37,0.3 0.3,0.71 0.6,-0.03 -0.04,0.53 0.61,0.46 0.34,0.76 -0.34,0.97 -0.71,0.05 -0.21,0.31 -0.99,-0.18 -0.32,1.07 0.22,0.13 -0.2,0.63 -0.98,-0.3 -0.13,0.53 -1.06,0.07 -1.41,-0.35 -1.36,-1.78 -0.54,0.58 -0.89,-1.01 -1.17,-0.51 0.12,0.11 -2.15,3.16 -0.18,0.55 0,0 -1.15,-0.75 -0.65,-0.91 -1.07,-0.94 0,0 -0.18,-0.21 0,0 -0.26,-0.98 -1.03,-1.73 -2.52,-2.45 -1.94,-1.13 -0.95,-0.94 0,0 -0.4,-0.49 0.2,-0.6 0.46,-0.45 1.05,-0.44 0,-0.35 0.43,-0.33 -0.26,-1.27 0.29,-0.02 0.12,-0.39 0.39,-0.28 0.69,-0.19 -0.31,-0.23 0.11,-0.43 0.33,-0.25 1,0.26 -0.16,-2.52 -0.74,-1.73 -0.93,-1.16 -1.33,-0.76 -1.4,0.28 -0.34,0.33 -1.83,-0.91 -1.51,0.13 -1.25,-0.98 -1.21,-0.38 -1.33,-3.22 -0.04,-1.91 0.34,-2.27 -0.67,-0.31 -2.01,0.34 -0.96,-0.34 -0.08,-0.48 1.27,-0.86 0.12,-0.47 -0.27,-0.27 -1.31,-0.09 -0.62,-0.35 -0.18,-0.6 0.08,-1.05 -1.58,-0.18 -1.1,-2.44 -1.06,-1.12 -0.02,-2.02 0,0 0.65,0.06 2.03,0.95 1.36,-0.21 0.51,0.44 0.58,0.08 2.76,1.44 0.36,-0.4 0.38,-0.6 0.33,-0.43 1.06,-0.86 0.4,-0.87 1.06,-0.6 0.97,-2.21 0.29,-0.08 z"
                            id="tinh-dong-thap" data-id="tinh-dong-thap" data-link="{PROVINCE_LIST_803.link}"
                            data-tinh="{PROVINCE_LIST_803.title}" data-province="{PROVINCE_LIST_803.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 197.74847,696.96323 8.36,0.71 -0.16,-2.77 5.48,-0.18 4.24,-0.42 1.25,1.12 0.49,-0.24 1.06,1.27 0.41,0.07 -0.01,0.84 1.11,0.6 -0.15,0.33 0.28,0.26 -0.16,0.43 0.27,0.19 -0.17,0.33 0.46,0.42 0.66,0.14 0.57,-0.76 0.75,0.19 -0.03,0.32 1.27,0.81 0.02,0.43 0.57,0.27 -1.33,1.02 -0.63,0.06 0.32,0.29 0.22,0.84 0.17,0.06 0.13,-0.23 1.1,0.07 0.42,0.43 -0.3,0.21 -0.29,-0.15 -0.05,0.25 0.31,-0.05 0.11,0.25 0.46,-0.21 0.78,0.12 0.08,-0.39 0.25,0.04 0.09,-0.35 0.58,-0.26 0.57,-0.68 0.44,0.2 0.54,-0.47 0.43,0.67 0.44,-0.46 0.14,0.25 0,0 0.48,0 0,0 0.06,0.62 1.04,-0.09 0,0 0.53,-0.19 -0.07,-0.21 0,0 -0.03,-0.2 0,0 -0.1,-0.51 0.67,-0.06 0,0 0.87,-0.54 0,0 0.94,-0.59 0,0 0.16,-0.75 0,0 0.31,-0.48 0.41,-0.1 0.32,0.16 0.23,0.81 0,0 -0.02,0.82 0,0 0.18,0.4 0.72,0.18 0,0 0.27,-0.08 0,0 0.9,-1.25 0,0 1.84,-1.75 0,0 1.36,2.29 1.37,1.61 0,0 -0.46,0.44 0.32,0.38 -0.05,5.85 -0.31,0.61 -0.77,0.69 -0.55,0.17 -0.24,0.52 1.34,0.94 0.46,0.88 -1.16,0.92 -0.75,0.21 -0.29,-0.19 -0.25,0.36 0,0 -4.55,-2.05 -5.82,-1.58 -0.79,-0.7 -2.44,-1.33 -1.34,0 -2.26,-1.39 -1.36,-0.35 -0.69,0.04 -3.14,1.05 -0.98,-0.5 -3.33,0.54 -1.05,0.27 -3.37,1.59 -1.67,-0.08 -1.29,0.35 -2.37,-1.1 0,0 0,0 0,0 -2.08,-1.59 -0.42,-0.08 -0.55,0.17 -2.6,2.31 -1.3,0.48 -0.36,-0.09 0,0 -1.04,-0.84 -1.86,-0.33 -0.12,-0.37 -0.76,-0.21 0.43,-0.47 -0.67,-0.23 0.13,-1.05 -0.19,-1.46 1.49,0.23 0.79,-2.2 4.28,-4.56 z"
                            id="tinh-tien-giang" data-id="tinh-tien-giang" data-link="{PROVINCE_LIST_807.link}"
                            data-tinh="{PROVINCE_LIST_807.title}" data-province="{PROVINCE_LIST_807.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 96.628471,701.45323 0.54,0.23 1.19,1.72 0.53,-0.1 0.85,0.7 1.159999,1.66 -0.11,0.34 0.19,0.54 -0.33,0.29 -0.07,0.64 0.4,1.45 -0.3,1.62 0.35,1.71 -0.55,1.57 -0.769999,0.74 -0.69,2.57 -0.58,0.63 -0.06,0.41 0.32,0.7 -0.56,0.73 -0.01,0.74 -0.39,0.38 0.28,0.49 0.23,-0.23 0.82,0.54 -0.03,0.42 -0.23,0.02 -0.17,0.35 0.2,0.67 -0.29,0.02 -0.19,0.37 0.62,1.06 0.29,-0.03 0.22,0.25 -0.01,0.23 -0.71,-0.58 -0.36,0.31 -0.33,-0.38 -0.32,-0.03 -0.45,0.42 -0.52,-1.85 -0.51,-0.2 -0.18,-1.94 -1.49,-6.93 -0.52,-1.33 -0.99,-1.11 -0.72,-1.27 0.18,-0.29 -0.23,0.22 -0.42,-0.26 -0.67,0.29 -0.99,-0.91 -0.53,-0.01 -0.17,0.27 -0.93,-2.81 -0.52,-0.38 0.3,-0.82 2.23,0.33 2.04,0.63 0.31,-0.08 0.75,-0.92 0.31,-1.19 1.05,-0.46 0.32,-1.54 0.59,-0.52 0.63,-0.09 z m 35.889999,-4.51 3.39,1.01 1.32,-0.03 0,0 0.23,0.78 -0.24,0.86 1,1.04 1.61,3.1 -0.12,0.12 2.02,2.64 8.03,1.99 5.53,3.38 4.19,3.67 1.13,-1.56 0.18,0.22 0,0 -0.25,0.4 3.73,3.29 0.3,-0.26 0.39,0.46 0.32,-0.31 1.86,2.09 0.2,0.98 0.4,0.48 8.72,8.08 0,0 0.37,0.35 -1.19,2.61 0.23,0.2 -3.38,1.91 -1.29,0.34 0.31,1.24 -0.58,0.27 0.03,0.22 -0.19,-0.12 -0.07,0.36 -0.19,-0.22 0.08,0.27 -0.28,-0.07 0.13,0.27 -0.28,-0.19 -0.06,0.28 0.22,0.09 -0.46,0.05 0.11,0.5 0.4,0.06 -0.14,0.5 -0.46,-0.06 -0.5,0.3 -0.15,0.47 -0.96,0.31 -0.8,0.9 -1.48,0.61 0.49,0.72 0.06,0.57 0.7,0 0.19,0.38 0.86,-0.2 -0.14,0.7 1.21,-0.05 0.69,0.46 -0.27,0.24 0.27,0.19 -0.39,1.1 -0.46,-0.17 -0.72,0.56 -0.15,0.97 0,0 -0.14,0.13 0,0 -0.03,0.34 0,0 -0.31,-0.24 -0.95,-0.01 0.12,-0.37 -0.29,-0.23 -0.05,-0.45 -0.65,0.7 -1.03,-0.42 -0.29,-1.09 -0.6,-0.44 -0.29,0.58 -0.01,1.35 0.47,0.82 0.89,0.62 -0.25,0.85 0.84,-0.13 -0.07,0.71 0.38,0.91 -0.51,0.15 0.19,0.42 -0.4,0.34 0,0.41 -0.3,-0.23 -0.25,0.2 -0.26,-0.16 -0.33,0.86 -0.15,1.52 0.19,1.41 0.61,1.11 0.24,0.1 0.44,1.52 -0.54,-0.1 -0.41,-0.43 -0.32,0.22 0,0 -0.76,-0.48 -0.95,-0.11 -13.46,-8.35 -0.51,0.98 -0.81,-0.23 -0.77,-0.62 -0.89,1.22 -1.52,-1.54 -1.82,3.54 -1.88,-1.86 -0.55,-0.07 0,0 1.19,-9.49 1.52,-5.99 0.4,-0.9 0.45,-0.39 1.27,0.26 1.49,-0.39 2.9,-3.11 1.47,-1.24 1.23,-0.66 0.87,0.05 0.61,1.69 0.29,-0.09 0.29,-0.6 0.18,-0.96 -1.19,-3.09 -0.37,-0.28 -4.39,-4.55 -1.75,0 -2.98,0.66 -0.9,-0.22 -1.08,-1.09 -0.84,-1.68 -1.39,-1.88 -1.2,-1.21 -1.52,-0.92 -1.7,-0.44 -1.28,0.53 -1.76,1.55 -1.34,1.75 -0.1,0.74 -0.22,0.16 -1.5,-0.58 -0.39,0.34 -0.36,-0.06 -0.12,-0.16 0.53,-0.78 -1.05,-1.31 0.36,-0.38 -0.62,-0.63 0.42,-0.52 -0.58,-2.1 -0.68,-1.04 -0.85,-0.76 -0.43,-0.1 -0.45,0.7 -0.22,0.03 0.16,-0.63 -0.33,-0.73 0.25,-0.35 -0.27,-0.7 -0.75,-1.14 -1.37,-1.43 -0.17,-0.2 -0.19,-0.29 -0.39,0 -0.64,0.69 -0.91,0.16 0.19,-0.44 -0.12,-0.52 0.48,-0.88 -0.66,-1.15 2.04,-0.42 1.1,1.24 1.06,-1.05 0.37,-1.04 0.81,-0.09 0.42,-0.43 0.42,-1.88 1.21,-0.61 0.29,-1.3 0.64,-0.22 0.85,0.3 1.53,-0.1 0.01,-0.56 2.04,0.11 z"
                            id="tinh-kien-giang" data-id="tinh-kien-giang" data-link="{PROVINCE_LIST_813.link}"
                            data-tinh="{PROVINCE_LIST_813.title}" data-province="{PROVINCE_LIST_813.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 202.57847,709.46323 -0.33,1.29 0.35,0.14 0.11,0.65 -0.2,0.08 0.19,0.36 0.97,0.33 2.45,0.22 1.18,0.6 0.83,0.72 0.12,0.56 0.41,0.38 0.85,0.49 0.46,0.61 0.62,-0.06 1.9,1.81 3.35,4.88 0,0 -1.49,-1.22 -1.03,-0.46 -0.7,0.63 0.07,0.58 -0.22,0.1 0.1,0.45 -0.22,0.25 0.46,0.07 0.05,0.99 -1.12,0.79 -0.37,-0.08 -0.45,-0.77 -0.25,-0.08 -2.07,0.45 -0.8,-0.34 -0.16,3.25 -1,1.24 -1.35,-1.11 -0.52,0.77 -1.12,-0.1 0.02,0.28 -0.5,0.26 -0.34,0.5 -0.53,-0.01 -0.4,-0.27 -0.37,0.45 0.09,0.41 -0.93,0.6 -0.6,0.11 -1.1,-0.93 -0.47,-0.06 1.34,2.01 0,0 -3.12,-2.47 -0.79,-0.09 -0.54,-0.34 0,0 -2.72,-2.4 0,0 -3.09,-4.39 -5.32,-3.5 0,0 0.18,-0.55 2.15,-3.16 -0.12,-0.11 1.17,0.51 0.89,1.01 0.53,-0.59 1.37,1.78 1.41,0.35 1.06,-0.07 0.13,-0.53 0.99,0.3 0.2,-0.63 -0.22,-0.12 0.32,-1.08 0.99,0.19 0.21,-0.32 0.72,-0.05 0.34,-0.97 -0.34,-0.76 -0.61,-0.46 0.03,-0.53 -0.59,0.03 -0.3,-0.72 -0.37,-0.29 0.85,-0.66 0,0 0.36,0.09 1.3,-0.48 2.6,-2.31 0.55,-0.17 0.42,0.08 z"
                            id="tinh-vinh-long" data-id="tinh-vinh-long" data-link="{PROVINCE_LIST_809.link}"
                            data-tinh="{PROVINCE_LIST_809.title}" data-province="{PROVINCE_LIST_809.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 202.57847,709.46323 2.38,1.1 1.28,-0.35 1.67,0.08 3.37,-1.58 1.05,-0.27 3.33,-0.54 0.98,0.49 3.14,-1.05 0.7,-0.03 1.36,0.35 2.26,1.39 1.34,-0.01 2.44,1.34 0.79,0.69 5.82,1.58 4.55,2.06 0,0 -0.28,0.4 1.48,0.88 1,0.28 0.71,0.9 -0.07,0.79 -0.4,0.26 0.46,1.18 -0.46,0.83 -1.82,2.1 -0.65,0.38 -0.23,-0.06 0.49,-0.87 -1.14,0.95 -0.89,0.22 -0.47,-0.14 -0.11,-0.13 0.35,-0.22 0.28,-0.61 -0.14,-0.51 -0.15,0.06 0.02,0.31 -0.94,0.82 0.67,1.5 -0.06,0.31 -0.79,0.7 -0.39,-0.04 -0.16,0.52 -0.85,0.99 -0.67,0.18 -0.62,-0.33 -0.51,0.04 -1.63,-0.85 -0.5,0.73 1.51,1.14 1.47,0.41 1.6,1.02 0.54,1.31 1.04,1.02 -0.19,0.7 -1.39,2 -0.35,0.09 -0.12,-0.36 -0.43,0.38 -0.35,0.95 -0.45,0.45 -0.9,0.08 -6.5,-6.38 -0.72,-0.5 -0.51,0.09 0,0 -2.22,-1.01 0,0 -1.26,-0.94 0,0 -0.95,-0.49 -1.13,-1.11 0,0 -1.6,-0.69 0,0 -0.86,-0.69 -0.92,-1.19 0,0 -3.35,-4.88 -1.91,-1.81 -0.62,0.06 -0.46,-0.6 -0.85,-0.49 -0.4,-0.38 -0.12,-0.56 -0.83,-0.72 -1.18,-0.6 -2.45,-0.22 -0.97,-0.33 -0.19,-0.35 0.2,-0.08 -0.11,-0.65 -0.35,-0.14 0.29,-1.35 z"
                            id="tinh-ben-tre" data-id="tinh-ben-tre" data-link="{PROVINCE_LIST_811.link}"
                            data-tinh="{PROVINCE_LIST_811.title}" data-province="{PROVINCE_LIST_811.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 199.85847,731.28323 -1.34,-2.01 0.47,0.06 1.1,0.93 0.6,-0.1 0.93,-0.61 -0.09,-0.4 0.37,-0.45 0.4,0.27 0.53,0.01 0.34,-0.5 0.5,-0.26 -0.02,-0.28 1.11,0.1 0.53,-0.77 1.35,1.11 1,-1.24 0.16,-3.25 0.8,0.34 2.07,-0.46 0.25,0.08 0.45,0.77 0.37,0.08 1.12,-0.79 -0.05,-0.99 -0.46,-0.07 0.22,-0.25 -0.1,-0.46 0.23,-0.1 -0.07,-0.58 0.7,-0.63 1.02,0.45 1.5,1.23 0,0 0.92,1.19 0.86,0.69 0,0 1.6,0.69 0,0 1.13,1.11 0.95,0.49 0,0 1.26,0.94 0,0 2.22,1.01 0,0 -0.04,0.27 1.45,1 1.75,2.14 1.24,1.03 -0.29,0.54 -0.94,0.35 -0.69,-1.06 -2.25,-2.19 -0.82,-0.52 -0.56,0.57 1.58,1.56 2.02,2.68 0.45,0.26 0.04,2.1 1.08,0.83 0.32,0 0.9,0.34 0.43,0.47 0,1.94 0.39,0.43 0.06,0.79 -0.9,2.73 -1.65,2.32 -1.36,1.04 -2.54,1.02 -2.05,0.51 -0.9,-0.17 -1.12,0.22 -0.34,-0.23 -0.16,-0.59 -0.18,-0.2 -1.04,-1.3 -0.94,-0.71 -1.99,-1.11 -0.44,0.46 0,0 -1.35,-0.76 -2.31,-1.97 -5.29,-5.45 -1.98,-1.23 -0.93,-1.25 -2.4,-2.36 -1.18,-1.85 z"
                            id="tinh-tra-vinh" data-id="tinh-tra-vinh" data-link="{PROVINCE_LIST_817.link}"
                            data-tinh="{PROVINCE_LIST_817.title}" data-province="{PROVINCE_LIST_817.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 195.40847,728.38323 0.54,0.34 0.79,0.09 3.12,2.47 0,0 1.18,1.86 2.4,2.36 0.92,1.25 1.98,1.23 5.29,5.45 2.31,1.97 1.35,0.76 0,0 -0.35,0.46 1.13,0.82 -0.35,1.67 -1.34,1.99 -1.07,0.61 -0.44,-0.14 -0.73,-1.56 -0.49,0.51 -0.12,2.82 -2.07,2.05 0.05,0.25 0.75,0.12 0.4,0.61 -0.11,1.38 -1.16,1.21 -1.46,0.6 -0.5,0.52 -1.65,-0.14 -1.92,0.56 -12.04,4.27 0,0 -0.44,-1.52 0.35,-0.2 -0.72,-3.28 2.42,-1.06 -0.31,-1.06 0.21,-0.2 -0.42,-0.28 0,0 -0.3,-0.79 -0.31,0.16 0,0 -0.24,-0.03 0,0 -0.05,0.14 0,0 0.19,0 -0.14,0.38 -0.59,-0.11 0.06,0.43 -0.4,0.57 0,0 -0.47,-0.07 0,0 -0.66,-0.08 -0.13,-0.35 -0.25,0.07 -0.38,-0.4 0,0 -0.07,-0.14 0,0 0.44,-0.39 -0.36,-0.09 -0.33,-0.48 -0.35,0.09 -0.19,-0.2 -0.22,0.22 -0.34,-0.07 0.04,0.22 -0.3,0.02 -0.22,0.31 -0.24,-0.07 0.02,0.34 -0.47,-0.03 -0.25,0.28 -0.8,-0.22 -0.14,0.86 -1.05,-0.01 -0.64,-0.65 -0.87,0.28 -0.34,-0.12 -0.12,-0.4 -0.56,-0.06 0.22,-0.31 -0.12,-0.36 -0.96,-0.29 -0.26,0.43 -0.83,-0.91 -0.12,0.2 -0.44,-0.25 -0.42,0.73 -1.83,-2.07 -0.08,-1.05 0.63,-4.72 -0.51,-1.34 0,0 0.06,-0.56 0.52,0.03 0.28,-0.53 0.35,0.16 0.36,-0.61 0.58,0.12 0.41,0.5 0.02,-0.3 0.59,-0.59 0.45,-0.11 0.1,0.35 0.93,-0.18 0.6,-0.34 0.14,-0.47 0.54,-0.06 0.39,-0.33 0.1,0.45 2.12,-1.85 6.93,-4.22 0.62,-2.95 -0.34,-0.09 0,0 0.22,-0.9 0,0 0.97,-2.98 -0.15,-0.54 0.27,-0.5 -0.05,-0.41 0.41,-0.52 0,0 0.13,-0.32 0,0 z"
                            id="tinh-soc-trang" data-id="tinh-soc-trang" data-link="{PROVINCE_LIST_819.link}"
                            data-tinh="{PROVINCE_LIST_819.title}" data-province="{PROVINCE_LIST_819.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 176.38847,43.033229 0.49,-0.19 -0.01,-0.8 0.3,-0.34 1.21,-0.49 0.6,0.09 0.31,-0.66 0.53,0.35 0.45,-0.01 -0.16,-0.97 0.4,-0.33 0.45,-0.08 0.08,-0.23 1.46,0.08 0.82,-1.41 0.48,-0.09 0.98,0.41 0.25,-0.63 0.29,0.5 0.5,-0.67 0.54,0.1 0.29,-0.44 0.65,-0.14 0.56,0.77 0.4,0.02 0.05,0.34 -0.37,0.45 0.37,0.79 0.96,0.36 -0.2,0.43 -1.2,1.08 -0.58,1.26 -0.58,0.25 0.01,0.38 0.61,0.22 0.05,0.39 0.63,0.69 -0.43,0.77 0.31,0.44 0.76,-0.09 0.59,0.59 0.08,0.28 -0.43,0.4 0.14,0.33 -0.28,0.22 0.3,0.09 -0.1,0.22 0.75,-0.07 0.9,1.5 0.89,0.25 0.54,-0.37 0.07,0.59 0.38,0.03 1.52,-0.31 0.3,0.38 1.18,0.41 0.33,0.65 0.37,-0.01 0.74,-1.43 0.4,-0.36 0.73,-0.04 0.47,0.29 0.52,-0.27 0.25,-0.46 1.7,-0.69 0.21,-0.36 1.71,-0.46 0.45,-0.84 0.95,0.46 0.79,-0.2 0.05,0.32 -0.42,0.38 -0.01,0.43 0.66,0.03 0.44,0.31 -0.12,0.55 0.42,0.13 -0.31,0.31 0.37,0.41 -0.07,0.34 0.97,0.36 -0.23,1.48 0.21,0.61 -1.32,1.57 0.38,0.52 -0.07,1.16 0.29,0.7 0.23,0.14 0.61,-0.18 0.92,0.43 0.97,-0.86 0.48,0.13 0.03,0.3 0.51,0.06 -0.04,0.5 0.38,0.12 0,0 0.07,0.53 1.11,0.64 0.48,0.65 0.07,0.51 -0.54,0.55 0,0 -0.16,0.31 0,0 -0.32,1.04 0.27,0.65 0.37,0.63 0.25,0.15 0.21,-0.14 0.2,0.43 0.53,0.21 0.53,0.66 0.1,0.59 -0.49,1.94 -0.3,0.08 -0.55,0.73 0,0.56 0.65,1.04 -0.41,0.51 -0.48,0.18 -1.04,1.3 -0.27,-0.05 -0.54,0.83 0.22,0.64 -0.45,0.4 -0.38,-0.06 -0.34,0.5 -0.29,1.17 -1.76,-0.2 -0.47,0.69 -0.32,-0.19 -0.41,0.26 0.13,1.48 -0.38,0.49 0.81,1.22 -0.18,0.93 0.35,0.23 0.06,0.87 -0.17,0.86 -0.41,0.09 0,0 -0.29,0.1 0,0 0,0.44 0.68,0.07 -0.69,0.69 0,0 -0.28,-0.24 -1.76,0.02 -0.35,-0.27 -0.31,-0.78 -0.88,-0.42 -0.9,1.69 -0.61,0.27 -0.68,-0.26 0,0 -0.57,-0.11 0,0 -0.34,0.24 0.04,0.57 -0.55,0.19 -0.04,0.44 -0.68,0.61 -0.86,0.38 -1.01,0.03 -1.5,2.04 -0.41,0.16 -0.58,-0.17 0,0 0.11,0.39 -0.21,0.33 0,0 -2.39,0.12 -0.55,0.72 -0.24,1.44 -0.68,0.04 -0.38,-0.36 -0.6,-0.12 -0.38,-0.34 0.1,-0.36 -0.23,-0.34 0.62,-1.51 -0.08,-0.5 0.26,-0.45 -0.16,-0.53 0.57,-0.24 -0.44,-0.38 -0.07,-0.75 -0.37,-0.27 0.71,-1.12 -0.21,-0.25 0.07,-0.78 -0.76,-0.85 0.16,-0.96 -0.4,-0.36 -0.23,-1.17 -0.63,-0.33 -0.41,0.2 -0.5,-0.51 -0.77,0.09 -0.67,-0.85 -1.32,-0.53 -0.37,1.02 -0.46,0.33 -0.64,-0.2 -0.23,0.45 -0.81,0.03 -0.45,0.37 0,0 -0.4,-0.03 0,0 -0.6,0.1 -0.34,0.51 -0.44,1.48 0.18,0.34 -0.46,0.13 0.16,0.49 -1.03,0.01 0,0 -0.22,-0.29 -0.98,-0.28 -0.4,-0.77 -0.44,-0.31 -0.22,0.15 -0.05,-0.3 -0.32,-0.14 0.04,-0.86 -0.6,-0.2 -0.35,-2.02 -0.65,-0.6 -0.07,-0.54 -0.56,-0.36 0.34,-0.37 -0.07,-0.47 0.35,-0.44 -0.52,-0.37 -0.1,-0.63 0.34,-0.54 0.3,-0.09 -0.03,-0.59 0.38,-0.51 0.6,-0.25 -0.01,-0.39 -0.89,-0.34 -0.37,0.15 -0.35,-0.46 0.09,-0.67 -0.21,-0.46 -0.2,0.15 -0.31,-0.22 -1,-1.43 0.19,-0.05 0.05,-0.59 0.29,-0.14 -0.27,-0.51 0.14,-0.85 -0.16,-0.24 0.94,-0.52 0.49,0.19 0.5,-0.48 -0.66,-0.58 1.66,-0.54 -0.68,-0.83 0.19,-0.48 0.78,-0.18 -0.07,-1.18 0.93,-0.41 0.15,-1 -0.18,-0.54 0.33,-0.76 -0.08,-0.6 0.9,-0.4 1.45,0.21 0.4,-0.86 -0.61,0.51 -0.34,-0.1 -0.14,-0.49 0.21,-0.42 -0.29,-0.53 0.39,-0.03 0.19,-1.36 2,-1.49 -0.35,-1.07 0.33,-0.81 -0.36,-1.15 -0.68,-0.89 -0.77,-0.15 -0.23,-0.62 -0.36,-0.18 -0.28,-0.69 0.13,-0.61 -1.04,-1.12 z"
                            id="tinh-bac-kan" data-id="tinh-bac-kan" data-link="{PROVINCE_LIST_207.link}"
                            data-tinh="{PROVINCE_LIST_207.title}" data-province="{PROVINCE_LIST_207.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 195.15847,115.91323 -0.61,-0.65 0,0 0.34,-0.46 0,0 0.55,0.19 0.55,-0.38 -0.02,-0.15 -0.67,0.03 0.88,-1.36 0.26,0.23 1.22,-0.03 0.25,-1.02 -0.27,-0.41 0.68,-0.45 0.07,0.31 0.22,-0.03 -0.21,-0.64 0.33,-1.29 0.2,0.02 0.71,1.23 0.32,0.18 0.32,-0.12 0.24,0.46 0.53,-0.11 0.6,0.19 0.15,0.42 0.47,0.07 -0.1,-0.27 0.36,-0.5 -0.03,-0.31 -0.43,-1 0.34,-0.35 0.84,-0.19 0.17,-1.06 -0.16,-0.03 0,0 -0.21,-0.15 0,0 -0.19,-0.42 0.45,-0.47 -0.6,-0.58 0.36,-1.15 0,0 -0.13,-0.19 0,0 0.61,-1.35 -0.13,-0.36 0.17,-0.31 -0.35,-0.45 -0.88,-0.21 0.13,-0.5 0.66,-0.47 0,0 0.16,-0.07 0,0 0.42,-1.2 0.52,-0.1 0.59,-0.750001 1.1,-0.37 1.43,0.5 0,0 0.51,-0.22 0,0 0.91,-0.23 0,0 0.41,-0.05 0,0 0.6,0.840001 0.57,0.31 0,0 -0.82,0.52 -0.05,4.56 2.19,0.87 1.4,0.12 0.38,0.72 0.26,-0.31 0.37,0.03 0,0 0.23,-0.2 0,0 0.68,-0.17 0.72,0.13 -0.29,1.02 0.21,0.27 -0.14,0.87 0.32,-0.19 -0.07,0.36 0.35,0.34 0.16,-0.18 0.39,0.22 -0.09,-0.31 0.33,-0.21 0.29,0.41 0.64,-0.14 0.22,0.63 -0.44,1.23 0.27,0.31 0,0 0.3,0.2 0,0 0.29,0.39 0.29,-0.21 0.27,0.3 0.2,-0.09 0,0 0.35,0.08 0,0 0.25,-0.18 0.55,0.3 0.52,-0.2 0,0 0.41,-0.44 0.26,0 0,0 0.74,-0.12 0.04,-0.47 -0.53,-0.59 0.68,-0.6 0.75,-0.03 0.31,-0.32 0.31,0.41 0.45,-0.17 0.57,0.13 0.48,-0.6 0.49,-0.06 0.62,-1.87 0.79,-0.24 0.61,-0.73 -0.71,-1.37 0.74,-0.63 0.3,-1.26 0.75,-0.65 0.77,-1.29 0.4,-0.09 0.33,-0.41 1.02,0.22 1.29,-0.52 1.05,0.4 0.48,-0.32 0.57,0.17 0.75,-0.31 1.69,0.88 -0.15,1.21 0.54,0.59 0.84,0.3 0.37,0.57 0.48,0.18 0.67,0.06 0.15,-0.2 0,-0.48 0.16,-0.06 -0.3,-0.42 0.21,-0.48 1.39,-0.37 0.34,0.59 0.68,0.39 0.81,-0.18 1.13,0.16 0.37,1.01 0.76,0.47 -0.3,0.6 -0.36,-0.17 -0.12,0.48 -0.54,0.25 0.29,0.97 0.34,-0.28 0.07,0.24 0.78,-0.1 1.08,0.39 0.14,0.74 0.52,0.36 -0.5,0.6 -0.05,0.45 1.04,1.18 0.28,0.01 0.5,-0.58 0.19,0.23 -0.07,0.61 0.83,-0.51 0.46,0.64 0.81,-0.72 0.91,-0.2 0.28,0.54 0.63,0.47 -0.68,0.43 0.03,0.33 -0.31,0.37 0.4,1.33 -0.29,0.98 1.59,0.82 0.22,0.73 0.52,0.35 0,0 0.08,0.57 -0.71,0.88 -0.49,0.25 -1.19,0.07 -0.79,0.51 0.19,0.77 0.38,0.25 0.09,0.4 -0.42,1.07 0.07,0.69 -0.49,0.11 -0.66,1.05 -0.04,0.83 -0.41,0.13 -0.25,0.8 -0.37,0.3 -1.02,-0.86 -1.23,0.29 -0.51,-0.24 -0.5,0.53 -0.62,-0.14 -0.31,-0.68 -1.5,0.02 -0.02,1.34 0.39,0.64 -0.76,-0.2 -1.34,0.39 -1.23,-0.17 -0.27,-0.22 -1.04,0.01 -0.59,-0.29 -0.59,0.11 -1.25,-0.6 -0.82,0.17 -0.16,-0.2 -1.39,-0.2 -0.57,-0.36 -0.16,0.68 -0.33,-0.2 -1.13,0.06 -0.23,-0.48 -0.87,0.07 0.09,-0.32 -0.43,-0.43 -0.8,0.18 -0.52,-0.32 -0.71,0.14 -0.78,-1.71 -0.45,-0.15 -0.62,0.22 -0.31,-0.16 0,0 -0.49,-0.2 -0.18,-0.5 -0.12,0.45 -0.64,-0.07 -0.42,0.22 -0.4,-0.17 0,0 -0.16,-0.2 -0.17,0.13 0,0 -1.46,-0.01 -0.08,0.4 -0.29,0.05 -0.23,0.83 -2,0.68 -0.17,0.77 -0.34,0.13 0.12,0.31 -1.15,0.01 0.06,0.45 -1.18,-1.11 0.09,-0.37 -0.58,-0.05 -0.21,0.72 0.69,1.74 -0.47,0.95 -0.76,0.52 0,0 -0.48,-0.45 -0.92,-0.16 -0.33,-0.7 -0.48,-0.33 0.07,-0.73 -0.19,-0.32 -0.64,0.11 -0.71,0.52 -0.71,0.02 0.31,-0.91 -1.28,-0.37 -0.9,0.29 -0.43,-0.27 -0.94,-1.5 -0.61,-0.34 -0.64,0.01 -0.37,0.93 -1.62,-0.43 -0.24,-0.2 -0.07,-0.54 -0.53,0.08 -0.99,-0.57 -0.65,0.7 0.24,0.6 -0.31,0.12 -0.35,-0.76 0.08,-1.65 -1.29,-0.76 -0.6,0.02 -0.33,-0.29 -0.33,0.55 0.24,0.95 -0.54,0.3 -0.24,0.47 -0.62,-0.4 -0.38,0.17 -0.27,-0.43 -0.58,0.09 -0.27,-0.27 0.11,-0.28 0,0 0.05,-0.21 -0.83,-1.31 0.01,-0.91 0.44,-0.38 -0.82,-0.16 0,0 0.06,-0.49 0,0 -0.41,0.22 -0.43,-0.14 -0.18,-0.25 0.11,-0.89 z"
                            id="tinh-bac-giang" data-id="tinh-bac-giang" data-link="{PROVINCE_LIST_221.link}"
                            data-tinh="{PROVINCE_LIST_221.title}" data-province="{PROVINCE_LIST_221.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 164.10847,757.22323 0.32,-0.22 0.41,0.44 0.54,0.1 -0.44,-1.51 -0.24,-0.1 -0.6,-1.11 -0.19,-1.42 0.15,-1.52 0.33,-0.86 0.26,0.16 0.25,-0.19 0.3,0.23 0,-0.41 0.39,-0.34 -0.18,-0.42 0.51,-0.15 -0.38,-0.91 0.07,-0.71 -0.84,0.13 0.26,-0.84 -0.9,-0.62 -0.47,-0.82 0.01,-1.35 0.29,-0.58 0.6,0.44 0.29,1.09 1.03,0.42 0.64,-0.7 0.05,0.45 0.3,0.23 -0.12,0.37 0.95,0.01 0.32,0.25 0,0 0.02,-0.34 0,0 0.15,-0.13 0,0 0.38,0.58 0.44,-0.24 0.33,0.1 0.17,-0.52 0.56,0.03 0.31,-0.23 0.11,0.49 0.54,-0.12 0.09,-0.3 0.56,0.3 0.31,-0.1 0.19,0.52 0.34,-0.18 0.3,0.18 0.43,-0.53 0.37,0.36 -0.12,0.29 0.71,0.13 0.22,-0.46 0.56,-0.02 0.01,0.26 0.42,0.11 0.1,-0.28 0.35,0.03 0.01,-0.3 0.7,-0.13 0.58,0.58 0,0 0.51,1.34 -0.63,4.72 0.08,1.05 1.83,2.07 0.42,-0.73 0.44,0.25 0.12,-0.2 0.83,0.91 0.26,-0.43 0.96,0.29 0.12,0.36 -0.22,0.31 0.56,0.06 0.12,0.4 0.34,0.12 0.87,-0.28 0.64,0.65 1.05,0.01 0.14,-0.86 0.8,0.22 0.25,-0.28 0.47,0.03 -0.02,-0.34 0.24,0.07 0.22,-0.31 0.3,-0.02 -0.04,-0.22 0.34,0.07 0.22,-0.22 0.19,0.2 0.35,-0.09 0.33,0.48 0.36,0.09 -0.44,0.39 0,0 0.07,0.14 0,0 0.38,0.4 0,0 0,0 0,0 0.25,-0.07 0.13,0.35 0.66,0.08 0,0 0.47,0.07 0,0 0.4,-0.57 -0.06,-0.43 0.59,0.11 0.14,-0.38 -0.19,0 0,0 0.05,-0.14 0,0 0.24,0.03 0,0 0.31,-0.15 0.3,0.78 0,0 0.42,0.28 -0.21,0.2 0.31,1.06 -2.42,1.06 0.72,3.28 -0.35,0.2 0.44,1.52 0,0 -2.04,0.9 -2.48,1.48 -0.74,0.14 -10.05,4.25 -2.76,1.72 -1.2,1.1 -1.69,2.12 -0.18,0.09 -0.22,-0.41 0,0 0.08,-0.6 -0.42,-0.32 -0.01,-0.4 -0.74,-0.48 -0.18,-0.35 -0.78,0.51 -1.04,-0.28 -0.05,-0.43 -0.83,-0.07 -0.33,0.31 -0.85,-0.28 -1.06,-0.93 -1.23,0.25 -0.58,-0.51 -0.5,0.17 -0.43,-0.48 0.28,-0.21 -0.18,-0.42 0.29,-1.19 0.49,-0.33 0.24,-0.94 0.71,-0.31 -0.3,-0.9 -0.68,0.23 -0.44,-1.06 -0.67,-0.11 -0.01,-0.44 -0.38,-0.25 0.01,-0.55 0.38,-0.08 0.94,-1.34 1.87,-1.48 -1.89,-1.81 -0.49,-1.07 z"
                            id="tinh-bac-lieu" data-id="tinh-bac-lieu" data-link="{PROVINCE_LIST_821.link}"
                            data-tinh="{PROVINCE_LIST_821.title}" data-province="{PROVINCE_LIST_821.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 197.52847,120.52323 -0.11,0.28 0.27,0.27 0.58,-0.09 0.27,0.43 0.38,-0.17 0.62,0.4 0.24,-0.47 0.54,-0.3 -0.24,-0.95 0.33,-0.55 0.33,0.29 0.6,-0.02 1.29,0.76 -0.08,1.65 0.35,0.76 0.31,-0.12 -0.24,-0.6 0.65,-0.7 0.99,0.57 0.53,-0.08 0.07,0.54 0.24,0.2 1.62,0.43 0.37,-0.93 0.64,-0.01 0.61,0.34 0.94,1.5 0.43,0.27 0.9,-0.29 1.28,0.37 -0.31,0.91 0.71,-0.02 0.71,-0.52 0.64,-0.11 0.19,0.32 -0.07,0.73 0.48,0.33 0.33,0.7 0.92,0.16 0.48,0.45 0,0 -0.19,0.85 0.43,1.92 0.42,0.75 -0.95,1.11 0.08,0.89 -1.04,0.91 -0.12,0.78 -0.31,0.22 -1.05,-0.54 -0.1,0.38 -1.23,-0.28 -0.02,0.33 -1.5,0.6 -0.23,-0.1 -1.04,0.65 -0.22,-0.36 0.28,-0.62 -0.94,-0.11 -0.66,-0.69 -0.31,0.12 -0.24,0.85 0,0 -0.63,0.02 0.03,-0.7 -0.34,-0.21 -0.35,0.14 -0.64,-0.54 -0.69,0.64 -0.52,-0.3 -0.47,0.03 -0.36,-0.38 -1.11,0.78 -0.45,-0.1 -0.2,-0.42 -0.3,0.22 -0.53,-0.08 0,0 -0.13,-0.36 0.6,-0.26 -0.28,-0.02 -0.03,-0.55 0.27,-0.17 -0.13,-0.22 0.27,-0.07 0.41,-0.63 -0.33,-0.81 -0.35,-0.11 0.07,-0.38 -0.11,0.09 -0.37,-0.63 -0.3,0.3 -1.32,-0.18 -0.31,-0.31 -0.22,-0.74 -0.91,0.41 -0.3,-1.22 -0.68,-0.61 0.13,-0.21 -0.42,0.1 -0.15,-0.38 0.46,-0.53 0.14,0.11 0.1,-0.41 -0.35,-0.8 0.18,-0.37 -0.32,-0.37 0.35,-0.8 -0.53,-0.49 -0.33,-0.03 0.44,-1.31 -0.25,-0.48 -0.28,0.12 -0.07,-0.16 0.13,-0.38 0.67,0.05 0.73,-0.78 z"
                            id="tinh-bac-ninh" data-id="tinh-bac-ninh" data-link="{PROVINCE_LIST_223.link}"
                            data-tinh="{PROVINCE_LIST_223.title}" data-province="{PROVINCE_LIST_223.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 238.34847,650.39323 0.2,-0.04 0,0 0.04,-0.44 0.72,-0.03 0.08,-0.4 0.77,-0.53 -0.32,-0.91 0.18,-0.44 1.91,-0.81 0.31,0.06 0.3,0.36 0.18,1.66 -0.95,1.04 -0.82,0.29 -0.12,0.45 0,0.7 0.24,0.26 0.57,0.08 0,0.94 0.71,-0.05 0.6,0.31 0.74,-0.41 0.8,0.45 0.78,-0.06 0.15,0.76 -0.2,0.05 0.06,0.36 0.24,0.21 -0.2,0.12 0.35,0.33 -0.37,0.58 0.04,0.84 0.49,-0.05 -0.06,0.2 0.52,0.1 -0.29,-0.61 0.13,-0.32 0.72,-0.76 0.89,0 0.22,-0.64 0.5,-0.16 0.11,0.19 0.25,-0.12 0.81,0.19 0.71,1.21 -0.76,1.03 0,0 0.1,0.56 -0.14,0.02 0.22,0.17 -0.44,0.31 -0.13,0.76 -0.31,0.09 0.17,0.33 -0.21,0.03 0.17,0.18 -0.04,0.71 0.26,0.14 -1.08,0.17 0.05,0.28 0.35,0.02 -0.27,0.2 0.24,0.62 -0.15,0.29 -0.72,0.29 0.29,0.28 0,0.58 0.67,-0.04 0.19,0.33 0.52,-0.12 0.31,0.55 0.34,0.14 -0.23,0.47 0.63,0.19 0.14,0.6 0.52,0.25 -0.02,0.26 -0.6,0.1 0.15,0.35 -0.19,0.28 0.59,1.11 0,0 -0.38,0.06 0,0 0.09,0.33 -0.65,0.62 -0.1,0.56 0.39,0.33 -0.23,0.65 -0.35,0.13 -0.48,-0.4 -0.49,0.45 0.04,0.43 -0.56,0.38 -0.22,0.96 -0.71,-0.27 -1.09,0.41 -0.26,-0.09 -0.53,-0.57 -0.97,-0.38 -0.66,-1.33 -1.46,0.43 0.51,0.62 0.21,0.7 -0.24,0.29 -1.2,0.42 -0.55,0.65 0.4,0.99 0.72,0.79 -1.08,0.66 -0.21,-0.32 -0.41,0.03 -0.03,0.62 0.42,0.07 0,0.47 1.09,0.7 -0.19,0.4 1.11,0.13 0.36,0.86 0.79,-0.19 0.43,0.44 0,0 -0.73,0.16 -0.15,0.42 -0.76,0.67 -1.04,0.15 -0.02,0.25 -0.41,-0.05 0.02,-0.68 -0.66,-0.68 -0.43,0.65 0.09,0.78 -0.49,0.17 -0.3,-0.43 0.32,-0.17 -0.09,-0.16 -0.83,-0.13 0.2,-0.31 -0.35,0.04 0.08,-0.29 -0.33,0.04 -0.48,-0.31 -0.25,0.35 0.23,0.09 0.02,0.35 -0.29,0.19 0.47,0.07 0.12,0.23 -0.42,-0.04 -0.12,0.37 -0.64,-0.12 -0.58,-0.66 0.25,-0.89 -0.44,-1.16 -0.28,-0.23 -0.73,0.13 -0.82,-0.61 -0.15,-2.5 -0.55,0.16 -0.64,-0.19 -0.46,-0.46 0.18,-1 -1.14,-0.38 -0.12,-0.69 0.41,-0.55 -0.21,-0.3 -0.65,0.47 -0.74,-0.96 -0.3,0.51 -0.81,-0.05 -0.95,-0.7 -0.48,-0.77 0.52,-0.88 -0.59,0.02 -0.32,-0.53 0.52,-0.3 0.03,-0.78 -0.57,-0.25 -0.44,-0.88 -1.85,-0.02 -0.26,-0.24 0.21,-0.38 -0.39,-0.27 -0.6,0.13 -0.01,0.32 0.3,-0.05 -0.02,0.48 0,0 -0.3,0.37 0,0 -0.2,0.04 0,0 -0.93,-0.73 0.39,-0.5 -0.25,-0.75 -1.14,0.02 0.02,-0.59 -0.3,-0.25 -0.24,0.34 -0.64,-0.21 -0.22,-0.44 0.28,-0.97 -0.49,-0.31 0.2,-0.53 -0.25,-0.02 -0.41,-0.84 -0.5,0.34 -0.53,-0.54 0.68,-0.64 -0.72,-0.13 0.23,-1.33 -0.13,0.15 -0.29,-0.22 -0.18,0.35 -0.15,-0.2 0.18,-0.46 -0.26,-0.11 0.04,0.3 -0.23,-0.19 0.5,-1.5 -0.96,-1.02 0.45,-1.11 0.54,0.03 -0.04,-0.66 0.1,-0.19 0.17,0.15 -0.08,-0.59 0.54,-0.56 -0.01,-1.16 0.47,-0.74 -0.17,-0.38 0.34,-0.31 -0.09,-0.15 0.17,-0.24 0.1,0.19 0.42,-0.07 0.3,-0.32 0.13,0.12 0.74,-0.31 -0.12,-0.21 0.5,-0.15 -0.04,-0.27 0.33,-0.1 -0.17,-0.09 0.54,-0.85 0,0 0.53,-0.34 2.11,0.26 0.75,-0.23 1.29,1.1 0.58,0.14 0.77,0.59 0.32,-0.05 0.47,-0.57 0.83,1.09 0,0 -0.01,0.32 -0.66,0.63 0,0 -0.48,0.13 -0.43,0.66 0.38,0.98 -0.22,0.73 0,0 -0.18,0.33 0,0 0.69,0.71 0,0 0.69,0.1 0.3,-0.29 0,0 1.52,-0.55 0.54,0 0.19,1.24 2.01,0.3 0,0 0.33,0.06 0,0 0.66,0.01 0,0 0.16,-0.45 0,0 0.1,-0.69 -0.16,-0.19 0.17,-0.16 -0.31,-0.87 0.74,0.3 0.53,-0.24 0.38,0.26 0.73,-1.01 -0.03,-0.38 0,0 z"
                            id="tinh-binh-duong" data-id="tinh-binh-duong" data-link="{PROVINCE_LIST_711.link}"
                            data-tinh="{PROVINCE_LIST_711.title}" data-province="{PROVINCE_LIST_711.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 249.57847,656.38323 0.77,-1.03 -0.72,-1.21 -0.81,-0.19 -0.25,0.13 -0.11,-0.2 -0.5,0.17 -0.22,0.64 -0.89,0 -0.72,0.76 -0.13,0.32 0.29,0.61 -0.52,-0.09 0.06,-0.21 -0.49,0.05 -0.04,-0.83 0.37,-0.58 -0.35,-0.34 0.21,-0.12 -0.24,-0.2 -0.06,-0.36 0.21,-0.06 -0.16,-0.76 -0.78,0.06 -0.8,-0.44 -0.74,0.4 -0.6,-0.31 -0.71,0.05 0,-0.94 -0.57,-0.07 -0.24,-0.26 0,-0.71 0.12,-0.45 0.82,-0.28 0.95,-1.04 -0.17,-1.66 -0.3,-0.36 -0.31,-0.06 -1.91,0.8 -0.18,0.44 0.33,0.91 -0.77,0.53 -0.07,0.4 -0.72,0.03 -0.04,0.44 0,0 -0.4,0.09 0,0 0.04,0.38 -0.74,1.01 -0.38,-0.26 -0.53,0.24 -0.73,-0.3 0.3,0.87 -0.17,0.15 0.17,0.19 -0.1,0.69 0,0 -0.16,0.45 0,0 -0.67,0 0,0 -0.32,-0.07 0,0 -2.01,-0.29 -0.19,-1.24 -0.55,-0.01 -1.52,0.55 0,0 -0.49,0.32 -0.5,-0.13 0,0 -0.68,-0.71 0,0 0.18,-0.33 0,0 0.22,-0.73 -0.38,-0.98 0.43,-0.66 0.48,-0.13 0,0 0.6,-0.52 0.08,-0.43 0,0 -0.83,-1.1 -0.47,0.58 -0.32,0.05 -0.77,-0.59 -0.58,-0.14 -1.29,-1.1 -0.75,0.23 -2.11,-0.26 -0.53,0.34 0,0 -0.13,-0.66 0.37,-0.16 0.17,-1.57 0.58,-0.35 0.55,0.3 0.99,-0.27 1,-1.23 -0.34,-0.46 0.25,-0.09 -0.4,-0.82 0.2,-0.45 -0.55,-0.36 0.05,-0.16 -0.4,-0.01 -0.29,-0.5 0.05,-0.84 -0.32,-0.3 0.04,-0.31 -0.3,-0.15 0.11,-0.21 -0.23,-0.14 -0.06,-0.51 0,0 0.18,-1.07 -0.81,-0.8 -1.22,-3.18 1.04,-2.23 0.7,-0.84 -0.5,-0.84 0.11,-0.92 0.44,-0.47 0.96,-0.06 -2.86,-5.61 1.05,0.06 0.49,-0.72 0.71,-0.13 0.5,0.34 0.2,0.47 1.09,0.23 1.44,-0.57 0.98,0.27 -0.35,0.37 5.1,-0.31 0.59,-0.01 0.35,0.45 0.75,0 -0.54,-0.83 0.38,-0.41 1.04,0.6 0.4,0.68 1.85,-0.26 1.47,-2.07 -0.04,-0.57 0.47,-0.27 0.23,-0.66 0.44,-0.62 0.7,-0.86 -0.02,-0.35 0.62,-0.52 1.03,0.17 0.5,-0.21 0.61,0.6 0.56,-0.13 0.32,0.36 1.7,-0.3 0.72,0.38 0.7,-0.04 0.24,0.25 1.48,-0.27 0.31,-0.37 2.22,-0.69 0.36,-0.38 0.46,-0.42 -0.32,-0.32 0.12,-0.37 0.49,-0.29 0.7,-0.01 0.54,-0.56 0.53,-0.35 0.37,-0.28 -0.12,-0.3 0.34,-0.64 0.22,0.19 0.82,-0.29 0.03,-0.35 -0.48,-0.37 0.23,-0.48 0.92,0.27 0.24,-0.93 0.52,-0.76 0.65,-0.57 0.96,-0.3 -0.11,-0.79 0.41,-1.02 0.34,-0.65 0.82,-0.12 0.93,-0.47 0.27,-0.42 0.8,-0.05 0,0 1.12,7.74 0.01,0.78 -0.3,0.44 0.3,0.58 0.64,0.1 -0.51,1.53 0.09,3.05 0.55,-0.63 0.17,0.27 -0.16,0.44 0.34,-0.05 0.18,0.34 0.48,0.03 0.35,0.33 0.1,-0.18 0.3,0.19 0.11,-0.24 0.12,0.28 0.18,-0.29 0.34,0.1 0.08,-0.26 0.22,0.31 -0.08,0.31 1.54,0.29 0.81,0.79 0.28,0.79 0.33,0.28 0.08,0.73 0.25,0.12 -0.26,0.25 0.26,0.39 -0.26,0.07 1.55,0.86 1.09,1.75 0.24,1.13 0.63,0.09 0.39,0.32 0.06,0.77 -0.29,0 -0.28,0.39 0.06,0.29 -0.17,-0.09 -0.05,0.38 -0.42,0.46 0.19,0.67 -0.37,0.36 -0.21,-0.15 -0.19,0.29 0.28,0.9 -0.52,0.67 0,0 -0.19,-0.31 -0.78,0.15 -0.48,0.47 -0.33,-0.05 -0.18,0.47 -1.37,0.17 -0.58,0.46 -0.71,0.07 -0.26,0.53 -0.36,0 -0.2,0.25 -0.09,1.39 -0.28,0.32 0.34,0.31 -0.99,1.34 0.38,0.61 -0.24,0.36 0.15,0.33 0.84,0.29 0.88,0.71 -0.19,0.59 0.31,0.4 -0.49,0.74 0.17,0.38 0.59,0.34 -0.1,0.16 -0.4,-0.13 0.42,0.84 1.17,0.05 0.21,0.42 0,0 -0.75,0.34 -9.65,2.07 -0.85,1.01 -1.24,0.15 -0.06,0.62 -1.04,0.7 -0.41,0.76 0,0.66 0.24,0.03 0.28,0.51 -0.41,0.29 0.02,0.44 -0.67,0.47 0.27,0.32 -0.21,0.44 0.2,0.18 -0.41,0.22 -0.61,-0.17 0.05,0.2 -0.36,0.09 0.31,0.2 -0.3,0.04 0.13,0.27 -0.21,0.26 -0.21,-0.03 0.02,0.2 -0.3,-0.11 -0.43,0.38 -0.33,-0.05 0.16,0.42 -0.55,0.11 -0.02,0.34 -0.21,-0.03 0.09,0.24 -0.17,-0.09 -0.14,0.2 -0.09,-0.3 -0.39,0.19 0.01,-0.18 -0.4,-0.01 0.37,0.47 -0.1,0.53 -0.85,-0.25 0.05,0.17 -0.52,0.22 -0.01,0.46 -0.49,-0.33 -0.75,-0.18 z"
                            id="tinh-binh-phuoc" data-id="tinh-binh-phuoc" data-link="{PROVINCE_LIST_707.link}"
                            data-tinh="{PROVINCE_LIST_707.title}" data-province="{PROVINCE_LIST_707.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 144.74847,793.45323 -3.69,0.06 -1.11,-1.15 -0.39,-1 2.66,-0.65 0.65,-1.6 0.7,-0.44 0.29,-1.25 -0.17,-0.07 -0.27,0.59 -0.52,0.28 -3.92,1.26 -1.1,-0.97 -0.55,-1.34 1.02,-5.15 0.11,-5.68 0.05,-1.7 0.27,-3.54 -0.06,-2.88 0.66,-3.74 0.77,-14.75 0.55,0.07 1.89,1.86 1.82,-3.54 1.53,1.54 0.89,-1.22 0.77,0.62 0.81,0.23 0.51,-0.98 13.46,8.36 0.95,0.11 0.76,0.48 0,0 -2.43,2.79 0.49,1.08 1.89,1.8 -1.87,1.49 -0.94,1.33 -0.37,0.09 -0.01,0.55 0.39,0.25 0.01,0.45 0.67,0.11 0.44,1.06 0.69,-0.22 0.3,0.9 -0.71,0.32 -0.24,0.94 -0.49,0.33 -0.29,1.19 0.19,0.42 -0.28,0.21 0.43,0.47 0.49,-0.16 0.58,0.51 1.24,-0.26 1.06,0.94 0.85,0.28 0.33,-0.31 0.83,0.07 0.05,0.43 1.03,0.28 0.79,-0.51 0.17,0.35 0.74,0.48 0.01,0.4 0.42,0.32 -0.08,0.6 0,0 -0.29,0.87 -0.88,1.22 -0.3,1.64 -1.79,4.16 -1.44,2.37 -0.33,0.36 -2.85,3.04 -2.09,0.53 -0.72,0.12 -1.62,0.84 -0.23,0.71 -0.41,0.17 -1.37,1.46 -0.61,1.03 -0.04,0.81 -1.05,1.13 -1.83,0.62 -0.56,-0.12 -2.84,1.31 -1.56,0.12 -0.9,0.54 -6.45,1.19 -0.52,-0.09 -1.24,-0.74 -2.9,-0.65 -1.58,-0.78 -0.24,-1.44 0.68,-0.71 0.92,0.13 0.41,0.68 0.35,-0.16 -0.08,-0.2 2.31,-0.78 0.86,0.14 -1.37,-0.94 -0.22,-1.22 0.11,-0.97 0.61,-0.66 2.28,1.77 3.63,0.04 0.43,-0.33 z"
                            id="tinh-ca-mau" data-id="tinh-ca-mau" data-link="{PROVINCE_LIST_823.link}"
                            data-tinh="{PROVINCE_LIST_823.title}" data-province="{PROVINCE_LIST_823.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 216.31847,127.25323 0.76,-0.52 0.47,-0.95 -0.69,-1.74 0.21,-0.72 0.58,0.05 -0.09,0.37 1.18,1.11 -0.06,-0.45 1.15,-0.01 -0.12,-0.31 0.34,-0.13 0.17,-0.77 2,-0.68 0.23,-0.83 0.29,-0.05 0.08,-0.4 1.46,0.01 0,0 0.16,-0.13 0.17,0.2 0,0 0.4,0.17 0.42,-0.22 0.64,0.07 0.12,-0.45 0.18,0.5 0.49,0.2 0,0 -0.19,0.74 0.13,0.53 -0.33,0.14 0.09,0.79 -0.35,0.22 -0.15,0.42 0.19,0.36 -0.39,0.45 -0.16,0.72 0.22,0.08 -0.01,0.88 -1.15,0.73 -0.2,-0.25 -0.85,0.13 -0.11,0.59 0.6,0.67 -0.5,0.91 0.78,0.72 0,0 0.57,-0.08 0,0 1.04,-0.08 0.86,0.62 0.68,-0.19 0.14,0.36 -0.19,0.41 0.4,0.02 0.08,-0.25 0.4,0.16 0.63,-0.38 0.48,0.09 0.37,-0.19 0.39,0.17 0.38,-0.23 1.46,1.16 0.39,0.85 0,0 -1.11,0.34 -0.6,-0.08 -0.26,0.31 -0.44,-0.47 -0.49,0.95 -0.02,0.47 0.59,0.76 0.96,0.34 -0.01,0.6 0.39,0.51 0,0 -0.03,0.34 0,0 -0.8,0.95 -0.32,-0.25 0.44,-0.47 -0.28,-0.4 -0.3,-0.01 0.02,0.71 -0.46,0.35 -0.18,-0.13 -0.01,-0.77 -0.48,0.01 -0.19,0.24 -0.02,1.1 0.32,0.21 -0.36,-0.09 -0.43,0.53 -0.3,-0.2 -0.03,0.43 0.68,0.41 -0.11,0.11 0.31,0.32 -0.24,0.28 0.45,0.14 -0.18,0.34 0.4,0.11 -0.01,0.18 -0.85,0.39 -0.35,0.82 -0.3,-0.07 0.06,-0.51 -0.73,-0.09 -0.54,0.6 -0.44,-0.8 -0.72,0.5 1,0.97 -0.41,1.08 1.18,-0.07 0.24,0.27 -0.4,0.92 -0.42,-0.41 -0.74,0.48 0.56,0.9 0.08,0.58 -0.29,0.57 0.3,0.14 -0.6,0.54 -0.7,-0.18 -0.4,0.57 -0.69,0.03 0.64,0.66 -0.24,0.46 -0.44,0.01 0.06,-0.47 -0.25,-0.3 -0.55,0.9 -0.67,-0.74 -0.61,-0.1 -0.27,0.38 0,0 -0.32,-0.02 -1.01,0.94 -1.26,0.59 -0.41,0.01 -0.47,-0.55 -0.42,0.01 -1.57,1.16 -0.47,-0.41 0.03,-0.54 -0.41,-0.26 -0.2,0.29 0.32,0.45 -0.09,0.25 -0.61,0.05 -0.53,-0.64 -0.84,0.44 0.27,0.33 0.8,0.32 0,0 -1.08,-0.12 -0.25,-0.49 -0.85,-0.52 -0.2,-0.7 -0.63,-0.73 0.35,-0.09 -0.13,-0.98 -0.65,-0.09 -0.55,-0.77 -0.62,0.14 -0.35,-0.52 0.1,-0.21 -0.23,-0.01 -0.32,-0.46 -0.41,0.27 -0.31,-0.11 -0.52,-0.89 -0.55,-0.47 0.37,-0.19 0.12,-0.39 -0.49,-0.63 -0.35,-0.08 0.28,-0.74 -0.19,-0.28 0.55,-0.14 0.25,-0.39 0.28,-1.31 0.02,-0.35 -0.45,-0.32 -0.13,-0.55 0.48,-0.55 0.52,-0.2 -0.36,-0.64 0.23,-0.26 -0.14,-0.6 0.41,-0.36 -0.43,-0.52 -0.74,0.01 -0.1,-0.52 0.2,-0.29 -0.53,-0.5 0,0 0.24,-0.85 0.31,-0.12 0.66,0.69 0.94,0.11 -0.28,0.62 0.22,0.36 1.04,-0.65 0.23,0.1 1.5,-0.6 0.02,-0.33 1.23,0.28 0.1,-0.38 1.05,0.54 0.31,-0.22 0.12,-0.78 1.04,-0.91 -0.08,-0.89 0.95,-1.11 -0.42,-0.75 -0.43,-1.92 z"
                            id="tinh-hai-duong" data-id="tinh-hai-duong" data-link="{PROVINCE_LIST_107.link}"
                            data-tinh="{PROVINCE_LIST_107.title}" data-province="{PROVINCE_LIST_107.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 200.90847,150.37323 0.74,0.2 0.63,0.56 0.3,0.97 0,0 -0.2,1.41 0.41,1.09 0.62,0.68 0,0 0.34,0.4 0.99,0.28 1.21,-0.74 1.09,-0.04 0,0 0.52,0.58 0.07,1.77 1.36,0.52 0.71,1.04 0.4,1.51 -0.05,1.32 0.24,0.58 0,0 -0.58,0.63 -1.29,0.26 -0.42,-0.58 0.07,-0.86 -0.55,-0.25 -0.8,0.16 -0.21,0.33 -0.55,0.18 0.12,0.31 -0.2,0.17 0.35,0.56 -0.25,0.72 -0.45,-0.08 -0.38,-0.46 0,0 -0.26,0.2 0,0 -0.25,-0.38 -0.48,0.17 -0.36,-0.41 -0.76,0.13 -0.07,1.08 -0.71,0.28 0.07,0.28 -0.53,1.3 0.42,0.84 -0.74,0.13 -0.62,-0.22 -0.08,0.4 -0.23,0.03 -0.33,-0.51 0.26,-1.29 -0.61,-0.18 -0.07,-0.49 -0.19,-0.06 -0.21,1.01 -0.47,-0.14 -0.5,0.59 -0.35,0.09 0.11,0.55 -0.43,0.28 -0.78,-0.06 -0.17,0.18 0.17,0.96 0.5,0.18 -0.12,0.42 0,0 -0.64,0.27 -0.57,-0.76 -0.12,0.24 -0.36,-0.04 -2.31,-2.92 0,0 -0.19,-0.41 -0.1,-2.45 -0.73,-0.13 -0.69,-0.48 -0.25,-0.31 -0.02,-0.62 -0.55,0.39 -0.21,-0.11 -0.21,-0.67 -0.31,0.06 -0.59,-1.49 -0.53,-0.6 -0.26,-0.87 0,0 1.43,-0.18 0.27,-1.36 0.54,-0.62 -0.32,-0.3 0.5,-0.45 -0.18,-0.54 0.16,-0.36 1.01,-0.74 -0.76,-0.7 0.15,-0.23 0.91,0.28 0.94,0.8 1.07,0.11 0.91,-0.49 0,-0.96 0.49,0.23 0.73,-0.59 1.23,-0.34 0.18,0.31 -0.09,1.19 0.7,0.25 0.01,-0.51 0.97,-0.33 0.07,-0.46 0.32,-0.37 0.44,-0.09 -0.07,0.22 0.31,0.02 -0.31,-0.65 0.33,-0.15 -0.13,-0.28 z"
                            id="tinh-ha-nam" data-id="tinh-ha-nam" data-link="{PROVINCE_LIST_111.link}"
                            data-tinh="{PROVINCE_LIST_111.title}" data-province="{PROVINCE_LIST_111.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 200.81847,134.01323 0.53,0.08 0.3,-0.22 0.2,0.42 0.45,0.1 1.11,-0.78 0.36,0.38 0.47,-0.03 0.52,0.3 0.69,-0.64 0.64,0.54 0.35,-0.14 0.34,0.21 -0.03,0.7 0.63,-0.02 0,0 0.53,0.5 -0.2,0.29 0.1,0.52 0.74,-0.01 0.43,0.52 -0.41,0.36 0.14,0.6 -0.23,0.26 0.36,0.64 -0.52,0.2 -0.48,0.55 0.13,0.55 0.45,0.32 -0.02,0.35 -0.28,1.31 -0.25,0.39 -0.55,0.14 0.19,0.28 -0.28,0.74 0.35,0.08 0.49,0.63 -0.12,0.39 -0.37,0.19 0.55,0.47 0.52,0.89 0.31,0.11 0.41,-0.27 0.32,0.46 0.23,0.01 -0.1,0.21 0.35,0.52 0.62,-0.14 0.55,0.77 0.65,0.09 0.13,0.98 -0.35,0.09 0.63,0.73 0.2,0.7 0.85,0.52 0.25,0.49 1.08,0.12 0,0 -0.18,0.27 -1.2,0.5 -0.55,0.63 -0.82,0.24 0,0 0.43,0.67 -0.3,0.08 0,0 -0.87,-0.14 -1.43,-0.79 -0.29,0.64 -0.32,0.09 -0.89,-0.76 -0.49,-0.01 -0.53,0.59 -0.84,0.31 0.03,0.33 0.4,0.01 0.51,0.45 0.01,0.26 -0.41,0.35 0,0 -1.09,0.04 -1.21,0.74 -0.99,-0.28 -0.34,-0.4 0,0 -0.59,-0.63 -0.42,-1.01 0.18,-1.54 0,0 -0.3,-0.97 -0.63,-0.56 -0.74,-0.2 0,0 -1.55,0.21 -0.46,-1.35 0.27,-0.52 -0.14,-2.15 -0.34,-0.33 -1.61,-0.4 -0.54,-0.64 -0.41,-1.77 0.3,-0.67 0.86,-0.52 0.14,-0.75 -0.3,-0.52 -1.72,-1.01 -0.09,-0.34 0.19,-0.35 0,0 0.73,-0.75 -0.25,-0.42 0.35,-0.6 -0.57,-0.5 0.02,-0.43 0.58,-1.19 0.85,0.09 0.35,0.34 0.86,-0.04 0.49,0.29 -0.14,-1.47 0.54,0.01 -0.02,-0.4 0.29,-0.12 0.38,0.14 0.36,-0.26 z"
                            id="tinh-hung-yen" data-id="tinh-hung-yen" data-link="{PROVINCE_LIST_109.link}"
                            data-tinh="{PROVINCE_LIST_109.title}" data-province="{PROVINCE_LIST_109.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 230.20847,174.12323 0.8,0.83 -0.48,0.29 0.65,0.61 0.17,0.68 -0.72,1.1 -0.71,0.25 -0.74,-0.55 0.16,-1.56 -0.14,-0.53 0.24,-0.67 0.77,-0.45 z m -32.85,-5.17 0.12,-0.42 -0.5,-0.18 -0.16,-0.96 0.17,-0.17 0.79,0.05 0.43,-0.28 -0.11,-0.55 0.35,-0.09 0.5,-0.59 0.47,0.14 0.21,-1.01 0.19,0.05 0.08,0.49 0.61,0.18 -0.26,1.3 0.33,0.51 0.22,-0.04 0.08,-0.39 0.63,0.22 0.74,-0.13 -0.42,-0.84 0.54,-1.3 -0.07,-0.28 0.71,-0.28 0.08,-1.08 0.76,-0.13 0.36,0.41 0.49,-0.18 0.25,0.38 0,0 0.26,-0.2 0,0 0.39,0.46 0.45,0.08 0.25,-0.71 -0.36,-0.56 0.2,-0.18 -0.12,-0.31 0.55,-0.18 0.21,-0.33 0.81,-0.16 0.55,0.25 -0.07,0.86 0.42,0.59 1.29,-0.26 0.58,-0.63 0,0 0.34,0.27 1.11,0.1 0.71,0.57 -0.14,1.01 -0.64,1.13 0.52,0.99 2.84,0.43 0.58,0.5 -0.09,0.77 -1.53,1.35 -0.09,0.71 0.43,0.78 0.92,-0.05 2.27,-1.79 0,0 0.88,-0.86 0,0 0.91,1.35 2.24,0.87 1.57,1.94 0.68,0.48 0.99,0.25 0.66,-0.1 0.53,-0.22 0.7,-0.74 0.65,-0.01 0.87,-0.43 1.71,1.78 0,0 0.25,0.54 -0.77,0.46 -0.25,0.63 0.13,0.61 -0.29,0.7 -0.53,0.41 -0.52,0.14 0.03,-0.57 -1.11,0.66 -3.03,0.35 -0.71,0.35 -1.1,0.16 -2.56,1.58 -0.85,1.06 -0.9,0.67 -0.71,1.15 -1.21,1.18 -4.64,5.6 -0.26,0.1 -0.78,1.33 -1.01,0.68 -0.75,-0.08 -0.64,0.6 -0.26,0.54 0.11,0.18 0.36,-0.11 0.17,0.55 0.33,-0.36 0.19,0.08 -0.33,0.83 -0.59,-0.23 -0.6,0.2 0,0.35 -0.24,0.18 -0.55,0.12 -0.53,-0.16 0,0 0.38,-1.18 -0.37,-2.04 0.85,-2.69 0.14,-1.48 1.34,-0.96 0.45,-2.12 0.99,-1.52 -0.31,-3.26 -0.48,-1.88 -0.39,-0.2 -2,0.59 -0.52,-2 -0.33,-0.09 -0.54,1.08 -0.59,0.61 -0.67,0.36 -0.61,-0.11 -0.62,-0.56 -0.14,-0.46 0.94,-0.2 -0.13,-0.59 -0.3,-0.21 -0.81,0.29 -1.04,-0.06 -1.26,-0.65 0.16,-1.53 -1.02,-1.28 0,0 -1.16,-0.56 0,0 -0.36,-0.25 0.67,-0.56 0.28,-0.81 -0.85,-0.15 -0.17,-0.64 z"
                            id="tinh-nam-dinh" data-id="tinh-nam-dinh" data-link="{PROVINCE_LIST_113.link}"
                            data-tinh="{PROVINCE_LIST_113.title}" data-province="{PROVINCE_LIST_113.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 153.22847,94.133229 0.8,0.03 0.39,0.65 0.75,0.08 0.49,0.43 0.78,-0.16 0.44,0.68 -0.05,0.34 0.52,-0.39 0.63,0.07 0.09,0.27 0.67,0.3 0.13,0.41 0.54,0.39 1.58,0.21 0.2,0.8 -0.68,0.82 0.42,0.68 0.87,0.350001 0.13,0.48 -0.41,0.25 0.25,0.75 0.59,0.94 0.63,0.37 -0.29,0.82 1.58,0.83 -0.12,0.55 0.26,0.67 0.38,0.12 0.71,-0.22 0.13,0.23 0,0 -0.12,0.57 0.55,0.79 0.51,2.15 0,1.24 0.53,0.17 1.07,-0.86 0.81,0.37 0.48,0.83 -0.36,1.41 0.15,1.49 0.67,0.76 1.73,0.91 0.77,0.99 0.01,0.45 -0.33,0.41 0.23,0.21 -0.27,1.56 -0.26,-0.01 0,0 -0.5,-1.28 -1.94,-0.88 -1.2,0.54 -0.25,-1.06 -0.26,0.14 -0.45,0.47 0.16,0.74 -0.19,2.08 -0.81,2.33 0.26,0.1 0,0 -0.31,0.3 -0.38,-0.06 0,0 -1.4,0.65 -0.86,1.43 0.16,0.59 1.54,2.3 -0.16,1.12 -1.18,2.22 0.56,1.74 0.65,0.54 0,0 0.67,1.29 0.75,2.66 -1.43,0.33 -0.24,0.6 -0.83,0 -0.38,-0.4 0,0 -0.73,0.37 0,0 -0.2,-0.89 -0.6,0.29 -0.68,-0.56 -0.88,0.56 -0.39,-0.04 -0.18,0.21 0.29,0.34 -1.1,0.51 -0.37,-0.2 0.1,-0.22 -0.23,-0.26 0.65,-1.18 -0.74,-0.43 -0.14,0.37 -0.65,0.38 -0.78,0.06 -0.06,0.26 -0.69,-0.12 -0.85,0.18 -0.47,-0.21 -0.65,-0.75 -0.91,-0.28 -1.12,-2.11 -0.77,-0.63 -0.67,0.01 -0.65,-0.68 -0.61,0.39 0.09,-0.58 -0.56,-0.7 -1.05,-0.6 -1.06,-0.23 -0.3,-0.4 -1.29,0.25 -0.4,-0.5 -1.05,-0.13 0.27,-0.39 0.33,-0.06 0.21,-0.5 -0.58,-0.76 -0.18,-0.72 0,0 0.15,-0.02 -0.09,-0.29 -0.44,-0.11 -0.42,-0.54 -0.77,-0.24 -0.6,-0.59 -0.64,-0.13 -0.2,-0.27 0.33,-1.2 -0.39,-0.35 0.65,-1.53 -0.41,-0.65 -0.61,-0.33 0.27,-0.44 0.29,0 -0.32,-0.73 0.04,-0.56 -0.47,-0.37 0.1,-2.6 -1.27,-0.75 -0.15,-0.71 0,0 0.28,-0.21 1.44,-0.02 -0.25,-0.7 0.49,-0.01 -0.03,-0.43 0.27,-0.31 0.26,0.02 0.26,0.62 0.74,0.64 0.69,-0.12 0.23,-0.37 -0.37,-0.79 -0.47,-0.38 -0.13,-0.6 0.07,-0.46 0.61,-0.11 0.67,-1.19 -0.11,-0.74 0.32,-0.34 0,-0.68 -0.77,-1.26 -0.64,-1.75 0.02,-1.25 -0.36,-0.7 -0.51,-0.38 -0.03,-0.34 0.26,-0.44 0.72,-0.06 0.62,-0.41 -0.26,-2.22 -0.32,-0.43 0.97,0.2 -0.1,-0.860001 0.31,-0.57 0.5,-0.35 0.77,-0.09 0.18,-0.54 0.96,-0.5 0.79,0.82 0.07,-0.59 0.65,-0.29 0.04,-0.34 -0.33,-0.24 1.08,-0.4 -0.06,-0.44 1.02,-0.02 0.08,-0.46 0.26,0.04 0.25,-0.33 0.99,0.74 0.44,0.14 0.17,-0.23 0.58,0.42 0.78,-0.05 0.37,0.27 0.28,-0.64 -0.19,-0.59 -0.5,-0.11 0.27,-0.56 z"
                            id="tinh-phu-tho" data-id="tinh-phu-tho" data-link="{PROVINCE_LIST_217.link}"
                            data-tinh="{PROVINCE_LIST_217.title}" data-province="{PROVINCE_LIST_217.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 177.63847,80.803229 1.03,-0.01 -0.16,-0.49 0.46,-0.13 -0.18,-0.34 0.44,-1.48 0.34,-0.51 0.6,-0.1 0,0 0.4,0.03 0,0 0.45,-0.37 0.81,-0.03 0.23,-0.45 0.64,0.2 0.46,-0.33 0.37,-1.02 1.32,0.53 0.67,0.85 0.77,-0.09 0.5,0.51 0.41,-0.2 0.63,0.33 0.23,1.17 0.4,0.36 -0.16,0.96 0.76,0.85 -0.07,0.78 0.21,0.25 -0.71,1.12 0.37,0.27 0.07,0.75 0.44,0.38 -0.57,0.24 0.16,0.53 -0.26,0.45 0.08,0.5 -0.62,1.51 0.23,0.34 -0.1,0.36 0.38,0.34 0.6,0.12 0.38,0.36 0.68,-0.04 0.24,-1.44 0.55,-0.72 2.39,-0.12 0,0 0.2,-0.31 -0.1,-0.41 0,0 0.58,0.17 0.41,-0.16 1.5,-2.04 1.01,-0.03 0.86,-0.38 0.68,-0.61 0.04,-0.44 0.55,-0.19 -0.04,-0.57 0.34,-0.24 0,0 0.57,0.11 0,0 0.68,0.26 0.61,-0.27 0.9,-1.69 0.88,0.42 0.31,0.78 0.35,0.27 1.76,-0.02 0.28,0.24 0,0 1.72,0.96 -0.22,1.16 -0.74,0.51 0.12,1.1 -0.25,0.57 0.06,0.61 0.32,0.35 -0.55,0.14 0.03,0.48 -0.35,0.09 -0.3,0.54 0.63,0.99 -0.06,0.7 0.25,0.19 0,0 0.03,0.3 0.39,0.02 0,0 0.92,-0.2 0.63,1.32 0.97,0.37 0.04,0.26 0,0 0.68,0.36 0,0 0.67,0.1 0.12,0.33 0.75,0.38 0,0 0.36,0.1 0.38,-0.34 0.51,0.33 0.14,0.49 -0.23,0.57 0,0 -0.05,0.52 -0.43,0.41 0.07,0.65 -1.78,1.88 0.05,0.49 0.3,0.24 -0.16,0.34 0.13,0.36 -0.66,0.900001 0,0 -0.57,-0.31 -0.6,-0.840001 0,0 -0.41,0.05 0,0 -0.91,0.23 0,0 -0.51,0.22 0,0 -1.43,-0.5 -1.1,0.37 -0.59,0.750001 -0.52,0.1 -0.42,1.2 0,0 -0.16,0.07 0,0 -0.66,0.47 -0.13,0.5 0.88,0.21 0.35,0.45 -0.17,0.31 0.13,0.36 -0.61,1.35 0,0 0.13,0.19 0,0 -0.36,1.15 0.6,0.58 -0.45,0.47 0.19,0.42 0,0 0.21,0.15 0,0 0.16,0.03 -0.17,1.06 -0.84,0.19 -0.34,0.35 0.43,1 0.03,0.31 -0.36,0.5 0.1,0.27 -0.47,-0.07 -0.15,-0.42 -0.6,-0.19 -0.53,0.11 -0.24,-0.46 -0.32,0.12 -0.32,-0.18 -0.71,-1.23 -0.2,-0.02 -0.33,1.29 0.21,0.64 -0.22,0.03 -0.07,-0.31 -0.68,0.45 0.27,0.41 -0.25,1.02 -1.22,0.03 -0.26,-0.23 -0.88,1.36 0.67,-0.03 0.02,0.15 -0.55,0.38 -0.55,-0.19 0,0 -0.34,0.46 0,0 0.61,0.65 0,0 -1.11,-0.19 -0.14,0.14 -0.29,-0.59 -0.25,-0.04 -0.12,-0.3 0.18,-0.07 -0.33,-0.18 -0.07,-0.41 0.1,-0.75 0.32,0.14 -0.16,-0.53 -0.89,-0.05 -0.38,0.24 -0.09,-0.15 0,0 -0.07,-0.03 0,0 -1.44,-0.5 -0.69,0.56 -0.17,0.86 0,0 0,0.01 0,0 -0.49,-0.08 -0.12,-0.54 -0.69,-0.43 0.33,-0.61 -0.55,-1.19 -0.47,-0.3 -0.9,0.39 -0.41,-0.33 0.18,-0.99 -0.57,0.17 -0.49,-0.6 -0.36,0.1 -0.53,-0.22 -0.6,-0.83 -1.24,0.01 -0.38,-0.66 -0.67,-0.54 0.17,-0.76 -0.84,-0.31 -0.36,-0.38 -0.41,-1.3 -1.05,-0.37 -1.41,-1.96 -0.74,-0.18 0,0 -0.34,-0.42 -0.48,-0.07 -0.63,-1.08 -0.94,-0.910001 0.18,-0.76 -0.51,-0.64 -0.57,-1.69 0.39,-0.37 0.06,-0.45 -0.53,-0.49 0.63,-0.32 0.74,-1.47 0.62,-0.39 -0.36,-0.76 0.45,-0.28 0.42,-0.69 -0.51,-0.36 -0.82,0.21 -0.3,-0.24 0.18,-1.02 -0.25,-0.63 0.04,-1.3 0.15,-0.23 0.5,-0.05 0.54,0.34 0.59,-0.48 0.02,-0.95 -0.53,-1.35 0.32,-0.62 1,-0.2 0.07,-0.71 0.4,-0.24 -0.19,-2.19 z"
                            id="tinh-thai-nguyen" data-id="tinh-thai-nguyen" data-link="{PROVINCE_LIST_215.link}"
                            data-tinh="{PROVINCE_LIST_215.title}" data-province="{PROVINCE_LIST_215.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 176.95847,102.16323 0.74,0.18 1.41,1.96 1.05,0.37 0.41,1.3 0.36,0.38 0.84,0.31 -0.17,0.76 0.67,0.54 0.38,0.66 1.24,-0.01 0.6,0.83 0.53,0.22 0.36,-0.1 0.49,0.6 0.57,-0.17 -0.18,0.99 0.41,0.33 0.9,-0.39 0.47,0.3 0.55,1.19 -0.33,0.61 0.69,0.43 0.12,0.54 0.49,0.08 0,0 0,0 0,0 0.02,0.93 -0.44,0.3 -1.2,2.34 -0.56,0.14 -0.05,0.3 -0.29,0.02 -0.19,0.65 -0.38,0.13 0.21,0.19 -0.32,0.19 0,0.81 0.3,0.13 -0.07,0.46 0.13,0.13 0.15,0.26 1.29,0.6 0.16,0.49 0.4,0.01 0.12,0.46 0.9,0.52 0.15,0.76 -0.38,0.29 -0.61,-0.04 0.07,1.92 -0.32,0.47 -0.75,0.01 -0.03,0.17 -0.26,-0.3 -0.61,0.86 -0.19,-0.05 0,0 -0.38,-0.55 -1.16,-0.48 -2.06,-0.39 -1.61,-0.58 -0.03,-0.56 -0.43,-0.36 -3.38,0.96 -1.9,0.12 -1.52,-0.29 -1.18,-0.87 -0.43,-2.41 -0.79,-1.4 -0.13,-1.09 0,0 0.26,0.01 0.27,-1.56 -0.23,-0.21 0.33,-0.41 -0.01,-0.45 -0.77,-0.99 -1.73,-0.91 -0.67,-0.76 -0.15,-1.49 0.36,-1.41 -0.48,-0.83 -0.81,-0.37 -1.07,0.86 -0.53,-0.17 0,-1.24 -0.51,-2.15 -0.55,-0.79 0.12,-0.57 0,0 0.81,0.18 -0.19,-0.27 0.38,-0.55 0.8,-0.02 0.61,-0.32 0.8,0.27 0.2,0.46 1.12,-0.07 0.6,0.84 0.36,-0.47 1.3,-0.27 0.16,-0.42 0.53,0.05 0.27,-0.35 0.69,0.35 0.34,-0.59 0.25,0.16 0.7,-0.62 0.49,0.08 0.47,-1.29 z"
                            id="tinh-vinh-phuc" data-id="tinh-vinh-phuc" data-link="{PROVINCE_LIST_219.link}"
                            data-tinh="{PROVINCE_LIST_219.title}" data-province="{PROVINCE_LIST_219.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 9.688471,47.223229 0.05,0.76 0.4,0.55 0.65,0.36 -0.2,0.49 0.48,0.82 -0.02,0.83 0.56,0.04 0.22,0.81 1.22,0.41 0.36,0.45 -0.12,1.05 0.28,0.46 0.08,0.79 -0.33,-0.27 -0.27,0.75 1.22,1.24 0.64,0.15 0.2,0.82 1.18,0.57 0.42,0.61 -0.05,0.4 0.9,0.84 0.91,1.7 0.94,0.99 0.29,1.06 0.52,0.43 0.96,0.57 0.53,-0.05 0.52,-0.91 0.8,-0.05 1.36,1.16 0.8,0.29 -0.01,2 0.47,0.54 0.42,0.13 0.39,0.67 0,0 0.55,-0.05 0,0 1.2,0.46 0.62,-1.42 0.34,-0.29 0.09,-0.56 -0.53,-1.59 -1.27,-0.93 0.7,-0.49 0.8,0.87 0.87,0.42 0.93,0.04 1.43,1.69 -0.34,1.72 -0.48,0.4 0.16,1 -0.17,0.42 0.31,0.81 0.93,0.79 0.24,-0.04 0.65,0.89 -0.28,0.55 0.1,0.19 1.72,0.73 0.53,0.47 1.61,-0.04 0.47,0.35 0.38,-1 0.55,-0.14 0.54,0.32 1.26,-0.88 1.12,-0.27 0.11,0.53 -0.39,0.52 0.09,0.42 0.72,0.19 0.86,0.88 0.21,0.47 -0.14,0.23 0.3,0.36 0.94,0.26 0.52,-0.59 0.22,-0.85 0.54,0.1 0.57,-0.32 0.68,0.38 0.49,0.03 0.94,-0.78 1.65,0.32 0.04,-0.48 -0.33,-0.26 0.71,-1.72 0.99,0.4 0.24,0.7 0.93,0.82 0.28,-0.07 0.08,-0.38 1,-0.21 1.37,0.64 0.8,-0.09 1.6,1.13 2.07,0.59 0.39,-0.24 0.02,-0.38 -0.83,-0.98 -0.1,-0.46 0.55,-0.65 0.16,-1.24 1.08,-0.32 -0.07,-0.64 0.52,-0.43 0.14,-0.58 0.68,-0.56 2.92,-0.17 0.19,0.13 -0.01,0.72 0.73,0.77 0.84,3.15 0.46,0.38 0.26,0.58 0.43,-0.17 0.49,0.23 0.72,1.54 0,0 -0.73,0.61 0,0 -0.05,0.35 0.66,0.72 0.4,0.85 1.21,0.6 0,0 0.42,0.79 -0.36,0.3 -0.04,0.36 -0.62,0.38 0.24,0.23 -0.35,0.3 -0.16,1.01 0.4,0.33 -0.17,0.46 0.27,0.31 0.14,0.84 0.75,0.14 0.83,0.63 0.37,1.43 0.59,0.56 -0.19,0.54 0.5,0.99 -0.27,1.1 0.87,0.85 0.17,0.66 0.55,0.58 1.31,3.28 -0.2,0.25 -0.55,-0.45 -0.33,0.08 -0.76,0.79 -0.34,0.78 -0.73,0.47 0.91,1.15 -0.56,1.260001 -0.46,0.18 -0.1,0.47 -0.39,0.32 0.15,1.03 -0.67,0.28 0.08,0.95 -0.2,0.7 -0.88,0.01 -0.87,0.62 -1.26,0.19 0.05,0.33 -0.46,0.6 -0.19,0.78 0.4,1.45 -0.48,0.98 0.12,0.38 -0.37,0.31 -1,0.04 -0.28,0.2 -1.25,-0.53 -0.75,0.05 -0.08,0.34 -1.16,0.26 -0.54,-0.26 -0.66,0.82 0.49,0.8 0.87,0.71 0.16,0.84 1.21,0.21 0.2,0.24 -1.09,1.72 1.4,0.82 1.81,0.32 0.59,1.47 -0.08,1.15 -0.39,0.74 0.22,0.61 -0.14,1.75 0.25,0.95 -0.22,1.33 -0.24,0.27 -0.75,-0.12 -0.33,0.62 -0.63,0.4 0.34,2.3 0.39,1.01 0.89,1.04 -1.2,0.47 -1.64,-0.15 -1.94,-1.51 -2.15,-0.37 -0.3,0.18 0.21,0.54 -0.13,0.32 0.62,0.93 -1,-0.2 -0.02,0.2 -0.27,-0.05 0.06,0.92 0,0 0.3,0.36 0,0 0.48,1.03 -0.41,0.23 0.07,0.33 -0.41,0.6 0.28,0 -0.08,0.14 0.32,0.24 0.63,1.5 -0.02,1.01 -0.78,0.11 -0.45,0.59 0.17,0.76 0.49,0.74 -0.46,0.55 -0.46,-0.28 -1.3,0.48 -1.39,-0.09 0,0 -0.38,-0.32 -1.28,-0.16 -1.07,-0.67 -0.47,0.62 -1.32,-0.1 -0.65,0.16 0,0.38 -0.32,-0.01 -0.32,-1.72 -0.18,-0.34 -0.55,-0.05 -0.32,-0.31 -0.11,-1.53 -1.33,-1.94 -0.16,-0.65 -0.86,-1.27 -0.27,-1.05 -0.94,-0.38 -0.81,0.21 -1.44,-0.37 -0.6,-0.76 0.33,-0.73 -0.34,-1.04 -1.09,-0.9 0.48,-0.68 -0.66,-0.95 -0.45,-0.24 -0.26,0.32 -0.92,-0.51 0.27,-0.79 -0.27,-0.58 0.37,-0.75 -0.21,-1.07 -0.31,-0.53 -0.5,-0.68 -1.43,-0.64 -1.12,0.51 -0.9,0.09 -0.56,-0.32 -0.21,-0.65 0.35,-0.45 0.9,-0.41 0.46,-0.74 0.47,-0.34 2.26,-0.29 0.15,-0.73 -0.51,-0.84 0.33,-1.31 0.1,-0.51 -0.16,-1.09 0.74,-0.84 0.5,-0.42 0.18,-1.29 0.93,-1.41 -0.85,0.01 -0.53,0.59 -0.04,0.44 -0.48,0.21 -0.84,0.41 -0.71,0.15 -0.2,-0.23 0.43,-0.52 -0.05,-0.45 0.34,-0.91 0.33,-0.42 0.87,-0.59 -0.01,-0.34 0.32,-0.96 0.93,-1.13 0.67,-0.67 0.74,-1.04 0.46,-0.69 1.4,-1.09 -0.21,-0.95 -0.04,-1.100001 -0.32,-0.69 -0.1,-0.52 -0.08,-0.35 0.5,-1.76 -0.51,-0.39 -0.07,-0.4 0.51,-0.53 0.03,-0.47 -0.8,-1.23 -0.05,-0.46 -0.36,0.04 -0.63,0.85 -2.67,0.59 -0.76,0.34 -0.51,0.53 -1.08,-1.04 -0.09,-1.22 0.03,-1.41 0.15,-0.77 0.04,-0.82 -0.33,-2.34 -0.46,-0.39 -1.18,0.22 -0.53,1.1 0.62,1.27 -0.25,0.93 0.4,2.65 -0.56,0.19 -0.27,0.73 -0.67,0.63 -0.51,0.05 -1.24,1.71 -0.69,0.62 -0.53,0.33 -0.6,-0.28 -1.74,0.03 -0.64,0.47 -0.29,-0.06 -0.68,-1.4 -0.21,-1.69 -0.68,-1.13 0.12,-1.73 0.73,-1.63 -0.74,-1.01 0.1,-1 -0.41,-1.14 -0.42,-0.72 -0.27,-0.39 -0.86,-0.44 0.25,-1.02 0.26,-0.28 -0.4,-0.97 -2.44,-1.09 -1.48,-1.16 -1.69,-0.85 -0.94,-1.19 -0.04,-0.37 0.29,-0.66 0.75,-0.13 0.64,-0.46 -0.06,-0.51 -0.44,-0.26 -0.74,0.11 -0.57,-0.37 -0.64,-1.02 -1.57,-0.74 -0.17,-0.95 0.08,-0.62 -0.33,-0.81 -0.55,0.04 -0.82,-0.67 -1.13,-0.08 -0.19,-1.27 -1.06,-0.76 -0.74,0.13 -0.61,-0.18 -0.06,-1.16 -0.72,-0.92 -0.45,-0.23 -0.47,0.37 -0.95,-0.31 -0.38,-0.8 -0.31,-0.27 0.03,-0.6 -1.65,-0.15 0.01,-0.87 -0.55,-1.06 0.11,-0.6 -1.1,-0.19 -1.08,-0.83 -0.65,-0.21 0.13,-0.84 0.79,-0.86 -0.33,-0.21 -0.55,0.08 -0.23,-0.23 -0.5,-0.98 0.28,-0.78 -1.49999996,-1.36 0.48,-1.45 0.32999996,-0.3 1.48,-0.24 1.27,0.19 0.51,0.3 1.23,0.29 0.84,0.33 0.17,-0.95 -0.89,-1.65 0.77,-0.31 0.07,-0.59 0.41,-1.2 2.34,-2.84 0.14,-0.47 z"
                            id="tinh-dien-bien" data-id="tinh-dien-bien" data-link="{PROVINCE_LIST_301.link}"
                            data-tinh="{PROVINCE_LIST_301.title}" data-province="{PROVINCE_LIST_301.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 281.91847,577.68323 1.21,0.09 0.92,-0.32 0.39,0.25 1.3,-0.35 0.45,0.43 0.89,-0.19 1.31,0.47 2.5,0.43 0.54,-0.12 2,0.29 1.13,-0.1 0.29,-0.2 -0.1,-0.13 0.24,0.05 -0.02,-0.24 0.37,0.01 0.3,-0.64 0.47,-0.02 0.73,-0.55 0.43,0.28 0.4,0.6 -0.49,0.89 0.33,1.17 0.43,0.34 2.18,0.45 -0.02,0.48 -0.5,0.44 -0.23,0.67 0.9,1.03 -0.44,0.95 0.52,0.87 -0.53,0.39 0.16,0.24 0.68,0.01 -0.01,1.4 0.43,0.15 0.13,0.97 0.29,0.21 -0.11,0.67 -1.13,0.35 -0.93,0.63 0.14,0.57 -0.46,0.37 0.01,0.38 1.2,0.74 0.71,-0.2 0.4,0.23 0.51,-0.12 0.27,0.35 0.31,-0.03 1.05,0.71 0.28,0.62 0.57,0.15 -0.03,0.39 0.36,-0.04 0.2,0.51 -0.37,0.37 0.09,0.24 -0.2,0.01 -0.4,0.98 -1,0.52 0,0 -0.67,-0.26 0,0 -0.38,0.03 -0.45,0.51 -0.35,-0.04 0.04,0.42 -0.44,-0.19 0.11,0.47 -0.39,0.15 0.55,0.18 -0.61,0.3 -0.16,0.34 0.24,0.46 0.47,0.14 -0.27,0.12 -0.03,0.3 0.38,-0.01 0.24,0.36 0.09,1.71 0.55,0.39 -0.04,0.38 0.45,0.46 0.97,0.43 0.13,0.69 1.22,-0.39 0.31,0.45 0.58,-0.36 0.24,0.64 1.09,0.13 0.55,1.19 0.23,-0.19 0.25,0.27 0.34,-0.08 0.38,0.81 0.6,0.47 -0.09,0.39 0.27,0.73 1.24,1.29 0.06,0.63 0,0 0.03,0.86 -0.24,0.16 0.4,0.19 -0.22,0.46 0.12,0.25 -0.3,-0.12 -0.13,0.28 -0.2,-0.21 -0.04,0.65 -0.33,-0.39 -0.25,0.52 -0.65,-0.23 0.05,0.28 -0.42,0.04 -0.23,-0.3 -0.55,0.06 -0.08,-0.37 -0.19,0.27 -0.41,0 -0.44,-0.32 -0.55,0.46 -0.27,-0.27 0.04,0.71 -0.43,0.2 -0.21,0.43 -0.48,-0.65 0.06,0.27 -0.52,0.08 0.05,0.62 -0.27,-0.02 -0.13,0.32 0.02,1.28 -0.56,0 0.14,0.26 -0.26,0 -0.37,0.41 0.09,0.28 0.22,-0.06 0.54,0.54 0.6,-0.1 0.03,0.19 0.2,-0.11 0.27,0.23 0.58,-0.16 0.07,0.61 -0.19,-0.02 0.14,0.27 -0.15,0.07 0.16,0.14 -0.25,0.4 0.52,0.08 0.45,0.49 -0.16,0.54 0.25,0.25 -0.01,0.42 0.94,0.73 0.22,1.08 -0.18,0.34 0.75,0.38 0.39,-0.29 0.74,1 0.08,0.93 0.85,0.27 0,0 0.48,0.14 0.07,0.35 0,0 -0.51,1.04 -1.69,2.25 -0.54,-0.14 -1.76,1 -0.32,0.63 -0.69,-0.02 -0.36,0.31 -0.62,0.06 -0.33,0.46 -0.27,0.04 -0.4,-0.86 -0.42,-0.24 -0.03,-0.4 -0.46,0.09 -0.06,-0.34 -0.43,0.31 -0.05,0.31 -0.28,0.12 -0.15,-0.19 -0.29,0.85 -1.34,-0.6 -0.55,-0.65 0.13,-0.28 0.43,0.04 -0.43,-0.23 0.01,-0.76 0.54,-0.4 -0.19,-0.87 -0.21,-0.15 -2.07,-0.19 -0.53,0.3 -0.39,-0.45 -0.28,0.01 -0.41,0.75 -0.5,-0.25 -0.28,0.36 -1,0.06 -0.89,0.56 -1.06,-0.93 -0.86,-0.42 -0.41,0.24 -0.06,-0.3 0.36,-0.26 -0.51,-0.94 0.58,-0.24 -0.29,-0.63 -1.01,-0.38 -0.37,0.34 -0.65,-0.21 -0.88,0.46 0.16,0.44 -0.21,0.42 0.32,0.26 -0.16,0.2 -0.57,-0.06 -0.26,0.62 -0.55,0.06 -0.48,0.47 -0.49,0.02 -0.57,1.39 -0.66,0.03 -0.72,0.64 -0.73,0.26 -1.32,1.57 -0.49,-0.04 0,0 -0.55,-0.26 0,0 -1.21,-0.04 -0.15,-0.3 -0.24,0.29 -1.1,0.36 -0.16,0.73 -0.72,0.87 -0.76,0.16 -0.83,-0.41 -0.84,0.89 -1.37,-0.95 0,0 0.53,-0.67 -0.28,-0.9 0.19,-0.29 0.21,0.15 0.37,-0.36 -0.18,-0.67 0.42,-0.46 0.05,-0.37 0.17,0.09 -0.06,-0.29 0.28,-0.39 0.29,0 -0.07,-0.77 -0.39,-0.31 -0.62,-0.09 -0.25,-1.13 -1.09,-1.75 -1.55,-0.86 0.26,-0.07 -0.27,-0.38 0.26,-0.26 -0.25,-0.12 -0.08,-0.73 -0.32,-0.28 -0.29,-0.79 -0.8,-0.79 -1.54,-0.29 0.08,-0.31 -0.22,-0.31 -0.08,0.27 -0.33,-0.11 -0.18,0.29 -0.13,-0.28 -0.1,0.23 -0.3,-0.19 -0.1,0.19 -0.35,-0.33 -0.49,-0.03 -0.17,-0.33 -0.34,0.04 0.16,-0.44 -0.17,-0.27 -0.54,0.63 -0.09,-3.05 0.51,-1.52 -0.63,-0.1 -0.3,-0.59 0.3,-0.43 -0.01,-0.78 -1.12,-7.74 0,0 0.14,-0.2 1.13,0.21 1.76,-1.11 0.22,-0.43 0.66,0.16 1.45,-0.59 0.18,0.57 0.24,0.05 1.06,-0.27 0.36,-0.44 1.59,1.54 0.71,1.09 -0.09,0.74 0.6,0.52 0,0.25 0.78,0.45 0.45,0.12 0.62,0.04 0.44,-1.01 -0.19,-0.43 0.28,-0.29 0.02,-0.74 0.69,-0.11 0.48,-0.37 0.05,-0.53 2.27,-0.93 0.32,-0.45 0.54,-0.17 0.89,-0.62 0.02,-1.07 0.49,-0.42 -0.47,-0.79 -0.05,-1.13 0.35,-0.15 0.87,-1.82 0.05,-0.54 0.32,-0.79 0.2,-0.88 -0.54,-0.45 -0.36,-0.68 0.01,-0.29 0.76,-0.68 -0.06,-0.38 0.75,-0.88 -0.51,-1.16 0.31,-0.91 -0.18,-0.85 -0.19,-0.49 -0.31,-0.16 -0.17,-0.87 0.09,-1.28 0.55,-0.65 -1.21,-1.79 0.44,-0.8 -0.24,-0.55 -0.19,-0.61 0.15,-1.28 -0.16,-0.41 z"
                            id="tinh-dak-nong" data-id="tinh-dak-nong" data-link="{PROVINCE_LIST_606.link}"
                            data-tinh="{PROVINCE_LIST_606.title}" data-province="{PROVINCE_LIST_606.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 176.44847,729.36323 0.5,-1.14 0.19,0.05 0.55,-0.98 -0.26,-0.11 0.61,-1.36 0.35,0.16 0.25,-0.54 0.59,0.26 -0.09,0.31 1.83,1.98 0.49,-0.22 0.23,-0.52 0.49,-0.07 0.25,-0.78 0.6,0.23 -0.02,0.27 0.74,0.08 -0.72,0.77 0.61,0.56 1.23,-0.81 0.49,0.06 0.02,-0.26 0.5,-0.02 0.51,-0.88 1.88,0.94 0.86,0.19 0.1,-0.18 2.12,-0.38 1.34,-0.99 0,0 2.72,2.4 0,0 -0.7,0.71 0,0 -0.13,0.32 0,0 -0.41,0.52 0.05,0.41 -0.27,0.5 0.15,0.54 -0.97,2.98 0,0 -0.22,0.9 0,0 0.34,0.09 -0.62,2.95 -6.93,4.22 -2.12,1.85 -0.1,-0.45 -0.39,0.33 -0.54,0.06 -0.14,0.47 -0.6,0.34 -0.93,0.18 -0.1,-0.35 -0.45,0.11 -0.59,0.59 -0.02,0.3 -0.41,-0.5 -0.58,-0.12 -0.36,0.61 -0.35,-0.16 -0.28,0.53 -0.52,-0.03 -0.06,0.56 0,0 -0.58,-0.58 -0.7,0.13 -0.01,0.3 -0.35,-0.03 -0.1,0.28 -0.42,-0.11 -0.01,-0.26 -0.56,0.02 -0.22,0.46 -0.71,-0.13 0.12,-0.29 -0.37,-0.36 -0.43,0.53 -0.3,-0.18 -0.34,0.18 -0.19,-0.52 -0.31,0.1 -0.56,-0.3 -0.09,0.3 -0.54,0.12 -0.11,-0.49 -0.31,0.23 -0.56,-0.03 -0.17,0.52 -0.33,-0.1 -0.44,0.24 -0.38,-0.58 0,0 0.15,-0.97 0.72,-0.56 0.46,0.17 0.4,-1.1 -0.27,-0.19 0.27,-0.24 -0.69,-0.46 -1.21,0.06 0.14,-0.7 -0.86,0.2 -0.19,-0.39 -0.7,0 -0.06,-0.57 -0.49,-0.72 1.47,-0.61 0.8,-0.91 0.96,-0.31 0.16,-0.47 0.5,-0.3 0.46,0.06 0.14,-0.5 -0.4,-0.06 -0.11,-0.5 0.46,-0.04 -0.22,-0.09 0.06,-0.28 0.28,0.19 -0.13,-0.27 0.28,0.07 -0.08,-0.27 0.19,0.22 0.07,-0.36 0.19,0.12 -0.03,-0.22 0.58,-0.27 -0.31,-1.24 1.29,-0.34 3.38,-1.92 -0.23,-0.2 1.2,-2.61 z"
                            id="tinh-hau-giang" data-id="tinh-hau-giang" data-link="{PROVINCE_LIST_816.link}"
                            data-tinh="{PROVINCE_LIST_816.title}" data-province="{PROVINCE_LIST_816.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 174.51847,708.06323 0.96,0.93 1.94,1.14 2.52,2.45 1.03,1.73 0.26,0.98 0,0 0.18,0.21 0,0 1.07,0.93 0.65,0.91 1.15,0.75 0,0 5.32,3.5 3.09,4.39 0,0 -1.34,0.99 -2.12,0.38 -0.1,0.18 -0.86,-0.19 -1.88,-0.94 -0.51,0.88 -0.5,0.02 -0.02,0.26 -0.49,-0.06 -1.23,0.81 -0.61,-0.56 0.72,-0.77 -0.74,-0.08 0.02,-0.27 -0.6,-0.23 -0.25,0.78 -0.49,0.07 -0.23,0.52 -0.49,0.22 -1.83,-1.98 0.09,-0.31 -0.59,-0.26 -0.25,0.54 -0.35,-0.16 -0.61,1.36 0.26,0.11 -0.55,0.98 -0.19,-0.05 -0.5,1.14 0,0 -8.72,-8.08 -0.4,-0.47 -0.2,-0.98 -1.85,-2.09 -0.33,0.31 -0.39,-0.46 -0.29,0.25 -3.74,-3.29 0.26,-0.39 0,0 1.21,-1.61 0.34,-0.33 0.32,0.38 1.78,-2.02 0.27,0.43 0.33,-0.19 1.21,2.01 5.47,-1.86 -0.77,-1.75 2.63,-0.4 0.51,-0.68 z"
                            id="thanh-pho-can-tho" data-id="thanh-pho-can-tho" data-link="{PROVINCE_LIST_815.link}"
                            data-tinh="{PROVINCE_LIST_815.title}" data-province="{PROVINCE_LIST_815.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px"></path>
                        <path
                            d="m 295.36847,403.56323 0.07,-0.36 0,0 0.25,-0.26 -0.16,-0.42 0,0 0.57,-1 1.22,-0.95 0.17,-2.1 0.82,-0.42 0,0 -0.14,-0.91 0,0 -0.27,-0.27 0.53,-0.33 -0.09,-0.41 0.58,-0.14 0.16,-0.37 0.28,0.07 0.82,-0.56 0.96,0.96 1.37,0.46 0.71,-0.27 0.99,-1.07 0.89,0.48 0.49,0.61 1.12,0.13 1.16,0.66 0.28,-0.27 1.48,0.21 0.42,-0.35 1,0.29 1.93,-1.31 0.81,0.13 1.15,-0.39 0,0 0.02,0.6 -0.84,-0.04 -0.26,0.36 -0.92,0.18 -0.26,1.23 0.19,0.27 -0.29,0.1 -0.39,-0.22 -0.25,0.38 0.14,0.7 -0.84,0.28 0.01,0.87 0.56,0.26 -0.16,0.17 0.12,0.28 1.43,1.35 1.06,0.52 0.93,0.12 0.55,-0.33 0.44,-0.37 0.49,-0.03 0.45,-0.21 0.4,-0.55 -0.15,-0.53 -1.01,-0.55 -0.2,0.27 -0.24,-0.36 0.39,-0.34 0.46,-0.49 0.29,-0.54 0.65,-0.42 0.61,0.15 0.64,0.97 0.88,-0.13 0.94,0.76 0.23,-0.11 0.11,-0.93 0.41,-0.07 0.91,1.23 0,0.19 -0.88,0.04 -0.49,0.25 -0.24,0.87 -0.59,-0.52 -1.39,0.57 -0.4,-0.39 -0.4,0.27 -0.26,0.61 -0.1,1.09 0.22,1.17 1.99,4.12 0,0 -1,0.39 -0.08,-0.38 -0.37,0.07 -0.24,-0.48 -0.52,0 -0.85,0.67 0,0.25 -0.3,0.05 -0.11,0.52 -0.31,-0.06 -0.31,0.29 -0.42,-0.73 0.18,-0.29 -0.47,-0.17 -0.33,0.19 -0.34,-0.21 -0.28,1.37 -0.52,-0.11 -0.18,-0.52 -0.6,-0.09 -0.36,1.06 -0.46,0.16 -1.48,-0.94 -1.49,1.22 -0.66,-0.07 -0.4,-0.38 -1.01,-0.06 -1.09,0.26 -0.76,0.47 -1.01,0.14 -1.91,-1.62 0.34,-0.5 0.04,-0.52 1.25,-1.09 0.15,-1.27 -0.73,-0.44 -0.05,-0.43 -0.55,-0.46 0.15,-1.5 -1.13,0.37 -1.93,1.27 -0.44,-0.36 -0.06,-0.38 -0.94,-0.26 -0.61,-0.51 -1.17,0.09 -0.42,0.31 -0.75,-0.26 z"
                            id="thanh-pho-da-nang" data-id="thanh-pho-da-nang" data-link="{PROVINCE_LIST_501.link}"
                            data-tinh="{PROVINCE_LIST_501.title}" data-province="{PROVINCE_LIST_501.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px" class="{PROVINCE_LIST_501.current}">
                        </path>
                        <path d="m189.55847,114.06323 
                            0.17,-0.86 0.69,-0.56 1.44,0.5 0,0 0.07,0.03 0,0 0.09,0.15 0.38,-0.24 0.89,0.05 
                            0.16,0.53 -0.32,-0.14 -0.1,0.75 0.07,0.41 0.33,0.18 -0.18,0.07 0.12,0.3 0.25,0.04 
                            0.29,0.59 0.14,-0.14 1.11,0.19 0,0 0.37,0.09 -0.11,0.89 0.18,0.25 0.43,0.14 0.41,-0.22 
                            0,0 -0.06,0.49 0,0 0.82,0.16 -0.44,0.38 -0.01,0.91 0.83,1.31 -0.05,0.21 0,0 -0.23,-0.1 
                            -0.73,0.78 -0.67,-0.05 -0.13,0.38 0.07,0.16 0.28,-0.12 0.25,0.48 -0.44,1.31 0.33,0.03 
                            0.53,0.49 -0.35,0.8 0.32,0.37 -0.18,0.37 0.35,0.8 -0.1,0.41 -0.14,-0.11 -0.46,0.53 
                            0.15,0.38 0.42,-0.1 -0.13,0.21 0.68,0.61 0.3,1.22 0.91,-0.41 0.22,0.74 0.31,0.31 
                            1.32,0.18 0.3,-0.3 0.37,0.63 0.11,-0.09 -0.07,0.38 0.35,0.11 0.33,0.81 -0.41,0.63 
                            -0.27,0.07 0.13,0.22 -0.27,0.17 0.03,0.55 0.28,0.02 -0.6,0.26 0.13,0.36 0,0 -0.49,-0.06 
                            -0.36,0.26 -0.38,-0.14 -0.29,0.12 0.02,0.4 -0.54,-0.01 0.14,1.47 -0.49,-0.29 -0.86,0.04 
                            -0.35,-0.34 -0.85,-0.09 -0.58,1.19 -0.02,0.43 0.57,0.5 -0.35,0.6 0.25,0.42 -0.73,0.75 
                            0,0 -0.36,-0.2 -0.2,0.15 -0.49,-0.34 -0.2,0.32 -0.8,-0.03 -0.3,0.32 0.09,0.32 -0.82,-0.4 
                            -0.05,-0.41 -0.3,0.07 -0.02,0.4 -0.28,0.25 -0.14,-0.34 -0.46,0.04 -0.69,-0.55 -0.59,-0.16 
                            0,-0.62 0.52,-0.49 -0.68,-0.36 0.16,0 0.18,-0.75 0.48,-0.19 -0.61,-0.27 -0.12,-0.3 
                            0.52,-0.32 -0.25,-0.29 0.06,-0.34 -0.82,-0.13 -0.14,0.23 -0.61,0.01 -0.19,0.29 -0.25,-0.11 
                            -0.08,-0.43 -1.07,-0.39 0,-0.68 -0.26,-0.52 0.42,-0.31 0,-0.74 -0.26,-0.07 -0.04,-0.41 
                            -0.28,-0.16 0.06,-0.73 -0.6,-0.58 0.56,-0.56 -0.09,-0.93 0.8,-0.49 -0.34,-0.27 -0.16,-0.57 
                            0,0 0.19,0.05 0.61,-0.86 0.26,0.3 0.03,-0.17 0.75,-0.01 0.32,-0.47 -0.07,-1.92 0.61,0.04 
                            0.38,-0.29 -0.15,-0.76 -0.9,-0.52 -0.12,-0.46 -0.4,-0.01 -0.16,-0.49 -1.29,-0.6 -0.15,-0.26 
                            -0.13,-0.13 0.07,-0.46 -0.3,-0.13 0,-0.81 0.32,-0.19 -0.21,-0.19 0.38,-0.13 0.19,-0.65 
                            0.29,-0.02 0.05,-0.3 0.56,-0.14 1.2,-2.34 0.44,-0.3 -0.02,-0.93 z 
                            m-0.14,43.99 
                            -0.2,-0.58 -1.13,-1.27 -0.53,-0.04 -0.29,-0.32 -0.37,0.49 -0.62,-0.12 -0.22,-0.22 -0.15,-1.03 
                            -0.55,0 -0.22,0.29 -0.81,0 0,0 -0.28,-0.29 0,0 0.02,-0.49 0.28,-0.11 0.38,0.21 0.43,-0.52 
                            -0.41,-0.48 0.02,-1.25 -0.47,-0.42 0,0 -0.06,-0.18 0.17,0 0,0 0.26,-0.18 0,0 0.15,-0.23 
                            0,0 0.1,-0.59 -2.06,-2.75 0.01,-0.57 0.15,0.31 0.29,-0.1 -0.12,-2.09 -0.76,-0.02 0,0 -0.04,-0.18 
                            0,0 -0.21,-1.14 0.22,-0.27 0,0 -0.12,-0.11 0.32,0.1 0,0 0.26,-0.25 0,0 -0.08,-0.17 0,0 
                            -0.27,-0.23 0.06,-0.31 -0.21,0.04 -0.05,0.49 -0.75,-0.12 -0.39,-0.38 -0.66,-0.21 -0.02,0.6 -0.85,0.19 
                            0.04,-0.46 0.41,-0.48 -0.17,-0.74 -0.25,0.01 -0.24,-0.34 0,0 -0.06,-0.18 0,0 -0.77,-0.74 -0.34,-0.11 
                            -0.4,0.31 -0.09,-0.24 0.86,-0.47 -0.23,-0.5 0.23,-0.16 -0.25,-0.16 1.31,-0.63 -0.07,-0.23 0.3,-0.08 
                            -0.16,-0.33 -1.09,0.21 -0.43,-0.14 -0.32,0.28 -0.39,-0.29 -0.7,0.5 -0.64,-0.22 -0.57,-0.46 0.67,-0.45 
                            0.05,-0.54 0.36,-0.24 0,0 0.22,-0.04 0,0 0.09,-0.61 -0.91,-0.72 0.04,-0.16 -0.62,-0.05 -0.24,-0.65 
                            -0.34,0.07 -1.58,-1.2 -1.25,-0.21 -0.91,0.32 -0.35,-0.36 -0.91,0.21 -0.32,-0.19 0.03,-0.36 -0.68,0.59 
                            -1,0.34 -0.36,-0.35 -0.46,0.02 -0.27,0.31 -0.41,-0.06 0.14,-0.48 -0.2,-0.22 -0.38,0.52 0,0 -0.66,-0.55 
                            -0.56,-1.74 1.18,-2.22 0.16,-1.11 -1.54,-2.3 -0.16,-0.59 0.86,-1.43 1.4,-0.65 0,0 0.34,0.08 0.35,-0.32 
                            0,0 -0.25,-0.11 0.81,-2.33 0.18,-2.07 -0.15,-0.74 0.45,-0.47 0.26,-0.14 0.25,1.05 1.2,-0.54 1.94,0.88 
                            0.5,1.28 0,0 0.13,1.08 0.79,1.4 0.44,2.41 1.18,0.87 1.52,0.29 1.9,-0.12 3.37,-0.96 0.44,0.36 
                            0.03,0.56 1.61,0.58 2.06,0.4 1.17,0.48 0.38,0.55 0,0 0.16,0.57 0.34,0.27 -0.8,0.5 0.09,0.92 
                            -0.56,0.56 0.6,0.59 -0.06,0.72 0.28,0.16 0.05,0.42 0.26,0.07 0,0.74 -0.42,0.31 0.26,0.52 0,0.68 
                            1.08,0.39 0.08,0.43 0.25,0.11 0.19,-0.29 0.62,0 0.14,-0.23 0.82,0.13 -0.07,0.34 0.25,0.29 -0.51,0.32 
                            0.12,0.3 0.61,0.27 -0.49,0.19 -0.18,0.75 -0.16,-0.01 0.69,0.37 -0.52,0.49 0,0.62 0.59,0.16 
                            0.68,0.55 0.46,-0.04 0.14,0.34 0.28,-0.26 0.02,-0.39 0.3,-0.07 0.05,0.41 0.82,0.4 -0.09,-0.32 
                            0.3,-0.32 0.8,0.04 0.2,-0.32 0.48,0.34 0.2,-0.16 0.36,0.2 0,0 -0.19,0.35 0.09,0.34 
                            1.73,1.01 0.29,0.52 -0.14,0.75 -0.85,0.52 -0.3,0.67 0.41,1.77 0.54,0.64 1.61,0.41 0.34,0.33 
                            0.14,2.15 -0.27,0.51 0.46,1.35 1.55,-0.21 0,0 -0.38,0.39 0.13,0.28 -0.33,0.15 0.31,0.65 
                            -0.31,-0.01 0.07,-0.22 -0.43,0.09 -0.33,0.37 -0.06,0.46 -0.97,0.33 -0.01,0.51 -0.7,-0.25 
                            0.09,-1.19 -0.18,-0.31 -1.23,0.34 -0.73,0.59 -0.48,-0.23 0,0.97 -0.91,0.49 -1.07,-0.1 -0.93,-0.8 
                            -0.91,-0.28 -0.14,0.23 0.76,0.7 -1.01,0.74 -0.16,0.36 0.18,0.54 -0.49,0.45 0.31,0.3 -0.54,0.62 
                            -0.27,1.36 -1.59,0.08z" id="thanh-pho-ha-noi" data-id="thanh-pho-ha-noi"
                            data-link="{PROVINCE_LIST_101.link}" data-tinh="{PROVINCE_LIST_101.title}"
                            data-province="{PROVINCE_LIST_101.id}" fill="#0685d6" class="{PROVINCE_LIST_101.current}">
                        </path>
                        <path
                            d="m 246.09847,143.32323 1.77,1.65 -1.04,0.52 -0.32,-0.18 -1.42,0.32 -0.49,-1.4 1.5,-0.91 z m 4.05,-2.49 0.23,-0.06 0.16,0.27 0.37,1.09 0.36,-0.12 0.46,-0.1 1.15,-0.24 0.4,0.06 1.33,-0.06 0.32,0.62 0.33,0.15 0.02,0.78 0.26,0.2 0.48,-0.09 0.09,0.26 -0.23,0.23 0.58,0.32 0.3,-0.38 0.54,0.38 0.32,0.23 0.75,0.54 0.17,0.88 -0.74,-0.23 -0.01,0.34 0.3,0.09 0.04,0.38 -0.91,-0.01 -0.49,-0.46 -0.12,0.46 -0.87,0.38 0.01,0.29 -0.74,0.08 0.39,0.51 1.16,0.45 -0.04,1.26 -0.48,-0.15 -0.16,0.31 -0.1,0.23 -0.17,0.38 -0.55,-0.08 0.38,-0.61 -0.2,-0.21 -0.3,0.22 -0.3,0.14 -0.64,-0.18 -0.45,-0.32 0.1,-0.29 -0.1,-0.21 -0.74,-0.68 -0.17,0.62 0.27,0.46 -0.12,0.21 -0.58,-0.54 -0.5,-0.75 -0.3,-0.43 0.1,-0.29 -1.23,-1.67 -1.76,-0.34 0.12,-0.45 -0.76,-1.09 0.98,-1.36 0.68,-0.21 0.27,0.52 0.42,-0.17 0.03,-0.45 -0.43,-0.37 0.44,-0.4 -0.12,-0.34 z m -17.59,-7.99 0.81,0.53 1.31,-0.43 2.84,1.1 0,0 1.18,0.29 0.86,-0.04 0.42,0.25 0.51,0.79 0.14,2.06 -0.06,1.45 -0.25,0.69 -0.98,1.38 -0.35,-0.55 -0.25,-0.03 -0.38,0.36 -0.47,-0.42 -0.65,0.57 0.58,0.12 1,0.89 1.76,0.72 1.04,0.97 1.29,0.48 -0.46,0.54 -0.69,0.18 -1.24,-0.94 -0.31,0.1 -0.1,0.14 0.72,1.15 -0.9,1.34 1.1,0.86 0.81,1.84 0.64,0.15 -0.55,1.17 0.39,0.35 -0.16,0.55 -0.23,-0.12 0.07,-0.28 -0.43,-0.2 -0.43,-0.34 -0.48,-0.63 -2.21,1.62 -1.36,0.09 0.32,0.74 -0.61,0.97 0.2,0.28 -2.1,1.64 -0.57,-0.15 0,0 -0.59,-0.39 -1.18,-0.03 -0.53,-0.72 -0.79,0.65 -1.09,0.44 0,0 -0.32,-0.33 0,0 -0.64,-0.03 -0.28,0.24 -0.09,0.8 -0.43,0.26 0,0 -0.64,0.07 0,0 -0.2,-0.49 -1.17,-0.29 -0.58,0.95 -0.53,0.07 -0.29,-0.85 -0.83,-0.36 0.25,-1.04 -0.87,-1.29 -1.96,-1.44 0.73,-1.02 -0.12,-0.3 0.25,-0.26 -0.39,-0.27 -0.09,-0.36 0,0 0.27,-0.38 0.61,0.1 0.67,0.74 0.55,-0.9 0.25,0.3 -0.06,0.47 0.44,-0.02 0.25,-0.46 -0.65,-0.65 0.7,-0.03 0.4,-0.58 0.71,0.18 0.59,-0.54 -0.3,-0.14 0.29,-0.57 -0.08,-0.57 -0.56,-0.9 0.74,-0.48 0.42,0.41 0.4,-0.92 -0.24,-0.26 -1.18,0.07 0.41,-1.08 -1,-0.98 0.72,-0.5 0.44,0.81 0.54,-0.6 0.73,0.09 -0.06,0.51 0.3,0.07 0.35,-0.82 0.85,-0.38 0.01,-0.18 -0.4,-0.11 0.18,-0.34 -0.45,-0.14 0.24,-0.28 -0.3,-0.32 0.11,-0.11 -0.68,-0.41 0.03,-0.42 0.3,0.2 0.44,-0.53 0.36,0.09 -0.32,-0.2 0.02,-1.11 0.19,-0.23 0.48,-0.01 0.01,0.77 0.18,0.13 0.46,-0.35 -0.03,-0.79 0.3,0 0.28,0.4 -0.43,0.47 0.32,0.25 0.8,-0.94 0,0 0.03,-0.34 0,0 -0.38,-0.51 0.01,-0.61 -0.96,-0.33 -0.59,-0.77 0.01,-0.47 0.5,-0.95 0.44,0.47 0.26,-0.31 0.59,0.08 1.1,-0.33 z"
                            id="thanh-pho-hai-phong" data-id="thanh-pho-hai-phong" data-link="{PROVINCE_LIST_103.link}"
                            data-tinh="{PROVINCE_LIST_103.title}" data-province="{PROVINCE_LIST_103.id}" fill="#0685d6"
                            stroke="rgb(247, 247, 247)" strokewidth="0.621318px">
                        </path>
                        <path
                            d="m 246.04847,700.96323 -1,0.35 0.32,0.39 -0.19,1.84 -0.66,0.73 -0.36,0.12 -0.22,-0.16 -0.17,-0.54 -1.5,-1.25 -0.52,0.49 0,0 -1.37,-1.62 -1.36,-2.29 0,0 0.7,-1.49 -0.34,-1.9 0.19,-0.76 -0.53,-0.06 0.2,-0.27 -0.14,-0.21 -0.42,0.33 -0.44,-0.86 -0.07,-0.5 0.25,-0.3 0.47,0.09 -0.27,-0.33 0.22,-0.35 -0.14,-0.28 0.22,-0.03 -0.13,-0.34 -0.45,-0.56 -1.11,-0.42 -0.51,0.28 -0.28,-0.42 -0.28,0.56 -0.66,0.33 0,0 -0.09,-0.03 0,0 -0.48,-0.17 -0.17,0.55 -0.36,-0.09 -0.23,0.44 -0.41,0.09 -0.22,-0.09 -0.03,-0.81 -0.42,0.04 0.17,-0.34 -0.97,-0.61 -0.29,0.45 -1.06,-0.27 -0.15,0.35 -0.4,-0.4 -0.62,0.36 -0.07,-0.39 -0.36,-0.04 0.22,-0.53 -0.14,-0.92 -0.88,-0.14 -0.35,-1.08 0.13,-0.48 -0.49,-0.32 -0.58,0.34 -0.4,-0.5 -0.74,-0.17 -1.23,-1.4 2.1,-1.7 1.53,-5.91 -4.14,-2.14 -2.16,-1.67 -2.03,0.21 -0.85,-1.16 0,0 0.29,-0.25 1.35,-0.11 1.33,-0.52 -0.28,-0.72 0.24,-0.55 0.41,-0.15 0.19,-0.67 -0.17,-0.73 0.23,-0.21 0.03,-1.36 0.21,0 0.02,-0.35 0.57,-0.83 0.28,-1.08 0.2,-0.04 0,0 0.3,-0.37 0,0 0.03,-0.48 -0.3,0.06 0.01,-0.33 0.6,-0.13 0.39,0.27 -0.21,0.38 0.26,0.24 1.85,0.02 0.45,0.89 0.57,0.25 -0.03,0.78 -0.53,0.3 0.32,0.53 0.59,-0.02 -0.52,0.89 0.48,0.77 0.96,0.7 0.8,0.04 0.31,-0.5 0.74,0.96 0.65,-0.47 0.21,0.3 -0.42,0.55 0.13,0.7 1.13,0.37 -0.18,1.01 0.47,0.45 0.64,0.2 0.55,-0.17 0.15,2.5 0.83,0.61 0.73,-0.13 0.28,0.23 0.44,1.17 -0.24,0.89 0.58,0.66 0.64,0.12 0.12,-0.37 0.42,0.05 -0.12,-0.24 -0.47,-0.06 0.29,-0.19 -0.02,-0.35 -0.23,-0.1 0.24,-0.35 0.49,0.31 0.33,-0.04 -0.07,0.29 0.35,-0.04 -0.2,0.31 0.82,0.13 0.09,0.16 -0.31,0.17 0.3,0.43 0.5,-0.17 -0.09,-0.78 0.44,-0.64 0.66,0.67 -0.02,0.68 0.41,0.05 0.02,-0.25 1.04,-0.14 0.76,-0.67 0.15,-0.42 0.73,-0.16 0,0 0.19,0.75 0.38,0.44 -0.22,0.72 0.39,0.93 1.19,1.31 0.23,0.7 -0.89,0.91 0.49,0.89 -0.16,0.36 -0.28,0.04 -0.64,-0.52 -0.42,-0.03 -1.05,0.82 0.02,-0.93 0,0 -0.31,-0.19 0,0 -0.66,0.51 -0.28,0.57 -1.49,0.72 -1.03,1.3 0.08,1.28 1.34,1.25 0,0 0.17,-0.12 1.26,0.82 0.95,1.58 0.58,0.03 0.15,-0.48 1.02,-0.14 0.7,-0.47 0.52,0.1 1.23,1.25 0.28,0.8 0.54,-0.06 0.62,0.42 0.44,0.18 0.97,0.17 1.15,0.73 0.1,-0.51 0.1,-0.27 0.18,-0.41 0.88,-0.29 0,0 0.49,0.33 0.45,0.95 -0.91,1.58 0.23,0.51 0.67,0.25 -0.61,0.72 -0.48,-0.28 0.03,0.35 0.19,0.28 -0.26,0.42 -0.68,-0.09 -0.69,0.57 -0.13,0.41 0.16,0.58 -0.39,0.45 -0.2,0.76 -1.16,-0.53 0.55,-0.5 -0.35,-0.22 -0.82,0.67 0.23,1.61 0.28,0.38 0.38,0.42 1.36,0.69 -0.26,0.42 -2.93,1.26 -0.85,0.26 -1.13,0.02 0.23,-0.61 -0.94,-2.1 0.77,-1.02 0.25,-1.04 -0.88,-0.28 0.21,0.63 z"
                            id="thanh-pho-ho-chi-minh" data-id="thanh-pho-ho-chi-minh"
                            data-link="{PROVINCE_LIST_701.link}" data-tinh="{PROVINCE_LIST_701.title}"
                            data-province="{PROVINCE_LIST_701.id}" fill="#0685d6" stroke="rgb(247, 247, 247)"
                            strokewidth="0.621318px" class="{PROVINCE_LIST_701.current}"></path>

                    </svg>
                    <svg class="absolute custom-svg top-96 left-96 vietnammap" width="29" height="28"
                        viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path id="quan-dao-hoang-sa"
                            d="M22.1008 0.300049V0.900049L23.1008 1.90005L23.0008 2.80005L23.7008 3.10005L23.1008 3.50005V4.00005L22.5008 4.40005L21.9008 4.30005L21.4008 3.10005L21.5008 2.00005L21.1008 1.00005L21.2008 0.400049L22.1008 0.300049ZM13.5008 26.8L12.3008 26.6L11.9008 26.9L11.4008 26.5L11.0008 25.3L11.6008 24.4L12.0008 24.5V24.2001L11.7008 23.5H12.4008H12.7008L13.1008 23.8L13.5008 23.9L13.7008 24.3L14.2008 24.7001L13.9008 25L14.0008 25.3L14.3008 26L14.7008 26.3L14.9008 27.2001L14.0008 27.8L13.5008 27.4V26.8ZM1.90078 19.5L3.90078 20.9L4.20078 22L4.70078 22.3L5.50078 23.5L2.00078 23.4L1.20078 23.8V22.9L0.500781 22.1L0.300781 20.9L0.900781 19.4L1.90078 19.5ZM12.9008 10.2L13.1008 11.1L13.7008 11.2L14.4008 12.1L16.1008 13.4L15.3008 14.3L15.5008 15.2L14.2008 15.9V16.3L13.0008 16.4L12.9008 17L12.0008 17.3L12.9008 15.9L14.1008 15L13.4008 13.7L11.9008 13.1L12.1008 12.3L11.5008 12L12.1008 10.8L12.9008 10.2ZM21.7008 8.30005L22.2008 8.60005L22.5008 10.3L21.8008 10L21.3008 10.4L20.8008 10.6L20.5008 11.3L19.7008 11.9V12.4L19.5008 12.8L18.8008 12.9L18.9008 13.9H18.0008L17.4008 14.4L17.3008 13.9L17.8008 13.3L17.3008 12.8L17.5008 12L18.1008 11L18.5008 10.9L19.0008 9.80005L19.7008 9.10005L20.2008 8.50005L21.0008 9.00005L21.7008 8.30005ZM27.3008 7.30005L27.5008 7.50005L27.6008 8.30005L27.3008 8.20005L27.1008 8.40005L26.9008 8.50005L26.8008 8.80005L26.4008 9.00005V9.20005L26.3008 9.40005H26.0008V9.90005H25.6008L25.3008 10.1L25.2008 10L25.4008 9.70005L25.2008 9.40005L25.3008 9.00005L25.6008 8.50005H25.8008L26.0008 8.00005L26.3008 7.70005L26.5008 7.40005L27.0008 7.60005L27.3008 7.30005ZM17.8008 5.10005L18.0008 5.30005L18.1008 6.10005L17.9008 6.00005L17.7008 6.20005L17.5008 6.30005L17.4008 6.60005L17.0008 6.90005V7.10005L16.9008 7.30005H16.6008V7.80005H16.2008L15.9008 8.00005L15.8008 7.80005L16.0008 7.50005L15.8008 7.20005V7.00005L16.1008 6.50005H16.3008L16.5008 6.00005L16.8008 5.70005L17.0008 5.40005L17.4008 5.70005L17.8008 5.10005ZM9.20078 12.8L9.60078 13.1L9.80078 14.5L9.40078 14.3L9.00078 14.7L8.60078 14.9L8.40078 15.5L7.70078 16V16.4L7.60078 16.7001L7.00078 16.8L7.10078 17.7001H6.40078L5.90078 18.1L5.80078 17.7001L6.20078 17.2001L5.80078 16.7001L6.00078 16L6.50078 15.1L6.90078 15L7.20078 14.1L7.80078 13.5L7.90078 13L8.60078 13.5L9.20078 12.8ZM18.4008 14.7L18.6008 15.6L19.2008 15.7L19.9008 16.6L21.6008 17.9L20.8008 18.8L21.0008 19.7001L19.7008 20.4V20.8L18.5008 20.9L18.3008 21.6L17.4008 21.9L18.3008 20.5L19.5008 19.6L18.8008 18.3L17.3008 17.7001L17.5008 16.9L16.9008 16.5L17.5008 15.3L18.4008 14.7ZM27.1008 12.8L27.6008 13.1L27.9008 14.8L27.4008 14.5L26.9008 14.9L26.4008 15.1L26.0008 15.8L25.2008 16.4V16.9L25.0008 17.2001L24.3008 17.3L24.4008 18.3H23.5008L23.0008 18.9L22.9008 18.4L23.4008 17.8L22.9008 17.3L23.1008 16.5L23.7008 15.5L24.1008 15.4L24.5008 14.3L25.2008 13.6L25.7008 13L26.5008 13.5L27.1008 12.8ZM8.50078 19.1L8.70078 19.7001L9.20078 19.8L9.70078 20.4L10.9008 21.4L10.3008 22L10.5008 22.6L9.50078 23.1V23.4L8.60078 23.5L8.40078 24L7.70078 24.3L8.30078 23.3L9.20078 22.6L8.70078 21.6L7.50078 21.2001L7.60078 20.6L7.20078 20.3L7.70078 19.4L8.50078 19.1ZM15.0008 17.7001L15.3008 17.9L15.5008 19.1L15.1008 19L14.7008 19.3L14.3008 19.5L14.2008 20L13.6008 20.4V20.7001L13.5008 21L13.0008 21.1V21.9H12.4008L12.0008 22.3L11.9008 22L12.2008 21.6L11.9008 21.2001L12.1008 20.6L12.5008 19.8L12.8008 19.7001L13.1008 18.9L13.6008 18.4L14.0008 17.9L14.6008 18.3L15.0008 17.7001ZM19.4008 21.6L19.6008 22.2001L20.1008 22.3L20.6008 22.9L21.8008 23.9L21.2008 24.5L21.4008 25.1L20.4008 25.6V25.9L19.5008 26L19.3008 26.5L18.6008 26.8L19.2008 25.8L20.1008 25.1L19.6008 24.1L18.4008 23.7001L18.5008 23.1L18.3008 23L18.8008 22.1L19.4008 21.6ZM25.9008 20.2001L26.2008 20.4L26.4008 21.6L26.0008 21.5L25.6008 21.8L25.3008 22L25.1008 22.5L24.5008 22.9V23.2001L24.4008 23.5L23.9008 23.6V24.4H23.3008L22.9008 24.8L22.8008 24.5L23.1008 24.1L22.8008 23.7001L23.0008 23.1L23.4008 22.3L23.7008 22.2001L24.0008 21.4L24.5008 20.9L24.9008 20.4L25.5008 20.8L25.9008 20.2001Z"
                            fill="#0685d6" data-id="thanh-pho-da-nang" data-link="{PROVINCE_LIST_501.link}"
                            data-tinh="{LANG.hoangsa}" data-province="{PROVINCE_LIST_501.id}"
                            stroke="rgb(247, 247, 247)" stroke-width="0.5" strokewidth="0.621318px"></path>
                    </svg>
                    <svg class="absolute customSVG bottom-28 left-96 vietnammap ml-6" width="29" height="28"
                        viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path id="quan-dao-truong-sa" data-id="tinh-khanh-hoa"
                            d="M22.1008 0.300049V0.900049L23.1008 1.90005L23.0008 2.80005L23.7008 3.10005L23.1008 3.50005V4.00005L22.5008 4.40005L21.9008 4.30005L21.4008 3.10005L21.5008 2.00005L21.1008 1.00005L21.2008 0.400049L22.1008 0.300049ZM13.5008 26.8L12.3008 26.6L11.9008 26.9L11.4008 26.5L11.0008 25.3L11.6008 24.4L12.0008 24.5V24.2001L11.7008 23.5H12.4008H12.7008L13.1008 23.8L13.5008 23.9L13.7008 24.3L14.2008 24.7001L13.9008 25L14.0008 25.3L14.3008 26L14.7008 26.3L14.9008 27.2001L14.0008 27.8L13.5008 27.4V26.8ZM1.90078 19.5L3.90078 20.9L4.20078 22L4.70078 22.3L5.50078 23.5L2.00078 23.4L1.20078 23.8V22.9L0.500781 22.1L0.300781 20.9L0.900781 19.4L1.90078 19.5ZM12.9008 10.2L13.1008 11.1L13.7008 11.2L14.4008 12.1L16.1008 13.4L15.3008 14.3L15.5008 15.2L14.2008 15.9V16.3L13.0008 16.4L12.9008 17L12.0008 17.3L12.9008 15.9L14.1008 15L13.4008 13.7L11.9008 13.1L12.1008 12.3L11.5008 12L12.1008 10.8L12.9008 10.2ZM21.7008 8.30005L22.2008 8.60005L22.5008 10.3L21.8008 10L21.3008 10.4L20.8008 10.6L20.5008 11.3L19.7008 11.9V12.4L19.5008 12.8L18.8008 12.9L18.9008 13.9H18.0008L17.4008 14.4L17.3008 13.9L17.8008 13.3L17.3008 12.8L17.5008 12L18.1008 11L18.5008 10.9L19.0008 9.80005L19.7008 9.10005L20.2008 8.50005L21.0008 9.00005L21.7008 8.30005ZM27.3008 7.30005L27.5008 7.50005L27.6008 8.30005L27.3008 8.20005L27.1008 8.40005L26.9008 8.50005L26.8008 8.80005L26.4008 9.00005V9.20005L26.3008 9.40005H26.0008V9.90005H25.6008L25.3008 10.1L25.2008 10L25.4008 9.70005L25.2008 9.40005L25.3008 9.00005L25.6008 8.50005H25.8008L26.0008 8.00005L26.3008 7.70005L26.5008 7.40005L27.0008 7.60005L27.3008 7.30005ZM17.8008 5.10005L18.0008 5.30005L18.1008 6.10005L17.9008 6.00005L17.7008 6.20005L17.5008 6.30005L17.4008 6.60005L17.0008 6.90005V7.10005L16.9008 7.30005H16.6008V7.80005H16.2008L15.9008 8.00005L15.8008 7.80005L16.0008 7.50005L15.8008 7.20005V7.00005L16.1008 6.50005H16.3008L16.5008 6.00005L16.8008 5.70005L17.0008 5.40005L17.4008 5.70005L17.8008 5.10005ZM9.20078 12.8L9.60078 13.1L9.80078 14.5L9.40078 14.3L9.00078 14.7L8.60078 14.9L8.40078 15.5L7.70078 16V16.4L7.60078 16.7001L7.00078 16.8L7.10078 17.7001H6.40078L5.90078 18.1L5.80078 17.7001L6.20078 17.2001L5.80078 16.7001L6.00078 16L6.50078 15.1L6.90078 15L7.20078 14.1L7.80078 13.5L7.90078 13L8.60078 13.5L9.20078 12.8ZM18.4008 14.7L18.6008 15.6L19.2008 15.7L19.9008 16.6L21.6008 17.9L20.8008 18.8L21.0008 19.7001L19.7008 20.4V20.8L18.5008 20.9L18.3008 21.6L17.4008 21.9L18.3008 20.5L19.5008 19.6L18.8008 18.3L17.3008 17.7001L17.5008 16.9L16.9008 16.5L17.5008 15.3L18.4008 14.7ZM27.1008 12.8L27.6008 13.1L27.9008 14.8L27.4008 14.5L26.9008 14.9L26.4008 15.1L26.0008 15.8L25.2008 16.4V16.9L25.0008 17.2001L24.3008 17.3L24.4008 18.3H23.5008L23.0008 18.9L22.9008 18.4L23.4008 17.8L22.9008 17.3L23.1008 16.5L23.7008 15.5L24.1008 15.4L24.5008 14.3L25.2008 13.6L25.7008 13L26.5008 13.5L27.1008 12.8ZM8.50078 19.1L8.70078 19.7001L9.20078 19.8L9.70078 20.4L10.9008 21.4L10.3008 22L10.5008 22.6L9.50078 23.1V23.4L8.60078 23.5L8.40078 24L7.70078 24.3L8.30078 23.3L9.20078 22.6L8.70078 21.6L7.50078 21.2001L7.60078 20.6L7.20078 20.3L7.70078 19.4L8.50078 19.1ZM15.0008 17.7001L15.3008 17.9L15.5008 19.1L15.1008 19L14.7008 19.3L14.3008 19.5L14.2008 20L13.6008 20.4V20.7001L13.5008 21L13.0008 21.1V21.9H12.4008L12.0008 22.3L11.9008 22L12.2008 21.6L11.9008 21.2001L12.1008 20.6L12.5008 19.8L12.8008 19.7001L13.1008 18.9L13.6008 18.4L14.0008 17.9L14.6008 18.3L15.0008 17.7001ZM19.4008 21.6L19.6008 22.2001L20.1008 22.3L20.6008 22.9L21.8008 23.9L21.2008 24.5L21.4008 25.1L20.4008 25.6V25.9L19.5008 26L19.3008 26.5L18.6008 26.8L19.2008 25.8L20.1008 25.1L19.6008 24.1L18.4008 23.7001L18.5008 23.1L18.3008 23L18.8008 22.1L19.4008 21.6ZM25.9008 20.2001L26.2008 20.4L26.4008 21.6L26.0008 21.5L25.6008 21.8L25.3008 22L25.1008 22.5L24.5008 22.9V23.2001L24.4008 23.5L23.9008 23.6V24.4H23.3008L22.9008 24.8L22.8008 24.5L23.1008 24.1L22.8008 23.7001L23.0008 23.1L23.4008 22.3L23.7008 22.2001L24.0008 21.4L24.5008 20.9L24.9008 20.4L25.5008 20.8L25.9008 20.2001Z"
                            fill="#0685d6" data-link="{PROVINCE_LIST_511.link}" data-tinh="{LANG.truongsa}"
                            data-province="{PROVINCE_LIST_511.id}" stroke="rgb(247, 247, 247)" stroke-width="0.5"
                            strokewidth="0.621318px"></path>
                    </svg>
                    <!-- END: province_list -->
                </div>
            </div>
            <div class="col-lg-12 col-md-10 col-sm-24 col-xs-24">
                <label class="control-label">{LANG.diaphuong}</label>
                <select id="provinceSelector" class="selector_value form-control">
                    <option value="{LANG.linktinhthanh}">{LANG.tinhthanh}</option>
                    <!-- BEGIN: part -->
                    <option value="{PROVINCE.link}" {PROVINCE.selected}>{PROVINCE.title}</option>
                    <!-- END: part -->
                </select>
                <div id="tinh-thanh-pho-content" class="grid-content">
                    <!-- BEGIN: type -->
                    <!-- BEGIN: images -->
                    <br>
                    <div class="img">
                        <img src="{PROVINCETYPE.thumb}" alt="{PROVINCETYPE.title}" />
                    </div>
                    <!-- END: images -->
                    <!-- BEGIN: description -->
                    <br>
                    <p style="text-align:justify">{PROVINCETYPE.description}</p>
                    <!-- END: description -->
                    <!-- END: type -->
                    <!-- BEGIN: count -->
                    <br>
                    <div class="grid-col-top grid-col grid-col-p">
                        <div class="item {class_total_dadtpt}">
                            <div class="tabone">
                                <a href="#duandtpt">
                                    <svg class="svg-icon" width="44" height="44" viewBox="0 0 44 44" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M6 3H3V42C3 42.7956 3.31607 43.5587 3.87868 44.1213C4.44129 44.6839 5.20435 45 6 45H45V42H6V3Z"
                                            fill="white"></path>
                                        <path
                                            d="M45 13.5H34.5V16.5H39.885L28.5 27.885L22.065 21.435C21.9256 21.2944 21.7597 21.1828 21.5769 21.1067C21.3941 21.0305 21.198 20.9913 21 20.9913C20.802 20.9913 20.6059 21.0305 20.4231 21.1067C20.2403 21.1828 20.0744 21.2944 19.935 21.435L9 32.385L11.115 34.5L21 24.615L27.435 31.065C27.5744 31.2056 27.7403 31.3172 27.9231 31.3933C28.1059 31.4695 28.302 31.5087 28.5 31.5087C28.698 31.5087 28.8941 31.4695 29.0769 31.3933C29.2597 31.3172 29.4256 31.2056 29.565 31.065L42 18.615V24H45V13.5Z"
                                            fill="white"></path>
                                    </svg>
                                    <div class="info">
                                        <span class=" font-span">{PROVINCETYPE.total_dadtpt}</span>
                                        <br>
                                        <h2>{LANG.duandtpt}</h2> <!-- Dự án đầu tư phát triển -->
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="item bg_blue {class_total_plans_overall}">
                            <div class="tabone">
                                <a href="#plans_overall">
                                    <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                        xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                        viewBox="0,0,256,256">
                                        <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                            stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                            stroke-dasharray="" stroke-dashoffset="0" font-family="none"
                                            font-weight="none" font-size="none" text-anchor="none"
                                            style="mix-blend-mode: normal">
                                            <g transform="scale(5.12,5.12)">
                                                <path
                                                    d="M7.6875,0c-0.90234,0 -1.68359,0.66406 -1.875,1.625l-5.15625,30.59375c0.42188,-0.14062 0.86328,-0.21875 1.34375,-0.21875h46c0.48828,0 0.94922,0.10547 1.375,0.25l-5.09375,-30.65625c-0.18359,-0.92969 -0.96875,-1.59375 -1.875,-1.59375zM25,6.28125c1.02344,0 1.84375,0.84766 1.84375,1.875c0,0.72656 -0.42578,1.32031 -1.03125,1.625l4.125,8.28125l4.71875,-3.3125c-0.21094,-0.30078 -0.375,-0.63672 -0.375,-1.03125c0,-1.02734 0.85156,-1.875 1.875,-1.875c1.02344,0 1.84375,0.85156 1.84375,1.875c0,1.02734 -0.82031,1.84375 -1.84375,1.84375c-0.125,0 -0.25781,-0.03906 -0.375,-0.0625l-1.78125,7.5h-18l-1.78125,-7.5c-0.12109,0.02344 -0.25,0.0625 -0.375,0.0625c-1.02734,0 -1.84375,-0.82031 -1.84375,-1.84375c0,-1.02734 0.82031,-1.875 1.84375,-1.875c1.02344,0 1.875,0.84766 1.875,1.875c0,0.39844 -0.16406,0.73047 -0.375,1.03125l4.6875,3.3125l4.15625,-8.28125c-0.60547,-0.30469 -1.03125,-0.89844 -1.03125,-1.625c0,-1.02734 0.82031,-1.875 1.84375,-1.875zM17,25h16c0.55469,0 1,0.44531 1,1c0,0.55469 -0.44531,1 -1,1h-16c-0.55078,0 -1,-0.44531 -1,-1c0,-0.55469 0.44922,-1 1,-1zM2,34c-1.16016,0 -2,0.83984 -2,2v12c0,1.16016 0.83984,2 2,2h46c1.16016,0 2,-0.83984 2,-2v-12c0,-1.16016 -0.83984,-2 -2,-2zM40,40c1.10156,0 2,0.89844 2,2c0,1.10156 -0.89844,2 -2,2c-1.10156,0 -2,-0.89844 -2,-2c0,-1.10156 0.89844,-2 2,-2z">
                                                </path>
                                            </g>
                                        </g>
                                    </svg>
                                    <div class="info">
                                        <span class="font-span">{PROVINCETYPE.total_plans_overall}</span>
                                        <br>
                                        <h2>{LANG.pagetitle_khttlcnt}</h2> <!-- Kế hoạch tổng thể lựa chọn nhà thầu -->
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="item bg_red {class_total_plans}">
                            <div class="tabone">
                                <a href="#plan">
                                    <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                        xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                        viewBox="0,0,256,256">
                                        <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                            stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                            stroke-dasharray="" stroke-dashoffset="0" font-family="none"
                                            font-weight="none" font-size="none" text-anchor="none"
                                            style="mix-blend-mode: normal">
                                            <g transform="scale(5.12,5.12)">
                                                <path
                                                    d="M34.19,15.81l-1.41,1.41l-15.56,15.56l-1.41,1.41c2.35,2.35 5.61,3.81 9.19,3.81c7.17,0 13,-5.83 13,-13c0,-3.58 -1.46,-6.84 -3.81,-9.19zM41,4h-32c-2.76,0 -5,2.24 -5,5v32c0,2.76 2.24,5 5,5h32c2.76,0 5,-2.24 5,-5v-32c0,-2.76 -2.24,-5 -5,-5zM44,41c0,1.65 -1.35,3 -3,3h-32c-0.82,0 -1.58,-0.34 -2.12,-0.88l8.93,-8.93c-5.08,-5.07 -5.08,-13.31 0,-18.38c5.07,-5.08 13.31,-5.08 18.38,0l8.93,-8.93c0.54,0.54 0.88,1.3 0.88,2.12zM38,25c0,-3.58 -1.46,-6.84 -3.81,-9.19l-18.38,18.38c2.35,2.35 5.61,3.81 9.19,3.81c7.17,0 13,-5.83 13,-13z">
                                                </path>
                                            </g>
                                        </g>
                                    </svg>
                                    <div class="info">
                                        <span class="font-span">{PROVINCETYPE.total_plans}</span>
                                        <br>
                                        <span>{LANG.kehoachlcnt}</span> <!-- Kế hoạch lựa chọn nhà thầu -->
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="item bg__red ">
                            <div class="tabone">
                                <a href="#tbmt">
                                    <svg class="svg-icon" width="44" height="44" viewBox="0 0 44 44" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M12.8333 36.6666H31.1667M16.5 29.3333V36.6666M27.5 29.3333V36.6666M5.5 9.16659C5.5 8.68036 5.69315 8.21404 6.03697 7.87022C6.38079 7.52641 6.8471 7.33325 7.33333 7.33325H36.6667C37.1529 7.33325 37.6192 7.52641 37.963 7.87022C38.3068 8.21404 38.5 8.68036 38.5 9.16659V27.4999C38.5 27.9861 38.3068 28.4525 37.963 28.7963C37.6192 29.1401 37.1529 29.3333 36.6667 29.3333H7.33333C6.8471 29.3333 6.38079 29.1401 6.03697 28.7963C5.69315 28.4525 5.5 27.9861 5.5 27.4999V9.16659Z"
                                            stroke="white" stroke-width="3.66667" stroke-linecap="round"
                                            stroke-linejoin="round">
                                        </path>
                                        <path d="M14.6666 21.9998L20.1666 16.4998L23.8333 20.1665L29.3333 14.6665"
                                            stroke="white" stroke-width="3.66667" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                    </svg>
                                    <div class="info">
                                        <span class="font-span">{PROVINCETYPE.total_province}</span>
                                        <br>
                                        <h2>{LANG.tbmt}</h2> <!-- Thông báo mời thầu -->
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="item bg__green {class_total_open}">
                            <div class="tabone">
                                <a href="#open">
                                    <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                        xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                        viewBox="0,0,256,256">
                                        <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                            stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                            stroke-dasharray="" stroke-dashoffset="0" font-family="none"
                                            font-weight="none" font-size="none" text-anchor="none"
                                            style="mix-blend-mode: normal">
                                            <g transform="scale(3.2,3.2)">
                                                <path
                                                    d="M40,4c-2.94922,0 -5.30078,2.19141 -5.79687,5h-16.20312c-1.64453,0 -3,1.35547 -3,3v56c0,1.64453 1.35547,3 3,3h44c1.64453,0 3,-1.35547 3,-3v-56c0,-1.64453 -1.35547,-3 -3,-3h-16.20312c-0.49609,-2.80859 -2.84766,-5 -5.79687,-5zM40,6c2.21875,0 4,1.78125 4,4v1h6v3c0,0.56641 -0.43359,1 -1,1h-18c-0.56641,0 -1,-0.43359 -1,-1v-3h6v-1c0,-2.21875 1.78125,-4 4,-4zM40,9c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM18,11h10v3c0,1.64453 1.35547,3 3,3h18c1.64453,0 3,-1.35547 3,-3v-3h10c0.56641,0 1,0.43359 1,1v56c0,0.56641 -0.43359,1 -1,1h-44c-0.56641,0 -1,-0.43359 -1,-1v-56c0,-0.56641 0.43359,-1 1,-1zM22,13c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM22,17c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM22,21c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM22,25c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM55.78516,28.22266l-5.49219,5.29688l-2.65234,-2.71875l-1.43359,1.39453l4.04297,4.14453l6.92188,-6.67969zM22,29c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM28,32v2h14v-2zM22,33c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM22,37c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM22,41c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM22,45c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM55.78516,45.22266l-5.49219,5.29688l-2.65234,-2.71875l-1.43359,1.39453l4.04297,4.14453l6.92188,-6.67969zM28,48v2h14v-2zM22,49c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM22,53c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM22,57c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM22,61c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1zM22,65c-0.55078,0 -1,0.44922 -1,1c0,0.55078 0.44922,1 1,1c0.55078,0 1,-0.44922 1,-1c0,-0.55078 -0.44922,-1 -1,-1z">
                                                </path>
                                            </g>
                                        </g>
                                    </svg>
                                    <div class="info">
                                        <span class="font-span">{PROVINCETYPE.total_open}</span>
                                        <br>
                                        <h2>{LANG.listopen}</h2> <!-- Kết quả mở thầu -->
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="item bg_organ {class_total_kqlcnt}">
                            <div class="tabone">
                                <a href="#kqlcnt">
                                    <svg class="svg-icon" width="44" height="44" viewBox="0 0 44 44" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <g clip-path="url(#clip0_119_11)">
                                            <path
                                                d="M30.25 16.6289C30.4792 16.6146 30.7012 16.6074 30.916 16.6074C31.1309 16.6074 31.36 16.6074 31.6035 16.6074C32.1335 16.6074 32.8066 16.6289 33.623 16.6719C34.4395 16.7148 35.306 16.8151 36.2227 16.9727C37.1393 17.1302 38.0632 17.3307 38.9941 17.5742C39.9251 17.8177 40.7559 18.1471 41.4863 18.5625C42.2168 18.9779 42.8184 19.4577 43.291 20.002C43.7637 20.5462 44 21.2122 44 22V38.5C44 39.2734 43.7708 39.9395 43.3125 40.498C42.8542 41.0566 42.2526 41.5436 41.5078 41.959C40.763 42.3743 39.9323 42.7109 39.0156 42.9688C38.099 43.2266 37.1751 43.4342 36.2441 43.5918C35.3132 43.7493 34.4466 43.8568 33.6445 43.9141C32.8424 43.9714 32.1693 44 31.625 44C31.0951 44 30.4219 43.9714 29.6055 43.9141C28.7891 43.8568 27.9225 43.7565 27.0059 43.6133C26.0892 43.4701 25.1725 43.2624 24.2559 42.9902C23.3392 42.7181 22.5085 42.3815 21.7637 41.9805C21.0189 41.5794 20.4173 41.0853 19.959 40.498C19.5007 39.9108 19.2643 39.2448 19.25 38.5V22C19.25 21.4128 19.3932 20.89 19.6797 20.4316C19.9661 19.9733 20.3385 19.5651 20.7969 19.207C21.2552 18.849 21.7852 18.5339 22.3867 18.2617C22.9883 17.9896 23.5898 17.7604 24.1914 17.5742C24.793 17.388 25.3874 17.2376 25.9746 17.123C26.5618 17.0085 27.0703 16.9225 27.5 16.8652V11H5.5V27.5H2.75V11H0V8.25L16.5 0L33 8.25V11H30.25V16.6289ZM6.14453 8.25H26.8555L16.5 3.07227L6.14453 8.25ZM41.25 38.5V25.5449C39.6745 26.2754 38.1061 26.7839 36.5449 27.0703C34.9837 27.3568 33.3438 27.5 31.625 27.5C29.9062 27.5 28.2663 27.3568 26.7051 27.0703C25.1439 26.7839 23.5755 26.2754 22 25.5449V38.5C22 38.6576 22.0716 38.8079 22.2148 38.9512C22.3581 39.0944 22.4941 39.2018 22.623 39.2734C23.1673 39.6602 23.819 39.9824 24.5781 40.2402C25.3372 40.498 26.1322 40.6986 26.9629 40.8418C27.7936 40.985 28.6172 41.0924 29.4336 41.1641C30.25 41.2357 30.9805 41.2643 31.625 41.25C32.2695 41.25 32.9928 41.2214 33.7949 41.1641C34.597 41.1068 35.4206 41.0065 36.2656 40.8633C37.1107 40.7201 37.9056 40.5124 38.6504 40.2402C39.3952 39.9681 40.054 39.6458 40.627 39.2734C40.7415 39.2018 40.8704 39.0944 41.0137 38.9512C41.1569 38.8079 41.2357 38.6576 41.25 38.5ZM31.625 24.75C32.2695 24.75 32.9928 24.7214 33.7949 24.6641C34.597 24.6068 35.4206 24.5065 36.2656 24.3633C37.1107 24.2201 37.9056 24.0124 38.6504 23.7402C39.3952 23.4681 40.054 23.1458 40.627 22.7734C40.7272 22.7018 40.8561 22.5944 41.0137 22.4512C41.1712 22.3079 41.25 22.1576 41.25 22C41.25 21.8424 41.1712 21.6921 41.0137 21.5488C40.8561 21.4056 40.7272 21.2982 40.627 21.2266C40.0827 20.8398 39.431 20.5247 38.6719 20.2812C37.9128 20.0378 37.1107 19.8372 36.2656 19.6797C35.4206 19.5221 34.6042 19.4147 33.8164 19.3574C33.0286 19.3001 32.2982 19.2643 31.625 19.25C30.9805 19.25 30.25 19.2786 29.4336 19.3359C28.6172 19.3932 27.8008 19.5007 26.9844 19.6582C26.168 19.8158 25.373 20.0163 24.5996 20.2598C23.8262 20.5033 23.1673 20.8255 22.623 21.2266C22.5228 21.2982 22.3939 21.4056 22.2363 21.5488C22.0788 21.6921 22 21.8424 22 22C22 22.1576 22.0788 22.3079 22.2363 22.4512C22.3939 22.5944 22.5228 22.7018 22.623 22.7734C23.1673 23.1602 23.819 23.4824 24.5781 23.7402C25.3372 23.998 26.1322 24.1986 26.9629 24.3418C27.7936 24.485 28.6172 24.5924 29.4336 24.6641C30.25 24.7357 30.9805 24.7643 31.625 24.75ZM8.25 27.5V24.75H11V27.5H8.25ZM13.75 27.5V24.75H16.5V27.5H13.75ZM8.25 22V19.25H11V22H8.25ZM8.25 16.5V13.75H11V16.5H8.25ZM13.75 22V19.25H16.5V22H13.75Z"
                                                fill="white"></path>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_119_11">
                                                <rect width="44" height="44" fill="white"></rect>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    <div class="info">
                                        <span class="font-span">{PROVINCETYPE.total_kqlcnt}</span>
                                        <br>
                                        <h2>{LANG.kqlcnt}</h2> <!-- Kết quả lựa chọn nhà thầu -->
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                    <!-- END: count -->
                </div>
            </div>
            <div class="col-md-24 col-sm-24 col-xs-24 mt15">
                <div class="grid-col-bottom grid-col-new grid-col-p">
                    <div class="item bg_blue {class_total_mstnht}">
                        <div class="tabone">
                            <a href="#prequalification-notice">
                                <svg width="44" height="44" viewBox="0 0 44 44" fill="none"
                                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                    <rect width="44" height="44" fill="url(#pattern0_1_3)" />
                                    <defs>
                                        <pattern id="pattern0_1_3" patternContentUnits="objectBoundingBox" width="1"
                                            height="1">
                                            <use xlink:href="#image0_1_3" transform="scale(0.015625)" />
                                        </pattern>
                                        <image id="image0_1_3" width="64" height="64"
                                            xlink:href="data:image/png;base64,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" />
                                    </defs>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_mstnht}</span>
                                    <br>
                                    <span>{LANG.title_pq_notice_contractor}</span> <!-- Mời sơ tuyển nhà thầu -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_pink {class_total_moiquantam}">
                        <div class="tabone">
                            <a href="#eoi-invitation">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M42.96875,1.375c-0.16797,0 -0.35156,0 -0.5625,0.03125l-29.09375,5.59375h32.6875v-2.59375c0,-1.69922 -1.32812,-3.03125 -3.03125,-3.03125zM3,9c-1.65234,0 -3,1.34766 -3,3v32c0,1.65234 1.34766,3 3,3h44c1.65234,0 3,-1.34766 3,-3v-32c0,-1.65234 -1.34766,-3 -3,-3zM25,15c7.19922,0 13,5.80078 13,13c0,7.19922 -5.80078,13 -13,13c-7.19922,0 -13,-5.80078 -13,-13c0,-7.19922 5.80078,-13 13,-13zM21.90625,22c-2.83984,0 -4.90625,2.06641 -4.90625,4.90625c0,2.76953 6.90625,9.28125 8,9.28125c0.23438,0 0.44531,-0.07031 0.625,-0.21875c1.72656,-1.43359 7.375,-6.30469 7.375,-9.0625c0,-2.83984 -2.06641,-4.90625 -4.90625,-4.90625c-1.18359,0 -2.28516,0.56641 -3.09375,1.21875c-0.81641,-0.66797 -1.90234,-1.21875 -3.09375,-1.21875z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_moiquantam}</span>
                                    <br>
                                    <span>{LANG.title_interest_contractor}</span> <!-- Mời quan tâm nhà thầu -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_purple {class_total_ketquasotuyen}">
                        <div class="tabone">
                            <a href="#prequalification-result">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M25,1.92773l-5.53516,5.53711c-0.26124,0.25082 -0.36647,0.62327 -0.27511,0.97371c0.09136,0.35044 0.36503,0.62411 0.71547,0.71547c0.35044,0.09136 0.72289,-0.01388 0.97371,-0.27511l3.12109,-3.12109v9.24219c-0.0051,0.36064 0.18438,0.69608 0.49587,0.87789c0.3115,0.18181 0.69676,0.18181 1.00825,0c0.3115,-0.18181 0.50097,-0.51725 0.49587,-0.87789v-9.24219l3.12109,3.12109c0.25082,0.26124 0.62327,0.36647 0.97371,0.27511c0.35044,-0.09136 0.62411,-0.36503 0.71547,-0.71547c0.09136,-0.35044 -0.01388,-0.72289 -0.27511,-0.97371zM40,9.92773l-5.53516,5.53711c-0.26124,0.25082 -0.36648,0.62327 -0.27512,0.97371c0.09136,0.35044 0.36503,0.62411 0.71547,0.71547c0.35044,0.09136 0.72289,-0.01388 0.97371,-0.27512l3.12109,-3.12109v9.24219c-0.0051,0.36064 0.18438,0.69608 0.49587,0.87789c0.3115,0.18181 0.69676,0.18181 1.00825,0c0.3115,-0.18181 0.50097,-0.51725 0.49587,-0.87789v-9.24219l3.12109,3.12109c0.25082,0.26124 0.62327,0.36648 0.97371,0.27512c0.35044,-0.09136 0.62411,-0.36503 0.71547,-0.71547c0.09136,-0.35044 -0.01388,-0.72289 -0.27512,-0.97371zM10,16.92773l-5.53516,5.53711c-0.26124,0.25082 -0.36647,0.62327 -0.27511,0.97371c0.09136,0.35044 0.36503,0.62411 0.71547,0.71547c0.35044,0.09136 0.72289,-0.01388 0.97371,-0.27511l3.12109,-3.12109v9.24219c-0.0051,0.36064 0.18438,0.69608 0.49587,0.87789c0.3115,0.18181 0.69676,0.18181 1.00825,0c0.3115,-0.18181 0.50097,-0.51725 0.49587,-0.87789v-9.24219l3.12109,3.12109c0.25082,0.26124 0.62327,0.36648 0.97371,0.27512c0.35044,-0.09136 0.62411,-0.36503 0.71547,-0.71547c0.09136,-0.35044 -0.01388,-0.72289 -0.27512,-0.97371zM20,18c-0.55226,0.00006 -0.99994,0.44774 -1,1v27c0.00006,0.55226 0.44774,0.99994 1,1h10c0.55226,-0.00006 0.99994,-0.44774 1,-1v-27c-0.00006,-0.55226 -0.44774,-0.99994 -1,-1zM21,20h8v25h-8zM35,26c-0.55226,0.00006 -0.99994,0.44774 -1,1v19c0.00006,0.55226 0.44774,0.99994 1,1h10c0.55226,-0.00006 0.99994,-0.44774 1,-1v-19c-0.00006,-0.55226 -0.44774,-0.99994 -1,-1zM36,28h8v17h-8zM5,33c-0.55226,0.00006 -0.99994,0.44774 -1,1v12c0.00006,0.55226 0.44774,0.99994 1,1h10c0.55226,-0.00006 0.99994,-0.44774 1,-1v-12c-0.00006,-0.55226 -0.44774,-0.99994 -1,-1zM6,35h8v10h-8z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_ketquasotuyen}</span>
                                    <br>
                                    <span>{LANG.result_prequalification}</span> <!-- Kết quả sơ tuyển nhà thầu -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_red {class_total_ketquamosotuyen}">
                        <div class="tabone">
                            <a href="#ketquamosotuyen">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(9.84615,9.84615)">
                                            <path
                                                d="M8.46875,0c-0.58594,0 -6.4375,3 -7.46875,3h-1v5c2.72656,0 3.08203,2 5.625,2c1.5,0 3.87109,-0.40625 4.59375,-0.40625c0.72266,0 2.9375,0.45313 3.40625,0.5c1.47266,0.14844 2.09375,-1.03906 1.5,-1.9375c0.02344,0.02734 0.1875,0.25 0.1875,0.25c0.25391,0.30859 0.90234,0.51172 1.28125,0.375c0.56641,-0.20312 0.71094,-0.94922 0.46875,-1.375c0,0 -2.63672,-4.42578 -2.875,-4.6875c-0.23828,-0.26172 -5.13281,-2.71875 -5.71875,-2.71875zM8.9375,3.65625c0.16406,-0.01953 0.28516,0 0.34375,0c0.23047,0 2.98438,0.98438 3.0625,1.09375c0.07031,0.09375 2.04688,2.55078 2.59375,3.21875c-0.08203,-0.08984 -0.16797,-0.20312 -0.28125,-0.28125c-0.65625,-0.46094 -1.82422,-1.07422 -2.71875,-1.25c-0.89453,-0.17578 -2.51562,-0.0625 -2.75,-0.0625c-0.23437,0 -1.5625,-0.19531 -1.5625,-1.3125c0,-1.06641 0.81641,-1.34766 1.3125,-1.40625zM17.375,9.0625c-0.31641,0.30469 -0.80469,0.60547 -1.46875,0.40625c-0.06641,0.21875 -0.16406,0.42969 -0.28125,0.59375c-0.00781,0.01172 -0.02344,0.01953 -0.03125,0.03125c0.07813,0.11719 0.15625,0.25391 0.15625,0.40625c0,0.40625 -0.34375,0.71875 -0.75,0.71875c-0.32031,0 -0.58594,-0.18359 -0.6875,-0.46875c-0.15625,0.01172 -0.30469,-0.01172 -0.46875,-0.03125c-0.40625,-0.04687 -1.19141,-0.16016 -1.84375,-0.28125v0.5625c0,0.48438 0.4375,2 1,2h1v4.34375l0.5,0.65625h1l0.5,-0.65625v-4.34375h1c0.48438,0 1,-1.51562 1,-2v-1c0,-0.42187 -0.25391,-0.78906 -0.625,-0.9375zM20.3125,18.03125c-1.90625,0.12109 -2.98437,1.59375 -7,1.59375c-1.82812,0 -2.3125,0.59375 -2.3125,1.40625c0,1.01563 1.11719,1.375 2.34375,1.375h3.0625c1.29297,0 3.09375,-0.6875 3.09375,-0.6875c0,0 -1.51953,1.65625 -3.09375,1.65625h-3.71875c-0.9375,0 -2.6875,-0.34375 -2.6875,-2.09375c0,-1.08594 0.40625,-1.46875 0.40625,-1.46875c0,0 -1.66016,-0.73047 -2.25,-0.96875c-0.14844,-0.05859 -0.53125,-0.1875 -0.96875,-0.1875c-0.60937,0 -1.03125,0.42188 -1.03125,1.0625c0,0 -0.62891,-0.21875 -0.96875,-0.21875c-0.42187,0 -1.1875,0.20313 -1.1875,1.125c0,0.60938 0.33594,1.01953 1.09375,1.34375c0.73438,0.31641 9.5,4.03125 10.46875,4.03125c0.67969,0 4.41016,-1.47656 5.375,-1.78125c1.89063,-0.59375 3.375,0.5 3.375,0.5l1.6875,-4.90625c0,0 -2.10547,-0.85937 -3.375,-1.34375c-0.96484,-0.36719 -1.67578,-0.47656 -2.3125,-0.4375z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_ketquamosotuyen}</span>
                                    <br>
                                    <h2>{LANG.title_pq_open_contractor}</h2> <!-- Kết quả mở sơ tuyển nhà thầu -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_yellow {class_total_ketquamoiquantam}">
                        <div class="tabone">
                            <a href="#eoiresult">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="64" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(2.56,2.56)">
                                            <path
                                                d="M81.02148,11.49805c-0.12053,-0.00362 -0.24078,0.01356 -0.35547,0.05078c-18.19206,5.90382 -33.0997,6.20182 -45.68359,4.85352l-0.12305,-0.0918c-0.32392,-0.23955 -0.71263,-0.3757 -1.11523,-0.39062c-0.91383,-0.03476 -1.73486,0.55468 -1.99414,1.43164c-4.58507,15.54602 -11.09573,28.20571 -19.5957,37.86133c-0.3706,0.42049 -0.54776,0.97719 -0.48835,1.53453c0.05941,0.55734 0.34994,1.06418 0.80085,1.39711l48.05469,35.50195c0.77176,0.57019 1.83969,0.512 2.54492,-0.13867c9.16628,-8.45096 17.43694,-19.56814 22.36719,-38.12891c0.20981,-0.79171 -0.08425,-1.63134 -0.74219,-2.11914l-5.63281,-4.17773c2.71658,-12.47758 3.47433,-24.63733 2.98047,-36.57812c-0.02271,-0.55035 -0.467,-0.98952 -1.01758,-1.00586zM79.95898,13.89844c0.37271,11.07593 -0.34384,22.30767 -2.75586,33.80859l-38.9707,-28.89453c11.73422,0.90013 25.4976,0.14476 41.72656,-4.91406zM76.7793,20.74219c-0.09019,-0.0017 -0.1802,0.00881 -0.26758,0.03125l-6.20898,1.52148c-0.35964,0.07271 -0.65059,0.33651 -0.75806,0.68734c-0.10747,0.35082 -0.0142,0.73233 0.243,0.994c0.2572,0.26167 0.63705,0.36149 0.98967,0.26007l6.21094,-1.51953c0.49287,-0.11342 0.82349,-0.57694 0.77026,-1.07988c-0.05323,-0.50294 -0.47355,-0.88699 -0.97924,-0.89472zM35.66016,21.88477l45.48242,33.72266c-0.63265,2.25735 -1.31644,4.37938 -2.04102,6.40039l-1.65039,-0.48437c-0.09551,-0.02963 -0.19492,-0.04478 -0.29492,-0.04492c-0.50172,-0.00202 -0.92729,0.36801 -0.99499,0.86514c-0.0677,0.49713 0.24342,0.96749 0.72741,1.0997l1.51758,0.44336c-0.69271,1.79797 -1.41939,3.50661 -2.18164,5.12891l-1.99219,-1.03906c-0.14764,-0.07935 -0.31286,-0.12032 -0.48047,-0.11914c-0.4615,0.00364 -0.86058,0.32261 -0.96585,0.77196c-0.10527,0.44935 0.11062,0.91238 0.52249,1.12062l2.0332,1.06055c-1.41167,2.79952 -2.9269,5.34244 -4.52734,7.67969l-1.02148,-2.44141c-0.15388,-0.38273 -0.52697,-0.63172 -0.93945,-0.62695c-0.33505,0.00371 -0.64598,0.17496 -0.8282,0.45615c-0.18222,0.28119 -0.21152,0.63495 -0.07805,0.94229l1.50195,3.58789c-0.60636,0.81966 -1.22229,1.61806 -1.84961,2.38867l-0.54492,-1.75977c-0.12714,-0.43062 -0.52567,-0.72373 -0.97461,-0.7168c-0.31724,0.00499 -0.6133,0.16023 -0.79782,0.41834c-0.18453,0.25811 -0.23563,0.58846 -0.13772,0.89026l0.92383,2.98828c-1.47922,1.69088 -3.007,3.27275 -4.57422,4.77148l-9.87891,-7.29687l5.2832,-1.35547c0.4925,-0.11803 0.81903,-0.58559 0.76026,-1.08861c-0.05877,-0.50302 -0.48428,-0.88272 -0.99073,-0.88404c-0.08972,-0.00025 -0.17906,0.01158 -0.26562,0.03516l-6.86133,1.75977l-3.51758,-2.59961l3.55859,-0.66016c0.51989,-0.08579 0.88403,-0.56023 0.83247,-1.08462c-0.05156,-0.52439 -0.50114,-0.91882 -1.02778,-0.90171c-0.05709,0.00165 -0.11394,0.00818 -0.16992,0.01953l-5.39062,1.00391l-7.7793,-5.74609l1.45313,-2.94922c0.16281,-0.31901 0.1426,-0.70074 -0.05299,-1.00077c-0.19559,-0.30004 -0.53669,-0.47258 -0.89427,-0.45235c-0.36451,0.02088 -0.68868,0.23874 -0.8457,0.56836l-1.29297,2.625l-5.87695,-4.33984l1.04492,-0.69727c0.3779,-0.24387 0.54653,-0.71 0.41212,-1.1392c-0.13441,-0.4292 -0.53879,-0.71589 -0.98829,-0.70064c-0.19087,0.00648 -0.3759,0.06748 -0.5332,0.17578l-1.64648,1.09766l-3.01172,-2.22461l1.14258,-0.56055c0.42315,-0.19999 0.65101,-0.66672 0.54844,-1.12337c-0.10257,-0.45666 -0.50814,-0.7811 -0.97617,-0.78092c-0.15755,0.00026 -0.31281,0.03773 -0.45312,0.10938l-2.07422,1.01563l-1.5332,-1.13281l1.25,-0.5c0.45451,-0.17429 0.71752,-0.65051 0.62298,-1.12803c-0.09454,-0.47752 -0.51916,-0.81761 -1.0058,-0.80556c-0.12347,0.00298 -0.24531,0.02881 -0.35938,0.07617l-2.39844,0.95898l-2.0957,-1.54687c6.29238,-7.56604 11.43392,-16.68704 15.49805,-27.26562l0.15039,0.37305c0.12745,0.34052 0.42966,0.58504 0.78928,0.63861c0.35962,0.05357 0.71998,-0.09224 0.94115,-0.38082c0.22117,-0.28858 0.2683,-0.67446 0.12308,-1.00779l-0.97266,-2.40625c0.35016,-0.97794 0.68874,-1.97115 1.02148,-2.97266l1.72656,3.53711c0.15057,0.33121 0.46913,0.55452 0.83183,0.58312c0.3627,0.0286 0.71232,-0.14203 0.91293,-0.44555c0.20061,-0.30352 0.22057,-0.69204 0.05211,-1.01452zM65.31836,23.59766c-0.09007,0.00229 -0.17942,0.01675 -0.26562,0.04297c-5.83624,1.68893 -10.91602,1.25195 -10.91602,1.25195c-0.3602,-0.03908 -0.71338,0.11971 -0.9232,0.41509c-0.20982,0.29538 -0.24348,0.68114 -0.08799,1.00839c0.1555,0.32725 0.47583,0.54481 0.83736,0.56871c0,0 5.42077,0.47881 11.64453,-1.32227c0.4886,-0.13277 0.80089,-0.60974 0.7272,-1.11067c-0.07369,-0.50093 -0.51013,-0.86775 -1.01626,-0.85418zM74.89844,27.64063c-0.19119,0.00973 -0.37558,0.07413 -0.53125,0.18555c-1.80215,1.2585 -5.20024,2.03419 -8.09375,2.39063c-2.89351,0.35644 -5.27344,0.35547 -5.27344,0.35547c-0.36064,-0.0051 -0.69608,0.18438 -0.87789,0.49587c-0.18181,0.3115 -0.18181,0.69676 0,1.00825c0.18181,0.3115 0.51725,0.50097 0.87789,0.49587c0,0 2.49109,0.0017 5.51758,-0.37109c3.02649,-0.37281 6.59629,-1.05987 8.99414,-2.73437c0.3712,-0.25204 0.52911,-0.72045 0.38627,-1.14579c-0.14284,-0.42534 -0.55147,-0.70348 -0.99955,-0.68039zM36.97656,31.14453c-0.595,0.082 -1.01269,0.63252 -0.92969,1.22852c0.083,0.595 0.63547,1.01269 1.23047,0.92969c0.596,-0.082 1.01173,-0.63447 0.92773,-1.23047c-0.083,-0.595 -0.63152,-1.01173 -1.22852,-0.92773zM76.28711,33.48047c-0.08998,0.00166 -0.17933,0.01545 -0.26562,0.04102l-6.37891,1.77539c-0.35647,0.08461 -0.63813,0.35736 -0.73417,0.71092c-0.09603,0.35356 0.00895,0.73133 0.27363,0.98465c0.26468,0.25332 0.64669,0.34164 0.9957,0.23021l6.38086,-1.77344c0.49275,-0.12598 0.81247,-0.60172 0.74299,-1.10555c-0.06948,-0.50383 -0.50602,-0.87528 -1.01448,-0.8632zM45.43164,39.36133c-1.82088,0.0537 -3.66789,0.73531 -5.3418,1.82617c-2.23187,1.45448 -4.18752,3.82533 -4.55273,6.91992c-0.87634,7.43678 1.24581,10.69504 2.94922,14.1543c0.0013,0.00261 0.0026,0.00521 0.00391,0.00781c0.66139,1.32949 2.98633,6.17188 2.98633,6.17188c0.00513,0.00981 0.01034,0.01957 0.01563,0.0293c0.01007,0.02036 0.02049,0.04054 0.03125,0.06055c0.4276,0.83468 1.3777,1.2602 2.28516,1.02344c0.52402,-0.02727 3.4795,-0.18258 4.83984,-0.22852c4.55935,-0.15525 10.97748,0.0031 16.15625,-4.39258c4.53041,-3.84538 4.81184,-10.0386 1.74023,-13.49805c-2.59254,-2.9244 -5.77735,-3.93521 -8.58984,-4.00781c-0.58246,-0.01504 -1.13986,0.02103 -1.68164,0.07813c-0.1909,-0.72015 -0.49566,-1.58853 -1.00586,-2.48437c-1.23295,-2.16492 -3.74654,-4.56207 -8.03125,-5.49609c-0.59382,-0.12956 -1.19773,-0.18196 -1.80469,-0.16406zM46.0332,43.39063c0.13066,0.0063 0.24777,0.02075 0.34961,0.04297c0.00065,0 0.0013,0 0.00195,0c3.27529,0.71398 4.64615,2.23028 5.4082,3.56836c0.32035,0.5625 0.49231,1.04782 0.60547,1.44922c-1.68266,0.63784 -2.76172,1.33203 -2.76172,1.33203c-0.62316,0.37682 -0.99228,1.06254 -0.96367,1.7902c0.02861,0.72767 0.45044,1.38228 1.10127,1.709c0.65082,0.32672 1.42779,0.27391 2.02841,-0.13787c0,0 1.80576,-1.14171 4.21094,-1.57617c2.40518,-0.43446 5.05604,-0.28007 7.53906,2.52148c0.00065,0.00065 0.0013,0.0013 0.00195,0.00195c1.26839,1.42855 1.58974,5.30635 -1.33984,7.79297c-3.88123,3.29434 -8.98248,3.28261 -13.70312,3.44336c-1.20216,0.04059 -2.8843,0.12995 -4.00781,0.1875c-0.59509,-1.23893 -1.84676,-3.84938 -2.42969,-5.02148l-0.00391,-0.00781c-1.86821,-3.79143 -3.34494,-5.25035 -2.56055,-11.91016v-0.00195c0.19737,-1.6666 1.29109,-3.07549 2.76367,-4.03516c1.28899,-0.84002 2.84518,-1.19253 3.75977,-1.14844zM28.96875,54.45117c-0.596,0.083 -1.01269,0.63447 -0.92969,1.23047c0.083,0.595 0.63447,1.01073 1.23047,0.92773c0.596,-0.083 1.01269,-0.63252 0.92969,-1.22852c-0.083,-0.595 -0.63447,-1.01269 -1.23047,-0.92969zM71.2207,61.69531c-0.6,0.082 -1.02164,0.63447 -0.93164,1.23047c0.08,0.595 0.63047,1.01073 1.23047,0.92773c0.59,-0.082 1.00969,-0.63447 0.92969,-1.23047c-0.09,-0.595 -0.63852,-1.01174 -1.22852,-0.92773z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_ketquamoiquantam}</span>
                                    <br>
                                    <h2>{LANG.result_prequalification_title_qt}</h2>
                                    <!-- Kết quả mời quan tâm nhà thầu -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_red {class_total_ketquamoquantam}">
                        <div class="tabone">
                            <a href="#ketquamoquantam">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M21,2c-0.165,0 -0.33047,0.03914 -0.48047,0.11914l-18,9.78125c-0.32,0.17 -0.51953,0.50891 -0.51953,0.87891v3.2207c0,0.55 0.45,1 1,1h2v20c0,0.55 0.45,1 1,1h23.05273c-0.03317,0.32926 -0.05273,0.66245 -0.05273,1c0,5.49546 4.50455,10 10,10c5.49546,0 10,-4.50454 10,-10c0,-5.49545 -4.50454,-10 -10,-10c-0.68418,0 -1.35332,0.07026 -2,0.20313v-12.20312h2c0.55,0 1,-0.45 1,-1v-3.2207c0,-0.37 -0.19953,-0.70891 -0.51953,-0.87891l-18,-9.78125c-0.15,-0.08 -0.31547,-0.11914 -0.48047,-0.11914zM39,31c4.40454,0 8,3.59546 8,8c0,4.40454 -3.59546,8 -8,8c-4.40454,0 -8,-3.59546 -8,-8c0,-4.40454 3.59546,-8 8,-8zM36,34c-1.10457,0 -2,0.89543 -2,2c0,1.10457 0.89543,2 2,2c1.10457,0 2,-0.89543 2,-2c0,-1.10457 -0.89543,-2 -2,-2zM42.29297,34.29297l-8,8l1.41406,1.41406l8,-8zM42,40c-1.10457,0 -2,0.89543 -2,2c0,1.10457 0.89543,2 2,2c1.10457,0 2,-0.89543 2,-2c0,-1.10457 -0.89543,-2 -2,-2z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_ketquamoquantam}</span>
                                    <br>
                                    <h2>{LANG.title_interest_open_contractor}</h2> <!-- Kết quả mở quan tâm nhà thầu -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item {class_total_cbda}">
                        <div class="tabone">
                            <a href="#project-proposal">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M6,2c-3.30078,0 -6,2.69922 -6,6v29c0,3.30078 2.69922,6 6,6h44v-36h-38.20312c-0.49609,-2.80859 -2.84766,-5 -5.79687,-5zM6,4c2.21875,0 4,1.78125 4,4v24.54688c-1.0625,-0.95703 -2.46484,-1.54687 -4,-1.54687c-1.53516,0 -2.9375,0.58984 -4,1.54688v-24.54687c0,-2.21875 1.78125,-4 4,-4zM12,9h36v32h-42c-2.21875,0 -4,-1.78125 -4,-4c0,-2.21875 1.78125,-4 4,-4c2.21875,0 4,1.78125 4,4h2zM35,15l2.79297,2.79297l-5.29297,5.29297l-5.45312,-5.45703l-8.70312,7.62109l1.3125,1.5l7.29688,-6.37891l5.54688,5.54297l6.70703,-6.70703l2.79297,2.79297v-7zM17,32v2h25v-2z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_cbda}</span>
                                    <br>
                                    <h2>{LANG.project_proposal}</h2> <!-- Công bố danh mục dự án -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_green {class_total_khlcndt}">
                        <div class="tabone">
                            <a href="#planndt">
                                <svg class="svg-icon" width="44" height="44" viewBox="0 0 44 44" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M5.5 40.3333V14.6666H12.8333V40.3333H5.5ZM18.3333 40.3333V3.66663H25.6667V40.3333H18.3333ZM31.1667 40.3333V25.6666H38.5V40.3333H31.1667Z"
                                        fill="white"></path>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_khlcndt}</span>
                                    <br>
                                    <span>{LANG.khlcndt}</span> <!-- Kế hoạch lựa chọn nhà đầu tư -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg__red {class_total_moidautu}">
                        <div class="tabone">
                            <a href="#investment-invitation">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M7,2v46h11.30273c-0.193,-0.469 -0.30273,-0.967 -0.30273,-1.5v-8c0,-3.341 3.925,-5.5 10,-5.5c1.533,0 2.9272,0.13844 4.1582,0.39844c0.778,-2.705 4.4648,-4.39844 9.8418,-4.39844c0.341,0 0.673,0.00944 1,0.02344v-14.4375l-12.58594,-12.58594zM30,4.41406l10.58594,10.58594h-10.58594zM18,9h2v2.08594c3.128,0.504 4,3.34206 4,4.91406h-1l-1,0.00586c-0.006,-0.502 -0.177,-3.00586 -3,-3.00586c-2.816,0 -2.993,2.2113 -3,2.6543c0,1.711 1.46361,2.34342 3.84961,3.23242c2.295,0.855 5.15039,1.91828 5.15039,5.11328c0,1.6 -1.08,4.51441 -5,4.94141v2.05859h-2v-2.07031c-3.92,-0.512 -5,-4.00969 -5,-5.92969h2c0.004,0.405 0.147,4 4,4c3.954,0 4,-2.877 4,-3c0,-1.718 -1.46361,-2.34928 -3.84961,-3.23828c-2.295,-0.855 -5.15039,-1.92023 -5.15039,-5.11523c0,-1.461 0.872,-4.09841 4,-4.56641zM42,31c-2.02951,0 -3.86668,0.28442 -5.28516,0.79102c-0.70924,0.2533 -1.31912,0.5577 -1.81055,0.97266c-0.42279,0.357 -0.76967,0.85597 -0.86328,1.44727c-0.02774,0.09383 -0.04156,0.19122 -0.04102,0.28906v1.59961c-0.23001,-0.10804 -0.45783,-0.2168 -0.71484,-0.30859c-1.41847,-0.5066 -3.25565,-0.79102 -5.28516,-0.79102c-2.02951,0 -3.86668,0.28442 -5.28516,0.79102c-0.70924,0.2533 -1.31912,0.5577 -1.81055,0.97266c-0.42279,0.357 -0.76967,0.85597 -0.86328,1.44727c-0.02774,0.09383 -0.04156,0.19122 -0.04102,0.28906v8c0,0.72348 0.41285,1.32134 0.9043,1.73633c0.49144,0.41499 1.1013,0.71937 1.81055,0.97266c1.41849,0.50656 3.25567,0.79102 5.28516,0.79102c2.02948,0 3.86667,-0.28445 5.28516,-0.79102c0.66559,-0.23769 1.24003,-0.52351 1.71484,-0.90039c0.47481,0.37688 1.04925,0.6627 1.71484,0.90039c1.41849,0.50656 3.25567,0.79102 5.28516,0.79102c2.02948,0 3.86667,-0.28445 5.28516,-0.79102c0.70925,-0.25328 1.3191,-0.55767 1.81055,-0.97266c0.49144,-0.41499 0.9043,-1.01285 0.9043,-1.73633v-4v-4v-4c0.00132,-0.10046 -0.01251,-0.20054 -0.04102,-0.29687c0,-0.00065 0,-0.0013 0,-0.00195c-0.096,-0.58705 -0.44276,-1.08242 -0.86328,-1.4375c-0.49143,-0.41495 -1.10131,-0.71936 -1.81055,-0.97266c-1.41847,-0.5066 -3.25565,-0.79102 -5.28516,-0.79102zM42,33c1.83649,0 3.49826,0.2756 4.61328,0.67383c0.55751,0.19911 0.9744,0.43395 1.19141,0.61719c0.21701,0.18324 0.19531,0.24211 0.19531,0.20898c0,-0.03313 0.0217,0.02575 -0.19531,0.20898c-0.21701,0.18324 -0.63389,0.41808 -1.19141,0.61719c-1.11503,0.39822 -2.77679,0.67383 -4.61328,0.67383c-1.83649,0 -3.49826,-0.2756 -4.61328,-0.67383c-0.55751,-0.19911 -0.9744,-0.43395 -1.19141,-0.61719c-0.21701,-0.18324 -0.19531,-0.24211 -0.19531,-0.20898c0,0.03313 -0.0217,-0.02575 0.19531,-0.20898c0.21701,-0.18324 0.63389,-0.41808 1.19141,-0.61719c1.11503,-0.39822 2.77679,-0.67383 4.61328,-0.67383zM36,36.90039c0.23001,0.10804 0.45783,0.2168 0.71484,0.30859c1.41847,0.5066 3.25565,0.79102 5.28516,0.79102c2.02951,0 3.86668,-0.28442 5.28516,-0.79102c0.25701,-0.09179 0.48483,-0.20055 0.71484,-0.30859v1.59961c0,-0.03298 0.02168,0.02575 -0.19531,0.20898c-0.21699,0.18323 -0.6339,0.41809 -1.19141,0.61719c-1.11501,0.39819 -2.77676,0.67383 -4.61328,0.67383c-1.83652,0 -3.49827,-0.27564 -4.61328,-0.67383c-0.55751,-0.19909 -0.97441,-0.43395 -1.19141,-0.61719c-0.21699,-0.18323 -0.19531,-0.24196 -0.19531,-0.20898zM28,37c1.83649,0 3.49826,0.2756 4.61328,0.67383c0.55751,0.19911 0.9744,0.43395 1.19141,0.61719c0.21701,0.18324 0.19531,0.24211 0.19531,0.20898c0,-0.03313 0.0217,0.02575 -0.19531,0.20898c-0.21701,0.18324 -0.63389,0.41808 -1.19141,0.61719c-1.11502,0.39822 -2.77679,0.67383 -4.61328,0.67383c-1.83649,0 -3.49826,-0.2756 -4.61328,-0.67383c-0.55751,-0.19911 -0.9744,-0.43395 -1.19141,-0.61719c-0.21701,-0.18324 -0.19531,-0.24211 -0.19531,-0.20898c0,0.03313 -0.0217,-0.02575 0.19531,-0.20898c0.21701,-0.18324 0.63389,-0.41808 1.19141,-0.61719c1.11502,-0.39822 2.77679,-0.67383 4.61328,-0.67383zM22,40.90039c0.23001,0.10804 0.45783,0.2168 0.71484,0.30859c1.41847,0.5066 3.25565,0.79102 5.28516,0.79102c2.02951,0 3.86668,-0.28442 5.28516,-0.79102c0.25701,-0.09179 0.48483,-0.20055 0.71484,-0.30859v1.59961c0,-0.03298 0.02168,0.02575 -0.19531,0.20898c-0.21699,0.18323 -0.6339,0.41809 -1.19141,0.61719c-1.11501,0.39819 -2.77676,0.67383 -4.61328,0.67383c-1.83652,0 -3.49827,-0.27564 -4.61328,-0.67383c-0.55751,-0.19909 -0.97441,-0.43395 -1.19141,-0.61719c-0.21699,-0.18323 -0.19531,-0.24196 -0.19531,-0.20898zM36,40.90039c0.23001,0.10804 0.45783,0.21681 0.71484,0.30859c1.41849,0.50656 3.25567,0.79102 5.28516,0.79102c2.02948,0 3.86667,-0.28445 5.28516,-0.79102c0.25702,-0.09178 0.48484,-0.20056 0.71484,-0.30859v1.59961c0,-0.03298 0.02168,0.02575 -0.19531,0.20898c-0.21699,0.18323 -0.6339,0.41809 -1.19141,0.61719c-1.11501,0.39819 -2.77676,0.67383 -4.61328,0.67383c-1.83652,0 -3.49827,-0.27564 -4.61328,-0.67383c-0.55751,-0.19909 -0.97441,-0.43395 -1.19141,-0.61719c-0.21699,-0.18323 -0.19531,-0.24196 -0.19531,-0.20898zM22,44.90039c0.23001,0.10804 0.45783,0.21681 0.71484,0.30859c1.41849,0.50656 3.25567,0.79102 5.28516,0.79102c2.02948,0 3.86667,-0.28445 5.28516,-0.79102c0.25702,-0.09178 0.48484,-0.20056 0.71484,-0.30859v1.59961c0,-0.03298 0.02168,0.02575 -0.19531,0.20898c-0.21699,0.18323 -0.6339,0.41809 -1.19141,0.61719c-1.11501,0.39819 -2.77676,0.67383 -4.61328,0.67383c-1.83652,0 -3.49827,-0.27564 -4.61328,-0.67383c-0.55751,-0.19909 -0.97441,-0.43395 -1.19141,-0.61719c-0.21699,-0.18323 -0.19531,-0.24196 -0.19531,-0.20898zM36,44.90039c0.23001,0.10804 0.45783,0.21681 0.71484,0.30859c1.41849,0.50656 3.25567,0.79102 5.28516,0.79102c2.02948,0 3.86667,-0.28445 5.28516,-0.79102c0.25702,-0.09178 0.48484,-0.20056 0.71484,-0.30859v1.59961c0,-0.03298 0.02168,0.02575 -0.19531,0.20898c-0.21699,0.18323 -0.6339,0.41809 -1.19141,0.61719c-1.11501,0.39819 -2.77676,0.67383 -4.61328,0.67383c-1.83652,0 -3.49827,-0.27564 -4.61328,-0.67383c-0.55751,-0.19909 -0.97441,-0.43395 -1.19141,-0.61719c-0.21699,-0.18323 -0.19531,-0.24196 -0.19531,-0.20898z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_moidautu}</span>
                                    <br>
                                    <h2>{LANG.pagetitle_tbmt_type2}</h2> <!-- Thông báo mời đầu tư -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg__green {class_total_moisotuyen}">
                        <div class="tabone">
                            <a href="#moisotuyen">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M23,0c-7.22619,0 -12.55444,2.491 -16,6.40039c-3.44556,3.90939 -5,9.15913 -5,14.59961c0,4.40556 1.36599,8.50701 3.67578,11.86719c2.09383,3.04601 3.32422,6.65654 3.32422,10.33203v5.80078h2v-5.80078c0,-4.12051 -1.37161,-8.11285 -3.67578,-11.46484c-2.08821,-3.03783 -3.32422,-6.74193 -3.32422,-10.73437c0,-5.05952 1.44556,-9.80978 4.5,-13.27539c3.05444,-3.46561 7.72619,-5.72461 14.5,-5.72461c6.80236,0 11.20543,1.9786 14.11523,5.00586c2.9098,3.02726 4.35786,7.19978 4.89258,11.71289l0.02344,0.20703l4.79297,8.25977c0.24837,0.45002 0.1246,0.89305 -0.30859,1.13672l-4.51562,2.25781v6.02148c0,3.73832 -3.08023,6.64922 -6.80469,6.30078l-0.01562,-0.00195l-4.17969,-0.26758v6.36719h2v-4.23437l2.00977,0.12891c4.86954,0.45556 8.99023,-3.43728 8.99023,-8.29297v-4.78516l3.4668,-1.73242l0.01953,-0.01172c1.35867,-0.7565 1.83723,-2.50562 1.08789,-3.85742l-0.00586,-0.00977l-4.64648,-8.00586c-0.59563,-4.68245 -2.1168,-9.2006 -5.36328,-12.57813c-3.3147,-3.44849 -8.36095,-5.62109 -15.55859,-5.62109zM17.14258,9.53125c-1.40251,0.05012 -2.80933,0.25056 -4.29492,0.48047c-0.48766,0.07521 -0.84759,0.49485 -0.84766,0.98828v16c0.00027,0.27864 0.11676,0.54453 0.32142,0.73361c0.20466,0.18909 0.47892,0.28422 0.75671,0.26248c3.85616,-0.29896 6.74927,-0.008 10.67969,0.97461c0.01426,0.00292 0.02859,0.00552 0.04297,0.00781c0.01425,0.00357 0.02858,0.00683 0.04297,0.00977c0.01169,0.00151 0.02342,0.00281 0.03516,0.00391c0.02531,0.00292 0.05072,0.00488 0.07617,0.00586c0.03318,0.00165 0.06643,0.00165 0.09961,0c0.00065,0 0.0013,0 0.00195,0c0.03008,-0.0019 0.06006,-0.00516 0.08984,-0.00977c0.00848,-0.00119 0.01694,-0.0025 0.02539,-0.00391c0.02359,-0.00372 0.04705,-0.00828 0.07031,-0.01367c3.93042,-0.9826 6.82353,-1.27357 10.67969,-0.97461c0.27779,0.02174 0.55205,-0.0734 0.75671,-0.26248c0.20466,-0.18909 0.32115,-0.45498 0.32142,-0.73361v-16c-0.00007,-0.49343 -0.35999,-0.91307 -0.84766,-0.98828c-3.81573,-0.59053 -7.13589,-0.93671 -11.15234,0.92383c-1.93146,-0.89471 -3.71499,-1.32089 -5.45312,-1.40039c-0.46907,-0.02145 -0.93679,-0.02061 -1.4043,-0.00391zM18.41406,11.50586c1.46675,0.05998 2.9433,0.43334 4.58594,1.17578v14.12695c-3.10242,-0.68849 -5.9027,-0.95475 -9,-0.80273v-14.0957c1.57276,-0.23381 3.04104,-0.46045 4.41406,-0.4043zM29.58594,11.50586c1.37302,-0.05615 2.84131,0.17049 4.41406,0.4043v14.0957c-3.0973,-0.15201 -5.89758,0.11425 -9,0.80273v-14.12695c1.64264,-0.74244 3.11919,-1.1158 4.58594,-1.17578zM9.98438,11.98633c-0.55152,0.00862 -0.99193,0.46214 -0.98437,1.01367v17c-0.00023,0.52518 0.40581,0.96107 0.92969,0.99805l13.94336,0.99609c0.04663,0.00591 0.09362,0.00852 0.14063,0.00781c0.01891,-0.00077 0.0378,-0.00207 0.05664,-0.00391l0.05859,-0.00391c0.0013,0 0.0026,0 0.00391,0l13.9375,-0.99609c0.52388,-0.03698 0.92991,-0.47287 0.92969,-0.99805v-17c0.0051,-0.36064 -0.18438,-0.69608 -0.49587,-0.87789c-0.3115,-0.18181 -0.69676,-0.18181 -1.00825,0c-0.3115,0.18181 -0.50097,0.51725 -0.49587,0.87789v16.06836l-13,0.92773l-13,-0.92773v-16.06836c0.0037,-0.2703 -0.10218,-0.53059 -0.29351,-0.72155c-0.19133,-0.19097 -0.45182,-0.29634 -0.72212,-0.29212z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_moisotuyen}</span>
                                    <br>
                                    <h2>{LANG.title_pq_notice_investor}</h2> <!-- Mời sơ tuyển nhà đầu tư -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_organ {class_total_kqst_project}">
                        <div class="tabone">
                            <a href="#kqst-project">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(3.76471,3.76471)">
                                            <path
                                                d="M37.04688,2.79688c-1.50195,0 -3.17871,0.51025 -4.92578,1.51758c-6.19629,3.57764 -11.2207,12.27588 -11.19922,19.39062c0.0127,3.33594 1.12598,5.81738 3.13672,6.98828l1.30469,0.75586c-0.03662,-0.06042 -0.0625,-0.1333 -0.09766,-0.19531c-0.14819,-0.26068 -0.28638,-0.53247 -0.4082,-0.82031c-0.04834,-0.11407 -0.09082,-0.23138 -0.13477,-0.34961c-0.104,-0.27838 -0.19678,-0.5686 -0.27734,-0.86914c-0.03491,-0.13013 -0.06909,-0.25836 -0.09961,-0.39258c-0.073,-0.32184 -0.13086,-0.65778 -0.17773,-1.00195c-0.01709,-0.12396 -0.03906,-0.24237 -0.05273,-0.36914c-0.05054,-0.47571 -0.08252,-0.96515 -0.08398,-1.47852c-0.01514,-5.15765 2.53223,-11.11413 6.25977,-15.49805c1.59912,-1.88104 3.41138,-3.47754 5.33789,-4.58984c0.46802,-0.27032 0.93164,-0.49591 1.39063,-0.69922c0.06226,-0.02722 0.12158,-0.05017 0.18359,-0.07617c0.41162,-0.1748 0.81934,-0.32111 1.2207,-0.44141c0.04443,-0.01324 0.08838,-0.02454 0.13281,-0.03711c0.41113,-0.11737 0.81396,-0.20624 1.21094,-0.26562c0.0415,-0.00635 0.08374,-0.01373 0.125,-0.01953c0.35498,-0.04791 0.70361,-0.07129 1.04492,-0.07227c0.06445,-0.00092 0.13354,-0.01434 0.19727,-0.01367l-1.28125,-0.74023c-0.83008,-0.48242 -1.77734,-0.72266 -2.80664,-0.72266zM41.08789,5.06445c-0.13403,-0.00256 -0.27344,0.00995 -0.41016,0.01563c-0.2251,0.00916 -0.45142,0.02533 -0.68359,0.05664c-0.1499,0.02039 -0.30249,0.04852 -0.45508,0.07813c-0.21997,0.04248 -0.44287,0.09454 -0.66797,0.15625c-0.15967,0.04382 -0.31836,0.08917 -0.48047,0.14258c-0.23706,0.07806 -0.47876,0.17285 -0.7207,0.27148c-0.15088,0.06158 -0.30054,0.11597 -0.45312,0.18555c-0.35815,0.16333 -0.7207,0.34753 -1.08594,0.55469c-0.03418,0.01923 -0.06567,0.03314 -0.09961,0.05273c-2.77881,1.60425 -5.31738,4.24304 -7.27539,7.30273c-2.40747,3.76276 -3.93506,8.16345 -3.92383,12.08789c0.00928,2.40863 0.59961,4.36267 1.67969,5.70898c0.04297,0.05328 0.07861,0.11658 0.12305,0.16797c0.09546,0.11053 0.20605,0.20135 0.30859,0.30273c0.10767,0.10608 0.20703,0.22058 0.32227,0.31641c0.06787,0.05652 0.14624,0.09949 0.2168,0.15234c0.11499,0.08606 0.23145,0.16986 0.35352,0.24609c0.17163,0.10748 0.35156,0.19952 0.53516,0.28516c0.08325,0.03925 0.16455,0.08051 0.25,0.11523c0.22534,0.0907 0.45898,0.16516 0.69922,0.22461c0.04395,0.01111 0.08667,0.02509 0.13086,0.03516c1.80908,0.40063 3.99609,-0.05945 6.25195,-1.36133c0.38721,-0.2226 0.77148,-0.46808 1.15039,-0.73047c0.28442,-0.19757 0.56055,-0.41504 0.83789,-0.63281c0.09473,-0.07422 0.19312,-0.14008 0.28711,-0.2168c0.20532,-0.16748 0.40234,-0.3515 0.60352,-0.5293c0.17578,-0.15576 0.35425,-0.30426 0.52734,-0.46875c0.11523,-0.10895 0.22436,-0.22772 0.33789,-0.33984c0.27271,-0.27118 0.54517,-0.54309 0.81055,-0.83594c0.02295,-0.02478 0.04565,-0.05121 0.06836,-0.07617c0.33545,-0.36786 0.65893,-0.74884 0.97266,-1.13867c0.08472,-0.10559 0.16675,-0.21307 0.25,-0.32031c0.27832,-0.3576 0.54956,-0.72192 0.80664,-1.09375c2.76001,-3.95117 4.55322,-8.7583 4.54102,-13.00977c-0.00879,-3.3501 -1.12793,-5.8374 -3.15234,-7.00195c-0.03809,-0.02191 -0.08057,-0.03577 -0.11914,-0.05664c-0.19531,-0.10577 -0.396,-0.20166 -0.60352,-0.28125c-0.09058,-0.03491 -0.18457,-0.06189 -0.27734,-0.0918c-0.16772,-0.05383 -0.33716,-0.10107 -0.51172,-0.13867c-0.09985,-0.02161 -0.20068,-0.04028 -0.30273,-0.05664c-0.18482,-0.02942 -0.375,-0.04852 -0.56641,-0.06055c-0.0918,-0.00586 -0.18237,-0.01569 -0.27539,-0.01758zM39.27344,9.76953c0.6673,0.03128 1.2887,0.20691 1.8457,0.52734c1.47559,0.84912 2.29199,2.604 2.29883,4.94141c0.0127,4.66064 -3.27441,10.35693 -7.32812,12.69727c-1.19434,0.68945 -2.35449,1.03906 -3.4082,1.03906c-0.75195,0 -1.44922,-0.17725 -2.06836,-0.5332c-1.47656,-0.8501 -2.29395,-2.60498 -2.30078,-4.94141c-0.01367,-4.65869 3.27441,-10.354 7.33008,-12.69531c1.2793,-0.73944 2.51869,-1.08729 3.63086,-1.03516zM39.05078,10.56641c-0.91309,0 -1.9375,0.31152 -3.00781,0.92969c-3.83496,2.21436 -6.94336,7.59814 -6.92969,12.00195c0.00488,2.03857 0.67871,3.54834 1.89844,4.25c1.23047,0.70752 2.89063,0.52832 4.67773,-0.50391c3.83301,-2.21338 6.94043,-7.59815 6.92773,-12.00391c-0.00488,-2.04004 -0.68066,-3.54883 -1.89844,-4.25c-0.49219,-0.2832 -1.05469,-0.42383 -1.66797,-0.42383zM36.01172,15.20898c0.04761,0.02698 0.07007,0.08069 0.07031,0.1582l0.00195,0.62305c0.51294,-0.2713 0.96875,-0.43597 1.36719,-0.49219c0.14746,-0.02441 0.21997,0.05292 0.2207,0.23438c0,0.09619 -0.02808,0.19952 -0.08594,0.31055c-0.05786,0.11261 -0.13208,0.19543 -0.22656,0.25c-0.03101,0.01794 -0.07422,0.02997 -0.12695,0.03711c-0.35522,0.06067 -0.73755,0.19891 -1.14648,0.41797l0.00586,2.14648c0.37817,-0.11737 0.69092,-0.18658 0.93945,-0.20898c0.24853,-0.02399 0.45703,0.03711 0.625,0.17773c0.16797,0.14075 0.25464,0.40411 0.25586,0.79492c0.00073,0.31952 -0.07495,0.65295 -0.22266,0.99609c-0.14917,0.34558 -0.35937,0.67261 -0.63281,0.98438c-0.2749,0.3125 -0.59155,0.58429 -0.95312,0.81641l0.00195,0.64063c0.00024,0.07751 -0.02197,0.15552 -0.06641,0.23242c-0.04443,0.0769 -0.10278,0.13763 -0.17578,0.17969c-0.06763,0.03894 -0.12524,0.04779 -0.16992,0.02539c-0.04468,-0.02234 -0.06616,-0.07642 -0.06641,-0.16016l-0.00195,-0.64062c-0.56543,0.28467 -1.09595,0.44891 -1.58789,0.49219c-0.06226,0.00635 -0.11426,-0.01294 -0.15625,-0.05859c-0.04199,-0.04407 -0.06421,-0.10608 -0.06445,-0.18359c-0.00024,-0.10236 0.03076,-0.21002 0.08984,-0.32031c0.06055,-0.1095 0.1377,-0.19153 0.23242,-0.24609c0.05127,-0.0296 0.10107,-0.04639 0.14844,-0.04883c0.42432,-0.02911 0.87012,-0.16455 1.33594,-0.41016l-0.00586,-2.07422c-0.35645,0.09711 -0.65723,0.15289 -0.90039,0.16602c-0.24341,0.01465 -0.44678,-0.04803 -0.60937,-0.18555c-0.1626,-0.13763 -0.24512,-0.38312 -0.24609,-0.73828c-0.00098,-0.31177 0.06567,-0.63885 0.19727,-0.98047c0.13281,-0.34082 0.33154,-0.67548 0.5957,-1.00195c0.26392,-0.328 0.58057,-0.61646 0.95313,-0.86719l-0.00195,-0.63281c-0.00024,-0.07751 0.02002,-0.15631 0.06445,-0.23633c0.04419,-0.08002 0.10229,-0.13873 0.16992,-0.17773c0.06738,-0.03894 0.12451,-0.04657 0.17188,-0.01953zM35.60938,17.05078c-0.31421,0.22968 -0.56494,0.48474 -0.75195,0.76367c-0.18848,0.28125 -0.28369,0.55829 -0.2832,0.83594c0.00098,0.26367 0.0896,0.41803 0.26563,0.46094c0.17578,0.04291 0.43481,0.01587 0.77539,-0.07812zM36.92383,19.62891c-0.18652,-0.02277 -0.46069,0.01611 -0.82812,0.11328l0.00391,1.9375c0.73828,-0.48517 1.10718,-1.03937 1.10547,-1.66602c-0.00073,-0.23419 -0.09595,-0.36279 -0.28125,-0.38477zM42.95703,26.32422c-0.08301,0.11902 -0.17651,0.23004 -0.26172,0.34766c-0.09204,0.12689 -0.18701,0.25201 -0.28125,0.37695c-0.22607,0.30066 -0.4541,0.59692 -0.69336,0.88672c-0.12109,0.14758 -0.24731,0.28748 -0.37109,0.43164c-0.08496,0.09833 -0.16748,0.20013 -0.25391,0.29687c1.03198,1.26019 1.52441,2.72638 1.375,4.16602c-0.15918,1.5376 -1.04102,2.96143 -2.48242,4.01172c-1.62305,1.18994 -3.77051,1.7793 -5.96289,1.7793c-2.38086,0 -4.81348,-0.69434 -6.67969,-2.07031c-1.70996,-1.27747 -2.70044,-2.92688 -2.83008,-4.66406l-0.5332,-0.31055c-0.99512,0.6333 -2.01465,1.36133 -3.03125,2.16602c-2.56445,2.04443 -4.20605,4.93262 -4.62109,8.12891c-0.65527,4.94287 -0.53027,5.46191 2.68555,18.7793l0.06641,0.27539c0.66699,2.73779 5.21191,4.3833 11.75977,4.27148c8.82617,-0.13672 18.94043,-3.42285 19.95703,-8.09961c1.78711,-18.25 1.8125,-21.24463 -1.69336,-26.83594c-1.34375,-2.14844 -3.62891,-3.60742 -6.14844,-3.9375zM40.56055,29.26367c-0.15674,0.16687 -0.31909,0.31683 -0.47852,0.47656c-0.15454,0.15497 -0.3081,0.31464 -0.46484,0.46289c-0.22583,0.21368 -0.45532,0.41193 -0.68555,0.61133c-0.15112,0.13086 -0.30029,0.26794 -0.45312,0.39258c-0.25928,0.21149 -0.52466,0.40424 -0.78906,0.59766c-0.12842,0.09399 -0.25317,0.19745 -0.38281,0.28711c-0.35718,0.24737 -0.71899,0.47552 -1.08398,0.68945c-0.03979,0.02344 -0.0791,0.05115 -0.11914,0.07422c-0.01978,0.01141 -0.03906,0.01801 -0.05859,0.0293c-0.40283,0.23016 -0.80273,0.43518 -1.20117,0.61523c-0.20874,0.09491 -0.41284,0.16693 -0.61914,0.24805c-0.19702,0.07672 -0.39478,0.15655 -0.58984,0.2207c-0.2561,0.08527 -0.50659,0.1535 -0.75781,0.2168c-0.13428,0.03339 -0.26758,0.06457 -0.40039,0.0918c-0.2749,0.05707 -0.54639,0.10266 -0.81445,0.13281c-0.10107,0.01129 -0.20044,0.01593 -0.30078,0.02344c-0.19482,0.01465 -0.39551,0.04883 -0.58594,0.04883c-0.43555,0.00018 -0.85425,-0.05219 -1.26367,-0.12891c-0.01831,-0.00336 -0.03638,-0.00623 -0.05469,-0.00977c-0.29053,-0.05707 -0.57422,-0.13415 -0.84961,-0.23047c-0.03638,-0.01251 -0.07349,-0.02197 -0.10937,-0.03516c-0.28955,-0.10736 -0.57104,-0.23145 -0.8418,-0.38281c-0.00244,-0.00134 -0.00537,-0.0025 -0.00781,-0.00391c-0.02832,-0.00879 -0.05664,-0.02148 -0.08398,-0.03711l-2.18164,-1.25977c0.2666,1.29639 1.1123,2.52197 2.43945,3.51367c3.32227,2.44873 8.56934,2.57764 11.69531,0.28711c1.27344,-0.92822 2.01953,-2.11963 2.15625,-3.44727c0.12305,-1.18945 -0.27051,-2.41309 -1.11328,-3.48437z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_kqst_project}</span>
                                    <br>
                                    <h2>{LANG.result_prequalification_project}</h2> <!-- Kết quả sơ tuyển nhà đầu tư -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_blue {class_total_kqlcndt}">
                        <div class="tabone">
                            <a href="#kqlcndt">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M9,4c-0.55469,0 -1,0.44922 -1,1v9.875c0,5.85938 2.14844,7.84766 4.40625,9.9375c0.52734,0.48828 1.05859,0.99219 1.59375,1.5625v11.625h22v-18c0,-0.55469 -0.44531,-1 -1,-1h-0.0625c-0.30078,-2.55469 -1.69531,-4.91406 -3.15625,-6.65625c-1.62891,-1.94141 -7.03125,-8 -7.03125,-8c-0.22266,-0.26172 -0.56641,-0.39453 -0.90625,-0.34375c-0.36328,0.05469 -0.66797,0.30469 -0.78906,0.65234c-0.125,0.34375 -0.05078,0.73047 0.19531,1.00391c0,0 5.41406,6.14844 6.96875,8c1.23047,1.46484 2.36719,3.44922 2.6875,5.34375h-11.875l-0.625,-1.375c-0.23047,-0.5 -0.84375,-0.73047 -1.34375,-0.5c-0.50391,0.23047 -0.69922,0.80859 -0.46875,1.3125l4.28125,9.28125c1.73047,3.24219 2.05078,5.03125 1.0625,5.78125c-0.56641,0.42969 -1.23828,0.60938 -1.9375,0.5c-1.05859,-0.16406 -2.10156,-0.91016 -2.9375,-2.09375c-1.67969,-2.375 -2.83203,-5.90234 -2.84375,-5.9375l-0.0625,-0.1875l-0.125,-0.15625c-0.76562,-0.90234 -1.54297,-1.59766 -2.28125,-2.28125c-2.17578,-2.01562 -3.75,-3.48437 -3.75,-8.46875v-9.875c0,-0.55078 -0.44531,-1 -1,-1zM8,30c-0.47266,0 -0.87109,0.32031 -0.96875,0.78125l-3,14c-0.0625,0.29688 -0.00391,0.60938 0.1875,0.84375c0.19141,0.23438 0.47656,0.375 0.78125,0.375h40c0.30469,0 0.58984,-0.14062 0.78125,-0.375c0.19141,-0.23437 0.25,-0.54687 0.1875,-0.84375l-3,-14c-0.09766,-0.46484 -0.49609,-0.78125 -0.96875,-0.78125h-4v6c1.10156,0 2,0.89844 2,2c0,1.10156 -0.89844,2 -2,2h-26c-1.10156,0 -2,-0.89844 -2,-2c0,-1.10156 0.89844,-2 2,-2v-6z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_kqlcndt}</span>
                                    <!-- Kết quả lựa chọn nhà đầu tư -->
                                    <br>
                                    <span>{LANG.kqlcndt}</span>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item {class_total_hanghoa}">
                        <div class="tabone">
                            <a href="#hanghoa">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M11,4c-0.23437,0 -0.44531,0.07031 -0.625,0.21875l-5.75,4.78125h19.375v-5zM26,4v5h19.375l-5.71875,-4.78125c-0.17969,-0.14844 -0.42187,-0.21875 -0.65625,-0.21875zM4,11v34c0,0.55469 0.44922,1 1,1h40c0.55469,0 1,-0.44531 1,-1v-34zM21.5,16h7c0.80078,0 1.5,0.69922 1.5,1.5c0,0.80078 -0.69922,1.5 -1.5,1.5h-7c-0.80078,0 -1.5,-0.69922 -1.5,-1.5c0,-0.80078 0.69922,-1.5 1.5,-1.5zM31,31l3,3h-2v4h-2v-4h-2zM38,31l3,3h-2v4h-2v-4h-2zM28,39h13v2h-13z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_hanghoa}</span>
                                    <br>
                                    <h2>{LANG.dmhh}</h2>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_purple {class_total_nhathau}">
                        <div class="tabone">
                            <a href="#nhathau">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M24.15625,0c-0.60937,0 -1.15625,0.53906 -1.15625,1.21875v7.78125c0,0.55078 -0.44922,1 -1,1c-0.55078,0 -1,-0.44922 -1,-1v-6.78125c-3.76172,1.03516 -5.84766,4.94531 -6,8.78125c-1.02734,0.36719 -2,1.19922 -2,4v0.78125l0.75,0.1875c0.16797,0.04297 4.20313,1.03125 11.25,1.03125c7.05078,0 11.08203,-0.98828 11.25,-1.03125l0.75,-0.1875v-0.78125c0,-2.82031 -0.97266,-3.64453 -2,-4c-0.15234,-3.83203 -2.24219,-7.74609 -6,-8.78125v6.78125c0,0.55078 -0.44531,1 -1,1c-0.55469,0 -1,-0.44922 -1,-1v-7.78125c0,-0.67969 -0.54297,-1.21875 -1.15625,-1.21875zM15.71875,18.375c-0.39453,0.55078 -0.68359,1.32031 -0.5625,2.34375c0.26563,2.20313 1.12109,3.10938 1.84375,3.46875c0.34375,1.73047 1.29688,3.67578 2.21875,4.59375v0.46875c0.00781,1.01563 0.00391,1.89453 -0.09375,3.03125c-0.61328,1.40625 -2.64844,2.23047 -5,3.15625c-3.90625,1.53516 -8.76172,3.43359 -9.125,9.5l-0.0625,1.0625h40.125l-0.0625,-1.0625c-0.36328,-6.06641 -5.19922,-7.96484 -9.09375,-9.5c-2.33984,-0.92578 -4.39062,-1.75 -5,-3.15625c-0.09766,-1.13672 -0.07031,-2.01953 -0.0625,-3.03125v-0.46875c0.89844,-0.91797 1.82031,-2.85937 2.15625,-4.59375c0.70313,-0.36328 1.58203,-1.27344 1.84375,-3.46875c0.12109,-1.02344 -0.16797,-1.79297 -0.5625,-2.34375c-1.98828,0.30078 -5.13672,0.625 -9.28125,0.625c-4.17187,0 -7.29687,-0.32422 -9.28125,-0.625z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_nhathau}</span>
                                    <br>
                                    <h2>{LANG.nhathau}</h2> <!-- Nhà thầu -->
                                </div>
                            </a>
                        </div>
                    </div>

                    <div class="item bg__red {class_total_stocks}">
                        <div class="tabone">
                            <a href="#stocks">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M8,3v2h-4v26h9.99805v-2h-7.99805v-22h4v-2h6v2h4v1.92188h2v-3.92187h-4v-2zM8,9v2h2v-2zM12,9v2h2v-2zM16,9v2h2v-2zM20,11v2h-4v26h9.99805v-2h-7.99805v-22h4v-2h6v2h4v1.92188h2v-3.92187h-4v-2zM8,13v2h2v-2zM12,13v2h2v-2zM8,17v2h2v-2zM12,17v2h2v-2zM20,17v2h2v-2zM24,17v2h2v-2zM28,17v2h2v-2zM32,19v2h-4v26h18v-26h-1h-3v-2zM8,21v2h2v-2zM12,21v2h2v-2zM20,21v2h2v-2zM24,21v2h2v-2zM34,21h6v2h4v22h-14v-22h4zM8,25v2h2v-2zM12,25v2h2v-2zM20,25v2h2v-2zM24,25v2h2v-2zM32,25v2h2v-2zM36,25v2h2v-2zM40,25v2h2v-2zM20,29v2h2v-2zM24,29v2h2v-2zM32,29v2h2v-2zM36,29v2h2v-2zM40,29v2h2v-2zM20,33v2h2v-2zM24,33v2h2v-2zM32,33v2h2v-2zM36,33v2h2v-2zM40,33v2h2v-2zM32,37v2h2v-2zM36,37v2h2v-2zM40,37v2h2v-2zM32,41v2h2v-2zM36,41v2h2v-2zM40,41v2h2v-2z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_stocks}</span>
                                    <br>
                                    <span>{LANG.stocks}</span> <!-- Nhà thầu là các công ty niêm yết -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg__green {class_total_ntvp}">
                        <div class="tabone">
                            <a href="#ntvipham">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M42.21875,32.22266l-10.21875,-14.44141v-14.78125c0,-1.65234 -1.34766,-3 -3,-3h-20c-1.65234,0 -3,1.34766 -3,3v29c0,1.65234 1.34766,3 3,3h4.66797l0.23438,3.85938c0,0.04297 0.00781,0.08203 0.01563,0.125c1.1875,6.38281 6.73438,11.01563 13.18359,11.01563h6.59766c1.98828,0 3.83203,-1.20703 4.71875,-3.10547l4.21484,-9.83594c0.60938,-1.57812 0.47266,-3.31641 -0.41406,-4.83594zM40.78125,36.30859l-4.1875,9.76953c-0.53516,1.14844 -1.69922,1.92188 -2.89453,1.92188h-6.59766c-5.46484,0 -10.16797,-3.91797 -11.20703,-9.32422l-1.09375,-17.97656c0,-1.03906 0.60156,-1.92578 1.53125,-2.25781c0.57031,-0.19922 1.17188,-0.16406 1.6875,0.09766c0.5,0.25781 0.85938,0.69531 1.03125,1.26953l5.59766,17.20313c0.17188,0.52344 0.73828,0.8125 1.26172,0.64063c0.52344,-0.17187 0.8125,-0.73437 0.64063,-1.26172l-0.45312,-1.39062h2.90234c1.65234,0 3,-1.34766 3,-3v-10.75781l8.53516,12.0625c0.54297,0.92969 0.625,2.00781 0.24609,3.00391z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class=" font-span ">{PROVINCETYPE.total_ntvp}</span>
                                    <br>
                                    <span>{LANG.nha_thau_vi_pham}</span> <!-- Nhà thầu vi phạm -->
                                </div>
                            </a>
                        </div>
                    </div>

                    <div class="item bg_yellow {class_total_bmt}">
                        <div class="tabone">
                            <a href="#bmt">
                                <svg class="svg-icon" width="44" height="44" viewBox=" 0 0 44 44 " fill=" none "
                                    xmlns=" http://www.w3.org/2000/svg ">
                                    <path d=" M36.6666 12.8333C38.9583 15.8949 40.3333 19.7083 40.3333 23.8333C40.3333 33.9533 32.12
                                            42.1666 22 42.1666C11.88 42.1666 3.66663 33.9533 3.66663 23.8333C3.66663 13.7133 11.88
                                            5.49992 22 5.49992M22 1.83325V23.8333L39.6 10.6333C35.585 5.29825 29.1866 1.83325 22
                                            1.83325Z " stroke=" white " stroke-width=" 3.66667 "></path>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_bmt}</span>
                                    <br>
                                    <h2>{LANG.benmoithau}</h2> <!-- Bên mời thầu -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_green {class_total_dau_gia}">
                        <div class="tabone">
                            <a href="#dau_gia">
                                <svg version="1.1" viewBox="0 0 1600 1600" width="44" height="44"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path transform="translate(1304,525)"
                                        d="m0 0 5 2 7 6 7 8 14 14 5 4v2h2l4 5 2 1v2l4 2 22 22 7 8 17 17 7-1 11-7 16-3h9l15 3 13 6 11 7 8 9 7 11 5 12 2 15-1 15-4 11-7 11-11 13-14 16-226 226-8 7-9 8-15 9-15 4h-22l-12-3-12-6-11-9-7-10-6-10-5-15v-13l3-15 8-17-2-6-7-8-5-4-7-8-53-53-6-5v-2l-4-2-8-8 1-6 6-7 10-8 14-15 3-1 2-4 8-8 8-7 8-9 6-5 2-3h2l2-4 10-9 2-3h2l2-4 8-8h2l2-4 12-12h2l2-4 112-112h2l2-4 8-7 17-17 5-7z"
                                        fill="#FEFEFE" />
                                    <path transform="translate(919,96)"
                                        d="m0 0h13l14 2 15 6 8 6 7 7 9 14 5 13 2 11-1 16-4 12-10 15 4 1 6 7 20 18 39 39 7 8 17 16 4 6-2 4-13 12-53 53-5 6-7 6-7 8-5 4-7 8-7 7-6 5-7 8-5 4-7 8-7 7-6 5-7 8-5 4-5 5-8 9-8 7-8 9-8 7-8 9-8 7-4 5-8 7-26 26-7 8-9 10-2 2-4-1-13-11-77-77-5-4-9 3-10 5-8 2-9 1h-9l-14-3-16-8-13-12-7-11-5-11-2-7v-21l3-12 6-12 8-10 11-12 7-7 5-6 8-7 2-3h2l2-4 6-6h2l2-4 157-157 7-8 23-23h2l1-3 8-7 14-13 12-9 11-5z"
                                        fill="#FEFEFE" />
                                    <path transform="translate(471,1028)"
                                        d="m0 0 5 2 10 11 7 8 45 45 2 1v2l4 2 26 26-1 4-11 12-7 8-2 3h-2l-2 4-16 17-7 7-7 8-15 16-9 10-7 8h-2l-2 4-7 7-7 8-24 26-14 15-12 13-11 12-4 5-16 16-7 8-6 7-5 5-16 17-9 10-8 8-7 8-16 17-11 12-4 5h-2l-2 4-13 13-7 8-7 7-2 3h-2l-2 4-9 9-11 9-7 6-14 8-12 4-15 3h-15l-18-3-16-6-13-9-10-9-9-12-8-15-5-14-2-15 1-17 4-16 8-16 10-13 2-3h2l2-4 12-12 8-7 16-15 12-11 26-24 4-4h2v-2l8-7 8-8 8-7 10-10 8-7 8-8 8-7 7-7 8-7 8-8 8-7 9-9 8-7 4-2v-2l8-7 12-11 15-14 24-22 10-9 7-7 8-7 12-12 8-7 12-11 8-7 8-8 8-7 12-12h2l1-3z"
                                        fill="#fff" />
                                    <path transform="translate(1112,333)"
                                        d="m0 0 4 1 8 7 7 8 29 29 6 5v2h2l7 8 5 4 7 8 14 13v2l4 2 17 17v2l4 2 12 13 15 15 8 7 5 7-2 4-13 12-69 69-5 6-8 7-8 9-8 7-8 9-8 7-8 9-8 7-8 9-8 7-8 9-8 7-62 62h-2l-2 4-8 8-9 11-4 5-5-1-13-10-7-8-49-49-6-5-7-8-59-59-2-1v-2l-4-2-4-5 1-5 7-8 5-4h2v-2l6-5 7-8 6-5 6-7 8-7 7-7 1-2h2l2-4 8-7 8-9 6-5 2-3h2l2-4 10-9 7-8 119-119h2l2-4h2l2-4 8-7 17-17 5-7z"
                                        fill="#fff" />
                                    <path transform="translate(849,676)"
                                        d="m0 0 6 2 13 13 7 8 10 10 8 7 22 22 6 7v5l-8 10-14 14-7 8-7 7-7 8-8 8-7 8-9 10-12 13-9 10-7 8-14 15-29 31-11 12-14 15-7 7-7 8-7 7-7 8-7 7-7 8-16 17-14 15-7 8-30 32-10 10-9 11-12 13-4 2-7-7-7-5-7-8-7-6v-2l-4-2-9-9-8-7-19-19-7-8-10-10-5-4-5-7 1-6 14-11 12-12 11-9 7-7 8-7 15-14 8-7 50-47 3-3h2v-2l8-7 15-14 8-8 8-7 10-10 8-7 9-9 8-7 4-2v-2l8-7 12-11 17-16 16-15 3-1v-2l8-7 17-16 10-9 11-9 8-8 7-8z"
                                        fill="#fff" />
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_dau_gia}</span>
                                    <br>
                                    <span>{LANG.dau_gia}</span> <!-- Thông báo đấu giá -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_red {class_total_dau_gia_select}">
                        <div class="tabone">
                            <a href="#dau_gia_select">
                                <svg version="1.1" viewBox="0 0 1600 1600" width="44" height="44"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path transform="translate(1304,525)"
                                        d="m0 0 5 2 7 6 7 8 14 14 5 4v2h2l4 5 2 1v2l4 2 22 22 7 8 17 17 7-1 11-7 16-3h9l15 3 13 6 11 7 8 9 7 11 5 12 2 15-1 15-4 11-7 11-11 13-14 16-226 226-8 7-9 8-15 9-15 4h-22l-12-3-12-6-11-9-7-10-6-10-5-15v-13l3-15 8-17-2-6-7-8-5-4-7-8-53-53-6-5v-2l-4-2-8-8 1-6 6-7 10-8 14-15 3-1 2-4 8-8 8-7 8-9 6-5 2-3h2l2-4 10-9 2-3h2l2-4 8-8h2l2-4 12-12h2l2-4 112-112h2l2-4 8-7 17-17 5-7z"
                                        fill="#FEFEFE" />
                                    <path transform="translate(919,96)"
                                        d="m0 0h13l14 2 15 6 8 6 7 7 9 14 5 13 2 11-1 16-4 12-10 15 4 1 6 7 20 18 39 39 7 8 17 16 4 6-2 4-13 12-53 53-5 6-7 6-7 8-5 4-7 8-7 7-6 5-7 8-5 4-7 8-7 7-6 5-7 8-5 4-5 5-8 9-8 7-8 9-8 7-8 9-8 7-4 5-8 7-26 26-7 8-9 10-2 2-4-1-13-11-77-77-5-4-9 3-10 5-8 2-9 1h-9l-14-3-16-8-13-12-7-11-5-11-2-7v-21l3-12 6-12 8-10 11-12 7-7 5-6 8-7 2-3h2l2-4 6-6h2l2-4 157-157 7-8 23-23h2l1-3 8-7 14-13 12-9 11-5z"
                                        fill="#FEFEFE" />
                                    <path transform="translate(471,1028)"
                                        d="m0 0 5 2 10 11 7 8 45 45 2 1v2l4 2 26 26-1 4-11 12-7 8-2 3h-2l-2 4-16 17-7 7-7 8-15 16-9 10-7 8h-2l-2 4-7 7-7 8-24 26-14 15-12 13-11 12-4 5-16 16-7 8-6 7-5 5-16 17-9 10-8 8-7 8-16 17-11 12-4 5h-2l-2 4-13 13-7 8-7 7-2 3h-2l-2 4-9 9-11 9-7 6-14 8-12 4-15 3h-15l-18-3-16-6-13-9-10-9-9-12-8-15-5-14-2-15 1-17 4-16 8-16 10-13 2-3h2l2-4 12-12 8-7 16-15 12-11 26-24 4-4h2v-2l8-7 8-8 8-7 10-10 8-7 8-8 8-7 7-7 8-7 8-8 8-7 9-9 8-7 4-2v-2l8-7 12-11 15-14 24-22 10-9 7-7 8-7 12-12 8-7 12-11 8-7 8-8 8-7 12-12h2l1-3z"
                                        fill="#fff" />
                                    <path transform="translate(1112,333)"
                                        d="m0 0 4 1 8 7 7 8 29 29 6 5v2h2l7 8 5 4 7 8 14 13v2l4 2 17 17v2l4 2 12 13 15 15 8 7 5 7-2 4-13 12-69 69-5 6-8 7-8 9-8 7-8 9-8 7-8 9-8 7-8 9-8 7-8 9-8 7-62 62h-2l-2 4-8 8-9 11-4 5-5-1-13-10-7-8-49-49-6-5-7-8-59-59-2-1v-2l-4-2-4-5 1-5 7-8 5-4h2v-2l6-5 7-8 6-5 6-7 8-7 7-7 1-2h2l2-4 8-7 8-9 6-5 2-3h2l2-4 10-9 7-8 119-119h2l2-4h2l2-4 8-7 17-17 5-7z"
                                        fill="#fff" />
                                    <path transform="translate(849,676)"
                                        d="m0 0 6 2 13 13 7 8 10 10 8 7 22 22 6 7v5l-8 10-14 14-7 8-7 7-7 8-8 8-7 8-9 10-12 13-9 10-7 8-14 15-29 31-11 12-14 15-7 7-7 8-7 7-7 8-7 7-7 8-16 17-14 15-7 8-30 32-10 10-9 11-12 13-4 2-7-7-7-5-7-8-7-6v-2l-4-2-9-9-8-7-19-19-7-8-10-10-5-4-5-7 1-6 14-11 12-12 11-9 7-7 8-7 15-14 8-7 50-47 3-3h2v-2l8-7 15-14 8-8 8-7 10-10 8-7 9-9 8-7 4-2v-2l8-7 12-11 17-16 16-15 3-1v-2l8-7 17-16 10-9 11-9 8-8 7-8z"
                                        fill="#fff" />
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_dau_gia_select}</span>
                                    <br>
                                    <span>{LANG.dau_gia_select}</span> <!-- Thông báo chọn tổ chức đấu giá -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_organ {class_total_dau_gia_bidder}">
                        <div class="tabone">
                            <a href="#dau_gia_bidder">
                                <svg version="1.1" viewBox="0 0 1600 1600" width="44" height="44"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path transform="translate(1304,525)"
                                        d="m0 0 5 2 7 6 7 8 14 14 5 4v2h2l4 5 2 1v2l4 2 22 22 7 8 17 17 7-1 11-7 16-3h9l15 3 13 6 11 7 8 9 7 11 5 12 2 15-1 15-4 11-7 11-11 13-14 16-226 226-8 7-9 8-15 9-15 4h-22l-12-3-12-6-11-9-7-10-6-10-5-15v-13l3-15 8-17-2-6-7-8-5-4-7-8-53-53-6-5v-2l-4-2-8-8 1-6 6-7 10-8 14-15 3-1 2-4 8-8 8-7 8-9 6-5 2-3h2l2-4 10-9 2-3h2l2-4 8-8h2l2-4 12-12h2l2-4 112-112h2l2-4 8-7 17-17 5-7z"
                                        fill="#FEFEFE" />
                                    <path transform="translate(919,96)"
                                        d="m0 0h13l14 2 15 6 8 6 7 7 9 14 5 13 2 11-1 16-4 12-10 15 4 1 6 7 20 18 39 39 7 8 17 16 4 6-2 4-13 12-53 53-5 6-7 6-7 8-5 4-7 8-7 7-6 5-7 8-5 4-7 8-7 7-6 5-7 8-5 4-5 5-8 9-8 7-8 9-8 7-8 9-8 7-4 5-8 7-26 26-7 8-9 10-2 2-4-1-13-11-77-77-5-4-9 3-10 5-8 2-9 1h-9l-14-3-16-8-13-12-7-11-5-11-2-7v-21l3-12 6-12 8-10 11-12 7-7 5-6 8-7 2-3h2l2-4 6-6h2l2-4 157-157 7-8 23-23h2l1-3 8-7 14-13 12-9 11-5z"
                                        fill="#FEFEFE" />
                                    <path transform="translate(471,1028)"
                                        d="m0 0 5 2 10 11 7 8 45 45 2 1v2l4 2 26 26-1 4-11 12-7 8-2 3h-2l-2 4-16 17-7 7-7 8-15 16-9 10-7 8h-2l-2 4-7 7-7 8-24 26-14 15-12 13-11 12-4 5-16 16-7 8-6 7-5 5-16 17-9 10-8 8-7 8-16 17-11 12-4 5h-2l-2 4-13 13-7 8-7 7-2 3h-2l-2 4-9 9-11 9-7 6-14 8-12 4-15 3h-15l-18-3-16-6-13-9-10-9-9-12-8-15-5-14-2-15 1-17 4-16 8-16 10-13 2-3h2l2-4 12-12 8-7 16-15 12-11 26-24 4-4h2v-2l8-7 8-8 8-7 10-10 8-7 8-8 8-7 7-7 8-7 8-8 8-7 9-9 8-7 4-2v-2l8-7 12-11 15-14 24-22 10-9 7-7 8-7 12-12 8-7 12-11 8-7 8-8 8-7 12-12h2l1-3z"
                                        fill="#fff" />
                                    <path transform="translate(1112,333)"
                                        d="m0 0 4 1 8 7 7 8 29 29 6 5v2h2l7 8 5 4 7 8 14 13v2l4 2 17 17v2l4 2 12 13 15 15 8 7 5 7-2 4-13 12-69 69-5 6-8 7-8 9-8 7-8 9-8 7-8 9-8 7-8 9-8 7-8 9-8 7-62 62h-2l-2 4-8 8-9 11-4 5-5-1-13-10-7-8-49-49-6-5-7-8-59-59-2-1v-2l-4-2-4-5 1-5 7-8 5-4h2v-2l6-5 7-8 6-5 6-7 8-7 7-7 1-2h2l2-4 8-7 8-9 6-5 2-3h2l2-4 10-9 7-8 119-119h2l2-4h2l2-4 8-7 17-17 5-7z"
                                        fill="#fff" />
                                    <path transform="translate(849,676)"
                                        d="m0 0 6 2 13 13 7 8 10 10 8 7 22 22 6 7v5l-8 10-14 14-7 8-7 7-7 8-8 8-7 8-9 10-12 13-9 10-7 8-14 15-29 31-11 12-14 15-7 7-7 8-7 7-7 8-7 7-7 8-16 17-14 15-7 8-30 32-10 10-9 11-12 13-4 2-7-7-7-5-7-8-7-6v-2l-4-2-9-9-8-7-19-19-7-8-10-10-5-4-5-7 1-6 14-11 12-12 11-9 7-7 8-7 15-14 8-7 50-47 3-3h2v-2l8-7 15-14 8-8 8-7 10-10 8-7 9-9 8-7 4-2v-2l8-7 12-11 17-16 16-15 3-1v-2l8-7 17-16 10-9 11-9 8-8 7-8z"
                                        fill="#fff" />
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_dau_gia_bidder}</span>
                                    <br>
                                    <span>{LANG.dau_gia_bidder}</span> <!-- Tổ chức đấu giá -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_purple {class_total_dau_gia_dgv}">
                        <div class="tabone">
                            <a href="#dau_gia_dgv">
                                <svg version="1.1" viewBox="0 0 1600 1600" width="44" height="44"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path transform="translate(1304,525)"
                                        d="m0 0 5 2 7 6 7 8 14 14 5 4v2h2l4 5 2 1v2l4 2 22 22 7 8 17 17 7-1 11-7 16-3h9l15 3 13 6 11 7 8 9 7 11 5 12 2 15-1 15-4 11-7 11-11 13-14 16-226 226-8 7-9 8-15 9-15 4h-22l-12-3-12-6-11-9-7-10-6-10-5-15v-13l3-15 8-17-2-6-7-8-5-4-7-8-53-53-6-5v-2l-4-2-8-8 1-6 6-7 10-8 14-15 3-1 2-4 8-8 8-7 8-9 6-5 2-3h2l2-4 10-9 2-3h2l2-4 8-8h2l2-4 12-12h2l2-4 112-112h2l2-4 8-7 17-17 5-7z"
                                        fill="#FEFEFE" />
                                    <path transform="translate(919,96)"
                                        d="m0 0h13l14 2 15 6 8 6 7 7 9 14 5 13 2 11-1 16-4 12-10 15 4 1 6 7 20 18 39 39 7 8 17 16 4 6-2 4-13 12-53 53-5 6-7 6-7 8-5 4-7 8-7 7-6 5-7 8-5 4-7 8-7 7-6 5-7 8-5 4-5 5-8 9-8 7-8 9-8 7-8 9-8 7-4 5-8 7-26 26-7 8-9 10-2 2-4-1-13-11-77-77-5-4-9 3-10 5-8 2-9 1h-9l-14-3-16-8-13-12-7-11-5-11-2-7v-21l3-12 6-12 8-10 11-12 7-7 5-6 8-7 2-3h2l2-4 6-6h2l2-4 157-157 7-8 23-23h2l1-3 8-7 14-13 12-9 11-5z"
                                        fill="#FEFEFE" />
                                    <path transform="translate(471,1028)"
                                        d="m0 0 5 2 10 11 7 8 45 45 2 1v2l4 2 26 26-1 4-11 12-7 8-2 3h-2l-2 4-16 17-7 7-7 8-15 16-9 10-7 8h-2l-2 4-7 7-7 8-24 26-14 15-12 13-11 12-4 5-16 16-7 8-6 7-5 5-16 17-9 10-8 8-7 8-16 17-11 12-4 5h-2l-2 4-13 13-7 8-7 7-2 3h-2l-2 4-9 9-11 9-7 6-14 8-12 4-15 3h-15l-18-3-16-6-13-9-10-9-9-12-8-15-5-14-2-15 1-17 4-16 8-16 10-13 2-3h2l2-4 12-12 8-7 16-15 12-11 26-24 4-4h2v-2l8-7 8-8 8-7 10-10 8-7 8-8 8-7 7-7 8-7 8-8 8-7 9-9 8-7 4-2v-2l8-7 12-11 15-14 24-22 10-9 7-7 8-7 12-12 8-7 12-11 8-7 8-8 8-7 12-12h2l1-3z"
                                        fill="#fff" />
                                    <path transform="translate(1112,333)"
                                        d="m0 0 4 1 8 7 7 8 29 29 6 5v2h2l7 8 5 4 7 8 14 13v2l4 2 17 17v2l4 2 12 13 15 15 8 7 5 7-2 4-13 12-69 69-5 6-8 7-8 9-8 7-8 9-8 7-8 9-8 7-8 9-8 7-8 9-8 7-62 62h-2l-2 4-8 8-9 11-4 5-5-1-13-10-7-8-49-49-6-5-7-8-59-59-2-1v-2l-4-2-4-5 1-5 7-8 5-4h2v-2l6-5 7-8 6-5 6-7 8-7 7-7 1-2h2l2-4 8-7 8-9 6-5 2-3h2l2-4 10-9 7-8 119-119h2l2-4h2l2-4 8-7 17-17 5-7z"
                                        fill="#fff" />
                                    <path transform="translate(849,676)"
                                        d="m0 0 6 2 13 13 7 8 10 10 8 7 22 22 6 7v5l-8 10-14 14-7 8-7 7-7 8-8 8-7 8-9 10-12 13-9 10-7 8-14 15-29 31-11 12-14 15-7 7-7 8-7 7-7 8-7 7-7 8-16 17-14 15-7 8-30 32-10 10-9 11-12 13-4 2-7-7-7-5-7-8-7-6v-2l-4-2-9-9-8-7-19-19-7-8-10-10-5-4-5-7 1-6 14-11 12-12 11-9 7-7 8-7 15-14 8-7 50-47 3-3h2v-2l8-7 15-14 8-8 8-7 10-10 8-7 9-9 8-7 4-2v-2l8-7 12-11 17-16 16-15 3-1v-2l8-7 17-16 10-9 11-9 8-8 7-8z"
                                        fill="#fff" />
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_dau_gia_dgv}</span>
                                    <br>
                                    <span>{LANG.dau_gia_dgv}</span> <!-- Đấu giá viên -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_pink {class_total_dau_gia_deparment}">
                        <div class="tabone">
                            <a href="#dau_gia_department">
                                <svg version="1.1" viewBox="0 0 1600 1600" width="44" height="44"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path transform="translate(1304,525)"
                                        d="m0 0 5 2 7 6 7 8 14 14 5 4v2h2l4 5 2 1v2l4 2 22 22 7 8 17 17 7-1 11-7 16-3h9l15 3 13 6 11 7 8 9 7 11 5 12 2 15-1 15-4 11-7 11-11 13-14 16-226 226-8 7-9 8-15 9-15 4h-22l-12-3-12-6-11-9-7-10-6-10-5-15v-13l3-15 8-17-2-6-7-8-5-4-7-8-53-53-6-5v-2l-4-2-8-8 1-6 6-7 10-8 14-15 3-1 2-4 8-8 8-7 8-9 6-5 2-3h2l2-4 10-9 2-3h2l2-4 8-8h2l2-4 12-12h2l2-4 112-112h2l2-4 8-7 17-17 5-7z"
                                        fill="#FEFEFE" />
                                    <path transform="translate(919,96)"
                                        d="m0 0h13l14 2 15 6 8 6 7 7 9 14 5 13 2 11-1 16-4 12-10 15 4 1 6 7 20 18 39 39 7 8 17 16 4 6-2 4-13 12-53 53-5 6-7 6-7 8-5 4-7 8-7 7-6 5-7 8-5 4-7 8-7 7-6 5-7 8-5 4-5 5-8 9-8 7-8 9-8 7-8 9-8 7-4 5-8 7-26 26-7 8-9 10-2 2-4-1-13-11-77-77-5-4-9 3-10 5-8 2-9 1h-9l-14-3-16-8-13-12-7-11-5-11-2-7v-21l3-12 6-12 8-10 11-12 7-7 5-6 8-7 2-3h2l2-4 6-6h2l2-4 157-157 7-8 23-23h2l1-3 8-7 14-13 12-9 11-5z"
                                        fill="#FEFEFE" />
                                    <path transform="translate(471,1028)"
                                        d="m0 0 5 2 10 11 7 8 45 45 2 1v2l4 2 26 26-1 4-11 12-7 8-2 3h-2l-2 4-16 17-7 7-7 8-15 16-9 10-7 8h-2l-2 4-7 7-7 8-24 26-14 15-12 13-11 12-4 5-16 16-7 8-6 7-5 5-16 17-9 10-8 8-7 8-16 17-11 12-4 5h-2l-2 4-13 13-7 8-7 7-2 3h-2l-2 4-9 9-11 9-7 6-14 8-12 4-15 3h-15l-18-3-16-6-13-9-10-9-9-12-8-15-5-14-2-15 1-17 4-16 8-16 10-13 2-3h2l2-4 12-12 8-7 16-15 12-11 26-24 4-4h2v-2l8-7 8-8 8-7 10-10 8-7 8-8 8-7 7-7 8-7 8-8 8-7 9-9 8-7 4-2v-2l8-7 12-11 15-14 24-22 10-9 7-7 8-7 12-12 8-7 12-11 8-7 8-8 8-7 12-12h2l1-3z"
                                        fill="#fff" />
                                    <path transform="translate(1112,333)"
                                        d="m0 0 4 1 8 7 7 8 29 29 6 5v2h2l7 8 5 4 7 8 14 13v2l4 2 17 17v2l4 2 12 13 15 15 8 7 5 7-2 4-13 12-69 69-5 6-8 7-8 9-8 7-8 9-8 7-8 9-8 7-8 9-8 7-8 9-8 7-62 62h-2l-2 4-8 8-9 11-4 5-5-1-13-10-7-8-49-49-6-5-7-8-59-59-2-1v-2l-4-2-4-5 1-5 7-8 5-4h2v-2l6-5 7-8 6-5 6-7 8-7 7-7 1-2h2l2-4 8-7 8-9 6-5 2-3h2l2-4 10-9 7-8 119-119h2l2-4h2l2-4 8-7 17-17 5-7z"
                                        fill="#fff" />
                                    <path transform="translate(849,676)"
                                        d="m0 0 6 2 13 13 7 8 10 10 8 7 22 22 6 7v5l-8 10-14 14-7 8-7 7-7 8-8 8-7 8-9 10-12 13-9 10-7 8-14 15-29 31-11 12-14 15-7 7-7 8-7 7-7 8-7 7-7 8-16 17-14 15-7 8-30 32-10 10-9 11-12 13-4 2-7-7-7-5-7-8-7-6v-2l-4-2-9-9-8-7-19-19-7-8-10-10-5-4-5-7 1-6 14-11 12-12 11-9 7-7 8-7 15-14 8-7 50-47 3-3h2v-2l8-7 15-14 8-8 8-7 10-10 8-7 9-9 8-7 4-2v-2l8-7 12-11 17-16 16-15 3-1v-2l8-7 17-16 10-9 11-9 8-8 7-8z"
                                        fill="#fff" />
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_dau_gia_deparment}</span>
                                    <br>
                                    <span>{LANG.title_dau_gia_department}</span> <!-- Sở tư pháp -->
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item {class_total_contract}">
                        <div class="tabone">
                            <div class="itema">
                                <svg class="svg-icon" width="44" height="44" viewBox="0 0 44 44" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_119_13)">
                                        <path
                                            d="M42.4286 42.4287H1.57141V1.57153H14.1428V42.4287V20.4287H26.7143V42.4287V11.0001H39.2857V42.4287"
                                            stroke="white" stroke-width="3.14286" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_119_13">
                                            <rect width="44" height="44" fill="white"></rect>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_contract}</span>
                                    <br>
                                    <h2>{LANG.goithau}</h2>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="item bg_red {class_total_vbdt}">
                        <div class="tabone">
                            <a href="#legal-documents">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(4,4)">
                                            <path
                                                d="M13,0c-0.55228,0 -1,0.44772 -1,1v24c0,0.55228 0.44772,1 1,1c0.55228,0 1,-0.44772 1,-1v-23h30.58594l5.41406,5.41406v23.58594c0,0.55228 0.44772,1 1,1c0.55228,0 1,-0.44772 1,-1v-24c-0.00004,-0.2652 -0.10542,-0.51953 -0.29297,-0.70703l-6,-6c-0.18751,-0.18755 -0.44183,-0.29293 -0.70703,-0.29297zM31.9707,4c-0.54081,0.01585 -0.97094,0.45896 -0.9707,1v3h-1c-0.79609,-0.00232 -1.55996,0.3142 -2.12109,0.87891l-0.82617,0.82617c-0.18766,0.18874 -0.44283,0.29488 -0.70898,0.29492h-6.34375c-0.55228,0 -1,0.44772 -1,1c0,0.55228 0.44772,1 1,1h1.38281l-2.27734,4.55273c0,0.009 0.00019,0.0193 -0.00781,0.0293c-0.02332,0.06193 -0.04033,0.12606 -0.05078,0.19141c-0.02062,0.06479 -0.03436,0.13156 -0.04102,0.19922c0,0.011 -0.00586,0.02025 -0.00586,0.03125v1c0,2.20914 1.79086,4 4,4c2.20914,0 4,-1.79086 4,-4v-1.00391c0,-0.011 -0.00586,-0.0183 -0.00586,-0.0293c-0.02281,-0.13127 -0.05347,-0.26106 -0.0918,-0.38867c0,-0.01 0.00019,-0.0203 -0.00781,-0.0293l-2.27734,-4.55273h1.72656c0.79589,0.00132 1.55939,-0.31505 2.12109,-0.87891l0.82813,-0.82812c0.188,-0.18681 0.442,-0.29206 0.70703,-0.29297h1v14h-5c-1.65685,0 -3,1.34315 -3,3v2c0,0.55228 0.44772,1 1,1h16c0.55228,0 1,-0.44772 1,-1v-2c0,-1.65685 -1.34315,-3 -3,-3h-5v-14h1c0.26503,0.00091 0.51903,0.10616 0.70703,0.29297l0.82813,0.82813c0.56164,0.56395 1.32518,0.88033 2.12109,0.87891h1.72656l-2.2832,4.55273c0,0.009 0.00019,0.0193 -0.00781,0.0293c-0.02332,0.06193 -0.04033,0.12606 -0.05078,0.19141c-0.01995,0.06486 -0.03305,0.13163 -0.03906,0.19922c0,0.011 -0.00781,0.02025 -0.00781,0.03125v1c0,2.20914 1.79086,4 4,4c2.20914,0 4,-1.79086 4,-4v-1.00391c0,-0.011 -0.00586,-0.0183 -0.00586,-0.0293c-0.02217,-0.13123 -0.05217,-0.26102 -0.08984,-0.38867c0,-0.01 0.00019,-0.0203 -0.00781,-0.0293l-2.27344,-4.55273h1.38281c0.55228,0 1,-0.44772 1,-1c0,-0.55228 -0.44772,-1 -1,-1h-6.34375c-0.2652,-0.00004 -0.51953,-0.10542 -0.70703,-0.29297l-0.82812,-0.82812c-0.56113,-0.5647 -1.32501,-0.88122 -2.12109,-0.87891h-1v-3c0.00012,-0.27037 -0.10925,-0.52927 -0.30317,-0.71767c-0.19392,-0.1884 -0.45587,-0.29025 -0.72612,-0.28233zM23,13.23633l1.38281,2.76367h-2.76562zM41,13.23633l1.38281,2.76367h-2.76562zM21,18h4c0,1.10457 -0.89543,2 -2,2c-1.10457,0 -2,-0.89543 -2,-2zM39,18h4c0,1.10457 -0.89543,2 -2,2c-1.10457,0 -2,-0.89543 -2,-2zM26,26h12c0.55228,0 1,0.44772 1,1v1h-14v-1c0,-0.55228 0.44772,-1 1,-1zM9,28c-1.65685,0 -3,1.34315 -3,3v30c0,1.65685 1.34315,3 3,3h46c1.65685,0 3,-1.34315 3,-3v-24c0,-1.65685 -1.34315,-3 -3,-3h-30.92969c-0.33435,-0.00001 -0.64657,-0.16711 -0.83203,-0.44531l-2.8125,-4.21875c-0.55638,-0.83459 -1.49305,-1.33591 -2.49609,-1.33594zM9,30h8.92969c0.33435,0.00001 0.64657,0.16711 0.83203,0.44531l2.8125,4.21875c0.55638,0.83459 1.49305,1.33591 2.49609,1.33594h30.92969c0.55228,0 1,0.44772 1,1v24c0,0.55228 -0.44772,1 -1,1h-46c-0.55228,0 -1,-0.44772 -1,-1v-30c0,-0.55228 0.44772,-1 1,-1zM43,52c-0.55228,0 -1,0.44772 -1,1c0,0.55228 0.44772,1 1,1h8c0.55228,0 1,-0.44772 1,-1c0,-0.55228 -0.44772,-1 -1,-1zM43,56c-0.55228,0 -1,0.44772 -1,1c0,0.55228 0.44772,1 1,1h8c0.55228,0 1,-0.44772 1,-1c0,-0.55228 -0.44772,-1 -1,-1z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_vbdt}</span>
                                    <br>
                                    <h2>{LANG.van_ban_phap_quy}</h2>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_purple {class_total_project}">
                        <div class="tabone">
                            <a href="#qhxddt">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M10.5,0c-3.03125,0 -5.5,2.46875 -5.5,5.5c0,3.03125 2.46875,5.5 5.5,5.5c3.03125,0 5.5,-2.46875 5.5,-5.5c0,-3.03125 -2.46875,-5.5 -5.5,-5.5zM6,12c-3.38672,0 -4.25391,2.05078 -4.4375,4.15625c-0.12109,1.39063 -1.51562,11.79688 -1.5625,13.46875c-0.04297,1.5625 0.55078,2.375 1.71875,2.375c0.74219,0 1.71094,-0.52734 1.84375,-1.75l1.4375,-11.25c0.00781,4.58984 -0.95703,25.96484 -1,27.28125c-0.06641,2.08594 0.55469,3.71875 2.09375,3.71875c1.53906,0 2.46094,-0.80859 2.65625,-2.71875c0.14453,-1.43359 1.00391,-12.94922 1.4375,-17.625c0.43359,4.67578 1.32422,16.19141 1.46875,17.625c0.19531,1.91016 0.73438,2.71875 2.5,2.71875c1.76563,0 2.06641,-1.63281 2,-3.71875c-0.05859,-1.86719 -0.09375,-17.35547 -0.15625,-20.3125v-8.96875c2.375,1.14063 8.70703,3.84375 11.375,5h-6.375c-0.55078,0 -1,0.44922 -1,1v9c0,0.55469 0.44922,1 1,1h29v-8l-3.53125,6h-4.6875l4.1875,-7h4.03125v-2h-18.1875c0.01172,-0.01953 0.01953,-0.03906 0.03125,-0.0625c0.39844,-0.82422 -0.08984,-1.82812 -1.3125,-2.40625c-1.47266,-0.69531 -12.5625,-6.46875 -12.5625,-6.46875c-1.4375,-0.90625 -2.33203,-1.0625 -3.4375,-1.0625zM22,24h4.625l-4.1875,7h-0.4375zM33.96875,24h4.65625l-4.1875,7h-4.65625zM24,34v15c0,0.55469 0.44922,1 1,1h4c0.55469,0 1,-0.44531 1,-1v-15zM43,34v15c0,0.55469 0.44531,1 1,1h4c0.55469,0 1,-0.44531 1,-1v-15zM31,41v5h11v-5z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class=" font-span ">{PROVINCETYPE.total_project}</span>
                                    <br>
                                    <h2>{LANG.quyhoachxaydungvadothi}</h2>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_yellow {class_total_tochuc}">
                        <div class="tabone">
                            <a href="#tcxd">
                                <svg class="svg-icon" width="44" height="44" viewBox="0 0 44 44" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M29.0767 8.04841V35.5484C29.0767 38.6101 31.1667 40.3334 33.4584 40.3334C35.5484 40.3334 37.84 38.8851 37.84 35.5484V8.25008C37.84 5.42675 35.75 3.66675 33.4584 3.66675C31.1667 3.66675 29.0767 5.61008 29.0767 8.04841ZM17.6184 22.0001V35.5484C17.6184 38.6284 19.745 40.3334 22 40.3334C24.09 40.3334 26.3817 38.8851 26.3817 35.5484V22.2017C26.3817 19.3784 24.2917 17.6184 22 17.6184C19.7084 17.6184 17.6184 19.5617 17.6184 22.0001ZM10.5417 31.5884C12.9617 31.5884 14.9234 33.5501 14.9234 35.9517C14.9234 37.1138 14.4617 38.2283 13.64 39.0501C12.8183 39.8718 11.7038 40.3334 10.5417 40.3334C9.37961 40.3334 8.26512 39.8718 7.44339 39.0501C6.62167 38.2283 6.16003 37.1138 6.16003 35.9517C6.16003 33.5501 8.1217 31.5884 10.5417 31.5884Z"
                                        fill="white"></path>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_tochuc}</span>
                                    <br>
                                    <h2>{LANG.tochucxaydung}</h2>
                                </div>
                            </a>
                        </div>
                    </div>

                    <div class="item bg__red {class_total_las}">
                        <div class="tabone ">
                            <a href="#lasxd">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(4,4)">
                                            <path
                                                d="M29.99805,0v25.29492c-2.89,0.861 -4.99805,3.53508 -4.99805,6.70508c0,3.866 3.134,7 7,7c3.866,0 7,-3.134 7,-7c0,-3.171 -2.10995,-5.84508 -5.00195,-6.70508v-25.29492zM13.34375,10.30078l-2.82812,2.82813l6.88672,6.88672c-2.915,2.578 -4.79039,6.12638 -5.27539,9.98438h-6.12695v4h6.13867c0.472,3.759 2.24919,7.11075 4.86719,9.59375l-6.49023,6.49219l2.82813,2.82617l6.91797,-6.91797c2.296,1.275 4.93228,2.00586 7.73828,2.00586h2v16h4v-16.13867c2.915,-0.366 5.58292,-1.51728 7.79492,-3.23828l8.29102,8.29102l2.82812,-2.82812l-8.29102,-8.29102c1.72,-2.212 2.87228,-4.87992 3.23828,-7.79492h10.13867v-4h-10.14062c-0.384,-3.029 -1.63294,-5.88547 -3.58594,-8.23047l8.64062,-8.64062l-2.82812,-2.82812l-11.39258,11.39258l1.38672,1.38672l-0.02344,0.02539c2.506,2.273 3.94336,5.51453 3.94336,8.89453c0,6.617 -5.383,12 -12,12c-6.617,0 -12,-5.383 -12,-12c0,-3.441 1.4805,-6.72295 4.0625,-9.00195l-0.0293,-0.03125l0.98828,-0.98828z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_las}</span>
                                    <br>
                                    <h2>{LANG.laxsd}</h2>
                                </div>
                            </a>
                        </div>
                    </div>

                    <div class="item bg_red {class_total_vilas}">
                        <div class="tabone">
                            <a href="#vilas">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M36.13867,2c-0.37866,-0.0112 -0.74653,0.19423 -0.92578,0.55273l-1.00977,2.26172c-0.255,0.509 -0.03272,1.12933 0.48828,1.36133l1.57031,0.69727l-1.14648,2.59766l-0.43359,-0.17773c-0.609,-0.247 -1.08772,-0.44103 -1.63672,-0.20703c-0.548,0.234 -0.74109,0.71742 -0.99609,1.35742l-3.65234,8.83789l9.09766,3.64063l3.64648,-8.78711c0.592,-1.484 0.33161,-2.08574 -1.15039,-2.67773l-0.76562,-0.3125l1.0625,-2.48242l1.57422,0.69922c0.489,0.217 1.06178,0.0112 1.30078,-0.4668l1.00977,-2.26172c0.255,-0.51 0.03272,-1.12933 -0.48828,-1.36133l-7.16992,-3.18555c-0.12225,-0.05425 -0.24878,-0.0822 -0.375,-0.08594zM42.7793,15.40039l-2.33008,5.62109c0.57,0.656 0.76916,1.68041 0.41016,2.56641l-1.74805,4.17383c0.131,-0.008 0.26153,-0.01953 0.39453,-0.01953c2.125,0 4.02077,0.98739 5.25977,2.52539l1.56641,-3.74023c1.672,-4.13 0.08327,-8.78895 -3.55273,-11.12695zM27.67188,21.14648l-0.56445,1.51953c-0.089,0.24 -0.08247,0.50523 0.01953,0.74023l1.08594,2.53516c0.104,0.244 0.29988,0.43716 0.54688,0.53516l3.61914,1.44531c0.119,0.048 0.24314,0.07031 0.36914,0.07031c0.134,0 0.26853,-0.02608 0.39453,-0.08008l2.53711,-1.08594c0.252,-0.107 0.44697,-0.31136 0.54297,-0.56836l0.54688,-1.47461zM8.00391,22c-0.36064,-0.0051 -0.69608,0.18438 -0.87789,0.49587c-0.18181,0.3115 -0.18181,0.69676 0,1.00825c0.18181,0.3115 0.51725,0.50097 0.87789,0.49587h1v5.79102l-5.68945,13.00586c-0.85191,1.94849 0.62129,4.20313 2.74805,4.20313h14.70703c2.18864,0 3.66278,-2.38491 2.68359,-4.3418l-6.44922,-12.89453v-5.76367h1c0.36064,0.0051 0.69608,-0.18438 0.87789,-0.49587c0.18181,-0.3115 0.18181,-0.69676 0,-1.00825c-0.18181,-0.3115 -0.51725,-0.50097 -0.87789,-0.49587zM11.00391,24h4v6.23633l1.88281,3.76367h-7.54102l1.6582,-3.79102zM22.00781,26.99023c-0.38972,0.01167 -0.75309,0.25167 -0.90234,0.63867c-0.2,0.515 0.05531,1.09492 0.57031,1.29492l12.94531,5.01953c0.05851,0.02262 0.11825,0.0222 0.17773,0.0332c-0.01942,0.17422 -0.05273,0.34413 -0.05273,0.52344c0,2.623 2.13481,4.75781 4.75781,4.75781c2.623,0 4.75781,-2.13481 4.75781,-4.75781c0,-2.623 -2.13481,-4.75781 -4.75781,-4.75781c-1.75178,0 -3.2682,0.96303 -4.09375,2.37695c-0.02354,-0.01132 -0.03968,-0.0314 -0.06445,-0.04102l-12.94727,-5.01953c-0.12875,-0.04975 -0.26072,-0.07225 -0.39063,-0.06836zM21.8418,35.07617l2.96094,5.92383h12.85156c-1.766,-0.504 -3.2377,-1.70992 -4.0957,-3.29492c-0.521,0.21 -1.25874,0.30664 -2.55273,0.30664c-1.486,0 -5.8635,-1.67028 -8.3125,-2.73828c-0.278,-0.108 -0.56356,-0.16427 -0.85156,-0.19727zM11.00391,37c0.552,0 1,0.448 1,1c0,0.552 -0.447,1 -1,1c-0.552,0 -1,-0.448 -1,-1c0,-0.552 0.448,-1 1,-1zM14.50391,39c0.828,0 1.5,0.672 1.5,1.5c0,0.828 -0.671,1.5 -1.5,1.5c-0.828,0 -1.5,-0.672 -1.5,-1.5c0,-0.828 0.672,-1.5 1.5,-1.5zM25.61328,43c0.255,1.232 0.03652,2.52291 -0.64648,3.62891c-0.081,0.13 -0.17367,0.25009 -0.26367,0.37109h21.29688c0.552,0 1,-0.448 1,-1c0,-1.657 -1.343,-3 -3,-3h-5.60352h-0.10937z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_vilas}</span>
                                    <br>
                                    <span>{LANG.vilas}</span>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg__green {class_total_vicas}">
                        <div class="tabone">
                            <a href="#vicas">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.33333,5.33333)">
                                            <path
                                                d="M11.45703,3c-1.91495,0 -3.5,1.58505 -3.5,3.5v1.66992c-0.00765,0.54096 0.27656,1.04413 0.74381,1.31684c0.46725,0.27271 1.04514,0.27271 1.51239,0c0.46725,-0.27271 0.75146,-0.77588 0.74381,-1.31684v-1.66992c0,-0.29505 0.20496,-0.5 0.5,-0.5h25.08399c0.29505,0 0.5,0.20495 0.5,0.5v13.54297c-0.00765,0.54095 0.27656,1.04412 0.74381,1.31683c0.46725,0.27271 1.04514,0.27271 1.51238,0c0.46725,-0.27271 0.75146,-0.77588 0.74381,-1.31683v-13.54297c0,-1.91495 -1.58505,-3.5 -3.5,-3.5zM15.5,10c-0.54095,-0.00765 -1.04412,0.27656 -1.31683,0.74381c-0.27271,0.46725 -0.27271,1.04514 0,1.51238c0.27271,0.46725 0.77588,0.75146 1.31683,0.74381h18c0.54095,0.00765 1.04412,-0.27656 1.31683,-0.74381c0.27271,-0.46725 0.27271,-1.04514 0,-1.51238c-0.27271,-0.46725 -0.77588,-0.75146 -1.31683,-0.74381zM9.43555,11.69141c-0.39783,0.0057 -0.77709,0.1692 -1.05437,0.45453c-0.27728,0.28533 -0.42985,0.66913 -0.42415,1.06695v27.28711c0,1.91495 1.58505,3.5 3.5,3.5h9.5957c0.54095,0.00765 1.04412,-0.27656 1.31683,-0.74381c0.27271,-0.46725 0.27271,-1.04514 0,-1.51238c-0.27271,-0.46725 -0.77588,-0.75146 -1.31683,-0.74381h-9.5957c-0.29504,0 -0.5,-0.20495 -0.5,-0.5v-27.28711c0.00581,-0.40527 -0.15263,-0.79565 -0.43923,-1.08225c-0.2866,-0.2866 -0.67698,-0.44504 -1.08225,-0.43923zM15.5,19c-0.54095,-0.00765 -1.04412,0.27656 -1.31683,0.74381c-0.27271,0.46725 -0.27271,1.04514 0,1.51238c0.27271,0.46725 0.77588,0.75146 1.31683,0.74381h15c0.54095,0.00765 1.04412,-0.27656 1.31683,-0.74381c0.27271,-0.46725 0.27271,-1.04514 0,-1.51238c-0.27271,-0.46725 -0.77588,-0.75146 -1.31683,-0.74381zM38.51953,24.97852c-0.82766,0.01293 -1.48843,0.69381 -1.47656,1.52148v14c0,0.29505 -0.20495,0.5 -0.5,0.5h-1.54297v-4.5c0.00115,-0.09825 -0.00735,-0.19638 -0.02539,-0.29297c0,-0.00195 0,-0.00391 0,-0.00586c-0.06092,-1.23468 -0.48242,-2.46729 -1.35352,-3.44727c-0.94158,-1.05927 -2.41276,-1.75391 -4.12109,-1.75391c-1.70833,0 -3.17952,0.69463 -4.12109,1.75391c-0.87479,0.98414 -1.29752,2.22295 -1.35547,3.46289c-0.01676,0.09345 -0.0246,0.18827 -0.02344,0.2832v8.73438c0,1.57944 1.54209,2.79608 3.07813,2.43164l2.61133,-0.61914l2.17383,0.57227c1.54908,0.40804 3.13672,-0.81532 3.13672,-2.41797v-1.20117h1.54297c1.91495,0 3.5,-1.58505 3.5,-3.5v-14c0.00582,-0.40562 -0.15288,-0.7963 -0.43991,-1.08296c-0.28703,-0.28666 -0.67792,-0.44486 -1.08353,-0.43852zM15.5,25c-0.54095,-0.00765 -1.04412,0.27656 -1.31683,0.74381c-0.27271,0.46725 -0.27271,1.04514 0,1.51238c0.27271,0.46725 0.77588,0.75146 1.31683,0.74381h7c0.54095,0.00765 1.04412,-0.27656 1.31683,-0.74381c0.27271,-0.46725 0.27271,-1.04514 0,-1.51238c-0.27271,-0.46725 -0.77588,-0.75146 -1.31683,-0.74381zM29.5,34c0.95833,0 1.48715,0.30537 1.87891,0.74609c0.39176,0.44073 0.62109,1.08724 0.62109,1.75391c0,0.66667 -0.22934,1.31318 -0.62109,1.75391c-0.39176,0.44073 -0.92057,0.74609 -1.87891,0.74609c-0.95833,0 -1.48715,-0.30537 -1.87891,-0.74609c-0.39176,-0.44073 -0.62109,-1.08724 -0.62109,-1.75391c0,-0.66667 0.22934,-1.31318 0.62109,-1.75391c0.39176,-0.44073 0.92057,-0.74609 1.87891,-0.74609zM27,41.44336c0.7359,0.35026 1.58157,0.55664 2.5,0.55664c0.91843,0 1.7641,-0.20638 2.5,-0.55664v3.10938l-1.91016,-0.50391c-0.23851,-0.06243 -0.48872,-0.06511 -0.72852,-0.00781l-2.36133,0.56055z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_vicas}</span>
                                    <br>
                                    <span>{LANG.vicas}</span>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_blue {class_total_vias}">
                        <div class="tabone">
                            <a href="#vias">
                                <svg version="1.1" viewBox="0 0 1600 1600" width="44" height="44"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path transform="translate(355,255)"
                                        d="m0 0h87l4 3 2 21 3 24 4 17 5 12 7 12 9 11 11 9 15 9 13 5 13 2 15 1 97 1h319l83-1 29-2 18-4 17-8 13-9 12-12 8-13 5-14 4-17 3-29 2-17 1-1h90l4 3v1146l-1 2-4 1-324 1-512 1h-52l-4-2-1-13v-1058l1-77 1-3zm691 341-4 5-9 16-10 14-6 10-12 19-11 17-15 23-16 25-10 15-11 17-13 20-11 17-13 20-16 25-13 20-15 23-10 16-7 11-7 10-11 17-7 11-12 19-6 9-9 14-10 15-8 13-5 8-5-2-15-14-9-9-8-7-15-14-12-11-8-7-14-13h-2v-2l-8-7-10-9-13-11-12-11-8-7-14-12-14-13-8-7-10-8-9-5h-8l-2 1v2h-2l-7 13-6 5-7 8-8 10-1 6 11 10 10 8 3 3h2l1 3 8 7 12 11 8 7 13 12 8 7 12 11 8 7 12 11 8 7 12 11 8 7 12 11 8 7 15 14 8 7 10 9 3 3h2v2l11 9 14 13 8 7 3 1v2h2v2l8 7 3 2h5l6-8 8-12 9-13 8-14 13-20 15-23 9-14 22-34 3-5 9-14 13-20 7-11 7-10 8-13 11-17 15-23 7-11 18-28 15-23 11-17 26-40 14-22 8-12 7-11 7-10 9-14 9-15 12-17 6-11 3-4-1-5-5-4-15-10-16-10z"
                                        fill="#FEFEFE" />
                                    <path transform="translate(256,63)"
                                        d="m0 0h145l45 1 2 2v47l-1 76-5 2-153 1-1 39v1240l113-1h888l20-1 1-2-1-84 1-1120v-71h-65l-81 1-11-1-2-2-1-5v-61l1-43 1-15 3-3h174l23 1 17 2 12 4 14 8 10 8 8 7 13 17 8 15 4 15 1 9v1341l-1 27-3 17-5 12-7 11-8 11-13 12-30 15-3 3h9v2h-1149l-5-5-20-12-14-10-10-9-10-16-7-16-3-17-2-42-1-40v-1186l1-76 2-27 4-16 8-16 9-13 3-4h2l2-4 11-10 14-9 12-5 14-3z"
                                        fill="#FDFDFD" />
                                    <path transform="translate(529)"
                                        d="m0 0h551l4 5-1 2-7-5h-5l2 5 11 9 3 6 1 16v222l-1 32-2 12-7 8-12 6-6 1-289 1h-228l-16-2-5-3-4-5-4-9-3-18-1-20v-153l1-80 3-10 9-10 4-6z"
                                        fill="#FEFEFE" />
                                    <path transform="translate(219,1598)" d="m0 0h8l1 2h-10z" fill="#fff" />
                                    <path transform="translate(1387,1594)" d="m0 0 2 1-5 1z" fill="#fff" />
                                    <path transform="translate(1372,1595)" d="m0 0 1 2-2-1z" fill="#fff" />
                                    <path transform="translate(215,1598)" d="m0 0" fill="#fff" />
                                    <path transform="translate(526)" d="m0 0" fill="#fff" />
                                </svg>

                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_vias}</span>
                                    <br>
                                    <span>{LANG.vias}</span>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_organ {class_total_vilas_med}">
                        <div class="tabone">
                            <a href="#vilas_med">
                                <svg version="1.1" viewBox="0 0 1600 1600" width="44" height="44"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path transform="translate(784,162)"
                                        d="m0 0h31l19 3 15 5 27 13 20 12 18 12 19 14 16 13 13 10 14 11 15 12 14 11 13 11 30 23 15 12 13 10 11 9 15 12 16 12 14 11 17 13 13 11 14 11 45 35 16 13 10 8 14 11 13 10 14 11 10 8 10 9 8 7 10 9 11 12 12 14 13 18 14 24 12 25 9 23 5 19 3 26 1 22 1 126v278l-1 142-1 27-2 21-3 12-9 17-15 24-9 11-11 12-14 13-10 8-15 10-23 11-12 4-11 2-31 2-149 1h-720l-31-1-24-3-16-5-20-12-13-9-16-13-15-15-11-14-9-13-8-13-9-19-4-17-2-18-1-22-1-90v-242l1-232 2-20 3-17 8-24 17-35 10-18 9-14 10-14 8-9 7-8 17-17 11-9 14-12 14-11 12-10 16-12 16-13 9-7 15-12 17-13 15-12 13-10 14-11 16-13 17-13 15-12 13-10 14-11 11-9 13-10 15-12 28-22 16-13 14-11 16-13 28-22 12-9 20-14 25-15 24-12 18-6zm16 103-16 2-12 5-12 7-13 8-17 12-15 12-14 11-16 13-14 10-12 10-25 20-28 22-12 9-16 13-10 8-14 11-11 9-16 12-17 13-16 13-13 10-12 10-16 12-27 21-12 10-9 7-13 11-10 8-13 10-11 10-10 8v2l-4 2-12 13-11 15-8 16-6 14-4 14-2 18-1 17-1 70v491l2 20 4 12 7 11 9 8 9 4 16 3 15 1 106 1h639l137-1 18-2 11-4 9-6 8-9 6-12 2-13 1-16v-566l-2-20-4-18-8-20-10-18-9-11-12-13-16-15-11-9-16-13-14-11-16-13-14-11-18-14-15-12-14-11-10-8-28-22-13-10-10-8-28-22-15-12-12-9-16-13-17-13-16-13-13-10-14-11-15-12-27-21-13-10-17-12-18-11-15-8-15-5z"
                                        fill="#fff" />
                                    <path transform="translate(793,598)"
                                        d="m0 0h10l13 4 11 6 12 11 6 7 4 10 1 8v189h139l53 1 12 2 9 6 9 9 7 11 4 10 1 6v8l-3 11-6 11-12 16-6 5-6 2-12 1-115 1h-74l1 6-1 162-2 31-4 10-10 10h-2v2l-10 8-12 3h-19l-13-3-9-7-12-11-6-9-2-12-1-23v-167h-184l-14-1-9-3-6-5-11-14-6-10-3-8-1-10 2-13 6-11 8-11 8-8 8-5 5-1h196v-39l1-155 4-11 6-8 4-5h2v-2l10-8 8-4 5-2z"
                                        fill="#fff" />
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_vilas_med}</span>
                                    <br>
                                    <span>{LANG.vilas_med}</span>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_pink {class_total_vipas}">
                        <div class="tabone">
                            <a href="#vipas">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(5.12,5.12)">
                                            <path
                                                d="M46,9v32c0,2.757 -2.243,5 -5,5h-32c-2.757,0 -5,-2.243 -5,-5v-32c0,-2.757 2.243,-5 5,-5h32c2.757,0 5,2.243 5,5zM26,13h-5c-3.314,0 -6,2.686 -6,6v5v1h1h3h1v-1v-4.5c0,-0.827 0.673,-1.5 1.5,-1.5h4.5h1v-1v-3v-1zM34,25h-3h-1v1v4.5c0,0.827 -0.673,1.5 -1.5,1.5h-4.5h-1v1v3v1h1h5c3.314,0 6,-2.686 6,-6v-5v-1z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_vipas}</span>
                                    <br>
                                    <span>{LANG.vipas}</span>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="item bg_blue {class_total_viras}">
                        <div class="tabone">
                            <a href="#viras">
                                <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44"
                                    viewBox="0,0,256,256">
                                    <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                        stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10"
                                        stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                        font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                        <g transform="scale(2,2)">
                                            <path
                                                d="M14,1c-1.7,0 -3,1.3 -3,3c0,1.7 1.3,3 3,3h74c12.7,0 23,10.3 23,23c0,12.7 -10.3,23 -23,23h-48c-16,0 -29,13 -29,29c0,16 13,29 29,29h66.80078l-10.90039,10.90039c-1.2,1.2 -1.2,3.09922 0,4.19922c0.6,0.6 1.39961,0.90039 2.09961,0.90039c0.7,0 1.49961,-0.30039 2.09961,-0.90039l16,-16c1.2,-1.2 1.2,-3.09922 0,-4.19922l-16,-16c-1.2,-1.2 -3.09922,-1.2 -4.19922,0c-1.2,1.2 -1.2,3.09922 0,4.19922l10.90039,10.90039h-66.80078c-12.7,0 -23,-10.3 -23,-23c0,-12.7 10.3,-23 23,-23h48c16,0 29,-13 29,-29c0,-16 -13,-29 -29,-29zM88,15c-8.3,0 -15,6.7 -15,15c0,8.3 6.7,15 15,15c8.3,0 15,-6.7 15,-15c0,-8.3 -6.7,-15 -15,-15zM88,21c5,0 9,4 9,9c0,5 -4,9 -9,9c-5,0 -9,-4 -9,-9c0,-5 4,-9 9,-9z">
                                            </path>
                                        </g>
                                    </g>
                                </svg>
                                <div class="info">
                                    <span class="font-span">{PROVINCETYPE.total_viras}</span>
                                    <br>
                                    <span>{LANG.viras}</span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-24 col-sm-24 col-xs-24 mt15">
                <div class="alert alert-info">{NOTE_PROVINCE}</div>
            </div>
        </div>
        <div class="col-lg-24 col-md-24 col-sm-24 col-xs-24">
            <div class="province-conent provinceChange">
                <div id="elementToChange">
                    <div class="tabcontent relative_province">
                        <div id="main__menu_bidding">
                            <div class="bg_bidding__menu"></div>
                            <span class="btn btn__list_ol"> <i class="fa fa-list-ol" aria-hidden="true"></i>
                                &nbsp;{LANG.category}</span>
                            <div id="bidding__menu">
                                <div id="fixmenu">
                                    <div class="bidding__menu-title">
                                        <span class="table-heading f-w-500">
                                            <i class="fa fa-bars hidden__bidding__menu-tab"
                                                aria-hidden="true"></i>{LANG.category}</span>
                                        <span class="hidden__nav"> <i class="fa fa-times close__menu"
                                                aria-hidden="true"></i>
                                        </span>
                                    </div>
                                    <div class="bidding__menu-main tabone">
                                        <nav class="bidding__menu-tab-navigation" id="navigation">
                                            <ul id="tableMenuContent">
                                                <li data-toggle="plan" class="{class_total_dadtpt}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#duandtpt">{LANG.duandtpt}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_plans_overall}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#plans_overall">{LANG.pagetitle_khttlcnt}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_plans}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#plan">{LANG.kehoachlcnt}</a>
                                                </li>
                                                <li data-toggle="plan">
                                                    <a class="title__tab_heading bidding__link active"
                                                        href="#tbmt">{LANG.tbmt}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_open}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#open">{LANG.listopen}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_kqlcnt}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#kqlcnt">{LANG.kqlcnt}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_mstnht}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#prequalification-notice">{LANG.title_pq_notice_contractor}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_moiquantam}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#eoi-invitation">{LANG.title_interest_contractor}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_ketquasotuyen}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#prequalification-result">{LANG.result_prequalification}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_ketquamosotuyen}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#ketquamosotuyen">{LANG.title_pq_open_contractor}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_ketquamoiquantam}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#eoiresult">{LANG.result_prequalification_title_qt}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_ketquamoquantam}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#ketquamoquantam">{LANG.title_interest_open_contractor}</a>
                                                </li>

                                                <li data-toggle="plan" class="{class_total_cbda}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#project-proposal">{LANG.project_proposal}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_khlcndt}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#planndt">{LANG.khlcndt}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_moidautu}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#investment-invitation">{LANG.pagetitle_tbmt_type2}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_moisotuyen}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#moisotuyen">{LANG.title_pq_notice_investor}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_kqst_project}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#kqst-project">{LANG.result_prequalification_project}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_kqlcndt}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#kqlcndt">{LANG.kqlcndt}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_hanghoa}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#hanghoa">{LANG.dmhh}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_nhathau}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#nhathau">{LANG.nhathau}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_stocks}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#stocks">{LANG.stocks}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_ntvp}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#ntvipham">{LANG.nha_thau_vi_pham}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_bmt}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#bmt">{LANG.benmoithau}</a>
                                                </li>

                                                <li data-toggle="plan" class="{class_total_dau_gia}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#dau_gia">{LANG.dau_gia}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_dau_gia_select}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#dau_gia_select">{LANG.dau_gia_select}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_dau_gia_bidder}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#dau_gia_bidder">{LANG.dau_gia_bidder}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_dau_gia_dgv}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#dau_gia_dgv">{LANG.dau_gia_dgv}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_dau_gia_deparment}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#dau_gia_department">{LANG.title_dau_gia_department}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_vbdt}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#legal-documents">{LANG.van_ban_phap_quy}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_project}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#qhxddt">{LANG.quyhoachxaydungvadothi}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_tochuc}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#tcxd">{LANG.tochucxaydung}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_las}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#lasxd">{LANG.laxsd}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_vilas}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#vilas">{LANG.vilas}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_vicas}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#vicas">{LANG.vicas}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_vias}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#vias">{LANG.vias}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_vilas_med}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#vilas_med">{LANG.vilas_med}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_vipas}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#vipas">{LANG.vipas}</a>
                                                </li>
                                                <li data-toggle="plan" class="{class_total_viras}">
                                                    <a class="title__tab_heading bidding__link"
                                                        href="#viras">{LANG.viras}</a>
                                                </li>
                                            </ul>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="province_bodycontent">
                        <div class="tabcontent" id="bodycontent">
                            <div class="thanh-pho-content tinh-thanh-pho">
                                <div class="tab-content mobile-content-display">
                                    <div id="tbmt" class="tab-pane fade in active">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#tbmt"><span>{LANG.tbmt}</span></a>
                                            </div>
                                            <!-- BEGIN: view -->
                                            <table class="bidding-table">
                                                <thead>
                                                    <tr>
                                                        <th class="h-ten-goithau">{LANG.goi_thau}</th>
                                                        <th>{LANG.ben_moi_thau}</th>
                                                        <th>{LANG.ngay_dang_tai}</th>
                                                        <th>{LANG.den_ngay}</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- BEGIN: loop -->
                                                    <tr>
                                                        <td class="order-header" data-column="{LANG.goi_thau}">
                                                            <div class="wrap__text">
                                                                <a title="{VIEW.goi_thau}"
                                                                    href="{VIEW.link}"><span
                                                                        class="bidding-code">{VIEW.so_tbmt}</span>
                                                                    {VIEW.goi_thau}</a>

                                                                <!-- BEGIN: admin_link -->
                                                                ({LANG.crawl_time}: {VIEW.get_time})
                                                                <!-- END: admin_link -->

                                                                <!-- BEGIN: violate_warning -->
                                                                <span class="warning-area">
                                                                    <i class="fa fa-exclamation-triangle online-rule-violate"
                                                                        aria-hidden="true"></i>
                                                                    <blockquote class="custom_border">
                                                                        {VIEW.violate_msg}</blockquote>
                                                                </span>
                                                                <!-- END: violate_warning -->

                                                            </div>
                                                        </td>
                                                        <td data-column="{LANG.ben_moi_thau}">
                                                            <div>
                                                                <!-- BEGIN: link_solicitor -->
                                                                <a title="{VIEW.ben_moi_thau}"
                                                                    href="{VIEW.link_solicitor}">
                                                                    <!-- BEGIN: solicitor_code --> <span
                                                                        class="solicitor-code">{VIEW.solicitor_code}</span>
                                                                    <!-- END: solicitor_code -->
                                                                    {VIEW.ben_moi_thau}
                                                                </a>
                                                                <!-- END: link_solicitor -->
                                                                <!-- BEGIN: no_link_solicitor -->
                                                                {VIEW.ben_moi_thau}
                                                                <!-- END: no_link_solicitor -->
                                                            </div>
                                                        </td>
                                                        <td class="txt-center"
                                                            data-column="{LANG.ngay_dang_tai}">
                                                            <div>{VIEW.ngay_dang_tai}</div>
                                                        </td>
                                                        <td class="txt-center" data-column="{LANG.den_ngay}">
                                                            <div>{VIEW.den_ngay}</div>
                                                        </td>
                                                    </tr>
                                                    <!-- END: loop -->
                                                </tbody>
                                            </table>
                                            <div class="modal fade" id="idmodals" tabindex="-1" role="dialog"
                                                aria-labelledby="myModalLabel" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <button type="button" class="close" data-dismiss="modal"
                                                                aria-hidden="true">&times;</button>
                                                            <p class="modal-title">{LANG.bidding_info}</p>
                                                        </div>
                                                        <div class="modal-body">
                                                            <em class="fa fa-spinner fa-spin">&nbsp;</em>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- END: view -->
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__detail}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="duandtpt" class="tab-pane fade in {class_total_dadtpt}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#duandtpt"><span>{LANG.duandtpt}</span></a>
                                            </div>
                                            <div id="content_duandtpt"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__devprojects}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="plan" class="tab-pane fade in {class_total_plans}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#plan"><span>{LANG.kehoachlcnt}</span></a>
                                            </div>
                                            <div id="content_plan"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__listplan}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="kqlcnt" class="tab-pane fade in {class_total_kqlcnt}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#kqlcnt"><span>{LANG.kqlcnt}</span></a>
                                            </div>
                                            <div id="content_kqlcnt"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__listresult}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="planndt" class="tab-pane fade in {class_total_khlcndt}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#planndt"><span>{LANG.khlcndt}</span></a>
                                            </div>
                                            <div id="content_planndt"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__planndt}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="kqlcndt" class="tab-pane fade in {class_total_kqlcndt}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#kqlcndt"><span>{LANG.kqlcndt}</span></a>
                                            </div>
                                            <div id="content_kqlcndt"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__kqlcndt}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="nhathau" class="tab-pane fade in {class_total_nhathau}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#nhathau"><span>{LANG.nhathau}</span></a>
                                            </div>
                                            <div id="content_nhathau"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__nhathau}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="bmt" class="tab-pane fade in {class_total_bmt}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#bmt"><span>{LANG.benmoithau}</span></a>
                                            </div>
                                            <div id="content_bmt"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__bmt}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="ntvipham" class="tab-pane fade in {class_total_ntvp}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#ntvipham"><span>{LANG.nha_thau_vi_pham}</span></a>
                                            </div>
                                            <div id="content_ntvipham"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__ntvipham}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="lasxd" class="tab-pane fade in {class_total_las}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#lasxd"><span>{LANG.laxsd}</span></a>
                                            </div>
                                            <div id="content-las-xd"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__las}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="tcxd" class="tab-pane fade in {class_total_tochuc}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#tcxd"><span>{LANG.tochucxaydung}</span></a>
                                            </div>
                                            <div id="content_tcxd"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__tcxd}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="qhxddt" class="tab-pane fade in {class_total_project}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#qhxddt"><span>{LANG.quyhoachxaydungvadothi}</span></a>
                                            </div>
                                            <div id="content_qhxddt"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__qhxddt}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="vilas" class="tab-pane fade in {class_total_vilas}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#vilas"><span>{LANG.vilas}</span></a>
                                            </div>
                                            <div id="content_vilas"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__vilas}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="vicas" class="tab-pane fade in {class_total_vicas}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#vicas"><span>{LANG.vicas}</span></a>
                                            </div>
                                            <div id="content_vicas"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__vicas}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="vias" class="tab-pane fade in {class_total_vias}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#vias"><span>{LANG.vias}</span></a>
                                            </div>
                                            <div id="content_vias"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__vias}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="vilas_med" class="tab-pane fade in {class_total_vilas_med}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#vilas_med"><span>{LANG.vilas_med}</span></a>
                                            </div>
                                            <div id="content_vilas_med"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__vilas_med}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="vipas" class="tab-pane fade in {class_total_vipas}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#vipas"><span>{LANG.vipas}</span></a>
                                            </div>
                                            <div id="content_vipas"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__vipas}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="viras" class="tab-pane fade in {class_total_viras}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#viras"><span>{LANG.viras}</span></a>
                                            </div>
                                            <div id="content_viras"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__viras}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="dau_gia" class="tab-pane fade in {class_total_dau_gia}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#dau_gia"><span>{LANG.dau_gia}</span></a>
                                            </div>
                                            <div id="content_dau_gia"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__dau_gia_viewmore}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="dau_gia_select" class="tab-pane fade in {class_total_dau_gia_select}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#dau_gia_select"><span>{LANG.dau_gia_select}</span></a>
                                            </div>
                                            <div id="content_dau_gia_select"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__dau_gia_select_viewmore}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="dau_gia_bidder" class="tab-pane fade in {class_total_dau_gia_bidder}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#dau_gia_bidder"><span>{LANG.dau_gia_bidder}</span></a>
                                            </div>
                                            <div id="content_dau_gia_bidder"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__dau_gia_bidder_viewmore}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="dau_gia_dgv" class="tab-pane fade in {class_total_dau_gia_dgv}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#dau_gia_dgv"><span>{LANG.dau_gia_dgv}</span></a>
                                            </div>
                                            <div id="content_dau_gia_dgv"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__dau_gia_dgv_viewmore}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="dau_gia_department"
                                        class="tab-pane fade in {class_total_dau_gia_deparment}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#dau_gia_department"><span>{LANG.title_dau_gia_department}</span></a>
                                            </div>
                                            <div id="content_dau_gia_department"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger"
                                                    href="{PROVINCETYPE.link__dau_gia_department_viewmore}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="stocks" class="tab-pane fade in {class_total_stocks}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#stocks"><span>{LANG.stocks}</span></a>
                                            </div>
                                            <div id="content_stocks"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__stocks}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="plans_overall" class="tab-pane fade in {class_total_plans_overall}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#plans_overall"><span>{LANG.pagetitle_khttlcnt}</span></a>
                                            </div>
                                            <div id="content_plans_overall"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__plans_overall}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="prequalification-notice" class="tab-pane fade in {class_total_mstnht}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#prequalification-notice"><span>{LANG.title_pq_notice_contractor}</span></a>
                                            </div>
                                            <div id="content_mstnht"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__mstnht}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="eoi-invitation" class="tab-pane fade in {class_total_moiquantam}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#eoi-invitation"><span>{LANG.title_interest_contractor}</span></a>
                                            </div>
                                            <div id="content_moiquantam"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__moiquantam}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="prequalification-result"
                                        class="tab-pane fade in {class_total_ketquasotuyen}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#prequalification-result"><span>{LANG.result_prequalification}</span></a>
                                            </div>
                                            <div id="content_ketquasotuyen"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__ketquasotuyen}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="eoiresult" class="tab-pane fade in {class_total_ketquamoiquantam}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#eoiresult"><span>{LANG.result_prequalification_title_qt}</span></a>
                                            </div>
                                            <div id="content_ketquamoiquantam"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger"
                                                    href="{PROVINCETYPE.link__ketquamoiquantam}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="open" class="tab-pane fade in {class_total_open}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#open"><span>{LANG.listopen}</span></a>
                                            </div>
                                            <div id="content_open"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__open}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="ketquamosotuyen"
                                        class="tab-pane fade in {class_total_ketquamosotuyen}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#ketquamosotuyen"><span>{LANG.title_pq_open_contractor}</span></a>
                                            </div>
                                            <div id="content_ketquamosotuyen"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger"
                                                    href="{PROVINCETYPE.link__ketquamosotuyen}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="ketquamoquantam"
                                        class="tab-pane fade in {class_total_ketquamoquantam}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#ketquamoquantam"><span>{LANG.title_interest_open_contractor}</span></a>
                                            </div>
                                            <div id="content_ketquamoquantam"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger"
                                                    href="{PROVINCETYPE.link__ketquamoquantam}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="hanghoa" class="tab-pane fade in {class_total_hanghoa}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#hanghoa"><span>{LANG.dmhh}</span></a>
                                            </div>
                                            <div id="content_hanghoa"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__hanghoa}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="project-proposal" class="tab-pane fade in {class_total_cbda}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#project-proposal"><span>{LANG.project_proposal}</span></a>
                                            </div>
                                            <div id="content_cbda"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__cbda}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="moisotuyen" class="tab-pane fade in {class_total_moisotuyen}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#moisotuyen"><span>{LANG.title_pq_notice_investor}</span></a>
                                            </div>
                                            <div id="content_moisotuyen"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__moisotuyen}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="kqst-project" class="tab-pane fade in {class_total_kqst_project}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#kqst-project"><span>{LANG.result_prequalification_project}</span></a>
                                            </div>
                                            <div id="content_kqst_project"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__kqst_project}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="investment-invitation" class="tab-pane fade in {class_total_moidautu}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#investment-invitation"><span>{LANG.pagetitle_tbmt_type2}</span></a>
                                            </div>
                                            <div id="content_moidautu"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__moidautu}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="legal-documents" class="tab-pane fade in {class_total_vbdt}">
                                        <div class="content_mobile">
                                            <div class="border-bidding">
                                                <a class="title__tab_heading"
                                                    href="#legal-documents"><span>{LANG.van_ban_phap_quy}</span></a>
                                            </div>
                                            <div id="content_van_ban_phap_quy"></div>
                                            <div class="display-flex">
                                                <a class="btn btn-danger" href="{PROVINCETYPE.link__vbdt}"
                                                    style="margin: auto">{LANG.see_more}</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="flagEnd"></div>
                                </div>
                                <div class="province-pc province-box-right">
                                    <div class="province-conent">
                                        <div class="tabcontent thanh-pho-content">
                                            <div class="tabone border-bidding border-bidding-news">
                                                <div class="affixP">
                                                    <ul class="nav-pills class-left clear">
                                                        <li data-toggle="plan" class="{class_total_dadtpt}">
                                                            <a class="title__tab_heading" href="#duandtpt">{LANG.duandtpt}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_plans_overall}">
                                                            <a class="title__tab_heading"
                                                                href="#plans_overall">{LANG.pagetitle_khttlcnt}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_plans}">
                                                            <a class="title__tab_heading" href="#plan">{LANG.kehoachlcnt}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="active">
                                                            <a class="title__tab_heading" href="#tbmt">{LANG.tbmt}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_open}">
                                                            <a class="title__tab_heading" href="#open">{LANG.listopen}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_kqlcnt}">
                                                            <a class="title__tab_heading" href="#kqlcnt">{LANG.kqlcnt}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_mstnht}">
                                                            <a class="title__tab_heading"
                                                                href="#prequalification-notice">{LANG.title_pq_notice_contractor}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_moiquantam}">
                                                            <a class="title__tab_heading"
                                                                href="#eoi-invitation">{LANG.title_interest_contractor}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_ketquasotuyen}">
                                                            <a class="title__tab_heading"
                                                                href="#prequalification-result">{LANG.result_prequalification}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_ketquamosotuyen}">
                                                            <a class="title__tab_heading"
                                                                href="#ketquamosotuyen">{LANG.title_pq_open_contractor}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_ketquamoiquantam}">
                                                            <a class="title__tab_heading"
                                                                href="#eoiresult">{LANG.result_prequalification_title_qt}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_ketquamoquantam}">
                                                            <a class="title__tab_heading"
                                                                href="#ketquamoquantam">{LANG.title_interest_open_contractor}</a>
                                                        </li>

                                                        <li data-toggle="plan" class="{class_total_cbda}">
                                                            <a class="title__tab_heading"
                                                                href="#project-proposal">{LANG.project_proposal}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_khlcndt}">
                                                            <a class="title__tab_heading" href="#planndt">{LANG.khlcndt}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_moidautu}">
                                                            <a class="title__tab_heading"
                                                                href="#investment-invitation">{LANG.pagetitle_tbmt_type2}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_moisotuyen}">
                                                            <a class="title__tab_heading"
                                                                href="#moisotuyen">{LANG.title_pq_notice_investor}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_kqst_project}">
                                                            <a class="title__tab_heading"
                                                                href="#kqst-project">{LANG.result_prequalification_project}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_kqlcndt}">
                                                            <a class="title__tab_heading" href="#kqlcndt">{LANG.kqlcndt}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_hanghoa}">
                                                            <a class="title__tab_heading" href="#hanghoa">{LANG.dmhh}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_nhathau}">
                                                            <a class="title__tab_heading" href="#nhathau">{LANG.nhathau}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_stocks}">
                                                            <a class="title__tab_heading" href="#stocks">{LANG.stocks}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_ntvp}">
                                                            <a class="title__tab_heading"
                                                                href="#ntvipham">{LANG.nha_thau_vi_pham}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_bmt}">
                                                            <a class="title__tab_heading" href="#bmt">{LANG.benmoithau}</a>
                                                        </li>

                                                        <li data-toggle="plan" class="{class_total_dau_gia}">
                                                            <a class="title__tab_heading" href="#dau_gia">{LANG.dau_gia}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_dau_gia_select}">
                                                            <a class="title__tab_heading"
                                                                href="#dau_gia_select">{LANG.dau_gia_select}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_dau_gia_bidder}">
                                                            <a class="title__tab_heading"
                                                                href="#dau_gia_bidder">{LANG.dau_gia_bidder}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_dau_gia_dgv}">
                                                            <a class="title__tab_heading"
                                                                href="#dau_gia_dgv">{LANG.dau_gia_dgv}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_dau_gia_deparment}">
                                                            <a class="title__tab_heading"
                                                                href="#dau_gia_department">{LANG.title_dau_gia_department}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_vbdt}">
                                                            <a class="title__tab_heading"
                                                                href="#legal-documents">{LANG.van_ban_phap_quy}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_project}">
                                                            <a class="title__tab_heading"
                                                                href="#qhxddt">{LANG.quyhoachxaydungvadothi}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_tochuc}">
                                                            <a class="title__tab_heading" href="#tcxd">{LANG.tochucxaydung}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_las}">
                                                            <a class="title__tab_heading" href="#lasxd">{LANG.laxsd}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_vilas}">
                                                            <a class="title__tab_heading" href="#vilas">{LANG.vilas}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_vicas}">
                                                            <a class="title__tab_heading" href="#vicas">{LANG.vicas}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_vias}">
                                                            <a class="title__tab_heading" href="#vias">{LANG.vias}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_vilas_med}">
                                                            <a class="title__tab_heading" href="#vilas_med">{LANG.vilas_med}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_vipas}">
                                                            <a class="title__tab_heading" href="#vipas">{LANG.vipas}</a>
                                                        </li>
                                                        <li data-toggle="plan" class="{class_total_viras}">
                                                            <a class="title__tab_heading" href="#viras">{LANG.viras}</a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        const gridColTop = document.querySelector('.grid-col-top');
        const gridColBottom = document.querySelector('.grid-col-bottom');

        function moveVisibleItems() {
            const hiddenItemsInTop = Array.from(gridColTop.children).filter(item => item.classList.contains('hidden')).length;

            if (hiddenItemsInTop > 0) {
                const visibleItemsInBottom = Array.from(gridColBottom.children).filter(item => !item.classList.contains('hidden'));

                const itemsToMove = visibleItemsInBottom.slice(0, hiddenItemsInTop);
                
                itemsToMove.forEach(item => {
                    gridColTop.appendChild(item); 
                });
            }
        }

        moveVisibleItems();
        const provinceSelector = document.getElementById("provinceSelector");
        const tooltip = document.querySelector(".tooltip");
        const tooltip_hoangsa = document.querySelector(".tooltip_hoangsa");
        const tooltip_truongsa = document.querySelector(".tooltip_truongsa");
        // Lưu trạng thái tỉnh/thành phố hiện tại được chọn
        let selectedProvince = "";

        // Sự kiện khi di chuột vào path
        function onMouseEnterProvince(event) {
            const pathElement = event.target;
            const provinceId = pathElement.getAttribute("id");
            const provincedata = pathElement.getAttribute("data-tinh");
            const provincedataid = pathElement.getAttribute("data-id");
            const provinceid = pathElement.getAttribute("data-province");
            // Kiểm tra xem path có id và không phải là tỉnh/thành phố hiện đang chọn
            if (provinceId && provinceId !== selectedProvince) {
                // Hiển thị tooltip
                const bbox = pathElement.getBBox();
                tooltip.style.display = "block";
                tooltip.style.left = bbox.x + bbox.width + "px";
                tooltip.style.top = bbox.y + bbox.height / 2 + "px";
                tooltip.textContent = provincedata.replace(/-/g, ' ');
                $(".plan_chart-" + provinceid).addClass('active');
                if (provincedataid == "thanh-pho-da-nang" && $("#quan-dao-hoang-sa").data("id") == "thanh-pho-da-nang") {
                    pathElement.style.fill  = "#d69622";
                    $("#quan-dao-hoang-sa")[0].style.fill  = "#d69622";
                    tooltip_hoangsa.style.display = "block";
                    tooltip_hoangsa.textContent = $("#quan-dao-hoang-sa").data("tinh").replace(/-/g, ' ');
                } else if (provincedataid == "tinh-khanh-hoa" && $("#quan-dao-truong-sa").data("id") == "tinh-khanh-hoa") {
                    pathElement.style.fill  = "#d69622";
                    $("#quan-dao-truong-sa")[0].style.fill  = "#d69622";
                    tooltip_truongsa.style.display = "block";
                    tooltip_truongsa.textContent = $("#quan-dao-truong-sa").data("tinh").replace(/-/g, ' ');
                }
            }
        }
        // Sự kiện khi di chuột vào path
        function onMouseEnterProvinceHoangsa(event) {
            const pathElement = event.target;
            const provinceId = pathElement.getAttribute("id");
            const provincedata = pathElement.getAttribute("data-tinh");
            const provincedataid = pathElement.getAttribute("data-id");
            const provinceid = pathElement.getAttribute("data-province");
            // Kiểm tra xem path có id và không phải là tỉnh/thành phố hiện đang chọn
            if (provinceId && provinceId !== selectedProvince) {
                // Thay đổi màu fill

                // Hiển thị tooltip
                const bbox = $("#thanh-pho-da-nang")[0].getBBox();
                tooltip_hoangsa.style.display = "block";
                tooltip_hoangsa.textContent = provincedata.replace(/-/g, ' ');
                $(".plan_chart-" + provinceid).addClass('active');
                if (provincedataid == "thanh-pho-da-nang" && $("#thanh-pho-da-nang").data("id") == "thanh-pho-da-nang") {
                    $("#thanh-pho-da-nang")[0].style.fill  = "#d69622";
                    pathElement.style.fill  = "#d69622";
                    tooltip.style.display = "block";
                    tooltip.style.left = bbox.x + bbox.width + "px";
                    tooltip.style.top = bbox.y + bbox.height / 2 + "px";
                    tooltip.textContent = $("#thanh-pho-da-nang").data("tinh").replace(/-/g, ' ');
                }
            }
            
        }
        // Sự kiện khi di chuột vào path
        function onMouseEnterProvinceTruongsa(event) {
            const pathElement = event.target;
            const provinceId = pathElement.getAttribute("id");
            const provincedata = pathElement.getAttribute("data-tinh");
            const provincedataid = pathElement.getAttribute("data-id");
            const provinceid = pathElement.getAttribute("data-province");
            // Kiểm tra xem path có id và không phải là tỉnh/thành phố hiện đang chọn
            if (provinceId && provinceId !== selectedProvince) {
                // Thay đổi màu fill

                // Hiển thị tooltip
                const bbox = $("#tinh-khanh-hoa")[0].getBBox();
                tooltip_truongsa.style.display = "block";
                tooltip_truongsa.textContent = provincedata.replace(/-/g, ' ');
                $(".plan_chart-" + provinceid).addClass('active');
                if (provincedataid == "tinh-khanh-hoa" && $("#tinh-khanh-hoa").data("id") == "tinh-khanh-hoa") {
                    $("#tinh-khanh-hoa")[0].style.fill  = "#d69622";
                    pathElement.style.fill  = "#d69622";
                    tooltip.style.display = "block";
                    tooltip.style.left = bbox.x + bbox.width + "px";
                    tooltip.style.top = bbox.y + bbox.height / 2 + "px";
                    tooltip.textContent = $("#tinh-khanh-hoa").data("tinh").replace(/-/g, ' ');
                }
            }
        }
        // Sự kiện khi di chuột ra khỏi path
        function onMouseLeaveProvince(event) {
            const pathElement = event.target;
            const provinceId = pathElement.getAttribute("id");
            const provincedata = pathElement.getAttribute("data-tinh");
            const provincedataid = pathElement.getAttribute("data-id");
            tooltip.style.display = "none";
            tooltip_hoangsa.style.display = "none";
            tooltip_truongsa.style.display = "none";
            if (provincedataid == "thanh-pho-da-nang" && $("#quan-dao-hoang-sa").data("id") == "thanh-pho-da-nang") {
                pathElement.style.fill  = "#dc2626";
                $("#quan-dao-hoang-sa")[0].style.fill  = "#0685d6";
            } else if (provincedataid == "tinh-khanh-hoa" && $("#tinh-khanh-hoa").data("id") == "tinh-khanh-hoa") {
                $("#tinh-khanh-hoa")[0].style.fill  = "#0685d6";
                pathElement.style.fill  = "#0685d6";
            }
            $(".plan_chart").removeClass('active');
        }
        function onMouseLeaveProvinceHoangsa(event) {
            const pathElement = event.target;
            const provinceId = pathElement.getAttribute("id");
            const provincedata = pathElement.getAttribute("data-tinh");
            const provincedataid = pathElement.getAttribute("data-id");
            tooltip.style.display = "none";
            tooltip_hoangsa.style.display = "none";
            if (provincedataid == "thanh-pho-da-nang" && $("#thanh-pho-da-nang").data("id") == "thanh-pho-da-nang") {
                $("#thanh-pho-da-nang")[0].style.fill  = "#dc2626";
                pathElement.style.fill  = "#0685d6";
            }
            $(".plan_chart").removeClass('active');
        }
        function onMouseLeaveProvinceTruongsa(event) {
            const pathElement = event.target;
            const provinceId = pathElement.getAttribute("id");
            const provincedata = pathElement.getAttribute("data-tinh");
            const provincedataid = pathElement.getAttribute("data-id");
            tooltip.style.display = "none";
            tooltip_truongsa.style.display = "none";
            if (provincedataid == "tinh-khanh-hoa" && $("#tinh-khanh-hoa").data("id") == "tinh-khanh-hoa") {
                $("#tinh-khanh-hoa")[0].style.fill  = "#0685d6";
                pathElement.style.fill  = "#0685d6";
            }
            $(".plan_chart").removeClass('active');
        }
        // Sự kiện khi nhấp vào path
        function onClickProvince(event) {
            const pathElement = event.target;
            const provinceId = pathElement.getAttribute("data-link");
            if (provinceId) {
                window.location.href = provinceId;
            }
        }
        
        // Lắng nghe sự kiện khi chuột vào, ra khỏi, và nhấp vào path
        const pathElements = document.querySelectorAll("path[data-link]");
        
        for (const pathElement of pathElements) {
            pathElement.addEventListener("click", onClickProvince);
            if (pathElement.id === "quan-dao-hoang-sa") {
                pathElement.addEventListener("mouseenter", onMouseEnterProvinceHoangsa);
                pathElement.addEventListener("mouseleave", onMouseLeaveProvinceHoangsa);
            } else if (pathElement.id === "quan-dao-truong-sa") {
                pathElement.addEventListener("mouseenter", onMouseEnterProvinceTruongsa);
                pathElement.addEventListener("mouseleave", onMouseLeaveProvinceTruongsa);
            } else {
                pathElement.addEventListener("mouseenter", onMouseEnterProvince);
                pathElement.addEventListener("mouseleave", onMouseLeaveProvince);
            }
        }
    });
</script>
 <!-- BEGIN: js_plan -->
<script>
var width_pie = {STATISTICS_PROVINCE.width_statistics};
if (window.matchMedia("(max-width: 991px)").matches) {
    var width_pie = 380;
}
var options = {
    labels: {STATISTICS_PROVINCE.labels},
    series: {STATISTICS_PROVINCE.values},
    colors: {STATISTICS_PROVINCE.colors},
    chart: {
        type: 'pie',
        width: width_pie
    },
    stroke: {
        width: 1,
        colors: undefined
    },
    dataLabels: {
        enabled: false
    },
    title: {
        text: '{STATISTICS_PROVINCE.title}'
    },
    yaxis: {
        labels: {
            formatter: function (value) {
                return value.toLocaleString('{LOCALE}');
            }
        }
    },
    legend: {
        position: 'bottom'
    }
};
var chart = new ApexCharts(document.querySelector("#plan_pie-{STATISTICS_PROVINCE.id}"), options);
chart.render();
</script>
<!-- END: js_plan -->
<script>
    $(document).ready(function () {
        $(window).on("scroll", function() {
            var st = $(this).scrollTop();
            var isInView = false;  // Biến để kiểm tra nếu người dùng đang ở trong vùng nào đó

            $('.province-conent').each(function() {
                var remoTop = $('.province-conent').offset().top;
                var remoBottom = remoTop + $(this).outerHeight();

                // Kiểm tra nếu vị trí cuộn nằm trong phạm vi của phần tử này
                if (st >= remoTop && st <= remoBottom) {
                    isInView = true;  // Đánh dấu rằng người dùng đang ở trong vùng
                    return false;      // Thoát vòng lặp sớm, không cần tiếp tục kiểm tra
                }
            });

            // Thêm class nếu người dùng trong vùng và xóa class nếu ra ngoài vùng
            if (isInView) {
                $('#elementToChange').addClass('scrolling-class');
            } else {
                $('#elementToChange').removeClass('scrolling-class');
            }
        });
        function activateTab(targetId) {
            // Kích hoạt tab tương ứng trong navigation pills
            $(".province-conent .tabcontent .nav-pills li").removeClass("active");
            $(".province-conent .tabcontent ul.nav-pills li:has(a[href='" + targetId + "'])").addClass("active");

            // Kích hoạt tab tương ứng trong table menu content
            $("#tableMenuContent li a").removeClass("active");
            $("#tableMenuContent li a[href='" + targetId + "']").addClass("active");
            // Kích hoạt tab content pane tương ứng
            $(".province-conent .tabcontent .tab-pane").removeClass("active");
            $(targetId).addClass("active");
            const translateYValuenew = calculateTranslateYValue(targetId);
            $('html, body').animate({
                scrollTop: $(targetId).offset().top - 100
            }, 1000).promise().done(function() {
                $('.sidebar-province').css({
                    "transform": 'translateY(' + translateYValuenew + 'px)'
                });
            }); 
        }
        const ulElement = document.querySelector('.affixP .nav-pills');
        function calculateTranslateYValue(targetId) {
            let cumulativeHeight = 0;
            let activeLiElement = null;
            for (let li of ulElement.children) {
                const link = li.querySelector('a');
                if (link && link.getAttribute('href') === targetId) {
                    activeLiElement = li;
                    break;
                }
                cumulativeHeight += li.offsetHeight;
            }

            // Kiểm tra nếu tìm thấy phần tử li phù hợp
            if (activeLiElement) {
                return -cumulativeHeight; // Giá trị translateYValuenew
            } else {
                console.warn("Không tìm thấy tab với ID:", targetId);
                return 0; // Trả về 0 nếu không tìm thấy
            }
        }
        // Xử lý khi click vào liên kết tab
        $(".province-conent div.tabone a").click(function() {
            // Lấy giá trị thuộc tính href của liên kết được click
            var targetId = $(this).attr("href");

            // Kích hoạt tab
            if ($(targetId).length) {
                activateTab(targetId);
            } else {
                // Use default ID if targetId does not exist
                activateTab("#tbmt");
            }
            
            // Ngăn chặn hành vi mặc định của liên kết
            if (!window.location.hash) {
                return false;
            } else {
                return true;
            }
        });

        // Xử lý khi tải trang có hash
        if (window.location.hash) {
            var targetId = window.location.hash;
            var targetTab = $("a[href='" + targetId + "']").parent();
            if ($(targetId).length) {
                activateTab(targetId);
            } else {
                // Use default ID if targetId does not exist
                history.replaceState(null, null, window.location.pathname);
                activateTab("#tbmt");
            }
            if (targetTab.hasClass("hidden")) {
                history.replaceState(null, null, window.location.pathname);
                activateTab("#tbmt");
            }
        }
        // Kích hoạt select2 cho select element
        $('#provinceSelector').select2();
        $('#provinceSelector').each(function() {
            var pathselected = $(this).children('option:selected').val();
            $(".vietnammap path[data-link='" + pathselected + "']").addClass('active');
        });
        // Điều hướng khi chọn một option
        $('#provinceSelector').on('change', function () {
            var selectedValue = $(this).val();
            if (selectedValue !== '{LANG.linktinhthanh}') {
                // Điều hướng đến trang tương ứng
                window.location.href = selectedValue;
            } else if (selectedValue === '{LANG.linktinhthanh}') {
                window.location.href = '/{LANG.linktinhthanh}/';
            } else {
                //Các trường hợp còn lại
            }
        });
    });
</script>
<script>
function fetchDataAndUpdateContent(url, targetElement, selector) {
    // Hiển thị trạng thái đang tải trong phần tử mục tiêu
    $(targetElement).html('<div class="loading">Đang tải...</div>');

    // Sử dụng API fetch để lấy dữ liệu từ URL
    fetch(url)
        .then(function(response) {
            // Kiểm tra nếu phản hồi thành công
            if (!response.ok) {
                throw new Error('Phản hồi mạng không thành công: ' + response.statusText);
            }
            return response.text(); // Chuyển phản hồi sang dạng văn bản
        })
        .then(function(data) {
            // Tạo một phần tử DOM tạm để phân tích dữ liệu phản hồi
            var tempDiv = document.createElement('div');
            tempDiv.innerHTML = data;

            // Sử dụng jQuery để tìm selector trong phần tử tạm
            var element = $(tempDiv).find(selector);
            var height_right_wrap = $(".right_wrap").outerHeight();
            if (element.length > 0) {
                // Cập nhật nội dung phần tử mục tiêu với nội dung đã tìm thấy
                $(targetElement).html(element[0].outerHTML);
                let contentHeight = $(targetElement).height();
                
                // Kiểm tra xem có tồn tại phần tử .main_center trước khi thêm CSS
                if ($('.main_center').length > 0 && height_right_wrap > contentHeight + 200) {
                    $('.main_center').css({
                        "min-height": height_right_wrap - (contentHeight + 200)
                    });
                }
                
                if (url.includes('ketqua/luachon-nhathau') || url.includes('ketqua/luachon-nhadautu')) {
                    initKQLCNT();
                }
                // Bind lại eventHandler khi load ajax KHLCNT
                if (url.includes('kehoach/luachon-nhathau')) {
                    // Select kiểu này để tránh những icon cảnh báo phía TBMT bị gán event thêm lần nữa
                    $('#content_plan .online-rule-violate').click(function () {
                        handleClickViolateIcon(this);
                    })
                }
                
            } else {
                console.log('Không tìm thấy nội dung cho selector: ' + selector + ' trong URL: ' + url);
                $(targetElement).html('<div class="error">Không tìm thấy nội dung.</div>');
                let contentHeight = $(targetElement).height();

                // Kiểm tra tồn tại của .main_center trước khi thêm CSS
                if ($('.main_center').length > 0 && height_right_wrap > contentHeight + 200) {
                    $('.main_center').css({
                        "min-height": height_right_wrap - (contentHeight + 200)
                    });
                }
            }
        })
        .catch(function(error) {
            // Xử lý lỗi nếu có xảy ra trong quá trình fetch
            console.error('Có lỗi khi tải dữ liệu:', error);
            $(targetElement).html('<div class="error">Lỗi khi tải nội dung.</div>');
        });
}

function activateTabAndFetchData(targetId, url, targetElement, selector) {
    fetchDataAndUpdateContent(url, targetElement, selector);
}

$(document).ready(function() {
    const ulElement = document.querySelector('.affixP .nav-pills');
    function calculateActiveHeight() {
        const activeLiElement = ulElement.querySelector('li.active');
        const liSpacing = 38; 
        let activeHeight = 0;
        let translateYValue = 0;
        let lastScrollTop = 0;

        for (let li of ulElement.children) {
            if (li === activeLiElement) {
                break;
            }
            activeHeight += li.offsetHeight;
        }

        $(window).scroll(function () {
            const scrollTop = $(window).scrollTop();
            const provinceBodyTop = $('#elementToChange').offset().top;
            if (scrollTop > lastScrollTop) {
                if (scrollTop >= provinceBodyTop - 300 && scrollTop < provinceBodyTop - 50) {
                    translateYValue = -activeHeight;
                } else if (scrollTop >= provinceBodyTop) {
                    if (translateYValue < 0) {
                        translateYValue = Math.min(0, translateYValue + liSpacing);
                    } 
                } 
            } else if (scrollTop < lastScrollTop) {
                if (translateYValue < 0) {
                    translateYValue = Math.min(0, translateYValue + liSpacing);
                } else {
                    translateYValue = 0;
                }
            }
            $('.sidebar-province').css({
                "transform": 'translateY(' + translateYValue + 'px)'
            });

            lastScrollTop = scrollTop;
        });
    }
    calculateActiveHeight(); // Tính chiều cao mỗi lần click
    function handleTabClick(targetId, url, targetElement, selector) {
        setTimeout(() => {
            calculateActiveHeight(); // Tính chiều cao mỗi lần click
            activateTabAndFetchData(targetId, url, targetElement, selector);
        }, 0);
    }

    $("a[href='#duandtpt']").on("click", function() {
        handleTabClick('#duandtpt', "{PROVINCETYPE.link_devprojects}", "#content_duandtpt", ".bidding-table");
    });
    $("a[href='#plan']").on("click", function() {
        
        handleTabClick("#plan", "{PROVINCETYPE.link_listplan}", "#content_plan", ".bidding-table");
    });
    $("a[href='#kqlcnt']").on("click", function() {
        handleTabClick("#kqlcnt", "{PROVINCETYPE.link_listresult}", "#content_kqlcnt", ".bidding-table");
    });
    $("a[href='#planndt']").on("click", function() {
        handleTabClick("#planndt", "{PROVINCETYPE.link_planndt}", "#content_planndt", ".bidding-table");
    });
    $("a[href='#kqlcndt']").on("click", function() {
        handleTabClick("#kqlcndt", "{PROVINCETYPE.link_kqlcndt}", "#content_kqlcndt", ".bidding-table");
    });
    $("a[href='#nhathau']").on("click", function() {
        handleTabClick("#nhathau", "{PROVINCETYPE.link_nhathau}", "#content_nhathau", ".panel-listbuss .list-group");
    });
    $("a[href='#ntvipham']").on("click", function() {
        handleTabClick("#ntvipham", "{PROVINCETYPE.link_ntvipham}", "#content_ntvipham", ".bidding_vp");
    });
    $("a[href='#bmt']").on("click", function() {
        handleTabClick("#bmt", "{PROVINCETYPE.link_bmt}", "#content_bmt", ".bidding-table");
    });
    $("a[href='#lasxd']").on("click", function() {
        handleTabClick("#lasxd", "{PROVINCETYPE.link_las}", "#content-las-xd", ".bidding-list-detail");
    });
    $("a[href='#tcxd']").on("click", function() {
        handleTabClick("#tcxd", "{PROVINCETYPE.link_tcxd}", "#content_tcxd", ".bidding-table");
    });
    $("a[href='#qhxddt']").on("click", function() {
        handleTabClick("#qhxddt", "{PROVINCETYPE.link_qhxddt}", "#content_qhxddt", ".listQuyhoach .list");
    });
    $("a[href='#vicas']").on("click", function() {
        handleTabClick('#vicas', "{PROVINCETYPE.link_vicas}", "#content_vicas", ".table-striped");
    });
    $("a[href='#vilas']").on("click", function() {
        handleTabClick('#vilas', "{PROVINCETYPE.link_vilas}", "#content_vilas", ".table-striped");
    });
    $("a[href='#vias']").on("click", function() {
        handleTabClick('#vias', "{PROVINCETYPE.link_vias}", "#content_vias", ".table-striped");
    });
    $("a[href='#vilas_med']").on("click", function() {
        handleTabClick('#vilas_med', "{PROVINCETYPE.link_vilas_med}", "#content_vilas_med", ".table-striped");
    });
    $("a[href='#vipas']").on("click", function() {
        handleTabClick('#vipas', "{PROVINCETYPE.link_vipas}", "#content_vipas", ".table-striped");
    });
    $("a[href='#viras']").on("click", function() {
        handleTabClick('#viras', "{PROVINCETYPE.link_viras}", "#content_viras", ".table-striped");
    });
    $("a[href='#dau_gia']").on("click", function() {
        handleTabClick('#dau_gia', "{PROVINCETYPE.link_dau_gia}", "#content_dau_gia", ".bidding-table");
    });
    $("a[href='#dau_gia_select']").on("click", function() {
        handleTabClick('#dau_gia_select', "{PROVINCETYPE.link_dau_gia_select}", "#content_dau_gia_select", ".bidding-table");
    });
    $("a[href='#dau_gia_bidder']").on("click", function() {
        handleTabClick('#dau_gia_bidder', "{PROVINCETYPE.link_dau_gia_bidder}", "#content_dau_gia_bidder", ".bidding-table");
    });
    $("a[href='#dau_gia_dgv']").on("click", function() {
        handleTabClick('#dau_gia_dgv', "{PROVINCETYPE.link_dau_gia_dgv}", "#content_dau_gia_dgv", ".bidding-table");
    });
    $("a[href='#dau_gia_department']").on("click", function() {
        handleTabClick('#dau_gia_department', "{PROVINCETYPE.link_dau_gia_department}", "#content_dau_gia_department", ".bidding-table");
    });
    $("a[href='#stocks']").on("click", function() {
        handleTabClick('#stocks', "{PROVINCETYPE.link_stocks}", "#content_stocks", ".bidding-list");
    });
    $("a[href='#plans_overall']").on("click", function() {
        handleTabClick('#plans_overall', "{PROVINCETYPE.link_plans_overall}", "#content_plans_overall", ".bidding-table");
    });
    $("a[href='#prequalification-notice']").on("click", function() {
        handleTabClick('#prequalification-notice', "{PROVINCETYPE.link_mstnht}", "#content_mstnht", ".bidding-table");
    });
    $("a[href='#eoi-invitation']").on("click", function() {
        handleTabClick('#eoi-invitation', "{PROVINCETYPE.link_moiquantam}", "#content_moiquantam", ".bidding-table");
    });
    $("a[href='#prequalification-result']").on("click", function() {
        handleTabClick('#prequalification-result', "{PROVINCETYPE.link_ketquasotuyen}", "#content_ketquasotuyen", ".bidding-table");
    });
    $("a[href='#eoiresult']").on("click", function() {
        handleTabClick('#eoiresult', "{PROVINCETYPE.link_ketquamoiquantam}", "#content_ketquamoiquantam", ".bidding-table");
    });
    $("a[href='#open']").on("click", function() {
        handleTabClick('#open', "{PROVINCETYPE.link_open}", "#content_open", ".bidding-table");
    });
    $("a[href='#ketquamosotuyen']").on("click", function() {
        handleTabClick('#ketquamosotuyen', "{PROVINCETYPE.link_ketquamosotuyen}", "#content_ketquamosotuyen", ".bidding-table");
    });
    $("a[href='#ketquamoquantam']").on("click", function() {
        handleTabClick('#ketquamoquantam', "{PROVINCETYPE.link_ketquamoquantam}", "#content_ketquamoquantam", ".bidding-table");
    });
    $("a[href='#hanghoa']").on("click", function() {
        handleTabClick('#hanghoa', "{PROVINCETYPE.link_hanghoa}", "#content_hanghoa", ".bidding-table");
    });
    $("a[href='#project-proposal']").on("click", function() {
        handleTabClick('#project-proposal', "{PROVINCETYPE.link_cbda}", "#content_cbda", ".bidding-table");
    });
    $("a[href='#moisotuyen']").on("click", function() {
        handleTabClick('#moisotuyen', "{PROVINCETYPE.link_moisotuyen}", "#content_moisotuyen", ".bidding-table");
    });
    $("a[href='#kqst-project']").on("click", function() {
        handleTabClick('#kqst-project', "{PROVINCETYPE.link_kqst_project}", "#content_kqst_project", ".bidding-table");
    });
    $("a[href='#legal-documents']").on("click", function() {
        handleTabClick('#legal-documents', "{PROVINCETYPE.link_vbdt}", "#content_van_ban_phap_quy", ".flex-table-laws");
    });
    $("a[href='#investment-invitation']").on("click", function() {
        handleTabClick('#investment-invitation', "{PROVINCETYPE.link_moidautu}", "#content_moidautu", ".bidding-table");
    });
    $("a[href='#tbmt']").on("click", function() {
        handleTabClick('#tbmt', "{PROVINCETYPE.link_detail}", "#content_tbmt", ".bidding-table");
    });
    // Xử lý khi tải trang có hash
    if (window.location.hash) {
        var targetId = window.location.hash;
        var validHash = false;  // Biến để kiểm tra xem hash có hợp lệ không 
        switch (targetId) {
            case '#plan':
                handleTabClick(targetId, "{PROVINCETYPE.link_listplan}", "#content_plan", ".bidding-table");
                validHash = true;
                break;
            case '#tbmt':
                handleTabClick(targetId, "{PROVINCETYPE.link_detail}", "#content_tbmt", ".bidding-table");
                validHash = true;
                break;
            case '#kqlcnt':
                handleTabClick(targetId, "{PROVINCETYPE.link_listresult}", "#content_kqlcnt", ".bidding-table");
                validHash = true;
                break;
            case '#planndt':
                handleTabClick(targetId, "{PROVINCETYPE.link_planndt}", "#content_planndt", ".bidding-table");
                validHash = true;
                break;
            case '#kqlcndt':
                handleTabClick(targetId, "{PROVINCETYPE.link_kqlcndt}", "#content_kqlcndt", ".bidding-table");
                validHash = true;
                break;
            case '#nhathau':
                handleTabClick(targetId, "{PROVINCETYPE.link_nhathau}", "#content_nhathau", ".panel-listbuss .list-group");
                validHash = true;
                break;
            case '#ntvipham':
                handleTabClick(targetId, "{PROVINCETYPE.link_ntvipham}", "#content_ntvipham", ".bidding_vp");
                validHash = true;
                break;
            case '#bmt':
                handleTabClick(targetId, "{PROVINCETYPE.link_bmt}", "#content_bmt", ".bidding-table");
                validHash = true;
                break;
            case '#lasxd':
                handleTabClick(targetId, "{PROVINCETYPE.link_las}", "#content-las-xd", ".bidding-list-detail");
                validHash = true;
                break;
            case '#tcxd':
                handleTabClick(targetId, "{PROVINCETYPE.link_tcxd}", "#content_tcxd", ".bidding-table");
                validHash = true;
                break;
            case '#qhxddt':
                handleTabClick(targetId, "{PROVINCETYPE.link_qhxddt}", "#content_qhxddt", ".listQuyhoach .list");
                validHash = true;
                break;
            case '#vilas':
                handleTabClick(targetId, "{PROVINCETYPE.link_vilas}", "#content_vilas", ".table-striped");
                validHash = true;
                break;
            case '#vicas':
                handleTabClick(targetId, "{PROVINCETYPE.link_vicas}", "#content_vicas", ".table-striped");
                validHash = true;
                break;
            case '#vias':
                handleTabClick(targetId, "{PROVINCETYPE.link_vias}", "#content_vias", ".table-striped");
                validHash = true;
                break;
            case '#vilas_med':
                handleTabClick(targetId, "{PROVINCETYPE.link_vilas_med}", "#content_vilas_med", ".table-striped");
                validHash = true;
                break;
            case '#vipas':
                handleTabClick(targetId, "{PROVINCETYPE.link_vipas}", "#content_vipas", ".table-striped");
                validHash = true;
                break;
            case '#viras':
                handleTabClick(targetId, "{PROVINCETYPE.link_viras}", "#content_viras", ".table-striped");
                validHash = true;
                break;
            case '#dau_gia':
                handleTabClick(targetId, "{PROVINCETYPE.link_dau_gia}", "#content_dau_gia", ".bidding-table");
                validHash = true;
                break;
            case '#dau_gia_select':
                handleTabClick(targetId, "{PROVINCETYPE.link_dau_gia_select}", "#content_dau_gia_select", ".bidding-table");
                validHash = true;
                break;
            case '#dau_gia_bidder':
                handleTabClick(targetId, "{PROVINCETYPE.link_dau_gia_bidder}", "#content_dau_gia_bidder", ".bidding-table");
                validHash = true;
                break;
            case '#dau_gia_dgv':
                handleTabClick(targetId, "{PROVINCETYPE.link_dau_gia_dgv}", "#content_dau_gia_dgv", ".bidding-table");
                validHash = true;
                break;
            case '#dau_gia_department':
                handleTabClick(targetId, "{PROVINCETYPE.link_dau_gia_department}", "#content_dau_gia_department", ".bidding-table");
                validHash = true;
                break;
            case '#stocks':
                handleTabClick(targetId, "{PROVINCETYPE.link_stocks}", "#content_stocks", ".bidding-list");
                validHash = true;
                break;
            case '#plans_overall':
                handleTabClick(targetId, "{PROVINCETYPE.link_plans_overall}", "#content_plans_overall", ".bidding-table");
                validHash = true;
                break;
            case '#prequalification-notice':
                handleTabClick(targetId, "{PROVINCETYPE.link_mstnht}", "#content_mstnht", ".bidding-table");
                validHash = true;
                break;
            case '#eoi-invitation':
                handleTabClick(targetId, "{PROVINCETYPE.link_moiquantam}", "#content_moiquantam", ".bidding-table");
                validHash = true;
                break;
            case '#prequalification-result':
                handleTabClick(targetId, "{PROVINCETYPE.link_ketquasotuyen}", "#content_ketquasotuyen", ".bidding-table");
                validHash = true;
                break;
            case '#eoiresult':
                handleTabClick(targetId, "{PROVINCETYPE.link_ketquamoiquantam}", "#content_ketquamoiquantam", ".bidding-table");
                validHash = true;
                break;
            case '#open':
                handleTabClick(targetId, "{PROVINCETYPE.link_open}", "#content_open", ".bidding-table");
                validHash = true;
                break;  
            case '#ketquamosotuyen':
                handleTabClick(targetId, "{PROVINCETYPE.link_ketquamosotuyen}", "#content_ketquamosotuyen", ".bidding-table");
                validHash = true;
                break;   
            case '#ketquamoquantam':
                handleTabClick(targetId, "{PROVINCETYPE.link_ketquamoquantam}", "#content_ketquamoquantam", ".bidding-table");
                validHash = true;
                break; 
            case '#hanghoa':
                handleTabClick(targetId, "{PROVINCETYPE.link_hanghoa}", "#content_hanghoa", ".bidding-table");
                validHash = true;
                break;   
            case '#project-proposal':
                handleTabClick(targetId, "{PROVINCETYPE.link_cbda}", "#content_cbda", ".bidding-table");  
                validHash = true;
                break; 
            case '#moisotuyen':
                handleTabClick(targetId, "{PROVINCETYPE.link_moisotuyen}", "#content_moisotuyen", ".bidding-table"); 
                validHash = true;
                break; 
            case '#kqst-project':
                handleTabClick(targetId, "{PROVINCETYPE.link_kqst_project}", "#content_kqst_project", ".bidding-table");
                validHash = true;
                break;
            case '#legal-documents':
                handleTabClick(targetId, "{PROVINCETYPE.link_vbdt}", "#content_van_ban_phap_quy", ".flex-table-laws");
                validHash = true;
                break;
            case '#investment-invitation':
                handleTabClick(targetId, "{PROVINCETYPE.link_moidautu}", "#content_moidautu", ".bidding-table");
                validHash = true;
                break;
            case '#duandtpt':
                handleTabClick(targetId, "{PROVINCETYPE.link_devprojects}", "#content_duandtpt", ".bidding-table");
                validHash = true;
                break;

            // Default trường hợp không có ID hợp lệ
            default:
                console.log('Hash không hợp lệ: ' + targetId + '. Đang tải nội dung mặc định.');
                break;
        }
        // Nếu không có hash hợp lệ (validHash = false), tải nội dung của tab mặc định
        if (!validHash) {
            handleTabClick("#tbmt", "{PROVINCETYPE.link_detail}", "#content_tbmt", ".bidding-table");
        }
    } 
});

$(document).ready(function() {
    // When a link inside the menu is clicked
    $('#bidding__menu a').on('click', function() {
        $('#bidding__menu').removeClass('show');
        $('#main__menu_bidding').removeClass('show');
    });
    $(".btn__list_ol").click(function(event) {
        $("#main__menu_bidding").toggleClass("show");
    });

    $(".close__menu").click(function(event) {
        $("#main__menu_bidding").removeClass('show');
    });
    $('.bg_bidding__menu').on('click', function(event) {
        $('#bidding__menu').removeClass('show');
        $('#main__menu_bidding').removeClass('show');
    });
});
</script>
<script>
$(document).ready(function() {
    if (window.matchMedia("(min-width: 991px)").matches) {
        // Kiểm tra nếu phần tử .province-box-right tồn tại
        if ($('.province-box-right').length) {
            $('.province-box-right').appendTo('.right_center');
        }

        var province_top = $(".province-top").offset().top;
        var province_bodycontent_offset = $("#elementToChange").offset().top;
        var province_box_right_offset = $(".right_wrap").offset().top;
        var col_right = $(".col-right").offset().top;
        var col_right_height = $(".col-right").outerHeight();
        var height_tinhthanh = $(".right_wrap").outerHeight();
        var height_chung = province_box_right_offset - ((province_bodycontent_offset + 14) - province_top) - province_top;
        const lastElementHeight = $('.sidebar-widgets').outerHeight()/2;
        // Điều kiện ban đầu để đặt lại vị trí của .province-box-right
        if (province_bodycontent_offset > province_box_right_offset) {
            $('.right_wrap').css({
                "position": "absolute",
                "top": (province_bodycontent_offset + 14) - province_top
            });
            $('.op-tinh-thanh #province_bodycontent').css({
                "min-height": (height_tinhthanh - 16) 
            });
            $('.op-tinh-thanh .tab-content .tab-pane').css({
                "min-height": (height_tinhthanh - 16)
            });
        } else {
            $('.right_wrap').css({
                "position": "absolute",
                "top": ((province_bodycontent_offset + 14) - province_top) +  height_chung
            });
            $('.op-tinh-thanh .tab-content .tab-pane').css({
                "min-height": (height_tinhthanh - 16)
            });
            $('.op-tinh-thanh #province_bodycontent').css({
                "min-height": (height_tinhthanh - 16) + height_chung
            });
        }

        // Lấy tab đầu tiên có class 'active' và tính chiều cao nội dung của nó
        let initialActiveTab = $('#elementToChange .tab-pane.active').attr('id');
        let contentHeightactive = $('#' + initialActiveTab).height();

        // Kiểm tra nếu .main_center tồn tại trước khi thay đổi CSS
        if ($('.main_center').length > 0 && height_tinhthanh > contentHeightactive + 300) {
            $('.main_center').css({
                "min-height": height_tinhthanh - (contentHeightactive + 300)
            });
        }

        if (province_bodycontent_offset < province_box_right_offset) {
            var ticking = false;

            // Hàm xử lý cuộn
            $(window).scroll(function() {
                var scrollTop = $(window).scrollTop();

                if (!ticking) {
                    ticking = true;
                    window.requestAnimationFrame(function() {
                        updatePosition(scrollTop);
                        ticking = false; // Đặt lại ticking sau khi cuộn xong
                    });
                }
            });

            // Đảm bảo vị trí sau khi tải lại trang
            var scrollTop = $(window).scrollTop();
            updatePosition(scrollTop); // Cập nhật vị trí ngay khi trang tải
        }

        function updatePosition(scrollTop) {
            
            if (scrollTop >= province_bodycontent_offset - 300) {
                // Giảm dần giá trị top khi cuộn xuống
                var newTop = Math.max((province_bodycontent_offset + 16) - province_top, 0); // Đảm bảo top không nhỏ hơn 0
                $('.right_wrap').css({
                    "position" : "absolute",
                    "top": newTop + "px"
                });
                $('.op-tinh-thanh #province_bodycontent').css({
                    "min-height": (height_tinhthanh - 16)
                });
                $('.sidebar-widgets-wrap').addClass('active');
                $('.sidebar-widgets').css({
                    "transform": 'translateY(-' + lastElementHeight + 'px)'
                });
            
            } else {
                // Trả lại phần tử về vị trí ban đầu từ dưới
                $('.right_wrap').css({
                    "position": "absolute",
                    "top": ((province_bodycontent_offset + 14) - province_top) +  height_chung
                });
                $('.op-tinh-thanh #province_bodycontent').css({
                    "min-height": (height_tinhthanh - 16) + height_chung
                });
                $('.sidebar-widgets-wrap').removeClass('active');
                $('.sidebar-widgets').css({
                    "transform": 'translateY(0px)'
                });
            }
        }
    }
});
</script>
<!-- END: province -->
<!-- END: main -->