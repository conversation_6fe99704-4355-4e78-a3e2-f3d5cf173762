<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_SYSTEM')) {
    die('Stop!!!');
}
define('NV_IS_MOD_CERTIFICATE', true);

$other_lang = NV_LANG_DATA == 'en' ? 'vi' : 'en';
$other_lang_prefix = $db_config['prefix'] . '_' . $other_lang;
$other_lang_module_info = nv_site_mods($other_lang)[$module_name];
$sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province ORDER BY title ASC";
$province_list = $nv_Cache->db($sql, 'id', 'location_bidding');
$province_list[0] = array(
    'id' => 0,
    'title' => $nv_Lang->getModule('no_title'),
    'alias' => 'Chua-phan-loai'
);

if ($op == 'main') {
    if (isset($array_op[0]) and preg_match('/^([a-zA-Z0-9\-]+)\-([0-9]+)$/i', $array_op[0], $m)) {
        $id = intval($m[2]);
        $notice_page = true;
        $op = 'training';
    }
} elseif (isset($array_op[0]) && $array_op[0] == $module_info['alias']['student-list'] && isset($array_op[1]) && $array_op[1] == $nv_Lang->getModule('province_link') && isset($array_op[2]))
{
    $op = $module_info['alias']['student-list'];
}
function showDateErrorAndExit($msg)
{
    global $nv_Lang;
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">'
        . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $msg . $btn, 'danger');
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
    exit();
}