const previewForm = $('#preview-form');
const filterForm = $('#filter-form');

// Map trường vip_use với trường type_info ở form search (lo<PERSON><PERSON> thầu)
const filterMapNT = {
    1: 1,
    11: 1,
    2: 2,
    21: 2,
    5: 1,
    7: 3,
    8: 17
}

// Map trường vip_use với trường type_info2 ở form search (loại N<PERSON>à đầu tư)
const filterMapNDT = {
    1: 1,
    2: 2,
    3: 3,
    4: 4,
    5: 5,
    6: 6,
}

// Map trường vip_use3 với trường type_info3 ở form search (lo<PERSON>i <PERSON> gi<PERSON>)
const filterMapDG = {
    1: 1,
    2: 2,
}

const BASE = {
    'q': {
        type: 'TEXT',
        name: 'key'
    },
    
    'q2': {
        type: 'TEXT',
        name: 'key2'
    },
    
    'par_search': {
            type: 'CHECKBOX_ONE',
            name: 'par_search'
        },
    
    'search_one_key': {
            type: 'CHECKBOX_ONE',
            name: 'search_one_key'
        },
        
    'without_key': {
            type: 'TEXT',
            name: 'without_key'
        },
    
}

const data = {
    'KHLCNT': {
        // previewField: filterField
        ... BASE,
        'type_search': {
            type: 'SELECT',
            name: 'search_info',
        },
        'search_type_content': {
            type: 'CHECKBOX_ONE',
            name: 'search_type_content'
        },
        'searchkind': {
            type: 'RADIO',
            name: 'searchkind'
        },
        'idregion_plan': {
            type: 'SELECT',
            name: 'idregion'
        },
        'idprovince_plan[]': {
            type: 'SELECT_MANY', // select2
            name: 'idprovince[]'
        },
        'invest_from': {
            type: 'TEXT',
            name: 'invest_from'
        },
        'invest_to': {
            type: 'TEXT',
            name: 'invest_to'
        },
        'price_plan_from': {
            type: 'TEXT',
            name: 'price_plan_from'
        },
        'price_plan_to': {
            type: 'TEXT',
            name: 'price_plan_to'
        },
    },
 
    'TBMT': {
        ... BASE,
        'type_search': {
            type: 'SELECT',
            name: 'search_info',
        },
        'search_type_content': {
            type: 'CHECKBOX_ONE',
            name: 'search_type_content'
        },
        'searchkind': {
            type: 'RADIO',
            name: 'searchkind'
        },
        'idregion': {
            type: 'SELECT',
            name: 'idregion'
        },
        'idprovince[]': {
            type: 'SELECT_MANY', // select2
            name: 'idprovince[]'
        },
        'phanmucid[]': {
            type: 'SELECT_MANY', // select2
            name: 'phanmucid[]'
        },
        'vsic[]': {
            type: 'SELECT_MANY', // select2
            name: 'vsic[]'
        },
        'goods': {
            type: 'RADIO',
            name: 'goods_search'
        },
        'cat': {
            type: 'SELECT',
            name: 'cat'
        },
        'type_choose_id': {
            type: 'SELECT',
            name: 'type_choose_id'
        },
        'type_org': {
            type: 'RADIO',
            name: 'type_org'
        },
        'field[]': {
            type: 'CHECKBOX_MANY',
            name: 'field[]'
        },
        'phuong_thuc[]': {
            type: 'CHECKBOX_MANY',
            name: 'phuong_thuc[]'
        },
        'money_from': {
            type: 'TEXT',
            name: 'money_from'
        },
        'money_to': {
            type: 'TEXT',
            name: 'money_to'
        },
        'price_from': {
            type: 'TEXT',
            name: 'price_from'
        },
        'price_to': {
            type: 'TEXT',
            name: 'price_to'
        },
    },
    
    'KQLCNT': {
        ... BASE,
        'type_search': {
            type: 'SELECT',
            name: 'search_info',
        },
        'search_type_content': {
            type: 'CHECKBOX_ONE',
            name: 'search_type_content'
        },
        'searchkind': {
            type: 'RADIO',
            name: 'searchkind'
        },
        'idregion': {
            type: 'SELECT',
            name: 'idregion'
        },
        'idprovincekq[]': {
            type: 'SELECT_MANY', // select2
            name: 'idprovince[]'
        },
        'catressult': {
            type: 'SELECT',
            name: 'cat_htduthau'
        },
        'type_kqlcnt': {
            type: 'SELECT',
            name: 'type_kqlcnt'
        },
        'type_choose_id': {
            type: 'SELECT',
            name: 'type_choose_id'
        },
        'goods_2': {
            type: 'RADIO',
            name: 'goods_search'
        },
        'bidfieid[]': {
            type: 'CHECKBOX_MANY',
            name: 'bidfieid[]'
        },
        'price_plan_from': {
            type: 'TEXT',
            name: 'price_plan_from'
        },
        'price_plan_to': {
            type: 'TEXT',
            name: 'price_plan_to'
        },
        'win_price_from': {
            type: 'TEXT',
            name: 'win_price_from'
        },
        'win_price_to': {
            type: 'TEXT',
            name: 'win_price_to'
        },
    },
    
    'YCBG': {
        ... BASE,
        'type_search': {
            type: 'SELECT',
            name: 'search_info',
        },
        'searchkind': {
            type: 'RADIO',
            name: 'searchkind'
        },
        'rq_form_value': {
            type: 'RADIO',
            name: 'rq_form_value'
        },
        'rq_investor[]': {
            type: 'SELECT_MANY',
            name: 'rq_investor[]'
        },
        'rq_province': {
            type: 'SELECT',
            name: 'keyword_id_province'
        },
        'rq_district': {
            type: 'SELECT',
            name: 'keyword_id_district'
        },
        'rq_ward': {
            type: 'SELECT',
            name: 'keyword_id_ward'
        }
    },
    
    'DG': {
        ... BASE,
        'search_type_content': {
            type: 'CHECKBOX_ONE',
            name: 'search_type_content'
        },
        'searchkind': {
            type: 'RADIO',
            name: 'searchkind'
        },
        'keyword_id_bidder': {
            type: 'SELECT',
            name: 'keyword_id_bidder'
        },
        'keyword_min_bid_prices': {
            type: 'TEXT',
            name: 'keyword_min_bid_prices'
        },
        'keyword_max_bid_prices': {
            type: 'TEXT',
            name: 'keyword_max_bid_prices'
        },
    },
    
    'CBDMDA': BASE,
    'TBMDT': {
        ... BASE,
        'field[]': {
            type: 'CHECKBOX_MANY',
            name: 'field[]'
        },
        'phuong_thuc[]': {
            type: 'CHECKBOX_MANY',
            name: 'phuong_thuc[]'
        },
        'money_from': {
            type: 'TEXT',
            name: 'money_from'
        },
        'money_to': {
            type: 'TEXT',
            name: 'money_to'
        },
        'price_from': {
            type: 'TEXT',
            name: 'price_from'
        },
        'price_to': {
            type: 'TEXT',
            name: 'price_to'
        },
    },
    'TBMSTQTNDT': BASE,
    'KHLCNDT': {
        ... BASE,
        'invest_from': {
            type: 'TEXT',
            name: 'invest_from'
        },
        'invest_to': {
            type: 'TEXT',
            name: 'invest_to'
        },
    },
    'KQLCNDT': BASE,
    'KQSTNDT': BASE,
}

$('#preview-button').on('click', function (params) {
    function computeFilterValue(selector) {
        const fieldElements = $(selector, filterForm);
        if (fieldElements.length > 1) {
            for (let i = 0; i < fieldElements.length; i++) {
                if (fieldElements[i].getBoundingClientRect().width && fieldElements[i].getBoundingClientRect().height) {
                    return $(fieldElements[i]).val();
                }
            }
        } else {
            return $(fieldElements).val();
        }
    }

    function addToForm(name, val) {
        previewForm.append('<input type="hidden" name="' + name + '" value="' + val + '">');
    }

    function setFormField(kind) {
        previewForm.append('<input type="hidden" name="is_advance" value="1">');
        if (search_info == 1) {
            previewForm.append('<input type="hidden" name="type_info" value="'+ filterMapNT[vip_use] +'">');
        } else if (search_info == 2) {
            previewForm.append('<input type="hidden" name="type_info2" value="'+ filterMapNDT[vip_use2] +'">');
        } else {
            previewForm.append('<input type="hidden" name="type_info3" value="'+ filterMapDG[vip_use3] +'">');
        }
        
        if (search_info == 1 && vip_use == 5) {
            const v5 = computeFilterValue('[name=vip5_open]:checked');
            if (v5) {
                previewForm.append('<input type="hidden" name="vip5_open" value="1">');
            }
        }
        
        for (const previewField in data[kind]) {
            const type = data[kind][previewField].type;
            const name = data[kind][previewField].name;
            
            /*
                Phải lấy value của trường đang được hiển thị, bởi vì có nhiều field cùng name nhưng có những cái bị ẩn đi theo 
                từng loại bộ lọc, nên không check sự hiện diện của nó ở thực tế thì sẽ sai.
                Check bằng cách dùng hàm getBoundingClientRect(), vì cái input đó có thể bị ẩn đi bởi thẻ bao ngoài
                VD field `type_choose_id` có 2 cái, một cái bị display none đi
                
                Riêng trường hợp 'CHECKBOX_MANY' ko chạy qua hàm computeFilterValue, mà check width, height trực tiếp luôn
            */
            
            switch (type) {
                case 'RADIO':
                    const val = computeFilterValue('[name=' + name + ']:checked');
                    addToForm(previewField, val);
                    break;
                    
                case 'CHECKBOX_MANY':
                    const checks = $('[name="' + name + '"]:checked', filterForm);
                    for (let i = 0; i < checks.length; i++) {
                        const check = checks[i];
                        if (check.getBoundingClientRect().width && check.getBoundingClientRect().height) {
                            const val = $(check).val();
                            addToForm(previewField, val);
                        }
                    }
                    break;
                    
                case 'SELECT_MANY':
                    const ids = computeFilterValue('[name="' + name + '"]');
                    for (const val of ids) {
                        addToForm(previewField, val);
                    }
                    break;
                    
                case 'CHECKBOX_ONE':
                    const filterValue = computeFilterValue('[name=' + name + ']:checked');
                    if (filterValue) {
                        addToForm(previewField, 1);
                    }
                    break;
            
                default:
                    const value = computeFilterValue('[name=' + name + ']');
                    if (value) {
                        addToForm(previewField, value);
                    }
                    break;
            }
        }  
    }
       
    let actionUrl = '';
    
    const search_info =  parseInt($('#filter-form [name=search_info]').val());
    
    const vip_use =  parseInt($('#filter-form [name=vip_use]').val());
    const vip_use2 =  parseInt($('#filter-form [name=vip_use2]').val());
    const vip_use3 =  parseInt($('#filter-form [name=vip_use3]').val());
    
    if ( search_info == 1 ) {
        switch (vip_use) {
            case 2:
                actionUrl = Action.khlcnt;
                break;
            case 21:
                actionUrl = Action.khlcnt;
                break;
            case 7:
                actionUrl = Action.kqlcnt;
                break;
            case 8:
                actionUrl = Action.ycbg;
                break;
            default:
                actionUrl = Action.tbmt;
                break;
        }
    } else if ( search_info == 2 ) {
        switch (vip_use2) {
            case 1:
                actionUrl = Action.duan;
                break;
            case 2:
                actionUrl = Action.tbmdt;
                break;
            case 3:
                actionUrl = Action.mstndt;
                break;
            case 4:
                actionUrl = Action.khlcndt;
                break;
            case 5:
                actionUrl = Action.kqlcndt;
                break;
            default:
                actionUrl = Action.kqstndt;
                break;
        }
    } else {
        switch (vip_use3) {
            case 1:
                actionUrl = Action.tbdg;
                break;
            default:
                actionUrl = Action.tbtcdg;
                break;
        }
    }
    
    // Trước tiên, disabled tất cả để tránh bị lẫn các field không cần thiết
    $('input', previewForm).remove();
    
    if ( search_info == 1 ) {
        if (vip_use == 2 || vip_use == 21) {
            setFormField('KHLCNT');
        } else if (vip_use == 7) {
            setFormField('KQLCNT');
        } else if (vip_use == 8) {
            setFormField('YCBG');
        } else {
            if (vip_use == 11 || vip_use == 5) {
                delete data.TBMT['idregion'];
                delete data.TBMT['idprovince[]'];
                delete data.TBMT['phanmucid[]'];
            }
            setFormField('TBMT');
        }
    } else if (search_info == 3) {
        if (vip_use3 == 2) {
            delete data.DG['keyword_id_bidder'];
        }
        setFormField('DG');
    } else {
        if (vip_use2 == 1) {
            setFormField('CBDMDA');
        } else if (vip_use2 == 2) {
            setFormField('TBMDT');
        } else if (vip_use2 == 3) {
            setFormField('TBMSTQTNDT');
        } else if (vip_use2 == 4) {
            setFormField('KHLCNDT');
        } else if (vip_use2 == 5) {
            setFormField('KQLCNDT');
        } else {
            setFormField('KQSTNDT');
        }
    }
    
    $("#preview-form").attr('action', actionUrl).trigger('submit');
})
