<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_BIDDING'))
    die('Stop!!!');

if ($key != '') {
    $_qr = ' LIKE ' . $db->quote('%' . $key . '%');
} else {
    $_qr = ' != ""';
}

if ($array_key['type'] != 'all') {
    $_col = $array_key['type'];
    if ($key != '') {
        $w_sql = ' AND t1.' . $_col . $_qr;
    } else {
        $w_sql = ' AND t1.' . $_col . $_qr;
    }
} else {
    $arr_sql = [
        't1.goods_name' . $_qr,
        't1.sign_product' . $_qr,
        't1.description' . $_qr,
        't1.origin' . $_qr
    ];
    $w_sql = ' AND (' . implode(' OR ', $arr_sql) . ')';
}

if (!empty($_arr_sfrom) && !empty($_arr_sto)) {
    $w_sql .= ' AND t2.finish_time >= ' . mktime(0, 0, 0, $_arr_sfrom[1], $_arr_sfrom[0], $_arr_sfrom[2]);
    $w_sql .= ' AND t2.finish_time <= ' . mktime(23, 59, 59, $_arr_sto[1], $_arr_sto[0], $_arr_sto[2]);
}

$sql_in = $sql_in2 = $_sql_buy = '';
if (!empty($result)) {
    $_sql_buy .= ' AND t1.result_id IN(' . implode(',', $result) . ')';
}

if (!empty($array_points_goods)) {
    $sql_in .= ' AND t1.id IN(' . implode(',', $array_points_goods) . ')';
    $sql_in2 .= ' AND t1.id NOT IN(' . implode(',', $array_points_goods) . ')';
}
$num_items_buy = 0;

$order = ' ORDER BY t2.finish_time DESC';
if (!empty($array_points_goods)) {
    // Tìm kiếm trong các hàng hoá mà khách hàng đã đổi điểm trước
    $sql_goods = 'SELECT t1.*, t2.solicitor_id FROM ' . BID_PREFIX_GLOBAL . '_result_goods as t1 INNER JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_result as t2 ON t1.result_id = t2.id WHERE t1.type_info = 0 ' . $_sql_buy . $sql_in . $w_sql . ' LIMIT ' . $per_page . ' OFFSET ' . ($page - 1) * $per_page;
    $num_items_buy = $db->query($sql_goods)->rowCount();
}

$sql2 = 'SELECT t1.*, t2.solicitor_id FROM ' . BID_PREFIX_GLOBAL . '_result_goods as t1 INNER JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_result as t2 ON t1.result_id = t2.id WHERE t1.type_info = 0 ' . $_sql_buy . $w_sql . $sql_in2 . $order;

$num_items = $db->query($sql2)->rowCount();

$_goods = [];
if (!empty($sql_goods)) {
    $_goods = $db->query($sql_goods)->fetchAll();
}

if ($num_items_buy < $page * $per_page) {
    $goods = $db->query($sql2 . ' LIMIT ' . ($per_page - $num_items_buy) . ' OFFSET ' . ($page - 1) * $per_page)->fetchAll();
    $goods = !empty($_goods) ? array_merge($_goods, $goods) : $goods;
} else {
    $goods = $_goods;
}
foreach ($goods as &$good) {
    $good['bid_price'] = money2number($good['bid_price']);
}
unset($good);
// Kiểm tra hàng hóa từ dữ liệu bóc về của bộ y tế
if (isset($flag)) {
    if ($key != ''){
        $where = ' name LIKE ' . $db->quote('%' . $key . '%');
    }else{
        $where = ' name != ""';
    }

    $sql_1 = 'SELECT * FROM nv4_thiet_bi_byt WHERE '.$where;
    $num_items_byt = $db->query($sql_1)->rowCount();
    $byt_list = $db->query($sql_1 . ' LIMIT ' . $per_page . ' OFFSET ' . ($page - 1) * $per_page)->fetchAll();
    foreach ($byt_list as $k => $v) {
        $byt_list[$k]['price_bid'] = money2number($byt_list[$k]['price_bid']);
    }
}
