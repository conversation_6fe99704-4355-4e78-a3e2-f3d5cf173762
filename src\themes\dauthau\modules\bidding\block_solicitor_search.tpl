<!-- BEGIN: main -->
<div class="border-bidding">
    <span>{TITLE_BLOCK}</span>
</div>
<form action="{FORM_ACTION}" method="get" onsubmit="return checkSearchForm(this);" class="form-horizontal form-search margin-bottom-lg" id="bussSearchBlock" data-bussaction="{FORM_ACTION2}" data-bussaction1="{FORM_ACTION1}">
    <!-- BEGIN: no_rewrite -->
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"/>
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}"/>
    <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}"/>
    <!-- END: no_rewrite -->
    <div class="form-group">
        <label class="control-label col-md-6">{LANG.tukhoa}:</label>
        <div class="col-md-18">
            <input data-default="" placeholder="{LANG.search_title}" class="form-control" type="text" value="{KEYWORD}" name="q"/>
        </div>
    </div>
    <!-- BEGIN: type_data_select -->
    <div class="form-group">
        <label class="control-label col-md-6">{LANG.type_data}:</label>
        <div class="col-md-18">
            <select class="form-control" id="type_data" name="type_data" data-default="{TYPE_DATA_VAL}">
                <!-- BEGIN: type_data -->
                <option value="{TYPE_DATA.id}"{TYPE_DATA.select}> {TYPE_DATA.value}</option>
                <!-- END: type_data -->
            </select>
        </div>
    </div>
    <!-- END: type_data_select -->
    <div class="row">
        <div class="col-md-18 col-md-offset-6">
            <input type="hidden" name="is_submit_form" value="{IS_SUBMIT}" />
            <input type="hidden" name="is_advance" value="{ADVANCE}"/>
            <input id="fsearch" type="submit" value="{LANG.search}" class="btn btn-primary btn_submit_search"/>
            <a class="btn-search-advance btn btn-default" href="javascript:void(0);" data-search-simple="{LANG.search_simple}" data-search-advance="{LANG.search_advance}" data-icon-search-simple="icon-chevron-down" data-icon-search-advance="icon-chevron-up">
                <em class="<!-- BEGIN: advance_icon_0 -->icon-chevron-down <!-- END: advance_icon_0 -->
                <!-- BEGIN: advance_icon_1 -->icon-chevron-up <!-- END: advance_icon_1 -->margin-right-sm"></em>
                <strong class="txt">{LANG_ADVANCE}</strong>
            </a>
        </div>
    </div>
    <div class="advance-search-content" <!-- BEGIN: advance_search_hide -->style="display: none"<!-- END: advance_search_hide -->>
        <div class="form-group">
            <label class="control-label col-md-6">{LANG.project}:</label>
            <div class="col-md-18">
                <input placeholder="{LANG.search_total_project}" class="form-control" min="0" type="number" data-default="" value="{NUM_PROJECT}" name="num_project"/>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-6">{LANG.static_khlcnt}:</label>
            <div class="col-md-18">
                <input placeholder="{LANG.search_total_plan}" class="form-control" min="0" pattern="[0-9]" data-default="" type="number" value="{NUM_PLAN}" name="num_plan"/>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-6">{LANG.static_tbmt_1}:</label>
            <div class="col-md-18">
                <input placeholder="{LANG.search_total_bid}" class="form-control" min="0" type="number" data-default="" value="{NUM_TBMT}" name="num_tbmt"/>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-6">{LANG.static_kqmt_unlinked}:</label>
            <div class="col-md-18">
                <input placeholder="{LANG.search_total_open}" class="form-control" min="0" type="number" data-default="" value="{NUM_KQMT}" name="num_kqmt"/>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-6">{LANG.static_kqlcnt}:</label>
            <div class="col-md-18">
                <input placeholder="{LANG.search_total_result}" class="form-control" min="0" type="number" data-default="" value="{NUM_KQLCNT}" name="num_kqlcnt"/>
            </div>
        </div>
        <div class="form-group" id="location">
            <label class="control-label col-md-6">{LANG.diadiem}:</label>
            <div class="col-md-18">
                <select disabled="disabled" data-selected="{SEARCH_PROVINCE}" name="province" id="province" class="form-control" data-default="{LANG.pleaseselect}" data-no_title="{LANG.no_title}" data-timestamp="{TIMESTAMP}">
                    <option value="-1">{LANG.pleaseselect}</option>
                    <option value="0">{LANG.no_title}</option>
                </select>
                <div class="margin-top-lg{CLASS_DISTRICT}">
                    <select disabled="disabled" data-selected="{SEARCH_DISTRICT}" name="district" id="district" class="form-control" data-default="{LANG.pleaseselect}" data-timestamp="{TIMESTAMP}">
                        <option value="0">{LANG.pleaseselect}</option>
                    </select>
                </div>
                <div class="margin-top-lg{CLASS_WARD}">
                    <select disabled="disabled" data-selected="{SEARCH_WARD}" name="ward" id="ward" class="form-control" data-default="{LANG.pleaseselect}" data-timestamp="{TIMESTAMP}">
                        <option value="0">{LANG.pleaseselect}</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="form-group" id="date_approved">
            <label class="control-label col-md-6">{LANG.filter_ngay_phe_duyet}:</label>
            <div class="col-md-7">
                <input type="hidden" name="sfrom_business" value="{SFROM}" data-default="">
                <input type="hidden" name="sto_business" value="{STO}" data-default="">
                <input placeholder="dd/mm/yyyy - dd/mm/yyyy" class="form-control search_range_business" type="text" value="{SFROM} - {STO}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-6">{LANG.order_by}:</label>
            <div class="col-md-18">
                <select class="form-control" id="order_by" name="order_by" data-default="{ORDER_BY_VAL}">
                    <!-- BEGIN: order_by -->
                    <option value="{ORDER_BY.id}"{ORDER_BY.select}> {ORDER_BY.value}</option>
                    <!-- END: order_by -->
                </select>
                <em class="help-block margin-bottom-sm">({LANG.from_to})</em>
            </div>
        </div>
        <div class="row">
            <div class="col-md-18 col-md-offset-6">
                <input id="fsearch" type="submit" value="{LANG.search}" class="btn btn-primary btn_submit_search"/>
                <input class="btn btn-default form-reset" type="button" value="{LANG.reset_form}" onclick="formReset();">
            </div>
        </div>
    </div>

</form>

<style>
    #infobmt .border-bidding {
        border: none!important;
    }
</style>

<script type="text/javascript">
    var formObject_business = $("[id=bussSearchBlock]");
    function checkSearchForm(data) {
        var is_submit_form = parseInt($('[name=is_submit_form]').val());
        if (is_submit_form === 1) {
            return false;
        } else {
            return true;
        }
    }
    function bl_setDaterangepicker_business(_options) {

        // Menu khoảng tìm kiếm
        var ranges_business = {};
        ranges_business['{LANG.this_month}'] = [moment().startOf('month'), moment().endOf('month')];
        ranges_business['{LANG.last_3_months}'] = [moment().subtract(3, 'months'), moment()];
        ranges_business['{LANG.last_6_months}'] = [ moment().subtract(6, 'months'), moment() ];
        ranges_business['{LANG.this_year}'] = [moment().startOf('year'), moment().endOf('year')];
        ranges_business['{LANG.none}'] = [null, null];

        var calendar_options_business = {
            showDropdowns: true,
            locale: {customRangeLabel: '{LANG.custom_range}', format: 'DD/MM/YYYY', help: ''},
            ranges: ranges_business,
            startDate: moment().subtract(14, 'days'),
            endDate: moment(),
            opens: 'right',
            drops: "auto",
            alwaysShowCalendars: false,
            maxDate: moment(),
            minYear: 1971
        };

        $.extend(calendar_options_business, _options);

        $(".search_range_business", formObject_business).daterangepicker(calendar_options_business, function (start, end, label) {
            if (!start.isValid() || !end.isValid()) {

                $("[name=sfrom_business]", formObject_business).val('');
                $("[name=sto_business]", formObject_business).val('')
            } else {
                $("[name=sfrom_business]", formObject_business).val(start.format('DD/MM/YYYY'));
                $("[name=sto_business]", formObject_business).val(end.format('DD/MM/YYYY'))
            }

        }).on("apply.daterangepicker", function (e, picker) {
            if (!picker.startDate.isValid() || !picker.endDate.isValid()) {
                picker.element.val('');
            } else {
                picker.element.val(picker.startDate.format(picker.locale.format) + ' - ' + picker.endDate.format(picker.locale.format));
            }
        }).on("showCalendar.daterangepicker", function (e, picker) {
            if (!picker.startDate.isValid() || !picker.endDate.isValid()) {
                bl_setDaterangepicker_business({
                    startDate: moment().format('DD/MM/YYYY'),
                    endDate: moment().format('DD/MM/YYYY')
                });
                $(".search_range_business").trigger('click');
            }
        })
    }

    function checkSelect2Template(selector, value, e = null) {
        var $select = $(selector);
        var selectedValue = $select.val();
        if (e != null) {
            $select.select2({
                templateResult: updateSelectTemplate,
                templateSelection: updateSelectTemplate
            });
        }

        if (selectedValue == value) {
            $select.find('option:first').text("{LANG.pleaseselect}");
            if (e != null) {
                $select.trigger('change');
            }
        } else {
            $select.find('option:first').text("{LANG.deselect}");
            if (e != null) {
                $select.trigger('change');
            }
        }
    }

    function updateSelectTemplate(data) {
        var $option = $(data.element);

        return $option.text();
    }

    function sleep_promise(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
        bl_setDaterangepicker_business({
            startDate: $("[name=sfrom_business]", formObject_business).val(),
            endDate: $("[name=sto_business]", formObject_business).val()
        });
        if ($("[name=sfrom_business]", formObject_business).val() == '' || $("[name=sto_business]", formObject_business).val() == '' || $(".search_range_business").val() == 'Invalid date - Invalid date') {
            $(".search_range_business").val('');
        }
        $(".search_range_business").on('change', function() {
            if ($("[name=sfrom_business]", formObject_business).val() == '' || $("[name=sto_business]", formObject_business).val() == '' || $(".search_range_business").val() == 'Invalid date - Invalid date') {
                $(".search_range_business").val('');
            }
        });

        $(".btn-search-advance", formObject_business).click(function(a) {
            a.preventDefault();
            is_advance = $('input[name="is_advance"]', formObject_business).val();
            if(parseInt(is_advance) == 1) {
                $(".advance-search-content", formObject_business).not(":hidden") && $(".advance-search-content", formObject_business).slideUp();
                $('input[name="is_advance"]', formObject_business).val(0);
                $(".btn-search-advance em", formObject_business).removeClass($(".btn-search-advance", formObject_business).data("icon-search-advance")).addClass($(".btn-search-advance", formObject_business).data("icon-search-simple"));
                $(".btn-search-advance .txt", formObject_business).text($(".btn-search-advance", formObject_business).data("search-advance"))
            } else {
                $(".advance-search-content", formObject_business).is(":hidden") && $(".advance-search-content", formObject_business).slideDown();
                $('input[name="is_advance"]', formObject_business).val(1);
                $(".btn-search-advance em", formObject_business).removeClass($(".btn-search-advance", formObject_business).data("icon-search-simple")).addClass($(".btn-search-advance", formObject_business).data("icon-search-advance"));
                $(".btn-search-advance .txt", formObject_business).text($(".btn-search-advance", formObject_business).data("search-simple"))
            }
        });

        function convertToSlug(text) {
            text = text.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
            return text
                .toLowerCase()
                .replace(/đ/g, 'd')          // Đổi 'đ' thành 'd'
                .trim()
                .replace(/\s+/g, '-')        // Thay thế khoảng trắng bằng dấu gạch ngang
                .replace(/[^a-z0-9-]/g, '')  // Loại bỏ các ký tự không phải chữ cái và số
                .replace(/-+/g, '-');        // Loại bỏ dấu gạch ngang thừa
        }

        var selProvince = $('select[name="province"]');
        var selDistrict = $('select[name="district"]');
        var selWard = $('select[name="ward"]');

        // Load ra tỉnh
        function loadProvince() {
            selProvince.prop('disabled', true);
            selDistrict.prop('disabled', true);
            selWard.prop('disabled', true);
            $.ajax({
                url: domain_load_remote + 'data/config/location-province-' + nv_lang_interface + '.json?t=' + selProvince.data('timestamp'),
                cache: true,
                dataType: 'json',
                method: 'GET',
                success: function(json) {
                    var html = '<option value="-1">' + selProvince.data('default') + '</option>';
                    html += '<option data-alias="' + convertToSlug(selProvince.data('no_title')) + '" value="0"' + (selProvince.data('selected') == 0 ? ' selected="selected"' : '') + '>' + selProvince.data('no_title') + '</option>';
                    $.each(json, function(id, title) {
                        html += '<option data-alias="' + convertToSlug(title) + '" value="' + id + '"' + (id == selProvince.data('selected') ? ' selected="selected"' : '') + '>' + title + '</option>';
                    });
                    selProvince.html(html);
                    selProvince.prop('disabled', false);
                    selProvince.select2({
                        'width' : '100%'
                    });
                    loadDistrict();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR, textStatus, errorThrown);
                }
            });
        }

        // Load ra huyện
        function loadDistrict() {
            selDistrict.prop('disabled', true);
            selWard.prop('disabled', true);
            if (selProvince.val() <= 0) {
                selDistrict.prop('disabled', false);
                selDistrict.html('<option value="0">' + selDistrict.data('default') + '</option>').select2({
                    'width' : '100%'
                });
                selDistrict.parent().addClass('hidden');
                loadWard();
                return;
            }
            $.ajax({
                url: domain_load_remote + 'data/config/location-district-' + nv_lang_interface + '.json?t=' + selDistrict.data('timestamp'),
                cache: true,
                dataType: 'json',
                method: 'GET',
                success: function(json) {
                    var html = '<option value="0">' + selDistrict.data('default') + '</option>';
                    if (typeof json[selProvince.val()] != 'undefined') {
                        json = json[selProvince.val()];
                        $.each(json, function(id, title) {
                            html += '<option data-alias="' + convertToSlug(title) + '" value="' + id + '"' + (id == selDistrict.data('selected') ? ' selected="selected"' : '') + '>' + title + '</option>';
                        });
                    }
                    selDistrict.prop('disabled', false);
                    selDistrict.parent().removeClass('hidden');
                    selDistrict.html(html).select2({
                        'width' : '100%'
                    });
                    loadWard();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR, textStatus, errorThrown);
                }
            });
        }

        // Load ra xã
        function loadWard() {
            selWard.prop('disabled', true);
            if (selDistrict.val() == 0) {
                selWard.prop('disabled', false);
                selWard.html('<option value="0">' + selWard.data('default') + '</option>').select2({
                    'width' : '100%'
                });
                selWard.parent().addClass('hidden');
                return;
            }
            $.ajax({
                url: domain_load_remote + 'data/config/location-ward-' + nv_lang_interface + '.json?t=' + selWard.data('timestamp'),
                cache: true,
                dataType: 'json',
                method: 'GET',
                success: function(json) {
                    var html = '<option value="0">' + selWard.data('default') + '</option>';
                    if (typeof json[selDistrict.val()] != 'undefined') {
                        json = json[selDistrict.val()];
                        $.each(json, function(id, title) {
                            html += '<option data-alias="' + convertToSlug(title) + '" value="' + id + '"' + (id == selWard.data('selected') ? ' selected="selected"' : '') + '>' + title + '</option>';
                        });
                    }
                    selWard.prop('disabled', false);
                    selWard.parent().removeClass('hidden');
                    selWard.html(html).select2({
                        'width' : '100%'
                    });
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR, textStatus, errorThrown);
                }
            });
        }

        if (selProvince.length && selDistrict.length && selWard.length) {
            loadProvince();
        }

        function getSelectedLocationSlug() {
            var provinceSlug = $('#bussSearchBlock').find('#province option:selected').data('alias');
            var districtSlug = $('#bussSearchBlock').find('#district option:selected').data('alias');
            var wardSlug = $('#bussSearchBlock').find('#ward option:selected').data('alias');
            return { provinceSlug, districtSlug, wardSlug };
        }

        function updateFormAction() {
            sleep_promise(10).then(() => {
                var slugs = getSelectedLocationSlug();
                var id_province = $('#bussSearchBlock').find('#province option:selected').val();
                var district = $('#bussSearchBlock').find('#district option:selected').val();
                var ward = $('#bussSearchBlock').find('#ward option:selected').val();
                var keyword = $('#bussSearchBlock').find('[name=q]').val();
                var num_project = $('#bussSearchBlock').find('[name=num_project]').val();
                var num_plan = $('#bussSearchBlock').find('[name=num_plan]').val();
                var num_tbmt = $('#bussSearchBlock').find('[name=num_tbmt]').val();
                var num_kqmt = $('#bussSearchBlock').find('[name=num_kqmt]').val();
                var num_kqlcnt = $('#bussSearchBlock').find('[name=num_kqlcnt]').val();
                var sfrom_business = $('#bussSearchBlock').find('[name="sfrom_business"]').val();
                var sto_business = $('#bussSearchBlock').find('[name="sto_business"]').val();
                var type_data = $('#type_data').val();
                var order_by = $('#order_by').val();
                var actionUrl = $('#bussSearchBlock').data("bussaction");
                // nếu là chủ đầu tư thì chỉ lấy những chủ đầu tư có thông tin chi tiết
                if (nv_func_name == 'project-owner') {
                    type_data = 1; // Chỉ lấy dữ liệu có thông tin chi tiết
                }
                if (id_province !== "-1" && keyword === '' && num_project === "" && num_plan === "" && num_tbmt === "" && num_kqmt === "" && num_kqlcnt === "" && sfrom_business === "" && sto_business === "" && order_by === "1") {
                    // Nếu chỉ chọn tỉnh/thành phố
                    if (district === "0" && ward === "0") {
                        // Chỉ chọn province
                        actionUrl = `${$('#bussSearchBlock').data("bussaction1")}T-${encodeURIComponent(slugs.provinceSlug)}-${encodeURIComponent(id_province)}`;
                    } else if (district !== "0" && ward === "0") {
                        // Chọn province và district, nhưng không chọn ward
                        actionUrl = `${$('#bussSearchBlock').data("bussaction1")}H-${encodeURIComponent(slugs.districtSlug)}-${encodeURIComponent(district)}`;
                    } else if (district !== "0" && ward !== "0") {
                        // Chọn cả province, district và ward
                        actionUrl = `${$('#bussSearchBlock').data("bussaction1")}X-${encodeURIComponent(slugs.wardSlug)}-${encodeURIComponent(ward)}`;
                    }
                    $('input[name="is_submit_form"]').val(1);
                } else {
                    $('input[name="is_submit_form"]').val(0);
                }
                if (type_data == 1) {
                    $('#bussSearchBlock').attr("action", actionUrl);
                }
            });
        }

        $("select[name='province']").on('select2:select', function(e) {
            loadDistrict();
            checkSelect2Template("select[name='province']", '-1', e);
            var val_district = $("select[name='district']").data('selected');
            sleep_promise(100).then(() => {
                if (val_district == '0') {
                    $("select[name='district']").find('option:first').text("{LANG.pleaseselect}");
                    $("select[name='district']").trigger('change.select2');
                } else {
                    $("select[name='district'] option[value='0']").text("{LANG.deselect}");
                    $("select[name='district']").trigger('change.select2');

                    var val_ward = $("select[name='ward']").data('selected');
                    sleep_promise(100).then(() => {
                        if (val_ward == '0') {
                            $("select[name='ward']").find('option:first').text("{LANG.pleaseselect}");
                            $("select[name='ward']").trigger('change.select2');
                        } else {
                            $("select[name='ward'] option[value='0']").text("{LANG.deselect}");
                            $("select[name='ward']").trigger('change.select2');
                        }
                    });
                }
            });
        });

        $("select[name='district']").on('select2:select', function(e) {
            loadWard();
            checkSelect2Template("select[name='district']", '0', e);
        });
        $("select[name='ward']").on('select2:select', function(e) {
            checkSelect2Template("select[name='ward']", '0', e);
        });

        $('#bussSearchBlock').find('#province').on('change', updateFormAction);
        $('#bussSearchBlock').find('#district').on('change', updateFormAction);
        $('#bussSearchBlock').find('#ward').on('change', updateFormAction);
        $('#bussSearchBlock').find('[name=q]').on('input', updateFormAction);
        $('#bussSearchBlock').find('[name=num_project]').on('input', updateFormAction);
        $('#bussSearchBlock').find('[name=num_plan]').on('input', updateFormAction);
        $('#bussSearchBlock').find('[name=num_tbmt]').on('input', updateFormAction);
        $('#bussSearchBlock').find('[name=num_kqmt]').on('input', updateFormAction);
        $('#bussSearchBlock').find('[name=num_kqlcnt]').on('input', updateFormAction);
        $('#bussSearchBlock').find('.search_range_business').on('change', updateFormAction);
        $('#bussSearchBlock').find('#type_data').on('change', updateFormAction);
        $('#bussSearchBlock').find('#order_by').on('change', updateFormAction);

        $('.btn_submit_search').click(function(e) {
            var is_submit_form = parseInt($('[name=is_submit_form]').val());
            var actionUrl = $('#bussSearchBlock').attr("action");
            if (is_submit_form === 1) {
                window.location.href = actionUrl;
            }
        });
    });

    $(window).on('load', function() {
        checkSelect2Template("select[name='province']", '-1');
        checkSelect2Template("select[name='district']", '0');
        checkSelect2Template("select[name='ward']", '0');
    });

    var t1 = $("#type_data").val();
    change_type_data(t1);

    $("#type_data").change(function () {
        val = $(this).val();
        change_type_data(val);
    });

    function change_type_data(val) {
        if (val == 2) {
            // Không có thông tin chi tiết
            $('#date_approved').hide();
            $('#location').hide();
            $('#bussSearchBlock').attr('action', '{FORM_ACTION_UNLINK}');
        } else {
            $('#date_approved').show();
            $('#location').show();
            $('#bussSearchBlock').attr('action', '{FORM_ACTION}');
        }
    }

    function formReset() {
        $(".has-error", formObject_business).removeClass("has-error");
        $("[data-default]", formObject_business).each(function() {
            if ($(this).is("input[type=text], input[type=hidden], input[type=number]")) {
                $(this).val($(this).attr("data-default"));
            } else if ($(this).is("select")) {
                $(this).val($(this).attr("data-default"));
                $('#province').val(-1).trigger('change.select2');
                $('#district').val(0).trigger('change.select2').parent().addClass('hidden');
                $('#ward').val(0).trigger('change.select2').parent().addClass('hidden');
                $('#type_data').trigger('change.select2');
            }
        });
        bl_setDaterangepicker_business({ 'startDate' : $("[name=sfrom_business]", formObject_business).attr("data-default"), 'endDate' : $("[name=sto_business]", formObject_business).attr("data-default") });
        $('html, body').animate({ scrollTop : formObject_business.offset().top }, 800);
    }


</script>

<!-- END: main -->
