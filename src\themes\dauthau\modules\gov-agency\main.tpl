<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<link type="text/css" rel="stylesheet" href="{NV_BASE_SITEURL}themes/dauthau/css/bidding.css"></link>

<div class="border-bidding">
    <h2 class="title__tab_heading"><span>{LANG.national}</span></h2>
</div>

<!-- BEGIN: empty -->
<div class="alert alert-warning">{LANG.empty_result}</div>
<!-- END: empty -->
<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->
<div class="margin-bottom-lg text-center">{TITLE}</div>

<!-- BEGIN: main_filter -->
<form action="{NV_BASE_SITEURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="get" id="form-filter" class="m-bottom {DMDC_CLASS}">
    <input type="hidden" name="sort" value="{SORT}" />
    
    <div class="row">
        <div class="form-group col-md-8">
            <label for="time_ranges" class="control-label">{LANG.time_filter}</label>
            <select name="t_range" id="time_ranges" class="form-control">
                <!-- BEGIN: time_ranges -->
                <option value="{TIME_RANGES.val}"{SELECTED}>{TIME_RANGES.text}</option>
                <!-- END: time_ranges -->
            </select>
        </div>
        
        <div class="form-group col-md-8">
            <label for="source" class="control-label">{LANG.source_filter}</label>
            <select name="source" id="source" class="form-control">
                <!-- BEGIN: source -->
                <option value="{SOURCE.val}"{SELECTED}>{SOURCE.text}</option>
                <!-- END: source -->
            </select>
        </div>
    </div>
    
    <div class="row">
        <div class="form-group col-xs-24 col-sm-8">
            <label class="control-label">{LANG.classify_select_txt}</label>
            <div class="row">
                <!-- BEGIN: classify -->
                <div class="col-xs-12">
                    <label class="custom-radio">
                        <input {CHECKED} type="radio" name="classify" value="{CLASSIFY.val}" onchange="on_classify_change()">
                        <span class="txt">{CLASSIFY.text}</span>
                    </label>
                </div>
                <!-- END: classify -->
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <div class="form-group align-center msc-group">
                <label for="type" class="control-label">{LANG.type_filter}</label>
                <select name="type" id="type" class="msc-field form-control col-md-16">
                    <!-- BEGIN: type -->
                    <option value="{TYPE.val}"{SELECTED}>{TYPE.text}</option>
                    <!-- END: type -->
                </select>
            </div>
            
            <div class="form-group align-center dmdc-group">
                <label for="management_type" class="control-label">{LANG.type_filter}</label>
                <select name="management_type" id="management_type" class="dmdc-field form-control col-md-16">
                    <!-- BEGIN: management_type -->
                    <option value="{MANAGEMENT_TYPE.val}" {SELECTED}>{MANAGEMENT_TYPE.text}</option>
                    <!-- END: management_type -->
                </select>
            </div>
            
        </div>
        <div class="col-md-16 form-group">
            <label class="control-label">{LANG.agency_filter}</label>
            
            <div class="msc-group">
                <select id="agency" class="form-control msc-field" name="agency[]" multiple="multiple">
                    <!-- BEGIN: agency_filter -->
                    <option value="{AGENCY.id}">{AGENCY.title}</option>
                    <!-- END: agency_filter -->
                </select>
            </div>
            
            <div class="dmdc-group">
                <select id="agency-management" class="dmdc-field form-control" name="agency_management_filter[]" multiple="multiple">
                    <!-- BEGIN: management_agency_filter -->
                    <option value="{MANAGEMENT_AGENCY.id}">{MANAGEMENT_AGENCY.title}</option>
                    <!-- END: management_agency_filter -->
                </select>
            </div>
        </div>
    </div>
    <div class="text-right m-bottom">
        <input style="width: 100px;" type="submit" value="{LANG.filter}" class="form-control btn btn-primary" />
    </div>
</form>
<!-- END: main_filter -->

<!-- BEGIN: view -->
<!-- <blockquote>{MAIN_SUMMARY}</blockquote> -->
<form action="{NV_BASE_SITEURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post" id="data_bidding">
    <table class="table table-bordered table-top-align-head gov-agency-table">
        <thead>
            <tr>
                <th class="text-center">{LANG.stt}</th>
                <th class="text-center cw-name">{LANG.agency_name}</th>
                <th class="text-center">{LANG.num_investor}</th>
                <th class="text-center">{LANG.num_project}</th>
                <th class="text-center">{LANG.num_project_not_complete}</th>
                <th class="text-center">{LANG.num_project_completed}</th>
                <th class="text-center">
                    <div class="row">
                        <div class="col-xs-20">
                            <a href="{LINK_SORT.bid}">{LANG.num_bid}</a>
                        </div>
                        <div class="col-xs-2">
                            <i name="bid-up" class="fa fa-caret-up"></i> <i name="bid-down" class="fa fa-caret-down"></i>
                        </div>
                    </div>
                </th>
            </tr>
        </thead>
        <tbody>
            <!-- BEGIN: loop -->
            <tr>
                <td class="text-center"><div>{VIEW.stt}</div></td>
                <td class="text-left"><div><a href="{VIEW.link_agency}">{VIEW.title}</a></div></td>
                <td class="text-right"><div><a href="{VIEW.link_solicitor}">{VIEW.num_distinct_investor}</a></div></td>
                <td class="text-right"><div>{VIEW.num_projects}</div></td>
                <td class="text-right"><div>{VIEW.num_not_complete}</div></td>
                <td class="text-right"><div>{VIEW.num_completed}</div></td>
                <td class="text-right"><div>{VIEW.num_bid}</div></td>
            </tr>
            <!-- END: loop -->
            <!-- <tr>
                <td class="text-center" colspan="2"><strong>{LANG.sum}</strong></td>
                <td class="text-right"><strong>{SUM.num_investor}</strong></td>
                <td class="text-right"><strong>{SUM.num_projects}</strong></td>
                <td class="text-right"><strong>{SUM.num_not_complete}</strong></td>
                <td class="text-right"><strong>{SUM.num_completed}</strong></td>
                <td class="text-right"><strong>{SUM.num_bid}</strong></td>
            </tr> -->
        </tbody>
    </table>
    
    <div class="gov-agency-table-mobile">
        <!-- BEGIN: loop_mobile -->
        <div class="item">
            <p><big><b><span>{VIEW.stt}. <a href="{VIEW.link_agency}">{VIEW.title}</a></span></b></big></p>
            <table class="table table-bordered">
                <tr><th>{LANG.num_investor}</th> <td><a href="{VIEW.link_solicitor}">{VIEW.num_distinct_investor}</a></td></tr>
                <tr><th>{LANG.num_project}</th> <td>{VIEW.num_projects}</td></tr>
                <tr><th>{LANG.num_project_not_complete}</th> <td>{VIEW.num_not_complete}</td></tr>
                <tr><th>{LANG.num_project_completed}</th> <td>{VIEW.num_completed}</td></tr>
                <tr><th>{LANG.num_bid}</th> <td>{VIEW.num_bid}</td></tr>
            </table>
        </div>
        <!-- END: loop_mobile -->
     </div>

    <!-- BEGIN: generate_page -->
    <div class="text-center">{GENERATE_PAGE}</div>
    <!-- END: generate_page -->
</form>
<!-- END: view -->
 
<!-- BEGIN: view_anonymous -->
 <div class="gov-agency-anonymous">
   {VIP_OVERLAY}
     <table class="table table-bordered table-top-align-head gov-agency-table">
         <thead>
             <tr>
                 <th class="text-center">{LANG.stt}</th>
                 <th class="text-center cw-name">{LANG.agency_name}</th>
                 <th class="text-center">{LANG.num_investor}</th>
                 <th class="text-center">{LANG.num_project}</th>
                 <th class="text-center">{LANG.num_project_not_complete}</th>
                 <th class="text-center">{LANG.num_project_completed}</th>
                 <th class="text-center">{LANG.num_bid}</th>
             </tr>
         </thead>
         <tbody>
             <!-- BEGIN: loop -->
             <tr>
                <td class="text-center">{VIEW.stt}</td>
                 <td class="text-left cw-120"><div><a href="{VIEW.link_agency}">{VIEW.title}</a></div></td>
                 <td class="text-center">{LOCK}</td>
                 <td class="text-center">{LOCK}</td>
                 <td class="text-center">{LOCK}</td>
                 <td class="text-center">{LOCK}</td>
                 <td class="text-center">{LOCK}</td>
             </tr>
             <!-- END: loop -->
             <tr>
                 <td class="text-center" colspan="2"><strong>{LANG.sum}</strong></td>
                 <td class="text-center"><strong>{LOCK}</strong></td>
                 <td class="text-center"><strong>{LOCK}</strong></td>
                 <td class="text-center"><strong>{LOCK}</strong></td>
                 <td class="text-center"><strong>{LOCK}</strong></td>
                 <td class="text-center"><strong>{LOCK}</strong></td>
             </tr>
         </tbody>
     </table>
 </div>
 
 <div class="gov-agency-anonymous-mobile">
    <!-- BEGIN: loop_mobile -->
    <div class="item">
        <p><big><b><span>{VIEW.stt}. <a href="{VIEW.link_agency}">{VIEW.title}</a></span></b></big></p>
        <table class="table table-bordered">
            <tr><th>{LANG.num_investor}</th><td>{LOCK}</td></tr>
            <tr><th>{LANG.num_project}</th><td>{LOCK}</td></tr>
            <tr><th>{LANG.num_project_not_complete}</th><td>{LOCK}</td></tr>
            <tr><th>{LANG.num_project_completed}</th><td>{LOCK}</td></tr>
            <tr><th>{LANG.num_bid}</th><td>{LOCK}</td></tr>
        </table>
    </div>
    <!-- END: loop_mobile -->
 </div>
 
<p class="alert alert-info">{ANONYMOUS_NOTE}</p>
<!-- END: view_anonymous -->
    
<script type="text/javascript">
    window.nv_msc_agency = '{AGENCY_SELECTED}';
    window.nv_management_agency = '{MANAGEMENT_AGENCY_SELECTED}';
    function on_classify_change() {
        $('#form-filter').toggleClass('dmdc');
        onOffFields();
    }
    
    function onOffFields() {
        if ($('#form-filter').hasClass('dmdc')) {
            $('.msc-field').prop('disabled', true);
            $('.dmdc-field').prop('disabled', false);
        } else {
            $('.dmdc-field').prop('disabled', true);
            $('.msc-field').prop('disabled', false);
        }
    }

    function sort_show(s) {
        switch (s) {
            case 1:
                $('[name=bid-up]').hide();
                break;
            case 2:
                $('[name=bid-down]').hide();
                break;
            case 3:
                $('[name=invest-up]').hide();
                break;
            case 4:
                $('[name=invest-down]').hide();
                break;
            case 5:
                $('[name=win-up]').hide();
                break;
            case 6:
                $('[name=win-down]').hide();
                break;
            case 7:
                $('[name=total_bidding-up]').hide();
                break;
            case 8:
                $('[name=total_bidding-down]').hide();
                break;
        }
    }
    
    $(document).ready(function(){
        sort_show({SORT});
        $('#agency').select2();
        $('#agency-management').select2();
        let msc_agcs = [];
        if (nv_msc_agency) {
            msc_agcs = nv_msc_agency.split(',');
        }
        $('#agency').val(msc_agcs);
        $('#agency').trigger('change');
        
        let man_agcs = [];
        if (nv_management_agency) {
            man_agcs = nv_management_agency.split(',');
        }
        $('#agency-management').val(man_agcs);
        $('#agency-management').trigger('change');
        
        onOffFields();
    });

</script>
<!-- END: main -->
