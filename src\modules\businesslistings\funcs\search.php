<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_IS_MOD_BUSINESSLISTINGS'))
    die('Stop!!!');
use NukeViet\Point\Point;
use NukeViet\Point\Url;

$is_vip_3 = false;
if (defined('NV_IS_USER')) {
    if (isset($global_array_vip[3])) {
        $arr_customs = $global_array_vip[3];
    } elseif (isset($global_array_vip[31])) {
        $arr_customs = $global_array_vip[31];
    }
    if (!empty($arr_customs)) {
        $is_vip_3 = true;
    }
}

$module_industry = $site_mods[$global_array_config['module_industry']];
if ($nv_Request->get_int('addfavorite', 'post')) {
    $name = $nv_Request->get_title('name', 'post', '');
    $postid = $nv_Request->get_title('postid', 'post', '');
    $catid = $nv_Request->get_int('catid', 'post', '');
    if ($postid == "")
        exit($nv_Lang->getModule('khongtontaidoanhnghiepnao'));
    if ($name != "") {
        $alias = change_alias($name);
        $weight = intval($db->sql_fetchrow($db->query("SELECT weight FROM " . BUSINESS_PREFIX_GLOBAL . "_favorite_cat WHERE userid=" . $user_info['userid'] . " ORDER BY weight DESC LIMIT 1")));
        $weight++;
        $query = "INSERT INTO " . BUSINESS_PREFIX_GLOBAL . "_favorite_cat (id, userid ,alias ,title, weight,numlist )
        VALUES (NULL, " . intval($user_info['userid']) . ", " . $db->dbescape($alias) . ", " . $db->dbescape($name) . "," . intval($weight) . ", 0)";

        $catid = $db->query_insert_id($query);
    }
    if ($catid > 0) {
        $postid = explode(",", $postid);
        $flag = false;

        foreach ($postid as $postid_i) {
            $numrow = $db->sql_numrows($db->query("SELECT id FROM " . BUSINESS_PREFIX_GLOBAL . "_favorite_rows WHERE catid=" . $catid . " AND postid=" . $postid_i));

            if ($numrow == 0) {

                list ($email, $allowmail) = $db->sql_fetchrow($db->query("SELECT t2.email, t1.allowmail FROM " . BUSINESS_PREFIX_GLOBAL . "_info AS t1 INNER JOIN " . NV_USERS_GLOBALTABLE . " AS t2 ON t1.userid = t2.userid WHERE t1.id=" . $postid_i));
                if ($allowmail == 1) {
                    // insert into cronjobs
                    $query = "INSERT INTO " . BUSINESS_PREFIX_GLOBAL . "_listemail (id, email, userid )
                    VALUES (NULL, " . $db->dbescape($email) . "," . intval($user_info['userid']) . ")";
                    $db->query($query);
                }

                $query = "INSERT INTO " . BUSINESS_PREFIX_GLOBAL . "_favorite_rows (id, catid ,postid ,addtime )
                VALUES (NULL, " . intval($catid) . "," . intval($postid_i) . "," . NV_CURRENTTIME . ")";
                if ($db->query($query)) {
                    $flag = true;
                    $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_favorite_cat SET numlist = numlist+1 WHERE id = " . $catid);
                }
            }
        }
        if (!$flag) {
            exit($nv_Lang->getModule('error_savedn'));
        } else {
            $sql = "SELECT id, title FROM " . BUSINESS_PREFIX_GLOBAL . "_favorite_cat WHERE userid=" . $user_info['userid'];
            $result = $db->query($sql);
            $html = "<select name=\"catid\" id=\"catid\"  class=\"text ui-widget-content ui-corner-all\">";
            $html .= "<option value=\"\">" . $nv_Lang->getModule('pleaseselect') . "</option>";
            while (list ($id, $title) = $db->sql_fetchrow($result)) {
                $sl = ($catid == $id) ? "selected=\"selected\"" : "";
                $html .= "<option value=\"" . $id . "\" " . $sl . ">" . $title . "</option>";
            }
            $html .= "</select>";
            $html .= "<label for=\"email\">" . $nv_Lang->getModule('hoac') . "</label>";
            $html .= "<input type=\"text\" name=\"name\" id=\"name\" class=\"text ui-widget-content ui-corner-all\" />";
            $html .= "<input type=\"hidden\" name=\"postid\" id=\"postid\" />";
            exit("OK|" . $html);
        }
    }
    exit();
}

// action show _industry
if ($nv_Request->isset_request('action', 'post')) {
    $code = $nv_Request->get_title('id', 'post', '');
    if ($code == "0")
        die('');
    $level = $nv_Request->get_int('level', 'post', 0);
    $selectted = $nv_Request->get_title('selectted', 'post', 0);

    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_industry['module_data'] . " WHERE level=" . $level . " AND status=1 AND code LIKE '%" . $db->dblikeescape($code) . "%'";
    $level_next = $level + 1;
    $result = $db->query($sql);
    $num = $result->rowCount();
    if ($level < 5) {
        $onchange = "getValueIndustry_block('industry" . $level . "', " . $level_next . ", 'industry" . $level_next . "', '', '');";
    }

    $html = "<select class=\"form-control m-top\" name=\"industry" . $level . "\" onchange=\"" . $onchange . "\" >";
    $html .= "<option value=\"\">" . $nv_Lang->getModule('pleaseselect') . "</option>";
    while (list ($id_i, $level_i, $code_i, $title_i, $alias_i) = $result->fetch(3)) {
        $sl = ($selectted == $code_i) ? "selected='selected'" : "";

        $html .= "<option value=\"" . $code_i . "\" " . $sl . ">" . $title_i . "</option>";
    }
    $html .= "</select>";

    include (NV_ROOTDIR . "/includes/header.php");
    echo $html;
    include (NV_ROOTDIR . "/includes/footer.php");
}

$page_title = $module_info['custom_title'];
$key_words = $module_info['keywords'];
$row['ward'] = 0;

$page = $nv_Request->get_page('page', 'get', 1);
if ($page < 1) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_wrong_page') . $btn, 'danger');

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

$per_page = $global_array_config['show_list_number'];
$base_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op;
$global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op;

// Kiểm tra xem tk có là VIP hay không
$arr_customs = [];
if (!empty($global_array_vip[89])) {
    define('NV_IS_VIP_X2', true);
    $arr_customs = $global_array_vip[89];
}

$nation = $nv_Request->get_int('nation', 'get', 0);
$nation_code = '';
if ($nation > 0) {
    $sql = "SELECT code FROM " . $db_config['prefix'] . "_msc_nation WHERE id = " . $nation;
    $nation_code = $db->query($sql)->fetchColumn() ?: '';
}
$list_compare = $nv_Request->get_title('lstcpctt', 'cookie', true);

$list_compare = json_decode($list_compare, true);
!is_array($list_compare) && $list_compare = [];

$where = "";
$keyword = $nv_Request->get_title('q', 'get', '');
$industry1 = $nv_Request->get_title('industry1', 'get', '');
$industry2 = $nv_Request->get_title('industry2', 'get', '');
$industry3 = $nv_Request->get_title('industry3', 'get', '');
$industry4 = $nv_Request->get_title('industry4', 'get', '');
$province = $nv_Request->get_int('province', 'get', -1);
$district = $nv_Request->get_int('district', 'get', 0);
$ward = $nv_Request->get_int('ward', 'get', 0);
$userid = $nv_Request->get_int('userid', 'get', 0);
$businesstype = $nv_Request->get_int('businesstype', 'get', 0);
$lvkd = $nv_Request->get_int('lvkd', 'get', 0);
$fee = $nv_Request->get_int('fee', 'get', 0);
($fee > 3 || $fee < 0) && $fee = 0;

$is_advance = $nv_Request->get_int('is_advance', 'get', 0);

// Ngày phê duyệt
$sfrom_business = nv_substr($nv_Request->get_title('sfrom_business', 'get', ''), 0, 10);
$sto_business = nv_substr($nv_Request->get_title('sto_business', 'get', ''), 0, 10);
$from = $to = 0;

if (!empty($sfrom_business) && !empty($sto_business)) {
    if (preg_match('/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/(\d{4})$/', $sfrom_business, $_arr_sfrom)) {
        $from = mktime(0, 0, 0, $_arr_sfrom[2], $_arr_sfrom[1], $_arr_sfrom[3]);
    } else {
        $t = NV_CURRENTTIME - 2592000;
        $from = mktime(00, 00, 00, nv_date('m', $t), nv_date('d', $t), nv_date('Y', $t));
        $sfrom_business = nv_date('d/m/Y', $from);
    }

    if (preg_match('/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/(\d{4})$/', $sto_business, $_arr_sto)) {
        $to = mktime(23, 59, 59, $_arr_sto[2], $_arr_sto[1], $_arr_sto[3]);
    } else {
        $to = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), nv_date('d', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME));
        $sto_business = nv_date('d/m/Y', $to);
    }
}

// Thời gian tham gia thầu
// $bid_from = '01/2010';
// $bid_to = nv_date('m/Y', NV_CURRENTTIME);
$time_bid_from = 0;
if (!empty($arr_customs)) {
    // Nếu đã mua tính năng nâng cao
    if (!empty($arr_customs['x2_adv_static_bibding']) || !empty($arr_customs['x2_adv_static_result']) || !empty($arr_customs['x2_adv_static_bidsecurity'])) {
        define('NV_IS_VIP_X2_ADVANCE', true);
        $bid_from = nv_substr($nv_Request->get_title('bid_from', 'get', $bid_from), 0, 7);
        $bid_to = nv_substr($nv_Request->get_title('bid_to', 'get', $bid_to), 0, 7);
    }
}
if (!empty($bid_from)) {
    $_arr_bid_from = explode('/', $bid_from);
    $time_bid_from = !empty($_arr_bid_from) ? mktime(0, 0, 0, $_arr_bid_from[0], 1, $_arr_bid_from[1]) : 0;
}

$base_url .= "&amp;q=" . urlencode($keyword);
$base_url .= "&amp;industry1=" . $industry1;
$base_url .= "&amp;industry2=" . $industry2;
$base_url .= "&amp;industry3=" . $industry3;
$base_url .= "&amp;industry4=" . $industry4;
$base_url .= "&amp;province=" . $province;
$base_url .= "&amp;district=" . $district;
$base_url .= "&amp;ward=" . $ward;
$base_url .= "&amp;businesstype=" . $businesstype;
$base_url .= "&amp;userid=" . $userid;
$base_url .= "&amp;is_advance=" . $is_advance;
if (!empty($nation_code)) {
    $base_url .= "&amp;nation=" . $nation;
}

$global_lang_url .= "&amp;q=" . urlencode($keyword);
$global_lang_url .= "&amp;industry1=" . $industry1;
$global_lang_url .= "&amp;industry2=" . $industry2;
$global_lang_url .= "&amp;industry3=" . $industry3;
$global_lang_url .= "&amp;industry4=" . $industry4;
$global_lang_url .= "&amp;province=" . $province;
$global_lang_url .= "&amp;district=" . $district;
$global_lang_url .= "&amp;ward=" . $ward;
$global_lang_url .= "&amp;businesstype=" . $businesstype;
$global_lang_url .= "&amp;userid=" . $userid;
$global_lang_url .= "&amp;is_advance=" . $is_advance;
if (!empty($nation_code)) {
    $global_lang_url .= "&amp;nation=" . $nation;
}

if (!empty($bid_from) && !empty($bid_to)) {
    $base_url .= "&amp;bid_from=" . $bid_from . "&amp;bid_to=" . $bid_to;
    $global_lang_url .= "&amp;bid_from=" . $bid_from . "&amp;bid_to=" . $bid_to;
}

$pop_up = '';

if (($province >= 0) || !empty($district) || !empty($ward)) {
    $nv_BotManager->setPrivate();
}

if (!empty($fee)) {
    if (!defined('NV_IS_USER')) {
        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $pop_up = sprintf($nv_Lang->getModule('block_static_login'), $link_register);
        $fee = 0;
    } else {
        // Kiểm tra xem tk có là VIP hay không
        $is_vip3_renew = false;
        if (empty($arr_customs)) {
            // Kiểm tra nếu gói VIP 3 hết hạn
            $vip3_renew = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id=' . $user_info['userid'] . ' AND status != 1 AND (vip = 3 OR vip = 31) AND prefix_lang = ' . BID_LANG_DATA)->fetch();
            if (!empty($vip3_renew)) {
                $is_vip3_renew = true;
            }
        }
        if (!$is_vip_3 && !defined('NV_IS_MODADMIN')) {
            if ($is_vip3_renew) {
                $link_vip3_renew = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&renewal=1&vip=3';
                $pop_up = sprintf($nv_Lang->getModule('block_static_vip3_renew'), $link_vip3_renew);
            } else {
                $link_vip3 = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=bidding&' . NV_OP_VARIABLE . '=vip&form=1&vip=3';
                $pop_up = sprintf($nv_Lang->getModule('block_static_vip3'), $link_vip3);
            }
            $fee = 0;
        }
    }
}

// Thêm tìm kiếm theo ngành nghề theo dkkd (industry_dkkd)
$_where = [];
$industry_seacrh = [];
$array_industry_code = array_keys($industry_list);
if ($industry3 != "") {
    foreach ($array_industry_code as $_value) {
        if (preg_match('/^(' . $industry3 . ')/', $_value)) {
            if ($industry_list[$_value]['level'] == 4) {
                $_where[] = " FIND_IN_SET(" . $db->quote($_value) . ", tb1.industry_dkkd)";
                $industry_seacrh['should'][] = [
                    'match' => [
                        'industry_dkkd' => [
                            'query' => $_value
                        ]
                    ]
                ];
            }
        }
    }
} elseif ($industry2 != "") {
    foreach ($array_industry_code as $_value) {
        if (preg_match('/^(' . $industry2 . ')/', $_value)) {
            if ($industry_list[$_value]['level'] > 2 && $industry_list[$_value]['level'] <= 4) {
                $_where[] = " FIND_IN_SET(" . $db->quote($_value) . ", tb1.industry_dkkd)";
                $industry_seacrh['should'][] = [
                    'match' => [
                        'industry_dkkd' => [
                            'query' => $_value
                        ]
                    ]
                ];
            }
        }
    }
} elseif ($industry1 != "") {
    foreach ($array_industry_code as $_value) {
        if (preg_match('/^(' . $industry1 . ')/', $_value)) {
            if ($industry_list[$_value]['level'] > 1 && $industry_list[$_value]['level'] <= 4) {
                $_where[] = " FIND_IN_SET(" . $db->quote($_value) . ", tb1.industry_dkkd)";
                $industry_seacrh['should'][] = [
                    'match' => [
                        'industry_dkkd' => [
                            'query' => $_value
                        ]
                    ]
                ];
            }
        }
    }
}

// lưu csdl các request
$array_param = [
    'keyword' => $keyword,
    'industry1' => $industry1,
    'industry2' => $industry2,
    'industry3' => $industry3,
    'industry4' => $industry4,
    'province' => $province,
    'district' => $district,
    'ward' => $ward,
    'userid' => $userid,
    'businesstype' => $businesstype,
    'lvkd' => $lvkd,
    'fee' => $fee,
    'sfrom_business' => $from,
    'sto_business' => $to,
    'bid_from' => $bid_from,
    'bid_to' => $bid_to,
    'is_advance' => $is_advance
];
$arr_time_bid = [];
if ($bid_from != '' and $bid_to != '' and $is_advance == 1) {
    $arr_bid_from = explode('/', $bid_from);
    $arr_bid_to = explode('/', $bid_to);

    $time_bid_from = !empty($arr_bid_from) ? mktime(0, 0, 0, $arr_bid_from[0], 1, $arr_bid_from[1]) : 0;
    $time_bid_to = !empty($arr_bid_to) ? mktime(0, 0, 0, $arr_bid_to[0], 31, $arr_bid_to[1]) : 0;

    for ($i = $time_bid_from; $i < $time_bid_to; $i += (86400 * 30)) {
        $month = nv_date('m', $i);
        $year = nv_date('Y', $i);
        if ($month > 0 and $year > 0) {
            $arr_time_bid[$month . $year] = $month . '-' . $year;
        }
    }
}

$arr_data = array();

$sort_type_cookie = $nv_Request->get_title('businesslistings_sort', 'cookie', 'default');
$sort_type = $nv_Request->get_title('sort', 'get', $sort_type_cookie);

if (strpos($sort_type, ',') !== false) {
    $sort_types = explode(',', $sort_type);
    $valid_sort_types = true;
    foreach ($sort_types as $single_sort_type) {
        if (!in_array($single_sort_type, $array_sort_options)) {
            $valid_sort_types = false;
            break;
        }
    }
    if (!$valid_sort_types) {
        $sort_type = 'default';
    }
} else {
    $sort_type = in_array($sort_type, $array_sort_options) ? $sort_type : 'default';
}

if ($sort_type !== $sort_type_cookie) {
    $nv_Request->set_Cookie('businesslistings_sort', $sort_type, NV_LIVE_COOKIE_TIME);
}

if ($config_bidding['elas_use']) {
    // kết nối tới ElasticSearh
    $nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($config_bidding['elas_host'], $config_bidding['elas_port'], 'dauthau_businesslistings', $config_bidding['elas_user'], $config_bidding['elas_pass']);
    $search_elastic = array();
    if ($userid > 0) {
        $search_elastic['must'][] = [
            'match' => [
                'userid' => [
                    'query' => intval($userid)
                ]
            ]
        ];
    }

    if (!empty($nation_code)) {
        if (strtoupper($nation_code) == 'VN') {
            $nation_search = [
                'should' => [
                    [
                        'match' => [
                            'tax_nation' => [
                                'query' => 'VN'
                            ]
                        ]
                    ],
                    [
                        'range' => [
                            'province' => [
                                'gt' => 0
                            ]
                        ]
                    ]
                ],
                'minimum_should_match' => 1
            ];

            $search_elastic['must'][] = [
                'bool' => $nation_search
            ];
        } else {
            $search_elastic['must'][] = [
                'match' => [
                    'tax_nation' => [
                        'query' => $nation_code
                    ]
                ]
            ];
        }
    }

    if (!empty($arr_time_bid)) {
        $time_bid_search_elastic = [];
        foreach ($arr_time_bid as $value) {
            $time_bid_search_elastic['should'][] = [
                "match_phrase" => [
                    "time_bid" => $value
                ]
            ];
        }

        if (!empty($time_bid_search_elastic)) {
            $search_elastic['must'][] = [
                'bool' => $time_bid_search_elastic
            ];
        }
    }

    if ($keyword != "") {
        // $search_elastic['should'][] = [
        // "match_phrase" => [
        // "content" => $keyword
        // ]
        // ];
        //
        $keyword = trim($keyword);

        $arr_keyword = explode(',', $keyword);
        $arr_keyword = array_filter($arr_keyword);

        if (!empty($arr_keyword)) {
            // tối đa số từ khóa theo cấu hình
            if (count($arr_keyword) > $global_array_config['max_keyword']) {
                $arr_keyword = array_slice($arr_keyword, 0, $global_array_config['max_keyword']);
            }
            foreach ($arr_keyword as $key => $value_keyword) {
                if (preg_match('/^[0-9]*?$/', $value_keyword)) { // Nếu keyword chỉ là dãy số thì tìm wildcard
                    $search_elastic['should'][] = [
                        "wildcard" => [
                            "content" => [
                                "value" => '*' . $value_keyword . '*'
                            ]
                        ]
                    ];
                } else {
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            "thong_tin_nganh_nghe" => $value_keyword
                        ]
                    ];
                    $value_keyword = str_replace('-', ' ', change_alias($value_keyword));
                    $value_keyword = trim($value_keyword);
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            "content" => $value_keyword
                        ]
                    ];
                    // Tìm thêm trường hợp ngành nghề viết không dấu
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            "thong_tin_nganh_nghe" => $value_keyword
                        ]
                    ];
                }
            }
        }
    }

    if ($industry4 != "") {
        $industry_seacrh['should'][] = [
            'match' => [
                'industry4' => [
                    'query' => $industry4
                ]
            ]
        ];
        $industry_seacrh['should'][] = [
            'match' => [
                'industry_dkkd' => [
                    'query' => $industry4
                ]
            ]
        ];
    } elseif ($industry3 != "") {
        $industry_seacrh['should'][] = [
            'match' => [
                'industry3' => [
                    'query' => $industry3
                ]
            ]
        ];
        $industry_seacrh['should'][] = [
            'match' => [
                'industry_dkkd' => [
                    'query' => $industry3
                ]
            ]
        ];
    } elseif ($industry2 != "") {
        $industry_seacrh['should'][] = [
            'match' => [
                'industry2' => [
                    'query' => $industry2
                ]
            ]
        ];
        $industry_seacrh['should'][] = [
            'match' => [
                'industry_dkkd' => [
                    'query' => $industry2
                ]
            ]
        ];
    } elseif ($industry1 != "") {
        $industry_seacrh['should'][] = [
            'match' => [
                'industry1' => [
                    'query' => $industry1
                ]
            ]
        ];
        $industry_seacrh['should'][] = [
            'match' => [
                'industry_dkkd' => [
                    'query' => $industry1
                ]
            ]
        ];
    }
    if (!empty($industry_seacrh)) {
        $search_elastic['must'][] = [
            'bool' => $industry_seacrh
        ];
    }
    if ($businesstype > 0) {
        $search_elastic['must'][] = [
            'match' => [
                'businesstype' => [
                    'query' => $businesstype
                ]
            ]
        ];
    }
    if ($ward > 0) {
        $search_elastic['must'][] = [
            'match' => [
                'ward' => [
                    'query' => $ward
                ]
            ]
        ];
    } elseif ($district > 0) {
        $search_elastic['must'][] = [
            'match' => [
                'district' => [
                    'query' => $district
                ]
            ]
        ];
    } elseif ($province > -1) {
        $search_elastic['must'][] = [
            'match' => [
                'province' => [
                    'query' => $province
                ]
            ]
        ];
    }

    if (isset($array_lvkd[$lvkd])) {
        $base_url .= "&amp;lvkd=" . $lvkd;
        $global_lang_url .= "&amp;lvkd=" . $lvkd;
        $search_elastic['must'][] = [
            "wildcard" => [
                "linh_vuc_kinh_doanh" => [
                    "value" => '*' . $lvkd . '*'
                ]
            ]
        ];
    }
    if ($fee != 0) {
        $base_url .= "&amp;fee=" . $fee;
        $global_lang_url .= "&amp;fee=" . $fee;
        $search_elastic['must'][] = [
            'match' => [
                'nop_phi_type' => [
                    'query' => $fee
                ]
            ]
        ];
    }

    // ngày phê duyệt
    if (!empty($from) && !empty($to)) {
        $base_url .= "&amp;sfrom_business=" . $sfrom_business . "&amp;sto_business=" . $sto_business;
        $global_lang_url .= "&amp;sfrom_business=" . $sfrom_business . "&amp;sto_business=" . $sto_business;
        $search_elastic['must'][] = [
            'range' => [
                'ngay_phe_duyet' => [
                    "gte" => $from,
                    "lte" => $to
                ]
            ]
        ];
    }

    if ($keyword != '') {
        $search_elastic['minimum_should_match'] = '1';
        $search_elastic['boost'] = '1.0';
    }

    $array_query_elastic = array();
    if (!empty($search_elastic)) {
        $array_query_elastic['query']['bool'] = $search_elastic;
    }
    $array_query_elastic['track_total_hits'] = 'true';
    $array_query_elastic['size'] = $per_page;
    $array_query_elastic['from'] = ($page - 1) * $per_page;
    if (strpos($sort_type, ',') !== false) {
        $sort_types = explode(',', $sort_type);
        $script_parts = [];

        foreach ($sort_types as $type) {
            if ($type == 'independent_contractor') {
                $script_parts[] = "doc['total_win_inde'].value";
            } elseif ($type == 'joint_contractor') {
                $script_parts[] = "doc['total_win_partner'].value";
            } elseif ($type == 'direct_contractor') {
                $script_parts[] = "doc['total_chidinhthau'].value";
            } elseif ($type == 'other_contractor') {
                $script_parts[] = "(doc['total_win_inde'].value + doc['total_win_partner'].value - doc['total_chidinhthau'].value)";
            }
        }

        $script = !empty($script_parts) ? implode(' + ', $script_parts) :
                  "(doc['total_win_inde'].value + doc['total_win_partner'].value) + (doc['total_chidinhthau'].value + (doc['total_win_inde'].value + doc['total_win_partner'].value - doc['total_chidinhthau'].value))";

        $array_query_elastic['sort'] = [
            [
                '_script' => [
                    'type' => 'number',
                    'script' => ['source' => $script],
                    'order' => 'desc'
                ]
            ],
            ['id' => ['order' => 'desc']]
        ];
    } elseif ($sort_type == 'num_total_desc') {
        $array_query_elastic['sort'] = [
            [
                'num_total' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'num_result_desc') {
        $array_query_elastic['sort'] = [
            [
                'num_result' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'num_false_desc') {
        $array_query_elastic['sort'] = [
            [
                'num_false' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'ability_point_desc') {
        $array_query_elastic['sort'] = [
            [
                'ability_point' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'independent_contractor') {
        $array_query_elastic['sort'] = [
            [
                'total_win_inde' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'joint_contractor') {
        $array_query_elastic['sort'] = [
            [
                'total_win_partner' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'direct_contractor') {
        $array_query_elastic['sort'] = [
            [
                'total_chidinhthau' => [
                    'order' => 'desc',
                    'missing' => '_last'
                ]
            ],
            [
                'id' => [
                    'order' => 'desc'
                ]
            ]
        ];
    } elseif ($sort_type == 'other_contractor') {
        $array_query_elastic['sort'] = [
            [
                '_script' => [
                    'type' => 'number',
                    'script' => ['source' => "doc['total_win_inde'].value + doc['total_win_partner'].value - doc['total_chidinhthau'].value"],
                    'order' => 'desc'
                ]
            ],
            ['id' => ['order' => 'desc']]
        ];
    } elseif ($sort_type == 'total_by_role') {
        $array_query_elastic['sort'] = [
            [
                '_script' => [
                    'type' => 'number',
                    'script' => ['source' => "doc['total_win_inde'].value + doc['total_win_partner'].value"],
                    'order' => 'desc'
                ]
            ],
            ['id' => ['order' => 'desc']]
        ];
    } elseif ($sort_type == 'total_by_type') {
        $array_query_elastic['sort'] = [
            [
                '_script' => [
                    'type' => 'number',
                    'script' => ['source' => "doc['total_chidinhthau'].value + (doc['total_win_inde'].value + doc['total_win_partner'].value - doc['total_chidinhthau'].value)"],
                    'order' => 'desc'
                ]
            ],
            ['id' => ['order' => 'desc']]
        ];
    } elseif ($sort_type == 'total_revenue') {
        $array_query_elastic['sort'] = [
            [
                '_script' => [
                    'type' => 'number',
                    'script' => [
                        'source' => "(doc['total_win_inde'].value + doc['total_win_partner'].value) + (doc['total_chidinhthau'].value + (doc['total_win_inde'].value + doc['total_win_partner'].value - doc['total_chidinhthau'].value))"
                    ],
                    'order' => 'desc'
                ]
            ],
            ['id' => ['order' => 'desc']]
        ];
    } elseif ($sort_type == 'default' || empty($sort_type)) {
        $array_query_elastic['sort'] = [
            [
                "_score" => [
                    "order" => "desc"
                ],
                "id" => [
                    "order" => "desc"
                ]
            ]
        ];
    }

    $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_info', $array_query_elastic);
    $all_page = $num_items = $response['hits']['total']['value'];
    // xác định điểm và số dn trên mỗi block
    $total_point_download = 0;
    $config_bidding = $module_config['bidding'];
    $num_point_block = $config_bidding['num_point_block_notx2'];
    $num_business_block = $config_bidding['num_business_block_notx2'];
    if (defined('NV_IS_USER')) {
        if (defined('NV_IS_VIP_X2')) {
            $num_point_block = $config_bidding['num_point_block_x2'];
            $num_business_block = $config_bidding['num_business_block_x2'];
        }
    }
    $total_point_download = ceil($num_items/$num_business_block) * $num_point_block;
    require_once NV_ROOTDIR . '/modules/businesslistings/handle_download/handle_download.php';
    // nếu page > 100 thì thông báo
    if ($num_items > 0 && $page > 100) {
        $nv_BotManager->setPrivate();
        $pages_businesslistings = nv_generate_page($base_url, $all_page, $per_page, $page);

        $page_url = $base_url;

        if ($page > 1) {
            $page_url .= '&amp;page=' . $page;
        }
        if (empty($keyword) && empty($industry1) && empty($industry2) && empty($industry3) && empty($industry4) && empty($province) && empty($district) && empty($ward) && empty($businesstype) && empty($userid) && empty($fee) && !isset($array_lvkd[$lvkd])) {
            $canonicalUrl = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name, true);
            if ($page > 1) {
                $canonicalUrl .= 'page-' . $page;
            }
        } else {
            $canonicalUrl = getCanonicalUrl($page_url);
        }

        $urlappend = '&amp;page=';
        betweenURLs($page, ceil($num_items / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

        $contents = nv_theme_businesslistings_listbusinesslistings([], $nv_Lang->getModule('thongtintimiem'), $pages_businesslistings, $all_page, $num_items, $total_point_download);

        include NV_ROOTDIR . '/includes/header.php';
        echo nv_site_theme($contents);
        include NV_ROOTDIR . '/includes/footer.php';
    }
    foreach ($response['hits']['hits'] as $value) {
        if (!empty($value['_source'])) {
            $view = $value['_source'];
            if (in_array($view['id'], $list_compare)) {
                $view['is_added_cmp'] = 1;
            } else {
                $view['is_added_cmp'] = 0;
            }
            $view['checkremvcp'] = md5(NV_CHECK_SESSION . '_checkremvcp_' . $view['id']);
            $view['checkaddcp'] = md5(NV_CHECK_SESSION . '_checkaddcp_' . $view['id']);
            $arr_data[$view['id']] = $view;
        }
    }
} else {
    if ($userid > 0) {
        $where .= " AND tb1.userid = " . intval($userid);
    }

    if (!empty($nation_code)) {
        if ($nation == 244) {
            $where .= " AND (tb2.tax_nation = 'VN' OR tb1.province > 0)";
        } else {
            $where .= " AND tb2.tax_nation = " . $db->quote($nation_code);
        }
    }

    if (!empty($arr_time_bid)) {
        $time_bid_search_elastic = [];
        foreach ($arr_time_bid as $value) {
            $time_bid_search_elastic[] = "tb1.time_bid LIKE " . $db->quote('%' . $value . '%') . "";
        }

        if (!empty($time_bid_search_elastic)) {
            $where .= " AND (" . implode(' OR ', $time_bid_search_elastic) . ")";
        }
    }

    $arr_search_keyword = [];
    if ($keyword != "") {
        $keyword = trim($keyword);

        $arr_keyword = explode(',', $keyword);
        $arr_keyword = array_filter($arr_keyword);

        if (!empty($arr_keyword)) {
            // tối đa số từ khóa theo cấu hình
            if (count($arr_keyword) > $global_array_config['max_keyword']) {
                $arr_keyword = array_slice($arr_keyword, 0, $global_array_config['max_keyword']);
            }

            foreach ($arr_keyword as $key => $value_keyword) {
                $dblike = $db->dblikeescape($value_keyword);
                $arr_search_keyword[] = "tb1.companyname LIKE '%" . $value_keyword . "%'
                OR tb1.officialname LIKE '%" . $value_keyword . "%'
                OR tb1.short LIKE '%" . $value_keyword . "%'
                OR tb1.representative LIKE '%" . $value_keyword . "%'
                OR tb1.about LIKE '%" . $value_keyword . "%'
                OR tb1.code LIKE '%" . $value_keyword . "%'
                OR tb1.content LIKE '%" . $value_keyword . "%'
                OR tb1.phone LIKE '%" . $value_keyword . "%'
                OR tb2.thong_tin_nganh_nghe LIKE '%" . $value_keyword . "%'";
            }
            if (!empty($arr_search_keyword)) {
                $where .= " AND (";
                $where .= implode(" OR ", $arr_search_keyword);
                $where .= ")";
            }
        }
    }
    if ($industry4 != "") {
        $where .= " AND (FIND_IN_SET(" . $db->quote($industry4) . ", tb1.industry4) OR FIND_IN_SET(" . $db->quote($industry4) . ", tb1.industry_dkkd))";
    } elseif ($industry3 != "") {
        $where .= " AND (FIND_IN_SET(" . $db->quote($industry3) . ", tb1.industry3) OR FIND_IN_SET(" . $db->quote($industry3) . ", tb1.industry_dkkd)" . (!empty($_where) ? ' OR ' . implode(' OR ', $_where) : '') . ')';
    } elseif ($industry2 != "") {
        $where .= " AND (FIND_IN_SET(" . $db->quote($industry2) . ", tb1.industry2) OR FIND_IN_SET(" . $db->quote($industry2) . ", tb1.industry_dkkd)" . (!empty($_where) ? ' OR ' . implode(' OR ', $_where) : '') . ')';
    } elseif ($industry1 != "") {
        $where .= " AND (FIND_IN_SET(" . $db->quote($industry1) . ", tb1.industry1) OR FIND_IN_SET(" . $db->quote($industry1) . ", tb1.industry_dkkd)" . (!empty($_where) ? ' OR ' . implode(' OR ', $_where) : '') . ')';
    }

    if ($ward > 0) {
        $where .= " AND tb1.ward = " . intval($ward) . "";
    } elseif ($district > 0) {
        $where .= " AND tb1.district = " . intval($district) . "";
    } elseif ($province >= 0) {
        $where .= " AND tb1.province = " . intval($province) . "";
    }
    if (isset($array_lvkd[$lvkd])) {
        $base_url .= "&amp;lvkd=" . $lvkd;
        $global_lang_url .= "&amp;lvkd=" . $lvkd;
        $where .= " AND tb2.linh_vuc_kinh_doanh LIKE '%" . $db->dblikeescape($lvkd) . "%'";
    }
    if ($fee != 0) {
        $base_url .= "&amp;fee=" . $fee;
        $global_lang_url .= "&amp;fee=" . $fee;
        $where .= " AND tb2.nop_phi_type =" . $fee;
    }
    // Ngày phê duyệt
    if (!empty($from) && !empty($to)) {
        $base_url .= "&amp;sfrom_business=" . $sfrom_business . "&amp;sto_business=" . $sto_business;
        $global_lang_url .= "&amp;sfrom_business=" . $sfrom_business . "&amp;sto_business=" . $sto_business;
        $where .= " AND tb2.ngay_phe_duyet >=" . $from . " AND tb2.ngay_phe_duyet <=" . $to;
    }
    if ($businesstype > 0) {
        $where .= " AND tb1.businesstype =" . $businesstype;
    }
    $order_by = "";
    if (strpos($sort_type, ',') !== false) {
        $sort_types = explode(',', $sort_type);
        $sql_order_parts = [];

        foreach ($sort_types as $type) {
            if ($type == 'independent_contractor') {
                $sql_order_parts[] = "tb1.total_win_inde DESC";
            } elseif ($type == 'joint_contractor') {
                $sql_order_parts[] = "tb1.total_win_partner DESC";
            } elseif ($type == 'direct_contractor') {
                $sql_order_parts[] = "tb1.total_chidinhthau DESC";
            } elseif ($type == 'other_contractor') {
                $sql_order_parts[] = "(tb1.total_win_inde + tb1.total_win_partner - tb1.total_chidinhthau) DESC";
            }
        }

        if (!empty($sql_order_parts)) {
            $order_by = "ORDER BY " . implode(', ', $sql_order_parts) . ", tb1.id DESC";
        }
    } elseif ($sort_type == 'num_total_desc') {
        $order_by = "ORDER BY tb1.num_total DESC, tb1.id DESC";
    } elseif ($sort_type == 'num_result_desc') {
        $order_by = "ORDER BY tb1.num_result DESC, tb1.id DESC";
    } elseif ($sort_type == 'num_false_desc') {
        $order_by = "ORDER BY tb1.num_false DESC, tb1.id DESC";
    } elseif ($sort_type == 'ability_point_desc') {
        $order_by = "ORDER BY tb1.ability_point DESC, tb1.id DESC";
    } elseif ($sort_type == 'independent_contractor') {
        $order_by = "ORDER BY tb1.total_win_inde DESC, tb1.id DESC";
    } elseif ($sort_type == 'joint_contractor') {
        $order_by = "ORDER BY tb1.total_win_partner DESC, tb1.id DESC";
    } elseif ($sort_type == 'direct_contractor') {
        $order_by = "ORDER BY tb1.total_chidinhthau DESC, tb1.id DESC";
    } elseif ($sort_type == 'other_contractor') {
        $order_by = "ORDER BY (tb1.total_win_inde + tb1.total_win_partner - tb1.total_chidinhthau) DESC, tb1.id DESC";
    } elseif ($sort_type == 'total_by_role') {
        $order_by = "ORDER BY (tb1.total_win_inde + tb1.total_win_partner) DESC, tb1.id DESC";
    } elseif ($sort_type == 'total_by_type') {
        $order_by = "ORDER BY (tb1.total_chidinhthau + (tb1.total_win_inde + tb1.total_win_partner - tb1.total_chidinhthau)) DESC, tb1.id DESC";
    } elseif ($sort_type == 'total_revenue') {
        $order_by = "ORDER BY ((tb1.total_win_inde + tb1.total_win_partner) + (tb1.total_chidinhthau + (tb1.total_win_inde + tb1.total_win_partner - tb1.total_chidinhthau))) DESC, tb1.id DESC";
    } else {
        $order_by = "ORDER BY tb1.id DESC";
    }

    $sql = "SELECT SQL_CALC_FOUND_ROWS tb1.*, tb2.tax_nation FROM " . BUSINESS_PREFIX_GLOBAL . "_info tb1 LEFT JOIN " . BUSINESS_PREFIX_GLOBAL . "_addinfo tb2 ON tb1.id=tb2.id WHERE tb1.active = 1 " . $where . " " . $order_by . " LIMIT " . (($page - 1) * $per_page) . ", " . $per_page;

    $result = $db->query($sql);
    $result_page = $db->query("SELECT FOUND_ROWS()");
    list ($numf) = $result_page->fetch(3);
    $all_page = ($numf) ? $numf : 1;
    $num_items = $numf;

    // xác định điểm và số dn trên mỗi block
    $total_point_download = 0;
    $config_bidding = $module_config['bidding'];
    $num_point_block = $config_bidding['num_point_block_notx2'];
    $num_business_block = $config_bidding['num_business_block_notx2'];
    if (defined('NV_IS_USER')) {
        if (defined('NV_IS_VIP_X2')) {
            $num_point_block = $config_bidding['num_point_block_x2'];
            $num_business_block = $config_bidding['num_business_block_x2'];
        }
    }
    $total_point_download = ceil($num_items/$num_business_block) * $num_point_block;
    require_once NV_ROOTDIR . '/modules/businesslistings/handle_download/handle_download.php';
    // nếu page > 100 thì thông báo
    if ($num_items > 0 && $page > 100) {
        $nv_BotManager->setPrivate();

        $pages_businesslistings = nv_generate_page($base_url, $all_page, $per_page, $page);

        $page_url = $base_url;

        if ($page > 1) {
            $page_url .= '&amp;page=' . $page;
        }
        if (empty($keyword) && empty($industry1) && empty($industry2) && empty($industry3) && empty($industry4) && empty($province) && empty($district) && empty($ward) && empty($businesstype) && empty($userid) && empty($fee) && !isset($array_lvkd[$lvkd])) {
            $canonicalUrl = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name, true);
            if ($page > 1) {
                $canonicalUrl .= 'page-' . $page;
            }
        } else {
            $canonicalUrl = getCanonicalUrl($page_url);
        }

        $urlappend = '&amp;page=';
        betweenURLs($page, ceil($num_items / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

        $contents = nv_theme_businesslistings_listbusinesslistings([], $nv_Lang->getModule('thongtintimiem'), $pages_businesslistings, $all_page, $num_items, $total_point_download);

        include NV_ROOTDIR . '/includes/header.php';
        echo nv_site_theme($contents);
        include NV_ROOTDIR . '/includes/footer.php';
    }

    while ($_row = $result->fetch()) {
        if (in_array($_row['id'], $list_compare)) {
            $_row['is_added_cmp'] = 1;
        } else {
            $_row['is_added_cmp'] = 0;
        }
        $_row['checkremvcp'] = md5(NV_CHECK_SESSION . '_checkremvcp_' . $_row['id']);
        $_row['checkaddcp'] = md5(NV_CHECK_SESSION . '_checkaddcp_' . $_row['id']);
        $arr_data[$_row['id']] = $_row;
    }
}

// Kiểm tra từ khoá có phải orgcode không để thêm vào bảng nv22_nhathau_url
if (defined('NV_IS_USER')) {
    if (!empty($keyword) && empty($arr_data)) {
        if (preg_match('/^vn([0-9]{10})$/i', $keyword, $m)) {
            if (taxcodecheck2($m[1]) != false) {
                check_and_insert_orgcode_businesslistings($keyword);
            }
        } elseif (preg_match('/^vnz([0-9]{9})$/i', $keyword)) {
            if (defined('NV_IS_ADMIN')) {
                check_and_insert_orgcode_businesslistings($keyword);
            }
        }
    }
}

// Nation (Quốc gia)
$sql = "SELECT id, name, alias, code FROM " . $db_config['prefix'] . "_msc_nation";
$array_nation = $nv_Cache->db($sql, 'code', $module_name);

// Province (Tỉnh/Thành phố)
$sql_province = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province";
$province_list = $nv_Cache->db($sql_province, 'id', 'location');
$province_list[-1] = array(
    'id' => -1,
    'title' => $nv_Lang->getModule('undefined'),
    'alias' => strtolower(change_alias($nv_Lang->getModule('undefined')))
);

// District (Quận/Huyện)
$sql_district = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_district";
$district_list = $nv_Cache->db($sql_district, 'id', 'location');
$district_list[-1] = array(
    'id' => -1,
    'title' => $nv_Lang->getModule('undefined'),
    'alias' => strtolower(change_alias($nv_Lang->getModule('undefined')))
);

// Ward (Phường/Xã)
$sql_ward = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_ward";
$ward_list = $nv_Cache->db($sql_ward, 'id', 'location');
$ward_list[-1] = array(
    'id' => -1,
    'title' => $nv_Lang->getModule('undefined'),
    'alias' => strtolower(change_alias($nv_Lang->getModule('undefined')))
);

$pages_businesslistings = nv_generate_page($base_url, $all_page, $per_page, $page);

$page_url = $base_url;

if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}
if (empty($keyword) && empty($industry1) && empty($industry2) && empty($industry3) && empty($industry4) && empty($province) && empty($district) && empty($ward) && empty($businesstype) && empty($userid) && empty($fee) && !isset($array_lvkd[$lvkd])) {
    $canonicalUrl = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name, true);
    if ($page > 1) {
        $canonicalUrl .= 'page-' . $page;
    }
} else {
    $canonicalUrl = getCanonicalUrl($page_url);
}

$urlappend = '&amp;page=';
betweenURLs($page, ceil($num_items / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

$arr_content = array();
$link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=detail";
foreach ($arr_data as $row) {
    $row['addressfull'] = array();
    if ($row['address'] != "") {
        $row['addressfull'][] = handle_address_ward($row['address'], $row['ward']);
    }

    if (isset($ward_list[$row['ward']])) {
        $row['addressfull'][] = $ward_list[$row['ward']]['title'];
    }
    if (isset($district_list[$row['district']])) {
        $row['addressfull'][] = $district_list[$row['district']]['title'];
    }
    $row['addressfull'] = implode(', ', $row['addressfull']);
    $row['addressfull'] = str_replace(',,', ',', $row['addressfull']);
    if (empty($row['addressfull'])) {
        $row['addressfull'] = '';
    }
    if (isset($province_list[$row['province']])) {
        $row['location'] = $province_list[$row['province']]['title'];
        $row['link_location'] = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=businesslistings&amp;" . NV_OP_VARIABLE . "=listlocation/T-" . $province_list[$row['province']]['alias'] . '-' . $province_list[$row['province']]['id'];
    } else {
        $row['location'] = '';
        $row['link_location'] = '';
    }

    if (isset($array_nation[$row['tax_nation']])) {
        $nation = $array_nation[$row['tax_nation']];
        $row['nation'] = $nation['name'];
        $row['link_nation'] = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $module_info['alias']['listnation'] . "/" . $nation['alias'];
    } else {
        $row['nation'] = '';
        $row['link_nation'] = '';
    }

    if (!empty($row['website']) and !preg_match('/^http[s]*\:\/\//i', $row['website'])) {
        $row['website'] = 'http://' . $row['website'];
    }
    $row['website'] = ($row['website'] != "") ? "<a target='_blank' href='" . $row['website'] . "'>" . $row['website'] . "</a>" : '';
    (NV_LANG_DATA != 'vi' && !empty(trim($row['officialname']))) && $row['companyname'] = $row['officialname'];
    $row['link'] = $link . "/" . change_alias($row['companyname']) . "-" . $row['id'];
    if ($row['logo'] != "") {
        $row['logo'] = NV_BASE_SITEURL . NV_UPLOADS_DIR . "/" . $module_upload . "/" . $row['logo'];
    } else {
        $row['logo'] = NV_BASE_SITEURL . "themes/" . $module_info['template'] . "/images/" . $module_file . "/noimages.png";
    }
    $row['chartercapital'] = ConvertPriceTextSort($row['chartercapital']);
    $row['businesstype_text'] = isset($global_array_businesstype[$row['businesstype']]) ? $global_array_businesstype[$row['businesstype']]['title'] : '';
    $row['businesstype_link'] = isset($global_array_businesstype[$row['businesstype']]) ? $global_array_businesstype[$row['businesstype']]['title'] : '';
    $row['phone'] = !empty($row['phone']) ? $row['phone'] : '';
    $row['fax'] = !empty($row['fax']) ? $row['fax'] : '';
    $row['represent_phone'] = !empty($row['represent_phone']) ? $row['represent_phone'] : '';
    $row['total_other'] = $row['total_win_inde'] + $row['total_win_partner'] - $row['total_chidinhthau'];
    $row['total_other'] = intval($row['total_win_inde']) + intval($row['total_win_partner']) - intval($row['total_chidinhthau']);
    $row['total_by_type'] = intval($row['total_chidinhthau']) + intval($row['total_other']);
    $row['total_by_role'] = intval($row['total_win_inde']) + intval($row['total_win_partner']);
    $row['total_revenue'] = intval($row['total_by_role']) + intval($row['total_by_type']);
    $arr_content[] = $row;
}

$array_cat_favorite = array();
if (defined('NV_IS_USER')) {
    $sql = "SELECT id, title FROM " . BUSINESS_PREFIX_GLOBAL . "_favorite_cat WHERE userid =" . $user_info['userid'];
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $array_cat_favorite[] = $row;
    }
}

$cache_numberRow = NV_LANG_DATA . '_' . $module_info['template'] . '_number_search_results_' . NV_CACHE_PREFIX . '.cache';
$timeCache = 60;
if ($cache = $nv_Cache->getItem($module_name, $cache_numberRow, $timeCache) != false) {
    $count_item = $nv_Cache->getItem($module_name, $cache_numberRow, $timeCache);
} else {
    // $count_item = $db->query("SELECT count(tb1.code) as tongso FROM " . NV_PREFIXLANG . "_" . $module_data . "_info tb1 LEFT JOIN " . NV_PREFIXLANG . "_" . $module_data . "_addinfo tb2 ON tb1.id=tb2.id WHERE tb1.active = 1")->fetch()['tongso'];

    // Do tất cả các dữ liệu đều active, nên chấp nhận sai số
    $count_item = $db->query("SELECT count(code) FROM " . BUSINESS_PREFIX_GLOBAL . "_info")->fetchColumn();
    $nv_Cache->setItem($module_name, $cache_numberRow, $count_item, $timeCache);
}

// #2415: Thêm hằng chặn index
$query_params = [];
parse_str(parse_url($page_url, PHP_URL_QUERY), $query_params);
// Danh sách các tham số cần xóa
$params_to_remove = [
    'amp;' . NV_NAME_VARIABLE,
    'amp;' . NV_OP_VARIABLE,
    NV_LANG_VARIABLE
];
$query_params = array_diff_key($query_params, array_flip($params_to_remove));
$has_other_query_params = false;
foreach ($query_params as $key => $value) {
    if ($key !== 'amp;page') {
        $has_other_query_params = true;
        break;
    }
}
if (!empty($keyword) || empty($arr_content) || $has_other_query_params || !empty($nation_code)) {
    $nv_BotManager->setFollow()->setNoIndex();
}

$contents = nv_theme_businesslistings_listbusinesslistings($arr_content, $nv_Lang->getModule('thongtintimiem'), $pages_businesslistings, $all_page, $num_items, $total_point_download, $array_cat_favorite, $keyword, $count_item, $pop_up);

include (NV_ROOTDIR . "/includes/header.php");
echo nv_site_theme($contents);
include (NV_ROOTDIR . "/includes/footer.php");

function check_and_insert_orgcode_businesslistings($orgcode)
{
    global $cr_config;
    $dbcr = connect_dbcr();
    // Kiểm tra
    $check_org = $dbcr->query('SELECT id FROM nv22_nhathau_url WHERE orgcode = ' . $dbcr->quote($orgcode))
        ->fetchColumn();
    if (empty($check_org)) {
        // Thêm vào bảng nv22_nhathau_url để quét
        $prepared = $dbcr->prepare("INSERT INTO `nv22_nhathau_url` (`orgcode`) VALUES (:orgcode)");
        try {
            $prepared->bindParam(':orgcode', $orgcode, PDO::PARAM_STR);
            $exc = $prepared->execute();
        } catch (PDOException $e) {
            trigger_error($e);
            return false;
        }
    }
    return true;
}
