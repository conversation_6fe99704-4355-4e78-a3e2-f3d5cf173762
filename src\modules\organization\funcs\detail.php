<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 02:25:16 GMT
 */

use NukeViet\Dauthau\Share;

if (!defined('NV_IS_MOD_ORGANIZATION')) {
    die('Stop!!!');
}

if (empty($id)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
}
$db->sqlreset()
   ->select('*')
   ->from($db_config['prefix'] . '_tochuc')
   ->where('id = ' . $id);
$sth = $db->prepare($db->sql());
$sth->execute();
$data_detail_tochuc = $sth->fetch();
$_data_tochuc = $data_detail_tochuc;

if (!empty($_data_tochuc['ma_chung_chi'])) {
    $_org_duplicate = $db->query("SELECT * FROM " . $db_config['prefix'] . "_tochuc WHERE ma_chung_chi = " . $_data_tochuc['ma_chung_chi'] . " AND id != " . $id)->fetch();
}

// gọi hàm xử lý submit crawl để lấy text confirm
Share::submitConfirmCrawl();

if ($nv_Request->isset_request('update', 'post') and defined('NV_IS_USER')) {
    $check = $nv_Request->get_title('check', 'post, get', '');
    $info = $nv_Lang->getModule('update_err');
    if ($id > 0 and $user_info['userid'] > 0) {
        $last = $db->query("SELECT * FROM " . BID_PREFIX_GLOBAL . "_update_user WHERE userid = " . $user_info['userid'] . " AND to_chuc_id= " . $id)->fetch();
        if (!empty($last) and (NV_CURRENTTIME - $last['last_reload'] <= 600)) {
            $info = sprintf($nv_Lang->getModule('update_err_user_last'), nv_convertfromSec(600 - (NV_CURRENTTIME - $last['last_reload'])));
        } else {
            if (NV_CURRENTTIME - $data_detail_tochuc['thoi_gian_boc'] >= 1800) {
                $dbcr = connect_dbcr();
                try {
                    $_result = $dbcr->query("SELECT * FROM " . $db_config['prefix'] . '_tochuc_url' . " WHERE id_url = " . $dbcr->quote($data_detail_tochuc['id_url']))->fetch();
                    if (!empty($_result)) {
                        if ($_result['trang_thai'] == 1 and (NV_CURRENTTIME - $data_detail_tochuc['thoi_gian_boc'] >= 600)) {
                            $exec = $dbcr->exec("UPDATE " . $db_config['prefix'] . '_tochuc_url' . " SET trang_thai = 0 WHERE id_url = " . $_result['id_url']);
                            if ($exec) {
                                $info = $nv_Lang->getModule('update_ok');
                            } else {
                                $info = $nv_Lang->getModule('update_err_new');
                            }
                        } else {
                            $info = $nv_Lang->getModule('update_err_new');
                        }
                    } else {
                        $query = 'INSERT INTO ' . $db_config['prefix'] . '_tochuc_url' . ' (id_url, url_boc_tin, trang_thai, thoi_gian_lay_url) VALUES (' . $dbcr->quote($data_detail_tochuc['id_url']) . ', ' . $dbcr->quote('https://nangluchdxd.gov.vn/Tochuc/chitiet/' . $data_detail_tochuc['id_url']) . ', 0, ' . NV_CURRENTTIME . ')';
                        $_id = $dbcr->query($query);
                        if ($_id) {
                            $info = $nv_Lang->getModule('update_ok');
                        }
                    }
                    if (! empty($last)) {
                        $exec = $db->exec("UPDATE " . BID_PREFIX_GLOBAL . "_update_user SET last_reload='" . NV_CURRENTTIME . "' WHERE userid=" . $user_info['userid'] . " AND to_chuc_id= " . $id);
                    } else {
                        $exec = $db->exec("INSERT INTO " . BID_PREFIX_GLOBAL . "_update_user (userid, to_chuc_id, last_reload) VALUES (" . $user_info['userid'] . ", " . $id . ", " . NV_CURRENTTIME . ")");
                    }
                } catch (PDOException $e) {
                    trigger_error($e);
                    die();
                }
            } else {
                $info = $nv_Lang->getModule('update_err_new');
            }
        }
    }
    die($info);
}

$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;
$base_url .= '&amp;' . NV_OP_VARIABLE . '=' . strtolower(change_alias($data_detail_tochuc['ten_to_chuc'])) . '-' . $data_detail_tochuc['id'] . $global_config['rewrite_exturl'];
$global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;
$global_lang_url .= '&amp;' . NV_OP_VARIABLE . '=' . strtolower(change_alias($data_detail_tochuc['ten_to_chuc'])) . '-' . $data_detail_tochuc['id'] . $global_config['rewrite_exturl'];
$page_url = $base_url;

$db->sqlreset()
    ->select('id, title')
    ->from(NV_PREFIXLANG . '_' . 'location_province')
    ->order('title ASC');
$array_data['province'] = $nv_Cache->db($db->sql(), '', 'location');
$location_check = [];
foreach($array_data['province'] as $value) {
    $location_check[$value['id']] = $value['title'];
}

// district
$sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_district";
$district_list = $nv_Cache->db($sql, 'id', 'location');

if (empty($data_detail_tochuc)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
}
// Đếm số lượt xem
$sesion_name = str_replace("-", "", $module_data . '_' . $op . '_' . $id);
if (!$nv_Request->isset_request($sesion_name, 'session')) {
    $nv_Request->set_Session($sesion_name, NV_CURRENTTIME);
    /*
    $cur_month = intval(date('n', NV_CURRENTTIME));
    if (!empty($data_detail_tochuc['month_view'])) {
        $data_detail_tochuc['month_view'] = explode('.', $data_detail_tochuc['month_view']);
        if ($data_detail_tochuc['month_view'][0] != $cur_month) {
            $data_detail_tochuc['month_view'] = [$cur_month, 1];
        } else {
            $data_detail_tochuc['month_view'][1]++;
        }
    } else {
        $data_detail_tochuc['month_view'] = [$cur_month, 1];
    }
    $db->query("UPDATE " . $db_config['prefix'] . "_tochuc SET totalview=totalview+1, month_view=" . $db->quote(implode('.', $data_detail_tochuc['month_view'])) . " WHERE id=" . $id);
    */
    file_put_contents(NV_ROOTDIR . '/data/setview/' . gmdate('Ymd', NV_CURRENTTIME) . '.' . $db_config['prefix'] . '_tochuc.log', gmdate('H:i:s', NV_CURRENTTIME) . "\t" . $id . "\n", FILE_APPEND);
}
if (!empty($data_detail_tochuc['ma_so_thue'])) {
    // kiểm tra xem tổ chức xây dựng có phải là bên mời thầu k bằng mã số thuế
    $solicitor = $db->query("SELECT * FROM " . BID_PREFIX_GLOBAL . "_solicitor_detail as tb1 INNER JOIN " . BID_PREFIX_GLOBAL . "_solicitor as tb2 ON tb1.id = tb2.id WHERE tb1.no_business_licence = " . $db->quote($data_detail_tochuc['ma_so_thue']) . '')->fetch();

    // kiểm tra xem tổ chức xây dựng có phải là bên nhà thầu không bằng mã số thuế
    $contractor = $db->query("SELECT * FROM " . BUSINESS_PREFIX_GLOBAL . "_info WHERE active = 1 AND code = " . $db->quote($data_detail_tochuc['ma_so_thue']))->fetch();
}

$db->sqlreset()
   ->select('*')
   ->order('stt ASC')
   ->from($db_config['prefix'] . '_tochuc_linhvuc')
   ->where('to_chuc_id = ' . $id);
$sth = $db->prepare($db->sql());
$sth->execute();
$data_detail_linh_vuc = $sth->fetchAll();

$xtpl = new XTemplate('detail.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$_userid = defined('NV_IS_USER') ? intval($user_info['userid']) : 0;
$xtpl->assign('CHECKSESS_UPDATE', md5($_userid . $data_detail_tochuc['id'] . NV_CACHE_PREFIX . $client_info['session_id']));
$xtpl->assign('URL_UPDATE', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . strtolower(change_alias($data_detail_tochuc['ten_to_chuc'])) . '-' . $data_detail_tochuc['id'] . $global_config['rewrite_exturl'], true));
$xtpl->assign('UPDATE_INFO', ($data_detail_tochuc['so_lan_boc_tin'] == 1) ? $nv_Lang->getModule('cap_nhat_lan_dau') : sprintf($nv_Lang->getModule('cap_nhat_lan_thu'), num_of_time($data_detail_tochuc['so_lan_boc_tin']), nv_date('H:i d/m/Y', $data_detail_tochuc['update_time'])));

if (!defined("NV_IS_USER")) {
    $link_login = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=login&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
    $link_register = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=register&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
    $link = vsprintf($nv_Lang->getModule('log_in_up'), array(
        $link_login,
        $link_register
    ));
    $xtpl->assign('LOGIN', $link);
    $xtpl->parse('main.login');
}
foreach ($data_detail_linh_vuc as $val) {
    $val['ngay_het_han'] = !empty($val['ngay_het_han']) ? nv_date('d/m/Y', $val['ngay_het_han']) : '';
    $xtpl->assign('LINHVUC', $val);
    $xtpl->parse('main.loop');
}

$data_detail_tochuc['link_province'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=listlocation/' . change_alias($data_detail_tochuc['tinh']);
$data_detail_tochuc['link_dvkd'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=censorship/' . change_alias($data_detail_tochuc['text_tinh']);
$data_detail_tochuc['ngay_cap'] = !empty($data_detail_tochuc['ngay_cap']) ? nv_date('d/m/Y', $data_detail_tochuc['ngay_cap']) : '';
$data_detail_tochuc['thoi_gian_boc'] = nv_date('H:i d/m/Y', $data_detail_tochuc['thoi_gian_boc']);

if (defined('NV_IS_USER')) {
    if (!empty($arr_customs_permission) || !empty($global_array_vip)) {
        define('NV_IS_VIP', true);
    }
}

//Nếu là bên mời thầu
if (!empty($solicitor)) {
    // alias đa ngôn ngữ
    $solicitor['english_name'] = !empty($solicitor['english_name']) ? trim($solicitor['english_name']) : '';
    if (NV_LANG_DATA != 'vi' && !empty($solicitor['english_name'])) {
        $solicitor['alias'] = change_alias($solicitor['english_name']);
        $solicitor['title'] = $solicitor['english_name'];
    }
    $url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['solicitor'] . '/' . $solicitor['alias'] . '-' . $solicitor['id']);
    $xtpl->assign('IS_SOCLICTOR', sprintf($nv_Lang->getModule('is_solicitor'), $url, $solicitor['title'], $solicitor['num_khlcnt'], $solicitor['num_contract'], $solicitor['num_total'], $solicitor['num_tbmt'], $solicitor['num_tbmst'], $solicitor['num_result'], $solicitor['num_cancel'], $solicitor['num_notcode']));
    $xtpl->parse('main.solicitor');
}

//Nếu là nhà thầu
if (!empty($contractor)) {
    $contractor['companyname'] = NV_LANG_DATA == 'vi' ? $contractor['companyname'] : (trim($contractor['officialname'] ?? '') ?: $contractor['companyname']);
    $url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($contractor['companyname']) . '-' . $contractor['id'], true);
    // $request = [
    //     'id' => $contractor['id']
    // ];
    // $json = nv_local_api('GetDataContractor', $request, 'admin', 'businesslistings');
    // $_data = json_decode($json, true);
    // if($_data['status'] == 'success'){
    //     $contractor_static = $_data['main'];
    // }
    $filedir = NV_ROOTDIR . '/data/business-cache/';
    if (!is_dir($filedir)) {
        mkdir($filedir);
    }

    $cache_file = 'detail_business_' . $contractor['code'] . '_' . NV_LANG_DATA . '-vip.cache';
    if (file_exists($filedir . $cache_file)) {
        $contents_business_info = file_get_contents($filedir . $cache_file);
    } else {
        $request = [
            'id' => $contractor['id'],
        ];
        $_data = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'GetDataContractor', $request, 'businesslistings', 1);
        if ($_data['status'] == 'success') {
            $contractor['content_static'] = $_data['main'];
            $contractor['content_static'] = str_replace([
                '#openalias#',
                '#viewopenalias#',
                '#resultalias#',
                '#viewalias#',
                '#nv_base_siteurl#',
                '#nv_lang_data#',
                '#lang_companyname_alias#'
            ], [
                $site_mods['bidding']['alias']['open'],
                $site_mods['bidding']['alias']['viewopen'],
                $site_mods['bidding']['alias']['result'],
                $site_mods['bidding']['alias']['view'],
                NV_BASE_SITEURL,
                NV_LANG_DATA,
                change_alias($contractor['companyname'])
            ], $contractor['content_static']);
        } else {
            $contents_business_info = '';
        }
    }
    if (!empty($contents_business_info) && preg_match('/<div\sclass\=\"static\"\>(.*?)\<\/div\>/is', $contents_business_info, $m)) {
        if (isset($m[1])) {
            if (preg_match('/\<blockquote\sclass\=list\>(.*?)\<\/blockquote\>/is', $m[1], $_m)) {
                if (isset($_m[0])) {
                    $contractor['content_static'] = preg_replace('/(\<a href=\"\#.*?\>)/i','',$_m[0]);
                    $contractor['content_static'] = preg_replace('/\<\/a\>/','',$contractor['content_static']);
                    $contractor['content_static'] = str_replace('</strong>', '</strong></a>', $contractor['content_static']);
                    $contractor['content_static'] = str_replace('), <a', ')</a>, <a', $contractor['content_static']);
                    $contractor['content_static'] = preg_replace('/(\d)(\))(\<\/li\>)/','$1$2</a>$3',$contractor['content_static']);
                }
            }
        }
    }
    if (!empty($contractor['content_static'])) {
        $lock_symbol = '<i class="fa fa-lock" aria-hidden="true"></i>';
        if (!defined('NV_IS_VIP')) {
            $contractor['content_static'] = preg_replace('/\<lockvip\>(.*?)\<\/lockvip\>/', $lock_symbol, $contractor['content_static']);
        } else {
            $contractor['content_static'] = preg_replace('/\<lockvip\>(.*?)\<\/lockvip\>/', '$1', $contractor['content_static']);
        }
        // Sửa link không rewrite từ bên API được
        if (preg_match('/(' . str_replace('/', '\/', preg_quote(NV_BASE_SITEURL)) . 'index\.php.+?op=resultdetail)\//', $contractor['content_static'], $m)) {
            $link_result_detail = nv_url_rewrite($m[1], true);
            $contractor['content_static'] = preg_replace('/' . str_replace('/', '\/', preg_quote(NV_BASE_SITEURL)) . 'index.php.+?op=resultdetail\//', $link_result_detail, $contractor['content_static']);
        }
        $xtpl->assign('IS_CONTRACTOR', sprintf($nv_Lang->getModule('is_contractor'), $url, $contractor['companyname']));
        $xtpl->assign('CONTRACTOR_INFO', $contractor['content_static']);
        if (!defined('NV_IS_VIP')) {
            if (defined('NV_IS_USER')) {
                $xtpl->assign('CONTRACTOR_LOCK', $nv_Lang->getModule('sub_for_any_vip'));
            } else {
                $xtpl->assign('CONTRACTOR_LOCK', sprintf($nv_Lang->getModule('log_in_up'), NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']), NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl'])));
            }
        }

        $xtpl->parse('main.contractor');
    }
}
//Nếu có dữ liệu liên kết với hồ sơ doanh nghiệp trên dauthau.net
if ($data_detail_tochuc['update_tab_tochucxd'] > 0 and !empty($data_detail_tochuc['dtnet_alias'])) {
    $xtpl->assign('DTNET_LINK_BUTTON', 'https://dauthau.net/vi/dn/' . $data_detail_tochuc['dtnet_alias']);
    $xtpl->parse('main.dtnet_link_button');
}

// Nếu có tổ chức khác có trùng mã chứng chỉ với tổ chức này sẽ hiển thị thông báo
if (!empty($_org_duplicate)) {
    $_link_detail = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . strtolower(change_alias($_org_duplicate['ten_to_chuc'])) . '-' . $_org_duplicate['id'] . $global_config['rewrite_exturl'];
    if (!empty($data_detail_tochuc['is_old'])) {
        $warning_duplicate = $nv_Lang->getModule('warm_organ') . ' ' . sprintf($nv_Lang->getModule('warm_dup_mcc'), $_link_detail, $_org_duplicate['ten_to_chuc'], $_link_detail);
    } else {
        $warning_duplicate = sprintf($nv_Lang->getModule('warm_dup_mcc'), $_link_detail, $_org_duplicate['ten_to_chuc'], $_link_detail);
    }

    $xtpl->assign('WARNING_DUPLICATE', $warning_duplicate);
    $xtpl->parse('main.warning_duplicate');
} else if (!empty($data_detail_tochuc['is_old'])) {
    $xtpl->assign('WARNING_DUPLICATE', $nv_Lang->getModule('warm_organ'));
    $xtpl->parse('main.warning_duplicate');
}

// Chỉ thành viên hoặc Bot mới vào đây được
if (!(defined('NV_IS_USER') or $client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
    global $page_url;
    $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    $link__popup = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
    // Gọi hàm Popuplogin và truyền tham số link vào
    $content_popup = PopupLogin($link__popup, nv_url_rewrite($page_url, true));
    $xtpl->assign('POPUP_LOGIN', $content_popup);
    $xtpl->parse('main.popup_login');
}

$crawl_request_history = [];
// Lấy lịch sử bấm cập nhật lại của người dùng
if (defined('NV_IS_ADMIN')) {
    $result_crawl = $db->query('SELECT id, userid, last_reload FROM ' . BID_PREFIX_GLOBAL . '_update_user WHERE to_chuc_id = ' . $data_detail_tochuc['id'] . ' ORDER BY last_reload DESC');
    while ($row_crawl =  $result_crawl->fetch()) {
        $crawl_request_history[] = $row_crawl;
    }
    $result_crawl->closeCursor();
    if (!empty($crawl_request_history)) {
        $result_user = $db->query('SELECT userid, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', array_map([$db, 'quote'], array_column($crawl_request_history, 'userid'))) . ')');
        while ($row_user =  $result_user->fetch()) {
            foreach ($crawl_request_history as $k => $v) {
                if ($v['userid'] == $row_user['userid']) {
                    $crawl_request_history[$k]['username'] = $row_user['username'];
                }
            }
        }
    }
}

if (!empty($crawl_request_history)) {
    $stt = 1;
    foreach ($crawl_request_history as $cr) {
        $cr['last_reload'] = date('d/m/Y H:i', $cr['last_reload']);
        $cr['stt'] = $stt++;
        $xtpl->assign('CR', $cr);
        $xtpl->parse('main.crawl_request_history_list.loop');
    }
    $xtpl->parse('main.crawl_request_history_list');
    $xtpl->parse('main.update.crawl_request_history_button');
}

if (defined("NV_IS_USER")) {
    if ($module_captcha == 'recaptcha' and  $global_config['recaptcha_ver'] == 2) {
        $xtpl->assign('RECAPTCHA_ELEMENT', 'recaptcha' . nv_genpass(8));
        $xtpl->assign('N_CAPTCHA', $nv_Lang->getGlobal('securitycode1'));
        $xtpl->parse('main.recaptcha');
    }
    $xtpl->parse('main.update');
}

// Lấy thông tin lịch sử thay đổi các lần cập nhật
$_list_log_crawls = $array_data['relate'] = $data_change_tb = [];
$arr_unset_org = [
    "content_full",
    "update_time",
    "so_lan_boc_tin",
    "thoi_gian_boc",
    "syncdtnet_time",
    "update_tab_tochucxd",
    "dtnet_alias",
    "totalview",
    "month_view",
    "id_tinh",
    "id_dvkd"
];
$sth = $db->query('SELECT * FROM nv4_vi_logs_crawls_organization WHERE obj_id = ' . $id . ' ORDER BY log_time_new ASC LIMIT 30');
$m = 0;
$_arr_key = array_keys($_data_tochuc);
$_row_data_1 = $_data_tochuc;
$_row_data_2 = $_data_tochuc;
while ($row = $sth->fetch()) {

    $tmp_log = json_decode($row['log_data'], true);
    $tmp_log_field = json_decode($row['log_data_fields'], true);
    $tmp_log_field_old = !empty($tmp_log_field) ? $tmp_log_field['log_old'] : [];
    $tmp_log_field_new = !empty($tmp_log_field) ? $tmp_log_field['log_new'] : [];
    if (!empty($tmp_log)) {
        $_key_change = array_keys($tmp_log);
        $text_changetb = [];
        foreach ($_key_change as $_key) {
            if (in_array($_key, $arr_unset_org)) {
                continue;
            }
            if ($_key == 'text_tinh') {
                $text_changetb[] = $nv_Lang->getModule('moderator');
            } else {
                $text_changetb[] = $nv_Lang->getModule($_key);
            }
        }
        if ($row['log_time_old'] >= $row['log_time_new']) {
            $row['log_time_old'] = $row['log_time_new'];
        }
        $tmp_log['log_time_new'] = nv_date('H:i d/m/Y', $row['log_time_new']);
        $tmp_log['log_time_old'] = nv_date('H:i d/m/Y', $row['log_time_old']);
        foreach ($tmp_log as $key => $value) {
            if (in_array($key, $arr_unset_org)) {
                continue;
            }
            if ($key != 'log_time_new' and $key != 'log_time_old') {
                $_row_data_1[$key] = $value['old'];
                $_row_data_2[$key] = $value['new'];
            }
        }
        $_row_data_1['linh_vuc'] = $tmp_log_field_old;
        $_row_data_2['linh_vuc'] = $tmp_log_field_new;
        if (!empty($tmp_log_field)) {
            $text_changetb[] = $nv_Lang->getModule('linh_vuc_hoat_dong');
        }
        if ($m == 0) {
            $_row_data_1['log_time'] = $tmp_log['log_time_old'];
            $_row_data_2['log_time'] = $tmp_log['log_time_new'];
            $_row_data_1['key_change'] = '';
            $_row_data_2['key_change'] = implode(', ', $text_changetb);
            $_list_log_crawls[$m] = $_row_data_1;
            $_list_log_crawls[$m+1] = $_row_data_2;
        } else {
            $_row_data_2['log_time'] = $tmp_log['log_time_new'];
            $_row_data_2['key_change'] = implode(', ', $text_changetb);
            $_list_log_crawls[$m+1] = $_row_data_2;
        }
    } else if (!empty($tmp_log_field)) {
        $_row_data_1['linh_vuc'] = $tmp_log_field_old;
        $_row_data_2['linh_vuc'] = $tmp_log_field_new;
        if ($m == 0) {
            $_row_data_1['log_time'] = nv_date('H:i d/m/Y', $row['log_time_old']);
            $_row_data_2['log_time'] = nv_date('H:i d/m/Y', $row['log_time_new']);
            $_row_data_1['key_change'] = '';
            $_row_data_2['key_change'] = $nv_Lang->getModule('linh_vuc_hoat_dong');
            $_list_log_crawls[$m] = $_row_data_1;
            $_list_log_crawls[$m+1] = $_row_data_2;
        } else {
            $_row_data_2['log_time'] = nv_date('H:i d/m/Y', $row['log_time_new']);
            $_row_data_2['key_change'] = $nv_Lang->getModule('linh_vuc_hoat_dong');
            $_list_log_crawls[$m+1] = $_row_data_2;
        }
    }
    $m++;
}

if (!empty($_list_log_crawls)) {
    $n = 0;
    $_count = count($_list_log_crawls) - 1;
    foreach ($_list_log_crawls as $item) {
        $array_data['relate'][$n]['id'] = $_data_tochuc['id'];
        $array_data['relate'][$n]['date_update'] = $item['log_time'];
        $array_data['relate'][$n]['checked'] = ($n == $_count) ? 'checked' : '';
        $array_data['relate'][$n]['ver'] = $n;
        if ($n == 0) {
            $array_data['relate'][$n]['date_update'] = $data_detail_tochuc['thoi_gian_boc'];
            $array_data['relate'][$n]['title_first'] = $nv_Lang->getModule('title_post_first');
            $array_data['relate'][$n]['key'] = "";
            $array_data['relate'][$n]['text_changetb'] = "";
        } else {
            $array_data['relate'][$n]['title_first'] = $nv_Lang->getModule('title_label_change');
            $array_data['relate'][$n]['key'] = ($n-1) . '-' . $n;
            $array_data['relate'][$n]['text_changetb'] = $item['key_change'];
            if (!empty($_list_log_crawls[$n-1]) && !empty($_list_log_crawls[$n])) {
                $data_change_tb[($n-1) . '-' . $n] = [
                    $n-1 => $_list_log_crawls[$n-1],
                    $n => $_list_log_crawls[$n]
                ];
                $array_data['relate'][$n]['data_id_tbmt'] = ($n-1) . '-' . $n;
            }
        }
        $xtpl->assign('RELATE', $array_data['relate'][$n]);
        $xtpl->parse('main.history_change_update.loop');
        $n++;
    }
    $xtpl->parse('main.history_change_update');
}

$xtpl->assign('DETAIL', $data_detail_tochuc);
$_arr_key = array(
    'ten_viet_tat',
    'ma_chung_chi',
    'tinh',
    'dia_chi_tru_so',
    'text_tinh',
    'nguoi_dai_dien',
    'chuc_vu',
    'dia_chi_vp',
    'ma_so_thue',
    'ngay_cap',
    'co_quan_cap'
);
$_hidden_mcc = $_hidden_dc = $_hidden_ndd = $_hidden_nc = '';
foreach ($_arr_key as $key) {
    if (!empty($data_detail_tochuc[$key])) {
        $xtpl->parse('main.' . $key);
    }
    if (empty($data_detail_tochuc['ma_chung_chi']) and empty($data_detail_tochuc['tinh'])) {
        $_hidden_mcc = ' hidden';
    }
    if (empty($data_detail_tochuc['dia_chi_tru_so']) and empty($data_detail_tochuc['text_tinh'])) {
        $_hidden_dc = ' hidden';
    }
    if (empty($data_detail_tochuc['nguoi_dai_dien']) and empty($data_detail_tochuc['chuc_vu'])) {
        $_hidden_ndd = ' hidden';
    }
    if (empty($data_detail_tochuc['ngay_cap']) and empty($data_detail_tochuc['co_quan_cap'])) {
        $_hidden_nc = ' hidden';
    }
}

$xtpl->assign('HIDDEN_MCC', $_hidden_mcc);
$xtpl->assign('HIDDEN_DC', $_hidden_dc);
$xtpl->assign('HIDDEN_NDD', $_hidden_ndd);
$xtpl->assign('HIDDEN_NC', $_hidden_nc);
$xtpl->parse('main');
$contents = $xtpl->text('main');

if ($nv_Request->isset_request('action', 'post') == 'view_change_tb') {
    $strID = $nv_Request->get_title('id', 'post', '');
    if ($strID != '') {
        $arrID = explode('-', trim($strID));
        if (intval($arrID[1]) > 0 and intval($arrID[0]) > 0) {
            if (isset($data_change_tb[$strID])) {
                $data = [
                    'thongbao' => getcompareOrang($data_change_tb[$strID][$arrID[0]]),
                    'thongbao1' => getcompareOrang($data_change_tb[$strID][$arrID[1]])
                ];
            } else {
                $data = [
                    'thongbao' => getcompareOrang($_list_log_crawls[$arrID[0]]),
                    'thongbao1' => getcompareOrang($_list_log_crawls[$arrID[1]])
                ];
            }
    
            nv_jsonOutput(
                array(
                    'res' => 'success',
                    'data' => $data
                )
            );
        } else {
            nv_jsonOutput(array(
                'res' => 'error',
                'data' => $nv_Lang->getModule('error_check_notifi_ver')
            ));
        }
    } else {
        nv_jsonOutput(
            array(
                'res' => 'error',
                'data' => 'null'
            )
        );
    }
}

$page_title = $nv_Lang->getModule('chi_tiet_to_chuc') . NV_TITLEBAR_DEFIS . $data_detail_tochuc['ten_to_chuc'];
$array_mod_title[] = array(
    'title' => $data_detail_tochuc['ten_to_chuc'],
    'link' => $base_url
);
// Không cho đánh tùy ý alias
$base_url_rewrite = nv_url_rewrite($base_url, true);
$base_url_check = str_replace('&amp;', '&', $base_url_rewrite);
$request_uri = rawurldecode($_SERVER['REQUEST_URI']);
if (isset($array_op[2]) or (strpos($_SERVER['REQUEST_URI'], $base_url_check) !== 0 and strpos(NV_MY_DOMAIN . $_SERVER['REQUEST_URI'], $base_url_check) !== 0)) {
    nv_redirect_location($base_url_rewrite);
}
$canonicalUrl = getCanonicalUrl($page_url);
include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';

function getcompareOrang($array_data = [])
{
    global $xtpl, $module_file, $global_config;
    if (empty($array_data)) {
        return [];
    }
    $array_data['ngay_cap'] = !empty($array_data['ngay_cap']) ? nv_date('d/m/Y', $array_data['ngay_cap']) : '';
    $xtpl = new XTemplate('organization.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('DATA', $array_data);
    foreach ($array_data['linh_vuc'] as $item) {
        $item['ngay_het_han'] = !empty($item['ngay_het_han']) ? nv_date('d/m/Y', $item['ngay_het_han']) : '';
        $xtpl->assign('LINHVUC', $item);
        $xtpl->parse('main.linh_vuc.loop');
    }
    $xtpl->parse('main.linh_vuc');
    $xtpl->parse('main');
    return $xtpl->text('main');
}
