<!-- BEGIN: main -->
<div class="panel">
    <div class="pull-right">
        <p>[{LANG.total} {NUM_ITEMS}]</p>
    </div>
</div>
<!-- BEGIN: downloadExcel -->
<div class="panel-heading" >
    <button id='downloadExcel' class="btn" title="{LANG.title_btn_download_excel}"  style="background-color: #34A853; color: white; margin-bottom: 5px;" onclick="checkPoint();"><i class="fa fa-file-excel-o" aria-hidden="true"></i> {LANG.download_excel}</button>
    <span><i>({NUM_ITEMS} {LANG.business} = {TOTAL_POINT_DOWNLOAD} {LANG.point})</i></span>

    <div id="alertDownloadExcel"></div>
</div>
<!-- END: downloadExcel -->
<!-- BEGIN: x2_alert -->
<div id="x2_alert" class="alert alert-info">{X2_ALERT}</div>
<!-- END: x2_alert -->
<div id="business-list" class="panel panel-default panel-listbuss">
    <div class="panel-heading"><h2>{TITLE}</h2></div>
    <div class="list-group">
        <!-- BEGIN: loop -->
        <div class="list-group-item">
            <div class="clearfix">
                <div class="listing-summary">
                    <div class="row">
                        <h3>
                            <div class="col-xs-24 col-md-20">
                                <span class="org-code">{ROW.orgcode}</span> <a href="{ROW.link}">{ROW.companyname}</a>
                                <!-- BEGIN: is_fail -->
                                {LANG.is_fail}
                                <!-- END: is_fail -->
                            </div>
                        </h3>
                    </div>
                    <div class="row">
                        <div class="col-xs-24">
                            <div class="address">
                                <p class="add_content">
                                    <b># {LANG.taxcode}:</b> {ROW.code}
                                </p>
                                <p class="add_content">
                                    <b><i class="fa fa-location-arrow" aria-hidden="true"></i> {LANG.province}:</b>
                                    <a class="tag" href="{ROW.link_location}">{ROW.location}</a>
                                </p>
                                <p class="add_content">
                                    <b><i class="fa fa-map-marker" aria-hidden="true"></i> {LANG.address}:</b> {ROW.addressfull}
                                </p>

                                <p class="flex__custom_info add_content">
                                    <!-- BEGIN: phone -->
                                        <span><b><i class="fa fa-phone" aria-hidden="true"></i> {LANG.phone}:</b> {ROW.phone}</span>
                                    <!-- END: phone -->

                                    <!-- BEGIN: fax -->
                                        <span><b><i class="fa fa-mobile" aria-hidden="true"></i> {LANG.fax}:</b> {ROW.fax}</span>
                                    <!-- END: fax -->

                                    <!-- BEGIN: represent_phone -->
                                        <span><b><i class="fa fa-volume-control-phone" aria-hidden="true"></i> {LANG.represent_phone}:</b> {ROW.represent_phone}</span>
                                    <!-- END: represent_phone -->
                                </p>
                                <p>
                                    <!-- BEGIN: website -->
                                        <span>
                                            <b><i class="fa fa-globe" aria-hidden="true"></i> {LANG.website}:</b> {ROW.website}
                                        </span>
                                    <!-- END: website -->
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <a href="#" class="btn btn-primary btn-cmp-ar btn-compare-actt{HIDDEN_ADD_CMP} btn-xs" data-id="{ROW.id}" onclick="addCttCompare({ROW.id}, '{ROW.checkaddcp}');"><em class="fa fa-plus">&nbsp;</em>{LANG.add_compare}</a>
                <a href="#" class="btn btn-danger btn-cmp-ar btn-compare-rctt{HIDDEN_REM_CMP} btn-xs" data-id="{ROW.id}" onclick="removeCttCompare({ROW.id}, '{ROW.checkremvcp}');"><em class="fa fa-minus">&nbsp;</em>{LANG.remove_compare}</a>
            </div>
        </div>
        <!-- END: loop -->

        <!-- BEGIN: nodata -->
        <div class="alert alert-warning alert__no_mr">
            {NO_DATA}
        </div>
        <!-- END: nodata -->

        <!-- BEGIN: limit_page -->
        {LIMIT_PAGE}
        <!-- END: limit_page -->
    </div>
</div>
<!-- BEGIN: page -->
<div class="pages-links text-center">
    {PAGE}
</div>
<!-- END: page -->

{FILE "download_excel_script.tpl"}
<!-- END: main -->
