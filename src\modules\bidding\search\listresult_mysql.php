<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_BIDDING'))
    die('Stop!!!');

use NukeViet\Dauthau\Url;

if ($type_search == 2) {
    if (!empty($sfrom) && !empty($sto)) {
        $where = ' post_time>=' . $sfrom1 . ' AND post_time<=' . $sto1;
    } else {
        $where = ' post_time IS NOT NULL';
    }
    $goods = 0;
} else {
    if (in_array($static, [1, 3, 4])) {
        $where = 'time_open_static>=' . $sfrom1 . ' AND time_open_static<=' . $sto1;
    } else {
        $where = ' finish_time>=' . $sfrom1 . ' AND finish_time<=' . $sto1;
    }

    if ($catressult > 0) {
        $where .= $catressult == 1 ? " AND type_data=0" : " AND type_data=1";
    }

    if ($win_price_from > 0) {
        // tìm theo giá mời thầu
        $where .= " AND win_price_number >= " . $win_price_from;
    }
    if ($win_price_to > 0) {
        // tìm theo giá mời thầu
        $where .= " AND win_price_number <= " . $win_price_to;
    }

    if ($price_plan_from > 0) {
        $where .= " AND bid_price_number >= " . $price_plan_from;
    }
    if ($price_plan_to > 0) {
        $where .= " AND bid_price_number <= " . $price_plan_to;
    }
}

if ($solicitor_id > 0) {
    $where .= " AND solicitor_id=" . $solicitor_id;
}
if ($static > 1) {
    if ($static == 3) {
        $where .= " AND (update_static = 3 OR update_static = 5)";
    } elseif ($static == 4) {
        $where .= " AND (update_static = 4 OR update_static = 5)";
    }

    if (!empty($vip_static)) {
        if ($static == 4) {
            $where .= " AND FIND_IN_SET(" . $db->quote($vip_static) . ", vip_fail)";
        } else {
            $where .= " AND FIND_IN_SET(" . $db->quote($vip_static) . ", vip_win)";
        }
    }
}
if ($type_search != 2) {
    if ($type_bid > 0 and $is_advance) {
        $where .= " AND type_bid_id =" . $type_bid;
    }
    if ($type_choose_id > 0 and $is_advance) {
        $where .= " AND (type_choose_id =" . $type_choose_id . " OR type_bid_id = " . $type_choose_id . ")";
    }

    if ($type_kqlcnt == 1 and $is_advance) {
        $where .= " AND type_kqlcnt = 0";
    } elseif ($type_kqlcnt == 2 and $is_advance) {
        $where .= " AND type_kqlcnt > 0";
    }
}

if (!empty($without_key) and $is_advance) {
    // Tìm theo từ khóa loại trừ
    $arr_key = explode(',', $without_key);
    foreach ($arr_key as $key) {
        if ($search_type_content == 1 and $type_search == 1) {
            $key = trim($key);
            if (!$par_search) {
                $where .= " AND content_full COLLATE utf8mb4_unicode_ci NOT LIKE " . $db->quote('%' . $key . '%') . '';
            } else {
                if (!preg_match('/[0-9]/', $key)) {
                    $where .= " AND title COLLATE utf8mb4_unicode_ci NOT LIKE " . $db->quote('%' . $key . '%') . "";
                } else {
                    $where .= " AND code COLLATE utf8mb4_unicode_ci NOT LIKE " . $db->quote('%' . $key . '%') . " AND title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . '';
                }
            }
        } else {
            $key = trim($key);
            if (!$par_search) {
                if ($goods == 1 || $goods == 2) {
                    $where .= ' AND (content_goods NOT LIKE ' . $db->quote('%' . $key . '%') . ' OR content NOT LIKE ' . $db->quote('%' . $key . '%') . ')';
                } else {
                    $where .= ' AND content NOT LIKE ' . $db->quote('%' . $key . '%');
                }
            } else {
                if (!preg_match('/[0-9]/', $key)) {
                    $where .= " AND title NOT LIKE " . $db->quote('%' . $key . '%');
                } else {
                    $where .= " AND code NOT LIKE " . $db->quote('%' . $key . '%') . " AND title NOT LIKE " . $db->quote('%' . $key . '%');
                }
            }
        }
    }
}

if (!empty($key_search2) and $is_advance) {
    // Tìm theo Từ khóa bổ sung
    $arr_key = explode(',', $key_search2);
    $_where_1 = [];
    foreach ($arr_key as $key) {
        if (!empty($search_one_key)) {
            if ($search_type_content == 1 and $type_search == 1) {
                $key = trim($key);
                if (!$par_search) {
                    $_where_1[] = "content_full COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . '';
                } else {
                    if (!preg_match('/[0-9]/', $key)) {
                        $_where_1[] = "title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . "";
                    } else {
                        $_where_1[] = "code COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . " OR title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%');
                    }
                }
            } else {
                $key = trim($key);
                if (!$par_search) {
                    if ($goods == 1) {
                        $_where_1[] = 'content_goods LIKE ' . $db->quote('%' . $key . '%') . ' OR content LIKE ' . $db->quote('%' . $key . '%');
                    } elseif ($goods == 2) {
                        $_where_1[] = 'content_goods LIKE ' . $db->quote('%' . $key . '%');
                    } else {
                        $_where_1[] = 'content LIKE ' . $db->quote('%' . $key . '%');
                    }
                } else {
                    if (!preg_match('/[0-9]/', $key)) {
                        $_where_1[] = "title LIKE " . $db->quote('%' . $key . '%');
                    } else {
                        $_where_1[] = "code LIKE " . $db->quote('%' . $key . '%') . " OR title LIKE " . $db->quote('%' . $key . '%');
                    }
                }
            }
        } else {
            if ($search_type_content == 1 and $type_search == 1) {
                $key = trim($key);
                if (!$par_search) {
                    $where .= " AND content_full COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . '';
                } else {
                    if (!preg_match('/[0-9]/', $key)) {
                        $where .= " AND title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . "";
                    } else {
                        $where .= " AND (code COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . " OR title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . ')';
                    }
                }
            } else {
                $key = trim($key);
                if (!$par_search) {
                    if ($goods == 1) {
                        $where .= ' AND (content_goods LIKE ' . $db->quote('%' . $key . '%') . ' OR content LIKE ' . $db->quote('%' . $key . '%') . ')';
                    } elseif ($goods == 2) {
                        $where .= ' AND content_goods LIKE ' . $db->quote('%' . $key . '%');
                    } else {
                        $where .= ' AND content LIKE ' . $db->quote('%' . $key . '%');
                    }
                } else {
                    if (!preg_match('/[0-9]/', $key)) {
                        $where .= " AND title LIKE " . $db->quote('%' . $key . '%');
                    } else {
                        $where .= " AND (code LIKE " . $db->quote('%' . $key . '%') . " OR title LIKE " . $db->quote('%' . $key . '%') . ')';
                    }
                }
            }
        }
    }
    if (!empty($_where_1)) {
        $where .= ' AND (' . implode(' OR ', $_where_1) . ')';
    }
}

// Tìm kiếm theo tỉnh thành
if (!empty($_idprovince) and $is_advance && $type_search != 2) {
    if ($_idprovince[0] == 0) {
        $where .= ' AND ( id_province = 0)';
    } else {
        $where .= ' AND FIND_IN_SET ( (' . implode(',', $_idprovince) . ') , id_province)';
    }
}

if (!empty($q)) {
    $where .= ' AND (';
    $num = 0;
    $arr_key = explode(',', $q);
    foreach ($arr_key as $key) {
        if ($search_type_content == 1 and $type_search == 1) {
            $key = trim($key);
            $num > 0 && $where .= ' OR ';
            if (!$par_search) {
                $where .= "content_full COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . '';
            } else {
                if (!preg_match('/[0-9]/', $key)) {
                    $where .= "title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . "";
                } else {
                    $where .= "code COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . " OR title COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . '';
                }
            }
        } else {
            $key = trim($key);
            $num > 0 && $where .= ' OR ';
            if (!$par_search) {
                if ($goods == 1) {
                    $where .= ' (content_goods LIKE ' . $db->quote('%' . $key . '%') . ' OR content LIKE ' . $db->quote('%' . $key . '%') . ')';
                } elseif ($goods == 2) {
                    $where .= ' content_goods LIKE ' . $db->quote('%' . $key . '%');
                } else {
                    $where .= ' content LIKE ' . $db->quote('%' . $key . '%');
                }
            } else {
                if (!preg_match('/[0-9]/', $key)) {
                    $where .= "title LIKE " . $db->quote('%' . $key . '%');
                } else {
                    $where .= "code LIKE " . $db->quote('%' . $key . '%') . " OR title LIKE " . $db->quote('%' . $key . '%');
                }
            }
        }
        $num++;
    }
    $where .= ')';
}

//Tìm kiếm theo so_tbmt
if (!empty($code)) {
    $where .= ' AND (';
    $num = 0;
    $arr_code = explode(',', $code);
    foreach ($arr_code as $key) {
        $key = explode('-', $key)[0];
        $key = trim($key);
        if ($num == 0) {
            $where .= "code LIKE " . $db->quote($key . '%');
        } else {
            $where .= " OR code LIKE " . $db->quote($key . '%');
        }
        $num++;
    }
    $where .= ')';
}

// Tìm kiếm theo lĩnh vực kqlcnt
if (!empty($bidfieid) && $type_search != 2) {
    $fieldkq = array_map(fn($field) => $db->quote($field), $bidfieid);
    $where .= ' AND bidfieid IN (' . implode(',', $fieldkq) . ')';
}

// gói vip 4
if ($type_search == 2) {
    $db->sqlreset()
        ->select('COUNT(id)')
        ->from(NV_PREFIXLANG . '_' . $module_data . '_result_project')
        ->where($where);
    $sth = $db->prepare($db->sql());
} else {
    $db->sqlreset()
        ->select('COUNT(id)')
        ->from(NV_PREFIXLANG . '_' . $module_data . '_result')
        ->where($where);
    $sth = $db->prepare($db->sql());
}

$sth->execute();
$num_items = $sth->fetchColumn();

$db->select('*')
    ->order(($type_search == 1 ? 'finish_time' : 'post_time') . ' DESC, id DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());

$sth->execute();
$min_time = $max_time = 0;
while ($view = $sth->fetch()) {
    $view['link_edit'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $view['id'];
    $view['link_delete'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;delete_id=' . $view['id'] . '&amp;delete_checkss=' . md5($view['id'] . NV_CACHE_PREFIX . $client_info['session_id']);

    $view['bidder_list'] = [];
    $alias_re = isset($view['alias']) ? $view['alias'] : strtolower(change_alias($view['title']));

    if ($type_search == 2) {
        $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNDT() . '/' . $alias_re . '-' . $view['id'] . $global_config['rewrite_exturl'];
        $view['link_search'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=listresult&type=1&solicitor_id=' . $view['solicitor_id'];
        if (isset($view['finish_time'])) {
            $view['finish_time'] = date("d/m/Y H:i", $view['finish_time']);
            $view['finish_time'] = explode('00:00', $view['finish_time'])[0];
        } else {
            $view['finish_time'] = date("d/m/Y H:i", $view['post_time']);
            $view['finish_time'] = explode('00:00', $view['finish_time'])[0];
        }

        $view['bidder_list'][] = [
            'name' => $view['bidder_name'],
            'bid' => 0,
            'no_business_licence' => $view['no_business_licence']
        ];
        !empty($view['no_business_licence']) && $arr_bidder[] = $view['no_business_licence'];
    } else {
        $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNT() . '/' . $alias_re . '-' . $view['id'] . $global_config['rewrite_exturl'];
        $view['link_search'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=listresult&type=0&solicitor_id=' . $view['solicitor_id'];

        if (isset($view['finish_time'])) {
            if (empty($min_time)) {
                $min_time = $max_time = $view['finish_time'];
            }
            $view['finish_time'] < $min_time && $min_time = $view['finish_time'];
            $view['finish_time'] > $max_time && $max_time = $view['finish_time'];
            $view['finish_time'] = date("d/m/Y H:i", $view['finish_time']);
            $view['finish_time'] = explode('00:00', $view['finish_time'])[0];
        } else {
            $view['finish_time'] = date("d/m/Y H:i", $view['post_time']);
            $view['finish_time'] = explode('00:00', $view['finish_time'])[0];
        }
        $rids[] = $view['id'];
    }

    $array_data[$view['id']] = $view;
    $arr_solicitor_id[] = $view['solicitor_id'];
    $arr_tbmt[] = $view['code'];
}
