<?php

/**
 * @Project NUKEVIET 4.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2025 VINADES.,JSC. All rights reserved
 * @Createdate Mon, 24 Mar 2025 08:13:20 GMT
 */

if (!defined('NV_MAINFILE'))
    die('Stop!!!');

if (!nv_function_exists('nv_search_department_blocks')) {
    function nv_search_department_blocks($block_config)
    {
        global $module_info, $module_name, $module_data, $nv_Request, $module_file, $nv_Cache, $global_array_config, $array_op, $db, $province_list, $user_info, $nv_Lang;
        global $global_config;

        if (file_exists(NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file . "/block_search_department.tpl")) {
            $block_theme = $module_info['template'];
        } else {
            $block_theme = "default";
        }

        $xtpl = new XTemplate("block_search_department.tpl", NV_ROOTDIR . "/themes/" . $block_theme . "/modules/" . $module_file);
        $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

        if ($global_config['rewrite_enable']) {
            $xtpl->assign('FORM_ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=department', true));
        } else {
            $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php');
            $xtpl->assign('NV_OP_VALUE', 'department');
            $xtpl->assign('MODULE_NAME', $module_name);
            $xtpl->parse('main.no_rewrite');
        }

        // Lấy dữ liệu từ request
        $province = $nv_Request->get_int('province', 'get', 0);
        $department_name = $nv_Request->get_title('department_name', 'get', '');
        $director = $nv_Request->get_title('director', 'get', '');
        $min_chinhanh = $nv_Request->get_int('min_chinhanh', 'get', 0);
        $max_chinhanh = $nv_Request->get_int('max_chinhanh', 'get', 0);
        $min_dgv = $nv_Request->get_int('min_dgv', 'get', 0);
        $max_dgv = $nv_Request->get_int('max_dgv', 'get', 0);

        $a = $b = '';
        if (isset($array_op[1])) {
            $a = substr($array_op[1], 0, 1);
            $b = substr($array_op[1], 2);
            if (preg_match('/^([a-zA-Z0-9\-]+)\-([0-9]+)$/i', $b, $m)) {
                $b = $m[2];
            }
        }
        $module = "location";
        if ($a == "T") {
            $sql = "SELECT id FROM " . NV_PREFIXLANG . "_" . $module . "_province WHERE status=1 AND id = " . $db->quote($b);
            $result = $db->query($sql);
            list($_id) = $result->fetch(3);
            $province = $_id;
        }

        // Lấy danh sách tỉnh/thành phố
        $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province";
        $province_list = $nv_Cache->db($sql, 'id', 'location');
        foreach ($province_list as $province_list_i) {
            $sl = ($province == $province_list_i['id']) ? "selected='selected'" : "";
            $arr_province[] = array(
                "id" => $province_list_i['id'],
                "title" => $province_list_i['title'],
                "sl" => $sl);
        }

        foreach ($arr_province as $province_i) {
            $xtpl->assign('sl_province', $province_i['sl']);
            $xtpl->assign('key_province', $province_i['id']);
            $xtpl->assign('val_province', $province_i['title']);
            $xtpl->parse('main.province');
        }

        $xtpl->assign('department_name', $department_name);
        $xtpl->assign('director', $director);
        $xtpl->assign('min_chinhanh', $min_chinhanh > 0 ? $min_chinhanh : '');
        $xtpl->assign('max_chinhanh', $max_chinhanh > 0 ? $max_chinhanh : '');
        $xtpl->assign('min_dgv', $min_dgv > 0 ? $min_dgv : '');
        $xtpl->assign('max_dgv', $max_dgv > 0 ? $max_dgv : '');

        $xtpl->parse('main');
        return $xtpl->text('main');
    }
}

if (defined('NV_SYSTEM')) {
    $content = nv_search_department_blocks($block_config);
}
