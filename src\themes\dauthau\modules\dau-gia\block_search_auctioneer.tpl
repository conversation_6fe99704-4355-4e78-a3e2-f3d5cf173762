<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<form action="{FORM_ACTION}" method="get" class="block-bidding form-horizontal form-search" id="dauGiaSearchBlock">
    <!-- BEGIN: no_rewrite -->
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"/>
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}"/>
    <input type="hidden" name="{NV_OP_VARIABLE}" value="{NV_OP_VALUE}"/>
    <!-- END: no_rewrite -->
    <div class="form-group">
        <label class="control-label col-md-5">{LANG.auctioneer_search_name}:</label>
        <div class="col-md-19">
            <input class="form-control" type="text" value="{name}" name="name" data-error="{LANG.type_text_error}"/>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-md-5">{LANG.auctioneer_search_cchn}:</label>
        <div class="col-md-19">
            <input class="form-control" type="text" value="{cchn}" name="cchn" data-error="{LANG.type_text_error}"/>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-md-5">{LANG.auctioneer_search_province}:</label>
        <div class="col-md-19">
            <select name="province" id="province" class="form-control fselect2">
                <option value="0">{LANG.pleaseselect}</option>
                <!-- BEGIN: province -->
                <option value="{key_province}" {sl_province}>{val_province}</option>
                <!-- END: province -->
            </select>
        </div>
    </div>
    <div class="form-group" id="gr_organization">
        <label class="control-label col-md-5">{LANG.auctioneer_search_org}:</label>
        <div class="col-md-19">
            <select name="bidder" class="form-control fselect2" id="id_bidder" style="width: 100%" data-default="0">
                <option value="0">Tất cả</option>
                <!-- BEGIN: bidder -->
                <option value="{BIDDER.key}"{BIDDER.selected}>{BIDDER.name_bidder}</option>
                <!-- END: bidder -->
            </select>
        </div>
    </div>
    <div class="row">
        <div class="col-md-19 col-md-offset-5">
            <input id="fsearch" type="submit" value="{LANG.search}" class="btn btn-primary"/>
        </div>
    </div>
</form>
<script type="text/javascript">
    $(function() {
        $('.fselect2').select2({ language : nv_lang_interface });
        $('#id_bidder').select2({
            minimumInputLength : 2, multiple : false,
            ajax : {
                url : nv_base_siteurl + 'index.php?' + nv_name_variable + '=dau-gia&' + nv_fc_variable + '=organization&get_organ=1&nocache=' + new Date().getTime(),
                dataType : 'json',
                delay : 250, // wait 250  milliseconds before triggering the request
                data : function(term, page) {
                    return { q : term, page_limit : 20, page : page
                    //you need to send page number or your script do not know witch results to skip
                    };
                },
                results : function(data, page) {
                    var more = (page * 20) < data.total;
                    return { results : data.results, more : more };
                },
                dropdownCssClass : "bigdrop"
            }
        });
        $('#select2-id_bidder-container').on('click', function() {
            if ($('#select2-id_bidder-container').text() != 'Tất cả') {
                console.log($('#select2-id_bidder-container').text());
                $('[aria-controls=select2-id_bidder-results]').val($('#select2-id_bidder-container').text());
                $('[aria-controls=select2-id_bidder-results]').focus();
                $('[aria-controls=select2-id_bidder-results]').trigger('input');
            }
        });
    });
</script>
<!-- END: main -->
