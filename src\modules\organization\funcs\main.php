<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */

if (!defined('NV_IS_MOD_ORGANIZATION')) {
    die('Stop!!!');
}

$error = $where = [];
$array_data = $location_map = $location_id_map = [];
$q = $nv_Request->get_title('q', 'post,get');
$location = $nv_Request->get_title('location', 'post,get', -1);
$_province_id = $nv_Request->get_int('province_id', 'post,get', -1);
$censorship = $nv_Request->get_int('censorship', 'post,get', -1);

if (isset($array_op[1]))  $location_alias = $array_op[1];
if (isset($array_op[0]))  $location_op = $array_op[0];

$page_title = $nv_Lang->getModule('organization');
$description = $nv_Lang->getModule('description');

$page = $nv_Request->get_page('page', 'post,get', 1);
$home_page = $nv_Request->get_int('home_page', 'get', 0);
$per_page = ($home_page == 1) ? 10 : 20;
$id = 0;

// Lấy thông tin tỉnh thành
$db->sqlreset()
    ->select('id, title, alias')
    ->from(NV_PREFIXLANG . '_' . 'location_province')
    ->order('title ASC');
$provinces = $nv_Cache->db($db->sql(), '', 'location');

// Lấy thông tin các tổ chức quản lý
$db->sqlreset()
    ->select('id, title, alias')
    ->from(NV_PREFIXLANG . '_' . 'control_organization')
    ->order('title ASC');
$control_orgs = $nv_Cache->db($db->sql(), '', $module_name);

$array_data['province'] = array_merge($control_orgs, $provinces);
array_unshift($array_data['province'], array(
    'id' => 0,
    'title' => $nv_Lang->getModule('no_title'),
    'alias' => 'Chua-phan-loai'
));
$array_data['list_province'] = $provinces;

foreach ($array_data['province'] as $value) {
    $location_map[$value['alias']] = $value['id'];
    $location_id_map[$value['id']] = [
        'alias' => $value['alias'],
        'title' => $value['title']
    ];
}

// Giói hạn số trang tìm kiếm
if ($page < 1) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_wrong_page') . $btn, 'danger');
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}
if ($page > 100) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_max_searchpage') . $btn);
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;
$global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;

// Fetch Limit
if (isset($location_op) and $location_op == 'old') {
    $base_url .= '/old';
    $global_lang_url .= '/old';
    $where[] = 'is_old > 0';
    $heading2 = $nv_Lang->getModule('header_orang_not_show');
    $description = $nv_Lang->getModule('header_orang_not_show');
    $type_data = $nv_Request->get_int('type_data', 'post,get', 2);
    $page_title = $nv_Lang->getModule('header_orang_not_show');
} else {
    $type_data = $nv_Request->get_int('type_data', 'post,get', 1);
    if ($type_data == 2) {
        $where[] = 'is_old = 1';
        $heading2 = $nv_Lang->getModule('header_orang_not_show');
        $description = $nv_Lang->getModule('header_orang_not_show');
    } else {
        $where[] = 'is_old = 0';
    }
}

// Nếu vào link đơn vị kiểm duyệt cũ /location/ thì redirect về link mới
if(isset($location_alias) and isset($location_op) and $location_op == 'location') {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '/censorship/' . $location_alias);
}

if(isset($location_alias) and isset($location_op) and $location_op != 'censorship') {
    // Không cho tùy tiện gõ location_alias
    if (!isset($location_map[$location_alias])) nv_redirect_location(nv_url_rewrite($base_url, true));

    $base_url .= '/listlocation/' . $location_alias;
    $global_lang_url .= '/listlocation/' . $location_alias;
    $location = $location_map[$location_alias];
    if ($type_data != 2) {
        $heading2 = $nv_Lang->getModule('heading2_province', $location_id_map[$location]['title']);
        $description = $nv_Lang->getModule('description_province', $location_id_map[$location]['title']);
    }
}

if(isset($location_alias) and isset($location_op) and $location_op == 'censorship') {
    // Không cho tùy tiện gõ location_alias
    if (!isset($location_map[$location_alias])) nv_redirect_location(nv_url_rewrite($base_url, true));

    $base_url .= '/censorship/' . $location_alias;
    $global_lang_url .= '/censorship/' . $location_alias;
    $location = $location_map[$location_alias];
    if ($type_data != 2) {
        if ($location_alias == 'Chua-phan-loai') {
            $heading2 = $nv_Lang->getModule('heading2_no_dvkd', $location_id_map[$location]['title']);
            $description = $nv_Lang->getModule('description_no_dvkd', $location_id_map[$location]['title']);
        } else {
            $heading2 = $nv_Lang->getModule('heading2_dvkd', $location_id_map[$location]['title']);
            $description = $nv_Lang->getModule('description_dvkd', $location_id_map[$location]['title']);
        }
    }
}
if ((!isset($location_op) or $location_op == 'old') and ($_province_id > 0 or $censorship >= 0)) {
    // Tìm kiếm theo cả đơn vị kiểm duyệt và cả tỉnh thành
    if ($_province_id > 0 and $censorship >= 0) {
        $where[] = "id_dvkd = " . $censorship . ' AND id_tinh = ' . $_province_id;
        if ($type_data == 1) {
            if ($censorship == '0') {
                $heading2 = $nv_Lang->getModule('heading2_province_no_dvkd', $location_id_map[$_province_id]['title']);
                $description = $nv_Lang->getModule('description_province_no_dvkd', $location_id_map[$_province_id]['title']);
            } else {
                $heading2 = $nv_Lang->getModule('heading2_province_dvkd', $location_id_map[$_province_id]['title'], $location_id_map[$censorship]['title']);
                $description = $nv_Lang->getModule('description_province_dvkd', $location_id_map[$_province_id]['title'], $location_id_map[$censorship]['title']);
            }
        }
    } else if ($_province_id > 0) {
        $where[] = "id_tinh = " . $_province_id;
    } else if ($censorship >= 0) {
        $where[] = "id_dvkd = " . $censorship;
    }
    if ($_province_id > 0) {
        $base_url .= '&amp;province_id=' . $_province_id;
        $global_lang_url .= '&amp;province_id=' . $_province_id;
    }
    if ($censorship >= 0) {
        $base_url .= '&amp;censorship=' . $censorship;
        $global_lang_url .= '&amp;censorship=' . $censorship;
    }
}
if ($location >= 0) {
    // Không cho tùy tiện gõ location_id
    if(!isset($location_id_map[$location])) nv_redirect_location(nv_url_rewrite($base_url, true));

    if (!isset($location_alias)) {
        $base_url .= '&amp;location=' . $location;
        $global_lang_url .= '&amp;location=' . $location;
    }
    if (isset($location_op) and $location_op == 'censorship') {
        $where[] = "id_dvkd = " . $location;
    } else {
        $where[] = "id_tinh = " . $location;
    }
}
if (!empty($q)) {
    $base_url .= '&amp;q=' . urlencode($q);
    $global_lang_url .= '&amp;q=' . urlencode($q);
    $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getModule('search') . NV_TITLEBAR_DEFIS . $q;
    $where[] = "content_full LIKE " . $db->quote('%' . $q . '%');
}

if (!empty($where)) {
    $where = implode(' AND ', $where);
}

$db->sqlreset()
    ->select('COUNT(id)')
    ->from($db_config['prefix'] . '_tochuc')
    ->where($where);
$sth = $db->prepare($db->sql());
$sth->execute();
$num_items = $sth->fetchColumn();

$db->select('*')
    ->order('id_url DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$array_data['tochuc'] = $db->query($db->sql())->fetchAll();

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
$page_url = $base_url;
if (isset($array_op[0]) and $array_op[0] == 'censorship') {
    $page_url =  NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=censorship/' . $array_op[1];;
} else if (isset($array_op[1])) {
    $page_url =  NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=listlocation/' . $array_op[1];
}
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}
// #2415: Thêm hằng chặn index
$query_params = [];
parse_str(parse_url($page_url, PHP_URL_QUERY), $query_params);
// Danh sách các tham số cần xóa
$params_to_remove = [
    'amp;' . NV_NAME_VARIABLE,
    'amp;' . NV_OP_VARIABLE,
    NV_LANG_VARIABLE,
    'amp;type_data'
];
$query_params = array_diff_key($query_params, array_flip($params_to_remove));
$has_other_query_params = false;
foreach ($query_params as $key => $value) {
    if ($key !== 'amp;page') {
        $has_other_query_params = true;
        break;
    }
}
if (!empty($q) || empty($array_data['tochuc']) || $has_other_query_params) {
    $nv_BotManager->setFollow()->setNoIndex();
}

$canonicalUrl = getCanonicalUrl($page_url);
betweenURLs($page, ceil($num_items/$per_page), $base_url, '&amp;page=', $prevPage, $nextPage);

$_static = '';
if (empty($array_op) or $type_data != 2) {
    $_total_new = $db->query('SELECT COUNT(*) FROM ' . $db_config['prefix'] . '_tochuc WHERE is_old = 0')->fetchColumn();
    $_total_old = $db->query('SELECT COUNT(*) FROM ' . $db_config['prefix'] . '_tochuc WHERE is_old = 1')->fetchColumn();
    $link_old = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=old';
    if (!defined("NV_IS_USER")) {
        $link_login = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE . '=login&nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']);
        $_static = sprintf($nv_Lang->getModule('statics_no_login'), number_format($_total_new, 0, ',', '.'), number_format($_total_old, 0, ',', '.'), $link_login);
    } else {
        $_static = sprintf($nv_Lang->getModule('statics'), number_format($_total_new, 0, ',', '.'), number_format($_total_old, 0, ',', '.'), $link_old);
    }
}

$xtpl = new XTemplate('main.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('HEADING2', $heading2);
$xtpl->assign('STATICS', $_static);

if (isset($location_op) and $location_op == 'old') {
    if (!(defined('NV_IS_USER') or $client_info['is_bot'])) {
        global $page_url;
        $link_register = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=register&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
        $link__popup = sprintf($nv_Lang->getModule('confirm_not_user1'), $link_register);
        // Gọi hàm Popuplogin và truyền tham số link vào
        $page_url_1 = str_replace('&amp;', '&', $page_url);
        $content_popup = PopupLogin($link__popup, nv_url_rewrite($page_url_1, true));
        $xtpl->assign('POPUP_LOGIN', $content_popup);
        $xtpl->parse('main.popup_login');
    }
}
if (empty($array_data['tochuc'])) {
    $xtpl->parse('main.empty');
} else {
    $stt = (($page - 1) * $per_page) + 1;
    foreach ($array_data['tochuc'] as $view) {
        $view['link_province'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=listlocation/' . change_alias($view['tinh']);
        $view['link_dvkd'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=censorship/' . change_alias($view['text_tinh']);
        $view['stt'] = $stt++;
        $view['link_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . strtolower(change_alias($view['ten_to_chuc'])) . '-' . $view['id'] . $global_config['rewrite_exturl'];
        $xtpl->assign('VIEW', $view);
        $xtpl->parse('main.loop');
    }
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
    }
}
if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}
$xtpl->assign('NUM_RESULT', sprintf($nv_Lang->getModule('time_query'), number_format((microtime(true) - NV_START_TIME), 3, '.', ''), number_format($num_items, 0, ",", ".")));
$xtpl->parse('main');
$contents = $xtpl->text('main');
include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
