<!-- BEGIN: main -->
<!-- BEGIN: popup_login -->
{POPUP_LOGIN}
<!-- END: popup_login -->
<!-- BEGIN: error -->
    <div class="alert alert-danger">
        {ERROR}
    </div>
<!-- END: error -->
<!-- BEGIN: view -->
<div class="border-bidding">
    <h2 class="title__tab_heading"><span>{TITLE}</span></h2>
</div>
<table class="bidding-table">
    <thead>
        <tr>
            <th>{LANG.stt}</th>
            <th>{LANG.studentn}</th>
            <th>{LANG.place}</th>
            <th>{LANG.result}</th>
            <th>{LANG.birthday}</th>
            <th>{LANG.identify_code}</th>
        </tr>
    </thead>
    <tbody>
        <!-- BEGIN: loop -->
        <tr>
            <td data-column="{LANG.stt}">
                <div>
                    {ROW.stt} 
                </div>
            </td>
            <td data-column="{LANG.studentn}">
                <div>
                    {LANG.student_name}: <a href="{ROW.link_student}">{ROW.student_name}</a> <br/>
                    {LANG.certificate_code}: {ROW.certificate_code} <br/>
                    {LANG.certificate_date}: {ROW.issuedate}
                </div>
            </td>
            <td data-column="{LANG.place}"><div><a href="{ROW.link_place}">{ROW.place}</a></div></td>
            <td data-column="{LANG.result}" class="txt-center"><div><a href="{ROW.link_result}"> {ROW.result}</a></div></td>
            <!-- BEGIN: view3 -->
            <td data-column="{LANG.birthday}" class="txt-center"><div>{ROW.birthday}</div></td>
            <td data-column="{LANG.identify_code}" class="txt-center"><div>{ROW.identify_code}</div></td>
            <!-- END: view3 -->
             <!-- BEGIN: view2 -->
            <td data-column="{LANG.birthday}/{LANG.identify_code}" class="text-center" colspan="2">
                <div>
                    <a href="javascript:void(0)" class="view__price" data-id="{ROW.id}">{TB_POINT}</a>
                    <p>
                        {LINKVIP}
                        <!-- BEGIN: popup_login -->
                        {POPUP_LOGIN}
                        <!-- END: popup_login -->
                    </p>
                </div>
            </td>
            <!-- END: view2 -->
            <!-- BEGIN: view4 -->
            <td data-column="{LANG.birthday}/{LANG.identify_code}" class="text-center" colspan="2">
                <div>
                    <a href="javascript:void(0)" class="view__price" data-id="{ROW.id}">{TB_POINT}</a>
                    <p>
                        {LINKVIP_RENEW}
                    </p>
                </div>
            </td>
            <!-- END: view4 -->
            
            <!-- BEGIN: view -->
            <td data-column="{LANG.birthday}" class="txt-center"><div>{ROW.birthday}</div></td>
            <td data-column="{LANG.identify_code}" class="txt-center"><div>{ROW.identify_code}</div></td>
            <!-- END: view -->
        </tr>
        <!-- END: loop -->
    </tbody>
</table>
<!-- BEGIN: generate_page -->
<div class="text-center">{NV_GENERATE_PAGE}</div>
<!-- END: generate_page -->
<div id="trudiem"></div>
<style>
    #trudiem {
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background: #d15c18d1;
        padding: 10px;
        color: #ffffff;
        margin: 0;
        box-shadow: 0px 5px 10px rgb(141 136 136 / 60%);
        border-radius: 2px;
        display: none;
    }

    #trudiem p {
        margin: 0;
    }
    .tooltip {
        width: 400px;
    }
</style>
<script type="text/javascript">
$(document).ready(function($) {
    $(".view__price").click(function() {
        let button = $(this); // Lưu tham chiếu đến phần tử đã kích hoạt sự kiện
        let id = button.attr('data-id');
        let $html = '';
        $html += '<td data-column="{LANG.birthday}" class="txt-center"><div><span class="birthday_' + id +'"></span></div></td>';
        $html += '<td data-column="{LANG.identify_code}" class="txt-center"><div> <span class="identify_code_' + id +'"></span></div></td>';
        
        $.ajax({
            url: location.href,
            type: 'POST',
            data: {
                'view_price': 1,
                'id': id
            },
        })
        .done(function(res) {
            try {
                let result = JSON.parse(res);
                if (result['khongdudiem'] == 1) {
                    alert("{LANG.point_miss_goods}");
                } else if (result['khongdudiem'] == 2) {
                    alert("{LANG.point_goods_err}");
                } else {   
                    button.closest('tr').append($html); // Sử dụng tham chiếu button
                    button.parent().parent().remove();
                    $(".birthday_" + result['data']['id']).html(result['data']['birthday']);
                    $(".identify_code_" + result['data']['id']).html(result['data']['identify_code']);

                    if (!$("#trudiem").length) {
                        $("body").append('<div id="trudiem"></div>');
                    }
                    
                    $("#trudiem").html(result['notifi']);
                    $("#trudiem").slideDown(500);
                    setTimeout(function() {
                        $("#trudiem").slideUp(500);
                    }, 2000);
                }
            } catch (error) {
                console.error("Lỗi xử lý dữ liệu trả về ", error);
            }
        });
    });
});
</script>

<!-- END: view -->
<!-- END: main -->