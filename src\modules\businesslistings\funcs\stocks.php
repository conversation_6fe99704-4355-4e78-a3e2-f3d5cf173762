<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_IS_MOD_BUSINESSLISTINGS'))
    die('Stop!!!');

$key_words = $module_info['keywords'];
$module_industry = $site_mods[$global_array_config['module_industry']];

$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('stocks_list'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op, true)
);

$array_exchange = ['HOSE', 'HNX', 'UPCOM'];

if (defined('NV_IS_USER')) {
    if (!empty($global_array_vip[32]) || defined('NV_IS_ADMIN')) {
        define('NV_IS_VIP32', true);
    } else {
        $check_32 = $db->query("SELECT COUNT(*) FROM " . BID_PREFIX_GLOBAL . '_customs WHERE user_id = ' . $user_info['userid'] . ' AND vip = 32 AND prefix_lang = ' . (NV_LANG_DATA == 'vi' ? 0 : 1))->fetchColumn();
        if ($check_32) {
            define('NV_IS_VIP32_RENEW', true);
        }
    }
}

$q = $nv_Request->get_title('q', 'post,get');
$location = $nv_Request->get_int('province', 'post,get', -1);
$exchange = $nv_Request->get_int('exchange', 'post,get', -1);
$order = $nv_Request->get_int('order', 'post,get', 0);
$industry = $nv_Request->get_title('industry', 'post,get', -1);
$is_advance = $nv_Request->get_int('is_advance', 'get', 0);
$bid_year_search = $nv_Request->get_int('bid_year_search', 'post,get', 0);
$bid_from = nv_substr($nv_Request->get_title('sfrom', 'get', '01/2000'), 0, 7);
$bid_to = nv_substr($nv_Request->get_title('sto', 'get', nv_date('m/Y', NV_CURRENTTIME)), 0, 7);
$is_provinceBuss = $nv_Request->get_int('is_provinceBuss', 'post,get', 1);

if (!preg_match('/^([0-9]{2})\/([0-9]{4})$/', $bid_from)) {
    $bid_from = '01/2000';
}
if (!preg_match('/^([0-9]{2})\/([0-9]{4})$/', $bid_to)) {
    $bid_to = nv_date('m/Y', NV_CURRENTTIME);
}

if (!defined('NV_IS_VIP32')) {
    $order = 0;
    $bid_year_search = 0;
    $bid_from = '01/2000';
    $bid_to = nv_date('m/Y', NV_CURRENTTIME);
}

if ($order > 5 || $order < 0) {
    $order = 0;
}

switch ($order) {
    case 1:
        $order_by = 'total_bid DESC';
        break;
    case 2:
        $order_by = 'total_bid_result DESC';
        break;
    case 3:
        $order_by = 'total_price DESC';
        break;
    case 4:
        $order_by = 'total_winning_inde DESC';
        break;
    case 5:
        $order_by = 'total_winning DESC';
        break;
    default:
        $order_by = 't3.code ASC';
        break;
}

// Lấy toàn bộ ngành hàng ICB
$arr_industry = $nv_Cache->db('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_industry WHERE status = 1', 'code', $module_name);
$sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province";
$province_list = $nv_Cache->db($sql, 'id', 'location');


$location > 0 && !isset($province_list[$location]) && $location = -1;
$exchange > -1 && $exchange > 2 && $exchange = -1;
$industry > -1 && !isset($arr_industry[$industry]) && $industry = -1;

$industry_from_url = false;
$page_title = $nv_Lang->getModule('stocks_list');
if (!empty($array_op[1])) {
    $alias = $array_op[1];
    $array_id = explode('-', $alias);
    $code = end($array_id);
    if (isset($arr_industry[$code])) {
        $industry = $code;
    }
    $_GET['industry'] = $industry;
    $_GET['is_advance'] = 1;
    $industry_from_url = true;
    $is_advance = 1;
    if ($array_op[1] == $nv_Lang->getModule('linkprovince')) {
         if (preg_match('/^([A-Za-z0-9\-]+)$/',$array_op[2], $m)){
            if ($m[0] == 'Chua-phan-loai') {
                $_temp = $location = 0;
            } else if ($m[0] == change_alias($nv_Lang->getModule('alias_vn_out_territory'))) {
                $_temp = $location = 825;
            } else if ($m[0] == change_alias($nv_Lang->getModule('alias_nationwide'))) {
                $_temp = $location = 824;
            } else {
                if (empty($_idprovince)) {
                    $_temp = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_location_province WHERE alias = ' . $db->quote($m[0]))->fetchColumn();
                    if (!empty($_temp)) {
                        $location = $_temp;
                    } else {
                       nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $nv_Lang->getModule('linkprovince') . '/' . $province_list[$_temp]['alias']);
                    }
                } else {
                    $_temp = 0;
                }
            }
        }
        if (in_array($_temp,$arr_title_province)) {
            $pro = $nv_Lang->getModule('idprovince_i') . ' ';
        } else {
            $pro = $nv_Lang->getModule('idprovince_ii') . ' ';
        }
        $province_list[825] = array(
            'id' => 825,
            'title' => $nv_Lang->getModule('vn_out_territory'),
            'alias' => change_alias($nv_Lang->getModule('alias_vn_out_territory'))
        );
        $province_list[824] = array(
            'id' => 824,
            'title' => $nv_Lang->getModule('nationwide'),
            'alias' => change_alias($nv_Lang->getModule('alias_nationwide'))
        );
        if ($_temp == 825 || $_temp == 824) {
            $pro = '';
        }
        $province_list[$_temp]['title'] = str_replace('TP.','', $province_list[$_temp]['title']);
        $page_title = $nv_Lang->getModule('stocks_list') . ' ' . $pro . $province_list[$_temp]['title'];
        $array_mod_title[] = [
            'title' => $pro . $province_list[$_temp]['title'],
            'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $nv_Lang->getModule('linkprovince') . '/' . $province_list[$_temp]['alias']
        ];
        $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $nv_Lang->getModule('linkprovince') . '/' . $province_list[$_temp]['alias'];
        $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . '/' . $nv_Lang->getModule('linkprovince') . '/' . $province_list[$_temp]['alias'];
    } else {
        $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($arr_industry[$code]['title']) . '-' . $code;
        $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . '/' . change_alias($arr_industry[$code]['title']) . '-' . $code;
    }
} else {
    $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
    $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op;
}

$where[] = 't3.is_contractor = 1';

if (!empty($q)) {
    $list_q = explode(',', $q);
    $list_q = array_slice($list_q, 0, 5);
    foreach ($list_q as $kw) {
        $kw = trim($kw);
        if (!empty($kw)) {
            $sql_q[] = 't3.content LIKE ' . $db->quote('%' . $kw . '%');
        }
    }
    !empty($sql_q) and $where[] = '(' . implode(' OR ', $sql_q) . ')';
    $_GET['q'] = implode(',', $list_q);
    $base_url .= '&amp;q=' . implode(',', $list_q);
    $global_lang_url .= '&amp;q=' . implode(',', $list_q);
}
$advance_in_url = false;
if ($is_advance) {
    // Mã ngành hàng cấp cuối cùng
    $last_code = '';
    if ($industry != -1) {
        $last_code = $industry;
        $sql_industry[] = 't3.industry LIKE ' . $db->quote('%' . $last_code . '%');
        $where[] = '(' . implode(' OR ', $sql_industry) . ')';
        if (!$industry_from_url) {
            $base_url .= '&amp;industry=' . $last_code;
            $global_lang_url .= '&amp;industry=' . $last_code;
            $advance_in_url = true;
        }
    }

    if ($location > -1 && $is_provinceBuss == 0) {
        $where[] = 't3.province = ' . $location;
        $base_url .= '&amp;province=' . $location;
        $global_lang_url .= '&amp;province=' . $location;
        $advance_in_url = true;
    }

    if ($exchange > -1) {
        $where[] = 't3.exchange = ' . $exchange;
        $base_url .= '&amp;exchange=' . $exchange;
        $global_lang_url .= '&amp;exchange=' . $exchange;
        $advance_in_url = true;
    }
}
if ($advance_in_url) {
    $base_url .= '&amp;is_advance=1';
    $global_lang_url .= '&amp;is_advance=1';
}

if ($bid_year_search) {
    $base_url .= '&amp;sfrom=' . $bid_from . '&amp;sto=' . $bid_to . '&amp;bid_year_search=1';
    $global_lang_url .= '&amp;sfrom=' . $bid_from . '&amp;sto=' . $bid_to . '&amp;bid_year_search=1';

    $bid_from = mktime(0, 0, 0, substr($bid_from, 0, 2), 1, substr($bid_from, 3, 4));
    $bid_to = mktime(0, 0, 0, substr($bid_to, 0, 2), 1, substr($bid_to, 3, 4));
    $where[] = 'UNIX_TIMESTAMP(CONCAT(t2.year, "-", t2.month, "-01")) BETWEEN ' . $bid_from . ' AND ' . $bid_to;
}

if ($order > 0) {
    $base_url .= '&amp;order=' . $order;
    $global_lang_url .= '&amp;order=' . $order;
}

$page_url = $base_url;
$page = $nv_Request->get_page('page', 'get', 1);
$page > 1 && $page_url .= '&amp;page=' . $page;
$home_page = $nv_Request->get_int('home_page', 'get', 0);
$per_page = ($home_page == 1) ? 10 : 20;

if ($exchange != -1) {
    $page_title .= ' ' . $nv_Lang->getModule('at_exchange') . ' ' . $array_exchange[$exchange];
}

$db->sqlreset();
$db->select('COUNT(DISTINCT t3.id) as count, SUM(t2.total_bid) as total_bid, SUM(t2.total_winning) as total_winning, SUM(t2.total_winning_inde) as total_winning_inde, SUM(t2.total_price) as total_price')
    ->from(BUSINESS_PREFIX_GLOBAL . '_stocks t3')
    ->join('INNER JOIN ' . BUSINESS_PREFIX_GLOBAL . '_info t1 ON t1.code = t3.business_no LEFT JOIN ' . BUSINESS_PREFIX_GLOBAL . '_static t2 ON t1.id = t2.bussiness_id')
    ->where(implode(' AND ', $where));

$num_total = $db->query($db->sql())->fetch();
$num = $num_total['count'];
if (count($where) > 1 && !defined('NV_IS_VIP32')) {
    $db->where('t3.is_contractor = 1');
    $num_total_all = $db->query($db->sql())->fetch();
    $total_bid = nv_number_format($num_total_all['total_bid'] ?? 0);
    $total_winning = nv_number_format($num_total_all['total_winning'] ?? 0);
    $total_winning_inde = nv_number_format($num_total_all['total_winning_inde'] ?? 0);
    $total_price = nv_number_format($num_total_all['total_price'] ?? 0);
} else {
    $total_bid = nv_number_format($num_total['total_bid'] ?? 0);
    $total_winning = nv_number_format($num_total['total_winning'] ?? 0);
    $total_winning_inde = nv_number_format($num_total['total_winning_inde'] ?? 0);
    $total_price = nv_number_format($num_total['total_price'] ?? 0);
}

$array_data = [];
$static = '';
if ($num > 0) {
    betweenURLs($page, ceil($num / $per_page), $base_url, '&page=', $prevPage, $nextPage);
    $db->select('t3.code, t3.id_hose, t3.list_date, t3.exchange, t3.address, t3.industry, t1.id, t1.companyname, SUM(t2.total_bid) as total_bid, SUM(t2.total_bid_result) as total_bid_result, SUM(t2.total_winning) as total_winning, SUM(t2.total_winning_inde) as total_winning_inde, SUM(t2.total_price) as total_price')
        ->from(BUSINESS_PREFIX_GLOBAL . '_stocks t3')
        ->join('INNER JOIN ' . BUSINESS_PREFIX_GLOBAL . '_info t1 ON t1.code = t3.business_no LEFT JOIN ' . BUSINESS_PREFIX_GLOBAL . '_static t2 ON t1.id = t2.bussiness_id')
        ->where(implode(' AND ', $where))
        ->group('t3.id')
        ->order($order_by)
        ->limit($per_page)
        ->offset(($page - 1) * $per_page);
    $array_data = $db->query($db->sql())->fetchAll();

    foreach ($array_data as $key => $value) {
        $array_data[$key]['link_business'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($value['companyname']) . '-' . $value['id'];

        switch ($value['exchange']) {
            case 0:
                $array_data[$key]['link_hsx'] = 'https://www.hsx.vn/Modules/Listed/Web/SymbolView?id=' . $value['id_hose'];
                break;
            case 1:
                $array_data[$key]['link_hsx'] = 'https://www.hnx.vn/vi-vn/cophieu-etfs/chi-tiet-chung-khoan-ny-' . $value['code'] . '.html?_des_tab=1';
                break;
            case 2:
                $array_data[$key]['link_hsx'] = 'https://www.hnx.vn/vi-vn/cophieu-etfs/chi-tiet-chung-khoan-uc-' . $value['code'] . '.html?_des_tab=1';
                break;
        }
        $array_data[$key]['exchange_title'] = $array_exchange[$value['exchange']];
        $array_data[$key]['link_ex'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;exchange=' . $value['exchange'] . '&amp;is_advance=1';
        if (defined('NV_IS_VIP32')) {
            $array_data[$key]['total_bid'] = nv_number_format($array_data[$key]['total_bid'] ?? 0);
            $array_data[$key]['total_price'] = nv_number_format($array_data[$key]['total_price'] ?? 0);
            $array_data[$key]['total_bid_result'] = nv_number_format($array_data[$key]['total_bid_result'] ?? 0);
            $array_data[$key]['total_winning'] = nv_number_format($array_data[$key]['total_winning'] ?? 0);
            $array_data[$key]['total_winning_inde'] = nv_number_format($array_data[$key]['total_winning_inde'] ?? 0);
        } else {
            $key_censor = '<i class="fa fa-lock" aria-hidden="true"></i>';
            $array_data[$key]['total_bid'] = $array_data[$key]['total_price'] = $array_data[$key]['total_bid_result'] = $array_data[$key]['total_winning'] = $array_data[$key]['total_winning_inde'] = $key_censor;
        }
    }
    if (!defined('NV_IS_VIP32')) {
        $error[] = $nv_Lang->getModule('plp_show_only');
    }

    $static = $nv_Lang->getModule('block_static_stocks1', $total_bid, $total_price, $total_winning, $total_winning_inde);
}

$query_params = [];
parse_str(parse_url($page_url, PHP_URL_QUERY), $query_params);
// Danh sách các tham số cần xóa
$params_to_remove = [
    NV_NAME_VARIABLE,
    NV_OP_VARIABLE,
    NV_LANG_VARIABLE
];
$keys = array_keys($query_params);
$newKeys = array_map(function($key) {
    return str_replace('amp;', '', $key);
}, $keys);
$query_params = array_combine($newKeys, $query_params);
$query_params = array_diff_key($query_params, array_flip($params_to_remove));
$has_other_query_params = false;
foreach ($query_params as $key => $value) {
    if ($key !== 'amp;page') {
        $has_other_query_params = true;
        break;
    }
}

if (!empty($q) || empty($num) || $has_other_query_params) {
    $nv_BotManager->setPrivate();
}

$canonicalUrl = getCanonicalUrl($page_url);

if ($num > $per_page) {
    $generate_page = nv_generate_page($base_url, $num, $per_page, $page);
} else {
    $generate_page = '';
}

$contents = nv_theme_businesslistings_stocks($array_data, $page, $per_page, $generate_page, $arr_industry, $error, $static);

include (NV_ROOTDIR . "/includes/header.php");
echo nv_site_theme($contents);
include (NV_ROOTDIR . "/includes/footer.php");
