<!-- BEGIN: ajax_vi -->
<PERSON><PERSON><PERSON> mừng bạn đã được tặng: <br/><br/>
<!-- BEGIN: group -->
<div class="form-group">
    <!-- BEGIN: loop -->
    {ROW.message} <br />
    <!-- END: loop -->
    <div>Thời hạn sử dụng điểm tặng đến: {EXPIRED_TIME}</div>
</div>
<!-- END: group -->
Hãy sử dụng số điểm này trước khi hết hạn.
Vui lòng truy cập <a href="{URL_CRM_SITE}{NV_LANG_INTERFACE}/points/" target="_blank"><b>Quản lý điểm</b></a> để kiểm tra số điểm đã có.
<!-- BEGIN: online10 -->
<br />
<br />
{ONLINE10}
<!-- END: online10 -->
<!-- END: ajax_vi -->
<!-- BEGIN: ajax_en -->
Congratulation! You has been rewarded: <br/><br/>
<!-- BEGIN: group -->
<div class="form-group">
    <!-- BEGIN: loop -->
    {ROW.message} <br />
    <!-- END: loop -->
    <div>Rewarded points expire on {EXPIRED_TIME}</div>
</div>
<!-- END: group -->
Use these points before they expire.
Access <a href="{URL_CRM_SITE}{NV_LANG_INTERFACE}/points/" target="_blank"><b>Point Management</b></a> to check your available points.
<!-- BEGIN: online10 -->
<br />
<br />
{ONLINE10}
<!-- END: online10 -->
<!-- END: ajax_en -->
<!-- BEGIN: main -->
<!-- START FORFOOTER -->
<div id="backdrop-reward-point" class="modal fade auto-height" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <div class="h1" style="display: inline-block;">
                    <strong>{LANG.popup_title}</strong>
                </div>
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="backdrop-reward-point-body">
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
$(window).on('load', function() {
    $.ajax({
        type: 'POST',
        url: '{AJAX_URL}',
        data: {
            nocache: new Date().getTime(),
            load_reward_point_message: '{NV_CHECK_SESSION}'
        },
        cache: false,
        success: function(respon) {
            if (respon == '') {
                return false;
            }
            $('#backdrop-reward-point-body').html(respon);
            $('#backdrop-reward-point').modal('show');
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR, textStatus, errorThrown);
        }
    });
});
</script>
<!-- END FORFOOTER -->
<!-- END: main -->
