<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_BIDDING'))
    die('Stop!!!');

use NukeViet\Dauthau\Url;

// kết nối tới ElasticSearh
$nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_host'], $module_config[$module_name]['elas_port'], NV_LANG_ELASTIC . 'dauthau_bidding', $module_config[$module_name]['elas_user'], $module_config[$module_name]['elas_pass']);

isset($l) && $l === 0 && $search_kind = 1; // Kiểu tìm kiếm 1. Khớ<PERSON> từ hoặc một số từ 2. Khớ<PERSON> tất cả từ 0.<PERSON>hớ<PERSON> chính xác cụm từ
$par_search = $par_search ?? 0; // Chỉ tìm theo tên hoặc mã
$goods_search = $goods_search ?? 0; // Tìm kiếm hàng hóa: 0. Không tìm kiếm theo hàng hóa 1.Tìm kiếm mở rộng vào tên hàng hóa 2.Chỉ tìm kiếm theo tên hàng hóa
$open_only = $open_only ?? 0; // Chỉ tìm kiếm TBMT chưa đóng thầu
$search_elastic = [];
// Phân biệt dấu hay k
if ($search_type_content == 1) {
    if ($par_search) {
        $search_fields = [
            'so_tbmt',
            'goi_thau'
        ];
    } else {
        $search_fields = [
            'content_full'
        ];
    }
} else {
    if ($par_search) {
        $search_fields = [
            'so_tbmt',
            'goi_thau_search'
        ];
    } else {
        $search_fields = [
            'content'
        ];
    }
}

if ($goods_search == 1) {
    $search_fields[] = 'content_goods';
} elseif ($goods_search == 2) {
    $search_fields = [
        'content_goods'
    ];
}
if (!empty($q)) {
    $arr_key = explode(',', $q);
    foreach ($search_fields as $f) {
        foreach ($arr_key as $key) {
            if ($search_kind >= 1) { // 1. Khớp từ hoặc một số từ 2. Khớp tất cả từ
                $arr_key_temp = explode(' ', $key);
                $tmp_search_elastic = [];
                foreach ($arr_key_temp as $_key_temp) {
                    if ($search_type_content == 0 && $f != 'content_goods') {
                        $_key_temp = str_replace('-', ' ', change_alias($_key_temp));
                    } else if ($search_type_content == 0 && $f == 'content_goods') {
                        $_key_temp = strtolower($_key_temp);
                    }
                    $_key_temp = trim($_key_temp);
                    if (empty($_key_temp)) {
                        continue;
                    }
                    if (!preg_match('/[0-9]/', $_key_temp) && $f == 'so_tbmt') {
                        continue;
                    }

                    if ($search_kind == 1) {
                        if (preg_match('/^[0-9]+?$/', $_key_temp)) {
                            // Nếu keyword chỉ là dãy số thì tìm wildcard
                            $search_elastic['should'][] = [
                                "wildcard" => [
                                    $f => [
                                        "value" => '*' . $_key_temp . '*'
                                    ]
                                ]
                            ];
                        } else {
                            $search_elastic['should'][] = [
                                "match_phrase" => [
                                    $f => $_key_temp
                                ]
                            ];
                        }
                    } else {
                        if (preg_match('/^[0-9]+?$/', $_key_temp)) {
                            // Nếu keyword chỉ là dãy số thì tìm wildcard
                            $tmp_search_elastic[] = [
                                "wildcard" => [
                                    $f => [
                                        "value" => '*' . $_key_temp . '*'
                                    ]
                                ]
                            ];
                        } else {
                            $tmp_search_elastic[] = [
                                "match_phrase" => [
                                    $f => $_key_temp
                                ]
                            ];
                        }
                    }
                }
                if (!empty($tmp_search_elastic)) {
                    $search_elastic['should'][]['bool']['must'] = $tmp_search_elastic;
                }
            } else { // 0.Khớp chính xác cụm từ
                if ($search_type_content == 0 && $f != 'content_goods') {
                    $key = str_replace('-', ' ', change_alias($key));
                } else if ($search_type_content == 0 && $f == 'content_goods') {
                    $key = strtolower($key);
                }
                $key = trim($key);
                if (empty($key)) {
                    continue;
                }
                if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                    continue;
                }
                if (preg_match('/^[0-9]+?$/', $key)) { // Nếu keyword chỉ là dãy số thì tìm wildcard
                    $search_elastic['should'][] = [
                        "wildcard" => [
                            $f => [
                                "value" => '*' . $key . '*'
                            ]
                        ]
                    ];
                } else {
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                }
            }
        }
    }
}

// Tìm kiếm theo so_tbmt
if (!empty($code)) {
    $arr_code = explode(',', $code);
    foreach ($arr_code as $key) {
        $key = explode('-', $key)[0];
        $key = trim($key);
        $search_elastic['should'][] = [
            "match" => [
                "so_tbmt" => $key . '*'
            ]
        ];
    }
}

// Tìm theo Từ khóa bổ sung
if (!empty($key_search2) and $is_advance) {
    $arr_key = explode(',', $key_search2);
    $search_bosung = [];
    if (sizeof($search_fields) > 1) {
        // Nếu số lượng trường tìm kiếm > 1 thì sẽ dùng bool must từng từ khóa
        // mỗi từ khóa thì lại should trong từng trường
        foreach ($arr_key as $key) {
            if (empty($key)) {
                continue;
            }
            $search_bosung_should = [];
            foreach ($search_fields as $f) {
                if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                    continue;
                }
                if ($search_type_content == 1) {
                    $key = trim($key);
                    $search_bosung_should[] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                    $search_bosung[] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                } else {
                    $search_bosung_should[] = [
                        "match_phrase" => [
                            $f => ($f != 'content_goods' ? trim(str_replace('-', ' ', change_alias($key))) : trim(strtolower($key)))
                        ]
                    ];
                    $search_bosung[] = [
                        "match_phrase" => [
                            $f => ($f != 'content_goods' ? trim(str_replace('-', ' ', change_alias($key))) : trim(strtolower($key)))
                        ]
                    ];
                }
            }
            if (empty($search_one_key)) {
                //Tìm theo tất cả từ khóa bổ sung
                $search_elastic['must'][]['bool']['should'] = $search_bosung_should;
            }
        }
        if (!empty($search_one_key) and !empty($search_bosung)) {
            //Một trong các từ khóa là điều kiện bắt buộc
            $search_elastic['must'][] = [
                "bool" => [
                    "should" => $search_bosung,
                    "minimum_should_match" => 1
                ]
            ];
        }
    } else {
        foreach ($search_fields as $f) {
            $search_bosung_should = [];
            foreach ($arr_key as $key) {
                if (empty($key)) {
                    continue;
                }
                if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                    continue;
                }
                if ($search_type_content == 1) {
                    $key = trim($key);
                    $search_bosung_should[] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                } else {
                    $f != 'content_goods' && $key = str_replace('-', ' ', change_alias($key));
                    $f == 'content_goods' && $key = strtolower($key);
                    $key = trim($key);
                    $search_bosung_should[] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                }
            }
            if (!empty($search_one_key)) {
                $search_elastic['must'][]['bool']['should'] = $search_bosung_should;
            } else {
                $search_elastic['must'] = $search_bosung_should;
            }
        }
    }
}

// từ khóa loại trừ
if (!empty($without_key) and $is_advance) {
    $arr_key = explode(',', $without_key);
    foreach ($search_fields as $f) {
        foreach ($arr_key as $key) {
            if (empty($key)) {
                continue;
            }
            if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                continue;
            }
            if ($search_type_content == 1) {
                $key = trim($key);
                $search_elastic['must_not'][] = [
                    "match_phrase" => [
                        $f => $key
                    ]
                ];
            } else {
                $f != 'content_goods' && $key = str_replace('-', ' ', change_alias($key));
                $f == 'content_goods' && $key = strtolower($key);
                $key = trim($key);
                $search_elastic['must_not'][] = [
                    "match_phrase" => [
                        $f => $key
                    ]
                ];
            }
        }
    }
}

// Tìm kiếm theo phân mục
if (!empty($_phanmucid) and $is_advance) {
    if ($_phanmucid[0] > 0) {
        $search_elastic['must'][] = [
            'terms' => [
                'phanmuc' => array_values($_phanmucid)
            ]
        ];
    } else {
        $search_elastic['must'][] = [
            'term' => [
                'phanmuc.keyword' => ''
            ]
        ];
    }
}

$vsic_query = [];
// Tìm kiếm theo vsic
if (!empty($vsic_code) and $is_advance) {
    foreach ($vsic_code as $v) {
        if ($v != 0) {
            $vsic_regex[] = $v;
        }
    }
    if (!empty($vsic_regex)) {
        $vsic_regex = '.*(' . implode('|', $vsic_regex) . ').*';
        $vsic_query['bool']['should'][] = [
            'regexp' => [
                'vsic.keyword' => [
                    'value' => $vsic_regex
                ]
            ]
        ];
    }
    if (in_array(0, $vsic_code)) {
        $vsic_query['bool']['should'][] = [
            'terms' => [
                'vsic.keyword' => ['', '0']
            ]
        ];
    }
    $search_elastic['must'][] = $vsic_query;
}

// Tìm kiếm theo tỉnh thành
if (!empty($_idprovince) and $is_advance && $type_search != 2) {
    $search_idprovince = array();
    if ($_idprovince[0] == 0) {
        $search_idprovince[] = [
            'term' => [
                'province_id' => 0
            ]
        ];
    } else {
        foreach ($_idprovince as $key_idp) {
            $search_idprovince[] = [
                'regexp' => [
                    'province_id' => [
                        'value' => '~[0-9]' . $key_idp . '~[0-9]',
                        'flags' => 'ALL'
                    ]
                ]
            ];
        }
    }
    $search_elastic['must'][]['bool']['should'] = $search_idprovince;
}

// Tìm kiếm theo Loại công trình
if (!empty($_idlct) and $is_advance) {
    $search_idlct = array();
    foreach ($_idlct as $key_idp) {
        $search_idlct[] = [
            'term' => [
                'work_type' => $key_idp
            ]
        ];
    }
    $search_elastic['must'][]['bool']['should'] = $search_idlct;
}

// tìm kiếm theo khoảng thời gian
if ($sfrom1 > 0 and $sto1 > 0) {
    $search_elastic['must'][]['range']['ngay_dang_tai'] = [
        "gte" => $sfrom1,
        "lte" => $sto1
    ];
}

if ($open_only) {
    $search_elastic["must"][]["range"]["den_ngay"] = [
        "gte" => NV_CURRENTTIME
    ];
}

// tìm theo số tiền đảm bảo
if ($money_from > 0 and $money_to > 0 and $is_advance) {
    $search_elastic['must'][]['range']['money_bid'] = [
        "gte" => $money_from,
        "lte" => $money_to
    ];
} else {
    if ($money_from > 0 and $is_advance) {
        $search_elastic['must'][]['range']['money_bid'] = [
            "gte" => $money_from
        ];
    }

    if ($money_to > 0 and $is_advance) {
        $search_elastic['must'][]['range']['money_bid'] = [
            "lte" => $money_to
        ];
    }
}

// tìm theo giá mời thầu
if ($price_from > 0 and $price_to > 0 and $is_advance) {
    $search_elastic['must'][]['range']['price'] = [
        "gte" => $price_from,
        "lte" => $price_to
    ];
} else {
    if ($price_from > 0 and $is_advance) {
        $search_elastic['must'][]['range']['price'] = [
            "gte" => $price_from
        ];
    }
    if ($price_to > 0 and $is_advance) {
        $search_elastic['must'][]['range']['price'] = [
            "lte" => $price_to
        ];
    }
}
// tìm kiếm theo hình thức dự thầu
if ($cat > 0 and $is_advance) {
    $search_elastic['must'][] = [
        'match' => [
            'type_bid' => [
                'query' => $cat == 1 ? "1" : "0"
            ]
        ]
    ];
}

// tìm kiếm theo loại doanh nghiệp
if ($type_org == 2 and $is_advance) {
    $search_elastic['must'][] = [
        'match' => [
            'type_org' => [
                'query' => "1"
            ]
        ]
    ];
}
if ($id_hinhthucluachon == 2 and $is_advance) {
    $search_isDomestic = [];
    $search_isDomestic[] = [
        'match' => [
            'id_hinhthucluachon' => [
                'query' => $id_hinhthucluachon
            ]
        ]
    ];
    // thêm id_hinhthucluachon do isDomestic msc mới là 0
    $search_isDomestic[] = [
        'match' => [
            'id_hinhthucluachon' => [
                'query' => 0
            ]
        ]
    ];
    $search_elastic['must'][]['bool']['should'] = $search_isDomestic;
}
if (((!empty($type_choose_id) and $is_advance) or (!empty($type_choose_id) and $type_choose_id == 42)) and $type_search == 1) {
    $search_elastic['must'][] = [
        'match' => [
            'type_choose_id' => [
                'query' => $type_choose_id
            ]
        ]
    ];
}

// mã nhà mời thầu
if ($solicitor_id > 0) {
    $search_elastic['must'][] = [
        'match' => [
            'solicitor_id' => [
                'query' => $solicitor_id
            ]
        ]
    ];
}

if (defined("NV_IS_VIP5")) {
    // kiểm tra theo tùy chọn
    // - theo vốn khác
    // - theo tất cả lĩnh vực có từ khóa
    if ($vip5_open == 1) {
        $search_elastic['must'][] = [
            'term' => [
                'is_vip5' => 1
            ]
        ];
    } else {
        $search_elastic['must'][] = [
            'term' => [
                'pham_vi' => 1
            ]
        ];
    }

    if (!empty($field) and $is_advance) {
        $search_field = array();
        foreach ($field as $key) {
            if ($key != 20) {
                $search_field[] = [
                    'term' => [
                        'linh_vuc_thong_bao' => $key
                    ]
                ];
            }
        }
        $search_elastic['must'][]['bool']['should'] = $search_field;
    }
} else {
    // tim kiếm theo lĩnh vực thông báo
    if (!empty($field) and $is_advance) {
        $search_field = array();
        foreach ($field as $key) {
            if ($key != 20) {
                $search_field[] = [
                    'term' => [
                        'linh_vuc_thong_bao' => $key
                    ]
                ];
            }
        }

        $search_elastic['must'][]['bool']['should'] = $search_field;
    }

    if (defined('NV_IS_USER') and ($vip == 1 || $vip == 19 || $vip == 99)) {
        $search_elastic['must_not'][] = [
            'term' => [
                'pham_vi' => 1
            ]
        ];
    }
}

// tim kiếm theo phương thức
if (!empty($phuongthuc) and $is_advance) {
    $search_field = array();
    foreach ($phuongthuc as $key) {
        $search_field[] = [
            'term' => [
                'phuong_thuc_num' => $key
            ]
        ];
    }
    $search_elastic['must'][]['bool']['should'] = $search_field;
}

if (!empty($search_elastic['should'])) {
    $search_elastic['minimum_should_match'] = '1';
    $search_elastic['boost'] = '1.0';
}
if (!empty($search_elastic['must'])) {
    foreach ($search_elastic['must'] as $k => $v) {
        if (!empty($v['bool']['should'])) {
            $search_elastic['must'][$k]['bool']['minimum_should_match'] = '1';
            $search_elastic['must'][$k]['bool']['boost'] = '1';
        }
    }
}

$array_query_elastic = array();
if (!empty($search_elastic)) {
    $array_query_elastic['query']['bool'] = $search_elastic;
}
$array_query_elastic['track_total_hits'] = 'true';
$array_query_elastic['size'] = $per_page;
$array_query_elastic['from'] = ($page - 1) * $per_page;
$array_query_elastic['sort'] = [
    [
        "ngay_dang_tai" => [
            "order" => "desc"
        ]
    ]
];
try {
    $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_row', $array_query_elastic);
} catch (Exception $e) {
    file_put_contents(NV_ROOTDIR . '/data/logs/detailelastic_log_' . date('Ymd') . '.txt', "\n\n ---------- [" . date('d-m-Y H:i:s') . "] Lỗi detailelastic_log_: \n" . print_r($e, true), FILE_APPEND);
    file_put_contents(NV_ROOTDIR . '/data/logs/detailelastic_log_' . date('Ymd') . '.txt', "\n array_query_elastic: " . print_r($array_query_elastic, true), FILE_APPEND);
    trigger_error(print_r($e, true));
}
$num_items = $response['hits']['total']['value'];

foreach ($response['hits']['hits'] as $value) {
    if (!empty($value['_source'])) {
        $view = $value['_source'];

        $view['den_ngay'] = $view['den_ngay'] > 0 ? $TBMT::time_display($view['den_ngay']) : '';
        $view['get_time'] = $view['get_time'] > 0 ? $TBMT::time_display($view['get_time']) : '';
        $view['ngay_dang_tai'] = $view['ngay_dang_tai'] ? $TBMT::time_display($view['ngay_dang_tai']) : '';
        $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['view'] . '/' . ($type_search == 1 ? Url::getTBMT() : Url::getTBMDT()) . '/' . $view['alias'] . '-' . $view['id'] . $global_config['rewrite_exturl'];
        $view['link_search'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=detail&solicitor_id=' . $view['solicitor_id'];

        $array_data[$view['id']] = $view;
        $arr_solicitor_id[$view['solicitor_id']] = $view['solicitor_id'];
    }
}

if ((!empty($q) or !empty($key_search2) or !empty($without_key)) and $page == 1) {
    $nukeVietElasticSearh2 = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_result_host'], $module_config[$module_name]['elas_result_port'], 'bids_rows', $module_config[$module_name]['elas_result_user'], $module_config[$module_name]['elas_result_pass']);

    // Khởi tạo cấu trúc query
    $array_query_elastic = [
        'query' => [
            'bool' => [
                'must' => [],
                'should' => []
            ]
        ],
        'size' => 5,
        'sort' => [
            [
                'add_time' => [
                    'order' => 'desc'
                ]
            ]
        ]
    ];

    // Tìm kiếm theo khoảng thời gian
    if ($sfrom1 > 0 and $sto1 > 0) {
        $array_query_elastic['query']['bool']['must'][] = [
            'range' => [
                'add_time' => [
                    'gte' => $sfrom1,
                    'lte' => $sto1
                ]
            ]
        ];
    }

    $array_query_elastic['query']['bool']['must'][] = [
        'range' => [
            'pub_time' => [
                'gt' => 0,
                'lte' => NV_CURRENTTIME
            ]
        ]
    ];

    // Thêm điều kiện status
    $array_query_elastic['query']['bool']['must'][] = [
        'bool' => [
            'should' => [
                [
                    'match' => [
                        'status' => [
                            'query' => 1
                        ]
                    ]
                ],
                [
                    'match' => [
                        'status' => [
                            'query' => 2
                        ]
                    ]
                ]
            ]
        ]
    ];

    $array_query_elastic['query']['bool']['must'][] = [
        'match' => [
            'is_last' => [
                'query' => 1
            ]
        ]
    ];

    $array_query_elastic['query']['bool']['must'][] = [
        'match' => [
            'del_time' => [
                'query' => 0
            ]
        ]
    ];

    $array_query_elastic['query']['bool']['must'][] = [
        'match' => [
            'status_test' => [
                'query' => '0'
            ]
        ]
    ];

    // Tìm kiếm theo từ khóa chính
    if (!empty($q)) {
        $arr_key = explode(',', $q);
        foreach ($arr_key as $key) {
            if ($search_type_content == 1) {
                $array_query_elastic['query']['bool']['should'][] = [
                    'match_phrase' => [
                        'searchtext_full' => trim($key)
                    ]
                ];
            } else {
                $array_query_elastic['query']['bool']['should'][] = [
                    'match_phrase' => [
                        'searchtext_simple' => trim(str_replace('-', ' ', change_alias($key)))
                    ]
                ];
            }
        }
    }

    // Tìm theo Từ khóa bổ sung
    if (!empty($key_search2)) {
        $arr_key = explode(',', $key_search2);
        foreach ($arr_key as $key) {
            if ($search_type_content == 1) {
                $array_query_elastic['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_full' => trim($key)
                    ]
                ];
            } else {
                $array_query_elastic['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_simple' => trim(str_replace('-', ' ', change_alias($key)))
                    ]
                ];
            }
        }
    }

    // Tìm theo từ khóa loại trừ
    if (!empty($without_key)) {
        $array_query_elastic['query']['bool']['must_not'] = [];
        $arr_key = explode(',', $without_key);
        foreach ($arr_key as $key) {
            if ($search_type_content == 1) {
                $array_query_elastic['query']['bool']['must_not'][] = [
                    'match_phrase' => [
                        'searchtext_full' => trim($key)
                    ]
                ];
            } else {
                $array_query_elastic['query']['bool']['must_not'][] = [
                    'match_phrase' => [
                        'searchtext_simple' => trim(str_replace('-', ' ', change_alias($key)))
                    ]
                ];
            }
        }
    }

    if (!empty($array_query_elastic['query']['bool']['should'])) {
        $array_query_elastic['query']['bool']['minimum_should_match'] = '1';
        $array_query_elastic['query']['bool']['boost'] = '1.0';
    }

    try {
        $response_bids = $nukeVietElasticSearh2->search_data($db_config['prefix'] . '_' . $module_data . '_rows', $array_query_elastic);
        foreach ($response_bids['hits']['hits'] as $value) {
            if (!empty($value['_source'])) {
                $view = $value['_source'];
                $array_data_bids[$view['id']] = $view;
            }
        }
    } catch (Exception $e) {
        file_put_contents(NV_ROOTDIR . '/data/logs/detailelastic_log_bids_rows_' . date('Ymd') . '.txt', "\n\n ---------- [" . date('d-m-Y H:i:s') . "] Lỗi detailelastic_log_bids_: \n" . print_r($e, true), FILE_APPEND);
        file_put_contents(NV_ROOTDIR . '/data/logs/detailelastic_log_bids_rows_' . date('Ymd') . '.txt', "\n array_query_elastic_bids: " . print_r($array_query_elastic, true), FILE_APPEND);
        trigger_error(print_r($e, true));
    }
}
