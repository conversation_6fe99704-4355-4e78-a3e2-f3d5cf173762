<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 02:25:16 GMT
 */

if (!defined('NV_MAINFILE'))
    die('Stop!!!');

$lang_translator['author'] = "VINADES.,JSC (<EMAIL>)";
$lang_translator['createdate'] = "15/09/2011, 02:25";
$lang_translator['copyright'] = "@Copyright (C) 2011 VINADES.,JSC. All rights reserved";
$lang_translator['info'] = "";
$lang_translator['langtype'] = "lang_module";

$lang_module['sort_name'] = 'Short name';
$lang_module['search'] = 'Search';
$lang_module['organization'] = 'Verified construction organizations list';
$lang_module['stt'] = 'No.';
$lang_module['thong_tin_to_chuc'] = 'Information of Organization';
$lang_module['thong_tin_chung_chi'] = 'Certificates information';

$lang_module['heading2_dvkd'] = 'List of organizations that have been approved by %s';
$lang_module['heading2_no_dvkd'] = 'The organization list has not been classified by any moderator';
$lang_module['heading2_province_dvkd'] = 'List of organizations in %s has been reviewed by %s';
$lang_module['heading2_province_no_dvkd'] = 'The organization list at %s has not been classified by any moderator';

$lang_module['description'] = 'List of approved construction businesses, companies and contractors. These construction organizations have full information about representatives, tax codes, certificate codes,...';
$lang_module['description_dvkd'] = 'List of businesses, companies and construction contractors that have been verified by %s. These construction organizations have full information about representatives, tax codes, certificate codes,...';
$lang_module['description_province'] = 'List of businesses, companies and construction contractors in province %s that have been verified.
These construction organizations have full information about representatives, tax codes, certificate codes,...';
$lang_module['description_province_dvkd'] = 'List of businesses, companies and construction contractors of province/city %s that have been verified by %s.
These construction organizations have full information about representatives, tax codes, certificate codes,...';
$lang_module['description_no_dvkd'] = 'List of businesses, companies and construction contractors that have not been classified by any censorship unit. These construction organizations have full information about representatives, tax codes, certificate codes,...';
$lang_module['description_province_no_dvkd'] = 'List of businesses, companies and construction contractors of province/city %s that have not been classified by any censorship unit.
These construction organizations have full information about representatives, tax codes, certificate codes,...';

$lang_module['ten_to_chuc'] = 'Organization\'s name';
$lang_module['nguoi_dai_dien'] = 'Representative';
$lang_module['ma_so_thue'] = 'Tax code/ Establish decision no.';
$lang_module['dia_chi_tru_so'] = 'Address';
$lang_module['ma_chung_chi'] = 'Certificate code';
$lang_module['chi_tiet_to_chuc'] = 'Organization\'s detail';
$lang_module['xem_chi_tiet_to_chuc'] = 'See organization\'s detail';

$lang_module['note_max_searchpage'] = 'The number of search results is exceeding the software\'s display limit of 100 pages. Please use the search tool to narrow your search or return to the previous page.';
$lang_module['note_wrong_page'] = 'Where did you find this link? Blimey! It is not possible to proceed this request!';
$lang_module['notice'] = 'Notice';
$lang_module['back'] = 'Back';
$lang_module['so_chung_chi'] = 'Number of certificates';
$lang_module['chi_tiet_to_chuc'] = 'Organization\'s detail';
$lang_module['linh_vuc_hoat_dong'] = 'Field of activity';
$lang_module['linh_vuc'] = 'Category';
$lang_module['linh_vuc_mo_rong'] = 'Extended category';
$lang_module['hang'] = 'Rank';
$lang_module['ngay_het_han'] = 'Expired date';
$lang_module['ma_chung_chi_nang_luc'] = 'Certificate of Capacity in construction';
$lang_module['dia_chi_tru_so_chinh'] = 'Address of Head quarter';
$lang_module['dia_chi_vp'] = 'Address of representative office, branch office';
$lang_module['nguoi_dai_dien_theo_phap_luat'] = 'Legal representative';
$lang_module['ngay_cap'] = 'Certification date';
$lang_module['co_quan_cap'] = 'Granting agencies';
$lang_module['chuc_vu'] = 'Position';
$lang_module['tinh'] = 'Province';
$lang_module['khong_co_ket_qua'] = 'The requested results were not found, please change the search parameters.';
$lang_module['time_query'] = '<br/>Search in: %s - Number of results:  %s';

$lang_module['info'] = 'Info';
$lang_module['info_redirect_click'] = 'Click here if you feel like a long wait';
$lang_module['info_login'] = 'You need to login or register an account to get access to this area. The system will redirect you to the login section in a moment.';
$lang_module['cap_nhat_lan_cuoi'] = 'Last update';
$lang_module['cap_nhat_lai'] = '(Re-update)';
$lang_module['cap_nhat_lan_dau'] = 'This is the first update';
$lang_module['cap_nhat_lan_thu'] = '%s update at %s';
$lang_module['update_err'] = 'Failed to update.';
$lang_module['update_err_new'] = 'This information has just been update to our system. Please wait for 30 minutes if you want to reupdate it.';
$lang_module['update_ok'] = 'The operation is success. Please wait for about 10 minutes for the information to be updated.';
$lang_module['update_err_user_last'] = 'Get back after %s';
$lang_module['crawl_time'] = 'Found';

$lang_module['is_solicitor'] = 'This construction organization is also the Procuring Entity. The results of data analysis for the procuring entity <a href="%s"> <strong>%s</strong> </a> are as follows: <blockquote class=list><ul><li>Published contractor selection plan of <strong>%s</strong> project with total <strong>%s</strong> bidding packages.</li><li>Invitation for bids <strong>%s </strong> package (with <strong>%s</strong> bidding notice), conduct prequalification <strong>%s</strong> package.</li><li>Results announced of <strong>%s</strong> packages, cancel bids <strong>%s</strong> packages (among the packages above).</li><li>Yes <strong>%s</strong > packages with results without Tender/Prequalified notification .</li></ul><footer>DauThau.info software synthesizes and analyzes information from the national bidding database</footer></ blockquote>';
$lang_module['is_contractor'] = '<p>This construction organization is also a contractor. The results of data analysis for contractors <a href="%s"><strong>%s</strong></a> are as follows:</p>';
$lang_module['profile_dtnet'] = 'Business Profile';
$lang_module['profile_dtnet_title'] = 'Business Profile on DauThau.Net';
$lang_module['province_select'] = 'Select Province';
$lang_module['keyword'] = 'Keyword';
$lang_module['confirm_not_user1'] = 'To view information please <strong><a href="javascript:void(0)" data-toggle="loginFormShow">Login</a></strong> or < strong><a href="%s">Register</a></strong> if you don\'t have an account.
Signing up is simple and completely free.';
$lang_module['crawl_request_history'] = 'Request history';
$lang_module['request_time'] = 'Request time';
$lang_module['username'] = 'Account';

$lang_module['title_resg_vip'] = 'Sign up for the VIP3 package';
$lang_module['title_renew_vip'] = 'Renew VIP3 package';
$lang_module['title_view_info'] = "To view full information";
$lang_module['sub_for_any_vip'] = 'You need to <strong><a href="/en/vip/?plan=1">subscribe for any software package</a></strong> to view all informations.';
$lang_module['log_in_up'] = 'You need to <strong><a href="%s">Login</a></strong> or <strong><a href="%s">Sign up</a></strong> to view all informations.';
$lang_module['no_title'] = 'Uncategorized';
$lang_module['moderator'] = 'Moderator';
$lang_module['moderator_select'] = 'Select Moderator';
$lang_module['statics'] = 'DauThau.info found: <ul style="list-style: disc;"><li><strong>%s</strong> organizations have been moderated</li><li><strong>%s</strong> organizations are no longer visible (see details <a href="%s">here</a>)</li></ul>';
$lang_module['statics_no_login'] = 'DauThau.info found: <ul style="list-style: disc;"><li><strong>%s</strong> organizations have been moderated</li><li><strong>%s</strong> organizations are no longer visible (To view details, please <a href="javascript:void(0)" data-toggle="loginFormShow">Login</a>)</li></ul>';
$lang_module['recapcha_title'] = 'Want to update again?';
$lang_module['recapcha_body'] = 'Please confirm you are not a robot!<br />';
$lang_module['updates'] = 'Updates';
$lang_module['title_change_tb'] = 'View changes';
$lang_module['title_label_change'] = '- <b>Change:</b>';
$lang_module['tooltip_tb'] = 'Please select 2 updates to see the change';
$lang_module['kieusosanh'] = 'Select comparison type';
$lang_module['title_muti'] = 'Parallel 2 column mode';
$lang_module['title_one'] = '1 column mode';
$lang_module['title_linebyline'] = 'See line-by-line changes';
$lang_module['title_organ'] = 'Compare the history of changes in the organizational';
$lang_module['close'] = 'Close';
$lang_module['title_post_first'] = 'First found';
$lang_module['title_label_change'] = '- <b>Change:</b>';
$lang_module['list_oran_not_show'] = 'Organization does not show in the moderated list';
$lang_module['header_orang_not_show'] = 'Organization list not shown in moderated list';
$lang_module['warm_dup_mcc'] = 'The certificate code of this organization matches that of organization <a href="%s">%s</a>. Please <a href="%s">click here</a> to view details of the duplicate organization.</p>';
$lang_module['warm_organ'] = 'This organization is no longer visible in the list of moderated organizations.';
$lang_module['heading2_province'] = 'List of organizations in %s has been moderated';
