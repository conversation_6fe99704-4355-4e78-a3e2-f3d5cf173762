<!-- BEGIN: main -->
<form id="form_search_org" action="{FORM_ACTION}" method="get" onsubmit="return submitFormSearch(this);">
    <!-- BEGIN: no_rewrite -->
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
    <!-- END: no_rewrite -->
    <div class="row mt-1">
        <div class="form-row">
            <input type="hidden" id="has_param_submit" value="0">
            <div class="form-group col-md-8">
                <label for="">{LANG.keyword}</label>
                <input type="text" class="form-control" name="q" value="{Q}" oninput="updateActionForm();" placeholder="{LANG.keyword}">
            </div>
            <div class="form-group col-md-8">
                <label for="">{LANG.moderator_select}</label>
                <select onchange="updateActionForm()" id="censorship" name="censorship" class="form-control">
                    <option value="-1" selected>{LANG.moderator_select}</option>
                    <!-- BEGIN: censorship -->
                    <option value="{CENSORSHIP.id}" {CENSORSHIP.selected} data-alias="{CENSORSHIP.alias}">{CENSORSHIP.title}</option>
                    <!-- END: censorship -->
                </select>
            </div>
            <div class="form-group col-md-8">
                <label for="">{LANG.province_select}</label>
                <select onchange="updateActionForm()" id="location_tochuc" name="province_id" class="form-control">
                    <option value="-1" selected>{LANG.province_select}</option>
                    <!-- BEGIN: province -->
                    <option value="{ARR_PROVINCE.id}" {ARR_PROVINCE.selected} data-alias="{ARR_PROVINCE.alias}">{ARR_PROVINCE.title}</option>
                    <!-- END: province -->
                </select>
            </div>
            <div class="form-group col-md-8">
                <select id="type_tochuc" name="type_data" class="form-control type_tochuc" onchange="updateActionForm()">
                    <!-- BEGIN: type_data -->
                    <option value="{TYPE_DATA.id}" {TYPE_DATA.selected}>{TYPE_DATA.value}</option>
                    <!-- END: type_data -->
                </select>
            </div>
            <div class="form-group col-md-4">
                <input class="btn btn-primary btn-block btn-search-tc" type="submit" value="{LANG.search}">
            </div>
        </div>
    </div>
</form>
<script>
    updateActionForm();
    function submitFormSearch(data) {
        var has_param_submit = parseInt($('#has_param_submit').val());
        if (has_param_submit === 1) {
            return false;
        } else {
            $('#form_search_org').find('input[name="q"], select[name="censorship"], select[name="province_id"]').each(function() {
                var field = $(this);
                // Nếu trường không có giá trị thì xóa thuộc tính name
                if (field.val().trim() === "" || field.val().trim() === "-1") {
                    field.removeAttr('name');
                }
            });
            return true;
        }
    }
    function updateActionForm () {
        $('#type_tochuc').attr('name', 'type_data');
        var type_tochuc = $('#type_tochuc').val();
        var province_id = $('#location_tochuc').val();
        var q = $('input[name="q"]').val();
        var censorship = $('#censorship').val();
        var alias_loc = $('#location_tochuc').children('option:selected').attr('data-alias');
        var alias_cen = $('#censorship').children('option:selected').attr('data-alias');
        var link = '';
        if (province_id == '-1' && censorship != '-1' && type_tochuc == 1) {
            // Chỉ tìm kiếm theo đơn vị kiểm duyệt
            if (q != '') {
                link = '?q=' + encodeURIComponent(q);
            }
            $('#form_search_org').attr('action', '/' + '{MODULE_NAME}' + '/censorship/' + alias_cen + link);
            $('#has_param_submit').val(1);
        } else if (province_id != '-1' && censorship == '-1' && type_tochuc == 1) {
            // Chỉ tìm kiếm theo tỉnh/tp
            if (q != '') {
                link = '?q=' + encodeURIComponent(q);
            }
            $('#form_search_org').attr('action', '/' + '{MODULE_NAME}' + '/listlocation/' + alias_loc + link);
            $('#has_param_submit').val(1);
        } else if (type_tochuc == 2) {
            $('#type_tochuc').removeAttr('name', 'type_data');
            if (province_id == '-1' && censorship == '-1') {
                if (q != '') {
                    link = '?q=' + encodeURIComponent(q);
                }
                $('#has_param_submit').val(1);
            } else {
                $('#has_param_submit').val(0);
            }
            $('#form_search_org').attr('action', '{FORM_ACTION_UNSHOW}' + link);
        } else {
            $('#type_tochuc').removeAttr('name', 'type_data');
            $('#form_search_org').attr('action', '/' + '{MODULE_NAME}');
            if (q == '' && province_id == '-1' && censorship == '-1') {
                $('#has_param_submit').val(1);
            } else {
                $('#has_param_submit').val(0);
            }

        }
    }
    $(function () {
        $('.btn-search-tc').click(function(e) {
            var has_param_submit = parseInt($('#has_param_submit').val());
            var action_form = $('#form_search_org').attr("action");
            if (has_param_submit === 1) {
                window.location.href = action_form;
            }
        });
    });
</script>
<!-- END: main -->
