<?php
/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 10 Nov 2020 06:56:08 GMT
 */
if (!defined('NV_IS_MOD_DAU_GIA')) {
    die('Stop!!!');
}
// Chỉ thành viên hoặc Bot mới vào đây được
/*
 * if (!(defined('NV_IS_USER') or $client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
 * $url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=users&amp;' . NV_OP_VARIABLE . '=login&amp;nv_redirect=' . nv_redirect_encrypt($client_info['selfurl']), true);
 * $contents = nv_theme_alert($nv_Lang->getModule('info'), $nv_Lang->getModule('info_login'), 'info', $url, $nv_Lang->getModule('info_redirect_click'), 5);
 * include NV_ROOTDIR . '/includes/header.php';
 * echo nv_site_theme($contents);
 * include NV_ROOTDIR . '/includes/footer.php';
 * }
 *
 * // thành viên thì hiển thị thông báo đóng cửa, chỉ cho view, vip xem chi tiết
 * if ($type_user == 1 and !$client_info['is_bot']) {
 * $contents = '<div class="alert alert-warning"><div style="text-align: justify;"><strong>1.&nbsp;Từ ngày 27/04/2021</strong>, DauThau.info sẽ <a href="/news/tin-tuc/thong-bao-ngung-cung-cap-dich-vu-tra-cuu-thong-tin-thau-mien-phi-cho-cac-tai-khoan-dang-ky-mien-phi-367.html"><strong>ngừng cung cấp dịch vụ tra cứu thông tin thầu miễn phí</strong></a> cho các tài khoản đăng ký miễn phí trên dauthau.asia <strong>để tránh bị quy kết là cung cấp thông tin không đầy đủ</strong>. Nhà thầu&nbsp;muốn xem thông tin gói thầu cần <strong><a href="/vip/?plan=1">đăng ký mua gói phần mềm VIEWEB hoặc gói phần mềm VIP</a></strong>.<br><strong>2. Từ ngày 06/05/2021</strong>, DauThau.info sẽ áp dụng bảng giá mới do&nbsp;thay đổi về chính sách của cơ quan quản lý nhà nước không cho quét dữ liệu tự động dẫn tới các thay đổi về chi phí&nbsp;bảo trì và phát triển Phần mềm “săn” thông tin thầu DauThau.info. Chi tiết&nbsp;xem <a href="/news/blog/nang-cap-chuyen-sang-nhap-lieu-thu-cong-bang-gia-moi-369.html"><strong>tại đây</strong></a>.</div></div>';
 * include NV_ROOTDIR . '/includes/header.php';
 * echo nv_site_theme($contents);
 * include NV_ROOTDIR . '/includes/footer.php';
 * }
 */

$query = $db->query('SELECT * FROM nv4_dau_gia_deparment WHERE depofjusticeid=' . $id_dep);
$array_data = $query->fetch();
if (!empty($array_data)) {
    $page_title = $array_data['fullname'];
    $array_mod_title[] = array(
        'title' => $nv_Lang->getModule('department'),
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=department', true)
    );
    $array_mod_title[] = array(
        'title' => $array_data['fullname'],
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=department/' . change_alias($array_data['fullname']) . '-' . $array_data['depofjusticeid'] . '.html', true)
    );

    // Lấy tên tỉnh/thành
    $array_data['name_province'] = $db->query('SELECT title FROM '.NV_PREFIXLANG . '_location_province WHERE id=' . $array_data['id_province'])->fetchColumn() ?? '';

    // lấy các bid của tổ chức đấu giá này
    $query = $db->query('SELECT * FROM nv4_dau_gia_bidder WHERE id_dep=' . $id_dep . ' ORDER BY id_bidder DESC');
    $arr_bid = [];
    while ($row = $query->fetch()) {
        $row['url'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization/' . change_alias($row['name_bidder']) . '-' . $row['id_bidder'] . '.html';
        $row['full_address'] = build_full_address($row);
        $arr_bid[$row['id_bidder']] = $row;
    }

    $page_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=department/' . change_alias($array_data['fullname']) . '-' . $array_data['depofjusticeid'] . '.html';
    $canonicalUrl = getCanonicalUrl($page_url);

    $contents = nv_theme_dau_gia_departmentdetail($array_data, $arr_bid);

    // Schema: GovernmentOffice
    $nv_schemas[] = [
        '@context' => 'https://schema.org',
        '@type' => 'GovernmentOffice',
        'identifier' => $canonicalUrl,
        'name' => $array_data['fullname'],
        'alternateName' => $array_data['licenseno'],
        'foundingDate' => $array_data['licensedate'],
        'telephone' => $array_data['phonenumber'],
        'faxNumber' => $array_data['faxnumber'],
        'email' => $array_data['email'],
        'address' => [
            '@type' => 'PostalAddress',
            'streetAddress' => $array_data['address'],
            'addressLocality' => $db->query("SELECT title FROM " . $db_config['prefix'] . "_vi_location_district WHERE id=" . $array_data['id_district'])->fetchColumn(),
            'addressRegion' => $db->query("SELECT title FROM " . $db_config['prefix'] . "_vi_location_province WHERE id=" . $array_data['id_province'])->fetchColumn(),
            'addressCountry' => 'VN'
        ],
        'url' => $array_data['website'],
        'employee' => [
            '@type' => 'Person',
            'name' => $array_data['director'],
            'jobTitle' => $array_data['roleinfo']
        ]
    ];

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
} else {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    die();
}
