<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_BIDDING'))
    die('Stop!!!');

use NukeViet\Dauthau\Url;

// kết nối tới ElasticSearh
$nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_result_host'], $module_config[$module_name]['elas_result_port'], NV_LANG_ELASTIC . 'dauthau_result', $module_config[$module_name]['elas_result_user'], $module_config[$module_name]['elas_result_pass']);

isset($l) && $l === 0 && $search_kind = 1;
$goods = $goods ?? 0;
$search_elastic = [];
if ($search_type_content == 1) {
    if ($par_search) {
        $search_fields = ['code', 'title'];
    } else {
        $search_fields = ['content_full'];
    }
} else {
    if ($par_search) {
        $search_fields = ['code', 'title_search'];
    } else {
        $search_fields = ['content'];
    }
}

// Tìm kiếm theo tỉnh thành
if (!empty($_idprovince) and $is_advance && $type_search != 2) {
    $search_idprovince = array();
    if ($_idprovince[0] == 0) {
        $search_idprovince[] = [
            'term' => ['id_province' => 0]
        ];
    } else {
        foreach ($_idprovince as $key_idp) {
            $search_idprovince[] = [
                'regexp' => [
                    'id_province' => [
                        'value' => '~[0-9]' . $key_idp . '~[0-9]',
                        'flags' => 'ALL'
                    ]
                ]
            ];
        }
    }
    $search_elastic['must'][]['bool']['should'] = $search_idprovince;
}

if ($goods == 1) {
    $search_fields[] = 'content_goods';
} elseif ($goods == 2) {
    $search_fields = ['content_goods'];
}

// tìm kiếm theo từ khóa chính
if (!empty($q)) {
    $arr_key = explode(',', $q);
    if (!empty($search_kind)) {
        $arr_key = array_map(function ($a) {return explode(' ', $a);}, $arr_key);
        $arr_key = new RecursiveIteratorIterator(new RecursiveArrayIterator($arr_key));
    }
    $keyword_type = $search_kind <= 1 ? 'should' : 'must';
    foreach($search_fields as $f) {
        foreach ($arr_key as $key) {
            if (empty($key)){
                continue;
            }
            if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                continue;
            }
            if ($search_type_content == 0) {
                if ($f == 'content_goods') {
                    $key = strtolower($key);
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                }
            }
            $key = trim($key);
            if (preg_match('/^[0-9]*?$/', $key)) { // Nếu keyword chỉ là dãy số thì tìm wildcard
                $search_elastic[$keyword_type][] = [
                    "wildcard" => [
                        $f => [
                            "value" => '*' . $key . '*'
                        ]
                    ]
                ];
            } else {
                $search_elastic[$keyword_type][] = [
                    "match_phrase" => [
                        $f => $key
                    ]
                ];
            }
        }
    }
}

// Tìm kiếm theo so_tbmt
if (!empty($code)) {
    $arr_code = explode(',', $code);
    foreach ($arr_code as $key) {
        $key = explode('-', $key)[0];
        $key = trim($key);
        $search_elastic['should'][] = [
            "match" => [
                "code" => $key . '*'
            ]
        ];
    }
}

// Tìm theo Từ khóa bổ sung
if (!empty($key_search2) and $is_advance) {
    $arr_key = explode(',', $key_search2);
    $search_bosung_should = [];
    foreach($search_fields as $f) {
        foreach ($arr_key as $key) {
            if (empty($key)){
                continue;
            }
            if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                continue;
            }
            if (!empty($search_one_key)) {
                //Một trong các từ khóa là điều kiện bắt buộc
                if ($search_type_content == 1) {
                    $key = trim($key);
                    $search_bosung_should[] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = trim($key);
                    $search_bosung_should[] = [
                        "match_phrase" => [
                            $f => ($f != 'content_goods' ? $key : strtolower($key))
                        ]
                    ];
                }
            } else {
                if ($search_type_content == 1) {
                    $key = trim($key);
                    $search_elastic['must'][] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = trim($key);
                    $search_elastic['must'][] = [
                        "match_phrase" => [
                            $f => ($f != 'content_goods' ? $key : strtolower($key))
                        ]
                    ];
                }
            }
        }
    }
    if (!empty($search_bosung_should)) {
        $search_elastic['must'][] = [
            "bool" => [
                "should" => $search_bosung_should,
                "minimum_should_match" => 1
            ]
        ];
    }
}
// từ khóa loại trừ
if (!empty($without_key) and $is_advance) {
    $arr_key = explode(',', $without_key);
    foreach($search_fields as $f) {
        foreach ($arr_key as $key) {
            if (empty($key)){
                continue;
            }
            if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                continue;
            }
            if ($search_type_content == 1) {
                $key = trim($key);
                $search_elastic['must_not'][] = [
                    "match_phrase" => [
                        $f => $key
                    ]
                ];
            } else {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                if ($f == 'content_goods') {
                    $key = strtolower($key);
                }
                $search_elastic['must_not'][] = [
                    "match_phrase" => [
                        $f => $key
                    ]
                ];
            }
        }
    }
}

// tìm kiếm theo khoảng thời gian
if ($sfrom1 > 0 and $sto1 > 0) {
    $search_elastic['must'][]['range']['finish_time'] = [
        "gte" => $sfrom1,
        "lte" => $sto1
    ];
}

// mã nhà mời thầu
if ($solicitor_id > 0) {
    $search_elastic['must'][] = [
        'match' => [
            'solicitor_id' => [
                'query' => $solicitor_id
            ]
        ]
    ];
}

// Tìm kiếm theo so_tbmt
// if ($static > 1) {
// $search_elastic['must'][] = [
// 'range' => [
// 'update_static' => [
// 'gte' => $static
// ]
// ]
// ];
// }

if ($type_bid > 0 and $is_advance) {
    $search_elastic['must'][] = [
        'match' => [
            'type_bid_id' => [
                'query' => $type_bid
            ]
        ]
    ];
}
if ($type_choose_id > 0 and $is_advance) {
    // $search_elastic['must'][] = [
    // 'match' => [
    // 'type_choose_id' => [
    // 'query' => $type_choose_id
    // ]
    // ]
    // ];
    $search_elastic['must'][]['bool'] = [
        'should' => [
            [
                'match' => [
                    'type_choose_id' => [
                        'query' => $type_choose_id
                    ]
                ]
            ]
        ],
        'minimum_should_match' => 1,
        'boost' => '1.0'
    ];
}

if ($type_kqlcnt == 1 and $is_advance) {
    $search_elastic['must'][] = [
        'match' => [
            'type_kqlcnt' => [
                'query' => 0
            ]
        ]
    ];
} elseif ($type_kqlcnt == 2 and $is_advance) {
    $search_elastic['must'][] = [
        'range' => [
            'type_kqlcnt' => [
                'gt' => 0
            ]
        ]
    ];
}

// Tìm kiếm theo Lĩnh vực KQLCNT
if (!empty($bidfieid) and $is_advance) {
    $search_field = array();
    foreach ($bidfieid as $f) {
        $search_field[] = [
            'term' => [
                'bidfieid.keyword' => $f
            ]
        ];
    }
    $search_elastic['must'][]['bool']['should'] = $search_field;
}

if ($win_price_from > 0 and $win_price_to > 0 and $is_advance) {
    $search_elastic['must'][]['range']['win_price_number'] = [
        "gte" => $win_price_from,
        "lte" => $win_price_to
    ];
} else {
    if ($win_price_from > 0 and $is_advance) {
        $search_elastic['must'][]['range']['win_price_number'] = [
            "gte" => $win_price_from
        ];
    }
    if ($win_price_to > 0 and $is_advance) {
        $search_elastic['must'][]['range']['win_price_number'] = [
            "lte" => $win_price_to
        ];
    }
}

if ($price_plan_from > 0 and $price_plan_to > 0 and $is_advance) {
    $search_elastic['must'][]['range']['bid_price_number'] = [
        "gte" => $price_plan_from,
        "lte" => $price_plan_to
    ];
} else {
    if ($price_plan_from > 0 and $is_advance) {
        $search_elastic['must'][]['range']['bid_price_number'] = [
            "gte" => $price_plan_from
        ];
    }
    if ($price_plan_to > 0 and $is_advance) {
        $search_elastic['must'][]['range']['bid_price_number'] = [
            "lte" => $price_plan_to
        ];
    }
}

if ($catressult > 0 and $is_advance) {
    $search_elastic['must'][] = [
        'match' => [
            'type_data' => [
                'query' => $catressult == 1 ? "0" : "1"
            ]
        ]
    ];
}

if (!empty($search_elastic['should'])) {
    $search_elastic['minimum_should_match'] = '1';
    $search_elastic['boost'] = '1.0';
}

$array_query_elastic = array();
if (!empty($search_elastic)) {
    $array_query_elastic['query']['bool'] = $search_elastic;
}
$array_query_elastic['track_total_hits'] = 'true';
$array_query_elastic['size'] = $per_page;
$array_query_elastic['from'] = ($page - 1) * $per_page;
$array_query_elastic['sort'] = [
    [
        "finish_time" => [
            "order" => "desc"
        ]
    ]
];
// pr($array_query_elastic);
$response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_result', $array_query_elastic);
$num_items = $response['hits']['total']['value'];
$min_time = $max_time = 0;
foreach ($response['hits']['hits'] as $value) {
    if (!empty($value['_source'])) {
        $view = $value['_source'];

        $view['link_edit'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $view['id'];
        $view['link_delete'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;delete_id=' . $view['id'] . '&amp;delete_checkss=' . md5($view['id'] . NV_CACHE_PREFIX . $client_info['session_id']);

        if (isset($view['finish_time'])) {
            if (empty($min_time)) {
                $min_time = $max_time = $view['finish_time'];
            }
            $view['finish_time'] < $min_time && $min_time = $view['finish_time'];
            $view['finish_time'] > $max_time && $max_time = $view['finish_time'];
            $view['finish_time'] = date("d/m/Y H:i", $view['finish_time']);
            $view['finish_time'] = explode('00:00', $view['finish_time'])[0];
        } else {
            $view['finish_time'] = date("d/m/Y H:i", $view['post_time']);
            $view['finish_time'] = explode('00:00', $view['finish_time'])[0];
        }

        $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . ($type_search == 2 ? Url::getKQLCNDT() : Url::getKQLCNT()) . '/' . $view['alias'] . '-' . $view['id'] . $global_config['rewrite_exturl'];
        $view['link_search'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=listresult&solicitor_id=' . $view['solicitor_id'];

        $view['bidder_list'] = [];
        if ($type_search == 2) {
            $view['bidder_list'][] = [
                'name' => $view['bidder_name'],
                'bid' => 0,
                'no_business_licence' => $view['no_business_licence']
            ];
            !empty($view['no_business_licence']) && $arr_bidder[] = $view['no_business_licence'];
        } else {
            $rids[] = $view['id'];
        }

        $array_data[$view['id']] = $view;
        $arr_solicitor_id[] = $view['solicitor_id'];
        $arr_tbmt[] = $view['code'];
    }
}

if ((!empty($q) or !empty($key_search2) or !empty($without_key)) and $page == 1) {
    $nukeVietElasticSearh2 = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_result_host'], $module_config[$module_name]['elas_result_port'], 'bids_result_open', $module_config[$module_name]['elas_result_user'], $module_config[$module_name]['elas_result_pass']);

    // Khởi tạo truy vấn Elasticsearch cho bids_result_open
    $array_query_elastic_result = array();
    $array_query_elastic_result['query']['bool'] = [
        'must' => []
    ];

    // Tìm kiếm theo từ khóa chính
    if (!empty($q)) {
        $num = 0;
        $arr_key = explode(',', $q);
        foreach ($arr_key as $key) {
            if ($search_type_content == 1) {
                $key = trim($key);
                $array_query_elastic_result['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_full' => $key
                    ]
                ];
            } else {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                $array_query_elastic_result['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_simple' => $key
                    ]
                ];
            }
        }
    }

    // Tìm theo Từ khóa bổ sung
    if (!empty($key_search2)) {
        $arr_key = explode(',', $key_search2);
        foreach ($arr_key as $key) {
            if ($search_type_content == 1) {
                $key = trim($key);
                $array_query_elastic_result['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_full' => $key
                    ]
                ];
            } else {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                $array_query_elastic_result['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_simple' => $key
                    ]
                ];
            }
        }
    }

    // Tìm theo từ khóa loại trừ
    if (!empty($without_key)) {
        $arr_key = explode(',', $without_key);
        foreach ($arr_key as $key) {
            if ($search_type_content == 1) {
                $key = trim($key);
                $array_query_elastic_result['query']['bool']['must_not'][] = [
                    'match_phrase' => [
                        'searchtext_full' => $key
                    ]
                ];
            } else {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                $array_query_elastic_result['query']['bool']['must_not'][] = [
                    'match_phrase' => [
                        'searchtext_simple' => $key
                    ]
                ];
            }
        }
    }

    $array_query_elastic_result['query']['bool']['must'][] = [
        'term' => [
            'status_test' => 0
        ]
    ];

    if ($sfrom1 > 0 and $sto1 > 0) {
        $array_query_elastic_result['query']['bool']['must'][] = [
            'range' => [
                'create_time' => [
                    "gte" => $sfrom1,
                    "lte" => $sto1
                ]
            ]
        ];
    }

    $array_query_elastic_result['query']['bool']['must'][] = [
        'term' => [
            'del_time' => 0
        ]
    ];

    $array_query_elastic_result['size'] = 5;
    $array_query_elastic_result['sort'] = [
        [
            "create_time" => [
                "order" => "desc"
            ]
        ]
    ];

    try {
        // Thực hiện truy vấn Elasticsearch
        $response_result = $nukeVietElasticSearh2->search_data($db_config['prefix'] . '_bids_result_open', $array_query_elastic_result);
    } catch (Exception $e) {
        // Ghi log lỗi nếu có vấn đề
        file_put_contents(NV_ROOTDIR . '/data/logs/list_result_log_bids_result_' . date('Ymd') . '.txt', "\n\n ---------- [" . date('d-m-Y H:i:s') . "] Lỗi list_result_log_bids_result: \n" . print_r($e, true), FILE_APPEND);
        file_put_contents(NV_ROOTDIR . '/data/logs/list_result_log_bids_result_' . date('Ymd') . '.txt', "\n array_query_elastic_result: " . print_r($array_query_elastic_result, true), FILE_APPEND);
        trigger_error(print_r($e, true));
    }

    // Kết hợp kết quả từ Elasticsearch
    foreach ($response_result['hits']['hits'] as $value) {
        if (!empty($value['_source'])) {
            $view = $value['_source'];
            $array_data_result[$view['id']] = $view;
        }
    }
}
