/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 16 Jan 2018 07:25:26 GMT
 */
/**
 * Number.prototype.money_format(n, x, s, c) Format số sang dạng tiền giống như
 * trong PHP
 *
 * @param integer
 *            n: length of decimal
 * @param integer
 *            x: length of whole part
 * @param mixed
 *            s: sections delimiter
 * @param mixed
 *            c: decimal delimiter
 */
Number.prototype.money_format = function(n, x, s, c) {
    var re = "\\d(?=(\\d{" + (x || 3) + "})+" + (n > 0 ? "\\D" : "$") + ")",
        num = this.toFixed(Math.max(0, ~~n));
    return (c ? num.replace(".", c) : num).replace(new RegExp(re, "g"), "$&" + (s || ","));
};

var btnFollow = 0;

/**
 * findGetParameter Tìm trong biến GET cho key cần hay không
 *
 * @param parameterName
 * @returns
 */
function findGetParameter(parameterName) {
    var result = null,
        tmp = [];
    var items = location.search.substr(1).split("&");
    for (var index = 0; index < items.length; index++) {
        tmp = items[index].split("=");
        if (tmp[0] === parameterName) {
            result = decodeURIComponent(tmp[1]);
        }
    }
    return result;
}

function base64_encode(a) {
    var b = function(a) {
        return encodeURIComponent(a).replace(/%([0-9A-F]{2})/g, function(a, b) {
            return String.fromCharCode("0x" + b)
        })
    };
    if ("undefined" == typeof window) return (new Buffer(a)).toString("base64");
    if (void 0 !== window.btoa) return window.btoa(b(a));
    var e = 0,
        h = 0,
        c = "";
    if (c = [], !a) return a;
    a = b(a);
    do {
        b = a.charCodeAt(e++);
        var f = a.charCodeAt(e++),
            g = a.charCodeAt(e++),
            d = b << 16 | f << 8 | g;
        b = d >> 18 & 63;
        f = d >> 12 & 63;
        g = d >> 6 & 63;
        d &= 63;
        c[h++] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(b) + "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(f) + "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(g) + "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(d)
    } while (e < a.length);
    return c = c.join(""), ((a = a.length % 3) ? c.slice(0, a - 3) : c) + "===".slice(a || 3)
};

function confirmDialog(message, onConfirm) {
    var a = $("#confirm"),
        b = function() {
            a.modal("hide")
        };
    $("body").app
    a.modal("show");
    $(".modal-body", a).empty().html(message);
    $(".ok", a).unbind().one("click", onConfirm).one("click", b);
    $(".cancel", a).unbind().one("click", b)
}

function view_detail(a_ob, id, url) {
    $('#idmodals').removeData('bs.modal');
    $.post(url + '&popup=1', function(res) {
        $('#idmodals .modal-body').html( res );
        $('#idmodals').modal('show');
    });
}

function getDoc(arg1, sotbmt, url) {
    $('#idmodals').modal('hide');
    $('#idmodals').removeData('bs.modal');
    $.post(url + '&popup=1', function(res) {
        $('#idmodals .modal-body').html( res );
        $('#idmodals').modal('show');
    });
}

function formatNumber (num) {
    return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
}

function str2num(str) {
    str = parseInt(str.replace(/[^0-9]/gi, ''));
    if (isNaN(str)) return '';
    return str
}

function FormatMoney(str) {
    var strTemp = parseInt(str.replace(/[^0-9]/gi, ''));
    if (isNaN(strTemp)) return '';
    return strTemp.money_format(0, 3, ',', '.');
}

function replaceAddress(url) {
    if (history.pushState) {
        history.pushState(null, null, url);
    }
}

function getArrayDiff(a, b) {
    var ret = [],
        merged = [];

    merged = a.concat(b);

    for (var i = 0; i < merged.length; i++) {
        if (merged.indexOf(merged[i]) ==
            merged.lastIndexOf(merged[i])) {
            ret.push(merged[i]);
        }
    }

    return ret;
}

function show_captcha() {
    $("#captchaModal").modal()
}

function confirm_crawl() {
    $.ajax({
        url: window.location.href,
        type: "POST",
        dataType: "json",
        data: {
            'confirm_crawl': 1,
            'g-recaptcha-response' : $("[name=g-recaptcha-response]").val() ?? ''
        },
        success: function(a) {
            modalShow("", a.mess);
            $("#accept_crawl").click(function() {
                text = $(this).attr('data-text');
                $("#accept_crawl").prop("disabled", true).html('<i class="fa fa-spinner fa-spin"></i> ' + text);
                setTimeout(function() {
                    updateCrawl();
                }, 800)
            });
        }
    });
}

function click_update() {
    $('#captchaModal').modal('hide');
    confirm_crawl();
    return !1;
}

function updateCrawl() {
    // Đóng modal trước đó.
    modalHide();
    let url_link = $("#reupdate").data("link");
    if (url_link == '') {
        url_link = window.location.href;
    }
    $.ajax({
        type: 'POST',
        url: url_link,
        cache: !1,
        data: {
            check: $("#reupdate").data("check"),
            'update': "1",
            'id_update': $("#reupdate").data("id")
        },
        dataType: "json"
    }).done(function(a) {
        $("#reupdate").hide();
        if ("ok" != a.res) {
            if (typeof a.time !== "undefined" && a.time) {
                $("#update_wait").show();
                setTimeout(function() {
                    $("#update_wait").hide();
                    $("#reupdate").show();
                }, parseInt(a.time) * 1000);
            }
            modalShow("", a.mess);
            setTimeout(function() {
               $("#update_wait").hide();
            }, 3000);
        } else {
            $("#update_wait").show();
            setTimeout(function() {
                location.reload()
            }, parseInt(a.time) * 1000);
            modalShow("", a.mess);
            $("#update_wait").show();
            setTimeout(function() {
               $("#update_wait").hide();
            }, 3000);
        }
    });
    return !1;
}

function startTimer(duration, display) {
    var timer = duration,
        minutes, seconds;
    setInterval(function() {
        minutes = parseInt(timer / 60, 10);
        seconds = parseInt(timer % 60, 10);
        minutes = minutes < 10 ? "0" + minutes : minutes;
        seconds = seconds < 10 ? "0" + seconds : seconds;
        display.text(minutes + ":" + seconds);
        0 > --timer && (timer = duration, location.reload())
    }, 1E3)
}

function setWinParams(w, h) {
    var left = ((window.screen.width/2)-(w/2) + (typeof window.screenX != "undefined" ? window.screenX : window.screenLeft)),
        top = ((window.screen.height/2)-(h/2) + (typeof window.screenY != "undefined" ? window.screenY : window.screenTop));
      return "height=" + h + "px,width=" + w + "px,top=" + top + "px,left=" + left + "px,directories=no,titlebar=no,toolbar=no,location=no,status=1,scrollbars=1,fullscreen=yes";
}

function toFile(arg1, arg2) {
    $("#downloadFormPoster").remove();
    $("<div id='downloadFormPoster' style='display: none;'><iframe name='downloadFormPosterIframe'></iframe></div>").appendTo('body');
    var newForm = $("<form>", {
        "action": nv_base_siteurl + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=download&nocache=' + new Date().getTime(),
        "target": "downloadFormPosterIframe",
        "method": "POST",
        "name": "tofile"
    }).append($("<input>", {
        "name": "fileType",
        "value": arg1,
        "type": "hidden"
    })).append($("<input>", {
        "name": "fileName",
        "value": base64_encode(arg2),
        "type": "hidden"
    }));

    newForm.appendTo("#downloadFormPoster").submit();
    return !1
}

function toFilefromUrl(url, filename, disposition, type) {
    if ("attachment" == disposition) {
        $("#downloadFormPoster").remove();
        $("<div id='downloadFormPoster' style='display: none;'><iframe name='downloadFormPosterIframe'></iframe></div>").appendTo('body');
        var newForm = $("<form>", {
            "action": nv_base_siteurl + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=download&nocache=' + new Date().getTime(),
            "target": "downloadFormPosterIframe",
            "method": "POST",
            "name": "tofile"
        }).append($("<input>", {
            "name": "url",
            "value": base64_encode(url),
            "type": "hidden"
        })).append($("<input>", {
            "name": "filename",
            "value": base64_encode(filename),
            "type": "hidden"
        }));

        newForm.appendTo("#downloadFormPoster").submit();
        return !1
    } else {
        url = nv_base_siteurl + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=download&url=' + base64_encode(url) + '&fileName=' + base64_encode(filename);
        if (typeof type !== "undefined") {
            if ("pdf" == type) {
                url += '&reader=pdf';
                return toReader(url)
            }
        }

        url += '&reader=txt';
        return toReader(url)
    }
}

function toReader(url) {
    var b = setWinParams(1100 < $(window).width() ? 1100 : $(window).width(), $(window).height() - 100);
    b = window.open(url, "_Reader", b);
    b.focus();
    return !1
}

function toPdf(a) {
    a = $(a).parent().prev("a").attr("onclick").match(/toFile\('?(\d+)'?\s*,\s*'([^'']+)'\)/);
    a = nv_base_siteurl + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=download&fileType=' + parseInt(a[1]) + '&fileName=' + a[2] + '&reader=pdf';
    return toReader(a)
}

function bieumauchctrg(gongonum, gongocha, bid_succmethod) {
    var bidType = "15",
        url = "http://muasamcong.mpi.gov.vn:8081/webentry_xl/";
    if (bidType == "1" && bid_succmethod == "9") {
       url = url + "tiendo_thuchien_hh_chctrg/showPage?bid_no=" + gongonum + "&bid_turn_no=" + gongocha + "&bid_succmethod=" + bid_succmethod + "&bid_type=1&supplier_id=0&bidMethod=chctrg";
    } else if (bidType == "3" && bid_succmethod == "9") {
       url = "de_xuat_ns_xl_chctrg?bid_no=" + gongonum + "&bid_turn_no=" + gongocha + "&bid_succmethod=" + bid_succmethod + "&bid_type=3&supplier_id=0&bidMethod=chctrg";
    } else if (bidType == "15" && bid_succmethod == "9") {
       url = url + "tiendo_thuchien_ptv_chctrg/showPage?bid_no=" + gongonum + "&bid_turn_no=" + gongocha + "&bid_succmethod=" + bid_succmethod + "&bid_type=15&supplier_id=0&bidMethod=chctrg";
    }

    var a = $(window).width();
    modalShow("", '<iframe id="iframedown" width="' + (500 > a ? a - 50 : a / 2) + 'px" height="500px" src="' + encodeURI(url) + '"> </iframe>');
    return !1
}

function _buy_download(a, obj, op) {
    if (op == "redownfile" && $(a).data("isreact") == '1') {
        modalShow("",$(a).data("confirm"));
        return !1
    }
    str_type = '';
    if ($(a).data("type") != 'undefined') {
        str_type = '&type=' + $(a).data("type");
    }
    confirmDialog($(a).data("confirm"), function() {
        $.ajax({
            type: "POST",
            url: nv_base_siteurl + "index.php?" + nv_lang_variable + "=" + nv_lang_data + "&" + nv_name_variable + "=" + nv_module_name + "&" + nv_fc_variable + "=" + nv_func_name + "&nocache=" + (new Date).getTime(),
            data: op + "=1&id=" + $(a).data("id") + "&checkess=" + checkess + str_type,
            dataType: "json",
            success: function(b) {
             var mess = b.mess;
                if ("error" == b.res) {
                    if (typeof b.button_callback !== "undefined" && "buy_points" == b.button_callback) {
                        mess += '<p class="text-center"><a class="btn btn-danger" href="' + b.url + '" target="_blank">' + b.button_mess + '</a></p>';
                    }
                }
                modalShow("", mess, () => {}, () => {
                    location.reload();
                });

                if ("error" != b.res) {
                    setTimeout(() => {
                        $("#sitemodal").modal("hide");
                    }, 5000);
                }
            }
        })
    });
    return !1
}

function addButton(btn, url, filename, disposition, type) {
    var a = '<button class="btn btn-default btn-xs" onclick="toFilefromUrl(\'' + url + "', '" + filename + "', '" + disposition + "'";
    "undefined" !== typeof type && "" != type && (a += ", '" + type + "'");
    a += ');"><em class="fa fa-snowflake-o"></em> ' + btn + "</button>";
    return a;
}

function addA(cl, url, target, name, m , id, checkss, typefile, rid = '', is_req = 0) {
    return '<a typefile = "' + typefile + '" checkss ="' + checkss + '" is_req="' + is_req + '" rid="' + rid + '" id-file="' + id + '" class="download-file btn btn-' + cl + ' btn-xs" href="' + url + '" target="' + target + '" rel="noopener noreferrer nofollow"><em class="fa ' + m + '"></em> ' + name + "</a>"
}

function addPDF() {
    return '<button class="btn btn-default btn-xs" onclick="toPdf(this);"><em class="fa fa-snowflake-o"></em> ' + LNG.read + "</button>"
}

function link_reformat() {
    $(".is_points span").not(".is_ie").each(function() {
        var a = $(this).parent(),
            c = void 0 !== a.attr("href") ? a.attr("href") : "",
            f = void 0 !== a.attr("onclick") ? a.attr("onclick") : "",
            e = void 0 !== a.attr("target") ? a.attr("target") : "_self",
            d = $(this).text(),
            id = $(this).attr("id-file") !== "undefined" ? $(this).attr("id-file") : "",
            checkss = $(this).attr("checkss") !== "undefined" ? $(this).attr("checkss") : "",
            typefile = $(this).attr("typefile") !== "undefined" ? $(this).attr("typefile") : "",
            rid = $(this).attr("rid") !== "undefined" ? $(this).attr("rid") : "",
            is_req = $(this).attr("is_req") !== "undefined" ? $(this).attr("is_req") : "",
            b = a.next(),
            g = LNG.viplink,
            cl = 'primary',
            m = 'fa-snowflake-o';
        if (c === '#ErrorDownload' || checkss === "#ErrorDownload") {
            g = LNG.title_errorFile;
            cl = 'warning errorFile';
            m = 'fa-exclamation';
            c = '#1';
        } else if (checkss === "#checkss_" + id) {
            checkss = 'download'
            g = LNG.status1;
            cl = 'success';
            m = 'fa-clock';
            c = '#2';
        }
        a.addClass("disable-link");
        if (c === '#') {
            $(this).is(".is_vip") ? b.prepend(addA(cl, c, e, g, m, id, checkss, typefile, rid, is_req)) : b.prepend(addA("default", c, e, LNG.origlink, m, id, checkss, typefile, rid, is_req));
        } else if (checkss === "#checkss_" + id || checkss === "#ErrorDownload") {
            b.prepend(addA(cl, c, e, g, m, id, checkss, typefile, rid, is_req))
        } else {
            if ($(this).is(".is_fast")) {
                b.prepend(addA(cl, c, e, g, m, id, checkss, typefile, rid, is_req));
            }
        }
    })
}

function buy_fastlink(a) {
    return _buy_download(a, $("#confirm"), "downfile")
}

function buy_fastlink_qd(a) {
    return _buy_download(a, $("#confirm"), "downfile_qd")
}

function rebuy_fastlink(a) {
    return _buy_download(a, $("#confirm"), "redownfile")
}

function click_buy_follow(url, sess, callback, conf, sotbmt='', bidId=0) {
    var b = "checksess=" + sess + "&pointrow=1";
    "undefined" !== typeof conf && (b += "&confirm=1&sotbmt=" + sotbmt + "&bid_id=" + bidId);
    $.ajax({
        type: "POST",
        url: url,
        data: b,
        dataType: "json",
        success: function(a) {
            if ("success" == a.res) {
                0 < $("#followInfo").length && $("#followInfo").text(a.content);
                if ("function" === typeof callback) $("#sitemodal").on("hidden.bs.modal", function(e) {
                    $(e.currentTarget).unbind();
                    callback();
                    return !1
                });
                modalShow("", a.mess)
            } else "confirm" == a.res ? confirmDialog(a.mess, function() {
                click_buy_follow(url, sess, callback, 1);
                return !1
            }) : modalShow("", a.mess);
            return !1
        }
    });
    return !1
}

function change_searchType(a) {
    var c = $(a).data("confirmation"),
        d = $(a).data("negation"),
        e = $("#searchSector [name=search_type]"),
        b = parseInt(e.val());
    b = 1 == b ? 0 : 1;
    e.val(b);
    1 == b ? $(".fa", a).removeClass(d).addClass(c) : $(".fa", a).removeClass(c).addClass(d);
    return !1
}
function follow_search(t){
    var obj = $("#searchResult");
    code = $("[name=code]", t).val();
    error = $(t).data("error");
    url = $(t).attr("action");
    $('[name=code]', t).val(code);

    $.ajax({
        type: "POST",
        url: url,
        cache: !1,
        data: "code=" + code,
        success: function(a) {
            if (a == 'no_code') {
                modalShow("", error);
            } else {
                obj.hide(400).html(a).slideDown(400);
            }
        }
    })

    return!1
}

function follow_button_click() {
    if ("object" == typeof btnFollow) {
        $(btnFollow).trigger("click");
    }
    return !1
}

function other_click_buy_follow1(url, sess, sotbmt='') {
    if ($("#sitemodal.cr-show").length) {
        click_buy_follow1(url, sess, follow_button_click, '', sotbmt);
        modalHide();
    }
    return !1
}

function click_buy_follow1(url, sess, callback, conf, sotbmt='') {
    var b = "checksess=" + sess + "&pointrow=1";
    "undefined" !== typeof conf && (b += "&confirm=1&sotbmt=" + sotbmt);
    $.ajax({
        type: "POST",
        url: url,
        data: b,
        dataType: "json",
        success: function(a) {
            if ("success" == a.res) {
                // 0 < $("#followInfo").length && $("#followInfo").text(a.content);
                modalShow("", a.mess, () => {}, () => {
                    if ("function" === typeof callback) location.reload();
                });
            } else "confirm" == a.res ? confirmDialog(a.mess, function() {
                click_buy_follow1(url, sess, callback, 1);
                return !1
            }) : modalShow("", a.mess);
            return !1
        }
    });
    return !1
}


function other_click_buy_follow(url, sess, sotbmt='', bidId=0) {
    if ($("#sitemodal.cr-show").length) {
        click_buy_follow(url, sess, follow_button_click, '', sotbmt, bidId);
        modalHide();
    } else {
        click_buy_follow(url, sess, follow_button_click, '', sotbmt, bidId);
    }
    return !1
}

function click_follow(url, sess, t, sotbmt = '', vipid = 0) {
    btnFollow = t;
    $.ajax({
        type: "POST",
        url: url,
        data: {
            check: sess,
            ajax: "1",
            sotbmt: sotbmt,
            vipid: vipid,
        },
        dataType: "json",
        success: function(a) {
            modalShow("", a.mess, () => {}, () => {
                if ("success" == a.res) location.reload();
            });
            return !1
        }
    })
}

function click_unfollow(mess, url, id, checkss) {
    var obj = $("#confirm");
    $(".button", obj).text("");
    $(".modal-body", obj).text(mess);
    $(".ok", obj).show().on("click", function(e) {
        $(".ok", obj).hide();
        $.ajax({
            type: "POST",
            url: url,
            data: {
                delete_id: id,
                delete_checkss: checkss
            },
            success: function(a) {
                location.reload();
                return !1
            }
        })
    });
    obj.modal();
    return !1
}

function unmail_remind(id, url, status, success_msg, error_msg) {
    var tog_val = +$(`input[id="unmail_${id}"]`)[0].checked;
    $.ajax({
        type : 'POST',
        url,
        data : 'toggle_remind=1&id=' + id + '&status=' + status + '&tog_val=' + tog_val,
        success : function(data) {
            if (data.startsWith('OK_')) {
                alert(success_msg);
            } else {
                alert(error_msg);
            }
        },
        error : function() {
            alert(error_msg);
        },
    });
}

function vipSelLoad() {
    var $vipls = $("#vipls"),
        vipseled = [],
        vipstr = '';
        arr_in_vip69 = [1,2,3,77,88,8];
        in_vip69 = false;
        is_vip69 = false;

    $(".btn-vip", $vipls).prop("disabled", !0);
    $(".panel-vipplan.selected", $vipls).each(function() {
        vipseled.push(parseInt($(this).data("value")));

        if (jQuery.inArray(parseInt($(this).data("value")), arr_in_vip69) !== -1) {
            in_vip69 = true;
        } else if (parseInt($(this).data("value"))==69) {
            is_vip69 = true;
        }
    });
    vipstr = vipseled.join(',');
    $("[name=vip]", $vipls).val(vipstr);
    // Submit thong tin de kiem tra VIP
    $.ajax({
        type: 'POST',
        url: nv_base_siteurl + 'index.php?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=vip&nocache=' + new Date().getTime(),
        data: {
            'checkvipsel': 1,
            'planprecheck': 1,
            'vip': vipstr
        },
        cache: false,
        dataType: 'json',
        success: function(c) {
            $(".btn-vip", $vipls).prop("disabled", !1);
            if (in_vip69 == true) {
                $("#btn_vip69", $vipls).prop("disabled", !0);
            } else if (is_vip69 == true) {
                $("#btn_vip1", $vipls).prop("disabled", !0);
                $("#btn_vip2", $vipls).prop("disabled", !0);
                $("#btn_vip3", $vipls).prop("disabled", !0);
                $("#btn_vip77", $vipls).prop("disabled", !0);
                $("#btn_vip88", $vipls).prop("disabled", !0);
                $("#btn_vip8", $vipls).prop("disabled", !0);
            }

            var vipselNum = c.vips.length;
            if ($(".panel-vipplan.load", $vipls).length && c.vips.indexOf($(".panel-vipplan.load", $vipls).data('value')) < 0) {
                bootbox.alert($vipls.data('unknown-error'), function() {
                    $(".panel-vipplan.load", $vipls).removeClass("selected");
                })
            }
            $(".panel-vipplan", $vipls).removeClass("load");

            // Tinh gia va ma giam gia
            if (c.promotion.codevalid) {
                if (!c.promotion.vipisvalid) {
                    $('#promotion-vip-require').removeClass('hidden');
                    $('#promotion-vip-require').html(c.promotion.vipmesage);
                } else {
                    $('#promotion-vip-require').addClass('hidden');
                }

                // Hiển thị cảnh báo khác
                if (typeof c.promotion.othernote != 'undefined' && c.promotion.othernote) {
                    $('#promotion-other-note').removeClass('hidden');
                    $('#promotion-other-note').html(c.promotion.othernote);
                } else {
                    $('#promotion-other-note').addClass('hidden');
                }
            }

            // Sua lai address bar
            if (vipselNum) {
                replaceAddress($vipls.data('url') + encodeURI(c.vips.join(',')));
            } else {
                replaceAddress($vipls.data('urlno'));
            }

            // Hien thi/an tong tien dich vu
            if (vipselNum && c.promotion.codevalid && c.promotion.vipisvalid) {
                $('#promotion-total-money').removeClass('hidden');
                $('#promotion-total-money .price').text(c.respon.total_money);
                $('#promotion-total-discount').removeClass('hidden');
                $('#promotion-total-discount .price').text(c.respon.total_discount);
            } else {
                $('#promotion-total-money').addClass('hidden');
                $('#promotion-total-discount').addClass('hidden');
            }
            // Hien thi/An so tien thanh toan
            if (vipselNum) {
                $(".viplength", $vipls).text(vipselNum).parents(".basket").removeClass("primary").addClass("danger");
                var newString = '';
                c.vips.forEach(function(item) {
                    var obj = $(".panel-vipplan.vip" + item, $vipls);
                    newString += '<tr class="td__' + item + '"><td>' + obj.data("name") + '</td><td><span class="price">' + obj.data("price") + '</span></td><td><a href="javascript:void(0);" class="close" onclick="vipSelRemove(' + item + ');">&times;</a></td></tr>';
                });
                $(".viplist-table table tbody", $vipls).html(newString);
                $('#promotion-total-pay').removeClass('hidden');
                $('#regvip-next').removeClass('hidden');
                $('#promotion-total-pay .price').text(c.respon.total_pay);
                $(".viplist-table", $vipls).removeClass('hidden');
            } else {
                $(".viplength", $vipls).text(vipselNum).parents(".basket").removeClass("danger").addClass("primary");
                $(".viplist-table", $vipls).addClass('hidden');
                $('#promotion-total-pay').addClass('hidden');
                $('#regvip-next').addClass('hidden');
            }

            // Thay đổi lại tiêu đề giá x1
            $(".td__88").find(".price").text(c['price_x1_new']);
            $(".td__8").find(".price").text(c['price_8_new']);
            $(".panel-vipplan.vip8").data('price', c.price_8_new);
            $(".panel-vipplan.vip8").find(".price").text(c.price_8_new);
        },
        error: function(jqXHR, exception) {
            console.log(jqXHR);
            console.log(exception);
            bootbox.alert($vipls.data('unknown-error'), function() {
                $(".btn-vip", $vipls).prop("disabled", !1);
                $(".panel-vipplan.load", $vipls).removeClass("selected load");
            })
        }
    });
}

function handleSuspendVip(url, msg, s) {
    if (confirm(msg) == true) {
        $.ajax({
            type: "POST",
            url,
            data: `checksess=${s}`,
            success : function(res) {
                if (res.startsWith('OK_')) {
                    document.location.reload();
                }
            },
        })
    }
}

function vipSelRemove(num) {
    $("#vipls .panel-vipplan.vip" + num).removeClass("selected");
    vipSelLoad();
}

function changePromoCode() {
    $("#vipls .btn-changePromoCode").addClass('hidden');
    $("#newPromoCodeForm").removeClass('hidden');
}

function vipplanShow() {
    $("#vipls .block-total").removeClass("float-hidden");
    $("#vipls .basket").addClass("hidden");
    controlStickyWidget();
}

function vipplanHidden() {
    $("#vipls .block-total").addClass("float-hidden");
    $("#vipls .basket").removeClass("hidden")
}

function delPromoCode(msg) {
    bootbox.confirm(msg, function(result){
        if (result) {
            // Lam sau
        }
    });
}

/*
 * Xử lý khi người dùng nhập mã khuyến mãi mới - Trang VIP Plan - Trang đăng ký
 * VIP - Trang gia hạn VIP
 */
function newPromoCodeHandler(harea, promo_code) {
    var $vipls = $("#vipls");
    $.ajax({
        type: 'POST',
        url: nv_base_siteurl + 'index.php?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=vip&' + harea + '=1&nocache=' + new Date().getTime(),
        data: {
            'direct_set_promo': 1,
            'direct_set_code': promo_code
        },
        cache: false,
        dataType: 'json',
        success: function(c) {
            if (c.status == 'SUCCESS') {
                if (harea == 'plan' || c.data.loginrequire) {
                    // Tại trang chọn gói VIP hoặc bắt đăng nhập thì load
                    // lại và kết thúc
                    var url = window.location.href.replace(/\#(.*)/, "");
                    url = url.replace(/\&km\=[a-zA-Z0-9\_\-]+/, "");
                    window.location = url;
                    return;
                }
                if (!c.data.loginrequire) {
                    $('#promo-loginrequire-warning').hide();
                }
                $('#promotion-code-code').html(c.data.data.promo_code);
                $('#promotion-code-value').html(c.data.data.promo_value_display);
                $('#promotion-code').removeClass('hidden');
                if (harea == 'form') {
                    $($('.selvipyear')[0]).trigger('change');
                } else {
                    $($('.selviprenewalyear')[0]).trigger('change');
                }
                $('#newPromoCodeSubmit').find('span').html('');
                $('#newPromoCodeSubmit').prop('disabled', false);
                // Đánh dấu tùy biến mã khuyến mãi
                $('[name="is_custom_promo"]').val('1');
                return;
            }
            $('#newPromoCodeSubmit').find('span').html('');
            $('#newPromoCodeSubmit').prop('disabled', false);
            bootbox.alert(c.message)
        },
        error: function(jqXHR, exception) {
            console.log(jqXHR);
            console.log(exception);
            $('#newPromoCodeSubmit').find('span').html('');
            $('#newPromoCodeSubmit').prop('disabled', false);
            bootbox.alert($vipls.data("unknown-error"))
        }
    })
}

function handlerChooseVips(vips, years) {
    /*
     * Xác định subtype của VIPX1
     */
    var vip88_export = 1;
    if ($('[name="vip88_export"]:checked').length) {
        vip88_export = $('[name="vip88_export"]:checked').attr('value');
    }

    $('[name="vip55_type"]').each(function() {
        if ($(this).is(':checked')) {
           vip88_export = $(this).val();
        }
    });
    // Submit thông tin để kiểm tra VIP
    $.ajax({
        type: 'POST',
        url: nv_base_siteurl + 'index.php?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=vip&nocache=' + new Date().getTime(),
        data: {
            'checkvipsel': 1,
            'form': 1,
            'vipsel': vips,
            'numbers_year': years,
            'vip88_export': vip88_export
        },
        cache: false,
        dataType: 'json',
        success: function(c) {
            // X? l? tính toán giá và m? gi?m giá
            if (c.promotion.codevalid) {
                if (!c.promotion.vipisvalid) {
                    $('#promotion-vip-require').removeClass('hidden');
                    $('#promotion-vip-require').html(c.promotion.vipmesage);
                } else {
                    $('#promotion-vip-require').addClass('hidden');
                }

                // Hiển thị cảnh báo khác
                if (typeof c.promotion.othernote != 'undefined' && c.promotion.othernote) {
                    $('#promotion-other-note').removeClass('hidden');
                    $('#promotion-other-note').html(c.promotion.othernote);
                } else {
                    $('#promotion-other-note').addClass('hidden');
                }
            }
            // Xử lý tính toán giá và mã giảm giá
            if (c.vips.length && c.promotion.codevalid && c.promotion.vipisvalid) {
                $('#promotion-total-discount').removeClass('hidden');
                $('#promotion-total-discount').find('strong').html(c.respon.total_discount + ' VNĐ');
            }

            // Hiển thị, ẩn tổng giá dịch vụ
            if (c.vips.length) {
                $('#promotion-total-money').removeClass('hidden');
                $('#promotion-total-money').find('strong').html(c.respon.total_money + ' VNĐ');

                $('#promotion-total-pay').removeClass('hidden');
                $('#promotion-total-pay').find('strong').html(c.respon.total_pay + ' VNĐ');
                
                if (c.vips.includes(1) || c.vips.includes(19)) {
                    $('#total-upgrade-deduct').removeClass('hidden');
                }
                
            } else {
                $('#promotion-total-pay').addClass('hidden');
                $('#total-upgrade-deduct').addClass('hidden');
            }
        },
        error: function(jqXHR, exception) {
            console.log(jqXHR);
            console.log(exception);
            alert('Lỗi không xác định. Vui lòng thử lại lần nữa!');
        }
    });
}

function coppy_phone() {
    $.ajax({
        type: "POST",
        url: window.location.href,
        data: "&copy_userid=1",
        dataType: "json",
        success: function(data) {
            $("[name='contact_to']").val(data['name']);
            $('#contact_phone').val(data['contact_phone']);
            $("[name='tax']").val(data['tax']);
            $("[name='address_bill']").val(data['address_bill']);
        }
    });
}

function click_view_detail_adv(a) {
    var field = $(a).parent().attr('id');
    var contract = $(a).parent().data('contract');
    var typesearch = $(a).parent().data('typesearch');
    $.ajax({ type : "POST", url : nv_base_siteurl + "index.php?" + nv_lang_variable + "=" + nv_lang_data + "&" + nv_name_variable + "=" + nv_module_name + "&" + nv_fc_variable + "=" + nv_func_name + "&nocache=" + (new Date).getTime(),
        data : "id=" + $(a).data("id") + "&view_detail_adv=1&field=" + field + "&contract="+ contract + "&type_search="+typesearch,
        dataType : "json",
        success : function(b) {
            var mess = b.mess;
            if (b.body == "tai_ho_so" || b.body == "tai_quyet_dinh" || b.body == "tai_quyet_dinh_kh") {
                alert(mess);
                location.reload();
                $('#' + field).html("Đang tải lại trang, vui lòng chờ trong giây lát!");
            } else {
                if ("error" == b.res) {
                    if (typeof b.button_callback !== "undefined" && "buy_points" == b.button_callback) {
                        mess += '<p class="text-center"><a class="btn btn-danger" target="_blank" href="' + b.url + '">' + b.button_mess + '</a></p>';
                    }
                    modalShow("", mess);
                }
                if ("error" != b.res) {
                    alert_msg(mess);
                    if (typeof contract !== 'undefined' && contract) {
                        $('.' + field + '_' + contract).html(b.body);
                    } else {
                        $('#' + field).html(b.body);
                    }
                }
            }
        }
    })
    return !1
}
function alert_msg(msg) {
    $('#msgshow').html(msg);
    $('#msgshow').show('slide').delay(5000).hide('slow');
}

$(function() {
    var $vipls = $("#vipls");
    // Lựa chọn gói VIP, cơ bản
    $('[data-toggle="selvipplan"]').on('click', function(e) {
        e.preventDefault();
        var element = $(this);
        var $this = $(this).parents(".panel-vipplan");
        if ($this.is(".selected")) {
            return;
        }
        $this.addClass("selected load");

        // Google GTM dataLayer ecommerce
        window.dataLayer = window.dataLayer || [];
        dataLayer.push({ ecommerce: null });

        var ecommerce = {
            currency: element.data('currency'),
            value: element.data('price'),
            items: [{
                item_id: element.data('itemid').toString(),
                item_name: element.data('itemname'),
                quantity: 1,
                price: element.data('price')
            }]
        };
        dataLayer.push({
            event: "add_to_cart",
            ecommerce: ecommerce
        });

        vipSelLoad();
    });

    // Lựa chọn gói VIP, tại trang nhập thông tin
    $('[name="vipsel[]"]').on('change', function() {
        var vips = {};
        var years = {};
        $('.area-vip').each(function() {
            var $this = $(this);
            var vipid = $this.find('[type="checkbox"]').val();
            var vipsel = $this.find('[type="checkbox"]').is(':checked');
            if (!vipsel) {
                $this.find('option').prop('selected', false);
                $('#area-vip-' + vipid + '-year').addClass('hidden');
            } else {
                $('#area-vip-' + vipid + '-year').removeClass('hidden');
                vips[vipid] = vipid;
                years[vipid] = $('#area-vip-' + vipid + '-year').find('select').val();
            }
        });

        handlerChooseVips(vips, years);

        // var isVIP3 = (typeof vips[3] == "undefined") ? false : true;
        // var isVIP99 = (typeof vips[99] == "undefined") ? false : true;
        // var isVIP6 = (typeof vips[6] == "undefined") ? false : true;
        // var isVIP55 = (typeof vips[55] == "undefined") ? false : true;
        // var isVIP77 = (typeof vips[77] == "undefined") ? false : true;
        // var isVIP88 = (typeof vips[88] == "undefined") ? false : true;
        var numVipSel = Object.keys(vips).length;
        var show_send_time = false;
        var send_time_notice = false;
        var list_vip_no_sendtime = [3, 31, 32, 55, 77, 88, 99];
        for (const key in vips) {
            if (list_vip_no_sendtime.includes(parseInt(key))) {
                send_time_notice = true;
            } else {
                show_send_time = true;
            }
        }
        /*
         * Nếu chỉ chọn VIP 3, VIP 99 (VIEWEB), không có VIP khác thì ẩn các ô: -
         * Chu kỳ gửi email - Các email phụ
         */
        if (!show_send_time) {
            $("#group-send-time").hide();
            $("#group-sub-email").hide();
            $("#group-sub-email-notice").hide();
        } else {
            $("#group-send-time").show();
            $("#group-sub-email").show();
            if (!send_time_notice) {
                $("#group-sub-email-notice").hide();
            } else {
                $("#group-sub-email-notice").show();
            }
        }
        // if ((isVIP3 && isVIP99 && isVIP6  && isVIP55 && numVipSel == 2) || (numVipSel == 1 && (isVIP3 || isVIP99 || isVIP6 || isVIP55))) {
        //     $("#group-send-time").hide();
        //     $("#group-sub-email").hide();
        //     $("#group-sub-email-notice").hide();
        // } else {
        //     $("#group-send-time").show();
        //     $("#group-sub-email").show();

        //     // Thông báo mail phụ và chu kỳ gửi mail không áp dụng cho VIP 3
        //     if (isVIP3 || isVIP99 || isVIP6 || isVIP55) {
        //         $("#group-sub-email-notice").show();
        //     } else {
        //         $("#group-sub-email-notice").hide();
        //     }
        // }
    });

    // Xử lý khi thay đổi số năm đăng ký VIP
    $('.selvipyear').on('change', function() {
        var vips = {};
        var years = {};
        $('.area-vip').each(function() {
            var $this = $(this);
            var vipid = $this.find('[type="checkbox"]').val();
            var vipsel = $this.find('[type="checkbox"]').is(':checked');
            if (vipsel) {
                vips[vipid] = vipid;
                years[vipid] = $('#area-vip-' + vipid + '-year').find('select').val();
            }
        });

        handlerChooseVips(vips, years);
    });

    // Xử lý khi đổi kiểu VIP X1
    $('[name="vip88_export"]').on('change', function() {
        if ($('.selvipyear').length) {
            $('.selvipyear:first').trigger('change');
        }
        if ($('.selviprenewalyear').length) {
            $('.selviprenewalyear').trigger('change');
        }
    });

    $('[name="vip55_type"]').on('change', function() {
        if ($('.selvipyear').length) {
            $('.selvipyear:first').trigger('change');
        }
        if ($('.selviprenewalyear').length) {
            $('.selviprenewalyear').trigger('change');
        }
    });

    // Xử lý khi thay đổi số năm gia hạn VIP
    $('.selviprenewalyear').on('change', function() {

        // Các gói VIP và năm
        var vip = $(this).data('vip');
        var years = $(this).val();

        /*
         * Xác định subtype của VIPX1
         */
        var vip88_export = 1;
        if (vip == 88) {
            if ($('[name="vip88_export"]:checked').length) {
                vip88_export = $('[name="vip88_export"]:checked').attr('value');
            }
        } else if (vip == 55) {
            $('[name="vip55_type"]').each(function() {
                if ($(this).is(':checked')) {
                   vip88_export = $(this).val();
                }
            });
        }

        $.ajax({
            type: 'POST',
            url: nv_base_siteurl + 'index.php?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=vip&nocache=' + new Date().getTime(),
            data: {
                'renewal': 1,
                'checkvipsel': 1,
                'vip': vip,
                'numbers_year': years,
                'vip88_export': vip88_export
            },
            cache: false,
            dataType: 'json',
            success: function(c) {
                // Xử lý tính toán giá và mã giảm giá
                if (c.promotion.codevalid) {
                    if (!c.promotion.vipisvalid) {
                        $('#promotion-vip-require').removeClass('hidden');
                        $('#promotion-vip-require').html(c.promotion.vipmesage);
                    } else {
                        $('#promotion-vip-require').addClass('hidden');
                    }

                    // Hiển thị cảnh báo khác
                    if (typeof c.promotion.othernote != 'undefined' && c.promotion.othernote) {
                        $('#promotion-other-note').removeClass('hidden');
                        $('#promotion-other-note').html(c.promotion.othernote);
                    } else {
                        $('#promotion-other-note').addClass('hidden');
                    }
                }
                
                // Hiển thị, ẩn tổng giá dịch vụ
                if (c.vips.length && c.promotion.codevalid && c.promotion.vipisvalid) {
                    $('#promotion-total-money').removeClass('hidden');
                    $('#promotion-total-money').find('strong').html(c.respon.total_money + ' VNĐ');
                    $('#promotion-total-discount').removeClass('hidden');
                    $('#promotion-total-discount').find('strong').html(c.respon.total_discount + ' VNĐ');
                } else {
                    $('#promotion-total-money').addClass('hidden');
                    $('#promotion-total-discount').addClass('hidden');
                }

                // Hiển thị, ẩn giá thanh toán
                if (c.vips.length) {
                    $('#promotion-total-pay').removeClass('hidden');
                    $('#promotion-total-pay').find('strong').html(c.respon.total_pay + ' VNĐ');
                } else {
                    $('#promotion-total-pay').addClass('hidden');
                }
            },
            error: function(jqXHR, exception) {
                console.log(jqXHR);
                console.log(exception);
                alert('Lỗi không xác định. Vui lòng thử lại lần nữa!');
            }
        });
    });

    // Đăng nhập thành viên
    $('[data-toggle="ajaxlogin"]').on('click', function(e) {
        e.preventDefault();
        loginForm();
    });

    // Chuyển bước tiếp theo khi đăng ký VIP
    $('[data-toggle="vipplannext"]').on('click', function(e) {
        e.preventDefault();
        var vip = $('[name="vip"]').val();
        if (vip == '') {
            window.location = $(this).data('urlno');
        } else {
            window.location = $(this).data('url') + vip;
        }
    });


    $('#newPromoCodeSubmit').on('click', function() {
        var btn = $(this);
        var promo_code = $('#newPromoCodeInput').val();
        if (promo_code == '') {
            bootbox.alert($vipls.data("promocode-empty"));
            return;
        }
        btn.find('span').html('<i class="fa fa-spinner fa-spin"></i> ');
        btn.prop('disabled', !0);
        newPromoCodeHandler(btn.data('mode'), promo_code);
    });

    // Có tìm kiếm nâng cao và ấn vào các phân trang hoặc submit form tìm kiếm thì cuộn đến
    let searchState = sessionStorage.getItem('biddingsearchstate');
    if ($("#siteContent").length && (searchState == 'submited' || searchState == 'pagination' || (
        searchState == null && null != findGetParameter("is_advance")
    ))) {
        $("html, body").animate({
            scrollTop: $("#siteContent").offset().top
        }, 800);
    }

    // Theo dõi xem người dùng ấn vào đâu
    $('a').on('click', function() {
        var ul = $(this).closest('ul.pagination');
        if (ul.length == 1) {
            sessionStorage.setItem('biddingsearchstate', 'pagination');
        } else {
            sessionStorage.setItem('biddingsearchstate', 'other');
        }
    });

    // Load xong thì reset trạng thái trước đó
    sessionStorage.setItem('biddingsearchstate', 'other');
});

$(document).ready(function($) {
    // Mở Menu
    $(".btn__list_ol, .close__menu, .btn_list_menu").click(function(event) {
        $("#bidding__menu").toggleClass("show");
        height_menu = $(".bidding__menu-title").height() + $(".bidding__menu-main").height() + 30 + "px";
        $("#fixmenu").css({
            'height': height_menu,
            'min-height': height_menu
        })
    });

    $(".close__menu").click(function(event) {
        $("#bidding__menu").removeClass('show');
    });

    $_bidding__link = $(".bidding__link");
    for (var i = 0; i < $_bidding__link.length; i++) {
        if ($($_bidding__link.eq(i).attr('href')).length > 0) {
            // action
        } else {
            $_bidding__link.eq(i).hide();
        }
    }

    if ($(".scoll__menu").length > 0) {
        scoll__menu = $(".scoll__menu");
    } else {
        scoll__menu = $(".detail_scoll__menu");
    }
    for (i = 0; i < scoll__menu.length; i++) {
        a = $('a[href="#' + scoll__menu.eq(i).attr('id') + '"]');
        a.attr("data-offset", $("#" + scoll__menu.eq(i).attr('id')).offset().top);
    }

    // set chiều cao cho box menu

    //Scrolmenu
    if (($(".bidding-wrapper").length > 0 || $(".detail-wrapper").length > 0 ) && $('#fixmenu').length > 0) {
        if ($(".detail-wrapper").length > 0) {
            $("html, body").click(function(event) {
                if (!$(event.target).closest('.btn_list_menu, #fixmenu').length) {
                    $("#bidding__menu").removeClass('show');
                }
            });
            // Trang chi tiết bên mời thầu và trang chi tiết nhà thầu
            var length_d = $(".detail-wrapper").height() - $("#bidding__menu").height() + $(".detail-wrapper").offset().top + 100;
            var menu_height = $("#bidding__menu").height();
            var top_dw = $(".detail-wrapper").offset().top;
            $(window).on('scroll', function () {
                var scroll_top = $(this).scrollTop();
                // Kiểm tra nếu cả hai div được hiển thị thì mới tính lại length_d
                if ($("#activities .tab-pane.active").length > 1) {
                    length_d = $(".detail-wrapper").height() - menu_height + top_dw + 100;
                }
                var h_view_history = 0;
                if ($('.view_history').length > 0) {
                    h_view_history = $('.view_history').outerHeight();
                }
                var top_div = $(".detail-wrapper").offset().top + $('.detail-wrapper .border-bidding').outerHeight() + $('.share-content').outerHeight() + $('.detail-list-btn').outerHeight();
                if (window.matchMedia("(max-width: 481px)").matches) {
                    if ($('.solicitor-menu').length == 0) {
                        top_div = top_div + $('.crawl_time').outerHeight() + h_view_history - 30;
                    } else {
                        top_div = top_div - 50;
                    }
                } else {
                    top_div = top_div + 17;
                }

                if (scroll_top < top_div || scroll_top > length_d) {
                    $('#fixmenu').removeClass('fixed absolute mb-fixed').addClass('static');
                    $('#bidding__menu').addClass('menu-show-left');
                    $(".btn_list_menu").removeClass('fixed mb-fixed').addClass('static');
                    $(".bidding_menu_wrapper").removeClass('no-amination');
                } else {
                    $(".bidding_menu_wrapper").addClass('no-amination');
                    $('#bidding__menu').removeClass('menu-show-left');
                    if (window.matchMedia("(min-width: 768px)").matches) {
                        if (window.matchMedia("(min-width: 1366px)").matches) {
                            $(".btn_list_menu, #fixmenu").css('right', 'calc(50% - 320px)');
                        } else if (window.matchMedia("(min-width: 991px)").matches) {
                            $(".btn_list_menu, #fixmenu").css('right', '25.9%');
                        } else {
                            $(".btn_list_menu, #fixmenu").css('right', '34.5%');
                        }
                        $('#fixmenu').removeClass('absolute static mb-fixed').addClass('fixed');
                        $(".btn_list_menu").removeClass('fixed static mb-fixed').addClass('fixed');
                    } else {
                        $('#fixmenu').removeClass('static absolute fixed').addClass('mb-fixed');
                        $(".btn_list_menu").removeClass('static').addClass('mb-fixed');
                    }
                }
            });
            // Resize menu
            $(window).resize(function() {
                var width = $(window).width();
                height_mn = $(".bidding__menu-title").height() + $(".bidding__menu-main").height() + 36;
                if (width > 768) {
                    if (window.matchMedia("(min-width: 1366px)").matches) {
                        $(".btn_list_menu, #fixmenu").css('right', 'calc(50% - 320px)');
                    } else if (window.matchMedia("(min-width: 991px)").matches) {
                        $(".btn_list_menu, #fixmenu").css('right', '25.9%');
                    } else {
                        $(".btn_list_menu, #fixmenu").css('right', '34.5%');
                    }
                    $('#fixmenu').removeClass('absolute static mb-fixed').addClass('fixed');
                    $('#bidding__menu').removeClass('menu-show-left');
                    $(".btn_list_menu").removeClass('fixed static mb-fixed').addClass('fixed');
                } else {
                    $('#fixmenu').removeClass('static absolute fixed').addClass('mb-fixed');
                    $('#bidding__menu').removeClass('menu-show-left');
                    $(".btn_list_menu").removeClass('static').addClass('mb-fixed');
                }
            });
        }

        if ($(".bidding-wrapper").length > 0) {
            var length = $(".bidding-wrapper").height() - $("#bidding__menu").height() + $(".bidding-wrapper").offset().top;
            $(window).scroll(function () {
                height_mn = $(".bidding__menu-title").height() + $(".bidding__menu-main").height() + 30;
                var scroll = $(this).scrollTop(); // khi kéo chuột
                $position_mn = 'static';
                $bottom_mn = '0';
                $top_mn = '0';
                $height_mn = '0';
                if (scroll < $(".bidding-wrapper").offset().top + 300) {
                    $position_mn = 'static';
                    $bottom_mn = '0';
                    $top_mn = 'auto';
                    $height_mn = height_mn;
                    // Nếu cuộn chuột đến cuối box đang fix thì xử lý
                    $('#fixmenu').css({
                        'position': 'static',
                        'bottom': '0',
                        'top': 'auto',
                        'height': height_mn
                    });
                    $position_ol = 'static';
                    $bottom_ol = '0';
                    $background_ol = 'none';
                    $(".btn__list_ol").css({
                        'position': 'static',
                        'top': 0,
                        'background': 'none'
                    });
                } else if (scroll > length + 50) {
                    $position_mn = 'absolute';
                    $bottom_mn = '0';
                    $top_mn = 'auto';
                    $height_mn = height_mn;
                    $('#fixmenu').css({
                        'position': 'absolute',
                        'bottom': '0',
                        'top': 'auto',
                        'height': height_mn
                    });
                    $position_ol = 'static';

                    $(".btn__list_ol").css({
                        'position': 'static',
                    });
                } else {
                    if (scroll >= $("#fixmenu").offset().top) {
                        if (window.matchMedia("(min-width: 768px)").matches) {
                            $position_mn = 'fixed';
                            $top = '0';
                            $height_mn = height_mn;
                            $('#fixmenu').css({
                                'position': 'fixed',
                                'top': '0',
                                'height': height_mn
                            });
                            if (window.matchMedia("(min-width: 1920px)").matches) {
                                $position_ol = 'fixed';
                                $top_ol = '0';
                                $background_ol = 'rgb(247 247 247)';
                                $right_ol = '33.5%';
                                $z_index_ol = '2';
                                $(".btn__list_ol").css({
                                    'position': 'fixed',
                                    'top': 0,
                                    'right': '33.5%',
                                    'z-index': 2,
                                    'background': 'rgb(247 247 247)'
                                });
                            } else {
                                if (window.matchMedia("(min-width: 768px)").matches) {
                                    $(".btn__list_ol").css({
                                        'position': 'fixed',
                                        'top': 0,
                                        'right': '34.3%',
                                        'z-index': 2,
                                        'background': 'rgb(247 247 247)'
                                    });
                                    $position_ol = 'fixed';
                                    $top_ol = '0';
                                    $background_ol = 'rgb(247 247 247)';
                                    $right_ol = '34.3%';
                                    $z_index_ol = '2';
                                }

                                if (window.matchMedia("(min-width: 911px)").matches) {
                                    $(".btn__list_ol").css({
                                        'position': 'fixed',
                                        'top': 0,
                                        'right': '26.3%',
                                        'z-index': 2,
                                        'background': 'rgb(247 247 247)'
                                    });
                                    $position_ol = 'fixed';
                                    $top_ol = '0';
                                    $background_ol = 'rgb(247 247 247)';
                                    $right_ol = '26.3%';
                                    $z_index_ol = '2';
                                }
                            }
                        } else {
                            $position_mn = 'fixed';
                            $top = '58px';
                            $('#fixmenu').css({
                                'position': 'fixed',
                                'top': '58px',
                                // 'right': '7px',
                            });
                            $position_ol = 'fixed';
                            $top_ol = '58px';
                            $right_ol = '11px';
                            $background_ol = 'rgb(247 247 247)';
                            $z_index_ol = '2';

                            $(".btn__list_ol").css({
                                'position': 'fixed',
                                'top': '58px',
                                'right': '11px',
                                'z-index': 2,
                                'background': 'rgb(247 247 247)'
                            });
                        }

                        $("#bidding__menu").css({
                            'border': 'none'
                        })
                    }
                }
            });

            // Resize menu
            $(window).resize(function() {
                var width = $(window).width();
                height_mn = $(".bidding__menu-title").height() + $(".bidding__menu-main").height() + 36;
                if (width > 768){
                    $('#fixmenu').css({
                        'position': 'fixed',
                        'top': '0',
                        'height': height_mn
                    });
                    if (window.matchMedia("(min-width: 1920px)").matches) {
                        $(".btn__list_ol").css({
                            'position': 'fixed',
                            'top': 0,
                            'right': '33.5%',
                            'z-index': 2,
                            'background': 'rgb(247 247 247)'
                        });
                    } else {
                        if (window.matchMedia("(min-width: 911px)").matches) {
                            $(".btn__list_ol").css({
                                'position': 'fixed',
                                'top': 0,
                                'right': '26.3%',
                                'z-index': 2,
                                'background': 'rgb(247 247 247)'
                            });
                        }

                        if (window.matchMedia("(min-width: 768px)").matches) {
                            $(".btn__list_ol").css({
                                'position': 'fixed',
                                'top': 0,
                                'right': '34.3%',
                                'z-index': 2,
                                'background': 'rgb(247 247 247)'
                            });
                        }

                    }
                } else {
                    $('#fixmenu').css({
                        'position': 'fixed',
                        'top': '58px',
                        // 'right': '7px',
                        'height': height_mn
                    });
                    $(".btn__list_ol").css({
                        'position': 'fixed',
                        'top': '58px',
                        'right': '11px',
                        'z-index': 2,
                        'background': 'rgb(247 247 247)'
                    });
                }
            });
        }

        tmClick = "";

        $(".bidding__link").click(function(event) {
            tmClick = $(this).attr("href");
             //prevent the default action for the click event
            if ($(".bidding-wrapper").length > 0 || $(".detail-wrapper").length > 0) {
                event.preventDefault();
            }
            $(".bidding__menu-main").find('.bidding__link').each(function(index, el) {
                $(this).removeClass('active');
            });

            $(this).addClass('active');

            if ($(".detail-wrapper").length > 0) {
                history.replaceState(null, null, tmClick);
            }

            //get the full url - like mysitecom/index.htm#home
            var full_url = this.href;

            //split the url by # and get the anchor target name - home in mysitecom/index.htm#home
            var parts = full_url.split("#");
            var trgt = parts[1];

            //get the top offset of the target anchor
            var target_offset = $("#"+trgt).offset();
            var target_top = target_offset.top;

            //goto that anchor by setting the body scroll top to anchor top
            var mql = window.matchMedia('screen and (min-width: 768px)');
            if (mql.matches){
                $('html, body').animate({scrollTop:target_top}, 500, 'easeInSine', function(){
                });
            } else {
                $('html, body').animate({scrollTop:target_top - 58}, 500, 'easeInSine');
            }
            if (window.matchMedia('screen and (max-width: 480px)').matches && $(".detail_scoll__menu").length > 0) {
                $("#bidding__menu").removeClass('show');
            }
            $(document).on("scroll", onScroll);
        })

        $(document).on("scroll", onScroll);
    }

    // Bật tắt tự gia hạn gói VIP
    $('[data-toggle="renewalauto"]').on('click', function(e) {
        e.preventDefault();
        var $this = $(this);
        $.ajax({
            type: 'POST',
            url: nv_base_siteurl + 'index.php?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=vip&nocache=' + new Date().getTime(),
            data: {
                change_renewalauto: 1,
                id: $this.data('id'),
                checksess: $this.data('checksess')
            },
            dataType: 'json',
            cache: false,
            success: function(respon) {
                if (respon.error != '') {
                    alert(respon.error);
                    return;
                }
                alert(respon.success);
                location.reload();
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Request Error!!!');
                console.log(jqXHR, textStatus, errorThrown);
            }
        });
    });

    // Bật tắt tự gia hạn gửi email bộ lọc
    $('[data-toggle="renewalautofilter"]').on('click', function(e) {
        e.preventDefault();
        var $this = $(this);
        $.ajax({
            type: 'POST',
            url: nv_base_siteurl + 'index.php?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=filters&nocache=' + new Date().getTime(),
            data: {
                change_renewalauto: 1,
                id: $this.data('id'),
                userid: $this.data('userid'),
                checksess: $this.data('checksess')
            },
            dataType: 'json',
            cache: false,
            success: function(respon) {
                if (respon.error != '') {
                    alert(respon.error);
                    return;
                }
                alert(respon.success);
                location.reload();
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Request Error!!!');
                console.log(jqXHR, textStatus, errorThrown);
            }
        });
    });

    handleScrollProgress();
});

function handleScrollProgress(){
    if ( $('.prb_container').length == 0 ) return;

    // Xử lý độ dài của thanh tiến trình đấu thầu, đảm bảo không bao giờ gây ra vỡ giao diện:
    // Cho cuộn ngang nếu quá dài
    // Luôn luôn kéo cho phần current active vào gần giữa màn hình,
    // cách mép trái một khoảng bằng 1.5 độ rộng của chính nó
    const PAD = '25px';
    var titleBar = $('.prb_container .prb:first-child')[0];
    var scrollContainer = $('.prb_container .prb:last-child')[0];
    var current_active = $('.prb_container .current.active').parent();

    $(scrollContainer).css('overflow-x', 'auto');
    $(scrollContainer).css('padding-top', PAD);
    $(scrollContainer).css('position', 'relative'); // Để sử dụng PerfectScrollbar

    // Xử lý thanh title phía trên
    $(titleBar).css('display', 'none');
    var titleElement = $("<span></span>").text(window.nv_lang_data == 'vi'? 'Đang xem': 'Watching');
    $(titleElement).css({
        'position': 'absolute',
        'top': '-' + PAD,
        'display': 'block',
        'width': '100%',
        'text-align': 'center',
        'white-space': 'nowrap',
    });
    $(current_active).prepend(titleElement);

    var offsetLeft = $(current_active).offset().left;
    var activeWidth = $(current_active).width();
    $(scrollContainer).scrollLeft(offsetLeft - activeWidth * 1.5);

    new PerfectScrollbar(scrollContainer);
}

function onScroll(event){
    var scrollPos = $(document).scrollTop();
    bidding__link = $(".bidding__link");
    ttop = 0;
    if ($(".scoll__menu").length > 0) {
        scoll__menu = $(".scoll__menu");
    } else {
        scoll__menu = $(".detail_scoll__menu");
        ttop = 30;
    }
    for (i = 0; i < scoll__menu.length; i++) {
        id =  scoll__menu.eq(i).attr("id");
        if (id === 'bddt') {
            vt_x = parseInt($("#" + id).offset().top);
            vt_y = parseInt($("#" + id).height());
            height_id = scoll__menu.eq(i).attr("height-id");
            vt_y = $(height_id).height() + vt_y;
        } else {
            vt_x = parseInt($("#" + id).offset().top);
            vt_y = parseInt($("#" + id).height());
        }

        var mql = window.matchMedia('screen and (min-width: 768px)');
        if (mql.matches){
            if (scrollPos + ttop >= vt_x && (scrollPos <= (vt_x + vt_y))) {
                $(".bidding__menu-main").find('.bidding__link').each(function(index, el) {
                    $(this).removeClass('active');
                    $(this).css({
                        'background':'none',
                        'box-shadow':'none',
                        'font-weight': '400'
                    })
                });
                $('a[href="#'+ id +'"]').addClass('active').css({
                        'background':'#fff',
                        'box-shadow':'0 0 0 1px #e4eaf1',
                        'font-weight': '500'
                    });;
                $(".bidding__menu-title.title__heading").click();
            }
        } else {
            if (scrollPos + 60 >= vt_x && (scrollPos <= (vt_x + vt_y))) {
                $(".bidding__menu-main").find('.bidding__link').each(function(index, el) {
                    $(this).removeClass('active');
                    $(this).css({
                        'background':'none',
                        'box-shadow':'none',
                        'font-weight': '400'
                    })
                });
                $('a[href="#'+ id +'"]').addClass('active').css({
                        'background':'#fff',
                        'box-shadow':'0 0 0 1px #e4eaf1',
                        'font-weight': '500'
                    });
                $(".title__heading").click();
            }
        }


    }
}

function send_mail_support_filter() {
    var mail = "mailto:<EMAIL>?subject=Tôi cần hỗ trợ tìm kiếm thông tin thầu trên dauthau.asia&body=Kính gửi DauThau.info %0d%0aTôi tìm kiếm thông tin trên phần mềm DauThau.info nhưng không thấy nội dung cần tìm. %0d%0aĐây là URL tôi đã tìm kiếm: [" + encodeURIComponent(window.location.href) + "]";
    mail += " %0d%0aXin vui lòng hỗ trợ tôi! %0d%0aSố điện thoại của tôi là:";
    var a = document.createElement('a');
    a.href = mail;
    a.textContent = "<EMAIL>";
    a.click();
}

function showAllTimeFilter() {
    var searchParams = new URLSearchParams(window.location.search);
    var today = new Date();
    var dd = String(today.getDate()).padStart(2, '0');
    var mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
    var yyyy = today.getFullYear();
    today = dd + '/' + mm + '/' + yyyy;

    searchParams.set('sfrom','31/12/2010');
    searchParams.set('sto', today);
    window.location.href = "?" + searchParams.toString();
}

/**
 * <AUTHOR> Lâm
 * Hàm compare_highlight_difference() dùng để so sánh sự thay đổi HTML trước vào sau
 * $array là mảng full query vào bảng customs_log
 *
 * first_version: id div ban đầu của HTML
 * last_version: id div sau đó của HTML
 * new_version: id div sau khi chỉnh sửa của HTML
 * data_first dữ liệu html được mã hoá dạng JSON,
 * data_last dữ liệu html sau khi chỉnh sửa được mã hoá dạng JSON
 */

// compare_highlight_difference("outputOriginal", "output", "outputNew", false, JSON.stringify("1"), JSON.stringify("2, 3"));

function compare_highlight_difference(first_version = "", last_version = "", new_version = "", show_new_version = true, data_first = '', data_last = '') {
    var Match, calculate_operations, consecutive_where, create_index, diff, find_match, find_matching_blocks, html_to_tokens, is_end_of_tag, is_start_of_tag, is_tag, is_whitespace, isnt_tag, op_map, recursively_find_matching_blocks, render_operations, wrap;
    is_end_of_tag = function(char) {
        return char === '>';
    };

    is_start_of_tag = function(char) {
        return char === '<';
    };

    is_whitespace = function(char) {
        return /^\s+$/.test(char);
    };

    is_tag = function(token) {
        return /^\s*<[^>]+>\s*$/.test(token);
    };

    isnt_tag = function(token) {
        return !is_tag(token);
    };

    Match = class Match {
        constructor(start_in_before1, start_in_after1, length1) {
            this.start_in_before = start_in_before1;
            this.start_in_after = start_in_after1;
            this.length = length1;
            this.end_in_before = (this.start_in_before + this.length) - 1;
            this.end_in_after = (this.start_in_after + this.length) - 1;
        }
    };

    html_to_tokens = function(html) {
        var char, current_word, i, len, mode, words;
        mode = 'char';
        current_word = '';
        words = [];
        for (i = 0, len = html.length; i < len; i++) {
            char = html[i];
            switch (mode) {
                case 'tag':
                    if (is_end_of_tag(char)) {
                        current_word += '>';
                        words.push(current_word);
                        current_word = '';
                        if (is_whitespace(char)) {
                            mode = 'whitespace';
                        } else {
                            mode = 'char';
                        }
                    } else {
                        current_word += char;
                    }
                    break;
                case 'char':
                    if (is_start_of_tag(char)) {
                        if (current_word) {
                            words.push(current_word);
                        }
                        current_word = '<';
                        mode = 'tag';
                    } else if (/\s/.test(char)) {
                        if (current_word) {
                            words.push(current_word);
                        }
                        current_word = char;
                        mode = 'whitespace';
                    } else if (/[\w\#@]+/i.test(char)) {
                        current_word += char;
                    } else {
                        if (current_word) {
                            words.push(current_word);
                        }
                        current_word = char;
                    }
                    break;
                case 'whitespace':
                    if (is_start_of_tag(char)) {
                        if (current_word) {
                            words.push(current_word);
                        }
                        current_word = '<';
                        mode = 'tag';
                    } else if (is_whitespace(char)) {
                        current_word += char;
                    } else {
                        if (current_word) {
                            words.push(current_word);
                        }
                        current_word = char;
                        mode = 'char';
                    }
                    break;
                default:
                    throw new Error(`Unknown mode ${mode}`);
            }
        }
        if (current_word) {
            words.push(current_word);
        }
        return words;
    };

    find_match = function(before_tokens, after_tokens, index_of_before_locations_in_after_tokens, start_in_before, end_in_before, start_in_after, end_in_after) {
        var best_match_in_after, best_match_in_before, best_match_length, i, index_in_after, index_in_before, j, len, locations_in_after, looking_for, match, match_length_at, new_match_length, new_match_length_at, ref, ref1;
        best_match_in_before = start_in_before;
        best_match_in_after = start_in_after;
        best_match_length = 0;
        match_length_at = {};
        for (index_in_before = i = ref = start_in_before, ref1 = end_in_before;
            (ref <= ref1 ? i < ref1 : i > ref1); index_in_before = ref <= ref1 ? ++i : --i) {
            new_match_length_at = {};
            looking_for = before_tokens[index_in_before];
            locations_in_after = index_of_before_locations_in_after_tokens[looking_for];
            for (j = 0, len = locations_in_after.length; j < len; j++) {
                index_in_after = locations_in_after[j];
                if (index_in_after < start_in_after) {
                    continue;
                }
                if (index_in_after >= end_in_after) {
                    break;
                }
                if (match_length_at[index_in_after - 1] == null) {
                    match_length_at[index_in_after - 1] = 0;
                }
                new_match_length = match_length_at[index_in_after - 1] + 1;
                new_match_length_at[index_in_after] = new_match_length;
                if (new_match_length > best_match_length) {
                    best_match_in_before = index_in_before - new_match_length + 1;
                    best_match_in_after = index_in_after - new_match_length + 1;
                    best_match_length = new_match_length;
                }
            }
            match_length_at = new_match_length_at;
        }
        if (best_match_length !== 0) {
            match = new Match(best_match_in_before, best_match_in_after, best_match_length);
        }
        return match;
    };

    recursively_find_matching_blocks = function(before_tokens, after_tokens, index_of_before_locations_in_after_tokens, start_in_before, end_in_before, start_in_after, end_in_after, matching_blocks) {
        var match;
        match = find_match(before_tokens, after_tokens, index_of_before_locations_in_after_tokens, start_in_before, end_in_before, start_in_after, end_in_after);
        if (match != null) {
            if (start_in_before < match.start_in_before && start_in_after < match.start_in_after) {
                recursively_find_matching_blocks(before_tokens, after_tokens, index_of_before_locations_in_after_tokens, start_in_before, match.start_in_before, start_in_after, match.start_in_after, matching_blocks);
            }
            matching_blocks.push(match);
            if (match.end_in_before <= end_in_before && match.end_in_after <= end_in_after) {
                recursively_find_matching_blocks(before_tokens, after_tokens, index_of_before_locations_in_after_tokens, match.end_in_before + 1, end_in_before, match.end_in_after + 1, end_in_after, matching_blocks);
            }
        }
        return matching_blocks;
    };

    create_index = function(p) {
        var i, idx, index, len, ref, token;
        if (p.find_these == null) {
            throw new Error('params must have find_these key');
        }
        if (p.in_these == null) {
            throw new Error('params must have in_these key');
        }
        index = {};
        ref = p.find_these;
        for (i = 0, len = ref.length; i < len; i++) {
            token = ref[i];
            index[token] = [];
            idx = p.in_these.indexOf(token);
            while (idx !== -1) {
                index[token].push(idx);
                idx = p.in_these.indexOf(token, idx + 1);
            }
        }
        return index;
    };

    find_matching_blocks = function(before_tokens, after_tokens) {
        var index_of_before_locations_in_after_tokens, matching_blocks;
        matching_blocks = [];
        index_of_before_locations_in_after_tokens = create_index({
            find_these: before_tokens,
            in_these: after_tokens
        });
        return recursively_find_matching_blocks(before_tokens, after_tokens, index_of_before_locations_in_after_tokens, 0, before_tokens.length, 0, after_tokens.length, matching_blocks);
    };

    calculate_operations = function(before_tokens, after_tokens) {
        var action_map, action_up_to_match_positions, i, index, is_single_whitespace, j, last_op, len, len1, match, match_starts_at_current_position_in_after, match_starts_at_current_position_in_before, matches, op, operations, position_in_after, position_in_before, post_processed;
        if (before_tokens == null) {
            throw new Error('before_tokens?');
        }
        if (after_tokens == null) {
            throw new Error('after_tokens?');
        }
        position_in_before = position_in_after = 0;
        operations = [];
        action_map = {
            'false,false': 'replace',
            'true,false': 'insert',
            'false,true': 'delete',
            'true,true': 'none'
        };

        matches = find_matching_blocks(before_tokens, after_tokens);
        matches.push(new Match(before_tokens.length, after_tokens.length, 0));
        for (index = i = 0, len = matches.length; i < len; index = ++i) {
            match = matches[index];
            match_starts_at_current_position_in_before = position_in_before === match.start_in_before;
            match_starts_at_current_position_in_after = position_in_after === match.start_in_after;
            action_up_to_match_positions = action_map[[match_starts_at_current_position_in_before, match_starts_at_current_position_in_after].toString()];
            if (action_up_to_match_positions !== 'none') {
                operations.push({
                    action: action_up_to_match_positions,
                    start_in_before: position_in_before,
                    end_in_before: (action_up_to_match_positions !== 'insert' ? match.start_in_before - 1 : void 0),
                    start_in_after: position_in_after,
                    end_in_after: (action_up_to_match_positions !== 'delete' ? match.start_in_after - 1 : void 0)
                });
            }
            if (match.length !== 0) {
                operations.push({
                    action: 'equal',
                    start_in_before: match.start_in_before,
                    end_in_before: match.end_in_before,
                    start_in_after: match.start_in_after,
                    end_in_after: match.end_in_after
                });
            }
            position_in_before = match.end_in_before + 1;
            position_in_after = match.end_in_after + 1;
        }
        post_processed = [];
        last_op = {
            action: 'none'
        };

        is_single_whitespace = function(op) {
            if (op.action !== 'equal') {
                return false;
            }
            if (op.end_in_before - op.start_in_before !== 0) {
                return false;
            }
            return /^\s$/.test(before_tokens.slice(op.start_in_before, +op.end_in_before + 1 || 9e9));
        };
        for (j = 0, len1 = operations.length; j < len1; j++) {
            op = operations[j];
            if (((is_single_whitespace(op)) && last_op.action === 'replace') || (op.action === 'replace' && last_op.action === 'replace')) {
                last_op.end_in_before = op.end_in_before;
                last_op.end_in_after = op.end_in_after;
            } else {
                post_processed.push(op);
                last_op = op;
            }
        }
        return post_processed;
    };

    consecutive_where = function(start, content, predicate) {
        var answer, i, index, last_matching_index, len, token;
        content = content.slice(start, +content.length + 1 || 9e9);
        last_matching_index = void 0;
        for (index = i = 0, len = content.length; i < len; index = ++i) {
            token = content[index];
            answer = predicate(token);
            if (answer === true) {
                last_matching_index = index;
            }
            if (answer === false) {
                break;
            }
        }
        if (last_matching_index != null) {
            return content.slice(0, +last_matching_index + 1 || 9e9);
        }
        return [];
    };

    wrap = function(tag, content) {
        var length, non_tags, position, rendering, tags;
        rendering = '';
        position = 0;
        length = content.length;
        while (true) {
            if (position >= length) {
                break;
            }
            non_tags = consecutive_where(position, content, isnt_tag);
            position += non_tags.length;
            if (non_tags.length !== 0) {
                rendering += "<" + tag + "> " + non_tags.join('') + "</" + tag + ">";
            }
            if (position >= length) {
                break;
            }
            tags = consecutive_where(position, content, is_tag);
            position += tags.length;
            rendering += tags.join('');
        }
        return rendering;
    };

    op_map = {
        equal: function(op, before_tokens, after_tokens) {
            return before_tokens.slice(op.start_in_before, +op.end_in_before + 1 || 9e9).join('');
        },
        insert: function(op, before_tokens, after_tokens) {
            var val;
            val = after_tokens.slice(op.start_in_after, +op.end_in_after + 1 || 9e9);
            return wrap('ins', val);
        },
        delete: function(op, before_tokens, after_tokens) {
            var val;
            val = before_tokens.slice(op.start_in_before, +op.end_in_before + 1 || 9e9);
            return wrap('del', val);
        }
    };

    op_map.replace = function(op, before_tokens, after_tokens) {
        return (op_map.delete(op, before_tokens, after_tokens)) + (op_map.insert(op, before_tokens, after_tokens));
    };

    render_operations = function(before_tokens, after_tokens, operations) {
        var i, len, op, rendering;
        rendering = '';
        for (i = 0, len = operations.length; i < len; i++) {
            op = operations[i];
            rendering += op_map[op.action](op, before_tokens, after_tokens);
        }
        return rendering;
    };

    diff = function(before, after) {
        var ops;
        if (before === after) {
            return before;
        }
        before = html_to_tokens(before);
        after = html_to_tokens(after);
        ops = calculate_operations(before, after);
        return render_operations(before, after, ops);
    };
    diff.html_to_tokens = html_to_tokens;
    diff.find_matching_blocks = find_matching_blocks;
    find_matching_blocks.find_match = find_match;
    find_matching_blocks.create_index = create_index;
    diff.calculate_operations = calculate_operations;
    diff.render_operations = render_operations;
    if (typeof define === 'function') {
        define([], function() {
            return diff;
        });
    } else if (typeof module !== "undefined" && module !== null) {
        module.exports = diff;
    } else {
        this.htmldiff = diff;
    }

    if (data_first != '' && data_last != '') {
        let originalHTML = JSON.parse(data_first);

        let newHTML = JSON.parse(data_last);

        // Diff HTML strings
        let output = htmldiff(originalHTML, newHTML);

        if (first_version != '' && last_version != '') {
            // Show HTML diff output as HTML (crazy right?)!
            document.getElementById(last_version).innerHTML = output;
            document.getElementById(first_version).innerHTML = originalHTML;
        }

        if (new_version != '') {
            document.getElementById(new_version).innerHTML = newHTML;
        }

        if (!show_new_version) {
            document.getElementById("new_version").style.display = "None";
        }
    }
}

function initializeTree(tree, options, openAllIcon) {
    var openedClass = 'fa-minus-circle';
    var closedClass = 'fa-plus-circle';

    if (options) {
        if (options.openedClass) {
            openedClass = options.openedClass;
        }
        if (options.closedClass) {
            closedClass = options.closedClass;
        }
    }

    tree.classList.add("tree__custom");

    tree.querySelectorAll('li').forEach(function (branch) {
        if (branch.querySelector('ul')) {
            if (openAllIcon) {
                branch.insertAdjacentHTML('afterbegin', `<i class='fa indicator ${openedClass}'></i>`);
            } else {
                branch.insertAdjacentHTML('afterbegin', `<i class='fa indicator ${closedClass}'></i>`);
            }
            branch.classList.add('branch');

            branch.addEventListener('click', function (e) {
                if (e.target === branch) {
                    var icon = branch.querySelector('i.indicator');
                    icon.classList.toggle(openedClass);
                    icon.classList.toggle(closedClass);
                    var childUl = branch.querySelector('ul');
                    if (childUl) {
                        childUl.style.display = childUl.style.display === 'none' ? '' : 'none';
                    }
                }
            });

            if (!openAllIcon) {
                var childUl = branch.querySelector('ul');
                if (childUl) {
                    childUl.style.display = 'none';
                }
            }
        }
    });

    tree.querySelectorAll('.branch .indicator').forEach(function (indicator) {
        indicator.addEventListener('click', function (e) {
            indicator.closest('li').click();
        });
    });

    tree.querySelectorAll('.branch > a:not(.vsic-child)').forEach(function (anchor) {
        anchor.addEventListener('click', function (e) {
            anchor.closest('li').click();
        });
    });

    tree.querySelectorAll('.branch > button').forEach(function (button) {
        button.addEventListener('click', function (e) {
            button.closest('li').click();
        });
    });
}

// Initialization of treeviews
var tree1 = document.getElementById('tree1');
if (tree1) {
    initializeTree(tree1, { openedClass: 'fa-minus-circle', closedClass: 'fa-plus-circle' }, false);
}

var tree1Class = document.querySelectorAll('.tree1');
if (tree1Class.length > 0) {
    tree1Class.forEach(function(e) {initializeTree(e, { openedClass: 'fa-minus-circle', closedClass: 'fa-plus-circle' }, false);});
}

// $('#tree1').treed({openedClass:'fa fa-diamond', closedClass:'fa fa-diamond'});

$("select[name='table_one_length']").addClass("form-control");
var $quyet_dinh_normal = $('#quyet_dinh_normal .is_fast').length;
if ($quyet_dinh_normal > 0) {
    $('#quyet_dinh_normal').addClass('no-plane');
}

if ($('.item__notifi').length > 0) {
    $(".content_notifi").slideDown(500);
}

$(".item__notifi").click(function() {
    $(".content_notifi").slideDown(500);
    $(".content_notifi").find('.custom_border').addClass('chopsang');
    $(".content_notifi").addClass('zoomIn');
    setTimeout(function() {
        $(".content_notifi").find('.custom_border').removeClass('chopsang');
        $(".content_notifi").removeClass('zoomIn');
    }, 1000);
});
$(".close_notifi").click(function() {
    $(this).parent(".content_notifi").slideUp(500);
});
$("#onlrule-violate-msg i.fa-times-circle").click(function() {
    $("#onlrule-violate-msg").slideUp(500);
});
$("#onlrule-violate").click(function() {
    $("#onlrule-violate-msg").slideDown(500);
    // cho cuộn đến chỗ này
    var warning = document.getElementById('onlrule-violate-msg');
    warning.scrollIntoView({ behavior: "smooth", inline: "nearest" });
});

$('.online-rule-violate').click(function(){
    handleClickViolateIcon(this);
})
function handleClickViolateIcon(e) {
    if ($(e).siblings().css('display') === 'none') {
        $(e).siblings().slideDown(500);
    } else {
        $(e).siblings().slideUp(500);
    }
}
function redirect_link(link) {
    window.location.href = link;
    return;
}
