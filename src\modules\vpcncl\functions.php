<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Thu, 15 Sep 2011 02:25:16 GMT
 */

if (!defined('NV_SYSTEM')) {
    die('Stop!!!');
}

global $nv_Request;

define('NV_IS_MOD_VPCNCL', true);

define('ORG_TYPES', [
    'vilas' => ['id' => 0],
    'vicas' => ['id' => 1],
    'vias' => ['id' => 2],
    'vilas-med' => ['id' => 3],
    'vipas' => ['id' => 4],
    'viras' => ['id' => 5],
]);

define('ORG_STATUSES', [
    1 => $nv_Lang->getModule('hoat_dong'),
    0 => $nv_Lang->getModule('dinh_chi'),
    -1 => $nv_Lang->getModule('huy_bo'),
]);

$type_id = ORG_TYPES[$module_name]['id'];

$array_data = $location_map = $location_id_map = $linhvuc_map = $linhvuc_id_map = [];
$show_specializes = [0, 1, 3, 4];
$hiden_host_unit = [2, 4, 5];
$view_by_id = null;
// Khởi tạo dữ liệu
if (in_array($type_id, $show_specializes)) {
    get_linhvuc_data();
}
get_location_data();

if (isset($array_op[0])) {
    if (preg_match('/([0-9]+)$/i', $array_op[0])) {
        $tmp = explode('-', $array_op[0]);
        $org_id = end($tmp);
        $op = 'detail';
    }
    if (preg_match('/^(tt-)/i', $array_op[0]) && isset($location_map[str_replace('tt-', '', $array_op[0])])) {
        $is_filter_province = true;
        $view_by_id = $location_map[str_replace('tt-', '', $array_op[0])];
        $op = 'main';
    }
    if (preg_match('/^(lv-)/i', $array_op[0]) && isset($linhvuc_map[str_replace('lv-', '', $array_op[0])])) {
        $is_filter_linhvuc = true;
        $view_by_id = $linhvuc_map[str_replace('lv-', '', $array_op[0])];
        $op = 'main';
    }
}

function process_province($province_id, $location_id_map)
{
    global $module_name;
    $province_ids = explode(',', $province_id);

    return implode(', ', array_map(function ($id) use ($location_id_map, $module_name) {
        if (!isset($location_id_map[$id])) {
            $href = '#';
            $title = 'N/A';
        } else {
            $href = NV_BASE_SITEURL . 'index.php?' .
                NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' .
                NV_NAME_VARIABLE . '=' . $module_name . '&amp;' .
                NV_OP_VARIABLE . '=tt-' . $location_id_map[$id]['alias'];
            $title =  $location_id_map[$id]['title'];
        }
        return '<a href="' . $href . '">' . $title . '</a>';
    }, $province_ids));
}

function process_linhvuc($special_id, $linhvuc_id_map)
{
    global $module_name;
    $specialize_ids = explode(',', $special_id);
    return implode(', ', array_map(function ($id) use ($linhvuc_id_map, $module_name) {
        if (!isset($linhvuc_id_map[$id])) {
            $href = '#';
            $title = 'N/A';
        } else {
            $href = NV_BASE_SITEURL . 'index.php?' .
                NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' .
                NV_NAME_VARIABLE . '=' . $module_name . '&amp;' .
                NV_OP_VARIABLE . '=lv-' . $linhvuc_id_map[$id]['alias'];
            $title = $linhvuc_id_map[$id]['title'];
        }
        return '<a href="' . $href . '">' . $title . '</a>';
    }, $specialize_ids));
}

function process_donvichuquan($host_unit, $solicitor_id, $contractor_id)
{
    global $db, $site_mods, $global_config;
    $href = '#';
    $link = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=';

    if ($solicitor_id != 0) {
        $result_solicitor = $db->query('SELECT id, alias FROM ' . BID_PREFIX_GLOBAL . '_solicitor WHERE id = ' . $solicitor_id)->fetch();
        if (!empty($result_solicitor)) {
            $href = $link . $global_config['module_bidding_name'] . '&amp;' . NV_OP_VARIABLE . '=' . $site_mods['bidding']['alias']['solicitor'] . '/' . $result_solicitor['alias'] . '-' . $result_solicitor['id'];
        }
    }

    if ($contractor_id != 0) {
        $result_business = $db->query('SELECT id, companyname FROM ' . BUSINESS_PREFIX_GLOBAL . '_info WHERE id = ' . $contractor_id)->fetch();
        if  (!empty($result_business)) {
            $href = $link . 'businesslistings&amp;' . NV_OP_VARIABLE . '=' . $site_mods['businesslistings']['alias']['detail'] . '/' . change_alias($result_business['companyname']) . '-' . $result_business['id'];
        }
    }
    return $href != '#' ? '<a href="' . $href . '">' . $host_unit . '</a>' : $host_unit;
}

function process_file_download($files_data)
{
    $files = explode(',', $files_data);
    return implode('<br>', array_map(function ($file) {
        $file_elements = explode('-', $file);
        if (empty($file_elements[1])) return '';
        return '<a target="_blank" rel="nofollow" href="' . trim($file_elements[1]) . '">' .
            '<i class="fa fa-cloud-download" aria-hidden="true"></i> ' . trim($file_elements[0]) .
            '</a>';
    }, $files));
}

function get_location_data()
{
    global $db, $nv_Cache, $array_data, $location_map, $location_id_map, $nv_Lang;
    $db->sqlreset()
        ->select('id, title, alias')
        ->from(NV_PREFIXLANG . '_' . 'location_province')
        ->order('title ASC');
    $array_data['province'] = $nv_Cache->db($db->sql(), 'id', 'location_bidding');
    $array_data['province'][0] = array(
        'id' => 0,
        'title' => $nv_Lang->getModule('no_title'),
        'alias' => 'Chua-phan-loai'
    );
    foreach ($array_data['province'] as $value) {
        $location_map[$value['alias']] = $value['id'];
        $location_id_map[$value['id']] = [
            'alias' => $value['alias'],
            'title' => $value['title']
        ];
    }
}

function get_linhvuc_data()
{
    global $db, $nv_Cache, $module_name, $array_data, $linhvuc_map, $linhvuc_id_map, $type_id;
    $db->sqlreset()
        ->select('id, title, alias')
        ->from(NV_PREFIXLANG . '_vpcncl_linhvuc')
        ->where('type = ' . $type_id)
        ->order('title ASC');
    $array_data['linhvuc'] = $nv_Cache->db($db->sql(), '', $module_name);

    foreach ($array_data['linhvuc'] as $value) {
        $linhvuc_map[$value['alias']] = $value['id'];
        $linhvuc_id_map[$value['id']] = [
            'id'    => $value['id'],
            'alias' => $value['alias'],
            'title' => $value['title']
        ];
    }
}

function PopupLogin($mess, $page_url = '')
{
    global $module_info;
    $xtpl = new XTemplate('popup_login.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/vpcncl');
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('POPUP_LOGIN', $mess);
    if ($page_url != '') {
        $xtpl->assign('PAGE_URL', nv_redirect_encrypt(urlRewriteWithDomain($page_url, NV_MY_DOMAIN)));
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}
