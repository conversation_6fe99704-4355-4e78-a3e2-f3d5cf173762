<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<link type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css" rel="stylesheet" />
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/{TEMPLATE}/js/block_search_training.js"></script>
<div class="ltablesearch-cont blcourse panel-body">
    <form class="ltablesearch block-bidding" id="blcourse{BLOCKID}" action="{ACTION}" method="get"
        onsubmit="return bl_checkSearchFormc(this);" data-id="{BLOCKID}" data-action="{FORM_ACTION}"
        data-action1="{FORM_ACTION1}" data-action2="{FORM_ACTION2}" data-maxspan="{MAXSPAN}" data-mindate="{MINDATE}"
        data-opens="left">
        <!-- BEGIN: no_rewrite -->
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" data-default="{NV_LANG_DATA}" /> 
        <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" data-default="{MODULE_NAME}" /> 
        <input type="hidden" name="{NV_OP_VARIABLE}" value="search" data-default="search" />
        <!-- END: no_rewrite -->
        <div class="row block margin-bottom-lg">
            <div class="col-sm-24 col-md-24">
                <div class="row">
                    <div class="col-xs-24">
                        <div class="form-group">
                            <h2>{LANG.type_search}</h2>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-24 col-sm-16">
                        <div class="form-group col-sm-10 col-md-10 pdl0">
                            <label class="control-label">{LANG.type_couse}</label>
                            <select class="form-control" onchange="bl_changeTypeInfoc()" name="type_couse">
                                <!-- BEGIN: type_info -->
                                <option value="{TYPE.key}" {TYPE.selected}>{TYPE.title}</option>
                                <!-- END: type_info -->
                            </select>
                        </div>

                        <div class="form-group col-sm-14 col-md-14 pdl0">
                            <label class="control-label">{LANG.search_keywords}</label>
                            <div class="row">
                                <div class="col-xs-24">
                                    <div class="input-group">
                                        <input class="form-control" id="ls_key_bidding" type="text" name="q" value="{Q}" data-default="" maxlength="200" data-error="{LANG.type_text_error}" />
                                        <div class="input-group-btn">
                                            <input class="btn btn-primary submit_search" type="submit" value="{LANG.search}" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-24 col-sm-8">
                        <input type="hidden" name="is_advance" value="{ADVANCE}" data-default="0" />
                        <div class="panel panel-default" <!-- BEGIN: advance_bl_hide -->
                            style="display: none"
                            <!-- END: advance_bl_hide -->
                            > <a class="panel-heading btn-search" <!-- BEGIN: advance_btn_hide --> style="display: none"
                                <!-- END: advance_btn_hide --> href="javascript:void(0);"
                                data-search-simple="{LANG.search_simple}" data-search-advance="{LANG.search_advance}"
                                data-icon-search-simple="icon-chevron-down"
                                data-icon-search-advance="icon-chevron-up"><em
                                    class="<!-- BEGIN: advance_icon_0 -->icon-chevron-down <!-- END: advance_icon_0 --><!-- BEGIN: advance_icon_1 -->icon-chevron-up <!-- END: advance_icon_1 -->margin-right-sm"></em><strong
                                    class="txt">{LANG_ADVANCE}</strong></a>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="panel-body advance-search" style="display: none;">
                        <div id="show_key">
                            <div class="row">
                                <div class="search_couses"
                                <!-- BEGIN: no_couses -->
                                style="display: none"
                                <!-- END: no_couses -->
                                >
                                    <div class="col-sm-24 col-md-24">
                                        <div class="form-group type_org">
                                            <label class="control-label">{LANG.type_course}</label>
                                            <label class="custom-radio"> <input type="radio" name="typecourse" value="1" {TYPE_SEARCH1}><span class="txt">{LANG.type_course_LCNT}</span></label>
                                            <label class="custom-radio"> <input type="radio" name="typecourse" value="2" {TYPE_SEARCH2}><span class="txt">{LANG.type_course_LCNDT}</span></label>
                                            <label class="custom-radio"> <input type="radio" name="typecourse" value="3" {TYPE_SEARCH3}><span class="txt">{LANG.type_course_KHAC}</span></label>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.training}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <input class="form-control" type="text" name="training"
                                                        value="{Q8}" data-default="" maxlength="200"
                                                        data-error="{LANG.type_text_error}" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.certificate_singer}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <input class="form-control" type="text" name="certificate_singer"
                                                        value="{Q3}" data-default="" maxlength="200"
                                                        data-error="{LANG.type_text_error}" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.teacher} </label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <input class="form-control" type="text" name="teacher"
                                                        value="{Q4}" data-default="" maxlength="200"
                                                        data-error="{LANG.type_text_error}" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.role} </label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <input class="form-control" type="text" name="role"
                                                        value="{Q5}" data-default="" maxlength="200"
                                                        data-error="{LANG.type_text_error}" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.certificate_date} </label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <input type="hidden" name="sfrom_certificate_date" value="{FROM}" data-default="{FROM_DEFAULT}" /> <input type="hidden" name="sto_certificate_date" value="{TO}" data-default="{TO_DEFAULT}" /> <input class="form-control search_range" type="text" value="{FROM} - {TO}" data-default="{FROM_DEFAULT} - {TO_DEFAULT}" data-cancel-lang="{GLANG.cancel}" data-apply-lang="{GLANG.apply}" data-customrange-lang="{GLANG.custom_range}" data-today-lang="{LANG.today}" data-lastday-lang="{LANG.last_1_day}" data-last7days-lang="{LANG.last_7_days}" data-last14days-lang="{LANG.last_14_days}" data-last30days-lang="{LANG.last_30_days}" data-thismonth-lang="{LANG.this_month}" data-lastmonth-lang="{LANG.last_month}" data-last3months-lang="{LANG.last_3_months}" data-last6months-lang="{LANG.last_6_months}" data-last-maxspan-lang="{LAST_MAXSPAN_LANG}" data-lastall="{LAST_ALL_DAYS}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.time_traning} </label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <input type="hidden" name="sfrom_time_traning" value="{FROMTIMETRANING}" data-default="{FROM_DEFAULT}" /> <input type="hidden" name="sto_time_traning" value="{TOTIMETRANING}" data-default="{TO_DEFAULT}" /> <input class="form-control search_range_time" type="text" value="{FROMTIMETRANING} - {TOTIMETRANING}" data-default="{FROM_DEFAULT} - {TO_DEFAULT}" data-cancel-lang="{GLANG.cancel}" data-apply-lang="{GLANG.apply}" data-customrange-lang="{GLANG.custom_range}" data-today-lang="{LANG.today}" data-lastday-lang="{LANG.last_1_day}" data-last7days-lang="{LANG.last_7_days}" data-last14days-lang="{LANG.last_14_days}" data-last30days-lang="{LANG.last_30_days}" data-thismonth-lang="{LANG.this_month}" data-lastmonth-lang="{LANG.last_month}" data-last3months-lang="{LANG.last_3_months}" data-last6months-lang="{LANG.last_6_months}" data-last-maxspan-lang="{LAST_MAXSPAN_LANG}" data-lastall="{LAST_ALL_DAYS}">
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.idregion}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <select data-selected="{SREGION}" class="form-control selRegions" onchange="bl_changeLocationRegion()"
                                                            name="idregion" style="width: 100%" data-default="0">
                                                        <!-- BEGIN: loopregions -->
                                                        <option value="{REGION.key}" {REGION.selected}>{REGION.title}</option>
                                                        <!-- END: loopregions -->
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="search_student"
                                <!-- BEGIN: no_student -->
                                style="display: none"
                                <!-- END: no_student -->
                                >
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.certificate_code}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <input class="form-control" type="text" name="certificate_code"
                                                        value="{Q6}" data-default="" maxlength="200"
                                                        data-error="{LANG.type_text_error}" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.identify_code}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <input class="form-control" type="text" name="identify_code"
                                                        value="{Q7}" data-default="" maxlength="200"
                                                        data-error="{LANG.type_text_error}" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.birthday}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <input type="hidden" name="sfrom_birthday" value="{FROMTIMEBIRTHDAY}" data-default="{FROMTIMEBIRTHDAY}" /> 
                                                    <input class="form-control search_range_birthday" type="text" value="{FROMTIMEBIRTHDAY}" data-default="{FROMTIMEBIRTHDAY}" data-cancel-lang="{GLANG.cancel}" data-apply-lang="{GLANG.apply}" data-customrange-lang="{GLANG.custom_range}" data-today-lang="{LANG.today}" data-lastday-lang="{LANG.last_1_day}" data-last7days-lang="{LANG.last_7_days}" data-last14days-lang="{LANG.last_14_days}" data-last30days-lang="{LANG.last_30_days}" data-thismonth-lang="{LANG.this_month}" data-lastmonth-lang="{LANG.last_month}" data-last3months-lang="{LANG.last_3_months}" data-last6months-lang="{LANG.last_6_months}" data-last-maxspan-lang="{LAST_MAXSPAN_LANG}" data-lastall="{LAST_ALL_DAYS}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.place}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <select data-selected="{SPROVINCE}" name="idprovince[]" multiple="multiple" id="keyword_id_province" class="form-control fselect2" style="width: 100%;">
                                                        <option value="-1">{LANG.idprovince}</option>
                                                        <!-- BEGIN: province -->
                                                        <option value="{PROVINCE.key}"{PROVINCE.selected}>{PROVINCE.title}</option>
                                                        <!-- END: province -->
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.issuedate}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <input type="hidden" name="sfrom_issuedate" value="{FROMISSUE}" data-default="{FROM_DEFAULT}" /> <input type="hidden" name="sto_issuedate" value="{TOISSUE}" data-default="{TO_DEFAULT}" /> <input class="form-control search_range_issuedate" type="text" value="{FROMISSUE} - {TOISSUE}" data-default="{FROM_DEFAULT} - {TO_DEFAULT}" data-cancel-lang="{GLANG.cancel}" data-apply-lang="{GLANG.apply}" data-customrange-lang="{GLANG.custom_range}" data-today-lang="{LANG.today}" data-lastday-lang="{LANG.last_1_day}" data-last7days-lang="{LANG.last_7_days}" data-last14days-lang="{LANG.last_14_days}" data-last30days-lang="{LANG.last_30_days}" data-thismonth-lang="{LANG.this_month}" data-lastmonth-lang="{LANG.last_month}" data-last3months-lang="{LANG.last_3_months}" data-last6months-lang="{LANG.last_6_months}" data-last-maxspan-lang="{LAST_MAXSPAN_LANG}" data-lastall="{LAST_ALL_DAYS}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-12">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.result}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <select class="form-control result" onchange="bl_changeResult()"
                                                            name="result" style="width: 100%" data-default="0">
                                                        <!-- BEGIN: loopresult -->
                                                        <option value="{RESULT.key}" {RESULT.selected}>{RESULT.title}</option>
                                                        <!-- END: loopresult -->
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row form-group text-center">
                                <button class="btn btn-primary submit_search" type="button" onclick="$('#blcourse{BLOCKID}').submit();">{LANG.search}</button>
                                <input class="btn btn-default form-reset" type="button" value="{GLANG.reset}" onclick="bl_formReset();" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<!-- END: main -->