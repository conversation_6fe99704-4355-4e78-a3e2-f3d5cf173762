<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 10 Nov 2020 06:56:08 GMT
 */

if (!defined('NV_IS_MOD_DAU_GIA')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('auctioneer_title_seo');
$description = $nv_Lang->getModule('meta_description_list_auctioneer');
$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('auctioneer'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['auctioneer'], true)
);

// Khởi tạo mảng chứa các tham số tìm kiếm
$array_search = [];
$array_search['name'] = $nv_Request->get_title('name', 'get', '');
$array_search['cchn'] = $nv_Request->get_title('cchn', 'get', '');
$array_search['province'] = $nv_Request->get_int('province', 'get', 0);
$array_search['district'] = $nv_Request->get_int('district', 'get', 0);
$array_search['ward'] = $nv_Request->get_int('ward', 'get', 0);
$array_search['bidder'] = $nv_Request->get_int('bidder', 'get', 0);

$location_only = false;
$base_url_location = '';

if (empty($array_search['name']) && empty($array_search['cchn']) &&
    empty($array_search['bidder'])) {

    $province_id = 0;

    if (preg_match('/^T\-([a-zA-Z0-9\-]+)\-([0-9]+)$/i', ($array_op[1] ?? ''), $m)) {
        $province_id = intval($m[2]);
    } elseif ($array_search['province'] > 0) {
        $province_id = $array_search['province'];
    }

    if ($province_id > 0) {
        $sql = "SELECT id, alias, title FROM " . NV_PREFIXLANG . "_location_province WHERE status=1 AND id = " . $db->quote($province_id);
        $result = $db->query($sql);
        if ($row = $result->fetch()) {
            $array_search['province'] = $row['id'];
            $base_url_location = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['auctioneer'] . '/T-' . $row['alias'] . '-' . $array_search['province'];
            $location_only = true;
            $page_title = $nv_Lang->getModule('auctioneer') . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'];
            $description = sprintf($nv_Lang->getModule('meta_description_list_auctioneer'), $row['title']);
            $meta_property['og:description'] = $description;
        }
    } else {
        $description = sprintf($nv_Lang->getModule('meta_description_list_auctioneer'), $nv_Lang->getModule('vietnam'));
        $meta_property['og:description'] = $description;
    }
}

$where = [];
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['auctioneer'];

if (!empty($array_search['province'])) {
    $where[] = "id_province=" . $array_search['province'];
    $base_url .= "&amp;province=" . $array_search['province'];
}

if (!empty($array_search['district'])) {
    $where[] = "id_district=" . $array_search['district'];
    $base_url .= "&amp;district=" . $array_search['district'];
}

if (!empty($array_search['ward'])) {
    $where[] = "id_ward=" . $array_search['ward'];
    $base_url .= "&amp;ward=" . $array_search['ward'];
}

if (!empty($array_search['bidder'])) {
    $where[] = "id_bidder=" . $array_search['bidder'];
    $base_url .= "&amp;bidder=" . $array_search['bidder'];
}

if (!empty($array_search['name'])) {
    $where_name = [];
    $arr_key = explode(',', $array_search['name']);
    foreach ($arr_key as $key) {
        $key = trim($key);
        if (!empty($key)) {
            $where_name[] = "name LIKE '%" . $db->dblikeescape($key) . "%'";
        }
    }
    if (!empty($where_name)) {
        $where[] = "(" . implode(' OR ', $where_name) . ")";
        $base_url .= "&amp;name=" . urlencode($array_search['name']);
        $nv_BotManager->setPrivate(); // Issue: https://vinades.org/dauthau/dauthau.info/-/issues/3201
    }
}

if (!empty($array_search['cchn'])) {
    $where_cchn = [];
    $arr_key = explode(',', $array_search['cchn']);
    foreach ($arr_key as $key) {
        $key = trim($key);
        if (!empty($key)) {
            $where_cchn[] = "number_cchn LIKE '%" . $db->dblikeescape($key) . "%'";
        }
    }
    if (!empty($where_cchn)) {
        $where[] = "(" . implode(' OR ', $where_cchn) . ")";
        $base_url .= "&amp;cchn=" . urlencode($array_search['cchn']);
        $nv_BotManager->setPrivate(); // Issue: https://vinades.org/dauthau/dauthau.info/-/issues/3201
    }
}

$perpage = 10;
$page = $nv_Request->get_page('page', 'get', 1);

if ($page < 1) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_wrong_page') . $btn, 'danger');

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

$db->sqlreset()
    ->select('COUNT(id)')
    ->from('nv4_dau_gia_bidder_dgv');

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}

$sth = $db->prepare($db->sql());
$sth->execute();
$total = $sth->fetchColumn();

if ($location_only && !empty($base_url_location)) {
    $base_url = $base_url_location;
}

$urlappend = '&amp;page=';
// Kiểm tra đánh số trang
betweenURLs($page, ceil($total / $perpage), $base_url, $urlappend, $prevPage, $nextPage);

$db->select('*')
    ->order('id DESC')
    ->limit($perpage)
    ->offset(($page - 1) * $perpage);
$sth = $db->prepare($db->sql());
$sth->execute();
$i = ($page - 1) * $perpage;
$array_data = [];
while ($view = $sth->fetch()) {
    $view['stt'] = $i + 1;
    $array_data[$view['id']] = $view;
    $i++;
}

if (empty($array_data)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
}

// Xử lý title, description phân trang
if ($page > 1) {
    $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
    if (!empty($module_info['funcs'][$op]['description'])) {
        $description = $module_info['funcs'][$op]['description'] . NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
        $meta_property['og:description'] = $description;
    } elseif (!empty($description)) {
        $description .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
        $meta_property['og:description'] = $description;
    }
}

$generate_page = nv_generate_page($base_url, $total, $perpage, $page);

// lấy page_url
$page_url = $base_url;
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}
$canonicalUrl = getCanonicalUrl($page_url);

$contents = nv_theme_dau_gia_auctioneer($array_data, $generate_page);

// Schema: Danh sách đấu giá viên
if (!empty($array_data)) {
    $schema_items = [];
    foreach ($array_data as $dgv) {
        $person = [
            "@type" => "Person",
            "name" => $dgv['name'],
            "identifier" => [
                [
                    "@type" => "PropertyValue",
                    "name" => $nv_Lang->getModule('dgv_number_cchn'),
                    "value" => $dgv['number_cchn']
                ]
            ],
            "jobTitle" => $nv_Lang->getModule('auctioneer')
        ];
        $schema_items[] = $person;
    }
    $schema_collection = [
        '@context' => 'https://schema.org',
        '@type' => 'CollectionPage',
        'name' => ($page > 1) ? $nv_Lang->getModule('auctioneer_schema_page_title') . NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page : $nv_Lang->getModule('auctioneer_schema_page_title'),
        'url' => $canonicalUrl,
        'mainEntity' => [
            '@type' => 'ItemList',
            'name' => ($page > 1) ? $nv_Lang->getModule('auctioneer_schema_page_title_list') . NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page : $nv_Lang->getModule('auctioneer_schema_page_title_list'),
            'itemListElement' => $schema_items
        ]
    ];
    $nv_schemas[] = $schema_collection;
}


include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
