<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_IS_MOD_BUSINESSLISTINGS'))
    die('Stop!!!');
use NukeViet\Point\Point;
use NukeViet\Point\Url;

$industry = $nv_Request->get_array('industry', 'get', []);
$province = $nv_Request->get_int('province', 'get', -1);
$district = $nv_Request->get_int('district', 'get', 0);
$ward = $nv_Request->get_int('ward', 'get', 0);
$where = [];
$page = 1;
$per_page = $global_array_config['show_list_number'];
$module = "location";
$arr_beackcolumn = [];
if (isset($array_op[2]) and preg_match('/^page\-([0-9]+)$/', $array_op[2], $m)) {
    $page = $m[1];
}

if (isset($array_op[1])) {
    $a = substr($array_op[1], 0, 1);
    $b = substr($array_op[1], 2);
    $_alias = "";

    if (preg_match('/^([a-zA-Z0-9\-]+)\-([0-9]+)$/i', $b, $m)) {
        $b = $m[2];
        $_alias = $m[1];
    } else {
        nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
    }

    if ($a == "T") {
        if ($b == 0) {
            $arr_beackcolumn['T'] = array(
                "id" => 0,
                "key" => "T",
                "title" => $nv_Lang->getModule('undefined'),
                "alias" => strtolower(change_alias($nv_Lang->getModule('undefined')))
            );
            $province = $id = 0;
            $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $other_lang_module_info['alias'][$op] . "/T-" . $other_lang_alias . '-' . $id;
        } else {
            $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_" . $module . "_province WHERE status=1 AND id = " . $db->quote($b);
            $result = $db->query($sql);
            list ($id, $title, $alias) = $result->fetch(3);
            if (empty($id)) {
                $sql = "SELECT id, alias FROM " . NV_PREFIXLANG . "_" . $module . "_province WHERE status=1 AND alias = " . $db->quote($_alias);
                $result = $db->query($sql);
                list ($id, $alias) = $result->fetch(3);
                if (empty($id)) {
                    nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('admin_no_allow_func'), 404);
                } else {
                    nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation/T-" . $alias . '-' . $id);
                }
            }
            $arr_beackcolumn['T'] = array(
                "id" => $id,
                "key" => "T",
                "title" => $title,
                "alias" => $alias
            );
            $province = $id;
            $other_lang_alias = $db->query("SELECT alias FROM " . $other_lang_prefix . "_" . $module . "_province WHERE id = " . $db->quote($id))
                ->fetchColumn();
            $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $other_lang_module_info['alias'][$op] . "/T-" . $other_lang_alias . '-' . $id;
        }
    } elseif ($a == "H") {
        $sql = "SELECT id, idprovince, title, alias FROM " . NV_PREFIXLANG . "_" . $module . "_district WHERE status=1 AND id= " . $db->quote($b);
        $result = $db->query($sql);
        list ($iddistrict, $idprovince, $title, $alias) = $result->fetch(3);
        if (empty($iddistrict)) {
            $sql = "SELECT id, idprovince, title, alias FROM " . NV_PREFIXLANG . "_" . $module . "_district WHERE status=1 AND alias= " . $db->quote(substr($array_op[1], 2));
            $result = $db->query($sql);
            list ($iddistrict, $idprovince, $title, $alias) = $result->fetch(3);
            if (!empty($iddistrict)) {
                $url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "/H-" . $alias . '-' . $iddistrict;
                if ($page > 1) {
                    $url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "/H-" . $alias . '-' . $iddistrict . '/' . 'page-' . $page;
                }
                nv_redirect_location($url);
            }
        }

        if (empty($iddistrict)) {
            nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name);
        }

        $arr_beackcolumn['H'] = array(
            "id" => $iddistrict,
            "key" => "H",
            "title" => $title,
            "alias" => $alias
        );
        // lay tinh cua huyen
        $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_" . $module . "_province WHERE status=1 AND id = " . intval($idprovince);
        $result = $db->query($sql);
        list ($id, $title, $alias) = $result->fetch(3);
        $arr_beackcolumn['T'] = array(
            "id" => $id,
            "key" => "T",
            "title" => $title,
            "alias" => $alias
        );
        $district = $iddistrict;
        $other_lang_alias = $db->query("SELECT alias FROM " . $other_lang_prefix . "_" . $module . "_district WHERE id = " . $db->quote($iddistrict))
            ->fetchColumn();
        $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $other_lang_module_info['alias'][$op] . "/H-" . $other_lang_alias . '-' . $iddistrict;
    } elseif ($a == "X") {
        $sql = "SELECT id, idprovince, iddistrict, title, alias FROM " . NV_PREFIXLANG . "_" . $module . "_ward WHERE status=1 AND id = " . $db->quote($b);
        $result = $db->query($sql);
        list ($id, $idprovice, $iddistrict, $title, $alias) = $result->fetch(3);
        if (empty($id)) {
            $sql = "SELECT id, idprovince, iddistrict, title, alias FROM " . NV_PREFIXLANG . "_" . $module . "_ward WHERE status=1 AND alias= " . $db->quote(substr($array_op[1], 2));
            $result = $db->query($sql);
            list ($id, $idprovice, $iddistrict, $title, $alias) = $result->fetch(3);
            if (!empty($id)) {
                $url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation/X-" . $alias . '-' . $id;
                if ($page > 1) {
                    $url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation/X-" . $alias . '-' . $id . '/' . 'page-' . $page;
                }
                nv_redirect_location($url);
            }
        }

        if (empty($id)) {
            nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name);
        }

        // breakcolumn
        $arr_beackcolumn['X'] = array(
            "id" => $id,
            "key" => "X",
            "title" => $title,
            "alias" => $alias
        );
        // lay huyen cua xa
        $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_" . $module . "_district WHERE status=1 AND id = " . intval($iddistrict) . "";
        $result = $db->query($sql);
        list ($iddistrict, $title, $alias) = $result->fetch(3);
        $arr_beackcolumn['H'] = array(
            "id" => $iddistrict,
            "key" => "H",
            "title" => $title,
            "alias" => $alias
        );
        // lay tinh cua huyen
        $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_" . $module . "_province WHERE status=1 AND id = " . intval($idprovice) . "";
        $result = $db->query($sql);
        list ($idprovince, $title, $alias) = $result->fetch(3);
        $arr_beackcolumn['T'] = array(
            "id" => $idprovince,
            "key" => "T",
            "title" => $title,
            "alias" => $alias
        );

        $ward = $id;
        $other_lang_alias = $db->query("SELECT alias FROM " . $other_lang_prefix . "_" . $module . "_ward WHERE id = " . $db->quote($id))
            ->fetchColumn();
        $global_lang_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . $other_lang . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $other_lang_module_info['alias'][$op] . "/X-" . $other_lang_alias . '-' . $id;
    } else {
        nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
    }
} else {
    nv_redirect_location(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name);
}

$list_compare = $nv_Request->get_title('lstcpctt', 'cookie', true);

$list_compare = json_decode($list_compare, true);
!is_array($list_compare) && $list_compare = [];

if ($config_bidding['elas_use']) {
    // kết nối tới ElasticSearh
    $nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($module_config['bidding']['elas_host'], $module_config['bidding']['elas_port'], 'dauthau_businesslistings', $module_config['bidding']['elas_user'], $module_config['bidding']['elas_pass']);
    $search_elastic = [];
    if ($ward > 0) {
        $search_elastic['must'][] = [
            'match' => [
                'ward' => [
                    'query' => $ward
                ]
            ]
        ];
    }
    if ($district > 0) {
        $search_elastic['must'][] = [
            'match' => [
                'district' => [
                    'query' => $district
                ]
            ]
        ];
    }
    if ($province > -1) {
        $search_elastic['must'][] = [
            'match' => [
                'province' => [
                    'query' => $province
                ]
            ]
        ];
    }

    $search_elastic['must'][] = [
        'match' => [
            'active' => '1'
        ]
    ];

    /*
     * $search_elastic['minimum_should_match'] = '1';
     * $search_elastic['boost'] = '1.0';
     */

    $array_query_elastic = array();
    if (!empty($search_elastic)) {
        $array_query_elastic['query']['bool'] = $search_elastic;
    }
    $array_query_elastic['track_total_hits'] = 'true';
    $array_query_elastic['size'] = $per_page;
    $array_query_elastic['from'] = ($page - 1) * $per_page;
    $array_query_elastic['sort'] = [
        [
            'id' => [
                'order' => 'desc'
            ]
        ]
    ];

    $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_info', $array_query_elastic);
    $numf = $response['hits']['total']['value'];
    foreach ($response['hits']['hits'] as $value) {
        if (!empty($value['_source'])) {
            $view = $value['_source'];
            if (in_array($view['id'], $list_compare)) {
                $view['is_added_cmp'] = 1;
            } else {
                $view['is_added_cmp'] = 0;
            }
            $view['checkremvcp'] = md5(NV_CHECK_SESSION . '_checkremvcp_' . $view['id']);
            $view['checkaddcp'] = md5(NV_CHECK_SESSION . '_checkaddcp_' . $view['id']);
            $arr_data[] = $view;
        }
    }
} else {
    if ($province > -1) {
        $where[] = 'province = ' . $province;
    }
    if (!empty($district)) {
        $where[] = 'district = ' . $district;
    }
    if (!empty($ward)) {
        $where[] = 'ward = ' . $ward;
    }
    $where[] = 'active = 1';

    if (!empty($where)) {
        $_where = implode(' AND ', $where);
    }

    $db->sqlreset()
        ->select('SQL_CALC_FOUND_ROWS *')
        ->from(BUSINESS_PREFIX_GLOBAL . '_info')
        ->limit($per_page)
        ->offset(($page - 1) * $per_page)
        ->where($_where)
        ->order('id DESC');
    $sth = $db->prepare($db->sql());
    $sth->execute();
    $arr_data = $sth->fetchAll();
    foreach ($arr_data as $k => $view) {
        if (in_array($view['id'], $list_compare)) {
            $view['is_added_cmp'] = 1;
        } else {
            $view['is_added_cmp'] = 0;
        }
        $view['checkremvcp'] = md5(NV_CHECK_SESSION . '_checkremvcp_' . $view['id']);
        $view['checkaddcp'] = md5(NV_CHECK_SESSION . '_checkaddcp_' . $view['id']);
        $arr_data[$k] = $view;
    }

    list ($numf) = $db->query("SELECT FOUND_ROWS()")->fetch(3);
}
$link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation";
$beackcolumn = "";
foreach ($arr_beackcolumn as $item) {
    $beackcolumn .= "<a href=\"" . $link . "/" . $item['key'] . "-" . $item['alias'] . "-" . $item['id'] . "\" alt=\"" . $nv_Lang->getModule('list_company_at') . " " . $item['title'] . " \">" . $item['title'] . "</a>, ";
}
$beackcolumn = rtrim(trim($beackcolumn), ',');

// province
$sql_province = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province";
$province_list = $nv_Cache->db($sql_province, 'id', 'location');

// district
$sql_district = "SELECT id, title FROM " . NV_PREFIXLANG . "_location_district";
$district_list = $nv_Cache->db($sql_district, 'id', 'location');

// Gọi file để lấy arr xã
require NV_ROOTDIR . '/modules/' . $module_file . '/get_ward/ward.php';
// Trả về kết quả id, tên xã
$ward_list = get_xa($arr_data, $db);

$base_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation/" . $array_op[1];
$page_url = $base_url;

if ($page > 1) {
    $page_url .= '/page-' . $page;
    $global_lang_url .= '/page-' . $page;
}

$all_page = ($numf) ? $numf : 1;
$num_items = $numf;
$pages_businesslistings = nv_alias_page('', $base_url, $all_page, $per_page, $page);

$canonicalUrl = getCanonicalUrl($page_url);
$urlappend = '/page-';
betweenURLs($page, ceil($num_items / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

$arr_content = array();
$link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=detail";
if (!empty($arr_data)) {
    foreach ($arr_data as $row) {
        $row['addressfull'] = [];
        if ($row['address'] != "") {
            $row['addressfull'][] = handle_address_ward($row['address'], $row['ward']);
        }
        if (isset($ward_list[$row['ward']])) {
            $row['addressfull'][] = $ward_list[$row['ward']];
        }
        if (isset($district_list[$row['district']])) {
            $row['addressfull'][] = $district_list[$row['district']]['title'];
        }
        $row['addressfull'] = implode(', ', $row['addressfull']);
        $row['addressfull'] = str_replace(',,', ',', $row['addressfull']);
        if (empty($row['addressfull'])) {
            $row['addressfull'] = 'N/A';
        }
        if (isset($province_list[$row['province']])) {
            $row['location'] = $province_list[$row['province']]['title'];
            $row['link_location'] = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=listlocation/T-" . $province_list[$row['province']]['alias'] . '-' . $province_list[$row['province']]['id'];
        } else {
            $row['location'] = 'N/A';
            $row['link_location'] = '';
        }
        if (!empty($row['website']) and !preg_match('/^http[s]*\:\/\//i', $row['website'])) {
            $row['website'] = 'http://' . $row['website'];
        }
        $row['website'] = ($row['website'] != "") ? "<a target='_blank' href='" . $row['website'] . "'>" . $row['website'] . "</a>" : "N/A";
        (NV_LANG_DATA != 'vi' && !empty(trim($row['officialname']))) && $row['companyname'] = $row['officialname'];
        $row['link'] = $link . "/" . change_alias($row['companyname']) . "-" . $row['id'];
        if ($row['logo'] != "") {
            $row['logo'] = NV_BASE_SITEURL . NV_UPLOADS_DIR . "/" . $module_upload . "/" . $row['logo'];
        } else {
            $row['logo'] = NV_BASE_SITEURL . "themes/" . $module_info['template'] . "/images/" . $module_file . "/noimages.png";
        }
        $row['chartercapital'] = ConvertPriceTextSort($row['chartercapital']);
        $row['businesstype_text'] = isset($global_array_businesstype[$row['businesstype']]) ? $global_array_businesstype[$row['businesstype']]['title'] : '';
        $row['businesstype_link'] = isset($global_array_businesstype[$row['businesstype']]) ? $global_array_businesstype[$row['businesstype']]['title'] : '';
        $arr_content[] = $row;
    }
}
// Kiểm tra xem tk có là VIP hay không
$arr_customs = [];
if (!empty($global_array_vip[89])) {
    define('NV_IS_VIP_X2', true);
    $arr_customs = $global_array_vip[89];
}

// xác định điểm và số dn trên mỗi block
$total_point_download = 0;
$config_bidding = $module_config['bidding'];
$num_point_block = $config_bidding['num_point_block_notx2'];
$num_business_block = $config_bidding['num_business_block_notx2'];
if (defined('NV_IS_VIP_X2')) {
    $num_point_block = $config_bidding['num_point_block_x2'];
    $num_business_block = $config_bidding['num_business_block_x2'];
}
$total_point_download = ceil($num_items/$num_business_block) * $num_point_block;

//check điểm
if ($nv_Request->isset_request('confirm_download', 'post')) {
    $res = 'error';
    $mess = $nv_Lang->getModule('unknown_error');

    if (defined('NV_IS_USER')) {
        // Số điểm hiện tại

        $your_point = Point::getMyPoint()['point_total'];

        if ($total_point_download > $your_point) {
            $res = 'error';
            $mess = sprintf($nv_Lang->getModule('error_point_out'), $your_point, $total_point_download , Url::muaDiem());
        } else {
            $res = 'success';
            $mess = sprintf($nv_Lang->getModule('confirm_download_business_data'), number_format($total_point_download, 0, ...$array_round_format), number_format($your_point, 0, ...$array_round_format), number_format($num_items, 0, ...$array_round_format));
        }
    } else {
        $res = 'error';
        $mess = $nv_Lang->getModule('login_require');
    }
    //trả về respone
    nv_jsonOutput([
        'res' => $res,
        'mess' => $mess
    ]);
}

// Lưu thông tin request
if ($nv_Request->isset_request('checksess_download', 'post')) {
    $checksess_download = $nv_Request->get_title('checksess_download', 'post', '');
    if (defined('NV_IS_USER')) {
        if ($checksess_download != md5(NV_CHECK_SESSION)) {
            $res = 'error';
            $mess = $nv_Lang->getModule('wrong_checksess');

            nv_jsonOutput([
                'res' => $res,
                'mess' => $mess,
                'time_rune' => ''
            ]);
        } else {
            try {

                // Tính điểm
                $db->beginTransaction();
                // trừ điểm
                $type_transaction = defined('NV_IS_VIP_X2') ? 18 : 17;

                $message = [
                    'vi' => sprintf(get_lang('vi', 'x2_download_point_mess'), (defined('NV_IS_VIP_X2') ? get_lang('vi', 'title_with_x2') : ''), $num_items, $total_point_download),
                    'en' => sprintf(get_lang('en', 'x2_download_point_mess'), (defined('NV_IS_VIP_X2') ? get_lang('en', 'title_with_x2') : ''), $num_items, $total_point_download)
                ];
                $check = Point::subtractPoint($total_point_download, $user_info['userid'], json_encode($message), 0, 0, 0, 0, $type_transaction);

                if ($check['status'] == 'SUCCESS') {
                    // Lưu thông tin request
                    // k có lỗi thì ghi dữ liệu vào bảng yêu cầu xuất
                    // lưu csdl các request
                    $array_param = [
                        'keyword' => '',
                        'industry1' => '',
                        'industry2' => '',
                        'industry3' => '',
                        'industry4' => '',
                        'province' => 0,
                        'district' => 0,
                        'ward' => 0,
                        'userid' => $user_info['userid'],
                        'businesstype' => 0,
                        'lvkd' => 0,
                        'fee' => 0,
                        'sfrom_business' => 0,
                        'sto_business' => 0,
                    ];

                    $priority = $db->query('SELECT count(*) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_request WHERE userid=' . $user_info['userid'] . ' AND addtime >= ' . mktime(0, 0, 0, date('m', NV_CURRENTTIME), date('d', NV_CURRENTTIME), date('Y', NV_CURRENTTIME)))->fetchColumn();

                    $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_request (userid, email, request_params, addtime, send_mail, priority, num_row) VALUES (:userid, :email, :request_params, :addtime, :send_mail, :priority, :num_row)');

                    $stmt->bindParam(':userid', $user_info['userid'], PDO::PARAM_INT);
                    $stmt->bindParam(':email', $user_info['email'], PDO::PARAM_STR);
                    $stmt->bindValue(':request_params', json_encode($array_param, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), PDO::PARAM_STR);
                    $stmt->bindValue(':addtime', NV_CURRENTTIME, PDO::PARAM_INT);
                    $stmt->bindValue(':send_mail', 1, PDO::PARAM_INT);
                    $stmt->bindValue(':priority', $priority, PDO::PARAM_INT);
                    $stmt->bindValue(':num_row', $num_items, PDO::PARAM_INT);

                    $exc = $stmt->execute();
                    if ($exc) {
                        $id = $db->lastInsertId();
                        $nv_Cache->delMod($module_name);
                        nv_insert_logs(NV_LANG_DATA, $module_name, 'Add request', 'ID: ' . $id, $user_info['userid']);
                        // nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
                        $mess = $nv_Lang->getModule('request_add_ok');
                        $res = 'success';
                    }
                    $db->commit();

                } else {
                    $mess = sprintf($nv_Lang->getModule('error_point_out'), Url::muaDiem());
                    $res = 'error';
                }
                $time_run = round((microtime(true) - NV_START_TIME), 4);
            } catch (PDOException $e) {
                $db->rollBack();
                $mess = 'Error: Something Wrong!';
                $res = 'error';
                trigger_error(print_r($e, true));
            }
        }
    } else {
        $res = 'error';
        $mess = $nv_Lang->getModule('login_require');
    }
    //trả về respone
    nv_jsonOutput([
        'res' => $res,
        'mess' => $mess,
        'time_run' => $time_run
    ]);
}
$contents = nv_theme_businesslistings_listbusinesslistings($arr_content, $nv_Lang->getModule('list_company_at') . " " . $beackcolumn, $pages_businesslistings, $all_page, $num_items, $total_point_download);
$page_suffix = ($page > 1) ? (NV_TITLEBAR_DEFIS . $nv_Lang->getModule('page_num', $page)) : '';
$description = $nv_Lang->getModule('des_funs_listlocation', str_replace('&raquo;', '', strip_tags($beackcolumn))) . $page_suffix;
$page_title = $nv_Lang->getModule('title_funs_listlocation', str_replace('&raquo;', '', strip_tags($beackcolumn))) . $page_suffix;

include (NV_ROOTDIR . "/includes/header.php");
echo nv_site_theme($contents);
include (NV_ROOTDIR . "/includes/footer.php");
