<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

 if (!defined('NV_IS_MOD_CERTIFICATE')) {
    exit('Stop!!!');
}

use NukeViet\Point\Point;
use NukeViet\Dauthau\Share;
use NukeViet\Dauthau\Url;
$arr_title_province = [
    815,//Tp cần thơ
    501,//đà nẵng
    101,//hà nội
    103,//hải phòng
    701//HCM
];

$array_data = $data_courses = $array_province = $generate_page = array();
$alias_url = isset($array_op[1]) ? $array_op[1] : '';
$is_advance = 1;
$permision = 0;
// Chỉ thành viên hoặc Bot mới vào đây đượ<PERSON>
// if (!(defined('NV_IS_USER') or $client_info['is_bot']) and $module_config['bidding']['user_view_detail']) {
//     $permision = 1;
// }
// Cứ có gói VIP là xem được thông tin hàng hoá free
if (defined('NV_IS_USER')) {
    if (sizeof($global_array_vip) > 0) {
        $check_vip = 1;
    } else {
        if (!empty($arr_customs_permission)) {
            $check_vip = 1;
        } else {
            $vip1_hethan = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id = ' . $user_info['userid'] . ' AND vip = 1 AND status != 1')
                ->fetch();
            if (!empty($vip1_hethan)) {
                $check_vip = 2; // hết hạn. gói vip 1
            } else {
                $check_vip = 0;
            }
        }
    }
} else {
    $check_vip = -1;
}
$num_items = 0;
if (!(defined('NV_IS_USER'))) {
    $permision = 1;
}
if ($alias_url != '') {
    if (isset($array_op[2])) {
        
        if (preg_match('/^([A-Za-z0-9\-]+)$/',$array_op[2], $m)) {
            if ($m[0] == 'Chua-phan-loai') {
                $_temp = $_idprovince[] = 0;
            } else if ($m[0] == change_alias($nv_Lang->getModule('alias_vn_out_territory'))) {
                $_temp = $_idprovince[] = 825;
            } else if ($m[0] == change_alias($nv_Lang->getModule('alias_nationwide'))) {
                $_temp = $_idprovince[] = 824;
            } else {
                if (empty($_idprovince)) {
                    $_temp = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_location_province WHERE alias = ' . $db->quote($m[0]))->fetchColumn();
                    if (!empty($_temp)) {
                        $_idprovince[] = $_temp;
                    } else {
                        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_name . '/' . $nv_Lang->getModule('province_link') . '/' . $province_list[$_temp]['alias']);
                    }
                } else {
                    $_temp = $_idprovince[0];
                }
            }
        }
        $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'] . '/' . $nv_Lang->getModule('province_link') . '/' . $province_list[$_temp]['alias'];
        $global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'] . '/' . $nv_Lang->getModule('province_link') . '/' . $province_list[$_temp]['alias'];
        
        if (in_array($_temp,$arr_title_province)) {
            $pro = $nv_Lang->getModule('idprovince_i') . '';
        } else {
            $pro = $nv_Lang->getModule('idprovince_ii') . '';
        }
        $result = $nv_Request->get_int('result', 'get', 0);
        $idprovince = $_temp;
        $province_list[$_temp]['title'] = str_replace('TP.','', $province_list[$_temp]['title']);
        $page_title = $nv_Lang->getModule('students_title') . ' ' . $pro . $province_list[$_temp]['title'] ;
        $title_student = $nv_Lang->getModule('title_student') . ' ' . $pro . $province_list[$_temp]['title'];
        $array_mod_title[] = [
            'title' => $nv_Lang->getModule('students_title'),
            'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'], true)
        ];
        $array_mod_title[] = [
            'title' => $pro . $province_list[$_temp]['title'],
            'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'] . '/' . $nv_Lang->getModule('province_link') . '/' . $province_list[$_temp]['alias']
        ];
        $key_words = $nv_Lang->getModule('students_words') . ' ' . $pro . $province_list[$_temp]['title'];
        $description = $nv_Lang->getModule('students_description') . ' ' . $pro . $province_list[$_temp]['title'];
        // Lấy danh sách học viên mà khách hàng đã đổi điểm
        $array_points_certificate = [];
        if (defined('NV_IS_USER')) {
            $array_points_certificate = array_column($db->query("SELECT id_students FROM `" . $db_config['prefix'] . "_logs_buy_certificate` WHERE userid = " . $user_info['userid'])
                ->fetchAll(), 'id_students');
        }
        if (!empty($array_points_certificate)) {
            //Nếu khách hàng có đổi điểm thì lấy file cache riêng của họ
            $cache_file = 'certificate_' . NV_LANG_DATA . '_' . $user_info['userid'] . '.cache';
        } else {
            //Nếu khách hàng không đổi điểm thì lấy file cache chung của hệ thống
            $cache_file = 'certificate_' . NV_LANG_DATA . '.cache';
        }
        
        $result_list = [
            '1' => [
                'title' => $nv_Lang->getModule('average'), 
            ],
            '2' => [
                'title' => $nv_Lang->getModule('good'),
            ],
            '3' => [
                'title' => $nv_Lang->getModule('excellent'),
            ],
            '4' => [
                'title' => $nv_Lang->getModule('outstanding'),
            ],
        ];
        $result_title = isset($result_list[$result]) ? $result_list[$result]['title'] : '';
        $array_data = $array_key = [];
        $error = [];
        $list_courses = $array_courses = [];
        $where_courses = [];
        if ($nv_Request->isset_request('courses_id', 'get')) {
            $courses_id = $nv_Request->get_int('courses_id', 'get');
            $where_courses[] =  'id = ' . $courses_id;
        }
        $sql = "SELECT id, area, start_date, end_date, role, teacher, certificate_singer, number_student, certificate_date  FROM " . $db_config['prefix'] . "_certificate_courses";
        if (!empty($where_courses)) {
            $sql .= ' WHERE ' . implode(' AND ', $where_courses);
        }
        
        $row_courses = $db->query($sql);
        while ($_row = $row_courses->fetch()) {
            $list_courses[$_row['id']] = $_row;
            if ($nv_Request->isset_request('courses_id', 'get')) {
                $array_courses[] = $_row;
            }
        }
        $per_page = 20;
        $page = $nv_Request->get_page('page', 'post,get', 1);
        
        if ($page < 1) {
            $nv_BotManager->setPrivate();
            $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
            $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_wrong_page') . $btn, 'danger');
        
            include NV_ROOTDIR . '/includes/header.php';
            echo nv_site_theme($contents);
            include NV_ROOTDIR . '/includes/footer.php';
        }
        /**
         * thao: cho sửa dùm anh nếu page > 100 báo lỗi:
         * Vui lòng thay đổi tiêu chí tìm kiếm để có thông tin bạn cần
         */
        if ($page > 100) {
            $nv_BotManager->setPrivate();
            $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
            $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_max_searchpage') . $btn);
        
            include NV_ROOTDIR . '/includes/header.php';
            echo nv_site_theme($contents);
            include NV_ROOTDIR . '/includes/footer.php';
        }
        if (defined('NV_IS_USER') and $nv_Request->isset_request('view_price', 'post')) {
            $customs_points = Point::getMyPoint();
            $id = $nv_Request->get_int('id', 'post', 0);
            $data_certificate = [];
            $data_certificate['data'] = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_certificate_students WHERE status = 1 AND id = ' . $id)->fetch();
            if (!empty($data_certificate['data'])) {
                if (is_numeric($data_certificate['data']['birthday'])) {
                    $data_certificate['data']['birthday'] = !empty($data_certificate['data']['birthday']) ? date('d/m/Y', $data_certificate['data']['birthday']) : '';
                }
                if (is_numeric($data_certificate['data']['issuedate'])) {
                    $data_certificate['data']['issuedate'] = !empty($data_certificate['data']['issuedate']) ? date('d/m/Y', $data_certificate['data']['issuedate']) : '';
                }
                $bidding_config = $module_config['bidding'];
                $sodiem = $bidding_config['points_items_price'];
                $data_certificate['khongdudiem'] = 0;
                // Kiểm tra xem người dùng có đủ điểm để xem giá hàng hóa hay không, nếu không thì exit()
                if ($customs_points['point_total'] < $sodiem) {
                    $data_certificate['khongdudiem'] = 1;
                    echo json_encode($data_certificate);
                    exit();
                }
                // Trừ điểm của khách hàng
                $message = [
                    'vi' => sprintf(get_lang('vi', 'notifi_minutue_price'), $sodiem) . (' --- ' . get_lang('vi', 'student_name') . ': ' . $data_certificate['data']['student_name']),
                    'en' => sprintf(get_lang('en', 'notifi_minutue_price'), $sodiem) . (' --- ' . get_lang('en', 'student_name') . ': ' . $data_certificate['data']['student_name']),
                ];

                // Trừ điêmr khi xem giá hàng hóa
                // dungpt:point: trước không thấy check kết quả
                $subtract_point = Point::subtractPoint($sodiem, $user_info['userid'], json_encode($message), 0, 0, 0, 0, 7);
                if ($subtract_point['status'] == 'SUCCESS') {
                    // Lưu vào bảng nv4_logs_buy_certificate để lần sau không cần đổi điểm vẫn xem được giá
                    $db->query("INSERT IGNORE INTO " . $db_config['prefix'] . "_logs_buy_certificate (`id_points_log`, `userid`, `id_students`, `created_time`) VALUES (" . $subtract_point['log_id'] . "," . $user_info['userid'] . "," . $data_certificate['data']['id'] . "," . NV_CURRENTTIME . ")");
                    //Xoá cache đã lưu của user này
                    unlink(NV_ROOTDIR . '/data/cache/' . $module_name . '/' . $cache_file);
                }
                echo json_encode($data_certificate);
                exit();
            } else {
                $data_certificate['khongdudiem'] = 2;
                echo json_encode($data_certificate);
                exit();
            }
        }
        $db->sqlreset()
            ->select('COUNT(id)')
            ->from($db_config['prefix'] . '_certificate_students');
        
        $where = [];
        $where[] =  'status = 1';
        if ($idprovince > -1) {
            $where[] = "id_province = " . $idprovince ;
        }
        
        if (!empty($where)) {
            $db->where(implode(' AND ', $where));
        }
        
        $sth = $db->prepare($db->sql());
        $sth->execute();
        $num_courses = $sth->fetchColumn();
        
        $db->select('*');
        $orderBy = '';
        if (!empty($array_points_certificate)) {
            $orderBy .= "FIELD (id, " . implode(',', array_map([$db, 'quote'], $array_points_certificate)) . ") DESC, ";
        }
        $orderBy .= "result DESC"; 
        $db->order($orderBy)
            ->limit($per_page)
            ->offset(($page - 1) * $per_page);
        $sth = $db->prepare($db->sql());
        $sth->execute(); //echo $db->sql();die;
        
        while ($view = $sth->fetch()) {
            $alias = change_alias($view['student_name']);
            $view['link_student'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'] . '/' . $alias . '-' . $view['id']);
            $view['place'] = $province_list[$view['id_province']]['title'];
            $view['link_place'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'] . '/' . $nv_Lang->getModule('province_link') . '/' . $province_list[$view['id_province']]['alias']);
            $view['stt'] = count($array_data) + 1 + (($page - 1) * $per_page);
            $array_data[] = $view;
        }
        
        $generate_page = nv_generate_page($base_url, $num_courses, $per_page, $page);
        // lấy page_url
        $page_url = $base_url;
        if ($page > 1) {
            $page_url .= '&amp;page=' . $page;
            $page_title = $page_title . ' - ' . $nv_Lang->getGlobal('page') . ' ' . $page;
            $description = $description . ' - ' . $nv_Lang->getGlobal('page') . ' ' . $page;
            $title_student  = $title_student . ' - ' . $nv_Lang->getGlobal('page') . ' ' . $page;
        }
        $urlappend = '&amp;page=';
        $canonicalUrl = getCanonicalUrl($page_url);
        // Kiểm tra đánh số trang
        betweenURLs($page, ceil($num_courses / $per_page), $base_url, $urlappend, $prevPage, $nextPage);
        
        $contents = nv_theme_bidding_students($array_data, $array_courses, $generate_page, $permision, $check_vip, $array_points_certificate, $title_student, $error);        
        
    } else {
        $array_page = explode('-', $array_op[1]);
        $id = intval(end($array_page));
        // Lấy danh sách học viên mà khách hàng đã đổi điểm
        $array_points_certificate = [];
        if (defined('NV_IS_USER')) {
            $array_points_certificate = array_column($db->query("SELECT id_students FROM `" . $db_config['prefix'] . "_logs_buy_certificate` WHERE userid = " . $user_info['userid'])
                ->fetchAll(), 'id_students');
        }
        if (!empty($array_points_certificate)) {
            //Nếu khách hàng có đổi điểm thì lấy file cache riêng của họ
            $cache_file = 'certificate_' . NV_LANG_DATA . '_' . $user_info['userid'] . '.cache';
        } else {
            //Nếu khách hàng không đổi điểm thì lấy file cache chung của hệ thống
            $cache_file = 'certificate_' . NV_LANG_DATA . '.cache';
        }
        if (defined('NV_IS_USER') and $nv_Request->isset_request('view_price', 'post')) {
            $customs_points = Point::getMyPoint();
            $id = $nv_Request->get_int('id', 'post', 0);
            $data_certificate = [];
            $data_certificate['data'] = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_certificate_students WHERE status = 1 AND id = ' . $id)->fetch();
            if (!empty($data_certificate['data'])) {
                if (is_numeric($data_certificate['data']['birthday'])) {
                    $data_certificate['data']['birthday'] = !empty($data_certificate['data']['birthday']) ? date('d/m/Y', $data_certificate['data']['birthday']) : '';
                }
                if (is_numeric($data_certificate['data']['issuedate'])) {
                    $data_certificate['data']['issuedate'] = !empty($data_certificate['data']['issuedate']) ? date('d/m/Y', $data_certificate['data']['issuedate']) : '';
                }
                $bidding_config = $module_config['bidding'];
                $sodiem = $bidding_config['points_items_price'];
                $data_certificate['khongdudiem'] = 0;
                // Kiểm tra xem người dùng có đủ điểm để xem giá hàng hóa hay không, nếu không thì exit()
                if ($customs_points['point_total'] < $sodiem) {
                    $data_certificate['khongdudiem'] = 1;
                    echo json_encode($data_certificate);
                    exit();
                }
                // Trừ điểm của khách hàng
                $message = [
                    'vi' => sprintf(get_lang('vi', 'notifi_minutue_price'), $sodiem) . (' --- ' . get_lang('vi', 'student_name') . ': ' . $data_certificate['data']['student_name']),
                    'en' => sprintf(get_lang('en', 'notifi_minutue_price'), $sodiem) . (' --- ' . get_lang('en', 'student_name') . ': ' . $data_certificate['data']['student_name']),
                ];
                // Trừ điêmr khi xem giá hàng hóa
                // dungpt:point: trước không thấy check kết quả
                $subtract_point = Point::subtractPoint($sodiem, $user_info['userid'], json_encode($message), 0, 0, 0, 0, 7);
                if ($subtract_point['status'] == 'SUCCESS') {
                    // Lưu vào bảng nv4_logs_buy_certificate để lần sau không cần đổi điểm vẫn xem được giá
                    $db->query("INSERT IGNORE INTO " . $db_config['prefix'] . "_logs_buy_certificate (`id_points_log`, `userid`, `id_students`, `created_time`) VALUES (" . $subtract_point['log_id'] . "," . $user_info['userid'] . "," . $data_certificate['data']['id'] . "," . NV_CURRENTTIME . ")");
                    //Xoá cache đã lưu của user này
                    unlink(NV_ROOTDIR . '/data/cache/' . $module_name . '/' . $cache_file);
                }
                echo json_encode($data_certificate);
                exit();
            } else {
                $data_certificate['khongdudiem'] = 2;
                echo json_encode($data_certificate);
                exit();
            }
        }
        $data = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_certificate_students WHERE status = 1 AND id =' . $id)->fetch();
        if (!empty($data)) {
            $array_data = $data;
            if (!empty($data['courses_id'])) {
                $data_courses = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_certificate_courses WHERE id IN (' . $data['courses_id'] . ')')->fetch();
            }
            $data_training = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_certificate_training WHERE id =' . $data_courses['id_training'])->fetch();
            $data_courses['name_tranning'] = $data_training['name'];
            $alias_training = change_alias($data_training['name']);
            $data_courses['link_training'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $alias_training . '-' . $data_training['id']);
            $alias_courses = change_alias($data_courses['name_courses']);
            $data_courses['link_courses'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['courses'] . '/' . $alias_courses . '-' . $data_courses['id']);
        
        
        } else {
            nv_redirect_location(nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list']));
            die();
        }
        $array_mod_title[] = [
            'title' => $nv_Lang->getModule('students_title'),
            'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op, true)
        ];

        $array_mod_title[] = [
            'title' => $array_data['student_name'],
            'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . change_alias($data['student_name'] . '-' . $data['id']), true)
        ];
        $page_title = $nv_Lang->getModule('students_detail_title', $array_data['student_name']);
        $description = $nv_Lang->getModule('students_detail_description', $array_data['student_name']);
        $key_words = $array_data['student_name'];
        $array_data['place'] = $province_list[$array_data['id_province']]['title'];
        $array_data['link_place'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'] . '/' . $nv_Lang->getModule('province_link') . '/' . $province_list[$array_data['id_province']]['alias'];
        $page_url = $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'] . '/' . change_alias($array_data['student_name']) . '-' . $array_data['id'];
        $global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $other_lang_module_info['alias']['student-list'] . '/' . change_alias($array_data['student_name']) . '-' . $array_data['id'];
        $canonicalUrl = getCanonicalUrl($page_url);
        $base_url_rewrite = nv_url_rewrite($base_url, true);
        $contents = nv_theme_bidding_students_detail($array_data, $data_courses, $permision, $check_vip, $array_points_certificate);

    }
} else {
    $array_mod_title[] = [
        'title' => $nv_Lang->getModule('students'),
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op, true)
    ];

    $result_list = [
        1 => ['title' => $nv_Lang->getModule('average')],
        2 => ['title' => $nv_Lang->getModule('good')],
        3 => ['title' => $nv_Lang->getModule('excellent')],
        4 => ['title' => $nv_Lang->getModule('outstanding')],
    ];
    $error = [];
    $now = NV_CURRENTTIME;
    $default_from = nv_date('d/m/Y', $now - (13 * 86400));
    $default_to = nv_date('d/m/Y', $now);
    $per_page = 20;
    $page = $nv_Request->get_page('page', 'post,get', 1);
    $is_advance = $nv_Request->get_int('is_advance', 'get', 0);
    $array_search = [
        'q' => $nv_Request->get_title('q', 'post,get', ''),
        'certificate_code' => $nv_Request->get_title('certificate_code', 'post,get', ''),
        'identify_code' => $nv_Request->get_title('identify_code', 'post,get', ''),
        'idprovince' => $nv_Request->get_array('idprovince', 'get,post', []),
        'result' => $nv_Request->get_int('result', 'get', 0),
        'courses_id' => $nv_Request->get_int('courses_id', 'post,get', 0),
        'sfrom_birthday' => nv_substr($nv_Request->get_title('sfrom_birthday', 'get', ''), 0, 10),
        'sfrom_issuedate' => nv_substr($nv_Request->get_title('sfrom_issuedate', 'get', $default_from), 0, 10),
        'sto_issuedate' => nv_substr($nv_Request->get_title('sto_issuedate', 'get', $default_to), 0, 10)
    ];

    $array_search['birthday_from'] = strtotime(str_replace('/', '-', $array_search['sfrom_birthday']));
    $array_search['issuedate_from'] = strtotime(str_replace('/', '-', $array_search['sfrom_issuedate']) . ' 00:00:00');
    $array_search['issuedate_to'] = strtotime(str_replace('/', '-', $array_search['sto_issuedate']) . ' 23:59:59');
    if ($array_search['issuedate_from'] === false || $array_search['issuedate_to'] === false) {
        $error[] = $nv_Lang->getModule('invalid_date_format');
    }
    if (!empty($array_search['sfrom_birthday']) && $array_search['birthday_from'] === false) {
        $error[] = $nv_Lang->getModule('birthday_date_format');
    }
    // Xử lý tỉnh thành
    $array_search['idprovince'] = array_filter($array_search['idprovince'], function ($val) {
        return $val !== '' && $val !== '-1' && is_numeric($val);
    });

    // Điểm đã xem trước
    $array_points_certificate = [];
    if (defined('NV_IS_USER')) {
        $array_points_certificate = array_column($db->query("SELECT id_students FROM `" . $db_config['prefix'] . "_logs_buy_certificate` WHERE userid = " . $user_info['userid'])->fetchAll(), 'id_students');
    }
    $cache_file = 'certificate_' . NV_LANG_DATA . (defined('NV_IS_USER') ? '_' . $user_info['userid'] : '') . '.cache';

    // Danh sách khóa học
    $list_courses = $array_courses = [];
    $where_courses = [];

    if ($array_search['courses_id'] > 0) {
        $where_courses[] = 'id = ' . $array_search['courses_id'];
    }
    $sql = "SELECT id, area, start_date, end_date, role, teacher, certificate_singer, number_student, certificate_date FROM " . $db_config['prefix'] . "_certificate_courses";

    if (!empty($where_courses)) {
        $sql .= ' WHERE ' . implode(' AND ', $where_courses);
    }

    $result_title = $result_list[$array_search['result']]['title'] ?? '';
    $row_courses = $db->query($sql);

    while ($_row = $row_courses->fetch()) {
        $list_courses[$_row['id']] = $_row;
        if ($array_search['courses_id'] > 0) {
            $array_courses[] = $_row;
        }
    }
    // Xem giá học viên khi có yêu cầu từ user
    if (defined('NV_IS_USER') && $nv_Request->isset_request('view_price', 'post')) {
        $response = ['khongdudiem' => 0];
        $id = $nv_Request->get_int('id', 'post', 0);

        // Lấy dữ liệu học viên
        $student = $db->query("SELECT * FROM " . $db_config['prefix'] . "_certificate_students WHERE status = 1 AND id = " . $id)->fetch();

        if (!empty($student)) {
            // Format ngày sinh và ngày cấp
            if (is_numeric($student['birthday'])) {
                $student['birthday'] = !empty($student['birthday']) ? date('d/m/Y', $student['birthday']) : '';
            }
            if (is_numeric($student['issuedate'])) {
                $student['issuedate'] = !empty($student['issuedate']) ? date('d/m/Y', $student['issuedate']) : '';
            }

            $response['data'] = $student;

            // Lấy điểm người dùng hiện tại
            $user_points = Point::getMyPoint();
            $cost_point = $module_config['bidding']['points_items_price'];

            // Không đủ điểm
            if ($user_points['point_total'] < $cost_point) {
                $response['khongdudiem'] = 1;
                echo json_encode($response);
                exit();
            }

            // Trừ điểm và lưu log
            $message = [
                'vi' => sprintf(get_lang('vi', 'notifi_minutue_price'), $sodiem) . (' --- ' . get_lang('vi', 'student_name') . ': ' . $student['student_name']),
                'en' => sprintf(get_lang('en', 'notifi_minutue_price'), $sodiem) . (' --- ' . get_lang('en', 'student_name') . ': ' . $student['student_name']),
            ];

            $point_log = Point::subtractPoint($cost_point, $user_info['userid'], json_encode($message), 0, 0, 0, 0, 7);

            if ($point_log['status'] == 'SUCCESS') {
                // Ghi nhận đã xem học viên này
                $db->query("INSERT IGNORE INTO " . $db_config['prefix'] . "_logs_buy_certificate 
                    (`id_points_log`, `userid`, `id_students`, `created_time`) 
                    VALUES (
                        " . $point_log['log_id'] . ",
                        " . $user_info['userid'] . ",
                        " . $student['id'] . ",
                        " . NV_CURRENTTIME . ")");

                // Xoá cache học viên của user
                $cache_file_path = NV_ROOTDIR . '/data/cache/' . $module_name . '/' . $cache_file;
                if (file_exists($cache_file_path)) {
                    @unlink($cache_file_path);
                }
            }

            echo json_encode($response);
            exit();
        } else {
            // Không tìm thấy học viên
            $response['khongdudiem'] = 2;
            echo json_encode($response);
            exit();
        }
    }

    if ($page < 1 || $page > 100) {
        $nv_BotManager->setPrivate();
        $msg = $page < 1 ? $nv_Lang->getModule('note_wrong_page') : $nv_Lang->getModule('note_max_searchpage');
        showDateErrorAndExit($msg);
    }

    $db->sqlreset()
        ->select('COUNT(id)')
        ->from($db_config['prefix'] . '_certificate_students');

    $where[] = 'status = 1';

    if (!empty($array_search['q'])) {
        $where[] = "student_name LIKE " . $db->quote('%' . $array_search['q'] . '%');
    }
    if (!empty($array_search['courses_id'])) {
        $where[] = 'courses_id = ' . $array_search['courses_id'];
    }

    if ($is_advance > 0) {
        if (!empty($array_search['certificate_code'])) {
            $where[] = "certificate_code LIKE " . $db->quote('%' . $array_search['certificate_code'] . '%');
        }
        if (!empty($array_search['identify_code'])) {
            $where[] = "identify_code = " . $db->quote($array_search['identify_code']);
        }
        if (!empty($array_search['idprovince'])) {
            $province_clause = [];
            foreach ($array_search['idprovince'] as $val) {
                $province_clause[] = "FIND_IN_SET(" . $db->quote($val) . ", id_province)";
            }
            $where[] = '(' . implode(' OR ', $province_clause) . ')';
        }
        if (!empty($array_search['birthday_from'])) {
            $where[] = 'birthday = ' . $array_search['birthday_from'];
        }
        if (!empty($array_search['issuedate_from']) && !empty($array_search['issuedate_to'])) {
            $where[] = 'issuedate BETWEEN ' . $array_search['issuedate_from'] . ' AND ' . $array_search['issuedate_to'];
        }
    }
    if (!empty($result_title)) {
        $where[] = "result LIKE " . $db->quote('%' . $result_title . '%');
    }
    if (!empty($where)) {
        $db->where(implode(' AND ', $where));
    }
    $sth = $db->prepare($db->sql());
    $sth->execute();
    $num_courses = $sth->fetchColumn();

    // Truy vấn danh sách học viên
    $db->select('*');
    $orderBy = '';
    if (!empty($array_points_certificate)) {
        $orderBy .= "FIELD(id, " . implode(',', array_map([$db, 'quote'], $array_points_certificate)) . ") DESC, ";
    }
    $orderBy .= "result DESC";
    $db->order($orderBy)
        ->limit($per_page)
        ->offset(($page - 1) * $per_page);

    $sth = $db->prepare($db->sql());
    $sth->execute();

    $array_data = [];
    while ($view = $sth->fetch()) {
        $alias = change_alias($view['student_name']);
        $view['link_student'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'] . '/' . $alias . '-' . $view['id']);
        $view['place'] = $province_list[$view['id_province']]['title'] ?? '';
        $view['link_place'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['student-list'] . '/' . $nv_Lang->getModule('province_link') . '/' . $province_list[$view['id_province']]['alias']);
        $view['stt'] = count($array_data) + 1 + (($page - 1) * $per_page);
        $array_data[] = $view;
    }

    $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
    $global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $other_lang_module_info['alias']['courses'];
    $advanced_fields = ['certificate_code', 'identify_code', 'idprovince', 'sfrom_birthday', 'sfrom_issuedate', 'sto_issuedate', 'issuedate_from', 'birthday_from', 'issuedate_to'];
    foreach ($array_search as $key => $val) {
        if (in_array($key, $advanced_fields) && $is_advance != 1) {
            continue;
        }
        if (!empty($val) && !is_array($val)) {
            $base_url .= '&' . $key . '=' . urlencode($val);
            $global_lang_url .= '&' . $key . '=' . urlencode($val);
        }
        if (is_array($val)) {
            foreach ($val as $v) {
                $base_url .= '&' . $key . '[]=' . urlencode($v);
                $global_lang_url .= '&' . $key . '[]=' . urlencode($v);
            }
        }
    }
    if ($is_advance == 1) {
        $base_url .= '&is_advance=1';
        $global_lang_url .= '&is_advance=1';
    }

    $generate_page = nv_generate_page($base_url, $num_courses, $per_page, $page);
    $page_url = $base_url;

    $page_title = $nv_Lang->getModule('students_title');
    $description = $nv_Lang->getModule('students_description');
    $key_words = $nv_Lang->getModule('students_words');
    $title_student = $nv_Lang->getModule('title_student');

    if ($page > 1) {
        $page_title .= ' - ' . $nv_Lang->getGlobal('page') . ' ' . $page;
        $title_student .= ' - ' . $nv_Lang->getGlobal('page') . ' ' . $page;
        $description .= ' - ' . $nv_Lang->getGlobal('page') . ' ' . $page;
        $page_url .= '&amp;page=' . $page;
        $nv_BotManager->setFollow()->setNoIndex();
    }

    // Xử lý canonical
    $canonicalUrl = getCanonicalUrl($page_url);
    $urlappend = '&amp;page=';
    betweenURLs($page, ceil($num_courses / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

    // Noindex nếu thiếu dữ liệu
    if (!empty($array_search['q']) || empty($array_courses) || empty($array_data)) {
        $nv_BotManager->setFollow()->setNoIndex();
    }

    // Hiển thị
    $contents = nv_theme_bidding_students($array_data, $array_courses, $generate_page, $permision, $check_vip, $array_points_certificate, $title_student, $error);
}

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
