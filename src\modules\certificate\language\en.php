<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

$lang_translator['author'] = 'VINADES.,JSC <<EMAIL>>';
$lang_translator['createdate'] = '04/03/2010, 15:22';
$lang_translator['copyright'] = '@Copyright (C) 2009-2021 VINADES.,JSC. All rights reserved';
$lang_translator['info'] = '';
$lang_translator['langtype'] = 'lang_module';
$lang_module['search'] = 'Search for articles';
$lang_module['search_keywords'] = 'Search keywords';
$lang_module['search_from'] = 'From date';
$lang_module['search_to'] = 'To date';
$lang_module['search_please'] = 'Please enter at least one search condition';
$lang_module['search_empty'] = 'No results match the search criteria';
$lang_module['search_result_count'] = 'Total results found';
$lang_module['training'] = 'Full list of bidding training facilities';
$lang_module['training_title'] = 'Full list of bidding training facilities';
$lang_module['training_title_h2'] = 'List of bidding training facilities';
$lang_module['training_words'] = 'Full list of bidding training facilities';
$lang_module['training_description'] = 'A list of procurement training centers, offering basic procurement certification and information about the training centers, instructors, and individuals who have completed the courses.';
$lang_module['training_detail_description'] = 'Comprehensive details about the bidding training institution %s have been carefully compiled by DauThau.info, including essential information for participants in bidding.';
$lang_module['courses'] = 'List of procurement courses';
$lang_module['courses_title'] = 'List of procurement courses';
$lang_module['courses_words'] = 'List of procurement courses';
$lang_module['courses_description'] = 'A list of bidding and online bidding courses along with detailed information about the training institutions and instructors, fully and comprehensively listed by DauThau.info.';
$lang_module['courses_detail_description'] = 'A comprehensive summary of detailed information about %s and the list of students enrolled in that course.';
$lang_module['students'] = 'Individuals certified with basic procurement certification';
$lang_module['students_title'] = 'Individuals certified with basic procurement certification';
$lang_module['students_words'] = 'Individuals certified with basic procurement certification';
$lang_module['students_description'] = 'Individuals certified with basic procurement certification';
$lang_module['students_detail_title'] = 'Student Details: %s';
$lang_module['students_detail_description'] = 'Complete details about student %s, including certificate number, classification, place of origin, and relevant course information from the training institution and instructor.';
$lang_module['type_course'] = 'Course classification';
$lang_module['type_course_LCNT'] = 'Basic training on LCNT';
$lang_module['type_course_LCNDT'] = 'Basic training on LCNDT';
$lang_module['type_course_KHAC'] = 'Other';
$lang_module['certificate_singer'] = 'Certificate signer';
$lang_module['teacher'] = 'Instructor';
$lang_module['certificate_date'] = 'Certificate issuance date';
$lang_module['time_traning'] = 'Training duration';
$lang_module['type_course_s'] = 'Basic training on %s';
$lang_module['certificate_course'] = 'Training center';
$lang_module['area'] = 'Region';
$lang_module['role'] = 'Role';
$lang_module['number_student'] = 'Number of students';
$lang_module['time_info'] = 'Notification time';
$lang_module['today'] = 'Today';
$lang_module['yesterday'] = 'Yesterday';
$lang_module['last_1_day'] = 'Since yesterday';
$lang_module['last_7_days'] = 'Last 7 days';
$lang_module['last_14_days'] = 'Last 14 days';
$lang_module['last_30_days'] = 'Last 30 days';
$lang_module['last_all_days'] = 'All time history';
$lang_module['this_month'] = 'This month';
$lang_module['last_month'] = 'Last month';
$lang_module['last_3_months'] = 'Last 3 months';
$lang_module['last_6_months'] = 'Last 6 months';
$lang_module['last_maxspan_months'] = '%d recent month';
$lang_module['last_maxspan_years'] = '%d recent year';
$lang_module['idregion'] = 'Region';
$lang_module['north'] = 'North';
$lang_module['central'] = 'Central';
$lang_module['south'] = 'South';
$lang_module['undefined'] = 'Undefined';
$lang_module['certificate_code'] = 'Certificate code';
$lang_module['identify_code'] = 'ID/Passport number';
$lang_module['birthday'] = 'Date of birth';
$lang_module['idprovince'] = 'Province/City';
$lang_module['result'] = 'Grade';
$lang_module['average'] = 'Average';
$lang_module['good'] = 'Good';
$lang_module['excellent'] = 'Excellent';
$lang_module['outstanding'] = 'Outstanding';
$lang_module['optionresult'] = 'Select grade';
$lang_module['notice'] = 'Notice';
$lang_module['back'] = 'Back';
$lang_module['note_max_searchpage'] = 'The number of search results exceeds the display limit of 100 pages. Please narrow your search criteria or go back to the previous page.';
$lang_module['note_wrong_page'] = "Where did you find this link? I can't process your request!";
$lang_module['issuedate'] = 'Issue date';
$lang_module['name_courses'] = 'Course';
$lang_module['number_student'] = 'Number of students';
$lang_module['name_training_detail'] = 'List of training courses %s';
$lang_module['title_training_detail'] = 'Tendering Course %s';
$lang_module['title_courses_detail'] = 'Course %s %s';
$lang_module['studentn'] = 'Student';
$lang_module['student_name'] = 'Student name';
$lang_module['place'] = 'Hometown';
$lang_module['certificate_singer'] = 'Certificate signer';
$lang_module['title_student'] = 'List of individuals certified with basic procurement certification';
$lang_module['title_name_courses'] = 'List of students for the course: %s';
$lang_module['title_information'] = 'Course information: %s';
$lang_module['title_student_name'] = 'Student information: %s';
$lang_module['co_so_dao_tao'] = 'Training center';
$lang_module['so_khoa_hoc'] = 'Number of courses';
$lang_module['confirm_not_user'] = 'You are not logged in.<br /> To view information, please <strong><a href="%s">Login</a></strong> or <strong><a href="%s">Register</a></strong> if you do not have an account.<br /> Signing up is simple and completely free.';
$lang_module['confirm_not_user1'] = '<strong>You are not logged in</strong><br /> To view information, please <strong><a href="#" data-toggle="loginFormShow">Login</a></strong> or <strong><a href="%s">Register</a></strong> if you do not have an account.<br /> Signing up is simple and completely free.';
$lang_module['notifi_minutue_price'] = "<p>The system deducted %s of your points when you viewed the student's date of birth and ID/passport number<p>";
$lang_module['notifi_minutue_price_training'] = '<p>The system deducted %s of your points when you viewed the number of courses<p>';
$lang_module['notifi_minutue_price_courses'] = '<p>The system deducted %s of your points when you viewed the number of students<p>';
$lang_module['notifi_click_minutue_price'] = 'Click to view information (you will be deducted %s points)';
$lang_module['VIP1_reg2'] = 'You need to <a href="%s">register for VIP1 package</a> to use this feature!';
$lang_module['VIP1_reg2_new'] = '<a href="%s">Register for VIP1 package</a> to watch without deducting points';
$lang_module['VIP1_reg3'] = 'You need <a href="%s">to renew your VIP1 package</a> to use this feature!';
$lang_module['VIP1_renew_new'] = '<a href="%s">Renew VIP1 package</a> to watch without deducting points';
$lang_module['register_vip_rand'] = '<a href="%s">Register for any VIP software package</a> to watch without points being deducted';
$lang_module['point_miss_goods'] = 'You do not have enough points to use this feature';
$lang_module['point_goods_err'] = 'Failed to view information. Please contact support!';
$lang_module['no_title'] = 'Uncategorized';
$lang_module['idprovince'] = 'Province/City';
$lang_module['idprovince_i'] = 'City';
$lang_module['idprovince_ii'] = 'Province/City';
$lang_module['iddistrict'] = 'District';
$lang_module['idward'] = 'Wards';
$lang_module['province_link'] = 'tenderlistbylocation';
$lang_module['stt'] = 'STT';
$lang_module['training'] = 'Training Institution';
$lang_module['invalid_date_format'] = 'The certificate date format is invalid. Please re-enter.';
$lang_module['invalid_training_date'] = 'The training date format is invalid. Please re-enter.';
$lang_module['birthday_date_format'] = 'Invalid date format. Please re-enter.';
