<!-- BEGIN: main -->
<!-- BEGIN: error -->
    <p class="alert alert-danger">{ERROR}</p>
<!-- END: error -->
<div class="border-bidding" id="bodycontent">
    <h1 class="title__tab_heading"><span>{laboratorys_list}</h1></span>
</div>

<section class="block-bidding">
    <div class="bidding-list bidding-list-detail">
        <div class="bidding-list-header">
            <div class="c-stt-lg">{LANG.stt}</div>
            <div class="c-name">{LANG.ma_so_ten_ptn}</div>
            <div class="c-pub">{LANG.ma_so_thue}</div>
        </div>
        <div class="bidding-list-body">
            <!-- BEGIN: empty -->
                <div class="text-center"><i>{LANG.khong_co_ket_qua}</i></div>
            <!-- END: empty -->
            <!-- BEGIN: loop -->
            <div class="item">
                <div class="c-stt-lg"><span class="label-name">{LANG.stt}: </span> {VIEW.stt}</div>
                <div class="c-name">
                    <span class="label-name">{LANG.ma_so_ten_ptn}: </span>
                    <a href="{VIEW.link_detail}" data-toggle="tooltip" title="{VIEW.ma_so_ten_ptn}"><h2> {VIEW.ma_so_ten_ptn} </h2></a>
                    </br><b>{LANG.address}:</b></i> {VIEW.dia_chi_phongtn}
                    </br><b>{LANG.tinh_thanh}:</b> <a href="{VIEW.link_province}" title="{VIEW.province}">{VIEW.province}</a>
                </div>
                <div class="c-pub text-left wrap__text"><span class="label-name">{LANG.ma_so_thue}:</span> {VIEW.ma_so_thue}</div>
            </div>
            <!-- END: loop -->
        </div>
    </div>
</section>
<!-- BEGIN: generate_page -->
<div class="text-center">{NV_GENERATE_PAGE}</div>
<!-- END: generate_page -->
<style>
    .title__tab_heading span {
        border-bottom: 4px #0685d6 solid;
        text-transform: uppercase;
        font-size: 20px;
        color: #0685d6;
        font-weight: 500;
        padding-bottom: 3px;
        display: inline-block;
    }
    .title__tab_heading span::before {
        display: inline-block;
        font-family: FontAwesome;
        font-size: 14px;
        line-height: 1;
        vertical-align: middle;
        margin-right: 5px;
        content: " ";
        color: #d61c1d;
    }

</style>
<!-- END: main -->