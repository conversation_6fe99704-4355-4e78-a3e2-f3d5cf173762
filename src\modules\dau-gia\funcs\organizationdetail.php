<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 10 Nov 2020 06:56:08 GMT
 */
if (!defined('NV_IS_MOD_DAU_GIA')) {
    die('Stop!!!');
}

$query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_bidder WHERE id_bidder=' . $id_bidder);
$array_data = $query->fetch();
if (!empty($array_data)) {
    // Load mặc định dữ liệu của biểu đồ thống kê của tổ chức
    $listBid = $finalResult = [];
    $view_chart = $nv_Request->get_int('view_chart', 'post', 1);
    if ($view_chart == 1 || $view_chart == 2) {
        $currentYear = nv_date('Y', NV_CURRENTTIME);
        $startYear = $currentYear - 6;

        // Không giới hạn năm
        $conditionYear = '';
        $conditionYearRs = '';
        if ($view_chart == 1) {
            $conditionYear = "YEAR(FROM_UNIXTIME(opening_bid)) >= " . $startYear . " AND ";
            $conditionYearRs = "YEAR(FROM_UNIXTIME(date_published)) >= " . $startYear . " AND ";
        } else {
            $conditionYear = "YEAR(FROM_UNIXTIME(opening_bid)) >= 2010 AND ";
            $conditionYearRs = "YEAR(FROM_UNIXTIME(date_published)) >= 2010 AND ";
            $startYear = 2010;
        }

        // Lấy các thông báo đấu giá của tổ chưc
        $listBid = $db->query("
            SELECT
                YEAR(FROM_UNIXTIME(opening_bid)) AS year,
                SUM(min_bid_prices) AS total_min_bid_prices
            FROM
                " . $db_config['prefix'] . "_dau_gia_bid
            WHERE
                " . $conditionYear . "
                id_bidder = " . $id_bidder . "
            GROUP BY
                YEAR(FROM_UNIXTIME(opening_bid))
            ORDER BY
                YEAR(FROM_UNIXTIME(opening_bid)) DESC
        ")->fetchAll(PDO::FETCH_ASSOC);

        if (!empty($listBid)) {
            $dataByYear = array_column($listBid, 'total_min_bid_prices', 'year');
        }

        // Lấy tất cả các id_source trước
        $listBidResult = $db->query("
            SELECT 
                FROM_UNIXTIME(date_published, '%Y') AS year,
                id_source
            FROM " . $db_config['prefix'] . "_dau_gia_bid_select_results
            WHERE " . $conditionYearRs . " id_bidder = " . $id_bidder . "
        ")->fetchAll();

        $sumByYear = [];

        if (!empty($listBidResult)) {
            // Gom tất cả id_source thành mảng để query 1 lần
            $idSources = array_column($listBidResult, 'id_source');

            // Nếu có id_source mới xử lý tiếp
            $totalMinBidPrices = [];
            if (!empty($idSources)) {
                // Query 1 lần
                $query = $db->query("
                    SELECT id_bid, SUM(min_bid_prices) AS total_min_bid_prices
                    FROM " . $db_config['prefix'] . "_dau_gia_asset_select
                    WHERE id_bid IN (" . implode(',', array_map('intval', $idSources)) . ")
                    GROUP BY id_bid
                ")->fetchAll();

                foreach ($query as $item) {
                    $totalMinBidPrices[$item['id_bid']] = (float) $item['total_min_bid_prices'];
                }
            }

            // Gom dữ liệu theo năm
            $dataByYearRs = [];
            foreach ($listBidResult as $row) {
                $row['total_min_bid_prices'] = $totalMinBidPrices[$row['id_source']] ?? 0;
                $dataByYearRs[$row['year']][] = $row;
            }

            foreach ($listBidResult as $row) {
                $row['total_min_bid_prices'] = $totalMinBidPrices[$row['id_source']] ?? 0;
                $dataByYearRs[$row['year']][] = $row;

                // Cộng dồn tổng tiền theo năm
                if (!isset($sumByYear[$row['year']])) {
                    $sumByYear[$row['year']] = 0;
                }
                $sumByYear[$row['year']] += $row['total_min_bid_prices'];
            }
        }

        // Tạo mảng năm và khoảng thời gian cho 7 năm
        $years = [];
        for ($year = $startYear; $year <= $currentYear; $year++) {
            $finalResult[$year] = [
                'total_min_bid_prices_bid' => isset($dataByYear[$year]) ? (float)$dataByYear[$year] : 0,
                'total_min_bid_prices_asset' => isset($sumByYear[$year]) ? $sumByYear[$year] : 0
            ];
        }
    }

    // Xử lý biểu đồ thống kê giá trị đấu giá của tổ chức
    if ($nv_Request->get_title('action', 'post', '') == 'get_data_chart') {
        nv_jsonOutput($finalResult);
    }

    $array_data['data_bid'] = json_encode($finalResult);

    // So sánh thay đổi giữa các phiên bản
    if ($nv_Request->get_title('action', 'post') == 'view_change_org') {
        $strID = $nv_Request->get_title('id', 'post', '');
        if ($strID != '') {
            $arrID = explode('-', trim($strID));
            if (intval($arrID[1]) > 0 and intval($arrID[0]) > 0) {
                $data = [
                    'org' => buildInfoOrgTable($arrID[1], $array_data),
                    'org1' => buildInfoOrgTable($arrID[0], $array_data)
                ];

                nv_jsonOutput(array(
                    'res' => 'success',
                    'data' => $data
                ));
            } else {
                nv_jsonOutput(array(
                    'res' => 'error',
                    'data' => $nv_Lang->getModule('error_check_notifi_ver')
                ));
            }
        } else {
            nv_jsonOutput(array(
                'res' => 'error',
                'data' => 'null'
            ));
        }
    }

    $page_title = $array_data['name_bidder'];

    $space = ', ';
    $base_link = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization';

    if ($array_data['id_ward_bidder'] > 0) {
        $db->sqlreset()
            ->select('title, alias')
            ->from(NV_PREFIXLANG . '_location_ward')
            ->where('id =' . $array_data['id_ward_bidder']);
        $sql = $db->sql();
        $result = $db->query($sql);
        $row = $result->fetch();
        if (!empty($row)) {
            $link = $base_link . '/X-' . $row['alias'] . '-' . $array_data['id_ward_bidder'];
            $array_data['address_bidder'] .= $space;
            $array_data['address_bidder'] .= '<a href="' . $link . '">' . $row['title'] . '</a>';
        }
    }

    if ($array_data['id_district_bidder'] > 0) {
        $db->sqlreset()
            ->select('title, alias')
            ->from(NV_PREFIXLANG . '_location_district')
            ->where('id =' . $array_data['id_district_bidder']);
        $sql = $db->sql();
        $result = $db->query($sql);
        $row = $result->fetch();
        if (!empty($row)) {
            $link = $base_link . '/H-' . $row['alias'] . '-' . $array_data['id_district_bidder'];
            $array_data['address_bidder'] .= $space;
            $array_data['address_bidder'] .= '<a href="' . $link . '">' . $row['title'] . '</a>';
        }
    }

    if ($array_data['id_province_bidder'] > 0) {
        $db->sqlreset()
            ->select('title, alias')
            ->from(NV_PREFIXLANG . '_location_province')
            ->where('id =' . $array_data['id_province_bidder']);
        $sql = $db->sql();
        $row = $db->query($sql)->fetch();
        if (!empty($row)) {
            $link = $base_link . '/T-' . $row['alias'] . '-' . $array_data['id_province_bidder'];
            $array_data['address_bidder'] .= $space;
            $array_data['address_bidder'] .= '<a href="' . $link . '">' . $row['title'] . '</a>';
        }
    }
    $array_data['address_bidder'] = trim($array_data['address_bidder'], " ,");

    $array_mod_title[] = array(
        'title' => $nv_Lang->getModule('organization'),
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization', true)
    );
    $array_mod_title[] = array(
        'title' => $array_data['name_bidder'],
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization/' . change_alias($array_data['name_bidder']) . '-' . $array_data['id_bidder'] . '.html', true)
    );

    // lấy các thông tin khác
    $query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_bidder_chi_nhanh WHERE id_bidder=' . $id_bidder . '');
    $arr_chi_nhanh = [];
    while ($row = $query->fetch()) {
        $arr_chi_nhanh[$row['id']] = $row;
    }
    $query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_bidder_van_phong WHERE id_bidder=' . $id_bidder . '');
    $arr_van_phong = [];
    while ($row = $query->fetch()) {
        $arr_van_phong[$row['id']] = $row;
    }
    $query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_bidder_dgv WHERE id_bidder=' . $id_bidder . '');
    $arr_dgv = [];
    while ($row = $query->fetch()) {
        $arr_dgv[$row['id']] = $row;
    }

    if ($array_data['id_dep'] > 0) {
        $query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_deparment WHERE depofjusticeid=' . $array_data['id_dep'] . '');
        while ($row = $query->fetch()) {
            $row['url_dep'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=department/' . change_alias($row['fullname']) . '-' . $row['depofjusticeid'] . '.html';
            $array_data['dep'] = $row;
        }
    }

    // Lấy các lần thay đổi thông tin
    $_sql = 'SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_bidder_log WHERE id_bidder=' . $id_bidder . ' ORDER BY created_at ASC';
    $query = $db->query($_sql);
    $array_data['logs'] = [];
    $i = 1;
    $prev_id = 0;
    $has_first_published = false;
    while ($row = $query->fetch()) {
        $_log_data = json_decode($row['data'], true);
        // Kiểm tra nếu bị trùng log Đăng lần đầu, thì bỏ qua
        if ($_log_data['type'] == 'first_publish') {
            if ($has_first_published) {
                continue;
            }
            $has_first_published = true;
        }
        $array_data['logs'][$i]['stt'] = $i;
        $array_data['logs'][$i]['id'] = $row['id'];
        $array_data['logs'][$i]['created_at'] = $row['created_at'];
        $array_data['logs'][$i]['fields'] = $_log_data['fields'] ?? null;
        $array_data['logs'][$i]['type'] = $_log_data['type'] ?? '';
        $array_data['logs'][$i]['prev'] = $prev_id;
        $prev_id = $row['id'];
        $i++;
    }

    $array_data['logs'] = array_reverse($array_data['logs']);

    // Lấy các thông báo đấu giá của tổ chức này: Mặc định lấy 2 thông báo gần nhất, nếu có VIP thì được xem thêm
    $count_total['bid'] = $db->query('SELECT COUNT(*) FROM ' . $db_config['prefix'] . '_' . $module_data . '_bid WHERE id_bidder=' . $id_bidder . '')->fetchColumn();
    $query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_bid WHERE id_bidder=' . $id_bidder . ' ORDER BY opening_bid DESC LIMIT 2');
    $arr_bid = [];
    while ($row = $query->fetch()) {
        $row['opening_reg_format'] = nv_date("H:i d/m/Y", $row['opening_reg']);
        $row['closing_reg_format'] = nv_date("H:i d/m/Y", $row['closing_reg']);
        $row['opening_bid_format'] = nv_date("H:i d/m/Y", $row['opening_bid']);
        $row['date_bid_format'] = nv_date("H:i d/m/Y", $row['date_bid']);
        $row['url_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $row['alias'] . '-' . $row['id_bid'] . '.html';
        $arr_bid[$row['id_bid']] = $row;
    }

    // Lấy các kết quả lựa chọn tổ chức đấu giá mà tổ chức này được chọn: Mặc định lấy 2 kết quả gần nhất, nếu có VIP thì được xem thêm
    $count_total['selected'] = $db->query('SELECT COUNT(*) FROM ' . $db_config['prefix'] . '_' . $module_data . '_bid_select_results WHERE id_bidder=' . $id_bidder . '')->fetchColumn();
    $query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_bid_select_results WHERE id_bidder=' . $id_bidder . ' ORDER BY date_published DESC LIMIT 2');
    $arr_select_result = [];
    while ($row = $query->fetch()) {
        $row['date_published_format'] = nv_date("H:i d/m/Y", $row['date_published']);
        $row['url_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=resultorganization/' . $row['alias'] . '-' . $row['id'] . '.html';
        $row['name_owner'] = '';
        $row['min_bid_price'] = 'N/A';
        $arr_select_result[$row['id']] = $row;
    }
    if (!empty($arr_select_result)) {
        $arr_name_owner = $db->query("SELECT t2.id as id, t1.name as name FROM " . $db_config['prefix'] . "_" . $module_data . "_asset_owners t1 INNER JOIN " . $db_config['prefix'] . "_" . $module_data . "_bid_select_results t2 ON t1.id = t2.id_owner WHERE t2.id IN ( " . implode(',', array_keys($arr_select_result)) . ")")->fetchAll();
        if (!empty($arr_name_owner)) {
            foreach ($arr_name_owner as $row) {
                $arr_select_result[$row['id']]['name_owner'] = $row['name'];
            }
        }
        unset($arr_name_owner);

        $arr_min_bid_prices = $db->query("SELECT t3.id as id, MIN(t1.min_bid_prices) as min_bid_price FROM " . $db_config['prefix'] . "_" . $module_data . "_asset_select t1 INNER JOIN " . $db_config['prefix'] . "_" . $module_data . "_bid_select t2 ON t1.id_bid = t2.id_source INNER JOIN " . $db_config['prefix'] . "_" . $module_data . "_bid_select_results t3 ON t2.id_bid = t3.id_select WHERE t3.id IN ( " . implode(',', array_keys($arr_select_result)) . ") GROUP BY t3.id")->fetchAll();
        if (!empty($arr_min_bid_prices)) {
            foreach ($arr_min_bid_prices as $row) {
                $row['min_bid_price'] = nv_number_format($row['min_bid_price'], NV_LANG_DATA) . ' đồng';
                $arr_select_result[$row['id']]['min_bid_price'] = $row['min_bid_price'];
            }
        }
        unset($arr_min_bid_prices);
    }

    $page_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization/' . change_alias($array_data['name_bidder']) . '-' . $array_data['id_bidder'] . '.html';
    $canonicalUrl = getCanonicalUrl($page_url);

    $contents = nv_theme_dau_gia_organizationdetail($array_data, $arr_bid, $arr_chi_nhanh, $arr_van_phong, $arr_dgv, $arr_select_result, $count_total);

    // Lấy danh sách chi nhánh
    $schema_mentions = [];
    if (!is_array($arr_chi_nhanh)) {
        $arr_chi_nhanh = (array) $arr_chi_nhanh;
    }
    foreach ($arr_chi_nhanh as $chi_nhanh_item) {
        $schema_mentions[] = [
            "@type" => "Organization",
            "name" => $chi_nhanh_item['name'],
            'additionalProperty' => [
                [
                    '@type' => 'PropertyValue',
                    'name' => $nv_Lang->getModule('so_giay_dkhd'),
                    'value' => $chi_nhanh_item['number_dkhd']
                ],
                [
                    '@type' => 'PropertyValue',
                    'name' => $nv_Lang->getModule('ngay_cap'),
                    'value' => $chi_nhanh_item['time_dkhd']
                ]
            ]
        ];
    }
    // Lấy danh sách văn phòng
    if (!is_array($arr_van_phong)) {
        $arr_van_phong = (array) $arr_van_phong;
    }
    foreach ($arr_van_phong as $van_phong_item) {
        $schema_mentions[] = [
            "@type" => "Organization",
            "name" => $van_phong_item['name'],
            "additionalProperty" => [
                [
                    "@type" => "PropertyValue",
                    "name" => $nv_Lang->getModule('van_phong_number'),
                    "value" => $van_phong_item['number']
                ],
                [
                    "@type" => "PropertyValue",
                    "name" => $nv_Lang->getModule('van_phong_time'),
                    "value" => $van_phong_item['time']
                ]
            ]
        ];
    }
    // Lấy danh sách ĐGV
    if (!is_array($arr_dgv)) {
        $arr_dgv = (array) $arr_dgv;
    }
    foreach ($arr_dgv as $dgv_item) {
        $schema_mentions[] = [
            "@type" => "Person",
            "name" => $dgv_item['name'],
            "additionalProperty" => [
                [
                    "@type" => "PropertyValue",
                    "name" => $nv_Lang->getModule('dgv_number_cchn'),
                    "value" => $dgv_item['number_cchn']
                ],
                [
                    "@type" => "PropertyValue",
                    "name" => $nv_Lang->getModule('dgv_time_cchn'),
                    "value" => $dgv_item['time_cchn']
                ],
                [
                    "@type" => "PropertyValue",
                    "name" => $nv_Lang->getModule('dgv_number_card'),
                    "value" => $dgv_item['num_dgv']
                ],
                [
                    "@type" => "PropertyValue",
                    "name" => $nv_Lang->getModule('dgv_time_card'),
                    "value" => $dgv_item['time_dgv']
                ]
            ]
        ];
    }
    // Build address
    $address = [
        "@type" => "PostalAddress",
        "streetAddress" => strip_tags($array_data['address_bidder']),
        "addressLocality" => $db->query("SELECT title FROM " . $db_config['prefix'] . "_vi_location_district WHERE id=" . $array_data['id_district_bidder'])->fetchColumn(),
        "addressRegion" => $db->query("SELECT title FROM " . $db_config['prefix'] . "_vi_location_province WHERE id=" . $array_data['id_province_bidder'])->fetchColumn(),
        "addressCountry" => "VN"
    ];

    // Founding date
    $foundingDate = !empty($array_data['ngay_quyet_dinh']) ? $array_data['ngay_quyet_dinh'] : null;
    // Founding location (department/justice)
    $foundingLocation = null;
    if (!empty($array_data['dep']['fullname'])) {
        $foundingLocation = [
            "@type" => "Place",
            "name" => $array_data['dep']['fullname']
        ];
    }
    // Founder
    $founder = null;
    if (!empty($array_data['auctioneername'])) {
        $founder = [
            "@type" => "Person",
            "name" => $array_data['auctioneername']
        ];
    }
    // Number of employees
    $numberOfEmployees = isset($array_data['quantityauctioneer']) ? (int)$array_data['quantityauctioneer'] : null;
    // Identifier (PropertyValue array)
    $identifier = [];
    if (!empty($array_data['so_quyet_dinh'])) {
        $identifier[] = [
            "@type" => "PropertyValue",
            "name" => $nv_Lang->getModule('so_quyet_dinh'),
            "value" => $array_data['so_quyet_dinh']
        ];
    }
    if (!empty($array_data['so_cchn'])) {
        $identifier[] = [
            "@type" => "PropertyValue",
            "name" => $nv_Lang->getModule('dgv_number_cchn'),
            "value" => $array_data['so_cchn']
        ];
    }
    // Contact point
    $contactPoint = [
        "@type" => "ContactPoint",
        "contactType" => "customer service",
        "telephone" => !empty($array_data['phone']) ? $array_data['phone'] : '',
        "faxNumber" => !empty($array_data['fax']) ? $array_data['fax'] : '',
        "email" => !empty($array_data['email']) ? $array_data['email'] : ''
    ];
    // Schema Organization
    $my_schema = [
        "@context" => "https://schema.org",
        "@type" => "Organization",
        "@id" => $canonicalUrl,
        "url" => $canonicalUrl,
        "name" => $array_data['name_bidder'],
        "description" => $array_data['name_bidder'],
        "address" => $address,
        // Optional fields, only add if available
    ];
    if ($foundingDate) {
        $my_schema['foundingDate'] = $foundingDate;
    }
    if ($foundingLocation) {
        $my_schema['foundingLocation'] = $foundingLocation;
    }
    if ($founder) {
        $my_schema['founder'] = $founder;
    }
    if ($numberOfEmployees) {
        $my_schema['numberOfEmployees'] = $numberOfEmployees;
    }
    if (!empty($identifier)) {
        $my_schema['identifier'] = $identifier;
    }
    if (!empty($contactPoint['telephone']) || !empty($contactPoint['faxNumber']) || !empty($contactPoint['email'])) {
        $my_schema['contactPoint'] = $contactPoint;
    }
    $my_schema['publisher'] = [
        "@type" => "Organization",
        "name" => "DauGia.net",
        "url" => "https://daugia.net"
    ];
    $my_schema['sourceOrganization'] = [
        "@type" => "GovernmentOrganization",
        "name" => "Cổng thông tin điện tử quốc gia về đấu giá tài sản",
        "url" => "https://dgts.moj.gov.vn"
    ];
    $my_schema['citation'] = "Nguồn: Cổng thông tin điện tử quốc gia về đấu giá tài sản";
    if (!empty($schema_mentions)) {
        $my_schema['mentions'] = $schema_mentions;
    }
    $nv_schemas[] = $my_schema;

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
} else {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=organization');
    die();
}

function buildInfoOrgTable($log_id, $array_data)
{
    global $global_config, $module_file, $db, $db_config, $module_data;

    $xtpl = new XTemplate('organizationdetail.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    $_sql = 'SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_bidder_log WHERE id=' . $log_id . ' ORDER BY created_at ASC';
    $rowlog = $db->query($_sql)->fetch();
    if (empty($rowlog)) {
        return 'No data';
    }

    $log_data = json_decode($rowlog['data'], true);
    $log_data = $log_data['data'];

    $log_data['address_bidder'] = build_full_address($log_data);

    if ($log_data['id_dep'] > 0) {
        $query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_deparment WHERE depofjusticeid=' . $log_data['id_dep'] . '');
        while ($row = $query->fetch()) {
            $log_data['dep'] = $row;
        }
    }

    $xtpl->assign('ROW', $log_data);
    $xtpl->parse('info_table');
    return $xtpl->text('info_table');
}
