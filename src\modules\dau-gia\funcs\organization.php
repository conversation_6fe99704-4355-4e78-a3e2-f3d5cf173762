<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 10 Nov 2020 06:56:08 GMT
 */

if (!defined('NV_IS_MOD_DAU_GIA')) {
    die('Stop!!!');
}

if ($nv_Request->isset_request('get_organ', 'get, post', '')) {
    $q = $nv_Request->get_typed_array('q', 'get', 'title');
    $searchtext = $q['term'];
    $array_bidder[] = ['id' => 0, 'text' => $nv_Lang->getModule('select_all')];
    if (!empty($searchtext)) {
        $home_page = $nv_Request->get_int('home_page', 'get', 0);
        $perpage = ($home_page == 1) ? 10 : 20;
        $page = 1;
        $db->sqlreset()
            ->select('id_bidder as id, name_bidder as text')
            ->from('nv4_dau_gia_bidder')
            ->order('id_bidder ASC')
            ->limit($per_page)
            ->offset(($page - 1) * $per_page)
            ->where('name_bidder LIKE :searchtext');

        $sth = $db->prepare($db->sql());
        $sth->bindValue(':searchtext', '%' . $searchtext . '%');
        $sth->execute();
        while ($row = $sth->fetch()) {
            $array_bidder[] = $row;
        }
    }

    $array_data['results'] = $array_bidder;
    $array_data['total'] = sizeof($array_bidder);
    nv_jsonOutput($array_data);
}

$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('organization'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization', true)
);

$page_title = $nv_Lang->getModule('organization');
$module_config_view = $module_config[$module_name];
$home_page = $nv_Request->get_int('home_page', 'get', 0);
$perpage = ($home_page == 1) ? 10 : 20;
$page = $nv_Request->get_page('page', 'get', 1);

// Khởi tạo mảng chứa các tham số tìm kiếm
$array_search = [];
$array_search['q'] = $nv_Request->get_title('q', 'get', '');
$array_search['province'] = $nv_Request->get_int('province', 'get', 0);
$array_search['district'] = $nv_Request->get_int('district', 'get', 0);
$array_search['ward'] = $nv_Request->get_int('ward', 'get', 0);
$array_search['department'] = $nv_Request->get_int('department', 'get', 0);

$location_only = false;
$base_url_location = '';

// Kiểm tra nếu không có các tham số tìm kiếm khác
if (empty($array_search['q']) && empty($array_search['department'])) {
    $province_id = $district_id = $ward_id = 0;

    if (preg_match('/^([THX])\-([a-zA-Z0-9\-]+)\-([0-9]+)$/i', ($array_op[1] ?? ''), $m)) {
        $location_type = $m[1];
        $location_alias = $m[2];
        $location_id = intval($m[3]);

        if ($location_type == 'T') {
            $province_id = $location_id;
        } elseif ($location_type == 'H') {
            $district_id = $location_id;
        } elseif ($location_type == 'X') {
            $ward_id = $location_id;
        }
    } elseif ($array_search['ward'] > 0) {
        $ward_id = $array_search['ward'];
    } elseif ($array_search['district'] > 0) {
        $district_id = $array_search['district'];
    } elseif ($array_search['province'] > 0) {
        $province_id = $array_search['province'];
    }

    if ($ward_id > 0) {
        $sql = "SELECT w.id, w.alias, w.iddistrict, w.idprovince, w.title, d.title as district_title, p.title as province_title FROM " . NV_PREFIXLANG . "_location_ward w LEFT JOIN " . NV_PREFIXLANG . "_location_district d ON w.iddistrict = d.id LEFT JOIN " . NV_PREFIXLANG . "_location_province p ON w.idprovince = p.id WHERE w.status=1 AND w.id = " . $db->quote($ward_id);
        $result = $db->query($sql);
        if ($row = $result->fetch()) {
            $array_search['ward'] = $row['id'];
            $array_search['district'] = $row['iddistrict'];
            $array_search['province'] = $row['idprovince'];
            $base_url_location = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization/X-' . $row['alias'] . '-' . $row['id'];
            $location_only = true;
            $page_title = $nv_Lang->getModule('organization') . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'] . ', ' . $row['district_title'] . ', ' . $row['province_title'];

            if (!empty($module_info['funcs'][$op]['description'])) {
                $description = $module_info['funcs'][$op]['description'] . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'] . ', ' . $row['district_title'] . ', ' . $row['province_title'];
                $meta_property['og:description'] = $description;
            }
        }
    } elseif ($district_id > 0) {
        $sql = "SELECT d.id, d.alias, d.idprovince, d.title, p.title as province_title FROM " . NV_PREFIXLANG . "_location_district d LEFT JOIN " . NV_PREFIXLANG . "_location_province p ON d.idprovince = p.id WHERE d.status=1 AND d.id = " . $db->quote($district_id);
        $result = $db->query($sql);
        if ($row = $result->fetch()) {
            $array_search['district'] = $row['id'];
            $array_search['province'] = $row['idprovince'];
            $base_url_location = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization/H-' . $row['alias'] . '-' . $row['id'];
            $location_only = true;
            $page_title = $nv_Lang->getModule('organization') . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'] . ', ' . $row['province_title'];

            if (!empty($module_info['funcs'][$op]['description'])) {
                $description = $module_info['funcs'][$op]['description'] . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'] . ', ' . $row['province_title'];
                $meta_property['og:description'] = $description;
            }
        }
    } elseif ($province_id > 0) {
        $sql = "SELECT id, alias, title FROM " . NV_PREFIXLANG . "_location_province WHERE status=1 AND id = " . $db->quote($province_id);
        $result = $db->query($sql);
        if ($row = $result->fetch()) {
            $array_search['province'] = $row['id'];
            $base_url_location = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization/T-' . $row['alias'] . '-' . $row['id'];
            $location_only = true;
            $page_title = $nv_Lang->getModule('organization') . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'];

            if (!empty($module_info['funcs'][$op]['description'])) {
                $description = $module_info['funcs'][$op]['description'] . ' ' . $nv_Lang->getModule('at') . ' ' . $row['title'];
                $meta_property['og:description'] = $description;
            }
        }
    }
}

$where = [];
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organization';

$where[] = "id_bidder>0 and status=1";

if (!empty($array_search['province'])) {
    $where[] = "id_province_bidder=" . $array_search['province'];
    if (!$location_only) {
        $base_url .= "&amp;province=" . $array_search['province'];
    }
}

if (!empty($array_search['district'])) {
    $where[] = "id_district_bidder=" . $array_search['district'];
    if (!$location_only) {
        $base_url .= "&amp;district=" . $array_search['district'];
    }
}

if (!empty($array_search['ward'])) {
    $where[] = "id_ward_bidder=" . $array_search['ward'];
    if (!$location_only) {
        $base_url .= "&amp;ward=" . $array_search['ward'];
    }
}

if (!empty($array_search['department'])) {
    $where[] = "id_dep=" . $array_search['department'];
    $base_url .= "&amp;department=" . $array_search['department'];
}

if (!empty($array_search['q'])) {
    $where_q = [];
    $arr_key = explode(',', $array_search['q']);
    foreach ($arr_key as $key) {
        $key = trim($key);
        if (!empty($key)) {
            $where_q[] = "(name_bidder LIKE '%" . $db->dblikeescape($key) . "%' OR phone LIKE '%" . $db->dblikeescape($key) . "%' OR fax LIKE '%" . $db->dblikeescape($key) . "%' OR email LIKE '%" . $db->dblikeescape($key) . "%' OR auctioneername LIKE '%" . $db->dblikeescape($key) . "%')";
        }
    }
    if (!empty($where_q)) {
        $where[] = "(" . implode(' OR ', $where_q) . ")";
        $base_url .= "&amp;q=" . urlencode($array_search['q']);
        $nv_BotManager->setPrivate(); // Issue: https://vinades.org/dauthau/dauthau.info/-/issues/3201
    }
}

if ($page < 1) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_wrong_page') . $btn, 'danger');

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

/**
 * thao: cho sửa dùm anh nếu page > 100 báo lỗi:
 * Vui lòng thay đổi tiêu chí tìm kiếm để có thông tin bạn cần
 */
if ($page > 100) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_max_searchpage') . $btn);

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

$db->sqlreset()
    ->select('COUNT(id_bidder)')
    ->from('nv4_dau_gia_bidder')
    ->where(implode(' AND ', $where));
$sth = $db->prepare($db->sql());
$sth->execute();
$total = $sth->fetchColumn();

if ($location_only && !empty($base_url_location)) {
    $base_url = $base_url_location;
}

$urlappend = '&amp;page=';
// Kiểm tra đánh số trang
betweenURLs($page, ceil($total / $perpage), $base_url, $urlappend, $prevPage, $nextPage);

$db->select('*')
    ->order('create_at DESC')
    ->limit($perpage)
    ->offset(($page - 1) * $perpage);
$sth = $db->prepare($db->sql());
$sth->execute();
$i = ($page - 1) * $perpage;
$array_data = [];
while ($view = $sth->fetch()) {
    $view['stt'] = $i + 1;
    $array_data[$view['id_bidder']] = $view;
    $i++;
}

if ($page > 1 and empty($array_data)) {
    if (empty($array_data)) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
    }
    $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
}

$generate_page = nv_generate_page($base_url, $total, $perpage, $page);

// lấy page_url
$page_url = $base_url;
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}
$canonicalUrl = getCanonicalUrl($page_url);

// Xử lý title, description phân trang
if ($page > 1) {
    $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
    if (!empty($description)) {
        $description .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
        $meta_property['og:description'] = $description;
    } elseif (!empty($module_info['funcs'][$op]['description'])) {
        $description = $module_info['funcs'][$op]['description'] . NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
        $meta_property['og:description'] = $description;
    }
}

$contents = nv_theme_dau_gia_organization($array_data, $generate_page, $module_config_view);

// Schema: CollectionPage
$my_schema_items = [];
$pos = 1;
foreach ($array_data as $row) {
    $my_schema_items[] = [
        '@type' => 'ListItem',
        'position' => $pos,
        'item' => [
            '@type' => 'CreativeWork',
            'name' => $row['name_bidder'],
            'url' => DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/' . $op . '/' . $row['alias'] . '-' . $row['id_bidder'] . '.html',
            'creator' => [
                '@type' => 'Organization',
                'name' => $row['name_bidder']
            ],
            'datePublished' => nv_date("Y-m-d\TH:i:sP", $row['create_at']),
            'about' => [
                '@type' => 'Organization',
                'name' => $row['name_bidder']
            ],
            'inLanguage' => 'vi',
            'additionalProperty' => [
                [
                    '@type' => 'PropertyValue',
                    'name' => $nv_Lang->getModule('number_dgv'),
                    'value' => $row['quantityauctioneer']
                ]
            ]
        ]
    ];
    $pos++;
}
$nv_schemas[] = [
    '@context' => 'https://schema.org',
    '@type' => 'CollectionPage',
    'name' => ($page > 1) ? $nv_Lang->getModule('organization_schema_page_title') . NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page : $nv_Lang->getModule('organization_schema_page_title'),
    'url' => $canonicalUrl,
    'mainEntity' => [
        '@type' => 'ItemList',
        'name' => ($page > 1) ? $nv_Lang->getModule('organization_schema_page_title_list') . NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page : $nv_Lang->getModule('organization_schema_page_title_list'),
        'itemListElement' => $my_schema_items
    ]
];

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
