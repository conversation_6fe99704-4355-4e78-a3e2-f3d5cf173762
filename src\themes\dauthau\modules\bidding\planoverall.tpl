<!-- BEGIN: main -->
<!-- L<PERSON><PERSON> chọn nhà thầu -->
<div class="border-bidding">
    <span>{LANG.pagetitle_khttlcnt}</span>
</div>
<div id="confirm" class="modal fade auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body"></div>
            <div class="modal-footer">
                <span class="button"></span>
                <button type="button" class="ok btn btn-primary">{LANG.ok}</button>
                <button type="button" data-dismiss="modal" class="btn">{LANG.close}</button>
            </div>
        </div>
    </div>
</div>
<div id="popup_not_dismiss" class="modal fade auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body"></div>
        </div>
    </div>
</div>
<div class="msgshow" id="msgshow"></div>
<!-- BEGIN: popup_login -->
{POPUP_LOGIN}
<!-- END: popup_login -->

<!-- BEGIN: popup_not_point -->
<script>
$(function() {
    var mess = '{LANG.info_point_not_enought}';
    mess += '<p class="text-center"><a class="btn btn-danger" href="https://id.dauthau.net/{NV_LANG_DATA}/points/#muadiem" target="_blank">{LANG.buy_points}</a></p>';
    $("#popup_not_dismiss").find(".modal-body").html(mess);
    $("#popup_not_dismiss").modal({
        backdrop: "static",
        keyboard: false
    });
});
</script>
<!-- END: popup_not_point -->
<!-- BEGIN: msgshow -->
<script>
    alert_msg('{LANG.message_point_view_suss}');
</script>
<!-- END: msgshow -->
<!-- BEGIN: recaptcha -->
<div id="captchaModal" class="modal fade auto-width auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <p class="modal-title">{LANG.recapcha_title}</p>
            </div>
            <div class="modal-body">
                <div>{LANG.recapcha_body}</div>
                <div data-toggle="recaptcha" data-callback="verify_captcha" id="{RECAPTCHA_ELEMENT}"></div>
            </div>
        </div>
    </div>
</div>
<script>
function verify_captcha(e) {
    click_update();
}
</script>
<!-- END: recaptcha -->
<div class="bidding-wrapper">
    <div class="bidding-title">
        <h1 class="tl">{DATA.title}</h1>
    </div>

    <!-- BEGIN: prb -->
    <div class="margin-bottom">
        <div class="prb_container">
            <div class="prb clearfix">
                <!-- BEGIN: mess_item -->
                <span class="prb-progressbar">{MESS}</span>
                <!-- END: mess_item -->
            </div>
            <div class="prb clearfix">
                <!-- BEGIN: prb_item -->
                <!-- BEGIN: if_a -->
                <a class="item" href="{PROCESS.url}"><span class="icn {PROCESS.classes}" title="{PROCESS.title}"></span><span class="tl">{PROCESS.title}</span></a>
                <!-- END: if_a -->
                <!-- BEGIN: if_span -->
                <span class="item"><span class="icn {PROCESS.classes}" title="{PROCESS.title}"></span><span class="tl">{PROCESS.title}</span></span>
                <!-- END: if_span -->
                <!-- END: prb_item -->
            </div>
        </div>
    </div>
    <!-- END: prb -->
    <div class="row">
        <div class="col-xs-24 btn-share-group">
            <span>{LANG.share} </span>
            <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_fb_share();" title="{LANG.fb_share}">
                <span class="icon-facebook"></span>
            </a>
            <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_tw_share('', '{DATA.title}');" title="{LANG.tweet}">
                <span class="icon-twitter"></span>
            </a>
            <a href="javascript:void(0)" class="btn-share btn-copy-link" title="{LANG.copy_link}">
                <em class="fa fa-link"></em>
                <span class="tip" style="display: none;">{LANG.link_copy_successfully}</span>
            </a>
        </div>
    </div>
    <div class="bidding-page-btn flex_end">
        <!-- BEGIN: link_msc -->
        <a href="{LINK_MSC}"> <button class="btn btn-primary{BLUR_CLASS}">
            {LANG.icon_vneps}
            Link MSC</button></a>
        <!-- END: link_msc -->
         
        <div class="text-right">
            {FILE "button_show_log.tpl"}
            <div class="small">
                {LANG.crawl_time}: <strong>{DATA.fgettime}</strong>
            </div>
            <!-- BEGIN: update -->
            <div class="margin-top-sm">
                <span class="small">{DATA.update_info}</span> <!-- BEGIN: update_button --><a style="margin-left: auto" id="reupdate" class="btn btn-default btn-xs active" onclick="show_captcha()" href="javascript:void(0)" data-link="{DATA.link_update}" data-check="{NV_CHECK_SESSION}">{LANG.reupdate}</a> <img id="update_wait" style="display: none" src="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/images/load_bar.gif" /><!-- END: update_button -->
                <!-- BEGIN: crawl_request_history_button -->
                <a style="margin-left: auto" id="crawl_request_history" class="btn btn-default btn-xs active" href="javascript:void(0)">{LANG.crawl_request_history}</a>
                <!-- END: crawl_request_history_button -->
            </div>
            <!-- END: update -->
        </div>
    </div>
    {FILE "crawl_request_history_list.tpl"}

    <div class="bidding-detail">
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.so_khttlcnt}</div>
                <div class="c-val">
                    <span class="bidding-code">{DATA.code}</span>
                </div>
            </div>
            <div>
                <div class="c-tit">{LANG.ngay_dang_tai}</div>
                <div class="c-val">{DATA.addtime}</div>
            </div>
        </div>
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tit">{LANG.status_dt}</div>
                <div class="c-val">{DATA.notify_status}</div>
            </div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.plan_tt_title}</div>
            <div class="c-val">{DATA.title}</div>
        </div>
    </div>
    <!-- BEGIN: du_an -->
    <h2 class="bidding-sub-title">{LANG.project_info}</h2>
    <div class="bidding-detail">
        <!-- BEGIN: dev_project -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.num_project}</div>
            <div class="c-val">{DATA.project_code}</div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.name_project}</div>
            <div class="c-val">
                <a href="{DATA.dev_project.link}">{DATA.project_name}</a>
            </div>
        </div>
        <!-- END: dev_project -->
        <!-- BEGIN: chu_dau_tu -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.chu_dau_tu}</div>
            <div class="c-val">
                <!-- BEGIN: if_link -->
                <a href="{DATA.link_solicitor}" title="{DATA.solicitor_title}">{DATA.solicitor_title}</a>
                <!-- END: if_link -->
                <!-- BEGIN: if_no_link -->
                <strong>{DATA.solicitor_title}</strong>
                <!-- END: if_no_link -->
                <!-- BEGIN: if_unlink -->
                <a href="{DATA.link_solicitor_unlink}">{DATA.solicitor_title}</a>
                <!-- END: if_unlink -->
            </div>
        </div>
        <!-- END: chu_dau_tu -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.isoda}</div>
            <div class="c-val">{DATA.isoda}</div>
        </div>
        <!-- BEGIN: total_investment_show -->
        <div class="bidding-detail-item">
            <div class="c-tit">{TITLE_TMDT}</div>
            <div class="c-val" id="total_investment">
                <!-- BEGIN: total_invest -->
                {DATA.total_investment}
                <!-- END: total_invest -->
                <!-- BEGIN: total_invest_vip_web -->
                {view_adv_point} {INVEST_VIP_WEB}
                <!-- END: total_invest_vip_web -->
                <!-- BEGIN: total_invest_vip -->
                {view_adv_point} {INVEST_VIP}
                <!-- END: total_invest_vip -->
                <!-- BEGIN: total_invest_login -->
                {LINK_USER}
                <!-- END: total_invest_login -->
            </div>
        </div>
        <!-- END: total_investment_show -->
        <!-- BEGIN: pperiod -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.pperiod}</div>
            <div class="c-val">{DATA.pperiod} {DATA.pperiod_unit}</div>
        </div>
        <!-- END: pperiod -->
        <!-- BEGIN: location -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.location}</div>
            <div class="c-val">{DATA.location}</div>
        </div>
        <!-- END: location -->
    </div>
    <!-- END: du_an -->
    <!-- BEGIN: noi_dung -->
    <h2 class="bidding-sub-title">{LANG.plan_overall_detail}</h2>
    <div class="bidding-detail">
        <div class="bidding-detail-item">
            <div class="c-tit c-tit-lg">{LANG.plan_overall_attach}</div>
            <div class="c-val">
                <!--<div><a class="list-group-item" href="{LINK_NOIDUNG_NORMAL}"><span><i class="fa fa-file-text-o"></i> {DATA.file_msc_name}</span></a></div>-->
                <div><span><i class="fa fa-file-text-o"></i> {DATA.file_msc_name}</span></div>

            </div>
        </div>
    </div>
    <!-- END: noi_dung -->
    <h2 class="bidding-sub-title">{LANG.approval_info}</h2>
    <div class="bidding-detail">
        <!-- BEGIN: approval_org -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.approval_org}</div>
            <div class="c-val">{DATA.approval_org}</div>
        </div>
        <!-- END: approval_org -->
        <!-- BEGIN: no_date -->
        <div class="bidding-detail-item col-four">
            <!-- BEGIN: approval_date -->
            <div>
                <div class="c-tit">{LANG.approval_date}</div>
                <div class="c-val">{DATA.approval_date}</div>
            </div>
            <!-- END: approval_date -->
            <!-- BEGIN: no_approval -->
            <div>
                <div class="c-tit">{LANG.no_approval}</div>
                <div class="c-val">{DATA.approval_no}</div>
            </div>
            <!-- END: no_approval -->
        </div>
        <!-- END: no_date -->
        <!-- BEGIN: approval_file -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.quyet_dinh_phe_duyet}</div>
            <div class="c-val">
                <!--<div><a class="list-group-item" href="{LINK_QUYET_DINH_NORMAL}"><span><i class="fa fa-file-text-o"></i> {DATA.approval_filename}</span></a></div>-->
                <div><span><i class="fa fa-file-text-o"></i> {DATA.approval_filename}</span></div>

            </div>
        </div>
        <!-- END: approval_file -->
        <!-- BEGIN: thong_bao_lien_quan -->
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.thong_bao_lien_quan}</div>
            <div class="c-val">
                <!-- BEGIN: loop -->

                <!-- BEGIN: if_a -->
                <p>
                    <label> <input type="checkbox" {RELATE.checked} value="{RELATE.id}" class="chk_notification"> {RELATE.addtime} <span> - </span> <span><a href="{RELATE.link}">{RELATE.code}</a></span>
                    </label> <span>{RELATE.title_first}</span> <span class="info_change_tb" data-tb="{RELATE.key}">{RELATE.text_changetb}</span><span class="link_change_a"> (<a href="javascript:void(0)" data-id="{RELATE.data_id_tbmt}" data-toggle="modal" data-target="#showTB">{LANG.title_change_tb}</a>)
                    </span>
                </p>

                <!-- END: if_a -->

                <!-- BEGIN: not_a -->
                <p>
                    <label> <input type="checkbox" {RELATE.checked} value="{RELATE.id}" class="chk_notification"> {RELATE.addtime} <span> - </span> <span><a href="{RELATE.link}">{RELATE.code}</a></span>
                    </label> <span>{RELATE.title_first}</span> <span class="info_change_tb" data-tb="{RELATE.key}">{RELATE.text_changetb}</span> <span class="link_change_a"> (<a href="javascript:void(0)" data-id="{RELATE.data_id_tbmt}" data-toggle="modal" data-target="#showTB">{LANG.title_change_tb}</a>)
                    </span>
                </p>
                <!-- END: not_a -->
                <!-- END: loop -->
                <span data-toggle="tooltip" data-placement="right" title="{LANG.tooltip_tb}">
                    <button class="btn btn-primary btn-xs" id="view_change_tb" data-toggle="modal" data-target="#showTB">{LANG.title_change_tb}</button>
                    <button class="btn btn-primary btn-xs" id="view_change_tb1">{LANG.title_change_tb}</button>
                </span>
            </div>
        </div>

        <!-- Modal -->
        <div id="showTB" class="modal fade" role="dialog">
            <div class="modal-dialog modal-lg">
                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">{LANG.title_sskhlcmt}</h4>
                    </div>
                    <div class="modal-body">
                        <div id="main_change_notificaion">
                            <div class="change_notificaion">
                                <div class="row">
                                    <div class="col-md-24">
                                        <label>{LANG.kieusosanh}: </label> <label><input type="radio" name="sosanhtb" checked value="1">&nbsp;{LANG.title_muti}</label> <label><input type="radio" name="sosanhtb" value="2">&nbsp;{LANG.title_one}</label>
                                    </div>
                                    <div class="col-md-24">
                                        <div class="card">
                                            <label><input type="checkbox" class="change_by_line" value="3">&nbsp;{LANG.title_linebyline}</label>
                                            <div class="row_compare">
                                                <div class="col">
                                                    <div class="card" id="outputOriginal"></div>
                                                </div>
                                                <div class="col">
                                                    <div class="card" id="output"></div>
                                                </div>
                                                <div class="col" id="new_version">
                                                    <div class="card" id="outputNew"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">{LANG.close}</button>
                    </div>
                </div>

            </div>
        </div>
        <!-- END: thong_bao_lien_quan -->
    </div>

    <!-- BEGIN: contract -->
    <div class="bidding-sub-title display-flex">
        <div>{LANG.list_bidding}:</div>
        <div style="margin-left: auto">{LANG.contracts_number}: {DATA.num_tbmt}</div>
    </div>
    <!-- BEGIN: loop -->
    <div class="bidding-detail">
        <div class="bidding-detail-item">
            <div class="cc-tit">
                <span class="label label-default">{CONTRACT.number}.</span>{CONTRACT.title}
            </div>
        </div>
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tl">{LANG.so_tbmt_mst_dat_tao}</div>
                <div class="c-vl">
                    <!-- BEGIN: yes_link -->
                    <a href="{CONTRACT.so_tbmt_mst_link}"><strong>{CONTRACT.so_tbmt_mst}</strong></a>
                    <!-- END: yes_link -->
                    <!-- BEGIN: no_link -->
                    {CONTRACT.so_tbmt_mst}
                    <!-- END: no_link -->
                </div>
            </div>
            <div>
                <div class="c-tl">{LANG.time_todo}</div>
                <div class="c-vl">{CONTRACT.time_todo}</div>
            </div>
        </div>
        <div class="bidding-detail-item">
            <div class="c-tl">{LANG.is_domestic}</div>
            <div class="c-vl">
                {CONTRACT.name_domestic}
            </div>
        </div>

        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tl">{LANG.field}</div>
                <div class="c-vl">{CONTRACT.linh_vuc}</div>
            </div>
            <div>
                <div class="c-tl">{LANG.process_apply}</div>
                <div class="c-vl">{CONTRACT.process_apply}</div>
            </div>
        </div>
        <!-- BEGIN: price_show -->
        <div class="bidding-detail-item">
            <div class="c-tl">{LANG.price_contract}</div>
            <div class="c-vl price_{CONTRACT.number}" id="price" data-contract="{CONTRACT.number}">
                <!-- BEGIN: link -->
                {view_adv_point}
                <!-- END: link -->
                {PRICE_SHOW}
            </div>
        </div>
        <!-- END: price_show -->
        <!-- BEGIN: est_price_show -->
        <div class="bidding-detail-item">
            <div class="c-tl">{LANG.est_price_contract}</div>
            <div class="c-vl est_price_{CONTRACT.number}" id="est_price" data-contract="{CONTRACT.number}">
                <!-- BEGIN: link -->
                {view_adv_point}
                <!-- END: link -->
                {EST_PRICE_SHOW}
            </div>
        </div>
        <!-- END: est_price_show -->
        <div class="bidding-detail-item">
            <div class="c-tl">{LANG.nguon_von}</div>
            <div class="c-vl">{CONTRACT.owner_equity}</div>
        </div>
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tl">{LANG.type_choose_invest}</div>
                <div class="c-vl">{CONTRACT.type_choose}</div>
            </div>
            <div>
                <div class="c-tl">{LANG.method_bidder}</div>
                <div class="c-vl">{CONTRACT.method_choose}</div>
            </div>
        </div>
        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tl">{LANG.time_choose_invest}</div>
                <div class="c-vl">{CONTRACT.time_choose}</div>
            </div>
            <div>
                <div class="c-tl">{LANG.type_contract}</div>
                <div class="c-vl">{CONTRACT.type_contract}</div>
            </div>
        </div>

        <div class="bidding-detail-item col-four">
            <div>
                <div class="c-tl">{LANG.is_prequalification}</div>
                <div class="c-vl">{CONTRACT.is_prequalification}</div>
            </div>
            <div>
                <div class="c-tl">{LANG.location}</div>
                <div class="c-vl">{CONTRACT.bid_location}</div>
            </div>
        </div>

        <div class="bidding-detail-item">
            <div class="c-tl">{LANG.is_internet}</div>
            <div class="c-vl">{CONTRACT.is_internet}</div>
        </div>

    </div>
    <!-- END: loop -->
    <!-- END: contract -->

    <!-- BEGIN: chance_for_you -->
    <p class="bidding-sub-title">{LANG.chance_for_you}:</p>
    <div class="chance-cont">
        <div class="chance">
            <!-- BEGIN: follow_reg -->
            <div class="panel panel-default">
                <div class="cont panel-heading">
                    <div class="tl">{LANG.follow_plans}</div>
                    <div class="ct">{FOLLOW.content}</div>
                    <div class="bt">
                        <button type="button" class="btn btn-primary" onclick="click_follow('{DATA.link_follow}', '{CHECKSESS}', this, '{PLANCODE}', '{USERID}')">{FOLLOW.title}</button>
                    </div>
                </div>
            </div>
            <!-- END: follow_reg -->
            <!-- BEGIN: receive_email_content -->
            <div class="panel panel-default">
                <div class="cont panel-heading">
                    <div class="tl">{LANG.receive_email}</div>
                    <div class="ct">{RECEIVE_EMAIL.content}</div>
                    <div class="bt">
                        <a class="btn btn-danger" href="{RECEIVE_EMAIL.reg_link}">{RECEIVE_EMAIL.vip_paket}</a>
                    </div>
                </div>
            </div>
            <!-- END: receive_email_content -->
        </div>
    </div>
    <!-- END: chance_for_you -->

    [BLOCK_SUPPORT]

    <div class="margin-top margin-bottom display-flex">
        <div class="h3">
            <span class="label label-primary">{LANG.totalview}: <span class="badge">{DATA.totalview}</span></span>
        </div>
    </div>

    <!-- BEGIN: receive_email_content -->
    <p class="title-bidding">{LANG.receive_email}:</p>
    <div class="bidding-detail">
        <div class="bidding-detail-item">
            <div class="c-tit">{LANG.receive_email_type_registration}</div>
            <div class="c-val">{DATA.receive_email_content}</div>
        </div>
    </div>
    <!-- END: receive_email_content -->
</div>
<br />
<!-- <div class="news_column panel panel-default">
    <div class="panel-body">[FACEBOOK_COMMENT]</div>
</div> -->

<script type="text/javascript">
    $(function() {
        $(".link_change_a:last").hide();
        update_id_checkbox();
        $('[data-toggle="tooltip"]').tooltip();
         // Danh sách thông báo
        DATA_TB = '{DATA_TB}';

        $(".chk_notification").change(function(){
            var numberOfChecked = $('.chk_notification:checkbox:checked').length;

            if (numberOfChecked >= 2) {
                $('.chk_notification:checkbox').not(':checked').prop("disabled", true);
            } else {
                $('.chk_notification:checkbox').not(':checked').prop("disabled", false);
            }
            update_id_checkbox();
        });


        var title = $("#view_change_tb").parent().attr("data-original-title");
        function update_id_checkbox() {
            var $boxes = $('.chk_notification[type=checkbox]:checked');
            arrid = [];
            $boxes.each(function(v, k){
               arrid.push(k.value);
            });

            $("#view_change_tb").val(arrid.join("-"));
            if ($boxes.length < 2) {
                $("#view_change_tb1").removeClass("damdam");
                $("#view_change_tb1").addClass("momo");
                $("#view_change_tb").parent().attr("data-original-title", title);
                $("#view_change_tb1").show();
                $("#view_change_tb").hide();
            } else {
                $("#view_change_tb1").removeClass("momo");
                $("#view_change_tb1").addClass("damdam");
                $("#view_change_tb").parent().attr("data-original-title", "");
                $("#view_change_tb1").hide();
                $("#view_change_tb").show();
            }
        }

        $("#view_change_tb, .link_change_a a").click(function() {
            id = $(this).attr('data-id');
            if (id === undefined) {
                id = $(this).val();
            }
            $.ajax({
                url: location.href,
                type: 'POST',
                data: {
                    'action': 'view_change_tb',
                    'id' : id
                },
                success: function(data) {
                    compare_highlight_difference("outputOriginal", "output", "outputNew", false, JSON.stringify(data['data']['thongbao']), JSON.stringify(data['data']['thongbao1']));
                    tr_0 = $(".table__tb").eq(0).find('tr');
                    tr_1 = $(".table__tb").eq(1).find('tr');
                    for (i = 0; i < tr_0.length; i++) {
                        tr_0.eq(i).attr('data-row', i+1);
                    }

                    for (i = 0; i < tr_1.length; i++) {
                        tr_1.eq(i).attr('data-row', i+1);
                    }

                    bid_0 = $("#outputOriginal").find('.bidding-detail');
                    for (i = 0; i < bid_0.length; i++) {
                        bid_0.eq(i).attr('data-row', i+1);
                    }

                    bid_1 = $("#output").find('.bidding-detail');
                    for (i = 0; i < bid_1.length; i++) {
                        bid_1.eq(i).attr('data-row', i+1);
                    }

                    $(".change_by_line").click(function() {
                        view_change_line();
                    });

                    change_by_line = $(".change_by_line:checked").val();
                    if (change_by_line == '3') {
                        view_change_line();
                    }
                }
            })
        });


        function view_change_line() {
            arr_hide = [];
            arr_hide_gt = [];
            table = $(".change_by_line").closest('#main_change_notificaion').find('#output').find('table');
            del = table.find('tr').find('td');
            for (i = 0; i < del.length; i++) {
                row = del.eq(i).find('del').length;
                if (!row) {
                    row = del.eq(i).find('ins').length;
                }
                if (!row) {
                    data_row = del.eq(i).closest('tr').attr('data-row');
                    arr_hide.push(data_row);
                }
            }

            bidding_detail = $('#output').find(".bidding-detail");

            for (i = 0; i < bidding_detail.length; i++) {
                row = bidding_detail.eq(i).closest('.bidding-detail').find('ins').length;
                if (!row) {
                    row = bidding_detail.eq(i).closest('.bidding-detail').find('del').length;
                }
                if (!row) {
                    data_row = bidding_detail.eq(i).closest('.bidding-detail').attr('data-row');
                    arr_hide_gt.push(data_row);
                }
            }

            if($(".change_by_line").is(':checked')) {
                for (i = 0; i < arr_hide.length; i++) {
                    tr = $(".table__tb").find('tr[data-row="' + arr_hide[i] +'"]');
                    tr.fadeOut(0);
                }

                for (i = 0; i < arr_hide_gt.length; i++) {
                    bid_detail = $(".row_compare").find('.bidding-detail[data-row="' + arr_hide_gt[i] +'"]');
                    bid_detail.fadeOut(0);
                }
            } else {
                for (i = 0; i < arr_hide.length; i++) {
                    tr = $(".table__tb").find('tr[data-row="' + arr_hide[i] +'"]');
                    tr.fadeIn(0);
                }

                for (i = 0; i < arr_hide_gt.length; i++) {
                    bid_detail = $(".row_compare").find('.bidding-detail[data-row="' + arr_hide_gt[i] +'"]');
                    bid_detail.fadeIn(0);
                }
            }
        }

        $("input[name='sosanhtb']").change(function() {
            if ($(this).val() == 2) {
                $("#outputOriginal").parent().hide(500);
                $("#output").parent().show(500);
            } else if ($(this).val() == 3) {
                $("#outputOriginal").parent().hide();
                $("#output").parent().hide();

            } else {
                $("#outputOriginal").parent().show(500);
                $("#output").parent().show(500);
            }
        });
    });
</script>
<script>
var checkess='{NV_CHECK_SESSION}';
const LNG = {read:"{LANG.read}", origlink:"{LANG.origlink}", otherlink:"{LANG.otherlink}", viplink: "{LANG.viplink}"};

$(function() {
    <!-- BEGIN: countdown -->startTimer(300, $(".countdown"));<!-- END: countdown -->
    $(".is_other .is_ie").parents("a").addClass("disable-link");
    link_reformat();
    // $('a[target="_blank"]').click(function() {
    //     if (0 < $(this).attr("href").indexOf("muasamcong.mpi.gov.vn")) {
    //         var b = $(window).width(),
    //             a = $("span", this).is(".is_file"),
    //             c = window.open($(this).attr("href"), a ? "_MyFile" : "_MyWindow", setWinParams(a ? 200 : 500 > b ? b - 50 : b / 2, a ? 200 : 500));
    //         return a && (c.onblur = function() {
    //             return c.close(), !1
    //         }), c.focus(), !1
    //     }
    // })
});

</script>
{FILE "modal_log.tpl"}
<!-- END: main -->
