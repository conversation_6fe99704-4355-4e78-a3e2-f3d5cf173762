<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_BIDDING'))
    die('Stop!!!');

use NukeViet\Dauthau\Url;

isset($l) && $l === 0 && $search_kind = 1; // Kiểu tìm kiếm 1. Khớp từ hoặc một số từ 2. Khớp tất cả từ 0.Khớ<PERSON> chính xác cụm từ
$par_search = $par_search ?? 0; // Chỉ tìm theo tên hoặc mã
$goods_search = $goods_search ?? 0; // Tìm kiếm hàng hóa: 0. Không tìm kiếm theo hàng hóa 1.Tìm kiếm mở rộng vào tên hàng hóa 2.Chỉ tìm kiếm theo tên hàng hóa
$open_only = $open_only ?? 0; // Chỉ tìm kiếm TBMT chưa đóng thầu
$search_elastic = [];

if ($type_search == 2) { // TBMĐT chỉ có 1 kiểu search theo content và lọc dấu
    $search_fields = [
        'content'
    ];
} else {
    if ($search_type_content == 1) {
        if ($par_search) {
            $search_fields = [
                'so_tbmt',
                'goi_thau'
            ];
        } else {
            $search_fields = [
                'content_full'
            ];
        }
    } else {
        if ($par_search) {
            $search_fields = [
                'so_tbmt',
                'goi_thau_search'
            ];
        } else {
            $search_fields = [
                'content'
            ];
        }
    }
    if ($goods_search == 1) {
        $search_fields[] = 'content_goods';
    } elseif ($goods_search == 2) {
        $search_fields = [
            'content_goods'
        ];
    }
}

$where = 'ngay_dang_tai>=' . $sfrom1 . ' AND ngay_dang_tai<=' . $sto1;

if ($cat > 0 and $is_advance) {
    $where .= $cat == 1 ? " AND type_bid=1" : " AND type_bid=0";
}

if ($type_org == 2 and $is_advance) {
    $where .= " AND type_org=1";
}

if ($solicitor_id > 0) {
    $where .= " AND solicitor_id=" . $solicitor_id;
}

// tìm theo số tiền đảm bảo
if ($money_from > 0 and $is_advance) {
    $where .= " AND money_bid >= " . $money_from;
}
if ($money_to > 0 and $is_advance) {
    $where .= " AND money_bid <= " . $money_to;
}

if ($price_from > 0 and $is_advance) {
    // tìm theo giá mời thầu
    $where .= " AND price >= " . $price_from;
}
if ($price_to > 0 and $is_advance) {
    // tìm theo giá mời thầu
    $where .= " AND price <= " . $price_to;
}

// Tìm kiếm theo so_tbmt
if (!empty($code)) {
    $where .= ' AND (';
    $num = 0;
    $arr_code = explode(',', $code);
    foreach ($arr_code as $key) {
        $key = explode('-', $key)[0];
        $key = trim($key);
        if ($num == 0) {
            $where .= "so_tbmt LIKE " . $db->quote($key . '%');
        } else {
            $where .= " OR so_tbmt LIKE " . $db->quote($key . '%');
        }
        $num++;
    }
    $where .= ')';
}

if (defined("NV_IS_VIP5")) {
    if ($type_search != 2) {
        $where .= " AND is_vip5 = 1";
    }
    if (!empty($field) and $is_advance) {
        if ($key = array_search('20', $field)) {
            unset($field[$key]);
        }
        $field_search = implode(',', $field);
        $where .= " AND linh_vuc_thong_bao IN (" . $field_search . ")";
    }
    $where .= " AND pham_vi = 1 ";
} else {
    if (!empty($field) and $is_advance) {
        if ($key = array_search('20', $field)) {
            unset($field[$key]);
        }
        $field_search = implode(',', $field);
        $where .= " AND linh_vuc_thong_bao IN (" . $field_search . ")";
    } 
    if (defined('NV_IS_USER') and ($vip == 1 || $vip == 19 || $vip == 99) && $type_search != 2) {
        $where .= " AND pham_vi != 1 ";
    }
}

if (!empty($phuongthuc) and $is_advance and $type_search != 2) {
    $pts = implode(',', $phuongthuc);
    $where .= " AND (phuong_thuc_num IN (" . $pts . "))";
}

if ($id_hinhthucluachon == 2 and $is_advance) {
    $where .= " AND (id_hinhthucluachon = 0 OR id_hinhthucluachon = " . $id_hinhthucluachon . ")";
}

if (!empty($without_key) and $is_advance) {
    // Tìm theo từ khóa loại trừ
    $arr_key = explode(',', $without_key);
    foreach ($search_fields as $f) {
        foreach ($arr_key as $key) {
            if (empty($key)) {
                continue;
            }
            if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                continue;
            }

            if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                continue;
            }
            if ($search_type_content == 1 and $type_search == 1) {
                $key = trim($key);
                $where .= " AND " . $f . " COLLATE utf8mb4_unicode_ci NOT LIKE " . $db->quote('%' . $key . '%') . '';
            } else {
                $f != 'content_goods' && $type_search != 2 && $key = str_replace('-', ' ', change_alias($key));
                $f == 'content_goods' && $key = strtolower($key);
                $key = trim($key);
                $where .= " AND " . $f . " COLLATE utf8mb4_unicode_ci NOT LIKE " . $db->quote('%' . $key . '%') . '';
            }
        }
    }
}

if ($open_only) {
    $where .= ' AND den_ngay >= ' . NV_CURRENTTIME;
}

// Tìm kiếm theo phân mục
if (!empty($_phanmucid) and $is_advance && $type_search != 2) {
    if ($array_op[1] == $nv_Lang->getModule('alias_chua_phan_loai') || $_phanmucid[0] == 0) {
        $where .= " AND phanmuc = '' ";
    } else {
        $where .= ' AND (';
        $num = 0;
        foreach ($_phanmucid as $value) {
            if ($num == 0) {
                $where .= ' FIND_IN_SET(' . $value . ', phanmuc)';
            } else {
                $where .= ' OR FIND_IN_SET(' . $value . ', phanmuc)';
            }
            ++$num;
        }
        $where .= ')';
    }
}

// Tìm kiếm theo tỉnh thành
if (!empty($_idprovince) and $is_advance && $type_search != 2) {
    if ($_idprovince[0] == 0) {
        $where .= ' AND ( province_id = 0)';
    } else {
        $find_province = [];
        foreach ($_idprovince as $v) {
            if ($v != 0) {
                // Properly quoting the $v value using the database's quote method
                $quoted_value = $db->quote($v);
                $find_province[] = "FIND_IN_SET(" . $quoted_value . ", province_id)";
            }
        }

        if (!empty($find_province)) {
            $where .= " AND (" . implode(' OR ', $find_province) . ")";
        }
    }
}
// Tìm kiếm theo Loại công trình
if (!empty($_idlct) and $is_advance && $type_search != 2) {
    if ($_idlct[0] == 0) {
        $where .= ' AND ( work_type = 0)';
    } else {
        $where .= ' AND ( work_type IN (' . implode(',', $_idlct) . '))';
    }
}

if (((!empty($type_choose_id) and $is_advance) or (!empty($type_choose_id) and $type_choose_id == 42)) and $type_search == 1) {
    $where .= " AND type_choose_id = '" . $type_choose_id . "'";
}

if (!empty($key_search2)) {
    // Tìm theo Từ khóa bổ sung
    $arr_key = explode(',', $key_search2);
    if (sizeof($search_fields) > 1) {
        // Nếu số lượng trường tìm kiếm > 1 thì sẽ dùng bool must từng từ khóa
        // mỗi từ khóa thì lại should trong từng trường
        $search_bosung = [];
        foreach ($arr_key as $key) {
            if (empty($key)) {
                continue;
            }
            $search_bosung_should = [];
            foreach ($search_fields as $f) {
                if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                    continue;
                }
                if ($search_type_content == 1) {
                    $key = trim($key);
                    $search_bosung_should[] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                    $search_bosung[] = " " . $f . " COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . $key . '%') . "";
                } else {
                    $search_bosung[] = " " . $f . " COLLATE utf8mb4_unicode_ci LIKE " . $db->quote('%' . ($f != 'content_goods' ? trim(str_replace('-', ' ', change_alias($key))) : trim(strtolower($key))) . '%') . "";
                }
            }
        }
        $where .= ' AND (' . implode(' OR ', $search_bosung) . ')';
    } else {
        $_where_1 = [];
        foreach ($search_fields as $f) {
            foreach ($arr_key as $key) {
                if (empty($key)) {
                    continue;
                }
                if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                    continue;
                }
                if (!empty($search_one_key)) {
                    if ($search_type_content == 1) {
                        $key = trim($key);
                        $_where_1[] = $f . " LIKE " . $db->quote('%' . $key . '%');
                    } else {
                        ($f != 'content_goods' && $type_search != 2) && $key = str_replace('-', ' ', change_alias($key));
                        $f == 'content_goods' && $key = strtolower($key);
                        $key = trim($key);
                        $_where_1[] = $f . " LIKE " . $db->quote('%' . $key . '%');
                    }
                } else {
                    if ($search_type_content == 1) {
                        $key = trim($key);
                        $where .= " AND " . $f . " LIKE " . $db->quote('%' . $key . '%');
                    } else {
                        ($f != 'content_goods' && $type_search != 2) && $key = str_replace('-', ' ', change_alias($key));
                        $f == 'content_goods' && $key = strtolower($key);
                        $key = trim($key);
                        $where .= " AND " . $f . " LIKE " . $db->quote('%' . $key . '%');
                    }
                }
            }
        }
        if (!empty($_where_1)) {
            $where .= ' AND (' . implode(' OR ', $_where_1) . ')';
        }
    }
}
if (!empty($q)) {
    $arr_key = explode(',', $q);
    $search_q = [];
    foreach ($search_fields as $f) {
        foreach ($arr_key as $key) {
            if ($search_kind > 1) { // 1. Khớp từ hoặc một số từ 2. Khớp tất cả từ
                $arr_key_temp = explode(' ', $key);
                $tmp_search_elastic = [];
                foreach ($arr_key_temp as $_key_temp) {
                    if ($search_type_content == 0 && $f != 'content_goods' && $type_search != 2) {
                        $_key_temp = str_replace('-', ' ', change_alias($_key_temp));
                    } else if ($search_type_content == 0 && $f == 'content_goods') {
                        $_key_temp = strtolower($_key_temp);
                    }
                    $_key_temp = trim($_key_temp);
                    if (empty($_key_temp)) {
                        continue;
                    }
                    if (!preg_match('/[0-9]/', $_key_temp) && $f == 'so_tbmt') {
                        continue;
                    }

                    if ($search_kind == 1) {
                        $search_q[] = $f . ' LIKE ' . $db->quote('%' . $_key_temp . '%');
                    } else {
                        $tmp_search_elastic[] = $f . ' LIKE ' . $db->quote('%' . $_key_temp . '%');
                    }
                }
                $search_q[] = '(' . implode(' AND ', $tmp_search_elastic) . ')';
            } else { // 0.Khớp chính xác cụm từ
                if ($search_type_content == 0 && $f != 'content_goods' && $type_search != 2) {
                    $key = str_replace('-', ' ', change_alias($key));
                } else if ($search_type_content == 0 && $f == 'content_goods') {
                    $key = strtolower($key);
                }
                $key = trim($key);
                if (empty($key)) {
                    continue;
                }
                if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                    continue;
                }
                $search_q[] = $f . ' LIKE ' . $db->quote('%' . $key . '%');
            }
        }
    }
    $where .= ' AND (' . implode(' OR ', $search_q) . ')';
}

if ($khlcnt != '') {
    $arr_id = array();

    $db->sqlreset()
        ->select('id')
        ->from(NV_PREFIXLANG . '_' . $module_data . '_detail')
        ->where('khlcnt_code=' . $db->quote($khlcnt));
    $sth = $db->prepare($db->sql());
    $sth->execute();
    while ($bid = $sth->fetch()) {
        $arr_id[] = $bid['id'];
    }

    if (!empty($arr_id)) {
        $arr_id = implode(',', $arr_id);
        $where = "id IN (" . $db->quote($arr_id) . ")";
    }
}

// gói vip 4
if ($type_search == 2) {
    $db->sqlreset()
        ->select('COUNT(id)')
        ->from(NV_PREFIXLANG . '_' . $module_data . '_project_row')
        ->where($where);
    $sth = $db->prepare($db->sql());
} else {
    $db->sqlreset()
        ->select('COUNT(id)')
        ->from(NV_PREFIXLANG . '_' . $module_data . '_row')
        ->where($where);
    $sth = $db->prepare($db->sql());
}

$sth->execute();
$num_items = $sth->fetchColumn();

$sl_alias = $type_search == 2 ? '' : ', alias';

$db->select('*' . $sl_alias)
    ->order('ngay_dang_tai DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());
$sth->execute();

while ($view = $sth->fetch()) {
    $view['den_ngay'] = $view['den_ngay'] > 0 ? $TBMT::time_display($view['den_ngay']) : '';
    $view['get_time'] = $view['get_time'] > 0 ? $TBMT::time_display($view['get_time']) : '';
    $view['ngay_dang_tai'] = $view['ngay_dang_tai'] ? $TBMT::time_display($view['ngay_dang_tai']) : '';

    if ($type_search == 2) {
        $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $global_config['module_bidding_name'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['view'] . '/' . Url::getTBMDT() . '/' . strtolower(change_alias($view['goi_thau'])) . '-' . $view['id'] . $global_config['rewrite_exturl'];
    } else {
        $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $global_config['module_bidding_name'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['view'] . '/' . Url::getTBMT() . '/' . $view['alias'] . '-' . $view['id'] . $global_config['rewrite_exturl'];
    }

    $view['link_search'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $global_config['module_bidding_name'] . '&amp;' . NV_OP_VARIABLE . '=detail&solicitor_id=' . $view['solicitor_id'];

    $array_data[$view['id']] = $view;
    $arr_solicitor_id[$view['solicitor_id']] = $view['solicitor_id'];
}

$_t = round((microtime(true) - NV_START_TIME), 2);
if ($_t > 1) {
    $_info = [];
    $_info['user_agent'] = NV_USER_AGENT;
    $_info['ip'] = $client_info['ip'];
    $_info['country'] = $client_info['country'];
    $_info['url'] = $client_info['selfurl'];
    $_info['time'] = date('H:i:s');
    if (defined('NV_IS_USER')) {
        $_info['userid'] = $user_info['userid'];
    }
    $_filename = 'log_type_search_2.txt';
    file_put_contents(NV_ROOTDIR . '/tmp/' . $_filename, print_r($_info, true) . "\n", FILE_APPEND);
}
