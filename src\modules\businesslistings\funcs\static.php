<?php

/**
 * @Project NUKEVIET 4.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Wed, 10 Apr 2019 09:04:00 GMT
 */
if (!defined('NV_IS_MOD_BUSINESSLISTINGS'))
    die('Stop!!!');

$key_words = $module_info['keywords'];

$array_order = array();
$array_order[0] = $nv_Lang->getModule('select_total');
for ($i = nv_date('Y', NV_CURRENTTIME); $i >= 2010; $i--) {
    $array_order[$i] = $nv_Lang->getModule('year') . ' ' . $i;
}

$link = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=detail";
$array_data = array();

$order = $nv_Request->get_int('order', 'post, get', 0);
$select_top = 0;
if (empty($array_op[1])) {
    $select_top = -1;
    $page_url = $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['static'];
    $base_url = nv_url_rewrite($base_url, true);
    $canonicalUrl = getCanonicalUrl($page_url);
    $array_data = [];
    if ($config_bidding['elas_use']) {
        $nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($config_bidding['elas_host'], $config_bidding['elas_port'], 'dauthau_businesslistings', $config_bidding['elas_user'], $config_bidding['elas_pass']);

        // Xử lý các biểu đồ thông thường (dự thầu, trúng thầu, trượt thầu, năng lực)
        foreach (['duthau' => 'num_total', 'trungthau' => 'num_result', 'truotthau' => 'num_false', 'nangluc' => 'ability_point'] as $key => $field) {
            $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_info', ['track_total_hits' => 'true', 'size' => 10, 'sort' => [[$field => ['order' => 'desc']]]]);

            $_arr_bussiness_id = [];
            $_arr_bussiness = [];
            foreach ($response['hits']['hits'] as $value) {
                if (!empty($value['_source'])) {
                    $view = $value['_source'];
                    $_arr_bussiness_id[] = $view['id'];
                    $_arr_bussiness[$view['id']] = $view;
                    $_arr_bussiness[$view['id']]['num'] = floatval($view[$field]);
                }
            }

            if (!empty($_arr_bussiness_id)) {
                $sql = "SELECT id, officialname, companyname FROM " . BUSINESS_PREFIX_GLOBAL . "_info WHERE active = 1 AND id IN (" . implode(',', $_arr_bussiness_id) . ")";
                $result = $db->query($sql);
                while ($row = $result->fetch()) {
                    $view = $_arr_bussiness[$row['id']];
                    $view['companyname'] = (NV_LANG_DATA != 'vi' && !empty(trim($row['officialname']))) ? $row['officialname'] : $row['companyname'];
                    $view['link'] = $link . "/" . change_alias($view['companyname']) . "-" . $view['id'];
                    $view['num'] = floatval($view[$field]);
                    $array_data[$key][] = $view;
                }
                usort($array_data[$key], function($a, $b) {
                    return (float)$b['num'] <=> (float)$a['num'];
                });
            }
        }

        // Xử lý biểu đồ doanh số
        $script = "(doc['total_win_inde'].value + doc['total_win_partner'].value)";
        $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_info', [
            'track_total_hits' => 'true',
            'size' => 10,
            'sort' => [['_script' => ['type' => 'number', 'script' => ['source' => $script], 'order' => 'desc']]]
        ]);

        $_arr_bussiness = [];
        foreach ($response['hits']['hits'] as $value) {
            if (!empty($value['_source'])) {
                $view = $value['_source'];
                $view['companyname'] = (NV_LANG_DATA != 'vi' && !empty(trim($view['officialname']))) ? $view['officialname'] : $view['companyname'];
                $view['link'] = $link . "/" . change_alias($view['companyname']) . "-" . $view['id'];

                $total_win_inde = isset($view['total_win_inde']) ? floatval($view['total_win_inde']) : 0;
                $total_win_partner = isset($view['total_win_partner']) ? floatval($view['total_win_partner']) : 0;
                $total_chidinhthau = isset($view['total_chidinhthau']) ? floatval($view['total_chidinhthau']) : 0;

                $array_data['doclap'][] = [
                    'companyname' => $view['companyname'],
                    'link' => $view['link'],
                    'num' => $total_win_inde,
                    'id' => $view['id']
                ];

                $array_data['liendanh'][] = [
                    'companyname' => $view['companyname'],
                    'link' => $view['link'],
                    'num' => $total_win_partner,
                    'id' => $view['id']
                ];

                $array_data['chidinhthau'][] = [
                    'companyname' => $view['companyname'],
                    'link' => $view['link'],
                    'num' => $total_chidinhthau,
                    'id' => $view['id']
                ];

                $array_data['hinhthuckhac'][] = [
                    'companyname' => $view['companyname'],
                    'link' => $view['link'],
                    'num' => $total_win_inde + $total_win_partner - $total_chidinhthau,
                    'id' => $view['id']
                ];
            }
        }
    }
} elseif (!empty($array_op[1]) && $array_op[1] == 'vnr500') {
    $page_url = $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['static'] . '/vnr500';
    $base_url = nv_url_rewrite($base_url, true);
    $canonicalUrl = getCanonicalUrl($page_url);
} else {
    $static_aliases = array();
    for ($i = 0; $i <= 4; $i++) {
        $alias = $nv_Lang->getModule('static_type_' . $i);
        $static_aliases[NV_LANG_DATA][$i] = $alias;
    }

    if (preg_match('/^([A-Za-z0-9\-]+)$/', $array_op[1], $m)) {
        $url_op = $m[1];
        $alias_map = ['duthau' => 'bidding', 'bidding' => 'duthau', 'trungthau' => 'winning', 'winning' => 'trungthau', 'truotthau' => 'failed', 'failed' => 'truotthau', 'nangluc' => 'ability', 'ability' => 'nangluc', 'doanhso' => 'revenue', 'revenue' => 'doanhso'];
        $wrong_alias = (NV_LANG_DATA == 'vi' && in_array($url_op, ['bidding', 'winning', 'failed', 'ability', 'revenue'])) || (NV_LANG_DATA == 'en' && in_array($url_op, ['duthau', 'trungthau', 'truotthau', 'nangluc', 'doanhso']));

        if ($wrong_alias && isset($alias_map[$url_op])) {
            $redirect_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['static'] . '/' . $alias_map[$url_op] . $global_config['rewrite_exturl'];
            nv_redirect_location(nv_url_rewrite($redirect_url, true));
        }
        $select_top = array_search($url_op, $static_aliases[NV_LANG_DATA]);
        if ($select_top === false) {
            nv_redirect_location($base_url);
        }
        $url_alias = $static_aliases[NV_LANG_DATA][$select_top];
        $page_url = $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['static'] . '/' . $url_alias . $global_config['rewrite_exturl'];
        $base_url = nv_url_rewrite($base_url, true);

        if (!empty($other_lang)) {
            $alias_map = ['duthau' => 'bidding', 'bidding' => 'duthau', 'trungthau' => 'winning', 'winning' => 'trungthau', 'truotthau' => 'failed', 'failed' => 'truotthau', 'nangluc' => 'ability', 'ability' => 'nangluc', 'doanhso' => 'revenue', 'revenue' => 'doanhso'];
            $other_alias = isset($alias_map[$static_aliases[NV_LANG_DATA][$select_top]]) ? $alias_map[$static_aliases[NV_LANG_DATA][$select_top]] : $static_aliases[NV_LANG_DATA][$select_top];
            $global_lang_url = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['static'] . '/' . $other_alias . $global_config['rewrite_exturl'], true);
        }

        $canonicalUrl = getCanonicalUrl($page_url);

        $cacheTTL = ($order == 0 or $order == date('Y', NV_CURRENTTIME)) ? 12 * 3600 : 48 * 3600;
        $cacheFile = NV_LANG_DATA . '_' . $op . '_' . $select_top . '_' . $order . '_' . NV_CACHE_PREFIX . '.cache';

        if (($cache = $nv_Cache->getItem($module_name, $cacheFile, $cacheTTL)) != false) {
            $array_data = unserialize($cache);
        } else {
            $_arr_bussiness_id = $_arr_bussiness = [];

            if ($order != 0 && $select_top != 3 && $select_top != 4) {
                $select_top_sql = ($select_top == 1 ? 'SUM(total_bid_result)' : ($select_top == 2 ? 'SUM(total_bid_false)' : 'SUM(total_bid)'));
                $sql = 'SELECT bussiness_id, ' . $select_top_sql . ' AS num FROM ' . BUSINESS_PREFIX_GLOBAL . '_static WHERE year = ' . $order . ' GROUP BY bussiness_id ORDER BY `num` DESC LIMIT 10';
                $_query = $db->query($sql);
                while ($_row = $_query->fetch()) {
                    $_arr_bussiness_id[] = $_row['bussiness_id'];
                    $_arr_bussiness[$_row['bussiness_id']]['num'] = $_row['num'];
                }
            } elseif ($config_bidding['elas_use']) {
                $nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($config_bidding['elas_host'], $config_bidding['elas_port'], 'dauthau_businesslistings', $config_bidding['elas_user'], $config_bidding['elas_pass']);

                if ($select_top == 4) {
                    $script = "(doc['total_win_inde'].value + doc['total_win_partner'].value)";
                    $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_info', [
                        'track_total_hits' => 'true',
                        'size' => 10,
                        'sort' => [['_script' => ['type' => 'number', 'script' => ['source' => $script], 'order' => 'desc']]]
                    ]);

                    $_arr_bussiness = [];
                    foreach ($response['hits']['hits'] as $value) {
                        if (!empty($value['_source'])) {
                            $view = $value['_source'];
                            $_arr_bussiness_id[] = $view['id'];
                            $total_win_inde = isset($view['total_win_inde']) ? floatval($view['total_win_inde']) : 0;
                            $total_win_partner = isset($view['total_win_partner']) ? floatval($view['total_win_partner']) : 0;
                            $total_chidinhthau = isset($view['total_chidinhthau']) ? floatval($view['total_chidinhthau']) : 0;

                            $_arr_bussiness[$view['id']] = [
                                'doclap' => ['num' => $total_win_inde],
                                'liendanh' => ['num' => $total_win_partner],
                                'chidinhthau' => ['num' => $total_chidinhthau],
                                'hinhthuckhac' => ['num' => $total_win_inde + $total_win_partner - $total_chidinhthau],
                                'total' => $total_win_inde + $total_win_partner
                            ];
                        }
                    }

                    if (!empty($_arr_bussiness_id)) {
                        $sql = "SELECT id, officialname, companyname FROM " . BUSINESS_PREFIX_GLOBAL . "_info WHERE active = 1 AND id IN (" . implode(',', $_arr_bussiness_id) . ")";
                        $_query = $db->query($sql);
                        while ($_row = $_query->fetch()) {
                            (NV_LANG_DATA != 'vi' && !empty(trim($_row['officialname']))) && $_row['companyname'] = $_row['officialname'];
                            $_row['link'] = $link . "/" . change_alias($_row['companyname']) . "-" . $_row['id'];

                            // Lưu thông tin hiển thị biểu đồ
                            foreach(['doclap', 'liendanh', 'chidinhthau', 'hinhthuckhac'] as $type) {
                                $array_data[$type][$_row['id']]['companyname'] = $_row['companyname'];
                                $array_data[$type][$_row['id']]['link'] = $_row['link'];
                                $array_data[$type][$_row['id']]['num'] = $_arr_bussiness[$_row['id']][$type]['num'];
                            }

                            $array_data['table'][$_row['id']] = [
                                'companyname' => $_row['companyname'],
                                'link' => $_row['link'],
                                'num' => $_arr_bussiness[$_row['id']]['total']
                            ];
                        }

                        uasort($array_data['table'], function($a, $b) {
                            return (float)$b['num'] <=> (float)$a['num'];
                        });

                        foreach($array_data['table'] as &$item) {
                            $item['num'] = nv_number_format($item['num']);
                        }
                    }
                } else {
                    $_field = $select_top == 3 ? 'ability_point' : ($select_top == 1 ? 'num_result' : ($select_top == 2 ? 'num_false' : 'num_total'));
                    $array_query_elastic = [
                        'track_total_hits' => 'true',
                        'size' => 10,
                        'sort' => [[$_field => ['order' => 'desc']]]
                    ];

                    $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_info', $array_query_elastic);
                    foreach ($response['hits']['hits'] as $value) {
                        if (!empty($value['_source'])) {
                            $view = $value['_source'];
                            $_arr_bussiness_id[] = $view['id'];
                            $_arr_bussiness[$view['id']]['num'] = $view[$_field];
                        }
                    }
                }
            }
            if (!empty($_arr_bussiness_id)) {
                $sql = "SELECT id, officialname, companyname FROM " . BUSINESS_PREFIX_GLOBAL . "_info WHERE active = 1 AND id IN (" . implode(',', $_arr_bussiness_id) . ")";
                $_query = $db->query($sql);
                while ($_row = $_query->fetch()) {
                    (NV_LANG_DATA != 'vi' && !empty(trim($_row['officialname']))) && $_row['companyname'] = $_row['officialname'];
                    $_row['link'] = $link . "/" . change_alias($_row['companyname']) . "-" . $_row['id'];

                    if ($select_top == 4) {
                        foreach(['doclap', 'liendanh', 'chidinhthau', 'hinhthuckhac'] as $type) {
                            if (isset($_arr_bussiness[$_row['id']][$type])) {
                                $array_data[$type][$_row['id']]['companyname'] = $_row['companyname'];
                                $array_data[$type][$_row['id']]['link'] = $_row['link'];
                                $array_data[$type][$_row['id']]['num'] = $_arr_bussiness[$_row['id']][$type]['num'];
                            }
                        }

                        $array_data['table'][$_row['id']] = [
                            'companyname' => $_row['companyname'],
                            'link' => $_row['link'],
                            'num' => $_arr_bussiness[$_row['id']]['total']
                        ];
                    } else {
                        $array_data[$_row['id']]['companyname'] = $_row['companyname'];
                        $array_data[$_row['id']]['link'] = $_row['link'];
                        $array_data[$_row['id']]['num'] = $_arr_bussiness[$_row['id']]['num'];
                    }
                }

                if ($select_top == 4) {
                    foreach ($array_data as $type => $data) {
                        usort($array_data[$type], function($a, $b) {
                            return (float)$b['num'] <=> (float)$a['num'];
                        });
                    }
                } else {
                    usort($array_data, function($a, $b) {
                        return (float)$b['num'] <=> (float)$a['num'];
                    });
                }
            }

            $cache = serialize($array_data);
            $nv_Cache->setItem($module_name, $cacheFile, $cache, $cacheTTL);
        }
    }
}

// Biểu đồ xếp hạng VNR_TOP10 của VNR500
$array_data['vnr_max_year'] = $db->query('SELECT MAX(year) as year FROM ' . BUSINESS_PREFIX_GLOBAL . '_vnr_rank WHERE rank <= 10')->fetch();
$array_data['vnr_years'] = [];
for ($year = $array_data['vnr_max_year']['year']; $year >= 2007; $year--) {
    $array_data['vnr_years'][] = $year;
}

$array_data['vnr_selected_year'] = $nv_Request->get_int('year', 'post', $array_data['vnr_max_year']['year']);
$array_data['vnr_selected_type'] = $nv_Request->get_int('type', 'post', 1);

$sql_rank = 'SELECT vnr_id, rank, year FROM ' . BUSINESS_PREFIX_GLOBAL . '_vnr_rank WHERE year = ' . $array_data['vnr_selected_year'] . ' AND rank <= 10 AND type = ' . $array_data['vnr_selected_type'] . ' ORDER BY rank ASC';
$result_rank = $db->query($sql_rank);
$array_data['rank_data'] = [];
while ($row = $result_rank->fetch()) {
    $array_data['rank_data'][$row['vnr_id']] = $row;
}

$array_data['company_data'] = [];
if (!empty($array_data['rank_data'])) {
    // Lấy danh sách taxcode từ bảng vnr
    $sql_company = 'SELECT vnr_id, name as company_name, tax_code FROM ' . BUSINESS_PREFIX_GLOBAL . '_vnr WHERE vnr_id IN (' . implode(',', array_keys($array_data['rank_data'])) . ')';
    $result_company = $db->query($sql_company);
    $array_taxcodes = [];
    $array_vnr_taxcode = [];
    while ($row = $result_company->fetch()) {
        $array_taxcodes[] = $db->quote($row['tax_code']);
        $array_vnr_taxcode[$row['vnr_id']] = [
            'tax_code' => $row['tax_code'],
            'company_name' => $row['company_name']
        ];
    }

    if (!empty($array_taxcodes)) {
        $sql_info = 'SELECT id, companyname, code FROM ' . BUSINESS_PREFIX_GLOBAL . '_info WHERE code IN (' . implode(',', $array_taxcodes) . ')';
        $result_info = $db->query($sql_info);
        $array_company_info = [];
        while ($row = $result_info->fetch()) {
            $array_company_info[$row['code']] = $row;
        }

        foreach ($array_vnr_taxcode as $vnr_id => $data) {
            if (isset($array_company_info[$data['tax_code']])) {
                $array_data['company_data'][$vnr_id] = [
                    'company_name' => $array_company_info[$data['tax_code']]['companyname'],
                    'company_id' => $array_company_info[$data['tax_code']]['id']
                ];
            } else {
                // Sử dụng tên công ty từ bảng vnr nếu không tìm thấy trong bảng info
                $array_data['company_data'][$vnr_id] = [
                    'company_name' => $data['company_name'],
                    'company_id' => 0
                ];
            }
        }
    }
}

$array_data['chart_data'] = [];
if (!empty($array_data['company_data'])) {
    $sql = 'SELECT vnr_id, year, rank FROM ' . BUSINESS_PREFIX_GLOBAL . '_vnr_rank WHERE vnr_id IN (' . implode(',', array_keys($array_data['rank_data'])) . ') AND type = ' . $array_data['vnr_selected_type'] . ' AND rank <= 10 ORDER BY year ASC, rank ASC';
    $result = $db->query($sql);
    $array_data['rankings'] = [];
    $array_data['years'] = [];

    while ($row = $result->fetch()) {
        $year = intval($row['year']);
        $vnr_id = $row['vnr_id'];

        if (!isset($array_data['rankings'][$year])) {
            $array_data['rankings'][$year] = [];
            $array_data['years'][] = $year;
        }

        $array_data['rankings'][$year][$vnr_id] = intval($row['rank']);
    }

    foreach ($array_data['company_data'] as $vnr_id => $company) {
        $array_data['series_data'] = [];
        foreach ($array_data['vnr_years'] as $year) {
            $array_data['series_data'][] = [
                'x' => $year,
                'y' => isset($array_data['rankings'][$year][$vnr_id]) ? intval($array_data['rankings'][$year][$vnr_id]) : null
            ];
        }

        $array_data['chart_data'][] = [
            'name' => $company['company_name'],
            'data' => $array_data['series_data'],
            'link' => !empty($company['company_id']) ? (NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail'] . '/' . strtoupper(change_alias($company['company_name'])) . '-' . $company['company_id']) : ''
        ];
    }
}

if (!empty($array_data['chart_data'])) {
    foreach ($array_data['chart_data'] as &$series) {
        $series['data'] = array_reverse($series['data']);
    }
}

// chú giải
if (!empty($array_data['company_data'])) {
    $sql = 'SELECT vnr_id, COUNT(*) as total_top10 FROM ' . BUSINESS_PREFIX_GLOBAL . '_vnr_rank WHERE vnr_id IN (' . implode(',', array_keys($array_data['rank_data'])) . ') AND type = ' . $array_data['vnr_selected_type'] . ' AND rank <= 10 GROUP BY vnr_id';
    $result = $db->query($sql);
    $total_top10_data = [];
    while ($row = $result->fetch()) {
        $total_top10_data[$row['vnr_id']] = $row['total_top10'];
    }

    $array_data['legend'] = [];
    foreach ($array_data['rank_data'] as $vnr_id => $rank_info) {
        if (isset($array_data['company_data'][$vnr_id])) {
            $array_data['legend'][] = [
                'companyname' => $array_data['company_data'][$vnr_id]['company_name'],
                'rank' => $rank_info['rank'],
                'total_top10' => $total_top10_data[$vnr_id] ?? 0,
                'link_company' => !empty($array_data['company_data'][$vnr_id]['company_id']) ? (NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail'] . '/' . strtoupper(change_alias($array_data['company_data'][$vnr_id]['company_name'])) . '-' . $array_data['company_data'][$vnr_id]['company_id']) : ''
            ];
        }
    }

    // Sắp xếp theo thứ hạng của năm được chọn (từ hạng 1 đến hạng 10)
    $rank = array_column($array_data['legend'], 'rank');
    array_multisort($rank, SORT_ASC, $array_data['legend']);
}

if ($nv_Request->isset_request('vnr_ajax', 'post')) {
    $legend_html = '';
    if (!empty($array_op[1]) && $array_op[1] == 'vnr500') {
        $legend_html = nv_theme_businesslistings_legend($array_data);
    }
    nv_jsonOutput([
        'status' => 'success',
        'current_year' => $array_data['vnr_selected_year'],
        'max_year' => $array_data['vnr_max_year']['year'],
        'selected_type' => $array_data['vnr_selected_type'],
        'chart_data' => $array_data['chart_data'],
        'legend_html' => $legend_html
    ]);
}

$ajax = $nv_Request->get_int('ajax', 'post, get', 0);
$contents = nv_theme_businesslistings_static($array_data, $array_order, $select_top, $ajax, $order);

if (!empty($array_op[1]) && $array_op[1] == 'vnr500') {
    $page_title = $nv_Lang->getModule('vnr_top10_detail_title');
} else {
    $page_title = ($select_top == 1 ? $nv_Lang->getModule('static_num_result') : ($select_top == 2 ? $nv_Lang->getModule('static_num_false') : ($select_top == 3 ? $nv_Lang->getModule('static_num_capability'): ($select_top == 4 ? $nv_Lang->getModule('static_num_revenue') : ($select_top == -1 ? $nv_Lang->getModule('static_title_all') : $nv_Lang->getModule('static_num_total'))))));
}

if ($nv_Request->isset_request('ajax', 'post, get')) {
    nv_htmlOutput(nv_url_rewrite($contents));
}

// Breadcrumbs
$array_mod_title[] = [
    'title' => $nv_Lang->getModule('static_title_all'),
    'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['static']
];

if (!empty($array_op[1]) && $array_op[1] != 'vnr500') {
    $array_mod_title[] = [
        'title' => $page_title,
        'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['static'] . '/' . $static_aliases[NV_LANG_DATA][$select_top]
    ];
} elseif (!empty($array_op[1]) && $array_op[1] == 'vnr500') {
    $array_mod_title[] = [
        'title' => $page_title,
        'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['static'] . '/vnr500'
    ];
}
include (NV_ROOTDIR . "/includes/header.php");
echo nv_site_theme($contents);
include (NV_ROOTDIR . "/includes/footer.php");
