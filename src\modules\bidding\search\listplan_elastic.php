<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_BIDDING'))
    die('Stop!!!');

use NukeViet\Dauthau\Url;

// kết nối tới ElasticSearh
$nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_host'], $module_config[$module_name]['elas_port'], NV_LANG_ELASTIC . 'dauthau_plans', $module_config[$module_name]['elas_user'], $module_config[$module_name]['elas_pass']);

isset($l) && $l === 0 && $search_kind = 1;

$search_elastic = [];
if ($search_type_content == 1) {
    if ($par_search) {
        $search_fields = ['code', 'title'];
    } else {
        $search_fields = ['content_full'];
    }
} else {
    if ($par_search) {
        $search_fields = ['code', 'title_search'];
    } else {
        $search_fields = ['content'];
    }
}
// tìm kiếm theo từ khóa chính
if (!empty($q)) {
    $arr_key = explode(',', $q);
    if (!empty($search_kind)) {
        $arr_key = array_map(function ($a) {return explode(' ', $a);}, $arr_key);
        $arr_key = new RecursiveIteratorIterator(new RecursiveArrayIterator($arr_key));
    }
    $keyword_type = $search_kind <= 1 ? 'should' : 'must';
    foreach($search_fields as $f) {
        foreach ($arr_key as $key) {
            if (empty($key)){
                continue;
            }
            if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                continue;
            }
            if ($search_type_content == 0) {
                $key = str_replace('-', ' ', change_alias($key));
            }
            $key = trim($key);
            if (preg_match('/^[0-9]+?$/', $key)) { // Nếu keyword chỉ là dãy số thì tìm wildcard
                $search_elastic[$keyword_type][] = [
                    "wildcard" => [
                        $f => [
                            "value" => '*' . $key . '*'
                        ]
                    ]
                ];
            } else {
                $search_elastic[$keyword_type][] = [
                    "match_phrase" => [
                        $f => $key
                    ]
                ];
            }
        }
    }
}

// Tìm theo Từ khóa bổ sung
if (!empty($key_search2) and $is_advance) {
    $arr_key = explode(',', $key_search2);
    $search_bosung_should = [];
    foreach($search_fields as $f) {
        foreach ($arr_key as $key) {
            if (empty($key)){
                continue;
            }
            if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                continue;
            }
            if (!empty($search_one_key)) {
                //Một trong các từ khóa là điều kiện bắt buộc
                if ($search_type_content == 1) {
                    $key = trim($key);
                    $search_bosung_should[] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = trim($key);
                    $search_bosung_should[] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                }
            } else {
                if ($search_type_content == 1) {
                    $key = trim($key);
                    $search_elastic['must'][] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = trim($key);
                    $search_elastic['must'][] = [
                        "match_phrase" => [
                            $f => $key
                        ]
                    ];
                }
            }
        }
    }
    if (!empty($search_bosung_should)) {
        $search_elastic['must'][] = [
            "bool" => [
                "should" => $search_bosung_should,
                "minimum_should_match" => 1
            ]
        ];
    }
}

// từ khóa loại trừ
if (!empty($without_key) and $is_advance) {
    $arr_key = explode(',', $without_key);
    foreach($search_fields as $f) {
        foreach ($arr_key as $key) {
            if (empty($key)){
                continue;
            }
            if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                continue;
            }
            if ($search_type_content == 1) {
                $key = trim($key);
                $search_elastic['must_not'][] = [
                    "match_phrase" => [
                        $f => $key
                    ]
                ];
            } else {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                $search_elastic['must_not'][] = [
                    "match_phrase" => [
                        $f => $key
                    ]
                ];
            }
        }
    }
}

// mã nhà mời thầu
if ($solicitor_id > 0) {
    $search_elastic['must'][] = [
        'match' => [
            'solicitor_id' => [
                'query' => $solicitor_id
            ]
        ]
    ];
}

// tìm kiếm theo khoảng thời gian
if ($sfrom1 > 0 and $sto1 > 0) {
    $search_elastic['must'][]['range']['addtime'] = [
        "gte" => $sfrom1,
        "lte" => $sto1
    ];
}
if ($type_search == 2 and $is_advance) {
    // tìm theo mức đầu tư
    if ($invest_from > 0 and $invest_to > 0) {
        $search_elastic['must'][]['range']['total_cost_num'] = [
            "gte" => $invest_from,
            "lte" => $invest_to
        ];
    } else {
        if ($invest_from > 0) {
            $search_elastic['must'][]['range']['total_cost_num'] = [
                "gte" => $invest_from
            ];
        }
        if ($invest_to > 0) {
            $search_elastic['must'][]['range']['total_cost_num'] = [
                "lte" => $invest_to
            ];
        }
    }
} else {
    if ($invest_from > 0 and $invest_to > 0 and $is_advance) {
        $search_elastic['must'][]['range']['total_invest_number'] = [
            "gte" => $invest_from,
            "lte" => $invest_to
        ];
    } else {
        if ($invest_from > 0 and $is_advance) {
            $search_elastic['must'][]['range']['total_invest_number'] = [
                "gte" => $invest_from
            ];
        }
        if ($invest_to > 0 and $is_advance) {
            $search_elastic['must'][]['range']['total_invest_number'] = [
                "lte" => $invest_to
            ];
        }
    }
}

if ($id_hinhthucluachon != 0 and $is_advance) {
    $search_elastic_htlc = [];
    $search_elastic_htlc['should'][] = [
        'bool' => [
            'must' => [
                [
                    'match' => [
                        'id_hinhthucluachon' => '[' . $id_hinhthucluachon . ']'
                    ]
                ],
                [
                    'match' => [
                        'is_new_msc' => '0'
                    ]
                ]

            ],
        ],
    ];

    $search_elastic_htlc['should'][] = [
        'bool' => [
            'must' => [
                [
                    'match' => [
                        'is_domestic' => $id_hinhthucluachon == 1 ? 1 : 0
                    ]
                ],
                [
                    'match' => [
                        'is_new_msc' => '1'
                    ]
                ]

            ],
        ],
    ];
    $search_elastic_htlc['minimum_should_match'] = '1';
    $search_elastic_htlc['boost'] = '1.0';
    $search_elastic['must'][]['bool'] = $search_elastic_htlc;
}

// tìm theo giá mời thầu
if ($price_from > 0 and $price_to > 0 and $is_advance) {
    $search_elastic['must'][]['range']['price_min'] = [
        "gte" => $price_from,
        "lte" => $price_to
    ];
    $search_elastic['must'][]['range']['price_max'] = [
        "gte" => $price_from,
        "lte" => $price_to
    ];
} else {
    if ($price_from > 0 and $is_advance) {
        $search_elastic['must'][]['range']['price_min'] = [
            "gte" => $price_from
        ];
        $search_elastic['must'][]['range']['price_max'] = [
            "gte" => $price_from
        ];
    }
    if ($price_to > 0 and $is_advance) {
        $search_elastic['must'][]['range']['price_min'] = [
            "lte" => $price_to
        ];
        $search_elastic['must'][]['range']['price_max'] = [
            "lte" => $price_to
        ];
    }
}

// Tìm kiếm theo tỉnh thành
if (!empty($_idprovince) and $is_advance && $type_search != 2) {
    $search_idprovince = array();
    if ($_idprovince[0] == 0) {
        $search_idprovince[] = [
            'term' => ['list_id_province' => 0]
        ];
    } else {
        foreach ($_idprovince as $key_idp) {
            $search_idprovince[] = [
                'regexp' => [
                    'list_id_province' => [
                        'value' => '~[0-9]' . $key_idp . '~[0-9]',
                        'flags' => 'ALL'
                    ]
                ]
            ];
        }
    }
    $search_elastic['must'][]['bool']['should'] = $search_idprovince;
}

if (!empty($search_elastic['should'])) {
    $search_elastic['minimum_should_match'] = '1';
    $search_elastic['boost'] = '1.0';
}

$array_query_elastic = array();
if (!empty($search_elastic)) {
    $array_query_elastic['query']['bool'] = $search_elastic;
}

$array_query_elastic['track_total_hits'] = 'true';
$array_query_elastic['size'] = $per_page;
$array_query_elastic['from'] = ($page - 1) * $per_page;
$array_query_elastic['sort'] = [
    [
        "addtime" => [
            "order" => "desc"
        ]
    ]
];
$response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_plans', $array_query_elastic);
$num_items = $response['hits']['total']['value'];

foreach ($response['hits']['hits'] as $value) {
    if (!empty($value['_source'])) {
        $view = $value['_source'];

        $view['addtime'] = $view['addtime'] > 0 ? $TBMT::time_display($view['addtime']) : '';
        $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['plans'] . '/' . ($type_search == 2 ? Url::getKHLCNDT() : Url::getKHLCNT()) . '/' . $view['alias'] . '-' . $view['id'] . $global_config['rewrite_exturl'];
        $view['link_search'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=listplan&solicitor_id=' . $view['solicitor_id'];

        $array_data[$view['id']] = $view;
        $arr_solicitor_id[$view['solicitor_id']] = $view['solicitor_id'];
    }
}

if ((!empty($q) or !empty($key_search2) or !empty($without_key)) and $page == 1) {
    $nukeVietElasticSearh2 = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_result_host'], $module_config[$module_name]['elas_result_port'], 'bids_plans', $module_config[$module_name]['elas_result_user'], $module_config[$module_name]['elas_result_pass']);

    // Khởi tạo truy vấn Elasticsearch cho bids_plans
    $array_query_elastic_plan = array();
    $array_query_elastic_plan['query']['bool'] = [
        'must' => []
    ];

    // Tìm kiếm theo từ khóa chính
    if (!empty($q)) {
        $num = 0;
        $arr_key = explode(',', $q);
        foreach ($arr_key as $key) {
            if ($search_type_content == 1) {
                $key = trim($key);
                $array_query_elastic_plan['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_full' => $key
                    ]
                ];
            } else {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                $array_query_elastic_plan['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_simple' => $key
                    ]
                ];
            }
        }
    }

    // Tìm theo Từ khóa bổ sung
    if (!empty($key_search2)) {
        $arr_key = explode(',', $key_search2);
        foreach ($arr_key as $key) {
            if ($search_type_content == 1) {
                $key = trim($key);
                $array_query_elastic_plan['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_full' => $key
                    ]
                ];
            } else {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                $array_query_elastic_plan['query']['bool']['must'][] = [
                    'match_phrase' => [
                        'searchtext_simple' => $key
                    ]
                ];
            }
        }
    }

    // Tìm theo từ khóa loại trừ
    if (!empty($without_key)) {
        $arr_key = explode(',', $without_key);
        foreach ($arr_key as $key) {
            if ($search_type_content == 1) {
                $key = trim($key);
                $array_query_elastic_plan['query']['bool']['must_not'][] = [
                    'match_phrase' => [
                        'searchtext_full' => $key
                    ]
                ];
            } else {
                $key = str_replace('-', ' ', change_alias($key));
                $key = trim($key);
                $array_query_elastic_plan['query']['bool']['must_not'][] = [
                    'match_phrase' => [
                        'searchtext_simple' => $key
                    ]
                ];
            }
        }
    }

    // Thêm điều kiện status = 1 và status_test = 0
    $array_query_elastic_plan['query']['bool']['must'][] = [
        'term' => [
            'status' => 1
        ]
    ];

    $array_query_elastic_plan['query']['bool']['must'][] = [
        'term' => [
            'status_test' => 0
        ]
    ];

    $array_query_elastic_plan['query']['bool']['must'][] = [
        'term' => [
            'is_last' => 1
        ]
    ];

    $array_query_elastic_result['query']['bool']['must'][] = [
        'term' => [
            'del_time' => 0
        ]
    ];

    if ($sfrom1 > 0 and $sto1 > 0) {
        $array_query_elastic_plan['query']['bool']['must'][] = [
            'range' => [
                'pub_time' => [
                    "gte" => $sfrom1,
                    "lte" => $sto1
                ]
            ]
        ];
    }

    $array_query_elastic_plan['size'] = 5;
    $array_query_elastic_plan['sort'] = [
        [
            "add_time" => [
                "order" => "desc"
            ]
        ]
    ];

    try {
        // Thực hiện truy vấn Elasticsearch
        $response_plan = $nukeVietElasticSearh2->search_data($db_config['prefix'] . '_bids_plans', $array_query_elastic_plan);
    } catch (Exception $e) {
        // Ghi log lỗi nếu có vấn đề
        file_put_contents(NV_ROOTDIR . '/data/logs/list_plan_elastic_log_bids_plans_' . date('Ymd') . '.txt', "\n\n ---------- [" . date('d-m-Y H:i:s') . "] Lỗi list_plan_log_bids_plans: \n" . print_r($e, true), FILE_APPEND);
        file_put_contents(NV_ROOTDIR . '/data/logs/list_plan_elastic_log_bids_plans_' . date('Ymd') . '.txt', "\n array_query_elastic_plan: " . print_r($array_query_elastic_plan, true), FILE_APPEND);
        trigger_error(print_r($e, true));
    }

    // Kết hợp kết quả từ Elasticsearch
    foreach ($response_plan['hits']['hits'] as $value) {
        if (!empty($value['_source'])) {
            $view = $value['_source'];
            $array_data_plan[$view['plan_id']] = $view;
        }
    }
}
