<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2014 VINADES., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 3/9/2010 23:25
 */

if (!defined('NV_MAINFILE'))
    die('Stop!!!');

if (!nv_function_exists('list_org')) {
    /**
     * list_industry()
     *
     * @param mixed $block_config
     * @return
     */
    function list_org($block_config)
    {
        global $db, $array_data, $location_map, $location_id_map, $nv_Cache, $nv_Request, $array_op, $global_config, $module_info, $module_name, $my_head, $site_mods, $module_file, $nv_Lang;

        $module = $block_config['module'];
        $q = $nv_Request->get_title('q', 'post,get');
        $location = $nv_Request->get_int('location', 'post,get', -1);
        $_province_id = $nv_Request->get_title('province_id', 'post,get', -1);
        $_censorship = $nv_Request->get_title('censorship', 'post,get', -1);
        if (isset($array_op[0]) and $array_op[0] == 'old') {
            $type_data = $nv_Request->get_int('type_data', 'post,get', 2);
        } else {
            $type_data = $nv_Request->get_int('type_data', 'post,get', 1);
        }
        $_location = (isset($array_op[0])) ? $array_op[0] : 'censorship';
        if (isset($array_op[1]))  $location_alias = $array_op[1];

        if (file_exists(NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_file . "/block_search_org.tpl")) {
            $block_theme = $module_info['template'];
        } else {
            $block_theme = "default";
        }
        if(!isset($location_id_map[$location])) {
            $location = -1;
        }
        if(isset($location_alias)) {
            if ($_location == 'censorship') {
                $_censorship = !isset($location_map[$location_alias]) ? -1 : $location_map[$location_alias];
            } else {
                $location = !isset($location_map[$location_alias]) ? -1 : $location_map[$location_alias];
            }
        } else if (!isset($location_op) and ($_province_id > 0 or $_censorship >= 0)) {
            $_censorship = $_censorship;
            $location = $_province_id;
        }
        $array_type_data = array(
            1 => $nv_Lang->getModule('organization'),
            2 => $nv_Lang->getModule('list_oran_not_show')
        );
        $xtpl = new XTemplate("block_search_org.tpl", NV_ROOTDIR . "/themes/" . $block_theme . "/modules/" . $module_file);
        $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
        if ($global_config['rewrite_enable']) {
            $form_action = nv_url_rewrite(
                NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA .
                '&amp;' . NV_NAME_VARIABLE . '=' . $module_name,
                true
            );
            if (isset($location_alias)) {
                $form_action = nv_url_rewrite(
                    NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA .
                    '&amp;' . NV_NAME_VARIABLE . '=' . $module_name .
                    '&amp;' . NV_OP_VARIABLE . '=' . $_location . '/' . $location_alias,
                    true
                );
            }
            $xtpl->assign('FORM_ACTION', $form_action);
            $xtpl->assign('FORM_ACTION_UNSHOW',  nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=old', true));
        } else {
            $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php');
            $xtpl->assign('FORM_ACTION_UNSHOW', NV_BASE_SITEURL . 'index.php');
            $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
            $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
            $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
            $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
            $xtpl->parse('main.no_rewrite');
        }
        $xtpl->assign('MODULE_NAME', $module_name);
        foreach ($array_data['province'] as $province) {
            $province['selected'] = ($province['id'] == $_censorship) ? 'selected' : '';
            $xtpl->assign('CENSORSHIP', $province);
            $xtpl->parse('main.censorship');
        }
        foreach ($array_data['list_province'] as $_province) {
            $_province['selected'] = ($_province['id'] == $location) ? 'selected' : '';
            $xtpl->assign('ARR_PROVINCE', $_province);
            $xtpl->parse('main.province');
        }
        foreach ($array_type_data as $key => $value) {
            $array = array(
                'id' => $key,
                'selected' => ($key == $type_data) ? ' selected="selected"' : '',
                'value' => $value
            );
            $xtpl->assign('TYPE_DATA', $array);
            $xtpl->parse('main.type_data');
        }
        $xtpl->assign('Q', $q);
        $xtpl->parse('main');
        return $xtpl->text('main');
    }
}

if (defined('NV_SYSTEM')) {
    $content = list_org($block_config);
}
