<!-- BEGIN: main -->
<!-- BEGIN: recaptcha -->
<div id="captchaModal" class="modal fade auto-width auto-height" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <p class="modal-title">{LANG.recapcha_title}</p>
            </div>
            <div class="modal-body">
                <div>{LANG.recapcha_body}</div>
                <div data-toggle="recaptcha" data-callback="verify_captcha" id="{RECAPTCHA_ELEMENT}"></div>
            </div>
        </div>
    </div>
</div>
<script>
    function verify_captcha(e) {
        click_update_ndt();
    }
</script>
<!-- END: recaptcha -->

<!-- BEGIN: popup_login -->
{POPUP_LOGIN}
<!-- END: popup_login -->

<script type="text/javascript">
    var capcha = 0;
    function show_capcha() {
        $("#captchaModal").modal();
    }

    function click_update_ndt() {
        $('#captchaModal').modal('hide');
        confirm_crawl_detail();
        return !1;
    }

    function confirm_crawl_detail() {
        $.ajax({
            url: "{URL_UPDATE}",
            type: "POST",
            dataType: "json",
            data: {
                'confirm_crawl': 1,
                'g-recaptcha-response' : $("[name=g-recaptcha-response]").val() ?? ''
            },
            success: function(a) {
                modalShow("", a.mess);
                $("#accept_crawl").click(function() {
                    text = $(this).attr('data-text');
                    $("#accept_crawl").prop("disabled", true).html('<i class="fa fa-spinner fa-spin"></i> ' + text);
                    setTimeout(function() {
                        updateCrawl();
                    }, 800)
                });
            }
        });
    }
</script>
<div class="detail-wrapper">
    <div class="border-bidding">
        <span>{LANG.title_detail_ndt}</span>
    </div>

    <div class="row share-content">
        <div class="col-xs-24 btn-share-group">
            <span>{LANG.share} </span>
            <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_fb_share();" title="{LANG.fb_share}">
                <span class="icon-facebook"></span>
            </a>
            <a href="javascript:void(0)" class="btn-share" rel="nofollow" onclick="nv_tw_share('', '{ROW.companyname}');" title="{LANG.tweet}">
                <span class="icon-twitter"></span>
            </a>
            <a href="javascript:void(0)" class="btn-share btn-copy-link" title="{LANG.copy_link}">
                <em class="fa fa-link"></em>
                <span class="tip" style="display: none;">{LANG.link_copy_successfully}</span>
            </a>
        </div>
    </div>

    <div class="detail-list-btn">
        <div class="text-right crawl_time mg-bt-5">
            <div class="small">
                {LANG.crawl_time}: <strong>{DATA.fget_time}</strong>
            </div>
            <!-- BEGIN: update -->
            <div class="margin-top-sm">
                <span class="small">{UPDATE_INFO_LAST}</span>
                <a style="margin-left: auto" id="reupdate" class="btn btn-default btn-xs active" onclick="show_capcha()" href="javascript:void(0)" data-id="{DATA.id}" data-check="{CHECKSESS_UPDATE}">{LANG.reupdate}</a>
                <img id="update_wait" style="display: none" src="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/images/load_bar.gif" />
            </div>
            <!-- END: update -->

            <!-- BEGIN: show_btn_cache -->
            <div class="margin-top-sm">
                <a href="{BASE_URL}?delete_cache=1" class="btn btn-danger btn-sm"><i class="fa fa-trash" aria-hidden="true"></i> {LANG.delete_cache}</a>
            </div>
            <!-- END: show_btn_cache -->
        </div>
    </div>


    <table class="table detail_scoll__menu m-top">
        <tbody>
            <tr class="bg-xam">
                <td colspan="2"><b>{LANG.detail_info}</b></td>
            </tr>
            <tr>
                <td>
                    {LANG.fullname_org_msc_new}
                </td>
                <td>
                    <h1 class="bidding-name">{DATA.org_fullname}</h1>
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.english_name}
                </td>
                <td>
                    {DATA.org_enname}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.madinhdanh}
                </td>
                <td>
                    {DATA.org_code}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.tax}
                </td>
                <td>
                    {DATA.tax}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.solicitor_type}
                </td>
                <td>
                    {DATA.title_type_business}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.date_registration_msc_new}
                </td>
                <td>
                    {DATA.tax_date}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.decision_date}
                </td>
                <td>
                    {DATA.aproval_time}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.nation_msc_new}
                </td>
                <td>
                    {DATA.tax_nation}
                </td>
            </tr>

            <tr class="bg-xam">
                <td colspan="2"><b>{LANG.title_qdtl_ndt}</b></td>
            </tr>
            <tr>
                <td>
                    {LANG.dateestablished}
                </td>
                <td>
                    {DATA.decision_date}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.approval_org_msc_new}
                </td>
                <td>
                    {DATA.decision_agency}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.title_qgbh_ndt}
                </td>
                <td>
                    {DATA.decision_nation}
                </td>
            </tr>

            <tr class="bg-xam">
                <td colspan="2"><b>{LANG.dctruso}</b></td>
            </tr>

            <tr>
                <td>
                    {LANG.offadd}
                </td>
                <td>
                    {DATA.title_province}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.offdistrict}
                </td>
                <td>
                    {DATA.title_district}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.offward}
                </td>
                <td>
                    {DATA.title_ward}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.offdetail}
                </td>
                <td>
                    {DATA.full_address}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.website}
                </td>
                <td>
                    {DATA.website}
                </td>
            </tr>

            <tr class="bg-xam">
                <td colspan="2"><b>{LANG.persion_ddpl}</b></td>
            </tr>

            <tr>
                <td>
                    {LANG.fullname}
                </td>
                <td>
                    {DATA.rep_name}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.rep_position}
                </td>
                <td>
                    {DATA.rep_position}
                </td>
            </tr>

            <tr class="bg-xam">
                <td colspan="2"><b>{LANG.title_dkkd}</b></td>
            </tr>

            <tr>
                <td>
                    {LANG.title_sodkkd}
                </td>
                <td>
                    {DATA.tax}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.date_registration_msc_new}
                </td>
                <td>
                    {DATA.business_date}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.nation_msc_new}
                </td>
                <td>
                    {DATA.business_nation}
                </td>
            </tr>

            <tr class="bg-xam">
                <td colspan="2"><b>{LANG.title_tthd}</b></td>
            </tr>

            <tr>
                <td>
                    {LANG.title_sonv}
                </td>
                <td>
                    {DATA.num_employees}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.title_lvtg}
                </td>
                <td>
                    {DATA.lvtg_lcnt}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.title_qmdn}
                </td>
                <td>
                    {DATA.org_scale}
                </td>
            </tr>

            <tr>
                <td>
                    {LANG.dinh_kem}
                </td>
                <td>
                    <!-- BEGIN: dinhkem -->
                    <div class="tab-content download">
                        <div class="tab-pane fade active in">
                            <div class="list-group download-link">
                                <!-- BEGIN: loop -->
                                <div class="list-group-item list-group-item-light">{ROW.title}</div>
                                <div class="list-group-item display-flex">
                                    <a href="#" class="disable-link" target="_blank" rel="noopener noreferrer nofollow" title="{ROW.name_file}" alt="{ROW.name_file}"><span class="">{ROW.name_file}</span></a>
                                    <div class="text-nowrap" style="margin-left:auto">
                                        <a class="btn btn-default btn-xs" href="{ROW.link_download}"> {LANG.download_title}</a>
                                    </div>
                                </div>
                                <!-- END: loop -->
                            </div>
                        </div>
                    </div>
                    <!-- END: dinhkem -->

                    <!-- BEGIN: no_dinhkem -->
                        <span class="text-muted"><i>{LANG.title_no_file}</i></span>
                    <!-- END: no_dinhkem -->
                </td>
            </tr>

            <tr class="bg-xam">
                <td colspan="2"><b>{LANG.career}</b></td>
            </tr>

            <!-- BEGIN: nnkd-->
            <tr>
                <td colspan="2">
                   <div id="box_nnkd">
                        <div class="form-group">
                            <div class="input-group">
                                <input type="text" id="search-input" class="form-control" placeholder="{LANG.search_block_title}">

                                <span id="filter-btn" class="input-group-addon filter-btn">
                                    <i class="fa fa-filter" aria-hidden="true"></i> {LANG.title_filter_search}
                                </span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="rows-per-page">{LANG.title_show_pagination}:</label>
                            <select id="rows-per-page" class="form-control">
                                <option value="5">5</option>
                                <option value="10" selected>10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                                <option value="200">200</option>
                                <option value="500">500</option>
                                <option value="all">All</option>
                            </select>
                            <span><strong>{LANG.title_data_one_pagination}</strong>
                        </div>
                       <div class="box__table">
                           <table class="bidding-table">
                               <thead>
                                   <tr>
                                       <th class="text-center column-header" data-column="1">{LANG.title_manganh}</th>
                                       <th class="text-center column-header" data-column="2">{LANG.title_tennganh}</th>
                                       <th class="text-center column-header" data-column="3">{LANG.title_nganhchinh}</th>
                                   </tr>
                               </thead>
                               <tbody id="data-container">
                                    <!-- BEGIN: loop-->
                                   <tr class='table-row' data-index='{ROW.stt}'>
                                       <td data-column="{LANG.title_manganh}" class="text-center">{ROW.code}</td>
                                       <td data-column="{LANG.title_tennganh}">{ROW.name}</td>
                                       <td data-column="{LANG.title_nganhchinh}" class="text-center">
                                            <label class="custom-checkbox custom-checkbox-ndt">
                                                <input type="checkbox" class="ccb_field" data-default="false" {ROW.check}>
                                                <span class="txt"></span>
                                            </label>
                                       </td>
                                   </tr>
                                    <!-- END: loop-->
                               </tbody>
                           </table>
                       </div>

                       <div class="pagination-custom text-center">
                            <ul class="pagination" id="pagination1"></ul>
                        </div>
                   </div>
                </td>
            </tr>
            <!-- END: nnkd-->

            <!-- BEGIN: no_nnkd-->
            <tr>
                <td colspan="2">
                    <div class="text-center">
                        <span class="text-muted"><i>{LANG.title_no_nnkd}</i></span>
                    </div>
                </td>
            </tr>
            <!-- END: no_nnkd-->
        </tbody>
    </table>
</div>

<div class="investor_static">
    {DATA_STATIC}
</div>

<!-- BEGIN: error_api -->
    <p class="alert alert-warning">
        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i> {DATA.message_api}
    </p>
<!-- END: error_api -->

<!-- BEGIN: show_timeline -->
<div id="contractor_activities" class="detail_scoll__menu">
    <div class="bidding-simple wrap__text">
        <div class="border-bidding">
            <span>{LANG.title_investor_activities}</span>
        </div>

        {DATA.timeline}
    </div>
</div>
<!-- END: show_timeline -->

<!-- BEGIN: list_project_proposal -->
<div class="bidding-simple wrap__text">
    <section class="block-bidding">
        <div class="list-projects-participated">
            <div class="bidding-simple wrap__text">
                <div class="border-bidding">
                    <span>{LANG.title_list_projects_participated}</span>
                </div>

                <div class="bidding-list bidding-list-detail">
                    <div class="bidding-list-header">
                        <div class="c-name">{LANG.project_invesment_title}</div>
                        <div class="c-author">{LANG.don_vi}</div>
                        <div class="c-pub ">{LANG.time_post_sort}</div>
                        <div class="c-pub ">{LANG.so_bo_tong_chi_phi_th_da}</div>
                    </div>
                    <div class="bidding-list-body">
                        <!-- BEGIN: loop -->
                        <div class="item">
                            <div class="c-name">
                                <span class="label-name">{LANG.project_invesment_title}: </span>
                                <a href="{PROJECT_PROPPOSAL.link}" title="{PROJECT_PROPPOSAL.ten_du_an}"><span class="bidding-code">{PROJECT_PROPPOSAL.code}</span>{PROJECT_PROPPOSAL.ten_du_an}</a>
                            </div>

                            <!-- BEGIN: lock -->
                            <div class="c-author text-center coating">
                                <span class="label-name">{LANG.don_vi}: </span>
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                                {BACKGROUND}
                            </div>

                            <div class="c-pub text-center coating">
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                            </div>

                            <div class="c-pub text-center coating">
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                            </div>
                            <!-- END: lock -->

                            <!-- BEGIN: unlock -->
                            <div class="c-author">
                                <span class="label-name">{LANG.don_vi}: </span>
                                <a href="{PROJECT_PROPPOSAL.link_solictor}" title="{PROJECT_PROPPOSAL.don_vi}">
                                    <div>{PROJECT_PROPPOSAL.don_vi}</div>
                                </a>
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.time_post_sort}: </span>
                                {PROJECT_PROPPOSAL.time_post}
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.so_bo_tong_chi_phi_th_da}: </span>
                                {PROJECT_PROPPOSAL.total_cost_number}
                            </div>
                            <!-- END: unlock -->
                        </div>
                        <!-- END: loop -->

                        <!-- BEGIN: no_summary -->
                        <div class="alert alert-info mt-1">{NO_SUMMARY}</div>
                        <!-- END: no_summary -->

                        <!-- BEGIN: show_summary -->
                        <p class="mt-2">
                            {DATA.link_summary_project_proposal}
                        </p>
                        <!-- END: show_summary -->
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<!-- END: list_project_proposal -->

<!-- BEGIN: list_orther_investor -->
<div class="bidding-simple wrap__text">
    <section class="block-bidding">
        <div class="list-projects-participated">
            <div class="bidding-simple wrap__text">
                <div class="border-bidding">
                    <span>{LANG.ds_nt_liendanh_cdt}</span>
                </div>

                <div class="bidding-list bidding-list-detail">
                    <div class="bidding-list-header">
                        <div class="c-name">{LANG.name_investor}</div>
                        <div class="c-pub">{LANG.goithau_total}</div>
                        <div class="c-pub">{LANG.goithau_trung}</div>
                        <div class="c-pub ">{LANG.goithau_truot}</div>
                    </div>
                    <div class="bidding-list-body">
                        <!-- BEGIN: loop -->
                        <div class="item">
                            <div class="c-name">
                                <span class="label-name">{LANG.name_investor}: </span>
                                <a href="{INVESTOR_PARTNERSHIP.link_investordetail}" title="{INVESTOR_PARTNERSHIP.info_investor.org_fullname}"><span class="bidding-code">{INVESTOR_PARTNERSHIP.orgcode}</span>{INVESTOR_PARTNERSHIP.info_investor.org_fullname}</a> - <a href="{INVESTOR_PARTNERSHIP.link}">{LANG.view_relative}</a>
                            </div>

                            <!-- BEGIN: lock -->
                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_total}: </span>
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                                {BACKGROUND}
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_trung}: </span>
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_truot}: </span>
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                            </div>
                            <!-- END: lock -->

                            <!-- BEGIN: unlock -->
                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_total}: </span>
                                {INVESTOR_PARTNERSHIP.num_kqlcndt}
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_trung}: </span>
                                {INVESTOR_PARTNERSHIP.num_kqlcndt_win}
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_truot}: </span>
                                {INVESTOR_PARTNERSHIP.num_kqlcndt_lost}
                            </div>
                            <!-- END: unlock -->
                        </div>
                        <!-- END: loop -->
                    </div>

                    <!-- BEGIN: no_summary -->
                    <div class="alert alert-info mt-1">{NO_SUMMARY}</div>
                    <!-- END: no_summary -->

                    <!-- BEGIN: show_summary -->
                    <p class="mt-2">
                        {DATA.link_summary_orther_investor}
                    </p>
                    <!-- END: show_summary -->
                </div>
            </div>
        </div>
    </section>
</div>
<!-- END: list_orther_investor -->

<!-- BEGIN: list_solictor -->
<div class="bidding-simple wrap__text">
    <section class="block-bidding">
        <div class="list-projects-participated">
            <div class="bidding-simple wrap__text">
                <div class="border-bidding">
                    <span>{LANG.title_solictor_inviters}</span>
                </div>

                <div class="bidding-list bidding-list-detail">
                    <div class="bidding-list-header">
                        <div class="c-stt">{LANG.stt}</div>
                        <div class="c-name">{LANG.solictor}</div>
                        <div class="c-pub">{LANG.num_bid_row}</div>
                        <div class="c-pub ">{LANG.subdivision_yes}</div>
                    </div>
                    <div class="bidding-list-body">
                        <!-- BEGIN: loop -->
                        <div class="item">
                            <div class="c-stt text-center">
                                <span class="label-name">{LANG.STT}: </span>
                                {SOLICTOR.stt}
                            </div>

                            <div class="c-name">
                                <span class="label-name">{LANG.solictor}: </span>
                                <a href="{SOLICTOR.link}" title="{SOLICTOR.title}">{SOLICTOR.title}</a>
                            </div>

                            <!-- BEGIN: lock -->
                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.num_bid_row}: </span>
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                                {BACKGROUND}
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.subdivision_yes}: </span>
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                            </div>
                            <!-- END: lock -->

                            <!-- BEGIN: unlock -->
                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.num_bid_row}: </span>
                                {SOLICTOR.num_tbmt}
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.subdivision_yes}: </span>
                                {SOLICTOR.num_kq}
                            </div>
                            <!-- END: unlock -->
                        </div>
                        <!-- END: loop -->
                    </div>

                    <!-- BEGIN: no_summary -->
                    <div class="alert alert-info mt-1">{NO_SUMMARY}</div>
                    <!-- END: no_summary -->

                    <!-- BEGIN: show_summary -->
                    <p class="mt-2">
                        {DATA.link_summary_investor_solictor}
                    </p>
                    <!-- END: show_summary -->
                </div>
            </div>
        </div>
    </section>
</div>
<!-- END: list_solictor -->

<!-- BEGIN: list_competiors -->
<div class="bidding-simple wrap__text">
    <section class="block-bidding">
        <div class="list-projects-participated">
            <div class="bidding-simple wrap__text">
                <div class="border-bidding">
                    <span>{LANG.ds_nt_tungdau_cdt}</span>
                </div>

                <div class="bidding-list bidding-list-detail">
                    <div class="bidding-list-header">
                        <div class="c-stt">{LANG.STT}</div>
                        <div class="c-name">{LANG.name_investor}</div>
                        <div class="c-pub">{LANG.goithau_trung}</div>
                        <div class="c-pub">{LANG.goithau_truot}</div>
                    </div>

                    <div class="bidding-list-body">
                        <!-- BEGIN: loop -->
                        <div class="item">
                            <div class="c-stt text-center">
                                <span class="label-name">{LANG.STT}: </span>
                                {COMPETIORS.stt}
                            </div>

                            <div class="c-name">
                                <span class="label-name">{LANG.name_investor}: </span>
                                <a href="{COMPETIORS.link_investor_detail}" title="{COMPETIORS.bidder_name}"><span class="bidding-code">{COMPETIORS.orgcode}</span>{COMPETIORS.bidder_name}</a>
                            </div>

                            <!-- BEGIN: lock -->
                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_trung}: </span>
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                                {BACKGROUND}
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_truot}: </span>
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                            </div>
                            <!-- END: lock -->

                            <!-- BEGIN: unlock -->
                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_trung}: </span>
                                {COMPETIORS.num_win}
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_truot}: </span>
                                {COMPETIORS.num_lost}
                            </div>
                            <!-- END: unlock -->
                        </div>
                        <!-- END: loop -->

                        <!-- BEGIN: no_summary -->
                        <div class="alert alert-info mt-1">{NO_SUMMARY}</div>
                        <!-- END: no_summary -->

                        <!-- BEGIN: show_summary -->
                        <p class="mt-2">
                            {DATA.link_summary_project_competitors}
                        </p>
                        <!-- END: show_summary -->
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<!-- END: list_competiors -->

<!-- BEGIN: list_province -->
<div class="bidding-simple wrap__text">
    <section class="block-bidding">
        <div class="list-projects-participated">
            <div class="bidding-simple wrap__text">
                <div class="border-bidding">
                    <span>{LANG.ds_nt_tinhtp_cdt}</span>
                </div>

                <div class="bidding-list bidding-list-detail">
                    <div class="bidding-list-header">
                        <div class="c-stt">{LANG.STT}</div>
                        <div class="c-name">{LANG.province_org}</div>
                        <div class="c-pub">{LANG.goithau_total}</div>
                        <div class="c-pub">{LANG.goithau_trung}</div>
                        <div class="c-pub">{LANG.goithau_truot}</div>
                        <div class="c-pub">{LANG.gia_du_an}</div>
                    </div>
                    <div class="bidding-list-body">
                        <!-- BEGIN: loop -->
                        <div class="item">
                            <div class="c-stt text-center">
                                <span class="label-name">{LANG.STT}: </span>
                                {PROVINCE_INVESTOR.stt}
                            </div>

                            <div class="c-name text-center">
                                <span class="label-name">{LANG.province_org}: </span>
                                {PROVINCE_INVESTOR.info.title}
                            </div>

                            <!-- BEGIN: lock -->
                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_total}: </span>
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                                {BACKGROUND}
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_trung}: </span>
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_truot}: </span>
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.gia_du_an}: </span>
                                <a href="javascript:void(0)"class="title__lock"><i class="fa fa-lock" aria-hidden="true"></i></a>
                            </div>
                            <!-- END: lock -->

                            <!-- BEGIN: unlock -->
                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_total}: </span>
                                {PROVINCE_INVESTOR.size}
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_trung}: </span>
                                {PROVINCE_INVESTOR.result_status_win}
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.goithau_truot}: </span>
                                {PROVINCE_INVESTOR.result_status_lost}
                            </div>

                            <div class="c-pub text-center">
                                <span class="label-name">{LANG.gia_du_an}: </span>
                                {PROVINCE_INVESTOR.total_sum}
                            </div>
                            <!-- END: unlock -->
                        </div>
                        <!-- END: loop -->

                        <!-- BEGIN: no_summary -->
                        <div class="alert alert-info mt-1">{NO_SUMMARY}</div>
                        <!-- END: no_summary -->

                        <!-- BEGIN: show_summary -->
                        <p class="mt-2">
                            {DATA.link_summary_province}
                        </p>
                        <!-- END: show_summary -->
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<!-- END: list_province -->

<div class="filter-overlay" id="filter-overlay"></div>
<div class="filter-popup" id="filter-popup">
    <p><b>{LANG.title_select_solumns_to_display}</b></p>
    <div id="filter-options"></div>
    <button class="btn btn-primary" id="apply-filter">{LANG.title_apply}</button>
    <button class="btn btn-default" id="close-filter">{LANG.close}</button>
</div>

<script type="text/javascript">
    (() => {
        var totalRows = $("#data-container .table-row").length;
        var rowsPerPage = 10;
        var totalPages = Math.ceil(totalRows / rowsPerPage);
        var currentPage = 1;

        // Hiển thị trang
        function displayRows(page) {
            const rows = $("#data-container .table-row");
            rows.hide();

            const start = (page - 1) * rowsPerPage;
            const end = page * rowsPerPage;

            rows.slice(start, end).show();
        }

        // Function khởi tạo phân trang và các xử lý phân trang bên dưới đây
        function renderPagination() {
            const paginationContainer = $("#pagination1");
            paginationContainer.empty();
            if (totalPages == 1) {
                $(".pagination-custom").hide();
                return false;
            }

            // Previous button
            if (currentPage > 1) {
                const prevLi = $("<li><a href='#'>«</a></li>");
                prevLi.click(function (e) {
                    e.preventDefault();
                    changePage(currentPage - 1);
                });
                paginationContainer.append(prevLi);
            }

            // First Page button
            const firstPageLi = $("<li><a href='#'>1</a></li>");
            firstPageLi.click(function (e) {
                e.preventDefault();
                changePage(1);
            });
            if (currentPage === 1) firstPageLi.addClass('active');  // Set active if on page 1
            paginationContainer.append(firstPageLi);

            // Pages and "..."
            if (currentPage > 3) {
                paginationContainer.append("<li><a href='#'>...</a></li>");
            }

            const pageNumbers = [];
            for (let i = Math.max(2, currentPage - 2); i <= Math.min(totalPages - 1, currentPage + 2); i++) {
                pageNumbers.push(i);
            }

            pageNumbers.forEach(function (page) {
                createPageButton(page, paginationContainer, currentPage === page);
            });

            if (currentPage < totalPages - 2) {
                paginationContainer.append("<li><a href='#'>...</a></li>");
            }

            let lastPageLi;
            // Last Page button

            if (totalPages > 1) {
                lastPageLi = $("<li><a href='#'>" + totalPages + "</a></li>");
                lastPageLi.click(function (e) {
                    e.preventDefault();
                    changePage(totalPages);
                });
            }


            if (currentPage === totalPages) lastPageLi.addClass('active');  // Set active if on last page
            paginationContainer.append(lastPageLi);

            // Next button
            if (currentPage < totalPages) {
                const nextLi = $("<li><a href='#'>»</a></li>");
                nextLi.click(function (e) {
                    e.preventDefault();
                    changePage(currentPage + 1);
                });
                paginationContainer.append(nextLi);
            }
        }

        // Function to create page button
        function createPageButton(page, container, isActive = false) {
            const li = $("<li>").addClass(isActive ? "active" : "");
            const a = $("<a href='#'>").text(page);
            a.click(function (e) {
                e.preventDefault();
                changePage(page);
            });
            li.append(a);
            container.append(li);
        }

        // Change current page
        function changePage(page) {
            currentPage = page;
            displayRows(currentPage);
            renderPagination();
            // Di chuyển tới đầu table
            $("html, body").animate({
                scrollTop: $("#search-input").offset().top - 80
            }, 500);
        }

        $("#search-input").on("input", function () {
            const query = $(this).val().trim().toLowerCase();
            const rows = $("#data-container .table-row");
            // Nếu ô tìm kiếm trống, hiển thị lại toàn bộ danh sách
            if (query === '') {
                rowsPerPage = 10;
                rows.show(); // Hiển thị tất cả các hàng
                totalPages = Math.ceil(rows.length / rowsPerPage); // Cập nhật lại số trang
                currentPage = 1; // Đặt lại trang về 1
                displayRows(currentPage); // Hiển thị các hàng theo trang hiện tại
                renderPagination(); // Cập nhật phân trang
                return;
            }

            // Lọc các hàng phù hợp với từ khóa
            filteredRowsGlobal = rows.filter(function () {
                const text = $(this).text().toLowerCase();
                return text.includes(query); // Giữ các hàng phù hợp
            });

            rows.hide(); // Ẩn tất cả các hàng
            filteredRowsGlobal.show(); // Hiển thị hàng tìm được

            // Cập nhật số trang dựa trên kết quả tìm kiếm
            const filteredCount = filteredRowsGlobal.length;
            totalPages = Math.floor(filteredCount / rowsPerPage);

            if (totalPages <= 1) {
                totalPages = 0;
            }

            // Set dữ liệu tìm kiếm khoảng 100 rows
            rowsPerPage = 100;

            // Reset về trang đầu tiên và hiển thị kết quả
            currentPage = 1;
            displayFilteredRows(currentPage);
            renderPagination();
        });

        $("#search-input").on("change", function () {
            query = $(this).val().trim();
            $(this).val(query);
        });

        function displayFilteredRows(page) {
            const start = (page - 1) * rowsPerPage;
            const end = page * rowsPerPage;

            filteredRowsGlobal.hide(); // Ẩn tất cả các hàng
            filteredRowsGlobal.slice(start, end).show(); // Hiển thị hàng trong phạm vi trang
        }

        // Filter Popup functionality
        const filterBtn = $("#filter-btn");
        const filterPopup = $("#filter-popup");
        const filterOverlay = $("#filter-overlay");
        const closeFilterBtn = $("#close-filter");
        const applyFilterBtn = $("#apply-filter");
        const filterOptions = $("#filter-options");

        // arr select column bảng tb
        let selectedColumns = [];

        // Load các danh sách tr table ra
        function populateFilterOptions() {
            filterOptions.empty();

            // Nếu selectedColumns rỗng, mặc định chọn tất cả các cột
            if (selectedColumns.length === 0) {
                $(".column-header").each(function () {
                    const column = $(this).data("column");
                    selectedColumns.push(column);
                });
            }

            $(".column-header").each(function () {
                let column = $(this).data("column");
                let text = $(this).text();
                isChecked = selectedColumns.includes(column); // Luôn đúng do đã gán mặc định
                if (column  <= 2) {
                    disabled = 'disabled';
                } else {
                    disabled = '';
                }
                let checkbox = '<label><input type="checkbox" ' + disabled + '  class="column-checkbox" data-column="' + column + '"' + (isChecked ? "checked" : "") + '>&nbsp;' + text + '</label><br>';
                filterOptions.append(checkbox);
            });
        }

        // Mở filter popup
        filterBtn.click(function () {
            populateFilterOptions();
            filterPopup.show();
            filterOverlay.show();
        });

        // Đóng popup
        closeFilterBtn.click(function () {
            filterPopup.hide();
            filterOverlay.hide();
        });

        // Cập nhật lựa chọn hiển thị hoặc ẩn cột
        applyFilterBtn.click(function () {
            selectedColumns = $(".column-checkbox:checked").map(function () {
                return $(this).data("column");
            }).get();  // Update selectedColumns array with checked columns

            // Show/hide columns based on selected checkboxes
            $(".column-header").each(function () {
                const column = $(this).data("column");
                $(this).toggle(selectedColumns.includes(column));
            });

            $(".table-row").each(function () {
                const columns = $(this).children();
                columns.each(function (index) {
                    const columnIndex = index + 1;
                    $(this).toggle(selectedColumns.includes(columnIndex));
                });
            });

            filterPopup.hide();
            filterOverlay.hide();
        });

        // Handle rows theo select người dùng chọn...
        $("#rows-per-page").change(function () {
            const selectedValue = $(this).val(); // Lấy giá trị mới từ selector

            if (selectedValue === "all") {
                rowsPerPage = totalRows; // Hiển thị tất cả các dòng
                totalPages = 1; // Chỉ có một trang
                currentPage = 1; // Reset về trang đầu tiên
                displayRows(currentPage); // Hiển thị tất cả các dòng
                renderPagination(); // Cập nhật phân trang
            } else {
                rowsPerPage = parseInt(selectedValue, 10); // Chuyển đổi giá trị thành số nguyên
                totalPages = Math.ceil(totalRows / rowsPerPage); // Cập nhật tổng số trang
                currentPage = 1; // Reset về trang đầu tiên
                displayRows(currentPage); // Hiển thị các dòng tương ứng
                renderPagination(); // Cập nhật phân trang
            }
        });

        // Khởi tạo table and phân trang
        function init() {
            displayRows(currentPage);
            renderPagination();
        }

        // Khởi tạo bảng tb
        init();
    })();
</script>
<!-- END: main -->
